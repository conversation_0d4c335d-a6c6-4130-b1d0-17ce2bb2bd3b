# 项目名称

moego-server-grooming

# 简介

moego-server-grooming 是 <PERSON>Go 项目的预约服务，提供预约相关的接口，例如预约的查询与创建。

# 启动

测试环境系统信息：https://moego.atlassian.net/wiki/spaces/ET/pages/13828130

Java 多分支开发方案：https://moego.atlassian.net/wiki/spaces/ET/pages/68845732/Java+v3+2022-08-03

# 使用

Swagger 接口文档地址：https://api.t2.moego.pet/swagger/grooming/swagger-ui.html

新 Api 接口文档地址：https://api-docs.t2.moego.pet/?module=REST-GROOMING

Jenkins 地址：https://ci.devops.moego.pet/job/MoeGolibrary/job/moego-server-grooming/

# 贡献

研发开发流程：https://moego.atlassian.net/wiki/spaces/ET/pages/62063015/v3+2022-07-21

后端代码规范：https://moego.atlassian.net/wiki/spaces/ET/pages/29557059/v1

单元测试规范：https://moego.atlassian.net/wiki/spaces/ET/pages/40665466

SQL 相关的改动，放入 [/doc](/doc) 目录下。

提交代码前，使用`git pull --rebase`命令，保证本地代码是最新的。然后使用 `yarn prettier --write .` 命令检查代码格式，确保代码格式正确。最后使用`git push`命令提交代码。

在 SonarQube 上查看代码质量：https://sonarqube.devops.moego.pet/projects
