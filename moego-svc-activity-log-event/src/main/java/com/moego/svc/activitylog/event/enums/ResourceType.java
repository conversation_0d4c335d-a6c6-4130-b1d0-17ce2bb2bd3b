package com.moego.svc.activitylog.event.enums;

/**
 * @deprecated by <PERSON> on 2024/7/26, use {@link com.moego.idl.models.activity_log.v1.Resource.Type} instead.
 * <AUTHOR>
 */
@Deprecated(since = "2024/7/26")
public enum ResourceType {
    // Grooming
    APPOINTMENT,
    REPEAT_RULE,
    BLOCK,
    GROOMING_SERVICE,
    GROOMING_NOTE,

    // Online booking
    LANDING_PAGE_SETTING,
    ONLINE_BOOKING_SETTING, // aka. Book Online
    ONLINE_BOOKING_TEAM_SCHEDULE,
    ONLINE_BOOKING_SERVICE, // GROOMING_SERVICE
    ONLINE_BOOKING_PET_LIMIT,
    ONLINE_BOOKING_CUSTOMIZED_PAYMENT,
    ONLINE_BOOKING_NOTIFICATION, // ONLINE_BOOKING_SETTING
    ONLINE_BOOKING_QUESTION,
    ABANDONED_BOOKING,
    CUSTOMER_PROFILE_REQUEST,

    // Customer
    CUSTOMER,
    <PERSON><PERSON><PERSON>ME<PERSON>_TAG,
    CUSTOMER_ADDRESS,
    CUSTOMER_CONTACT,
    C<PERSON>TOMER_NOTE,
    CUSTOMER_PREFERRED_TIP,
    PET,
    PET_NOTE,
    PET_BEHAVIOR,
    PET_BREED,
    PET_CODE,
    PET_FIXED,
    PET_PHOTO,
    PET_TYPE,
    PET_VACCINE,
    PET_VACCINE_BINDING,
    PET_HAIR_LENGTH,
    PET_CUSTOMIZED_SERVICE,
    AGREEMENT_SIGNATURE,

    // Business
    ACCOUNT,

    // Message
    MESSAGE,
    CHAT,
    AUTO_MESSAGE_SETTING,
    REMINDER_SETTING,
    AUTO_REPLY_SETTING,
    SCHEDULE_MESSAGE,
    MESSAGE_TEMPLATE,

    // review booster
    REVIEW_BOOSTER,

    // order
    ORDER,
    // package
    PACKAGE,
    // customer package
    CUSTOMER_PACKAGE,
    // payment
    PAYMENT,
    REFUND,
    PAYMENT_STRIPE_ACCOUNT,
    PAYOUT,
    TERMINAL,
    TERMINAL_LOCATION,
    STRIPE_CARD,
    STRIPE_CUSTOMER,
    SQUARE_CARD,
    SQUARE_CUSTOMER,
    SUBSCRIPTION,
    MESSAGE_EMAIL_PACKAGE,
    HARDWARE,
    // customized payment setting
    CUSTOMIZED_PAYMENT_SETTING,
    // enterprise customized payment setting
    ENTERPRISE_CUSTOMIZED_PAYMENT_SETTING,
    // company plan feature
    COMPANY_PLAN_FEATURE,
    // platform care
    PLATFORM_CARE,

    // staff
    STAFF,
    BUSINESS_SETTING;

    @Override
    public String toString() {
        return toReadableString(super.toString());
    }

    /**
     * APPOINTMENT -> Appointment
     * <p> PET -> Pet
     * <p> CUSTOMER_CONTACT -> Customer Contact
     *
     */
    static String toReadableString(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        String[] arr = str.toLowerCase().replace('_', ' ').split(" ");
        StringBuilder sb = new StringBuilder();
        for (String s : arr) {
            sb.append(capitalizeFirstLetter(s)).append(" ");
        }
        return sb.toString().stripTrailing();
    }

    /**
     * Capitalize first letter of a String.
     *
     * @param str string
     * @return capitalized string
     */
    static String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
