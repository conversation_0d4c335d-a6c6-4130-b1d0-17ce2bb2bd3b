package com.moego.svc.activitylog.event;

import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.svc.activitylog.event.enums.ResourceType;
import java.util.Date;
import lombok.Data;

/**
 * Activity log event.
 *
 * <AUTHOR>
 */
@Data
public class ActivityLogEvent {
    public static final String TOPIC = "activity-log:ActivityLogEvent";

    /**
     * one of company_id and business_id
     */
    private Long companyId;

    /**
     * one of company_id and business_id
     */
    private Long businessId;

    /**
     * operator_id, nullable
     */
    private String operatorId;

    /**
     * Event time
     */
    private Date time;

    /**
     * request_id, nullable
     */
    private String requestId;

    /**
     * Whether the event is root event
     */
    private boolean root;

    // properties below need to pass manually

    /**
     * Action, not null, use capitalized formatting.
     *
     * <p> Use 'Create' instead of 'create'
     * <p> Use 'Batch Delete' instead of 'Batch delete'
     */
    private String action;

    /**
     * resource_type, not null
     *
     * @deprecated by <PERSON> on 2024/7/29, use {@link #resourceTypeV2} instead.
     */
    @Deprecated(since = "2024/7/29")
    private ResourceType resourceType;

    /**
     * resource_type, not null
     */
    private Resource.Type resourceTypeV2;

    /**
     * resource_id, nullable
     */
    private String resourceId;

    /**
     * updated value, JSON value, nullable
     */
    private String details;

    /**
     * class name of details, nullable
     */
    private String className;
}
