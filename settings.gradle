rootProject.name = "moego-java-lib"

include(':moego-lib-activemq')
include(':moego-lib-aws')
include(':moego-lib-common')
include(':moego-lib-common:integration-tests:dynamic-datasource:mybatis')
include(':moego-lib-encryption')
include(':moego-lib-event-bus')
include(':moego-lib-feature-flag')
include(':moego-lib-ip2region')
include(':moego-lib-messaging')
include(':moego-lib-mybatis-plugins')
include(':moego-lib-permission')
include(':moego-lib-risk-control')
include(':moego-lib-springdoc')
include(':moego-lib-uid')
include(':moego-lib-utils')

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
configureSourceBranchControl()

new File("${rootDir}/.githooks").eachFile(groovy.io.FileType.FILES) {
  java.nio.file.Files.copy(it.toPath(), new File("${rootDir}/.git/hooks", it.name).toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING)
}
