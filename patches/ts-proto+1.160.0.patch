diff --git a/node_modules/ts-proto/build/generate-services.js b/node_modules/ts-proto/build/generate-services.js
index 8117f7f..2dbeb50 100644
--- a/node_modules/ts-proto/build/generate-services.js
+++ b/node_modules/ts-proto/build/generate-services.js
@@ -22,7 +22,8 @@ function generateService(ctx, fileDesc, sourceInfo, serviceDesc) {
     const chunks = [];
     (0, utils_1.maybeAddComment)(sourceInfo, chunks, (_a = serviceDesc.options) === null || _a === void 0 ? void 0 : _a.deprecated);
     const maybeTypeVar = options.context ? `<${main_1.contextTypeVar}>` : "";
-    chunks.push((0, ts_poet_1.code) `export interface ${(0, ts_poet_1.def)(serviceDesc.name)}${maybeTypeVar} {`);
+    chunks.push((0, ts_poet_1.code) `export interface ${(0, ts_poet_1.def)(serviceDesc.name)}${maybeTypeVar} {
+      $fullName: '${(0, utils_1.maybePrefixPackage)(fileDesc, serviceDesc.name)}';`);
     serviceDesc.method.forEach((methodDesc, index) => {
         var _a;
         (0, utils_1.assertInstanceOf)(methodDesc, utils_1.FormattedMethodDescriptor);
diff --git a/node_modules/ts-proto/build/main.js b/node_modules/ts-proto/build/main.js
index 57a65e7..cd3c780 100644
--- a/node_modules/ts-proto/build/main.js
+++ b/node_modules/ts-proto/build/main.js
@@ -616,7 +616,7 @@ function generateInterfaceDeclaration(ctx, fullName, messageDesc, sourceInfo, fu
         (0, utils_1.maybeAddComment)(info, chunks, (_a = fieldDesc.options) === null || _a === void 0 ? void 0 : _a.deprecated);
         const name = (0, case_1.maybeSnakeToCamel)(fieldDesc.name, options);
         const isOptional = (0, types_1.isOptionalProperty)(fieldDesc, messageDesc.options, options);
-        const type = (0, types_1.toTypeName)(ctx, messageDesc, fieldDesc, isOptional);
+        const type = (0, types_1.toTypeName)(ctx, messageDesc, fieldDesc, false);
         chunks.push((0, ts_poet_1.code) `${maybeReadonly(options)}${name}${isOptional ? "?" : ""}: ${type}, `);
     });
     if (ctx.options.unknownFields) {
diff --git a/node_modules/ts-proto/build/options.js b/node_modules/ts-proto/build/options.js
index ad268dc..5800123 100644
--- a/node_modules/ts-proto/build/options.js
+++ b/node_modules/ts-proto/build/options.js
@@ -162,7 +162,7 @@ function optionsFromParameter(parameter) {
         }
         else {
             // useJsonWireFormat implies stringEnums=true and useDate=string
-            options.stringEnums = true;
+            // options.stringEnums = true;
             options.useDate = DateOption.STRING;
         }
     }
diff --git a/node_modules/ts-proto/build/types.js b/node_modules/ts-proto/build/types.js
index 3b8567d..0f8f17f 100644
--- a/node_modules/ts-proto/build/types.js
+++ b/node_modules/ts-proto/build/types.js
@@ -490,7 +490,7 @@ function valueTypeName(ctx, typeName) {
                     ? (0, ts_poet_1.code) `readonly string[]`
                     : (0, ts_poet_1.code) `string[]`;
         case ".google.protobuf.Duration":
-            return ctx.options.useJsonWireFormat ? (0, ts_poet_1.code) `string` : undefined;
+            return ctx.options.useJsonWireFormat || true ? (0, ts_poet_1.code) `string` : undefined;
         case ".google.protobuf.Timestamp":
             return ctx.options.useJsonWireFormat ? (0, ts_poet_1.code) `string` : undefined;
         default:
@@ -614,9 +614,7 @@ function toTypeName(ctx, messageDesc, field, ensureOptional = false) {
     // clause, spelling each option out inside a large type union. No need for
     // union with `undefined` here, either.
     const { options } = ctx;
-    return finalize(type, (!isWithinOneOf(field) &&
-        isMessage(field) &&
-        (options.useOptionals === false || options.useOptionals === "none")) ||
+    return finalize(type,
         (isWithinOneOf(field) && options.oneof === options_1.OneofOption.PROPERTIES) ||
         (isWithinOneOf(field) && field.proto3Optional) ||
         ensureOptional);
diff --git a/node_modules/ts-proto/build/utils.js b/node_modules/ts-proto/build/utils.js
index aab20fb..50b33f7 100644
--- a/node_modules/ts-proto/build/utils.js
+++ b/node_modules/ts-proto/build/utils.js
@@ -173,8 +173,8 @@ class FormattedMethodDescriptor {
     static formatName(methodName, options) {
         let result = methodName;
         if (options.lowerCaseServiceMethods || options.outputServices.includes(options_1.ServiceOption.GRPC)) {
-            if (options.snakeToCamel)
-                result = (0, case_1.camelCaseGrpc)(result);
+            // 只处理首字母
+            result = result.substring(0, 1).toLowerCase() + result.substring(1);
         }
         return result;
     }
