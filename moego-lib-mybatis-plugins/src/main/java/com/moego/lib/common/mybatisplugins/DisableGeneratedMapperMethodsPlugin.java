package com.moego.lib.common.mybatisplugins;

import java.util.List;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.Interface;
import org.mybatis.generator.api.dom.java.Method;

/**
 * <p> This plugin disables some mapper methods generated by MyBatis Generator.
 *
 * <p> The following methods are disabled:
 * <ul>
 *     <li>updateByPrimaryKey</li>
 *     <li>insert</li>
 * </ul>
 *
 * <p> Usage:
 * <pre>{@code
 * <plugin type="com.moego.lib.common.mybatisplugins.DisableGeneratedMapperMethodsPlugin"/>
 * }</pre>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
public class DisableGeneratedMapperMethodsPlugin extends PluginAdapter {

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean clientUpdateByPrimaryKeyWithBLOBsMethodGenerated(
            Method method, Interface interfaze, IntrospectedTable introspectedTable) {
        return false;
    }

    @Override
    public boolean clientInsertMethodGenerated(
            Method method, Interface interfaze, IntrospectedTable introspectedTable) {
        return false;
    }
}
