package com.moego.api.thirdparty;

import com.moego.server.payment.params.BookFeeClaimParam;
import com.moego.server.payment.params.PaymentBlockedParam;
import com.moego.server.payment.params.PreAuthMessageParam;
import com.moego.server.payment.params.ReconcileMessageParam;
import com.moego.server.payment.params.VolCheckMessageParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/11/02
 */
@FeignClient(
        name = "payment-slack-client",
        url = "${notification.slackWorkflowsBaseUrl}",
        contextId = "IPaymentSlackClient")
public interface IPaymentSlackClient {
    @PostMapping("${notification.bookingFee.slackBonusClaimUrl}")
    void sendBonusClaim(@RequestBody BookFeeClaimParam params);

    @PostMapping("${notification.reconcile.url}")
    void sendReconcileMessage(@RequestBody ReconcileMessageParam param);

    @PostMapping("${notification.volCheck.url}")
    void sendVolCheckMessage(@RequestBody VolCheckMessageParam param);

    @PostMapping("${notification.preAuth.url}")
    void sendPreAuthMessage(@RequestBody PreAuthMessageParam param);

    @PostMapping("${notification.paymentBlocked.url}")
    void sendPaymentBlockedMessage(@RequestBody PaymentBlockedParam param);
}
