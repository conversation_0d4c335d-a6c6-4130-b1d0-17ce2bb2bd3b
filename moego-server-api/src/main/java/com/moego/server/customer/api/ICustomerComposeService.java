package com.moego.server.customer.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.customer.dto.CustomerQbQueryDto;
import com.moego.server.customer.dto.SaveCustomerPetResultDto;
import com.moego.server.customer.params.CustomerQbQueryParams;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerComposeService {

    /**
     * DONE(account structure): 已支持 company 维度 check & create
     *
     * create  customer and pets  (annotation `@RequestParam` is needed. )
     *
     * @param tokenBusinessId
     * @param tokenStaffId
     * @param customerPetVo
     * @return
     */
    @PostMapping("/service/customer/compose/createCustomerWithPermissionCheck")
    ResponseResult<SaveCustomerPetResultDto> createCustomerWithPermissionCheck(
            @RequestParam(value = "tokenCompanyId", required = false) Long tokenCompanyId,
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @Valid @RequestBody SaveWithPetCustomerVo customerPetVo);

    /**
     * DONE(account structure): 已支持 company 维度 create
     *
     * Internal api for creating customer without permission check
     */
    @PostMapping("/service/customer/compose/createCustomerAndPets")
    SaveCustomerPetResultDto createCustomerAndPets(
            @RequestParam(value = "tokenCompanyId", required = false) Long tokenCompanyId,
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @Valid @RequestBody SaveWithPetCustomerVo customerPetVo);

    /**
     * DONE(account structure): quick book 应该不需要 company 维度，先保留 business 维度，后面有需要再改
     */
    @PostMapping("/service/customer/compose/queryCustomerQbDto")
    List<CustomerQbQueryDto> queryCustomerQbDto(@RequestBody CustomerQbQueryParams qbQueryParams);
}
