/*
 * @since 2021-11-17 14:57:11
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.customer.client;

import com.moego.server.customer.api.IPetBelongingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = "moego-customer-server", url = "${moego.server.url.customer}", contextId = "IPetBelongingClient")
public interface IPetBelongingClient extends IPetBelongingService {}
