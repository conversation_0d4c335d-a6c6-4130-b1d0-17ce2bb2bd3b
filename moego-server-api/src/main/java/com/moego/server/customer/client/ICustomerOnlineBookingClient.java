package com.moego.server.customer.client;

import com.moego.server.customer.api.ICustomerOnlineBookingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-customer-server",
        url = "${moego.server.url.customer}",
        contextId = "ICustomerOnlineBookingClient")
public interface ICustomerOnlineBookingClient extends ICustomerOnlineBookingService {}
