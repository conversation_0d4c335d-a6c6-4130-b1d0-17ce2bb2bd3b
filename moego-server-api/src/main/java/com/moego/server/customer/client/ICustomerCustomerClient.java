/*
 * @since 2021-11-17 14:56:34
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.customer.client;

import com.moego.server.customer.api.ICustomerCustomerService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-customer-server",
        url = "${moego.server.url.customer}",
        contextId = "ICustomerCustomerClient")
public interface ICustomerCustomerClient extends ICustomerCustomerService {}
