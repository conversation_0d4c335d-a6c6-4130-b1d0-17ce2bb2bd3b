package com.moego.server.customer.api;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.response.ResponseResult;
import com.moego.server.customer.dto.CustomerAndPetNoteListDTO;
import com.moego.server.customer.dto.CustomerFilterResult;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerPrimaryDto;
import com.moego.server.customer.dto.CustomerSearchListDto;
import com.moego.server.customer.dto.CustomerSearchVo;
import com.moego.server.customer.dto.CustomerShareInfoDTO;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.customer.dto.LeadLinkAdsDataDTO;
import com.moego.server.customer.dto.LinkAccountDTO;
import com.moego.server.customer.dto.LinkCustomerDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.PreferredTipDto;
import com.moego.server.customer.dto.WebsiteCustomerSummaryDTO;
import com.moego.server.customer.params.ClientListParams;
import com.moego.server.customer.params.ClientListRequest;
import com.moego.server.customer.params.ClientListRequestWithParamsSnapshot;
import com.moego.server.customer.params.CustomerEmailParams;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.customer.params.FindCustomerIdsByKeywordParam;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.server.customer.params.UpdateCustomerInfoParams;
import com.moego.server.customer.params.UpdateCustomerLastServiceParams;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerCustomerService {

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/customer/queryCustomerWithOwnPhone")
    CustomerInfoDto queryCustomerWithOwnPhone(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/queryCustomerWithOwnPhoneList")
    List<CustomerInfoDto> queryCustomerListWithOwnPhone(
            @RequestParam("includeDeleted") Boolean includeDeleted, @RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/queryCustomerList")
    List<MoeBusinessCustomerDTO> queryCustomerList(@RequestBody CustomerIdListParams idListParams);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/queryCustomerListWithDeleted")
    List<MoeBusinessCustomerDTO> queryCustomerListWithDeleted(@RequestBody CustomerIdListParams idListParams);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     */
    @PostMapping("/service/customer/customer/getClientList")
    CustomerFilterResult getClientList(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody CustomerSearchVo searchVo);

    /**
     * TODO(account structure): 这个接口不好兼容，调用方需要改动，迁移后的 company 需要改用 getSmartClientListV2
     *
     * @param clientListParams filters and queries
     * @return client list
     * @see #getSmartClientListV2(ClientListRequest)
     * <p>
     * Get client list by filters and queries
     */
    @Deprecated
    @PostMapping("/service/customer/customer/getSmartClientList")
    CustomerFilterResult getSmartClientList(
            @RequestParam("businessId") Integer businessId, @RequestBody ClientListParams clientListParams);

    /**
     * DONE(account structure)
     * <p>
     * Get client list by filters and queries
     */
    @PostMapping("/service/customer/customer/getSmartClientListV2")
    CustomerFilterResult getSmartClientListV2(@RequestBody ClientListRequest request);

    /**
     * 部分业务场景，会直接存 ClientListParams 的快照
     * 为了保持兼容性，将快照的解析统一收在 Customer 服务
     * 因此提供该接口，接受可解析的快照作为参数
     */
    @PostMapping("/service/customer/customer/getSmartClientListV2ByParamsSnapshot")
    CustomerFilterResult getSmartClientListV2ByParamsSnapshot(@RequestBody ClientListRequestWithParamsSnapshot request);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     * <p>
     * 返回所有customer详情，无论是否删除
     */
    @PostMapping("/service/customer/customer/getCustomerDetailWithPrimary")
    CustomerPrimaryDto getCustomerDetailWithPrimary(@RequestBody CustomerInfoIdParams customerInfoIdParams);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     * <p>
     * 获取用户基础信息： 姓名，avatar，电话，邮箱 （无地址）
     */
    @GetMapping("/service/customer/customer/getCustomerContactInfo")
    GroomingCustomerInfoDTO getCustomerContactInfo(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     */
    @PutMapping("/service/customer/customer/updateCustomer")
    ResponseResult<Boolean> updateCustomer(
            @RequestParam(value = "tokenCompanyId", required = false) Long tokenCompanyId,
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestBody UpdateCustomerInfoParams customerInfoParams);

    /**
     * DONE(account structure)
     */
    @PostMapping("/service/customer/customer")
    Boolean updateCustomer(
            @RequestParam("tokenCompanyId") Long tokenCompanyId,
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestBody UpdateCustomerInfoParams customerInfoParams);

    /**
     * DONE(account structure): 忽略 business id
     */
    @PutMapping("/service/customer/customer/lastServiceTime")
    Boolean updateCustomerLastServiceTime(@RequestBody UpdateCustomerLastServiceParams updateCustomerParams);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     */
    @DeleteMapping("/service/customer/customer/deleteCustomer")
    Integer deleteCustomer(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需兼容
     * <p>
     * Access business_customer table only to get customer basic informations
     */
    @GetMapping("/service/customer/customer/getCustomerWithDeleted")
    MoeBusinessCustomerDTO getCustomerWithDeleted(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure)
     * <p>
     * 根据 phone number 查找 customer owner，用于 ob exist customer login & send code
     *
     * @param businessId
     * @param phoneNumber customer owner phone number
     * @return
     */
    @GetMapping("/service/customer/customer/getCustomerInfoByPhoneNumberForOnlineBooking")
    MoeBusinessCustomerDTO getCustomerInfoByPhoneNumberForOnlineBooking(
            @RequestParam("businessId") Integer businessId, @RequestParam("phoneNumber") String phoneNumber);

    /**
     * DONE(account structure)
     * <p>
     * Full matching first, and fuzzy match the last 9 digits if there is no match
     *
     * @param businessId  bid
     * @param phoneNumber customer owner phone number
     * @return
     */
    @GetMapping("/service/customer/customer/getCustomerInfoByPhoneNumberForNewOB")
    MoeBusinessCustomerDTO getCustomerInfoByPhoneNumberForNewOB(
            @RequestParam("businessId") Integer businessId, @RequestParam("phoneNumber") String phoneNumber);

    /**
     * DONE(account structure): 已兼容 business / company 鉴权
     * <p>
     * 根据联系方式查询用户信息，用于 message 场景。
     * 需要限定： this phoneNumber is Primary number for message
     *
     * @param businessId
     * @param phoneNumber primary phone number
     * @return
     */
    @GetMapping("/service/customer/customer/getCustomerInfoByPhoneNumberForMessage")
    MoeBusinessCustomerDTO getCustomerInfoByPhoneNumberForMessage(
            @RequestParam("businessId") Integer businessId, @RequestParam("phoneNumber") String phoneNumber);

    /**
     * DONE(account structure): 无需兼容
     * <p>
     * Only return customer and business ids, use post to avoid email url encoding
     */
    @PostMapping("/service/customer/customer/queryCustomersByEmail")
    List<MoeBusinessCustomerDTO> queryCustomersByEmail(@RequestBody CustomerEmailParams emailParams);

    /**
     * DONE(account structure):已兼容
     *
     * @param params
     * @return
     */
    @PostMapping("/service/customer/customer/getCustomerWithDeleted")
    CustomerInfoDto getCustomerWithDeleted(@RequestBody CustomerInfoIdParams params);

    /**
     * DONE(account structure):use getCustomerWithDeletedHasCompanyId instead
     *
     * @param params
     * @return
     */
    @Deprecated
    @PostMapping("/service/customer/customer/getCustomerWithDeletedHasBusinessId")
    CustomerInfoDto getCustomerWithDeletedHasBusinessId(@RequestBody CustomerInfoIdParams params);

    /**
     * DONE(account structure): 无需兼容
     */
    @PostMapping("/service/customer/customer/getCustomerWithDeletedHasCompanyId")
    CustomerInfoDto getCustomerWithDeletedHasCompanyId(@RequestBody CustomerInfoIdParams params);

    /**
     * DONE(account structure): 无需兼容
     */
    @GetMapping("/service/customer/customer/getCustomerWithDeletedNoBusinessId")
    CustomerInfoDto getCustomerWithDeletedNoBusinessId(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     */
    @GetMapping("/service/customer/customer/checkingBizByCustomerId")
    Boolean checkingBizByCustomerId(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 已兼容 company / business 鉴权
     */
    @GetMapping("/service/customer/customer/getCustomerIdByFindPetNameAndBreed")
    List<Integer> getCustomerIdByFindPetNameAndBreed(
            @RequestParam("businessId") Integer businessId, @RequestParam("keyword") String keyword);

    /**
     * DONE(account structure): 目前只有两个调用方，一个是 waitlist 的 client 搜索，一个是 message center 的 client 搜索
     * 对于迁移前的用户，按 business 维度搜索，迁移后的用户，按 company 维度搜索，暂不支持 working location 范围搜索
     * <p>
     * Search customer ids by keyword, will search in following tables:
     * <ul>
     *     <li>customer</li>
     *     <li>customer_contact</li>
     *     <li>pet</li>
     * </ul>
     *
     * @param param {@link SearchCustomerIdsParam}
     * @return customer ids
     */
    @PostMapping("/service/customer/customer-ids/search")
    Set<Integer> searchCustomerIds(@RequestBody @Valid SearchCustomerIdsParam param);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/share")
    Map<Integer, CustomerShareInfoDTO> listCustomerShareInfo(@RequestBody CustomerIdListParams idListParams);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/link")
    boolean linkCustomer(@RequestParam("accountId") Long accountId, @RequestBody List<Integer> customerIdList);

    @PostMapping("/service/customer/customer/linkAccountToBrandedCustomer")
    Boolean linkAccountToBrandedCustomer(
            @RequestParam("accountId") Long accountId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/customer/links")
    List<BaseBusinessCustomerIdDTO> getLinkBusinessList(@RequestParam("accountId") Long accountId);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/getLinkAccountId")
    Long getLinkAccountId(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/customer/listLinkAccountId")
    List<LinkAccountDTO> listLinkAccountId(@RequestParam("customerIds") List<Integer> customerIds);

    /**
     * DONE(account structure)
     */
    @GetMapping("/service/customer/customer/account/invite/url")
    String getCustomerAccountInviteUrl(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/customer/getCustomerIdByCustomerCode")
    Integer getCustomerIdByCustomerCode(@RequestParam("customerCode") String customerCode);

    /**
     * DONE(account structure): 无需修改
     * <p>
     * Count customer between startId (inclusive) and endId (inclusive).
     *
     * @param startId startId, null means start at -infinity
     * @param endId   endId, null means end at +infinity
     * @return {@link WebsiteCustomerSummaryDTO}
     */
    @GetMapping("/service/customer/customer/countBetween")
    WebsiteCustomerSummaryDTO countBetween(
            @RequestParam(required = false) @Nullable Integer startId,
            @RequestParam(required = false) @Nullable Integer endId);

    /**
     * DONE(account structure): 没有调用方，可以删除接口，请勿继续使用
     * <p>
     * List customer preferred frequency day
     *
     * @param businessId  business id
     * @param customerIds customer id list
     * @return customer id - frequency day
     */
    @Deprecated
    @PostMapping("/service/customer/customer/frequency/day")
    Map<Integer, Integer> listCustomerFrequencyDay(
            @RequestParam("businessId") Integer businessId, @RequestBody Set<Integer> customerIds);

    /**
     * DONE(account structure): 提升至 company 鉴权
     */
    @PostMapping("/service/customer/customer/unsubscribe")
    void unsubscribeMarketingEmail(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure)：兼容 company / business 鉴权
     */
    @PostMapping("/service/customer/customer/preferredTip")
    PreferredTipDto getPreferredTip(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 直接返回 company 维度的 note （不影响迁移前 business 维度）
     */
    @PostMapping("/service/customer/customer/note")
    CustomerAndPetNoteListDTO getCustomerPetNote(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petIdList") List<Integer> petIdList);

    /**
     * DONE(account structure): 这个接口只有 OB Abandon Record 用，暂时不需要变成 company 维度
     * 如果其他业务需要接入或者修改这个接口，请与 well 沟通
     */
    @PostMapping("/service/customer/customer/findCustomerIdsByKeyword")
    Set<Integer> findCustomerIdsByKeyword(@RequestBody FindCustomerIdsByKeywordParam param);

    /**
     * DONE(account structure): 这个接口只有 C App 用，暂时不需要变成 company 维度
     * 如果其他业务需要接入或者修改这个接口，请与 well 沟通
     */
    @PostMapping("/service/customer/customer/getCustomerByPhone")
    LinkCustomerDTO getCustomerByPhone(@RequestParam Integer businessId, @RequestParam String phoneNumber);

    @PostMapping("/service/customer/customer/getCompanyCustomerByPhone")
    LinkCustomerDTO getCompanyCustomerByPhone(@RequestParam Long companyId, @RequestParam String phoneNumber);

    @GetMapping("/service/customer/customer/search")
    CustomerSearchListDto search(@RequestParam("companyId") Long companyId, @RequestParam String keyword);

    @GetMapping("/service/customer/customer/search/id/full-text")
    Set<Long> searchCustomerIdsByFullText(@RequestParam("companyId") Long companyId, @RequestParam String keyword);

    @PostMapping("/service/customer/lead/link/ads")
    Boolean leadLinkAds(@RequestBody LeadLinkAdsDataDTO LeadLinkAdsDataDTO);
}
