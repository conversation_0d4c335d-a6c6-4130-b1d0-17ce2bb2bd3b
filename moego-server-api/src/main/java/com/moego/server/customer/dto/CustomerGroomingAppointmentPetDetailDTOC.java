package com.moego.server.customer.dto;

import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CustomerGroomingAppointmentPetDetailDTOC {

    private Integer petDetailId;
    private Integer groomingId;
    private Integer petId;
    private Integer serviceId;
    private Integer staffId;
    private String serviceName;
    private Integer serviceTime;
    private BigDecimal servicePrice;

    private Boolean serviceInactive;
    private Boolean serviceDeleted;

    private String petName;
    private String petBreed;

    /**
     * work mode
     * 0 - parallel
     * 1 - sequence
     */
    private Integer workMode;

    private Integer scopeTypePrice;
    private Integer scopeTypeTime;

    private List<GroomingServiceOperationDTO> operationList;
}
