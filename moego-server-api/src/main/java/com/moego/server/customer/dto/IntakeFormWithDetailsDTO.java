package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/11 7:27 PM
 */
@Data
public class IntakeFormWithDetailsDTO {
    private Integer businessId;
    private Integer formId;
    private String title;
    private String uuid;
    private Byte isAllowDelete;
    private Byte isAllowEdit;

    @Schema(description = "是否card选项 显示")
    private Byte isCardShow;

    @Schema(description = "是否card选项 必填")
    private Byte isCardRequired;

    private String themeColor;
    private String message;

    private List<IntakeFormDetailParam> formDetails;
}
