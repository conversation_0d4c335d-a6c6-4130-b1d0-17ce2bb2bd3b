package com.moego.server.customer.client;

import com.moego.server.customer.api.ICustomerProfileRequestService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-customer-server",
        url = "${moego.server.url.customer}",
        contextId = "ICustomerProfileRequestClient")
public interface ICustomerProfileRequestClient extends ICustomerProfileRequestService {}
