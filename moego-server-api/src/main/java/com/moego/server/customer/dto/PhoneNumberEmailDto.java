package com.moego.server.customer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PhoneNumberEmailDto {

    private Integer customerId;
    private String phoneNumber;
    private String email;
    private String firstName;
    private String lastName;
    private Integer preferredBusinessId;

    private String avatarPath;
    private Byte preferredFrequencyType;
    private Integer preferredFrequencyDay;
    private Boolean isNewCustomer;
    private Boolean isProspectCustomer;
    private Boolean hasPetParentAppAccount;
    private Byte inactive;
}
