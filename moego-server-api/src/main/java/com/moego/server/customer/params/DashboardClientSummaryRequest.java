package com.moego.server.customer.params;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardClientSummaryRequest {

    private Integer businessId;
    private String startDate;
    private String endDate;
    private List<Integer> petIds;
    private List<Integer> customerIds;
}
