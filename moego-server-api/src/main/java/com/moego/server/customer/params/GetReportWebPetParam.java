package com.moego.server.customer.params;

import jakarta.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetReportWebPetParam {

    private Integer businessId;
    private Integer reportId;

    /**
     * @deprecated by <PERSON> since 2023/7/13, use {@link #startDate} and {@link #endDate} instead
     */
    @Nullable
    @Deprecated
    private Integer daysToVaccineExpiry;

    @Nullable
    private String startDate;

    @Nullable
    private String endDate;
}
