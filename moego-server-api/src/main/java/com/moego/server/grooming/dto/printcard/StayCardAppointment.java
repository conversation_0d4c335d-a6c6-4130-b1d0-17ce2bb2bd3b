package com.moego.server.grooming.dto.printcard;

import com.moego.common.enums.ServiceItemEnum;
import java.util.List;

public record StayCardAppointment(
        String appointmentStartDate,
        String appointmentEndDate,
        Integer appointmentStartTime,
        Integer appointmentEndTime,
        String lodgingTypeName,
        String lodgingRoom,
        String ticketComments,
        String alertNotes,
        String mainServiceName,
        ServiceItemEnum mainServiceItemType,
        List<SplitLodgingInfo> splitLodgingInfos) {}
