package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IGroomingGroomingReportService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IGroomingGroomingReportClient")
public interface IGroomingGroomingReportClient extends IGroomingGroomingReportService {}
