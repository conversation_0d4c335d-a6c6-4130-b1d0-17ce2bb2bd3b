package com.moego.server.grooming.params;

import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.MembershipParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import com.moego.server.grooming.params.ob.PrepayDetailParams;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BookOnlineSubmitParams {

    @Schema(description = "内部赋值字段")
    private Integer businessId;

    @Schema(description = "内部赋值字段")
    private Long companyId;

    /**
     * 对于嵌套的字段，需要添加@Valid注解使内部的Validation注解生效
     * `@Valid` 必须紧挨着类型名
     */
    @NotNull
    @Valid
    private BookOnlineCustomerParams customerData;

    @NotEmpty
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    /**
     * 快速提交  simplify submit
     * 没有appointment date，没有appointment start time
     */
    @Pattern(message = "Invalid date format, valid example: 2020-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})?$")
    @Schema(description = "日期格式： 2020-02-08")
    private String appointmentDate;

    @Max(1440)
    private Integer appointmentStartTime;

    @Hidden
    private boolean noStartTime = false;

    /**
     * for mobile grooming
     */
    private Byte outOfArea;

    private Integer staffId;
    private String orderId;
    private Byte isAgreePolicy;

    @NotNull
    List<BookOnlineAgreementParams> agreements;

    private String note;

    private BookOnlineCustomerAdditionalParams bookOnlineCustomerAdditionalParams;

    @Schema(description = "prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传")
    private String prepayGuid;

    @Schema(description = "Prepay detail")
    private PrepayDetailParams prepayDetail;

    private OBRequestSourceType sourceType;

    @Schema(description = "PreAuth detail")
    private PreAuthDetailParams preAuthDetail;

    private DiscountCodeParams discountCodeParams;

    private Boolean fromPetParentPortal;

    @Schema(description = "Appointment end date")
    private String endDate;

    @Schema(description = "临时字段，value=base64(customerId)，仅用来提供给 workflow 临时使用，尽快过度到 session 获取，不可靠值")
    private String clientId;

    private MembershipParams membershipParams;
}
