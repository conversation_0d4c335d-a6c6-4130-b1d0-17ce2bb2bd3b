package com.moego.server.grooming.params.report;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class QueryPayrollReportByPageParams {

    private Long companyId;

    @NotEmpty
    private List<Integer> businessIds;

    private Integer staffId;
    private Byte type;
    private String startDate;
    private String endDate;
    private Integer pageNum;
    private Integer pageSize;
}
