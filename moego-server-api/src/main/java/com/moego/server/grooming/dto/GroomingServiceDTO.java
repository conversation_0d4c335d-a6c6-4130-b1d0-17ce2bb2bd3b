package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GroomingServiceDTO {
    /**
     * id
     */
    private Integer id;
    /**
     * 商家id
     */
    private Integer businessId;
    /**
     * 类型id
     */
    private Integer categoryId;
    /**
     * 服务名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 数据类型：1-主服务(service)；2-额外服务(addons)
     */
    private Byte type;
    /**
     * 税费id
     */
    private Integer taxId;
    /**
     * 服务价格
     */
    private BigDecimal price;
    /**
     * 服务时间
     */
    private Integer duration;
    /**
     *
     */
    private Byte inactive;
    /**
     * 排序值
     */
    private Integer sort;
    /**
     * color code
     */
    private String colorCode;
    /**
     * 1 正常 2删除
     */
    private Byte status;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long updateTime;
    /**
     * booking online 是否显示价格
     * 0 do not show price
     * 1 show fixd service price
     * 2 show price with "starting at"
     * 3 show price sa "Varies"
     */
    private Byte showBasePrice;
    /**
     * 0-not  1-yes
     */
    private Byte bookOnlineAvailable;
    /**
     * 是否全选staff
     */
    private Byte isAllStaff;
    /**
     * filtered by breed, 0-all breed, 1-filter by selected breeds, see table moe_grooming_service_type_breed_binding
     */
    private Byte breedFilter;
    /**
     * filtered by weight, 0-all weight, 1-filter by weight range
     */
    private Byte weightFilter;
    /**
     * weight filter down limit
     */
    private BigDecimal weightDownLimit;
    /**
     * weight filter up limit
     */
    private BigDecimal weightUpLimit;
    /**
     * filtered by coat, 0-all coat, 1-filter by selected coat
     */
    private Integer coatFilter;
    /**
     *
     */
    private Long companyId;
    /**
     *
     */
    private Integer isAllLocation;
    /**
     *
     */
    private String images;
    /**
     * 1 - grooming, 2 - boarding, 3 - daycare
     */
    private Integer serviceItemType;
    /**
     * 1 - per session, 2 - per night, 3 - per hour
     */
    private Integer priceUnit;
    /**
     *
     */
    private Boolean addToCommission;
    /**
     *
     */
    private Boolean canTip;
    /**
     *
     */
    private Boolean requireDedicatedStaff;
    /**
     *
     */
    private Boolean requireDedicatedLodging;
    /**
     *
     */
    private Boolean lodgingFilter;
    /**
     * allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
     */
    private String allowedLodgingList;
    /**
     * only for add-on
     */
    private Boolean serviceFilter;
    /**
     * allowed pet size list, only when service_filter is true
     */
    private String allowedPetSizeList;
    /**
     *
     */
    private Boolean petSizeFilter;
    /**
     * max duration in minutes, only for daycare
     */
    private Integer maxDuration;
}
