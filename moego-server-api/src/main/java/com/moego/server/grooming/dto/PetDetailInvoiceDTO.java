package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PetDetailInvoiceDTO {

    private Integer petDetailId;
    private Integer serviceId;
    private String serviceName;
    private Integer serviceType;
    private Integer serviceItemType;
    private Integer taxId;
    private String serviceDescription;
    private BigDecimal servicePrice;
    /**
     * 用于计算 order line item 的 quantity
     */
    private Integer priceUnit;

    private Integer petId;
    private String startDate;
    private String endDate;
    private Long startTime;
    private Long endTime;
}
