package com.moego.server.grooming.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/4
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerApptCountDTO {

    private Integer customerId;
    private Integer totalApptCount;
    private Integer totalApptAndRequestsCount;
    private Integer finishedApptCount;
}
