package com.moego.server.grooming.params.appointment.conflict;

import com.moego.server.grooming.params.RepeatStaffInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class ConflictCheckParams {

    @Schema(description = "当前 appointment id，用于检查冲突时排除自己，新预约不用传")
    private Integer appointmentId;

    @NotNull
    @Schema(description = "检查冲突的日期")
    private String date;

    @Schema(description = "检查冲突的时间")
    private Integer startTime;

    @Schema(description = "检查冲突的时长")
    private Integer duration;

    @NotEmpty
    @Valid
    @Schema(description = "检查冲突的 staff 信息")
    private List<@Valid RepeatStaffInfoParams> staffConflictCheckParams;
}
