package com.moego.server.grooming.dto.ob;

import com.moego.server.grooming.dto.ApplyServiceChargeDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CalculateServiceAmountDTO {

    private BigDecimal serviceAmount;
    private BigDecimal taxAmount;

    private BigDecimal serviceChargeAmount;
    private BigDecimal discountAmount;

    @Deprecated(since = "2025-05-30")
    private List<ServiceChargeDTO> serviceChargeList;

    private List<ApplyServiceChargeDTO> applyServiceChargeList;

    /**
     * Pet id - List of services (contains saved price and saved duration)
     *
     * @deprecated by <PERSON>, 这个字段不应该出现在这里，多个 new pet 情况下，会出现覆盖的问题，使用 {@link #services} 替代
     */
    @Deprecated(since = "2024/11/14")
    private Map<Integer, List<MoeGroomingServiceDTO>> petServicesMap;

    /**
     * 为了解决 {@link #petServicesMap} 的问题，返回 customized service。
     *
     * <p> 这个属性感觉不应该存在，计算 amount 和查询 customized service 应该是两个不同的方法。
     *
     * <p> 添加这个属性只为了 fix {@link #petServicesMap} 存在的问题，并且不对现有代码进行大量重构。
     */
    private List<MoeGroomingServiceDTO> services;

    private List<Long> usedMembershipIds;
}
