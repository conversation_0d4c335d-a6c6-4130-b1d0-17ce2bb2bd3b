package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportTemplateParams {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Integer updateBy;

    @Size(max = 50)
    @Schema(description = "thank you message in grooming report, moe_grooming_report_template.thank_you_message")
    private String thankYouMessage;

    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")
    @Schema(description = "theme color: #xxxxxx or #xxx, moe_grooming_report_template.theme_color")
    private String themeColor;

    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")
    @Schema(description = "light theme color: #xxxxxx or #xxx, moe_grooming_report_template.light_theme_color")
    private String lightThemeColor;

    @Size(max = 30)
    @Schema(description = "default theme code")
    private String themeCode;

    @Schema(description = "is showcase section show")
    private Boolean showShowcase;

    @Schema(description = "show overall feedback section")
    private Boolean showOverallFeedback;

    @Schema(description = "is before photo required when edit grooming report")
    private Boolean requireBeforePhoto;

    @Schema(description = "is after photo required when edit grooming report")
    private Boolean requireAfterPhoto;

    @Schema(description = "show pet condition section when edit or show on client page")
    private Boolean showPetCondition;

    @Schema(description = "show staff name")
    private Boolean showServiceStaffName;

    @Schema(description = "is next appointment section show")
    private Boolean showNextAppointment;

    @Schema(description = "next appointment datetime format type: 1-only date, 2-date and time")
    @Max(2)
    @Min(1)
    private Byte nextAppointmentDateFormatType;

    @Schema(description = "show review booster on report page")
    private Boolean showReviewBooster;

    @Schema(description = "show yelp review icon")
    private Boolean showYelpReview;

    @Schema(description = "yelp review icon jump link")
    @Size(max = 2000)
    private String yelpReviewLink;

    @Schema(description = "show google review icon")
    private Boolean showGoogleReview;

    @Schema(description = "google review icon jump link")
    @Size(max = 2000)
    private String googleReviewLink;

    @Schema(description = "show facebook review icon")
    private Boolean showFacebookReview;

    @Schema(description = "facebook review icon jump link")
    @Size(max = 2000)
    private String facebookReviewLink;

    @Schema(description = "customized grooming report title")
    @Size(max = 27)
    private String title;

    @Valid
    @Schema(description = "question list: overall feedback, pet conditions")
    private TemplateQuestionParams questions;

    @Data
    public static class TemplateQuestionParams {

        @Valid
        private List<GroomingReportQuestionParams> feedbacks;

        @Valid
        private List<GroomingReportQuestionParams> petConditions;
    }
}
