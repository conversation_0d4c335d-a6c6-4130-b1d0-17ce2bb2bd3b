package com.moego.server.grooming.params.appointment;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record StaffUpcomingAppointmentCountParams(
        @JsonIgnore Integer tokenCompanyId,
        @Schema(description = "需要查 upcoming appointment count 的 staff id，必传") @NotNull @Positive Long staffId,
        @Schema(description = "需要查 upcoming appointment count 的 business id list，不传默认查询 staff 所有 working location")
                List<Long> businessIds) {}
