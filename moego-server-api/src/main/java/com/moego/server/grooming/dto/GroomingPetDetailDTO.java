package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class GroomingPetDetailDTO {

    private Integer id;
    private Integer groomingId;
    private Integer petId;
    private Integer staffId;
    private Integer serviceId;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Long endTime;
    private Integer scopeTypePrice;
    private Integer scopeTypeTime;

    private Integer starStaffId;

    private String serviceName;
    private Integer serviceType;
    private Integer serviceStatus;

    private String staffFirstName;
    private String staffLastName;
    private String petName;
    private Integer petLifeStatus;

    private Boolean enableOperation;
    private String colorCode;

    /**
     * work mode
     * 0 - parallel
     * 1 - sequence
     */
    private Integer workMode;

    /**
     * operation record list
     */
    private List<GroomingServiceOperationDTO> operationList;

    /**
     * service item type
     */
    private Integer serviceItemType;

    /**
     * lodging id
     */
    private Long lodgingId;

    private String startDate;
    private String endDate;

    // 内部计算用。暂时不用对外暴露
    private Integer priceUnit;
    private String specificDates;
    private Long associatedServiceId;
    // 本条 pet detail 等价的 order line item 中 quantity数量
    private Integer quantity;
    /**
     * ServiceOverrideType 用的是 protobuf enum，会导致前端生成两份 enum，这里返回 int
     */
    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType priceOverrideType;

    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType durationOverrideType;

    private Date updatedAt;

    private Integer quantityPerDay;

    private Boolean requireDedicatedStaff;

    /**
     * date type
     * 1-every day except checkout day, 2-specific date, 3-date point, 4-every day include checkout day
     */
    private Integer dateType;

    /**
     * order_line_item.id
     */
    private Long orderLineItemId;

    /**
     * petdetail_xxx
     * evaluation_xxx
     */
    private String externalId;
}
