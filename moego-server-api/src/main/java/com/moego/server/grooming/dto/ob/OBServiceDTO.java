package com.moego.server.grooming.dto.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/12
 */
@Data
public class OBServiceDTO {

    @Schema(description = "existing client id")
    private Integer customerId;

    @NotNull
    @Schema(description = "business id")
    private Integer businessId;

    @NotEmpty
    @Schema(description = "filter service by pet info")
    private List<@NotNull OBPetDataDTO> petDataList;
}
