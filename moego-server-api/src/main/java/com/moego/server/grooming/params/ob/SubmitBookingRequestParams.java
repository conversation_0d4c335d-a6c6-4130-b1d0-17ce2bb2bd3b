package com.moego.server.grooming.params.ob;

import com.moego.server.grooming.params.BookOnlineAgreementParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.OBRequestSourceType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/3
 */
@Data
public class SubmitBookingRequestParams {

    private Integer businessId;

    private Integer customerId;

    private Integer staffId;

    private String appointmentDate;

    private Integer appointmentStartTime;

    private String orderId;

    List<BookOnlineAgreementParams> agreements;

    private String additionalNote;

    @NotEmpty
    @Valid
    private List<@NotNull BookOnlinePetParams> petData;

    private Byte outOfArea;

    private OBRequestSourceType sourceType;

    private String prepayGuid;

    private CardOnFileParams cardOnFile;

    private PrepayDetailParams prepayDetail;

    private PreAuthDetailParams preAuthDetail;

    private String discountCode;

    private Integer addressId;
}
