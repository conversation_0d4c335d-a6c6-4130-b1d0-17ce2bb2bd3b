package com.moego.server.grooming.dto.appointment.history;

import com.moego.server.grooming.enums.ActionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class AppointmentActionHistory {
    @Schema(description = "操作类型")
    private ActionTypeEnum actionType;

    private Integer operatorId;
    private Long operateTime;

    /**
     * 改变前后的状态，仅针对 actionType 为 UPDATE_STATUS 的情况有意义
     */
    private UpdateStatusLogDTO updateStatusLog;

    /**
     * 改变前后的时间，仅针对 actionType 为 CHANGE_CHECK_IN_TIME 和 CHANGE_CHECK_OUT_TIME 的情况有意义
     */
    private ChangeTimeLogDTO changeTimeLog;

    /**
     * 发送通知的详情，仅针对 actionType 为 SEND_NOTIFICATION 的情况有意义
     */
    private SendNotificationLogDTO sendNotificationLog;

    /**
     * 取消的原因，仅针对 actionType 为 CANCEL 的情况有意义
     */
    @Schema(description = "取消的原因，仅针对 actionType 为 CANCEL 的情况有意义")
    private String cancelReason;

    /**
     * 取消的详情，仅针对 actionType 为 CANCEL 的情况有意义
     */
    private CancelLogDTO cancelLogDTO;

    /**
     * 客户回复的详情，仅针对 actionType 为 CUSTOMER_REPLY 的情况有意义
     */
    private CustomerReplyLogDTO customerReplyLogDTO;

    /**
     * 创建详情，仅针对 actionType 为 CREATE 的情况有意义
     */
    private CreateLogDTO createLogDTO;

    /**
     * 通知发送结果更新，主要来源是第三方系统回调
     */
    private NotificationUpdateDTO notificationUpdateDTO;

    /**
     * Auto rollover log, 仅针对 actionType 为 AUTO_ROLLOVER 的情况有意义
     */
    private AutoRolloverLogDTO autoRolloverLog;

    /**
     * OB client update log, 仅针对 actionType 为 CLIENT_UPDATE 的情况有意义
     */
    private ClientUpdateLog clientUpdateLog;

    public record ClientUpdateLog(List<ServiceDetail> addedServices) {
        public record ServiceDetail(String petName, String serviceName) {}
    }
}
