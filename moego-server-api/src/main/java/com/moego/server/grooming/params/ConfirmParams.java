package com.moego.server.grooming.params;

import com.moego.server.message.enums.MessageMethodTypeEnum;
import lombok.Data;

@Data
public class ConfirmParams {

    private Integer id; // appointmentId
    private Integer businessId;
    private Integer accountId; // confirm/change by account
    private Byte confirmByType; // GroomingAppointmentEnum.CONFIRM_TYPE_BY_*
    private MessageMethodTypeEnum confirmByMethod;
}
