package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IClientPortalAppointmentService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IClientPortalAppointmentClient")
public interface IClientPortalAppointmentClient extends IClientPortalAppointmentService {}
