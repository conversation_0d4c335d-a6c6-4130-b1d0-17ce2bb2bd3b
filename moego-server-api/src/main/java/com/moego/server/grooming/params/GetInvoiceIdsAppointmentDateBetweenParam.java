package com.moego.server.grooming.params;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetInvoiceIdsAppointmentDateBetweenParam {
    @NotNull
    private Integer businessId;

    @Nullable
    private Integer staffId;

    @Nullable
    private String startDate;

    @Nullable
    private String endDate;
}
