package com.moego.server.grooming.dto;

import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AbandonRecordDTO {
    private Integer id;
    private Integer businessId;
    private String bookingFlowId;
    private Integer customerId;
    private String referer;
    private String phoneNumber;
    private String firstName;
    private String lastName;
    private String email;
    private Integer referralSourceId;
    private Integer preferredGroomerId;
    private Integer preferredFrequencyDay;
    private Byte preferredFrequencyType;
    private String preferredDay;
    private String preferredTime;
    private String customerQuestionAnswers;
    private Integer addressId;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String zipcode;
    private String country;
    private String lat;
    private String lng;
    /**
     * @see AbandonStep
     */
    private String abandonStep;

    private Long abandonTime;
    /**
     * @see AbandonStatus
     */
    private String abandonStatus;

    private Long lastTextedTime;
    private Long lastEmailedTime;
    private Long recoveryType;
    private Long recoveryTime;
    private Integer staffId;
    private Integer appointmentId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private String agreementInfo;
    /**
     * @see LeadType
     */
    private String leadType;

    private Boolean isDeleted;
    private Byte deleteType;
    private Date createTime;
    private Date updateTime;
    private String additionalNote;
    private Boolean isSendScheduleMessage;
    private String careType;
    private Boolean isNotificationSent;

    /**
     * 使用小写是为了兼容老代码 :)
     *
     * <p color="red">枚举用大写！
     */
    public enum LeadType {
        new_visitor,
        existing_client
    }

    /**
     * 使用小写是为了兼容老代码 :)
     *
     * <p color="red">枚举用大写！
     */
    public enum AbandonStep {
        welcome_page,
        basic_info,
        select_care_type,
        select_address,
        select_pet,
        select_date,
        select_service,
        select_groomer,
        select_time,
        additional_pet_info,
        personal_info,
        card_on_file,
        prepay,
        pre_auth,
        submit_appt;

        public static List<AbandonStep> listRecoverableSteps() {
            return List.of(
                    select_address,
                    select_pet,
                    select_service,
                    select_date,
                    select_groomer,
                    select_time,
                    additional_pet_info,
                    personal_info,
                    card_on_file,
                    prepay,
                    pre_auth);
        }
    }

    /**
     * 使用小写是为了兼容老代码 :)
     *
     * <p color="red">枚举用大写！
     */
    public enum AbandonStatus {
        abandoned,
        contacted,
        recovered;

        public static final Set<String> NOT_RECOVERED_STATUSES = Set.of(abandoned.name(), contacted.name());
    }
}
