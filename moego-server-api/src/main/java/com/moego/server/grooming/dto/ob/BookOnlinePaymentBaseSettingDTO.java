package com.moego.server.grooming.dto.ob;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class BookOnlinePaymentBaseSettingDTO {
    @NotNull
    private Byte paymentType;

    @NotNull
    private Byte prepayType;

    @NotNull
    private Byte prepayTipEnable;

    @NotNull
    private Byte depositType;

    @NotNull
    private Integer depositPercentage;

    @NotNull
    private BigDecimal depositAmount;

    @NotNull
    private Byte preAuthTipEnable;

    @NotNull
    private String prepayPolicy;

    @NotNull
    private String preAuthPolicy;

    @NotNull
    private String cancellationPolicy;
}
