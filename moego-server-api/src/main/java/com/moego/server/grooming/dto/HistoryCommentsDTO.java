package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HistoryCommentsDTO {
    private Integer businessId;
    private Integer groomingId;

    private String comments;
    private Long createTime;
    private Long updateTime;
    // appointment date
    private String appointmentDate;
    private String appointmentEndDate;

    @Schema(description = "编辑者")
    private Integer editor;

    private String editorLastName;
    private String editorFirstName;

    @Schema(description = "创建者")
    private Integer creator;

    private String creatorFirstName;
    private String creatorLastName;
}
