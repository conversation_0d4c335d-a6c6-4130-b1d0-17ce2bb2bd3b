package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存 Grooming Report 后返回对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroomingReportInfoForInputDTO {

    @Schema(description = "是否需要更新template")
    private Boolean needRefreshTemplate;

    @Schema(description = "保存成功后或者 merge 最新 template 的 grooming report info")
    private GroomingReportInfoDTO groomingReportInfo;
}
