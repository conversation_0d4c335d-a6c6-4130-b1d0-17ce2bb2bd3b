package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.quickbooks.ListQuickBookInvoiceDTO;
import com.moego.server.grooming.params.SyncAppointmentParams;
import com.moego.server.grooming.params.quickbook.ListQuickBookInvoiceParams;
import com.moego.server.payment.dto.ListQuickBookSettingDTO;
import com.moego.server.payment.params.ListQuickBookSettingParams;
import com.moego.server.payment.params.UpdateQuickBookSettingParams;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IQuickBooksService {
    @RequestMapping("/service/grooming/qb/begin/task")
    String syncTaskBegin();

    @RequestMapping("/service/grooming/qb/syncGrooming")
    boolean syncGroomingAppointment(@RequestParam("groomingId") Integer groomingId);

    @PostMapping("/service/grooming/qb/invoice/check")
    void invoiceCheckQbSyncStatus(
            @RequestParam(value = "tokenBusinessId", required = false) Integer tokenBusinessId,
            @RequestParam("invoiceId") Integer invoiceId);

    @PostMapping("/service/grooming/qb/addRedisSyncGroomingData")
    Boolean addRedisSyncGroomingData(@RequestBody SyncAppointmentParams params);

    @PostMapping("/service/grooming/qb/addNeeSyncPaymentToQB")
    Long addNeedSyncPaymentToQB(
            @RequestParam("businessId") Integer businessId, @RequestParam("paymentId") Integer paymentId);

    @PostMapping("/service/grooming/qb/setting/list")
    ListQuickBookSettingDTO listQuickBookSetting(@RequestBody @Validated ListQuickBookSettingParams params);

    @PostMapping("/service/grooming/qb/setting/update")
    Integer updateQuickBookSetting(@RequestBody @Validated UpdateQuickBookSettingParams params);

    @PostMapping("/service/grooming/qb/compensation/time-range")
    Integer compensationTimeRange(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startTime") Long startTime,
            @RequestParam("endTime") Long endTime);

    @PostMapping("/service/grooming/qb/compensation/invoice")
    List<String> compensationInvoice(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> invoiceIds);

    @PostMapping("/service/grooming/qb/list/invoice")
    ListQuickBookInvoiceDTO listQuickBookInvoice(@RequestBody @Validated ListQuickBookInvoiceParams params);
}
