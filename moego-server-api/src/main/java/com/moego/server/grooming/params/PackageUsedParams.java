package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PackageUsedParams {

    @NotNull
    private Integer customerId;

    private Integer businessId;
    private Integer invoiceId;

    @NotNull
    private Integer packageId;

    @NotNull
    private Integer packageServiceId;

    @NotNull
    private Integer serviceId;

    @NotNull
    private Integer quantity;

    @NotNull
    private Integer groomingId;
}
