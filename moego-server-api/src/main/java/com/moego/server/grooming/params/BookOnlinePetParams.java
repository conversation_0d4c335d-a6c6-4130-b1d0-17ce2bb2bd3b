package com.moego.server.grooming.params;

import com.moego.common.params.VaccineParams;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.server.grooming.dto.PetFeedingDTO;
import com.moego.server.grooming.dto.PetMedicationDTO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BookOnlinePetParams {

    @Size(max = 255)
    private String avatarPath;

    @NotNull
    @Size(max = 50)
    private String petName;

    @NotNull
    private String breed;

    private Byte breedMix;
    private Integer petTypeId;

    @Schema(description = "1: 雄 2：雌性")
    private Byte gender;

    @Pattern(
            message = "Invalid birthday format, valid ex:2020-02-08",
            regexp =
                    "^$|^((\\d{2}(([02468][048])|([13579][26]))[\\-]?((((0?[13578])|(1[02]))[\\-]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-]?((((0?[13578])|(1[02]))[\\-]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]?((0?[1-9])|(1[0-9])|(2[0-8]))))))$")
    @Schema(description = "日期格式： 2020-02-08")
    private String birthday;

    @Size(max = 50)
    private String weight;

    @Size(max = 50)
    private String fixed;

    @Size(max = 50)
    private String behavior;

    @Size(max = 50)
    private String hairLength;

    private Byte expiryNotification;

    @Size(max = 100)
    private String vetName;

    @Size(max = 30)
    private String vetPhone;

    @Size(max = 100)
    private String vetAddress;

    @Size(max = 100)
    private String emergencyContactName;

    @Size(max = 30)
    private String emergencyContactPhone;

    @Size(max = 3000)
    private String healthIssues;

    /**
     * pet primary id
     */
    private Integer petId;
    /**
     * vaccineId expirationDate
     */
    private List<VaccineParams> vaccineList;
    /**
     * pet images
     */
    private String petImage;
    /**
     *  ob service
     */
    private Boolean isSelected;

    private Integer serviceId;
    /**
     * @deprecated by Freeman, use {@link #addons} instead
     */
    @Deprecated(since = "2024/11/13")
    private List<Integer> addOnIds;
    /**
     * custom pet question
     */
    private Map<String, Object> petQuestionAnswers;

    @Nullable
    private String startDate;

    @Nullable
    private String endDate;

    @Nullable
    private Integer startTime;

    @Nullable
    private Integer endTime;

    @Nullable
    private List<Addon> addons;

    @Data
    public static class Addon {
        private int id;

        /**
         * 专门给 daycare addon 用
         */
        @Nullable
        private Boolean isEveryDay;

        @Nullable
        private List<String> dates;

        @Nullable
        private Integer quantityPerDay;

        /**
         * @see PetDetailDateType
         */
        @Nullable
        private Integer dateType;

        @Nullable
        private String startDate;
    }

    /**
     * 内部使用，不对外暴露
     *
     * <p> 在计算 pricing rule 时，会依赖 pet id，但是在 new client OB flow 里面是没有 pet id 的，因为需要生成一个 virtual pet id，这个字段用来表示 {@link #petId} 是否是 virtual pet id
     */
    @Hidden
    private boolean isVirtualPetId;

    @Nullable
    private Integer staffId;

    @Schema(description = "pet medication schedule")
    private List<PetMedicationDTO> petMedications;

    @Schema(description = "pet feeding schedule")
    private List<PetFeedingDTO> petFeedings;
}
