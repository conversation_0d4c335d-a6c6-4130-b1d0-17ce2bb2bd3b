package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class BookOnlineDepositDTO {

    private Integer id;
    private Integer businessId;
    private String guid;
    private Integer groomingId;
    private Integer paymentId;
    private Byte status;
    private BigDecimal amount;
    private BigDecimal bookingFee;
    private BigDecimal tipsAmount;
    private BigDecimal convenienceFee;
    private BigDecimal serviceTotal;
    private BigDecimal taxAmount;
    private BigDecimal serviceChargeAmount;

    private Byte depositType;
    private BigDecimal discountAmount;

    private Long bookingRequestId;

    private PreAuth preauthInfo;

    @Data
    public static class PreAuth {
        private Double tipsAmount;
        private Double serviceTotal;
        private Double taxAmount;
        private Double serviceChargeAmount;
        private String paymentMethodId;
        private String cardNumber;
        private String chargeToken;
    }
}
