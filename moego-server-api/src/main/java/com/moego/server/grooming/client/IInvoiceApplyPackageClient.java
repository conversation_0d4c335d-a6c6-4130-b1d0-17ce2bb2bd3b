package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IInvoiceApplyPackageService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IInvoiceApplyPackageClient")
public interface IInvoiceApplyPackageClient extends IInvoiceApplyPackageService {}
