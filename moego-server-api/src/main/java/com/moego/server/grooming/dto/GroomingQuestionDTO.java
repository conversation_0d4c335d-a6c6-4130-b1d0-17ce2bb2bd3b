package com.moego.server.grooming.dto;

import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.AcceptPetEntryType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GroomingQuestionDTO {

    private Integer businessId;
    private Integer id;
    private String question;
    private String placeholder;
    private Byte isShow;
    private Byte isRequired;
    private Byte type;
    private Byte isAllowDelete;
    private Byte isAllowChange;
    private Byte isAllowEdit;
    private Integer sort;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private Byte questionType;
    private String extraJson;
    private String key;
    /**
     * @see AcceptCustomerType
     * @deprecated by <PERSON> since 2025/7/9, use {@link #newClientAccessMode} and {@link #existingClientAccessMode} instead
     */
    @Deprecated
    private Integer acceptCustomerType;
    /**
     * @see AcceptPetEntryType
     * @deprecated by <PERSON> since 2025/6/24, use {@link #newPetAccessMode} and {@link #existingPetAccessMode} instead
     */
    @Deprecated
    private Integer acceptPetEntryType;
    /**
     * @see com.moego.idl.models.online_booking.v1.NewPetAccessMode
     */
    private Integer newPetAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.ExistingPetAccessMode
     */
    private Integer existingPetAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.NewClientAccessMode
     */
    private Integer newClientAccessMode;
    /**
     * @see com.moego.idl.models.online_booking.v1.ExistingClientAccessMode
     */
    private Integer existingClientAccessMode;

    public interface Type {
        byte PET = 1;
        byte PET_OWNER = 2;
    }
}
