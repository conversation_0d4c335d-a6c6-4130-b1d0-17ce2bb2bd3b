package com.moego.server.grooming.params;

import java.util.List;
import lombok.Data;

@Data
public class EditPetDetailStaffCommissionParam {

    Long orderId;
    List<EditPetDetailStaffCommissionItem> editPetDetailStaffCommissionItemList;

    @Data
    public static class EditPetDetailStaffCommissionItem {
        String orderItemType;
        Long orderItemId;
        Long petDetailId;
        Long serviceId;
        Long petId;
        Long staffId;
        Long businessId;
        Long companyId;
        List<EditPetDetailStaffCommissionOperationItem> operationItemList;
    }

    @Data
    public static class EditPetDetailStaffCommissionOperationItem {
        Long staffId;
        Double ratio;
        Integer duration;
        String operationName;
    }
}
