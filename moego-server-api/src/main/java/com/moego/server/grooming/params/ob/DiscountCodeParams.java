package com.moego.server.grooming.params.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class DiscountCodeParams {

    @Schema(description = "Discount code id")
    private Long discountCodeId;

    @Schema(description = "Discount amount")
    private BigDecimal discountAmount;

    @Schema(description = "Discount code")
    private String discountCode;
}
