package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.order.OrderItemType;
import com.moego.server.payment.dto.RefundChannelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class InvoiceSummaryDTO {

    private Integer id;
    private Integer businessId;
    private Integer groomingId;
    private String type;
    private Integer customerId;
    private String customerFirstName;
    private String customerLastName;
    private String customerEmail;
    private Long completeTime;
    private String orderType;
    private String fulfillmentStatus;
    private String extraChargeReason;
    private BigDecimal subTotalAmount;
    private BigDecimal discountAmount;
    private BigDecimal depositAmount;
    private BigDecimal discountedSubTotalAmount;
    private BigDecimal discountRate;
    private String discountType;

    @Schema(
            description =
                    "add discount details for client credit story, same as com.moego.server.grooming.dto.OrderInvoiceSummaryDTO.discountList")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderInvoiceSummaryDTO.DiscountDTO> discountList;

    private BigDecimal tipsAmount;
    private BigDecimal tipsRate;
    private BigDecimal taxAmount;
    private String tipsType;
    private BigDecimal totalAmount;
    private BigDecimal totalAmountWithFee;
    private BigDecimal paymentAmount;
    private BigDecimal paidAmount;
    private BigDecimal remainAmount;
    private BigDecimal refundedAmount;
    private BigDecimal discountedSubTotalNoTipAmount;
    private BigDecimal amountDue;
    private BigDecimal totalCollected;
    private String status;
    private Integer createBy;
    private Integer updateBy;
    private Long createTime;
    private Long updateTime;
    private BigDecimal convenienceFee;
    private List<ItemDTO> items;
    private List<PackageServiceDTO> appliedPackageServices;

    private RefundChannelDTO refundChannel;

    @Schema(description = "提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client) ")
    private BigDecimal initProcessingFee;

    @Schema(description = "本次订单如果是prepay，关联的booking fee，用于C端展示")
    private BigDecimal bookingFeeAmount;

    @JsonIgnore
    private Integer bookingFeePaymentId; // 记录支付booking fee的payment id

    @Schema(description = "本次支付是否需要增加processing fee")
    private Boolean requiredProcessingFee;

    @Schema(description = "for deposit")
    DepositDto depositInfo;

    private PaymentSummary paymentSummary;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String businessName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long checkInTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long checkOutTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Byte appointmentStatus;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String appointmentDate;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer appointmentStartTime;

    private Integer appointmentEndTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long appointmentCheckInTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long appointmentCheckOutTime;

    public String getConfirmationId() {
        return String.format("%d%08d", createTime, id);
    }

    public BigDecimal getOriginalAmount() {
        if (items == null || items.size() == 0) {
            return BigDecimal.ZERO;
        }

        return items.stream()
                .filter(item -> OrderItemType.ITEM_TYPE_SERVICE.getType().equals(item.getType())
                        || OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType().equals(item.getType()))
                .map(t -> t.getServiceUnitPrice().multiply(new BigDecimal(t.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Data
    public static class ItemDTO {

        private Integer id;
        private Integer invoiceId;
        private Integer serviceId;
        private Integer petDetailId;

        @Schema(description = "item类型：service、product、package、noshow")
        private String type;

        private String serviceName;
        private String serviceDescription;
        private BigDecimal serviceUnitPrice;
        private Integer taxId;
        private BigDecimal taxRate;
        private Integer quantity;
        private Integer purchasedQuantity;
        private BigDecimal totalListPrice;
        private BigDecimal totalSalePrice;
        private BigDecimal discountAmount;
        private BigDecimal taxAmount;
        private Long createTime;
        private Long updateTime;
        private List<PetDetailDTO> petDetails;
    }

    @Data
    public static class PetDetailDTO {

        private Integer petId;
        private String petName;
        private Integer serviceId;
        private Integer serviceTime;
        private BigDecimal servicePrice;
        private Long startTime;
        // pet breed name
        private String petBreed;
        // staff信息
        private String staffFirstName;
        private String staffLastName;
    }

    @Data
    public static class PackageServiceDTO {

        private Integer id;
        private Integer invoiceId;
        private Integer invoiceItemId;
        private Integer packageId;
        private Integer serviceId;
        private BigDecimal servicePrice;
        private Integer packageServiceId;
        private String packageName;
        private String serviceName;
        private Integer quantity;
        private Long createTime;
        private Long updateTime;
    }
}
