package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RepeatStaffInfoParams {

    @Schema(description = "员工id 校验是否冲突用，Grooming 用")
    private Integer staffId;

    @NotNull
    @Schema(description = "服务开始时间 校验是否冲突用")
    private Integer startTime;

    @Schema(description = "服务持续时间 校验是否冲突用，Grooming 用")
    private Integer serviceTime;

    @Schema(description = " 服务结束时间，Daycare 用")
    private Integer endTime;
}
