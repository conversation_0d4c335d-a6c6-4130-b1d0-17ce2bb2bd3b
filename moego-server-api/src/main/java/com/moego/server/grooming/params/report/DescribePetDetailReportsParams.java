package com.moego.server.grooming.params.report;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribePetDetailReportsParams(
        Set<Integer> ids,
        Set<Integer> groomingIds,
        Set<Integer> staffIds,
        Set<Integer> petIds,
        <PERSON>olean includeDeleted,
        @NotNull @Valid Pagination pagination) {}
