package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportPaymentSummaryDto {

    private String paymentMethod;
    private BigDecimal sumAmount;

    private BigDecimal apptSale; // appointment 类型对应的金额
    private BigDecimal noShowFee; // no show fee 类型对应的金额
    private Integer ticketNum;

    // 上面是老字段，以下是新字段，需要兼容，sumAmount -> totalCollectedRev, apptSale -> apptCollectedRev, noShowFee -> noShowCollectedRev
    private BigDecimal totalCollectedRevenue; // 总收入
    private BigDecimal totalPayment; // 总收款
    private BigDecimal totalRefund; // 总退款
    private BigDecimal noShowCollectedRevenue; // no show fee实收
    private BigDecimal noShowPaidAmount; // no show fee收款
    private BigDecimal noShowRefundAmount; // no show fee退款
    private BigDecimal apptCollectedRevenue; // appt实收
    private BigDecimal apptPaidAmount; // appt收款
    private BigDecimal apptRefundAmount; // appt退款

    private BigDecimal collectedServicePrice;
    private BigDecimal collectedProductPrice;
    private BigDecimal collectedTips;
    private BigDecimal collectedTax;
    private BigDecimal discount;
    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
}
