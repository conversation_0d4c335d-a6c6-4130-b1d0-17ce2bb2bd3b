package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.AppointmentDayDTO;
import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.dto.AppointmentListClientPortalDTO;
import com.moego.server.grooming.dto.UpdateAppointmentDTO;
import com.moego.server.grooming.dto.ob.CalculateServiceAmountDTO;
import com.moego.server.grooming.dto.ob.SubmitBookingRequestDTO;
import com.moego.server.grooming.dto.ob.UpdateBookingRequestDTO;
import com.moego.server.grooming.params.AppointmentDetailParams;
import com.moego.server.grooming.params.AppointmentListParams;
import com.moego.server.grooming.params.appointment.UpdateAppointmentParams;
import com.moego.server.grooming.params.ob.CalculatePaymentParams;
import com.moego.server.grooming.params.ob.CancelBookingRequestParams;
import com.moego.server.grooming.params.ob.SubmitBookingRequestParams;
import com.moego.server.grooming.params.ob.UpdateBookingRequestParams;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
public interface IClientPortalAppointmentService {

    /**
     * List appointment list by appointment type
     *
     * @return appointment list
     */
    @PostMapping("/service/grooming/client-portal/getAppointmentList")
    AppointmentListClientPortalDTO getAppointmentList(@RequestBody AppointmentListParams params);

    /**
     * Get appointment detail
     *
     * @return appointment list
     */
    @PostMapping("/service/grooming/client-portal/getAppointmentDetail")
    AppointmentDetailClientPortalDTO getAppointmentDetail(@RequestBody AppointmentDetailParams params);

    /**
     * Get last finished appointment detail
     *
     * @return appointment and pet details
     */
    @PostMapping("/service/grooming/client-portal/getLastFinishedAppointmentDetail")
    AppointmentDetailClientPortalDTO getLastFinishedAppointmentDetail(@RequestBody AppointmentDetailParams params);

    /**
     * Get next upcoming appointment detail
     * contains booking request and scheduled appointment
     *
     * @return appointment and pet details
     */
    @PostMapping("/service/grooming/client-portal/getNextUpcomingAppointmentDetail")
    AppointmentDetailClientPortalDTO getNextUpcomingAppointmentDetail(@RequestBody AppointmentDetailParams params);

    /**
     * Get today appointment list detail
     * contains booking request and scheduled appointment
     *
     * @return appointment and pet details
     */
    @PostMapping("/service/grooming/client-portal/getTodayAppointmentListDetail")
    List<AppointmentDetailClientPortalDTO> getTodayAppointmentListDetail(@RequestBody AppointmentDetailParams params);

    /**
     * pre submit booking request
     *
     * @param params selected pets, services, staff, date and time slot
     * @return submit result
     */
    @PostMapping("/service/grooming/client-portal/preSubmitBookingRequest")
    Boolean preSubmitBookingRequest(@RequestBody SubmitBookingRequestParams params);

    /**
     * Submit booking request
     *
     * @param params selected pets, services, staff, date and time slot
     * @return submit result
     */
    @PostMapping("/service/grooming/client-portal/submitBookingRequest")
    SubmitBookingRequestDTO submitBookingRequest(@RequestBody SubmitBookingRequestParams params);

    /**
     * Update booking request
     *
     * @param params booking id, selected staff id, selected date and time slot
     * @return update result
     */
    @PostMapping("/service/grooming/client-portal/updateBookingRequest")
    UpdateBookingRequestDTO updateBookingRequest(@RequestBody UpdateBookingRequestParams params);

    /**
     * Cancel booking request
     *
     * @param params booking id, selected staff id, selected date and time slot
     * @return update result
     */
    @PostMapping("/service/grooming/client-portal/cancelBookingRequest")
    UpdateBookingRequestDTO cancelBookingRequest(@RequestBody CancelBookingRequestParams params);

    /**
     * Update appointment
     *
     * @param params booking id, update status
     * @return update result
     */
    @PostMapping("/service/grooming/client-portal/updateAppointment")
    UpdateAppointmentDTO updateAppointment(@RequestBody UpdateAppointmentParams params);

    /**
     * Send appointment day notification to c app client
     *
     * @return notification result
     */
    @PostMapping("/service/grooming/client-portal/sendAppointmentDayNotificationToClient")
    AppointmentDayDTO sendAppointmentDayNotificationToClient();

    /**
     * Calculating OB Estimated Payment Amount Information
     *
     * @param params customer selected pets, services
     * @return estimated payment amount
     */
    @PostMapping("/service/grooming/client-portal/calculatePaymentInfo")
    CalculateServiceAmountDTO calculatePaymentInfo(@RequestBody CalculatePaymentParams params);
}
