package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Grooming report grooming frequency 和 next appointment date 存放
 */
@Data
public class GroomingRecommendation {

    @Schema(description = "customer prefer frequency day")
    private Integer frequencyDay;

    @Schema(description = "frequency type: 0-day, 1-week, 2-month")
    private Byte frequencyType;

    @Schema(description = "formatted frequency text: Every xx days, Every xx weeks, Every xx months")
    private String frequencyText;

    @Schema(description = "next appointment date, standard format: yyyy-MM-dd, need to be formatted by front end")
    private String nextAppointmentDate;

    @Schema(description = "next appointment date")
    private String nextAppointmentDateText;
}
