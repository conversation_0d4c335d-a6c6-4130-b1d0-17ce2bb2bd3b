package com.moego.server.grooming.dto;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class GroomingServiceOperationDTO {

    private Long id;

    private Integer businessId;

    private Integer groomingId;

    private Integer groomingServiceId;

    private Integer petId;

    @Positive(message = "The staff id should be greater than 0.")
    private Integer staffId;

    @NotBlank
    @Size(max = 150)
    private String operationName;

    @Min(value = 0, message = "The start time should be after 00:00.")
    @Max(value = 1440, message = "The start time should be before 23:59.")
    private Integer startTime;

    @Min(value = 0, message = "The service time should be greater than 0.")
    @Max(value = 1440, message = "The service time should take no more than 24 hours.")
    private Integer duration;

    @Size(max = 200)
    private String comment;

    @Digits(
            integer = 18,
            fraction = 2,
            message = "The operation price should be less than 18 digits and up to 2 decimal places.")
    @NotNull(message = "The operation price is required.")
    private BigDecimal price;

    @Digits(
            integer = 1,
            fraction = 2,
            message = "The price ratio should be less than 1 digit and up to 2 decimal places.")
    private BigDecimal priceRatio;
}
