package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class AppointmentWithPetDetailsDto {

    private Integer appointmentId;
    private Byte status;
    private Integer customerId;
    private Integer businessId;
    private String appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;
    private Long companyId;

    /**
     * arrival window before
     */
    private Integer arrivalBeforeStartTime;

    /**
     * arrival window after
     */
    private Integer arrivalAfterStartTime;

    private Integer source;

    private List<AppointmentServiceInfo> services;

    private String endDate;
}
