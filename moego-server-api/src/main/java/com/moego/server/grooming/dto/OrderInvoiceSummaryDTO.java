package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.dto.PaymentSummary;
import com.moego.server.payment.dto.PreAuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderInvoiceSummaryDTO {

    // 保留原来 invoice summary 的字段
    private Integer id;
    private Integer orderVersion;
    private Integer businessId;
    private Integer groomingId;
    private String type;
    private Integer customerId;
    private String status;
    private String paymentStatus;
    private Long completeTime;
    private String orderType;
    private String fulfillmentStatus;
    private Long orderRefId;
    private String extraChargeReason;
    private BigDecimal subTotalAmount;
    private BigDecimal discountAmount;
    private BigDecimal discountedSubTotalAmount;
    private BigDecimal tipsAmount;

    @Schema(description = "tip split 计算基数")
    private BigDecimal tipsSplitAmount;

    private BigDecimal tipsRate;
    private BigDecimal taxAmount;
    private String tipsType;
    private BigDecimal totalAmount;

    private BigDecimal totalAmountWithFee;
    private BigDecimal paymentAmount;
    private BigDecimal paidAmount;

    @Schema(description = "remainAmount = orderModel.remainAmount (must be non-negative)")
    private BigDecimal remainAmount;

    @Schema(description = "remainAmountV2 = totalAmount - paidAmount + refundedAmount (may be negative)")
    private BigDecimal remainAmountV2;

    private BigDecimal refundedAmount;
    private BigDecimal convenienceFee;
    private BigDecimal totalCollected;
    private Integer createBy;
    private Integer updateBy;
    private Long createTime;
    private Long updateTime;
    private PreAuthDTO preAuthInfo;

    @Schema(description = "tips计算基数，同旧字段originAmount，是订单中服务的原价")
    private BigDecimal tipBasedAmount;

    @Schema(description = "提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client) ")
    private BigDecimal initProcessingFee;

    @Schema(description = "本次订单如果是prepay，关联的booking fee，用于C端展示")
    private BigDecimal bookingFeeAmount;

    @Schema(description = "本次支付是否需要增加processing fee")
    private Boolean requiredProcessingFee;

    private List<OrderItemDTO> items;

    @Schema(description = "订单中所有service的subTotalAmount，不包含tax、tips、discount")
    private Map<String, BigDecimal> subTotalAmountMap;

    @Schema(description = "订单中的discountMap")
    private Map<String, DiscountDTO> discountMap;

    private List<PackageServiceDTO> appliedPackageServices;

    @Schema(description = "根据targetType=65 查询出的该订单的messageDetail id列表")
    private List<IdAndCreateTimeDTO> receiptSentList;

    private String customerFirstName;
    private String customerLastName;
    private String customerEmail;

    @Schema(description = "for deposit")
    DepositDto depositInfo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PaymentSummary paymentSummary;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String businessName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AppointmentInfoDTO appointmentInfo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<DiscountDTO> discountList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<MembershipDTO> membershipList;

    public String getConfirmationId() {
        return String.format("%d%08d", createTime, id);
    }

    @Getter
    @Setter
    public static class AppointmentInfoDTO {

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long checkInTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long checkOutTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Byte appointmentStatus;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String appointmentDate;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer appointmentStartTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer appointmentEndTime;
    }

    @Data
    public static class OrderItemDTO {

        private Integer id;
        private Integer orderId;
        private Integer staffId;
        private Integer objectId;
        private Integer petId;

        @Schema(description = "item类型：service、product、package、noshow")
        private String type;

        private String name;
        private String description;
        private BigDecimal unitPrice;

        private Integer quantity;
        private Integer purchasedQuantity;
        private BigDecimal subTotalAmount;
        private BigDecimal totalAmount;
        private BigDecimal discountAmount;
        private BigDecimal taxAmount;
        private Long createTime;
        private Long updateTime;

        private TaxDTO taxInfo;
        private DiscountDTO discountInfo;

        private List<OrderItemPetDetailDTO> petDetails;
        private List<OrderItemPetEvaluationDetailDTO> petEvaluationDetails;

        private List<MembershipDTO> membershipDetailList;
    }

    @Data
    public static class DiscountDTO {

        private Long id;
        private BigDecimal discountRate;
        private BigDecimal discountAmount;
        private String discountType;
        private Long discountCodeId;
        private String discountCode;
        private String description;
        private Integer applySequence;
        private String applyType;

        private Boolean allowedAllThing;
        private Boolean allowedAllServices;
        private Boolean allowedAllProducts;
        private Boolean allowedAllClients;
        private List<String> serviceNames;
        private List<String> productNames;
    }

    @Data
    public static class TaxDTO {

        private Long id;
        private Integer taxId;
        private BigDecimal taxRate;
        private BigDecimal taxAmount;
    }

    @Data
    public static class MembershipDTO {

        private Long id;
        private String name;
        private String description;
        private BigDecimal discountAmount;
    }
}
