package com.moego.server.grooming.params;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Data
@Builder
public class AppointmentDetailParams {

    /**
     * Appointment id
     */
    private Integer appointmentId;

    /**
     * Link customers
     */
    @NotEmpty
    private List<@NotNull BaseBusinessCustomerIdDTO> linkCustomers;
}
