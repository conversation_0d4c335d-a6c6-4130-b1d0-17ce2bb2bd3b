package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Deprecated
public class RepeatWithSSParams extends PreviewRepeatParams {

    @Schema(description = "smart schedule开关 true/false")
    @NotNull
    private Boolean smartSchedule;

    @Schema(description = "是否应用client preference")
    private Boolean applyClientPreference;
}
