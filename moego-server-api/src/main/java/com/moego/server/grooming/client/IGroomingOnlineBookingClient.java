package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IGroomingOnlineBookingClient")
public interface IGroomingOnlineBookingClient extends IGroomingOnlineBookingService {}
