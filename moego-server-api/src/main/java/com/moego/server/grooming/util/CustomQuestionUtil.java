package com.moego.server.grooming.util;

import com.google.common.reflect.TypeToken;
import com.moego.common.utils.GsonUtil;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2020/12/29 3:34 PM
 *
 * what is your name?
 * tom
 *
 * what is your gender?
 * female
 *
 * your favoriates?
 * basketball
 * ride
 *
 */
public class CustomQuestionUtil {

    private static final String LINE_SEPARATOR = "\n";

    public static String generateNote(List<GroomingQuestionDTO> questions, Map<String, Object> answers) {
        if (CollectionUtils.isEmpty(answers)) {
            return "";
        }
        // multiple type question,  basic: Q: xxx   A:xxx
        StringBuilder sb = new StringBuilder();
        questions.forEach(question -> {
            if (answers.containsKey(question.getKey())) {
                String currentQuestionName = question.getQuestion();
                sb.append(currentQuestionName).append(LINE_SEPARATOR);
                String answer = (String) answers.get(question.getKey());
                if (question.getQuestionType() == null || question.getQuestionType() != 5) {
                    sb.append(answer).append(LINE_SEPARATOR).append(LINE_SEPARATOR);
                } else {
                    if (!StringUtils.hasLength(answer)) {
                        return;
                    }
                    // String json array
                    Type type = new TypeToken<List<String>>() {}.getType();
                    List<String> lines = GsonUtil.fromJson(answer, type);
                    lines.forEach(line -> {
                        sb.append(line).append(LINE_SEPARATOR);
                    });
                    sb.append(LINE_SEPARATOR);
                }
            }
        });
        return sb.toString();
    }
}
