package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class EditCommentsParams {

    private Integer ticketId;
    private String ticketComments;
    private String alertNotes;
    private Integer businessId;
    private Long companyId;
    /**
     * @deprecated by <PERSON> since 2023/7/24, use {@link #staffId} instead.
     */
    @Deprecated
    private Integer accountId;

    private Integer staffId;
    private Long petId;

    @Schema(description = "修改 repeat 预约的 comments 类型：1-only this, 2-this and following, 3-all")
    @Min(1)
    @Max(3)
    private Integer repeatType;
}
