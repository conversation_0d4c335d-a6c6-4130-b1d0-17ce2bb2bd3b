package com.moego.server.grooming.dto.report;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroomingMobileOverviewDTO {

    private List<CustomerSpend> topSpendingClients;

    private Integer recurringClientPercentage;

    private List<EmployeeContribute> employeeOverview;
}
