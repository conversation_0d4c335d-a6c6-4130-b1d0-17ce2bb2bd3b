package com.moego.server.grooming.dto.ob;

import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/3
 */
@Data
@Builder
public class StaffAvailableDateDTO {

    private String date;

    private Boolean isAm;

    private Boolean isPm;

    private List<StaffAvailableTimeSlotDTO> staffList;

    @Data
    @Builder
    public static class StaffAvailableTimeSlotDTO {

        private Integer staffId;

        private String firstName;

        private String lastName;

        private List<Integer> amTimeslots;

        private List<Integer> pmTimeslots;
    }
}
