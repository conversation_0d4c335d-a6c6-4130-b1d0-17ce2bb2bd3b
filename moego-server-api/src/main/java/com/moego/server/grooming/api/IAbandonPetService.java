package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.AbandonPetDTO;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Validated
public interface IAbandonPetService {

    /**
     * List abandon pets by abandon record id.
     *
     * @param abandonRecordId abandon record id
     * @return abandon pet list
     */
    @GetMapping("/service/grooming/abandon-pet/getByAbandonRecordId")
    List<AbandonPetDTO> listByAbandonRecordId(@RequestParam("abandonRecordId") @NotNull Integer abandonRecordId);
}
