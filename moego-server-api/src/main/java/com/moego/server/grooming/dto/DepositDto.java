package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/21 11:31 AM
 */
@Data
public class DepositDto {

    private Integer id;
    private String deGuid;
    private BigDecimal amount;
    private Integer invoiceId;
    private String desc;

    @Schema(description = "0:已删除 1: 已创建 2：已支付")
    private Byte status;

    private Integer businessId;
    private Integer staffId;
    private Date updateTime;

    private Long companyId;

    @Schema(description = "提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client)")
    private BigDecimal initProcessingFee;

    @Schema(description = "本次支付是否需要添加processingFee，需满足：1.首选支付方式为Stripe 2.全局开关打开 3.单次开关打开")
    private Boolean requiredProcessingFee;
}
