package com.moego.server.payment.dto;

import com.moego.server.payment.params.TipMap;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class SmartTipConfigForClientDTO {

    private Integer businessId;

    @Schema(description = "Smart tip开关")
    private Byte enable;

    @Schema(description = "Smart tip区分低档和高档的阈值，大于0，最多2位小数")
    private BigDecimal threshold;

    @Schema(description = "低档位tip类型，0-by amount, 1-by percentage")
    private Byte lowerTipType;

    @Schema(description = "低档位tip配置")
    private TipMap lowerTip;

    @Schema(description = "低档位默认选中tip选项，1-low，2-medium，3-high")
    private Byte lowerPreferredTip;

    @Schema(description = "Smart tip关闭时全局tip type/Smart tip打开时高档位tip type")
    private Byte upperTipType;

    @Schema(description = "Smart tip关闭时全局tip配置/Smart tip打开时高档位tip配置")
    private TipMap upperTip;

    @Schema(description = "Smart tip关闭时全局tip/Smart tip打开时高档位tip 默认选中tip选项，1-low，2-medium，3-high")
    private Byte upperPreferredTip;
}
