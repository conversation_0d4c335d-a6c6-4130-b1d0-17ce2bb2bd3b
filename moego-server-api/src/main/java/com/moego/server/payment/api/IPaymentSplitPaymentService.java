package com.moego.server.payment.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
public interface IPaymentSplitPaymentService {
    @PostMapping("/service/payment/split-payment/retry")
    void retryStripeSplitPaymentTask();

    /**
     * 分账退款重试任务
     */
    @PostMapping("/service/payment/split-payment/refund/retry")
    void retrySplitPaymentRefundTask();

    /**
     * 分账切流比例调整
     */
    @PostMapping("/service/payment/split-payment/set/ratio")
    void setRatio(@RequestParam("ratio") Integer ratio);

    /**
     * 分账切流黑白名单控制
     * @param businessId business id
     * @return
     */
    @PostMapping("/service/payment/split-payment/set/traffic/white-list/add")
    void addTrafficWhiteList(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/payment/split-payment/set/traffic/white-list/del")
    void delTrafficWhiteList(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/payment/split-payment/set/traffic/black-list/add")
    void addTrafficBlackList(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/payment/split-payment/set/traffic/black-list/del")
    void delTrafficBlackList(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/split-payment/allconfig")
    String getAllSplitPaymentTrafficSwitchConfig();

    @PostMapping("/service/payment/split-payment/set/loanswitch")
    void setLoanSwitch(@RequestParam("loanSwitch") String loanSwitch);
}
