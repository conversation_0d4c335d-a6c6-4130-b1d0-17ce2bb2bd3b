package com.moego.server.payment.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class UsBankAccountDTO {
    // id of the payment method
    private String id;
    // account holder type
    private String accountHolderType;
    // account type
    private String accountType;
    // bank name
    private String bankName;
    // financial connections account
    private String financialConnectionsAccount;
    // fingerprint
    private String fingerprint;
    // last4
    private String last4;
    // routing number
    private String routingNumber;
    // status details
    private String statusDetails;
}
