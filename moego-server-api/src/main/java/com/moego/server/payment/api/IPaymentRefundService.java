package com.moego.server.payment.api;

import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.RefundChannelDTO;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.server.payment.params.CheckRefundChannelParams;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import com.moego.server.payment.params.CreateRefundParams;
import com.moego.server.payment.params.SubmitRefundParams;
import com.moego.server.payment.params.refund.RefundHistoryListParams;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPaymentRefundService {
    @PostMapping("/service/payment/getRefunds")
    List<RefundDTO> getRefunds(@RequestParam("module") String module, @RequestBody List<Integer> invoiceIds);

    /**
     * OrderVersion >= 2 的订单依赖该接口发起 Payment 的退款.
     */
    @PostMapping("/service/payment/refund")
    PaymentDTO createRefund(@RequestParam("businessId") Integer businessId, @RequestBody CreateRefundParams params);

    /**
     * svc-order-v2 依赖该接口实现对 OrderVersion < 2 的老版本的订单的退款支持.
     * @param submitRefundParams
     */
    @PostMapping("/service/payment/invoice/refund/submit")
    void submitRefund(@RequestBody SubmitRefundParams submitRefundParams);

    @GetMapping("/service/payment/refund/getRefundByRefundOrderPaymentId")
    RefundDTO getRefundByRefundOrderPaymentId(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("refundOrderPaymentId") Long refundOrderPaymentId);

    @GetMapping("/service/payment/refund/getRefundById")
    RefundDTO getRefundById(@RequestParam("refundId") Long refundId);

    @PostMapping("/service/payment/refund/createByPaymentId")
    PaymentDTO createRefundByPaymentId(
            @RequestParam("businessId") Integer businessId, @RequestBody CreateRefundByPaymentIdParams params);

    /**
     * edit appt 编辑价格导致触发退款
     * svc-order-v2 依赖该接口实现对 OrderVersion < 2 的老版本订单的退款支持.
     * @param params
     * @return
     */
    @PostMapping("/service/payment/refund/check")
    RefundChannelDTO refundCheckByChangeAmount(@RequestBody CheckRefundChannelParams params);

    @GetMapping("/service/payment/refund/getByPaymentId")
    List<RefundDTO> getRefunds(@RequestParam Integer paymentId);

    @GetMapping("/service/payment/refund/getByStripeApi")
    List<RefundDTO> getRefundsByStripeApi(@RequestParam String chargeId, @RequestParam Long limit);

    @PostMapping("/service/payment/refund/getByParams")
    List<RefundDTO> getRefundsByParams(@RequestBody @Validated RefundHistoryListParams params);
}
