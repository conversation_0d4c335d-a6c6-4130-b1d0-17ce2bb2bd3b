package com.moego.server.payment.api;

import com.moego.server.payment.dto.PlatformSalesRecordDTO;
import com.moego.server.payment.dto.PlatformSalesRecordView;
import com.moego.server.payment.params.CreatePlatformSalesLinkParams;
import com.moego.server.payment.params.PlatformSalesQueryParams;
import com.moego.server.payment.params.PlatformSalesUpdateParams;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
public interface IPlatformSalesService {
    @PostMapping("/service/payment/platform/sales/link")
    String createPlatformSalesLink(@RequestBody @Validated CreatePlatformSalesLinkParams params);

    @PostMapping("/service/payment/platform/sales/list")
    PlatformSalesRecordDTO getPlatformSalesRecordList(@RequestBody @Validated PlatformSalesQueryParams params);

    @PostMapping("/service/payment/platform/sales/delete")
    int deletePlatformSales(@RequestParam("id") Long id);

    @PostMapping("/service/payment/platform/sales/get/status")
    Map<Integer, String> getPlatformSalesRecordStatusMap();

    @PostMapping("/service/payment/platform/sales/get")
    PlatformSalesRecordView getPlatformSalesRecord(@RequestParam("id") Long id);

    @PostMapping("/service/payment/platform/sales/update")
    Boolean updatePlatformSalesRecord(@RequestBody @Validated PlatformSalesUpdateParams params);
}
