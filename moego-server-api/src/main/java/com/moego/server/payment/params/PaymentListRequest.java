package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class PaymentListRequest {

    @Schema
    private Long companyId;

    @Schema(defaultValue = "")
    private String module;

    @Schema(defaultValue = "")
    private Integer customerId;

    @Schema(defaultValue = "")
    private String method;

    @Schema(defaultValue = "stripe")
    private String vendor;

    @Schema(defaultValue = "1")
    private Integer pageNum;

    @Schema(defaultValue = "30")
    private Integer pageSize;

    @Schema(defaultValue = "desc")
    private String order;

    @Schema(defaultValue = "")
    private String startDate;

    @Schema(defaultValue = "")
    private String endDate;

    private Integer searchBusinessId;

    private List<Integer> invoiceIds;
}
