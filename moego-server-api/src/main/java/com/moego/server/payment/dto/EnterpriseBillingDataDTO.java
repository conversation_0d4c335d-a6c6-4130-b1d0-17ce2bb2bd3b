package com.moego.server.payment.dto;

import com.moego.common.dto.NewPricingLevelDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(description = "data object")
public class EnterpriseBillingDataDTO {

    @Schema(description = "当月套餐开始日期")
    private Long beginDate;

    @Schema(description = "当月套餐结束日期 ")
    private Long expireDate;

    @Schema(description = "套餐价格")
    private BigDecimal price;

    private int vansNum;
    private int salonsNum;

    @Schema(description = "是否自动续费，0-不自动续费，1-自动续费")
    private Byte autoRenew;

    @Schema(description = "all cards in the enterprise, 复用原有结构")
    private List<CompanyCardDTO> cardList;

    @Schema(description = "isSubscription=true : 表示已订阅")
    private Boolean isSubscription;

    @Schema(description = "套餐类型，0-月套餐，1-年套餐")
    private Byte planType;

    @Schema(description = "扣费记录")
    private List<ChargeDTO> charges;

    @Schema(description = "charge failed time")
    private Long chargeFailedTime;

    @Schema(description = "planVersion and premiumType")
    private NewPricingLevelDto planTier;

    private Boolean isDowngrade;
    private PlanData downgradePlanData;

    @Schema(description = "付费状态下，即首次付费时间")
    private Long createTime;

    private Long updateTime;
}
