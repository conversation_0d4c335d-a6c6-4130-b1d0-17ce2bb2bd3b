package com.moego.server.payment.api;

import com.moego.server.payment.dto.stripe.StripeAccountDTO;
import com.moego.server.payment.params.stripe.DeleteExternalAccountParams;
import com.moego.server.payment.params.stripe.SetDefaultExternalAccountParams;
import com.moego.server.payment.params.stripe.StripeAccountParams;
import com.moego.server.payment.params.stripe.StripePersonParams;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/1/13
 */
public interface IPaymentVerificationService {

    @PostMapping("/service/payment/verification/search/account")
    StripeAccountDTO searchStripeAccountByBusinessId(@RequestParam("businessId") Long id);

    @PostMapping("/service/payment/verification/update/account/info")
    String updateStripeAccountByParams(@RequestBody @Validated StripeAccountParams params);

    @PostMapping("/service/payment/verification/update/account/person")
    String updateStripePersonByParams(@RequestBody @Validated StripePersonParams params);

    @PostMapping("/service/payment/verification/account/external-account/set-default")
    String setDefaultExternalAccount(@RequestBody @Validated SetDefaultExternalAccountParams req);

    @PostMapping("/service/payment/verification/account/external-account/delete")
    String deleteExternalAccount(@RequestBody @Validated DeleteExternalAccountParams req);
}
