package com.moego.server.payment.dto;

import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Data
@Accessors(chain = true)
@Builder
public class CompanyFeatureRelationView {
    // id
    private Integer id;
    // company name
    private String companyName;
    // company id
    private Integer companyId;
    // code
    private String code;
    // allow type
    private Byte allowType;
    // enable
    private Byte enable;
    // quota
    private Integer quota;
    // expiration time
    private Long expirationTime;
    // create time
    private Date createTime;
    // update time
    private Date updateTime;
    // note
    private String note;
    // is deleted
    private Byte deleted;
}
