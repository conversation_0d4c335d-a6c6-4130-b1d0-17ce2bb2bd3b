package com.moego.server.payment.params;

import com.moego.common.enums.QuickBooksConst;
import com.moego.common.utils.Pagination;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ListQuickBookSettingParams {
    Integer businessId;

    Integer companyId;

    Byte receiptStatus;

    @Builder.Default
    Byte enableSyncStatus = QuickBooksConst.ENABLE_SYNC_OPEN;

    Byte userVersion;

    Pagination pagination;
}
