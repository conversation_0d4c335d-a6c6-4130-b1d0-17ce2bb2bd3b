package com.moego.server.payment.params;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StripeCustomerParams {

    /**
     * mapping to stripe customer
     */
    private Integer companyId;

    /**
     * 1.credit
     * 2.debit
     */
    private Byte adjustmentType;

    /**
     * adjust balance
     */
    private BigDecimal amount;

    /**
     * 16 bytes v4 UUID
     */
    private String idempotentKey;
}
