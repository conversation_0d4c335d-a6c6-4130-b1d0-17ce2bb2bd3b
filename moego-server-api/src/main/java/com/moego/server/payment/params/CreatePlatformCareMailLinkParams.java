package com.moego.server.payment.params;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePlatformCareMailLinkParams {
    // email
    @Email(message = "Email should be valid")
    private String email;

    // agreement id
    @Min(value = 1, message = "agreement id should be greater than or equal to 1")
    private Long agreementId;

    private Boolean showAccounting;
}
