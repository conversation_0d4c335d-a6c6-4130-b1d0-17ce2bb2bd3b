package com.moego.server.payment.params.dispute;

import com.moego.common.utils.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Getter
@Setter
@Builder(toBuilder = true)
public class QueryStripeDisputeParams {
    @Min(value = 0, message = "business id must gt 0")
    private Long businessId;

    @Schema(description = "customer name, support fuzzy query")
    private String customer;

    private Long dateStart;
    private Long dateEnd;

    @Schema(description = "dispute status, the corresponding status is for Stripe.")
    private List<String> status;

    @Schema(description = "dispute reason, the corresponding reason is for Stripe.")
    private String reason;

    /**
     *  No longer effective, default sorted by disputed_on.
     */
    @Deprecated
    @Schema(description = "sort field, which is sorted according to this field. default is create_time")
    @Builder.Default
    private String orderBy = "created_at";

    @Builder.Default
    private Byte orderByDesc = 0;

    Pagination pagination;
}
