package com.moego.server.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.SquarePaymentMethodEnum;
import com.moego.common.StripePaymentMethodEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.StripeApi;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class PaymentDTO {

    private Integer id;
    private Integer businessId;
    private String module;
    private Integer invoiceId;
    private Integer customerId;
    private Integer staffId;
    private String method;
    private BigDecimal amount;
    private BigDecimal processingFee;
    private Byte status;

    @Schema(description = "Credit/Debit etc.")
    private String cardFunding;

    private String checkNumber;
    private String cardType;
    private String cardNumber;
    private String expMonth;
    private String expYear;
    private String signature;
    private String paidBy;
    private String description;
    private Integer methodId;
    private Byte squarePaymentMethod;
    private Byte stripePaymentMethod;
    private Boolean isOnline;
    private Long createTime;
    private Long updateTime;

    @Schema(description = "current desc: square支付不成功时，记录失败原因")
    private String cancelReason;

    private String locationId;

    private String customerName;
    private List<RefundDTO> refunds;

    private BigDecimal refundedAmount;

    private BigDecimal totalCollected;

    private Long companyId;

    private String vendor;

    private Long orderPaymentId;
    private Long transactionId;

    public void setRefundAndCollected() {
        if (refunds == null || refunds.size() == 0) {
            this.refundedAmount = BigDecimal.ZERO;
        } else {
            this.refundedAmount = refunds.stream()
                    .filter(r -> !r.getStatus().equals(PaymentStatusEnum.FAILED))
                    .map(RefundDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        this.totalCollected = this.amount.subtract(this.refundedAmount);
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer groomingId;

    @Schema(description = "当前支付记录是否为prepay")
    private Boolean isPrepay;

    private String paymentMethod;

    private String stripeIntentId;

    public void setPaymentMethod() {
        StringBuilder sb = new StringBuilder();
        if (method.equalsIgnoreCase(PaymentMethodEnum.METHOD_NAME_CREDIT_CARD)) {
            if (StripeApi.FUNDING_DEBIT.equalsIgnoreCase(cardFunding)) {
                // 使用smart reader 支付前端效果：Credit card - Smart reader 9969(Visa)
                sb.append("Debit card");
            } else {
                sb.append(PaymentMethodEnum.METHOD_NAME_CREDIT_CARD);
            }
            sb.append(" - ");
            if (squarePaymentMethod != null && squarePaymentMethod > 0) {
                sb.append(SquarePaymentMethodEnum.valueOf(squarePaymentMethod));
            } else {
                sb.append(StripePaymentMethodEnum.valueOf(stripePaymentMethod));
            }
            if (StringUtils.hasLength(cardNumber)) {
                sb.append(cardNumber);
            }
            if (StringUtils.hasLength(cardType)) {
                sb.append("(").append(cardType).append(")");
            }
        } else if (method.equals(PaymentMethodEnum.METHOD_NAME_CHECK) && StringUtils.hasLength(this.checkNumber)) {
            sb.append("check ").append(this.checkNumber);
        } else {
            sb.append(method);
        }

        if (this.isPrepay) {
            sb.append(" (prepaid)");
        }
        this.paymentMethod = sb.toString();
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
}
