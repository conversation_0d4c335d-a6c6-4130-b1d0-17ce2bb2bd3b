package com.moego.server.payment.api;

import com.moego.server.payment.dto.AssociationIdDTO;
import com.moego.server.payment.dto.CheckMoeGoPayStatusResponse;
import com.moego.server.payment.dto.EnterpriseStripeCustomerDTO;
import com.moego.server.payment.dto.StripeAccountReviewingDTO;
import com.moego.server.payment.dto.StripeCustomerDTO;
import com.moego.server.payment.dto.StripeInvoiceCountDTO;
import com.moego.server.payment.dto.stripe.PayoutListSummaryDTO;
import com.moego.server.payment.params.CheckMoeGoPayStatusRequest;
import com.moego.server.payment.params.CreateEnterpriseCustomerParams;
import com.moego.server.payment.params.QueryAssociationIdParams;
import com.moego.server.payment.params.StripeAccountScheduleParams;
import com.moego.server.payment.params.StripeCustomerParams;
import com.moego.server.payment.params.stripe.QueryPayoutListSummaryParams;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/7/3
 */
public interface IPaymentStripeService {
    @GetMapping("/service/payment/stripe/customer")
    StripeCustomerDTO getStripeCustomer(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/payment/stripe/customer/balance")
    StripeCustomerDTO getStripeCustomerBalance(@RequestParam("companyId") Integer companyId);

    @PutMapping("/service/payment/stripe/customer/balance")
    void increaseStripeCustomerBalance(@RequestBody List<StripeCustomerParams> paramsList);

    @GetMapping("/service/payment/stripe/invoice")
    List<Integer> getPaidGteCountByCompanyIdList(
            @RequestParam("companyIdList") List<Integer> companyIdList,
            @RequestParam("invoicePaidCount") Integer invoicePaidCount);

    @GetMapping("/service/payment/stripe/invoice/count")
    List<StripeInvoiceCountDTO> countPaidInvoiceByCompanyIdList(
            @RequestParam("companyIdList") List<Integer> companyIdList);

    @PostMapping("/service/payment/stripe/customer")
    void saveStripeCustomer(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("stripeCustomerId") String stripeCustomerId);

    @GetMapping("/service/payment/stripe/customerIdStr")
    String getStripeCustomerId(@RequestParam("customerId") Integer customerId);

    @PostMapping("/service/payment/stripe/capturePaymentIntent")
    void capturePaymentIntent(@RequestParam("paymentPrimaryId") Integer paymentPrimaryId);

    @PostMapping("/service/payment/stripe/payoutSchedule/applying")
    List<StripeAccountReviewingDTO> getAllReviewingPayoutT1();

    @PostMapping("/service/payment/stripe/payoutSchedule/action")
    StripeAccountReviewingDTO reviewPayoutSchedule(@RequestBody StripeAccountScheduleParams req);

    @PostMapping("/service/payment/stripe/checkMoeGoPayStatus")
    CheckMoeGoPayStatusResponse checkMoeGoPayStatus(@RequestBody CheckMoeGoPayStatusRequest req);

    @GetMapping("/service/payment/stripe/createEnterpriseCustomer")
    EnterpriseStripeCustomerDTO createEnterpriseCustomer(@RequestBody CreateEnterpriseCustomerParams req);

    /**
     * 根据 business id 获取 Payout 摘要, 限制只能查询 2023-06-07之后的数据
     * @param params 查询条件, 包括 business id 和 start time、end time
     */
    @GetMapping("/service/payment/stripe/payout/get")
    PayoutListSummaryDTO getPayOutListSummary(@Validated @RequestBody QueryPayoutListSummaryParams params);
    /**
     * 查询stripe id/company id/business id
     * 参数可以是其中一个id, 返回值会返回该id对应的其他两个id
     * @params id集合体, 包括stripe id, company id, business id
     */
    @PostMapping("/service/payment/stripe/association/id")
    AssociationIdDTO getAssociationIdByParams(@Validated @RequestBody QueryAssociationIdParams params);

    @GetMapping("/service/payment/stripe/payout")
    String getPayoutModelJson(@RequestParam("payoutId") String payoutId, @RequestParam("businessId") Long businessId);

    @GetMapping("/service/payment/stripe/customerIdWithCreate")
    String getStripeCustomerIdWithCreate(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/payment/stripe/reader/updateCache")
    void updateReaderCache(
            @RequestParam("paymentIntentId") String paymentIntentId,
            @RequestParam("readerId") String readerId,
            @RequestParam("errMsg") String errMsg);

    @PostMapping("/service/payment/stripe/reader/updateCacheForCancelAndFail")
    void updateReaderCacheForCancelAndFail(
            @RequestParam("paymentIntentId") String paymentIntentId, @RequestParam("readerId") String readerId);
}
