package com.moego.server.payment.dto.stripe;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/12
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class StripeAddressDTO {

    @Schema(description = "City, district, suburb, town, or village.")
    String city;

    @Schema(description = "Two-letter country code ")
    String country;

    @Schema(description = "Address line 1 (e.g., street, PO Box, or company name).")
    String line1;

    @Schema(description = "Address line 2 (e.g., apartment, suite, unit, or building).")
    String line2;

    @Schema(description = "ZIP or postal code.")
    String postalCode;

    @Schema(description = "State, county, province, or region.")
    String state;
}
