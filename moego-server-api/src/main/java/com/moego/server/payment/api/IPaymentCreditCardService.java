package com.moego.server.payment.api;

import com.moego.server.payment.dto.CardDTO;
import com.moego.server.payment.dto.CustomerDTO;
import com.moego.server.payment.dto.CustomerStripInfoSaveResponse;
import com.moego.server.payment.dto.DeleteCustomerCardResponse;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.SetupIntentDTO;
import com.moego.server.payment.dto.UsBankAccountDTO;
import com.moego.server.payment.params.CardParams;
import com.moego.server.payment.params.CreateStripeCustomerParams;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import com.moego.server.payment.params.DeleteCustomerCardRequest;
import com.moego.server.payment.params.IntakeFormCustomerStripRequest;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/15 11:24 AM
 */
public interface IPaymentCreditCardService {
    @PostMapping("/service/payment/creditCard/newCard")
    CustomerStripInfoSaveResponse saveNewCard(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody CustomerStripInfoRequest request);

    @PostMapping("/service/payment/creditCard/createCreditCardCustomer")
    String createCreditCardCustomer(@RequestBody IntakeFormCustomerStripRequest request);

    /**
     * TODO(account structure): company 迁移后，应该在 company 维度检查？
     */
    @GetMapping("/service/payment/creditCard/hasCreditCard")
    boolean hasCreditCard(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * TODO(account structure): company 迁移后，应该在拉取 company 维度的卡
     */
    @GetMapping("/service/payment/creditCard/listCreditCard")
    List<CardDTO> getCreditCardList(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @GetMapping("/service/payment/ach/listUsBankAccounts")
    List<UsBankAccountDTO> getUsBankAccounts(@RequestParam("customerId") Integer customerId);

    @GetMapping("/service/payment/creditCard/listStripeCreditCards")
    List<CardDTO> listStripeCreditCards(@RequestParam("customerId") Integer customerId);

    @PostMapping("/service/payment/creditCard/createStripeCreditCard")
    String createStripeCreditCard(@RequestParam("customerId") Integer customerId, @RequestBody CardParams cardParams);

    @PostMapping("/service/payment/creditCard/addCreditCardCustomer")
    Integer addCreditCardCustomer(@RequestBody CreateStripeCustomerParams createStripeCustomerParams);

    /**
     * this square customer id should contain merchant id
     * @param businessId
     * @param formSavedCusId
     * @return
     */
    @GetMapping("/service/payment/creditCard/squareCard")
    CardDTO getSquareCard(
            @RequestParam("businessId") Integer businessId, @RequestParam("formSavedCusId") String formSavedCusId);

    @GetMapping("/service/payment/creditCard/pmethod")
    Object getStripePaymentMethod(
            @RequestParam("businessId") Integer businessId, @RequestParam("stripeCustomerId") String stripeCustomerId);

    @GetMapping("/service/payment/creditCard/businessHasBank")
    Boolean businessHasVerifiedBank(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/creditCard/square/auth/token")
    GetSquareTokenResponse getToken(@RequestParam("tokenBusinessId") Integer tokenBusinessId);

    @PostMapping("/service/payment/creditCard/stripe/getAccountId")
    String getStripeAccountId(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/creditCard/stripe/getByStripeId")
    CardDTO getByCardId(@RequestParam("stripeId") String stripeId);

    @GetMapping("/service/payment/customer/getByStripeId")
    CustomerDTO getByStripeCustomerId(@RequestParam("stripeCustomerId") String stripeCustomerId);

    @GetMapping("/service/payment/card/getByFingerprint")
    List<CardDTO> getByFingerprint(@RequestParam("cardId") String cardId);

    @PostMapping("/service/payment/creditCard/stripe/newCard")
    CustomerStripInfoSaveResponse saveStripeCard(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody CustomerStripInfoRequest request);

    @PostMapping("/service/payment/creditCard/stripe/deleteCard")
    DeleteCustomerCardResponse deleteCustomerCard(@RequestBody DeleteCustomerCardRequest request);

    @PostMapping("service/payment/ach/createSetupIntent")
    SetupIntentDTO createACHSetupIntent(@RequestParam("customerId") Integer customerId);

    @PostMapping("/service/payment/creditCard/stripe/tokenToCard")
    CustomerStripInfoSaveResponse stripeToken2Card(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody CustomerStripInfoRequest request);
}
