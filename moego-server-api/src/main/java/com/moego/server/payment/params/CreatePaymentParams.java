package com.moego.server.payment.params;

import com.moego.server.grooming.params.BookOnlineCustomerParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreatePaymentParams extends GetPaymentParams {

    private String method;

    @Positive(message = "Refresh to get the updated payment status.")
    @NotNull
    private BigDecimal amount;

    @Schema(description = "tips amount")
    private BigDecimal tipsAmount;

    // 仅在OB来源的支付有效
    @Deprecated(since = "2024-09-25", forRemoval = true)
    private BigDecimal bookingFeeAmount;

    @Positive
    private Integer customerId;

    private Integer staffId;
    private String paidBy;
    private String description;
    private Integer methodId;

    @Schema(description = "for credit card")
    private String chargeToken;

    private Boolean saveCard;

    // check
    private String checkNumber;

    @Schema(description = "set when Stripe reader/terminal calling")
    private String locationId;

    @Schema(
            description =
                    "1 - card, 2 - card on file, 3- bluetooth reader, 4 - smart reader, 5 - apple pay, 6 - google pay")
    private Byte stripePaymentMethod;

    private String readerId;
    // 如果不为空说明需要后端对 paymentIntent 进行更新（用于 pay online 场景）
    private String paymentIntentId;

    // credit card
    private String cardType;
    private String cardNumber;
    private String expMonth;
    private String expYear;
    private String signature;
    private String stripePaymentMethodId;
    private Boolean isOnline;

    @Schema(description = "标记是否支付定金 1：是  0：否")
    private Byte isDeposit;

    private Boolean isCancellationFee;

    @Schema(description = "支付请求的来源是OB时需要传的businessName")
    private String businessName;

    // 用于参数传递
    private String guid;

    private BookOnlineCustomerParams customerData;

    public BigDecimal getAmountWithoutTips() {
        if (this.getTipsAmount() == null) {
            return this.getAmount();
        }
        return this.getAmount().subtract(this.getTipsAmount());
    }

    public BigDecimal getAmountWithoutBookingFee() {
        if (this.getBookingFeeAmount() == null) {
            return this.getAmount();
        }
        return this.getAmount().subtract(this.getBookingFeeAmount());
    }

    @Schema(description = "是否在支付金额加入processing fee pay by client")
    private Boolean addProcessingFee;

    private Boolean fromPreAuth = false;
    private String rawId;
    private Long rawCreateTime;
    private Long rawUpdateTime;
    private Integer preAuthId;
}
