package com.moego.server.payment.api;

import com.moego.common.dto.PaymentSummary;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.response.ResponseResult;
import com.moego.server.payment.dto.AssociationAccountDTO;
import com.moego.server.payment.dto.ConvenienceFeeDTO;
import com.moego.server.payment.dto.CustomerPaymentDTO;
import com.moego.server.payment.dto.DescribePaymentsDTO;
import com.moego.server.payment.dto.FinesByTwilioResult;
import com.moego.server.payment.dto.InvoicePaymentDto;
import com.moego.server.payment.dto.MoeGoPayTransactionSummaryDto;
import com.moego.server.payment.dto.PaymentBlockDTO;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.PaymentFeeDTO;
import com.moego.server.payment.dto.PaymentListDto;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.SyncOrderPaymentDetail;
import com.moego.server.payment.params.ConvenienceFeeParams;
import com.moego.server.payment.params.CreatePaymentParams;
import com.moego.server.payment.params.DescribePaymentsParams;
import com.moego.server.payment.params.GetPaymentListParams;
import com.moego.server.payment.params.GetPaymentParams;
import com.moego.server.payment.params.ListPaymentFeeParams;
import com.moego.server.payment.params.PaymentFeeParams;
import com.moego.server.payment.params.PaymentListRequest;
import com.moego.server.payment.params.QueryPaymentParams;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/15 2:14 PM
 */
public interface IPaymentPaymentService {
    @PostMapping("/service/payment/invoice/info")
    ResponseResult<PaymentSummary> getPayments(@RequestBody GetPaymentParams paymentParams);

    @PostMapping("/service/payment/invoice/list")
    ResponseResult<List<PaymentSummary>> getPaymentList(@RequestBody GetPaymentListParams params);

    @PostMapping("/service/payment/invoice/grooming/invoice")
    List<InvoicePaymentDto> getGroomingInvoicePaymentDto(@RequestBody QueryPaymentParams queryPaymentParams);

    @GetMapping("/service/payment/getConvenienceFee")
    BigDecimal getConvenienceFee(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("amount") BigDecimal amount,
            @RequestParam("stripePaymentMethod") Byte stripePaymentMethod);

    @PostMapping("/service/payment/listConvenienceFees")
    List<ConvenienceFeeDTO> listConvenienceFees(@RequestBody List<ConvenienceFeeParams> params);

    /**
     * Create and confirm payment
     *
     * @param businessId business id
     * @param params     create payment params
     * @return payment dto
     */
    @PostMapping("/service/payment/payment/createAndConfirmPayment")
    PaymentSummary.PaymentDto createAndConfirmPayment(
            @RequestParam Integer businessId, @RequestBody CreatePaymentParams params);

    @PutMapping("/service/payment/setting/info")
    PaymentSettingDTO updatePaymentSetting(@RequestBody PaymentSettingDTO paymentSettingDTO);

    @PostMapping("/service/payment/chargeCompanyOwnerFee")
    Boolean chargeCompanyOwnerFee(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("chargeFee") BigDecimal chargeFee,
            @RequestParam("feeDesc") String feeDesc);

    @PostMapping("/service/payment/fineCompanyOwnerFeeByTwilio")
    FinesByTwilioResult fineCompanyOwnerFeeByTwilio(
            @RequestParam("companyId") Long companyId, @RequestParam("chargeFee") BigDecimal chargeFee);

    @PostMapping("/service/payment/updatePaymentRecord")
    Boolean updatePaymentRecord(@RequestBody PaymentDTO paymentDTO);

    /**
     * Get MoeGo pay (Stripe) transaction summary.
     *
     * @param businessId business id
     * @param startTime  start timestamp in seconds
     * @param endTime    end timestamp in seconds
     * @return count
     */
    @GetMapping("/service/payment/getMoeGoPayTransactionSummary")
    MoeGoPayTransactionSummaryDto getMoeGoPayTransactionSummary(
            @RequestParam int businessId, @RequestParam long startTime, @RequestParam long endTime);

    @GetMapping("/service/payment/method/info")
    PaymentDTO getPaymentMethodById(@RequestParam("paymentId") Integer paymentId);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     *
     * Filter client by paid amount
     * If there has customer id set, filter in customer id set, otherwise filter in business
     *
     * @param clientsFilterDTO filter params
     * @return filtered customer id set
     */
    @PostMapping("/service/payment/paid/filter")
    Set<Integer> listCustomerIdByFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    @PostMapping("/service/payment/cof/filter")
    Set<Integer> listCustomerIdByCreditCardFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     *
     * List customer payment info
     *
     * @param businessClientsDTO business id and customer id set
     * @return customer id to payment info map
     */
    @PostMapping("/service/payment/paid")
    Map<Integer, CustomerPaymentDTO> listCustomerPaymentInfo(@RequestBody BusinessClientsDTO businessClientsDTO);

    @PostMapping("/service/payment/cof/timestamp/refresh")
    String updateCofCodeTimestamp(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/payment/summary/failed/count/by/task")
    void summaryFailedCountByTask();

    @GetMapping("/service/payment/by/invoiceId")
    List<PaymentDTO> getPaymentsByInvoiceId(@RequestParam Integer invoiceId);

    @GetMapping("/service/payment/by/id")
    PaymentDTO getPaymentDetailById(@RequestParam Integer paymentId);

    @GetMapping("/service/payment/fraud/by/paymentId")
    PaymentBlockDTO getFraudByPaymentId(@RequestParam Integer paymentId);

    @PostMapping("/service/payment/describePayments")
    DescribePaymentsDTO describePayments(@RequestBody @Valid DescribePaymentsParams params);

    /**
     * used by open-api-v1, do not make any breaking change
     * @param customerId
     * @return
     */
    @GetMapping("/service/payment/customer/cof/link")
    String getCustomerCofLink(@RequestParam("customerId") Integer customerId);

    /**
     * according to business id query associated stripe account id or square token information
     * @param businessId id
     * @return {@link com.moego.server.payment.dto.AssociationAccountDTO}
     */
    @GetMapping("/service/payment/get/association/account")
    AssociationAccountDTO getAssociationAccountByBusinessId(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/payment/list")
    PaymentListDto listPayments(@RequestBody PaymentListRequest paymentListReq);

    @GetMapping("/service/payment/getPaymentDetail")
    SyncOrderPaymentDetail getPaymentDetail(@RequestParam("paymentId") Long paymentId);

    @PostMapping("/service/payment/listPaymentFees")
    List<PaymentFeeDTO> listPaymentFees(@RequestBody ListPaymentFeeParams params);

    @PostMapping("/service/payment/getPaymentFees")
    PaymentFeeDTO getPaymentFees(@RequestBody PaymentFeeParams params);
}
