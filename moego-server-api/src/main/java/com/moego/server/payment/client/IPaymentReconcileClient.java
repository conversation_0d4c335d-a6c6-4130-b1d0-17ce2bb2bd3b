package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentReconcileService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentReconcileService")
public interface IPaymentReconcileClient extends IPaymentReconcileService {}
