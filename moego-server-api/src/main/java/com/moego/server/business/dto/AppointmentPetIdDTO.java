package com.moego.server.business.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentPetIdDTO {

    private Integer staffId;

    private String appointmentDate;

    private Integer appointmentStartTime;

    private Integer petId;

    private Integer appointmentId;

    private List<Integer> serviceIds;
}
