package com.moego.server.business.client;

import com.moego.server.business.api.IBusinessSmartSchedulingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-business-server",
        url = "${moego.server.url.business}",
        contextId = "IBusinessSmartSchedulingClient")
public interface IBusinessSmartSchedulingClient extends IBusinessSmartSchedulingService {}
