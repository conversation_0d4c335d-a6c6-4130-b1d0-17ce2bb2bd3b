package com.moego.server.business.api;

import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollExceptionDTO;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPayrollSettingService {
    @GetMapping("/service/business/payroll/setting/business")
    BusinessPayrollSettingDTO getBusinessPayrollSetting(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/payroll/setting/company")
    BusinessPayrollSettingDTO getBusinessPayrollSettingByCompanyId(@RequestParam("companyId") Long companyId);

    @GetMapping("/service/business/payroll/setting/staff/list")
    List<StaffPayrollSettingDTO> getStaffPayrollSettingList(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/business/payroll/setting/staff/list/by/staffIds")
    List<StaffPayrollSettingDTO> getStaffPayrollSettingListByStaffIds(
            @RequestParam(value = "companyId") Long companyId, @RequestBody List<Integer> staffIds);

    @GetMapping("/service/business/payroll/setting/exception/list")
    List<PayrollExceptionDTO> getPayrollExceptionList(@RequestParam("businessId") Integer businessId);
}
