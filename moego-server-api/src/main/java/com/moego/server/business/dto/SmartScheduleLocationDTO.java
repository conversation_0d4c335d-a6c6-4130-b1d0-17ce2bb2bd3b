package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmartScheduleLocationDTO {

    private Integer id;
    private Integer businessId;
    private String startAddr;
    private String startLat;
    private String startLng;
    private AddressInfo startAddrInfo;
    private String endAddr;
    private String endLat;
    private String endLng;
    private AddressInfo endAddrInfo;
    private Integer createdBy;
    private Integer updatedBy;
    private Date createdAt;
    private Date updatedAt;

    @Schema(description = "关联的 van id 列表")
    private List<Integer> assignedVanIdList;
}
