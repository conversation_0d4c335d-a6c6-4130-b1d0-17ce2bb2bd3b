package com.moego.server.business.api;

import com.moego.server.business.dto.ReferralInfoDTO;
import com.moego.server.business.dto.ReferralStatusDTO;
import com.moego.server.business.params.ReferralInfoParams;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/6/30
 */
public interface IBusinessReferralService {
    @PutMapping("/service/business/referral/info")
    void updateReferralInfo(@RequestBody ReferralInfoParams referralInfoParams);

    @PutMapping("/service/business/referral/cancel")
    void cancelReferral(@RequestParam("companyId") Integer companyId);

    @PutMapping("/service/business/referral/active")
    void activeReferral(@RequestParam("companyId") Integer companyId);

    @PutMapping("/service/business/referral/upgraded")
    void syncUpgradedToSuccess();

    @PutMapping("/service/business/referral/fixed")
    void syncStripeBalance();

    @PutMapping("/service/business/referral/success")
    void syncSuccessToOver3Month();

    @GetMapping("/service/business/referral/code")
    ReferralStatusDTO getReferralStatus(
            @RequestParam("accountId") Integer accountId,
            @RequestParam("companyId") Integer companyId,
            @RequestParam("referralCode") String referralCode);

    @PutMapping("/service/business/referral/sync")
    void syncReferralEndStatus(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/business/referral/info")
    ReferralInfoDTO getReferralInfo(
            @RequestParam("companyId") Integer companyId, @RequestParam("accountId") Integer accountId);

    @PutMapping("/service/business/referral/balance")
    void resetReferralBalance(
            @RequestParam("companyId") Integer companyId, @RequestParam("accountId") Integer accountId);
}
