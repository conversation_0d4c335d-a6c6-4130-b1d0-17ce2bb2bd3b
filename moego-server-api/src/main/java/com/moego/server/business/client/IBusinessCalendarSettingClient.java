package com.moego.server.business.client;

import com.moego.server.business.api.IBusinessCalendarSettingService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-business-server",
        url = "${moego.server.url.business}",
        contextId = "IBusinessCalendarSettingClient")
public interface IBusinessCalendarSettingClient extends IBusinessCalendarSettingService {}
