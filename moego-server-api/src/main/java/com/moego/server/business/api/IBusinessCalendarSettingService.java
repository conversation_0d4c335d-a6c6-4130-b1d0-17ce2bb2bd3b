package com.moego.server.business.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.CalendarSettingDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessCalendarSettingService {
    @GetMapping("/service/business/calendarSetting/getCalendarSetting")
    ResponseResult<CalendarSettingDTO> getCalendarSetting(@RequestParam("tokenBusinessId") Integer tokenBusinessId);
}
