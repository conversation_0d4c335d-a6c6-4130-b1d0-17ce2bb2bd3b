package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.constant.CommonConstant;
import com.moego.common.constant.Dictionary;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Deprecated
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmartScheduleSettingDTO {

    private Integer businessId;

    // miles or kilometer
    private Integer maxDistance;

    // minutes
    private Integer maxTime;

    private String startLocationLat;
    private String startLocationLng;
    private String endLocationLat;
    private String endLocationLng;
    private String startLocationAddr;
    private String endLocationAddr;

    // miles
    private Integer serviceRange;

    private Byte unitOfDistanceType;

    /**
     * certain area for certain days, enable(1)
     */
    private Byte serviceAreaEnable;

    @JsonIgnore
    public Integer getMaxDistanceMile() {
        if (Objects.isNull(maxDistance)
                || !Objects.equals(unitOfDistanceType.intValue(), Dictionary.UNITED_DISTANCE_TYPE_2)) {
            return maxDistance;
        }
        // kilometer to mile
        return Double.valueOf(maxDistance / CommonConstant.MILE_TO_KILOMETER).intValue();
    }
}
