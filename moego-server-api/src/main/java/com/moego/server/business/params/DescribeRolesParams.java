/*
 * @since 2023-12-04 10:29:11
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.business.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeRolesParams(
        Set<Integer> companyIds,
        Set<Integer> businessIds,
        Set<Integer> ids,
        // you should manually add "%" to the beginning and end of the string.
        String nameLike,
        boolean includeDeleted,
        @NotNull @Valid Pagination pagination) {}
