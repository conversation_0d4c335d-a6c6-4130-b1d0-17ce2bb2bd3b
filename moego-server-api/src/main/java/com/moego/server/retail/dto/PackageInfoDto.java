package com.moego.server.retail.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class PackageInfoDto {

    private Integer id;
    private Long companyId;
    private Integer businessId;
    private String name;
    private String description;
    private BigDecimal price;
    private BigDecimal totalValue;
    private Integer taxId;
    private BigDecimal taxRate;
    /**
     * @deprecated by <PERSON> since 2024/7/23, use {@link #expirationDays} instead
     */
    @Deprecated(since = "2024/7/23")
    private String startDate;
    /**
     * @deprecated by <PERSON> since 2024/7/23, use {@link #expirationDays} instead
     */
    @Deprecated(since = "2024/7/23")
    private String endDate;

    private Integer soldQuantity;
    private Long createTime;
    private Long updateTime;
    /**
     * @deprecated by <PERSON> since 2024/7/17, use {@link #isActive} instead
     */
    @Deprecated(since = "2024/7/17")
    private String status;

    private Boolean isActive;
    private Boolean enableOnlineBooking;
    /**
     * 用一个魔法值 {@link ExpirationDays#NEVER_EXPIRE} 来表示永不过期
     *
     * @see com.moego.server.retail.dto.PackageInfoDto.ExpirationDays
     */
    private Integer expirationDays;

    /**
     * @deprecated by Freeman, use {@link #items} instead
     */
    @Deprecated(since = "2024/10/21")
    private List<ServiceInfo> services;

    /**
     * package 包含的 services，多个 service 共享 quantity
     */
    private List<Item> items;

    public void addService(Integer serviceId, BigDecimal unitPrice, Integer quantity) {
        if (this.services == null) {
            this.services = new ArrayList<>();
        }
        this.services.add(new ServiceInfo(serviceId, unitPrice, quantity));
    }

    @Data
    public static class Item {
        private Integer quantity;
        private List<Service> services;
    }

    @Data
    public static class Service {
        private Integer serviceId;
        private BigDecimal unitPrice;
        private String name;
    }

    @Data
    public static class ServiceInfo {

        private Integer serviceId;
        private Integer quantity;
        private BigDecimal unitPrice;

        private String name;
        private String description;
        private BigDecimal originalPrice;

        public ServiceInfo() {}

        public ServiceInfo(Integer serviceId, BigDecimal unitPrice, Integer quantity) {
            this.serviceId = serviceId;
            this.unitPrice = unitPrice;
            this.quantity = quantity;
        }
    }

    public interface ExpirationDays {
        Integer NEVER_EXPIRE = 99999999;
    }
}
