package com.moego.server.retail.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.common.dto.PaymentSummary;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class InvoiceDto {

    private Integer id;
    private Integer businessId;
    private Integer customerId;
    private Integer staffId;
    private String saleType;
    private String staffName;
    private String customerName;
    private String title;
    private BigDecimal subTotalAmount;
    private BigDecimal discountAmount;
    private BigDecimal discountedSubTotalAmount;
    private BigDecimal tipAmount;
    private BigDecimal taxAmount;
    private BigDecimal totalAmount;
    private BigDecimal paymentAmount;
    private Integer quantity;
    private String status;
    private Long createTime;
    private Long updateTime;
    private BigDecimal remainAmount;
    private BigDecimal paidAmount;
    private List<InvoiceItemDto> items;
    private List<InvoiceRemoveLogDto> removeLogs;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PaymentSummary paymentSummary;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String businessName;

    private String appointmentDate; // 如果 product 订单是 appointment invoice，额外查询预约时间，仅展示，不支持排序
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;

    @Data
    public static class InvoiceItemDto {

        private Integer id;
        private Integer invoiceId;
        private Integer productId;
        private Integer packageId;
        private String name;
        private String description;
        private String sku;
        private BigDecimal unitPrice;
        private BigDecimal taxRate;
        private Integer quantity;
        private BigDecimal totalListPrice;
        private BigDecimal totalSalePrice;
        private BigDecimal discountAmount;
        private BigDecimal taxAmount;
        private Long createTime;
        private Long updateTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private PackageInfoDto packageInfo;
    }
}
