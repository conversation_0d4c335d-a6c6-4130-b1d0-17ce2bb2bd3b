package com.moego.server.message.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-07-05 19:58
 */
@Data
public class MessageSendResponseDTO {

    /**
     * 可能一个 twilio mms 对应多个 message detail，比如 mms
     */
    private List<Integer> messageDetailIdList;

    private Integer messageDetailId;

    private String messageSid;

    private Integer sendStatus;

    private String numSegments;

    public static final Integer success = 200;

    private static final Integer fail = 400;

    private Integer errorCode;

    private String errorMessage;

    private MessageSendResponseDTO(Integer messageDetailId, Integer sendStatus) {
        this.sendStatus = sendStatus;
        this.messageDetailId = messageDetailId;
    }

    private MessageSendResponseDTO(List<Integer> messageDetailIdList, Integer sendStatus) {
        this.sendStatus = sendStatus;
        this.messageDetailIdList = messageDetailIdList;
    }

    public static MessageSendResponseDTO sendFailResponse(Integer messageDetailId) {
        MessageSendResponseDTO failResponse = new MessageSendResponseDTO(messageDetailId, fail);
        return failResponse;
    }

    public static MessageSendResponseDTO sendFailResponse(List<Integer> messageDetailId) {
        MessageSendResponseDTO failResponse = new MessageSendResponseDTO(messageDetailId, fail);
        return failResponse;
    }

    public static MessageSendResponseDTO sendFailResponse(
            Integer messageDetailId, Integer errorCode, String errorMessage) {
        MessageSendResponseDTO failResponse = new MessageSendResponseDTO(messageDetailId, fail);
        failResponse.errorCode = errorCode;
        failResponse.errorMessage = errorMessage;
        return failResponse;
    }

    public static MessageSendResponseDTO sendFailResponse(
            List<Integer> messageDetailIdList, Integer errorCode, String errorMessage) {
        MessageSendResponseDTO failResponse = new MessageSendResponseDTO(messageDetailIdList, fail);
        failResponse.errorCode = errorCode;
        failResponse.errorMessage = errorMessage;
        return failResponse;
    }

    public static MessageSendResponseDTO sendSuccessResponse(Integer messageDetailId) {
        return sendSuccessResponse(messageDetailId, null);
    }

    public static MessageSendResponseDTO sendSuccessResponse(List<Integer> messageDetailIdList) {
        return sendSuccessResponse(messageDetailIdList, null);
    }

    public static MessageSendResponseDTO sendSuccessResponse(Integer messageDetailId, String messageSid) {
        MessageSendResponseDTO successResponse = new MessageSendResponseDTO(messageDetailId, success);
        if (null != messageSid) {
            successResponse.setMessageSid(messageSid);
        }
        return successResponse;
    }

    public static MessageSendResponseDTO sendSuccessResponse(List<Integer> messageDetailIdList, String messageSid) {
        MessageSendResponseDTO successResponse = new MessageSendResponseDTO(messageDetailIdList, success);
        if (null != messageSid) {
            successResponse.setMessageSid(messageSid);
        }
        return successResponse;
    }

    public boolean sendSuccess() {
        if (success.equals(sendStatus)) {
            return true;
        }
        return false;
    }
}
