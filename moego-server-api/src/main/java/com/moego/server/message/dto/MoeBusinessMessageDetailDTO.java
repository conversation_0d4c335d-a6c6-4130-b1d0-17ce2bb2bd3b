package com.moego.server.message.dto;

import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-09-28 00:47
 */
@Data
public class MoeBusinessMessageDetailDTO {

    private CustomerContactDto messageReceiverDTO;

    private GroomingCustomerInfoDTO groomingCustomerInfoDTO;

    private MoeStaffDto moeStaffDto;

    private Integer id;
    private Integer targetType;
    private Integer targetId;
    private String messageSid;
    private Integer businessId;
    private Integer staffId;
    private Integer customerId;
    private String phoneNumber;
    private String messageText;
    private String mediaUrl1;
    private Integer type;
    private Integer method;
    private Integer isRead;
    private Integer customerIsRead;
    private Integer readTime;
    private Integer status;
    private Integer createTime;
    private Integer deleteTime;
    private Integer unconfirmApptid;
    private Integer isSuccessed;
    private Integer errorCode;
    private String errorMessage;
    private Integer source;
    private String contactName;
    private Integer messageType;
    private Integer deleteBy;

    @Schema(description = "Message 关联的信息，目前只有 grLinkOpenedCount，可能为空，不同类型的 Message 关联的信息不同")
    private MessageExtraInfo extraInfo;
}
