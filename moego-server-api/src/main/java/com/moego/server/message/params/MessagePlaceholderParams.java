package com.moego.server.message.params;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessagePlaceholderParams {

    @Size(max = 2000)
    private String text;

    @Min(0)
    private Long businessId;

    @Min(0)
    private Long customerId;

    @Min(0)
    private Long appointmentId;
}
