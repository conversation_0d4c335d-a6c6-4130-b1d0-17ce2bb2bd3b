package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/2/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationAssignedTaskParams extends NotificationParams {
    private String title = "Task assigned";
    private String batchTitle = "{assignCount} Tasks assigned";
    private String type = NotificationEnum.TYPE_ACTIVITY_ASSIGNED_TASK;
    private AssignedTaskExtra webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private Boolean isNotifyBusinessAllStaff = true;
    private String mobilePushTitle = "Task assigned";
    private String mobilePushBody = "Task on {taskDateTime} has been assigned to you";
    private String mobilePushBatchBody = "{assignCount} tasks have been assigned to you";

    @Data
    public static class AssignedTaskExtra {
        @NotNull
        private String startDate;

        /**
         * Add-on 可能没有 time
         */
        @Nullable
        private Integer startTime;

        @NotNull
        private Integer assignCount;
    }

    public String getTitle() {
        var isAssignedMulti = webPushDto != null && webPushDto.getAssignCount() > 1;
        if (isAssignedMulti) {
            return batchTitle.replace(
                    NotificationEnum.TEMPLATE_TASK_ASSIGN_COUNT, String.valueOf(webPushDto.getAssignCount()));
        }

        return title;
    }
}
