package com.moego.server.message.params.notification.ob;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import com.moego.common.enums.ClientApptConst;
import com.moego.server.message.params.notification.NotificationParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NotificationOBRequestCanceledParams extends NotificationParams {

    private String title = ClientApptConst.OBRequestNotificationTitle.CANCELED_BY_CLIENT;
    private String type = NotificationEnum.TYPE_ACTIVITY_ONLINE_BOOKING_REQUEST_CANCEL;
    private NotificationExtraApptCommonDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = ClientApptConst.OBRequestNotificationTitle.CANCELED_BY_CLIENT;
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
