package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationNewAbandonedBookingsParams extends NotificationParams {

    private String title = "New abandoned booking";
    private String body = """
        Abandoned step: %s
        %s %s"""
            .formatted(Variable.ABANDON_STEP, Variable.CLIENT_FIRST_NAME, Variable.CLIENT_LAST_NAME);
    private String type = NotificationEnum.TYPE_ACTIVITY_NEW_ABANDONED_BOOKINGS;
    private Extra webPushDto = new Extra();
    private Boolean isNotifyBusinessOwner = true;
    private Boolean isNotifyBusinessAllStaff = true;
    private String mobilePushTitle = "New abandoned booking";
    private String mobilePushBody =
            "%s %s %s".formatted(Variable.CLIENT_FIRST_NAME, Variable.CLIENT_LAST_NAME, Variable.ABANDON_STEP);
    /**
     * 用来替换 {@link #title}, {@link #body}, {@link #mobilePushTitle}, {@link #mobilePushBody} 中的变量
     */
    private Variable variable = new Variable();

    @Data
    @Accessors(chain = true)
    public static class Variable {
        private static final String CLIENT_FIRST_NAME = "{customerFirstName}";
        private static final String CLIENT_LAST_NAME = "{customerLastName}";
        private static final String ABANDON_STEP = "{abandonStep}";

        private String customerFirstName;
        private String customerLastName;
        private String abandonStep;
    }

    @Data
    @Accessors(chain = true)
    public static class Extra {
        private String customerFirstName;
        private String customerLastName;
        private String abandonStep;
    }

    /**
     * 替换 {@link #title}, {@link #body}, {@link #mobilePushTitle}, {@link #mobilePushBody} 中的变量
     */
    public void replaceVariable() {
        if (variable != null) {
            title = replaceVariable(title, variable);
            body = replaceVariable(body, variable);
            mobilePushTitle = replaceVariable(mobilePushTitle, variable);
            mobilePushBody = replaceVariable(mobilePushBody, variable);
        }
    }

    private static String replaceVariable(String template, Variable variable) {
        if (variable != null && template != null) {
            template = template.replace(
                    Variable.CLIENT_FIRST_NAME,
                    Optional.ofNullable(variable.getCustomerFirstName()).orElse(""));
            template = template.replace(
                    Variable.CLIENT_LAST_NAME,
                    Optional.ofNullable(variable.getCustomerLastName()).orElse(""));
            template = template.replace(
                    Variable.ABANDON_STEP,
                    Optional.ofNullable(variable.getAbandonStep()).orElse(""));
        }
        return template;
    }
}
