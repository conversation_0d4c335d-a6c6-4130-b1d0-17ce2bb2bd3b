package com.moego.server.message.api;

import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.server.message.dto.DescribeMessageThreadsDTO;
import com.moego.server.message.dto.MessageThreadDTO;
import com.moego.server.message.dto.VisibleThreadCustomerDTO;
import com.moego.server.message.params.UpdateCustomerBlockParams;
import com.moego.server.message.params.UpdateCustomerParams;
import com.moego.server.message.vo.DescribeMessageThreadsVO;
import jakarta.validation.Valid;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IMessageThreadService {

    /**
     * Insert a message thread.
     *
     * @param dto {@link MessageThreadDTO}
     * @return inserted id
     */
    @PostMapping("/service/message/message-thread/insert")
    int insert(@RequestBody MessageThreadDTO dto);

    /**
     * TODO(account structure): company 维度对 customer 进行 block？
     */
    @PutMapping("/service/message/thread/block")
    Boolean updateCustomerBlock(@RequestBody UpdateCustomerBlockParams updateCustomerBlockParams);

    /**
     * TODO(account structure): 迁移后更新 company 维度的数据
     */
    @PutMapping("/service/message/thread/customer")
    Boolean updateCustomerInfo(@RequestBody UpdateCustomerParams updateCustomerParams);

    /**
     * TODO(account structure): 兼容 company 维度删除
     */
    @PostMapping("/service/message/thread/customer/batchDelete")
    Boolean batchDeleteByCustomerId(@RequestBody BusinessClientsDTO businessClientsDTO);

    @GetMapping("/service/message/thread/getMessageThreadById")
    MessageThreadDTO getMessageThreadById(@RequestParam("messageThreadId") Integer messageThreadId);

    /**
     * describe message threads
     */
    @PostMapping("/service/message/thread/describeMessageThreads")
    DescribeMessageThreadsDTO describeMessageThreads(@RequestBody @Valid DescribeMessageThreadsVO vo);

    /**
     * Get visible thread customer list for specified staff
     *
     * @param businessId business id
     * @param staffId    staff id
     * @return visible thread customer id list
     */
    @PostMapping("/service/message/thread/getVisibleThreadCustomerList")
    VisibleThreadCustomerDTO getVisibleThreadCustomerList(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId);

    /**
     *
     * Filter client by cof link status
     * If there has customer id set, filter in customer id set, otherwise filter in business
     *
     * @param clientsFilterDTO filter params
     * @return filtered customer id set
     */
    @PostMapping("/service/message/cof/link")
    Set<Integer> listCustomerIdByFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);
}
