package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraInvoicePaidDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationInvoicePaidParams extends NotificationParams {

    private String title = "Invoice paid";
    private String type = NotificationEnum.TYPE_ACTIVITY_INVOICE_PAID;
    private Boolean isNotifyBusinessAllStaff = true;
    private NotificationExtraInvoicePaidDto webPushDto;
    private String mobilePushTitle = "Invoice paid";
    private String mobilePushBody = "{totalPaidAmount} has been paid by {customerFullName}";
}
