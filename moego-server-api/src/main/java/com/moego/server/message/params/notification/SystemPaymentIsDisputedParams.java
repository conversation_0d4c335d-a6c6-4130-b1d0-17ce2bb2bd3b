package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemPaymentIsDisputedParams extends NotificationParams {

    private String title = "Payment is disputed";
    private String type = NotificationEnum.TYPE_SYSTEM_PAYMENT_DISPUTED;
    private NotificationExtraApptCommonDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "";
    private String mobilePushBody = "";
}
