package com.moego.server.message.api;

import com.moego.common.dto.PageDTO;
import com.moego.server.message.dto.ApptReminderDetailDTO;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import com.moego.server.message.dto.BirthdayReminderDetailDTO;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.dto.MessageForPricingDataDto;
import com.moego.server.message.dto.RebookReminderDetailDTO;
import com.moego.server.message.dto.RepeatExpitySettingDto;
import com.moego.server.message.params.AutoMessageParams;
import com.moego.server.message.params.CardLinkParam;
import com.moego.server.message.params.ReminderDetailParams;
import java.util.List;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IMessageService {

    @GetMapping("/service/message/cycle/all")
    @Deprecated
    Map<Integer, Integer> queryAllCompanySubscriptionAmount(@RequestParam("companyIds") List<Integer> companyIds);

    @GetMapping("/service/message/queryMessageForPricingDataDto")
    MessageForPricingDataDto queryMessageForPricingDataDto(@RequestParam("companyIds") List<Integer> companyIds);

    @GetMapping("/service/message/business/twilio")
    BusinessTwilioNumberDTO getBusinessTwilioNumber(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/message/businesses/twilio")
    List<BusinessTwilioNumberDTO> listBusinessTwilioNumber(
            @RequestParam("businessIdList") List<Integer> businessIdList);

    @GetMapping("/service/message/getMessageById")
    MessageDetailDTO getMessageById(@RequestParam("messageId") Integer messageId);

    /**
     * 查询 arrival window 的设置信息
     */
    @GetMapping("/service/message/getArrivalWindow")
    ArrivalWindowSettingDto getArrivalWindow(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/message/listArrivalWindow")
    List<ArrivalWindowSettingDto> listArrivalWindow(@RequestParam("businessIdList") List<Integer> businessIdList);

    @PostMapping("/service/message/autoMessage/sent")
    List<MessageDetailDTO> getAutoMessageSentRecords(@RequestBody @Validated AutoMessageParams params);

    @PostMapping("/service/message/card/link/marked")
    Integer markAsSubmitted(@RequestParam("customerId") Integer customerId);

    @PostMapping("/service/message/card/link")
    Integer createOrUpdateCardLink(@RequestBody CardLinkParam cardLinkParam);

    @GetMapping("/service/message/reminder/setting/repeat/expiry")
    RepeatExpitySettingDto getRepeatExpirySetting(@RequestParam("businessId") Integer businessId);

    // 根据 messageSid，查询 bid,clientId,sendTime
    @GetMapping("/service/message/queryDetailByMessageSidId")
    MessageDetailDTO queryDetailByMessageSid(
            @RequestParam("companyId") Long companyId, @RequestParam("messageSidId") String messageSid);

    @GetMapping("/service/message/verifyMessageQuota")
    Boolean verifyMessageQuota(
            @RequestParam("companyId") Long companyId, @RequestParam("messageCount") Integer messageCount);

    /**
     * 查询预约提醒详情，api-v3 调用，待 auto-message 系统完全上线后，会废弃
     * @param reminderDetailParams
     * @return
     */
    @PostMapping("/service/message/detailAppointmentReal")
    PageDTO<ApptReminderDetailDTO> detailAppointmentReal(@RequestBody ReminderDetailParams reminderDetailParams);

    /**
     * 查询 rebook 预约提醒详情，api-v3 调用，待 auto-message 系统完全上线后，会废弃
     * @param reminderDetailParams
     * @return
     */
    @PostMapping("/service/message/rebookReminderDetail")
    PageDTO<RebookReminderDetailDTO> rebookReminderDetail(@RequestBody ReminderDetailParams reminderDetailParams);

    /**
     * 查询生日提醒详情，api-v3 调用，待 auto-message 系统完全上线后，会废弃
     * @param reminderDetailParams
     * @return
     */
    @PostMapping("/service/message/detailPetBirthday")
    PageDTO<BirthdayReminderDetailDTO> detailPetBirthday(@RequestBody ReminderDetailParams reminderDetailParams);
}
