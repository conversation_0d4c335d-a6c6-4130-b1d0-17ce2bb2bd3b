package com.moego.server.message.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class PayOnlineEmailDto {

    public static final int TYPE_TOTAL_PAID_EMAIL = 1;
    public static final int TYPE_DEPOSIT_EMAIL = 2;

    @NotNull
    Integer invoiceId;

    List<Integer> invoiceIds;

    @NotNull
    String email;

    @NotNull
    @Range(min = 1, max = 2)
    Integer type; // 1-total paid, 2-deposit

    @Positive
    BigDecimal depositAmount;

    @JsonIgnore
    Integer businessId;

    @JsonIgnore
    Integer staffId;

    @Schema(description = "顾客支付时是否需要添加processing fee")
    private Boolean requiredProcessingFee;
}
