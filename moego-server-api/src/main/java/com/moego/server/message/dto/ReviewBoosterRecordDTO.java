package com.moego.server.message.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReviewBoosterRecordDTO {

    private Integer id;
    private Long companyId;
    private Integer reviewBoosterId;
    private Integer businessId;
    private Integer customerId;
    private Integer positiveScore;
    private Integer appointmentId;
    private String appointmentDate;
    private String reviewContent;
    private Integer reviewTime;
    private Byte source;
    private List<Integer> staffIds;
    private List<Integer> petIds;
    private Integer createTime;
    private Integer updateTime;
}
