package com.moego.server.message.params;

import com.moego.server.message.enums.MessageDetailEnum;
import lombok.Builder;
import lombok.Data;

/**
 * 简化版本的消息发送结构，包含发送一条消息的最少必要字段。
 * 本结构主要是相对于 SendMessageParams 而言进行的简化(后者太复杂)
 * 本结构中的所有字段由调用者负责构造，在使用时请当作只读对象使用。
 */
@Data
@Builder
public class SendMsgParams {

    private Integer businessId;
    private Integer customerId;
    private Integer staffId;
    private Integer targetId;

    // see MessageTargetTypeEnums:
    // 1: thread
    // 2: batch
    // 30: auto agreement
    // 31: agreement
    // 40: review
    // 41: review booster reply
    // 501: appointment first
    // 502: appointment second
    // 503: appointment remind
    // 60: auto appointment book
    // 61: auto appointment rebook
    // 62: auto appointment canceled
    // ...
    private Integer targetType;

    // see MessageDetailEnum: 1-sms 2-email 4-call 5-app
    private Integer msgChannelType;

    // see MessageDetailEnum: 1-text 2-image
    private Integer msgContentType;

    // see MessageDetailEnum: 1-by-business 2-by-customer
    private Integer senderType;

    // see MessageDetailEnum:
    // 1: auto msg/email/call
    // 2: customer email
    // 3: employee
    // 4: message center
    // 5: verification code
    // 6: voice auto reply
    private Integer source;

    // sms
    private String fromPhoneNumber;
    private String toPhoneNumber;
    private String msgBody;
    private String msgMedia;

    // email
    private String fromEmail;
    private String toEmail;
    private String senderName;
    private String receiverName;
    private String emailTitle;
    private String emailContent;

    // strategy
    private Boolean skipUpdateThread;
    private Boolean skipOptoutCheck;

    public String getMsgContent() {
        if (MessageDetailEnum.MESSAGE_METHOD_MSG.getValue().equals(msgChannelType)) {
            if (MessageDetailEnum.MESSAGE_TYPE_TEXT.getValue().equals(msgChannelType)) {
                return msgBody;
            } else {
                return msgMedia;
            }
        } else if (MessageDetailEnum.MESSAGE_METHOD_EMAIL.getValue().equals(msgChannelType)) {
            return emailContent;
        } else {
            return null;
        }
    }
}
