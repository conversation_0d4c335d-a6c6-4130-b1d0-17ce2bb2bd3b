package com.moego.lib.common.grpc.client;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.api.todo.v1.TodoServiceGrpc;
import io.grpc.health.v1.HealthGrpc;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@SpringBootTest(
        classes = GrpcClientAutowiredIntegrationTests.Cfg.class,
        properties = {"moego.grpc.client.base-packages[0]=com.moego", "moego.grpc.client.base-packages[1]=io.grpc"})
public class GrpcClientAutowiredIntegrationTests {

    @Autowired
    private TodoServiceGrpc.TodoServiceBlockingStub todoBlockingStub;

    @Autowired
    private TodoServiceGrpc.TodoServiceFutureStub todoFutureStub;

    @Autowired
    private TodoServiceGrpc.TodoServiceStub todoStub;

    @Autowired
    private HealthGrpc.HealthBlockingStub healthBlockingStub;

    @Test
    void testAutowired() {
        assertThat(todoBlockingStub).isNotNull();
        assertThat(todoFutureStub).isNotNull();
        assertThat(todoStub).isNotNull();
        assertThat(healthBlockingStub).isNotNull();
    }

    @Test
    void testReuseChannel() {
        assertThat(todoBlockingStub.getChannel() == todoFutureStub.getChannel()).isTrue();
        assertThat(todoBlockingStub.getChannel() == todoStub.getChannel()).isTrue();
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
