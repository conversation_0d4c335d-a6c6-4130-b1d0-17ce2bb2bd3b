package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.freemanan.cr.core.anno.Verb;
import com.moego.lib.common.auth.AuthMetadataProcessor;
import com.moego.lib.common.grey.VersionMetadataProcessor;
import com.moego.lib.common.observability.tracing.TracingMetadataProcessor;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataClientInterceptor;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataServerInterceptor;
import com.moego.lib.common.observability.tracing.http.feign.MetadataRequestInterceptor;
import com.moego.lib.common.observability.tracing.http.mvc.MetadataFilter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Metadata} tester.
 */
@ClasspathReplacer({@Action(verb = Verb.EXCLUDE, value = "spring-cloud-openfeign-core-*.jar")})
public class MetadataWebAppNoFeignTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(Metadata.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(MetadataFilter.class);
            assertThat(context).doesNotHaveBean(MetadataRequestInterceptor.class);
            assertThat(context).hasSingleBean(GrpcMetadataServerInterceptor.class);
            assertThat(context).hasSingleBean(GrpcMetadataClientInterceptor.class);

            assertThat(context).hasSingleBean(AuthMetadataProcessor.class);
            assertThat(context).hasSingleBean(TracingMetadataProcessor.class);
            assertThat(context).doesNotHaveBean(VersionMetadataProcessor.class);
        });
    }
}
