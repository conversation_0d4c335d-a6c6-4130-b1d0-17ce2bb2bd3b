package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.logging.grpc.LoggingServerInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * {@link Logging} tester.
 */
public class LoggingWebAppTest {

    private final WebApplicationContextRunner runner = new WebApplicationContextRunner()
            .withUserConfiguration(Logging.class)
            .withBean(HttpProperties.class, () -> mock(HttpProperties.class));

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(WebMvcConfigurer.class);
            assertThat(context).doesNotHaveBean(LoggingServerInterceptor.class);
        });
    }

    @Test
    public void testHttpDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(WebMvcConfigurer.class);
        });
    }

    @Test
    public void testHttpEnabledButHttpServerDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(WebMvcConfigurer.class);
                });
    }

    @Test
    public void testHttpDisableLogging() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.observability.logging.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(WebMvcConfigurer.class);
                });
    }
}
