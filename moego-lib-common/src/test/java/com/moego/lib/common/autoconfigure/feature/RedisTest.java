package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link Redis}
 */
class RedisTest {

    final ApplicationContextRunner runner = new ApplicationContextRunner().withUserConfiguration(Redis.class);

    @Test
    void whenNoRedisDependency_thenNoBean() {
        runner.run(ctx -> {
            assertThat(ctx).doesNotHaveBean(Redis.class);
            assertThat(ctx).doesNotHaveBean("disablePeerVerificationLettuceClientConfigurationBuilderCustomizer");
        });
    }

    @Test
    @ClasspathReplacer(@Action("org.springframework.boot:spring-boot-starter-data-redis:3.1.4"))
    void whenHasRedisDependencyAndUsingLocalProfile_thenHasBean() {
        runner.withPropertyValues("spring.profiles.active=local").run(ctx -> {
            assertThat(ctx).hasSingleBean(Redis.class);
            assertThat(ctx).hasBean("disablePeerVerificationLettuceClientConfigurationBuilderCustomizer");
        });
    }

    @Test
    @ClasspathReplacer(@Action("org.springframework.boot:spring-boot-starter-data-redis:3.1.4"))
    void whenHasRedisDependencyAndNotUsingLocalProfile_thenHasRedisBeanAndNoCustomizerBean() {
        runner.run(ctx -> {
            assertThat(ctx).hasSingleBean(Redis.class);
            assertThat(ctx).doesNotHaveBean("disablePeerVerificationLettuceClientConfigurationBuilderCustomizer");
        });

        runner.withPropertyValues("spring.profiles.active=prod").run(ctx -> {
            assertThat(ctx).hasSingleBean(Redis.class);
            assertThat(ctx).doesNotHaveBean("disablePeerVerificationLettuceClientConfigurationBuilderCustomizer");
        });
    }
}
