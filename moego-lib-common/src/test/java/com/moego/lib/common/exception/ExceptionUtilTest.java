package com.moego.lib.common.exception;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.rpc.Status;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.errors.v1.CommonError;
import com.moego.lib.common.exception.grpc.GrpcExceptionAdvice;
import com.moego.lib.common.exception.http.CommonResponse;
import com.moego.lib.common.exception.http.HttpExceptionUtil;
import feign.Request;
import feign.codec.DecodeException;
import io.grpc.Metadata;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

/**
 * {@link ExceptionUtil} tester.
 */
class ExceptionUtilTest {

    static GrpcExceptionAdvice advice = new GrpcExceptionAdvice();

    static BizException bizException;
    static Status status;

    static {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "freeman");
        map.put("age", 22);
        bizException = new BizException(Code.CODE_FORBIDDEN_VALUE, "Forbidden", map, new RuntimeException("test"));
    }

    /**
     * {@link ExceptionUtil#extractDetails(StatusRuntimeException)}.
     */
    @Test
    void extractDetails() {
        // normal case
        StatusRuntimeException sre = advice.handleBizException(bizException);
        byte[] bytes = ExceptionUtil.extractDetails(sre);

        assertThat(bytes).hasSizeGreaterThan(0);

        // no details
        StatusRuntimeException e =
                io.grpc.Status.fromCode(io.grpc.Status.Code.UNAVAILABLE).asRuntimeException();
        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> ExceptionUtil.extractDetails(e))
                .isEqualTo(e);

        // not contains 'grpc-status-details-bin' key
        StatusRuntimeException exception =
                io.grpc.Status.fromCode(io.grpc.Status.Code.UNAVAILABLE).asRuntimeException(new Metadata());
        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> ExceptionUtil.extractDetails(exception))
                .isEqualTo(exception);
    }

    /**
     * {@link ExceptionUtil#extractDetailsStatus(StatusRuntimeException)}.
     */
    @Test
    void extractStatus() {
        StatusRuntimeException sre = advice.handleBizException(bizException);

        Status status = ExceptionUtil.extractDetailsStatus(sre);

        assertThat(status).isNotNull();
        assertThat(status.getCode()).isEqualTo(com.google.rpc.Code.PERMISSION_DENIED_VALUE);
        assertThat(status.getMessage()).isEqualTo("Forbidden");
        assertThat(status.getDetailsCount()).isEqualTo(1);
        assertThat(status.getDetails(0).getTypeUrl())
                .isEqualTo("type.googleapis.com/moego.models.errors.v1.CommonError");
    }

    /**
     * {@link ExceptionUtil#extractCommonError(StatusRuntimeException)}.
     */
    @Test
    void extractCommonError() throws InvalidProtocolBufferException {
        StatusRuntimeException sre = advice.handleBizException(bizException);

        CommonError err = ExceptionUtil.extractCommonError(sre);

        assertThat(err.getCode()).isEqualTo(Code.CODE_FORBIDDEN);
        assertThat(err.getMessage()).isEqualTo("Forbidden");
        assertThat(err.getCausedBy()).isEqualTo("java.lang.RuntimeException: test");
        assertThat(err.getData().getTypeUrl()).isEqualTo("type.googleapis.com/google.protobuf.Value");
        Struct data = err.getData().unpack(Value.class).getStructValue();
        assertThat(data.getFieldsMap().get("name").getStringValue()).isEqualTo("freeman");
        assertThat(data.getFieldsMap().get("age").getNumberValue()).isEqualTo(22);

        // not CommonError, throw the raw exception
        StatusRuntimeException statusRuntimeException = StatusProto.toStatusRuntimeException(Status.newBuilder()
                .setCode(com.google.rpc.Code.PERMISSION_DENIED_VALUE)
                .addDetails(Any.pack(Value.newBuilder().setBoolValue(true).build()))
                .build());
        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> ExceptionUtil.extractCommonError(statusRuntimeException))
                .isEqualTo(statusRuntimeException);
    }

    /**
     * {@link ExceptionUtil#extractCode(StatusRuntimeException)}.
     */
    @Test
    void extractCode() {
        StatusRuntimeException sre = advice.handleBizException(bizException);

        Code code = ExceptionUtil.extractCode(sre);

        assertThat(code).isEqualTo(Code.CODE_FORBIDDEN);
    }

    /**
     * {@link ExceptionUtil#extractMessage(Code)}}.
     */
    @Test
    void extractMessage() {
        String msg = ExceptionUtil.extractMessage(Code.CODE_FORBIDDEN);

        assertThat(msg).isEqualTo("Forbidden");

        msg = ExceptionUtil.extractMessage(Code.UNRECOGNIZED);
        assertThat(msg).isEqualTo("Unrecognized");

        msg = ExceptionUtil.extractMessage(Code.CODE_CUSTOMER_INVALID_CODE);
        assertThat(msg).isEqualTo("Customer invalid code");

        msg = ExceptionUtil.extractMessage(Code.CODE_CUSTOMER_INVALID_CODE);
        assertThat(msg).isEqualTo("Customer invalid code");
    }

    @Test
    void testDataNotNull_whenError() {
        BizException e = ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);

        assertThat(e.getMessage()).isEqualTo("Params error");
        assertThat(e.getData()).isEqualTo("Params error");
        assertThat(e.getCode()).isEqualTo(Code.CODE_PARAMS_ERROR.getNumber());
        assertThat(e.getCause()).isNull();
        assertThat(e.getCausedBy()).isNull();

        CommonResponse resp = HttpExceptionUtil.toCommonResponse(e);
        assertThat(resp.getCode()).isEqualTo(Code.CODE_PARAMS_ERROR.getNumber());
        assertThat(resp.getMessage()).isEqualTo("Params error");
        assertThat(resp.getData()).isEqualTo("Params error");
        assertThat(resp.getCausedBy()).isNull();
        assertThat(resp.getSuccess()).isFalse();
    }

    @Test
    void testIsBizException() {
        BizException e = ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        assertThat(ExceptionUtil.isBizException(e)).isTrue();

        RuntimeException re = new RuntimeException("test");
        assertThat(ExceptionUtil.isBizException(re)).isFalse();

        re = new RuntimeException("test", e);
        assertThat(ExceptionUtil.isBizException(re)).isTrue();
    }

    @Test
    void extractBizException() {
        BizException e = ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        assertThat(ExceptionUtil.extractBizException(e)).isEqualTo(e);

        DecodeException decodeException = new DecodeException(
                400, "", Request.create(Request.HttpMethod.GET, "", new HashMap<>(), (byte[]) null, null), e);
        assertThat(ExceptionUtil.extractBizException(decodeException)).isEqualTo(e);

        assertThat(ExceptionUtil.extractBizException(new RuntimeException("not BizException")))
                .isNull();
    }
}
