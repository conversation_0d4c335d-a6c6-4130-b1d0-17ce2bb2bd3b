package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsClientInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsHttpServer;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsServerInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.http.HttpMetricsServletRegistrationBean;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetricsAspect;
import com.moego.lib.common.thread.ThreadPoolMetricExporter;
import com.moego.lib.common.thread.http.TomcatThreadPoolRegister;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Metrics} tester.
 */
class MetricsWebAppTest {

    private final WebApplicationContextRunner runner = new WebApplicationContextRunner()
            .withUserConfiguration(Metrics.class)
            .withBean(HttpProperties.class, HttpProperties::new)
            .withBean(GrpcProperties.class, GrpcProperties::new);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(ThreadPoolMetricExporter.class);
            assertThat(context).hasSingleBean(TimerMetricsAspect.class);

            assertThat(context).hasBean("moeMetricsWebMvcConfigurer");
            assertThat(context).hasSingleBean(HttpMetricsServletRegistrationBean.class);
            assertThat(context).hasSingleBean(TomcatThreadPoolRegister.class);

            assertThat(context).hasSingleBean(MetricsServerInterceptor.class);
            assertThat(context).hasSingleBean(MetricsClientInterceptor.class);
            assertThat(context).doesNotHaveBean(MetricsHttpServer.class);
        });
    }

    @Test
    public void testHttpServerDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(ThreadPoolMetricExporter.class);
                    assertThat(context).hasSingleBean(TimerMetricsAspect.class);

                    assertThat(context).doesNotHaveBean("moeMetricsWebMvcConfigurer");
                    assertThat(context).hasSingleBean(HttpMetricsServletRegistrationBean.class);
                    assertThat(context).doesNotHaveBean(TomcatThreadPoolRegister.class);

                    assertThat(context).hasSingleBean(MetricsServerInterceptor.class);
                    assertThat(context).doesNotHaveBean(MetricsHttpServer.class);
                });
    }

    @Test
    public void testDisableMetrics() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.observability.metrics.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(ThreadPoolMetricExporter.class);
                    assertThat(context).hasSingleBean(TimerMetricsAspect.class);

                    assertThat(context).doesNotHaveBean("moeMetricsWebMvcConfigurer");
                    assertThat(context).hasSingleBean(HttpMetricsServletRegistrationBean.class);
                    assertThat(context).doesNotHaveBean(TomcatThreadPoolRegister.class);

                    assertThat(context).hasSingleBean(MetricsServerInterceptor.class);
                    assertThat(context).doesNotHaveBean(MetricsHttpServer.class);
                });
    }
}
