package com.moego.lib.common.grpc.server;

import static org.assertj.core.api.Assertions.assertThat;

import io.grpc.stub.StreamObserver;
import io.grpc.testing.protobuf.SimpleRequest;
import io.grpc.testing.protobuf.SimpleResponse;
import io.grpc.testing.protobuf.SimpleServiceGrpc;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.context.annotation.Configuration;

/**
 * {@link GrpcRequestContextServerInterceptor} tester.
 */
@SpringBootTest(
        classes = GrpcRequestContextServerInterceptorIT.Cfg.class,
        properties = {"moego.grpc.client.base-packages=io.grpc"})
@ExtendWith(OutputCaptureExtension.class)
class GrpcRequestContextServerInterceptorIT {

    @Autowired
    SimpleServiceGrpc.SimpleServiceBlockingStub stub;

    @Test
    void testGrpcRequestContextServerInterceptor(CapturedOutput output) {
        stub.unaryRpc(SimpleRequest.getDefaultInstance());
        assertThat(output).contains("GrpcRequestContext is not null");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @Slf4j
    static class Cfg extends SimpleServiceGrpc.SimpleServiceImplBase {
        @Override
        public void unaryRpc(SimpleRequest request, StreamObserver<SimpleResponse> responseObserver) {
            GrpcRequestContext ctx = GrpcRequestContext.get();
            if (ctx == null) {
                log.info("GrpcRequestContext is null");
            } else {
                log.info("GrpcRequestContext is not null");
            }
            responseObserver.onNext(SimpleResponse.newBuilder().build());
            responseObserver.onCompleted();
        }
    }
}
