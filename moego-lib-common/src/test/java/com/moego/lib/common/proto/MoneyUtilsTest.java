package com.moego.lib.common.proto;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Money;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
class MoneyUtilsTest {
    @Test
    void fromGoogleMoney_whenValidMoney() {
        Money money = Money.newBuilder().setUnits(10).setNanos(500000000).build();
        BigDecimal result = MoneyUtils.fromGoogleMoney(money);
        assertThat(result).isEqualByComparingTo(new BigDecimal("10.5"));
    }

    @Test
    void fromGoogleMoney_whenZeroMoney() {
        Money money = Money.newBuilder().setUnits(0).setNanos(0).build();
        BigDecimal result = MoneyUtils.fromGoogleMoney(money);
        assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    void toGoogleMoney_whenValidBigDecimal() {
        BigDecimal bigDecimal = new BigDecimal("10.5");
        Money result = MoneyUtils.toGoogleMoney(bigDecimal);
        assertThat(result.getUnits()).isEqualTo(10);
        assertThat(result.getNanos()).isEqualTo(500000000);
    }

    @Test
    void toGoogleMoney_whenZeroBigDecimal() {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        Money result = MoneyUtils.toGoogleMoney(bigDecimal);
        assertThat(result.getUnits()).isZero();
        assertThat(result.getNanos()).isZero();
    }

    @Test
    void isPositive_whenPositiveMoney() {
        Money money = Money.newBuilder().setUnits(1).setNanos(0).build();
        boolean result = MoneyUtils.isPositive(money);
        assertThat(result).isTrue();
    }

    @Test
    void isPositive_whenZeroMoney() {
        Money money = Money.newBuilder().setUnits(0).setNanos(0).build();
        boolean result = MoneyUtils.isPositive(money);
        assertThat(result).isFalse();
    }

    @Test
    void isNegative_whenNegativeMoney() {
        Money money = Money.newBuilder().setUnits(-1).setNanos(0).build();
        boolean result = MoneyUtils.isNegative(money);
        assertThat(result).isTrue();
    }

    @Test
    void isNegative_whenZeroMoney() {
        Money money = Money.newBuilder().setUnits(0).setNanos(0).build();
        boolean result = MoneyUtils.isNegative(money);
        assertThat(result).isFalse();
    }

    @Test
    void isPositive_whenPositiveBigDecimal() {
        BigDecimal bigDecimal = new BigDecimal("1.0");
        boolean result = MoneyUtils.isPositive(bigDecimal);
        assertThat(result).isTrue();
    }

    @Test
    void isPositive_whenZeroBigDecimal() {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        boolean result = MoneyUtils.isPositive(bigDecimal);
        assertThat(result).isFalse();
    }
}
