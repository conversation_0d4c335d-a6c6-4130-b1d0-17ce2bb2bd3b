package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.freemanan.cr.core.anno.Verb;
import com.moego.lib.common.Dependency;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.health.DataSourceHealthDetector;
import com.moego.lib.common.grpc.health.HealthChecker;
import com.moego.lib.common.grpc.health.RedisHealthDetector;
import com.moego.lib.common.http.health.InitController;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Health} tester.
 */
@ClasspathReplacer({
    @Action(
            verb = Verb.ADD,
            value = {Dependency.JDBC_STARTER, Dependency.REDIS_STARTER})
})
public class HealthWebAppTest {

    private final WebApplicationContextRunner runner = new WebApplicationContextRunner()
            .withUserConfiguration(Health.class)
            .withBean(GrpcProperties.class, () -> mock(GrpcProperties.class));

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(InitController.class);
            assertThat(context).hasSingleBean(HealthChecker.class);

            assertThat(context).hasBean("com.moego.lib.common.autoconfigure.feature.Health$Grpc$DataSource");
            assertThat(context).hasSingleBean(DataSourceHealthDetector.class);
            assertThat(context).hasBean("com.moego.lib.common.autoconfigure.feature.Health$Grpc$Redis");
            assertThat(context).hasSingleBean(RedisHealthDetector.class);
        });
    }

    @Test
    public void testDisableDataSourceHealth() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.health.data-source.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(InitController.class);
                    assertThat(context).hasSingleBean(HealthChecker.class);
                    assertThat(context)
                            .doesNotHaveBean("com.moego.lib.common.autoconfigure.feature.Health$Grpc$DataSource");
                    assertThat(context).doesNotHaveBean(DataSourceHealthDetector.class);
                    assertThat(context).hasSingleBean(RedisHealthDetector.class);
                });
    }

    @Test
    public void testDisableRedisHealth() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.health.redis.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(InitController.class);
                    assertThat(context).hasSingleBean(HealthChecker.class);
                    assertThat(context).hasSingleBean(DataSourceHealthDetector.class);
                    assertThat(context).doesNotHaveBean("com.moego.lib.common.autoconfigure.feature.Health$Grpc$Redis");
                    assertThat(context).doesNotHaveBean(RedisHealthDetector.class);
                });
    }
}
