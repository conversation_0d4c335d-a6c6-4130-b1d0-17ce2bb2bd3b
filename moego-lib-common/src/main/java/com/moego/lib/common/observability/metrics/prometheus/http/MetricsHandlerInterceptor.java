package com.moego.lib.common.observability.metrics.prometheus.http;

import com.moego.lib.common.http.util.Const;
import com.moego.lib.common.http.util.HttpUtil;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

public class MetricsHandlerInterceptor implements HandlerInterceptor {

    // 接口时间统计
    private static final Histogram httpLatencyMetrics = Histogram.build()
            .name("moego_http_request_latency")
            .help("MoeGo Server HTTP request latency in milliseconds.")
            .labelNames("server", "path", "method")
            .buckets(50, 100, 200, 300, 400, 500, 750, 1000, 2000, 5000, 10000)
            .register();

    // http 请求数量统计
    private static final Counter requestCounter = Counter.build()
            .name("moego_http_request_total")
            .help("MoeGo HTTP total requests.")
            .labelNames("server", "path", "method", "statusClass")
            .register();

    // 同时正在进行的未处理完的请求统计
    private static final Gauge inprogressRequests = Gauge.build()
            .name("moego_http_inprogress_requests")
            .help("MoeGo HTTP in progress requests.")
            .labelNames("server")
            .register();

    private final String appName;

    public MetricsHandlerInterceptor(String appName) {
        this.appName = appName;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        request.setAttribute(Const.KEY_REQ_START_AT, System.currentTimeMillis());
        inprogressRequests.labels(appName).inc();
        return true;
    }

    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex)
            throws Exception {
        String path = request.getRequestURI();
        String method = request.getMethod();
        String moeStatus = response.getHeader(Const.X_MOE_STATUS);
        int httpStatus = response.getStatus();

        inprogressRequests.labels(appName).dec();

        if (404 != httpStatus && !"404".equals(moeStatus)) {
            if (200 == httpStatus) {
                if (StringUtils.hasText(moeStatus)) {
                    requestCounter
                            .labels(appName, path, method, HttpUtil.categorizeCode(Integer.parseInt(moeStatus)))
                            .inc();
                } else {
                    requestCounter.labels(appName, path, method, "2xx").inc();
                }
            } else {
                requestCounter
                        .labels(appName, path, method, HttpUtil.categorizeCode(httpStatus))
                        .inc();
            }

            Long req_start = (Long) request.getAttribute(Const.KEY_REQ_START_AT);
            if (req_start != null) {
                long spent_time = System.currentTimeMillis() - req_start;
                request.setAttribute(Const.KEY_REQ_SPENT_TIME, spent_time);
                httpLatencyMetrics.labels(appName, path, method).observe(spent_time);
            }
        }
    }
}
