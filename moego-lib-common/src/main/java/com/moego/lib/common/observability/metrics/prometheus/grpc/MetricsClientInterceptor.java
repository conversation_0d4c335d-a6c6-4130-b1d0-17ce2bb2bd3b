package com.moego.lib.common.observability.metrics.prometheus.grpc;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.Status;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;

/**
 * Client interceptor for Prometheus metrics.
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
public class MetricsClientInterceptor implements ClientInterceptor {

    // Interface time statistics.
    private static final Histogram clientHandlingSeconds = Histogram.build()
            .name("grpc_client_handling_seconds")
            .help("Histogram of response latency (seconds) of the gRPC until it is finished by the application.")
            .labelNames("grpc_service", "grpc_method", "grpc_type")
            .unit("second") // use the same unit as the prometheus library in Go and micrometer.
            .buckets(0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10)
            .register();

    // gRPC request counter.
    private static final Counter clientHandledTotal = Counter.build()
            .name("grpc_client_handled_total")
            .help("Total number of RPCs completed by the client, regardless of success or failure.")
            .labelNames("grpc_service", "grpc_method", "grpc_type", "grpc_code")
            .register();

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
            MethodDescriptor<ReqT, RespT> methodDescriptor, CallOptions callOptions, Channel channel) {

        String gRPCMethod = methodDescriptor.getBareMethodName();
        String gRPCService = methodDescriptor.getServiceName();
        String gRPCType = methodDescriptor.getType().name();
        long start = System.currentTimeMillis();

        return new ForwardingClientCall.SimpleForwardingClientCall<>(channel.newCall(methodDescriptor, callOptions)) {
            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                var lisn = new ForwardingClientCallListener.SimpleForwardingClientCallListener<>(responseListener) {
                    @Override
                    public void onClose(Status status, Metadata trailers) {
                        double latencySecond = (System.currentTimeMillis() - start) / 1000.0;

                        String code = status.getCode().toString();
                        clientHandlingSeconds
                                .labels(gRPCService, gRPCMethod, gRPCType)
                                .observe(latencySecond);

                        clientHandledTotal
                                .labels(gRPCService, gRPCMethod, gRPCType, code)
                                .inc();

                        super.onClose(status, trailers);
                    }
                };
                super.start(lisn, headers);
            }
        };
    }
}
