package com.moego.lib.common.observability.tracing;

import java.util.HashSet;
import java.util.Set;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(RpcProperties.PREFIX)
public class RpcProperties {

    public static final String PREFIX = "moego.rpc";

    private boolean enabled = true;

    /**
     * Headers that need to be transferred to upstream.
     *
     * <p> Support Ant-style path patterns, such as:
     * <ul>
     *     <li>{@code gv-*}</li>
     *     <li>{@code MG_*}</li>
     * </ul>
     *
     * @see org.springframework.util.AntPathMatcher
     */
    private Set<String> transferHeaders = new HashSet<>();
}
