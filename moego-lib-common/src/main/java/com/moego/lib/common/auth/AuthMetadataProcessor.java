/*
 * @since 2022-06-25 10:25:55
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.auth;

import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.RequestHolder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.util.StringUtils;

public class AuthMetadataProcessor implements MetadataProcessor<AuthContext> {

    @Override
    public String[] keys(RequestHolder holder) {
        return new String[] {
            AuthContext.HK_TODO_USER_ID,
            AuthContext.HK_ACCOUNT_ID,
            AuthContext.HK_ENTERPRISE_ID,
            AuthContext.HK_COMPANY_ID,
            AuthContext.HK_BUSINESS_ID,
            AuthContext.HK_STAFF_ID,
            AuthContext.HK_SESSION_ID,
            AuthContext.HK_SUB_SESSION_ID,
            AuthContext.HK_CUSTOMER_ID,
            AuthContext.HK_SESSION_DATA,
            AuthContext.HK_SUB_SESSION_DATA,
            AuthContext.HK_OB_NAME,
            AuthContext.HK_IMPERSONATOR,
            AuthContext.HK_BRANDED_APP_ID
        };
    }

    @Override
    public AuthContext build(Map<String, String> entries) {
        LinkedCaseInsensitiveMap<String> map = new LinkedCaseInsensitiveMap<>();
        map.putAll(entries);
        // authz 带过来的 sessionData 是经过 base64 编码的，需要解码，解码后得到的是一个 json 字符串
        var sessionData = map.get(AuthContext.HK_SESSION_DATA);
        if (StringUtils.hasText(sessionData)) {
            sessionData = new String(Base64.getUrlDecoder().decode(sessionData), StandardCharsets.UTF_8);
        }
        var subSessionData = map.get(AuthContext.HK_SUB_SESSION_DATA);
        if (StringUtils.hasText(subSessionData)) {
            subSessionData = new String(Base64.getUrlDecoder().decode(subSessionData), StandardCharsets.UTF_8);
        }
        return new AuthContext(
                longValue(map.get(AuthContext.HK_TODO_USER_ID)),
                longValue(map.get(AuthContext.HK_ACCOUNT_ID)),
                longValue(map.get(AuthContext.HK_ENTERPRISE_ID)),
                longValue(map.get(AuthContext.HK_COMPANY_ID)),
                longValue(map.get(AuthContext.HK_BUSINESS_ID)),
                longValue(map.get(AuthContext.HK_STAFF_ID)),
                longValue(map.get(AuthContext.HK_SESSION_ID)),
                // TODO: 先兼容子会话不存在的场景，把主会话填入 subSessionId。等到 ob anti spam 第二次发版后改为 HK_SUB_SESSION_ID
                longValue(map.get(AuthContext.HK_SESSION_ID)),
                longValue(map.get(AuthContext.HK_CUSTOMER_ID)),
                sessionData,
                subSessionData,
                map.get(AuthContext.HK_OB_NAME),
                map.get(AuthContext.HK_IMPERSONATOR),
                map.get(AuthContext.HK_BRANDED_APP_ID));
    }

    private static Long longValue(String str) {
        if (!StringUtils.hasText(str)) {
            return null;
        }
        return Long.valueOf(str);
    }
}
