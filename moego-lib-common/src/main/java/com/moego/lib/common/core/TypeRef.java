package com.moego.lib.common.core;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import lombok.Getter;
import org.springframework.util.Assert;

/**
 * Copy form {@link org.springframework.core.ParameterizedTypeReference}.
 *
 * @param <T> the referenced type
 * <AUTHOR>
 * @see <a href="https://gafter.blogspot.nl/2006/12/super-type-tokens.html"><PERSON> on Super Type Tokens</a>
 */
@Getter
public abstract class TypeRef<T> {

    private final Type type;

    protected TypeRef() {
        Class<?> typeRefSubclass = findTypeRefSubclass(getClass());
        Type t = typeRefSubclass.getGenericSuperclass();
        Assert.isInstanceOf(ParameterizedType.class, t, "Type must be a parameterized type");
        ParameterizedType parameterizedType = (ParameterizedType) t;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        Assert.isTrue(actualTypeArguments.length == 1, "Number of type arguments must be 1");
        this.type = actualTypeArguments[0];
    }

    private TypeRef(Type type) {
        this.type = type;
    }

    /**
     * Build a {@code TypeRef} wrapping the given type.
     *
     * @param type a generic type (possibly obtained via reflection,
     *             e.g., from {@link java.lang.reflect.Method#getGenericReturnType()})
     * @return a corresponding reference which may be passed into
     * {@code TypeRef}-accepting methods
     */
    public static <T> TypeRef<T> forType(Type type) {
        return new TypeRef<>(type) {};
    }

    private static Class<?> findTypeRefSubclass(Class<?> child) {
        Class<?> parent = child.getSuperclass();
        if (Object.class == parent) {
            throw new IllegalStateException("Expected TypeRef superclass");
        } else if (TypeRef.class == parent) {
            return child;
        } else {
            return findTypeRefSubclass(parent);
        }
    }
}
