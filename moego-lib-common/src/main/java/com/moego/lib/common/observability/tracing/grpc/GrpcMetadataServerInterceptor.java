/*
 * @since 2022-06-25 11:53:32
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.observability.tracing.grpc;

import com.moego.lib.common.grpc.server.GrpcResponseModifier;
import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.thread.MetadataContext;
import com.moego.lib.common.thread.ThreadContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import io.grpc.ForwardingServerCall.SimpleForwardingServerCall;
import io.grpc.ForwardingServerCallListener;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.Status;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class GrpcMetadataServerInterceptor implements ServerInterceptor {

    private final List<MetadataProcessor<?>> processors;

    public GrpcMetadataServerInterceptor(List<MetadataProcessor<?>> processors) {
        this.processors = processors;
    }

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        MetadataContext metadataContext = new MetadataContext();
        GrpcRequestHolder headerHolder = new GrpcRequestHolder(headers, call);
        Headers transferHeaders = new Headers();

        for (MetadataProcessor<?> processor : processors) {
            Map<String, String> mapping = new HashMap<>();
            for (String k : processor.keys(headerHolder)) {
                String v = headers.get(Metadata.Key.of(k, Metadata.ASCII_STRING_MARSHALLER));
                if (StringUtils.hasText(v)) {
                    transferHeaders.put(k, v);
                }
                mapping.put(k, v);
            }
            Object ctx = processor.build(mapping);
            if (ctx != null) {
                metadataContext.put(ctx.getClass(), ctx);
            }
        }

        // headers need to be transferred to upstream
        metadataContext.put(Headers.class, transferHeaders);
        // gRPC header holder
        metadataContext.put(RequestHolder.class, headerHolder);
        // gRPC response modifier
        metadataContext.put(GrpcResponseModifier.class, new GrpcResponseModifier());

        ThreadContext threadContext = new ThreadContext(metadataContext);
        ThreadContextHolder.set(threadContext);
        try {
            ServerCall<ReqT, RespT> modifyMetadataServerCall = new ModifyMetadataServerCall<>(call);
            return new MetadataServerCallListener<>(next.startCall(modifyMetadataServerCall, headers), threadContext);
        } finally {
            ThreadContextHolder.remove();
        }
    }

    static class ModifyMetadataServerCall<Req, Resp> extends SimpleForwardingServerCall<Req, Resp> {

        protected ModifyMetadataServerCall(ServerCall<Req, Resp> delegate) {
            super(delegate);
        }

        @Override
        public void sendHeaders(Metadata headers) {
            setResponseHeaders(headers);
            super.sendHeaders(headers);
        }

        @Override
        public void close(Status status, Metadata trailers) {
            setResponseHeaders(trailers);
            super.close(status, trailers);
        }

        private static void setResponseHeaders(Metadata headers) {
            // add custom headers
            GrpcResponseModifier modifier = ThreadContextHolder.getContext(GrpcResponseModifier.class);
            if (modifier != null) {
                modifier.getMetadataActions().forEach(action -> action.accept(headers));
            }
        }
    }

    static class MetadataServerCallListener<Req>
            extends ForwardingServerCallListener.SimpleForwardingServerCallListener<Req> {

        private final ThreadContext threadContext;

        public MetadataServerCallListener(ServerCall.Listener<Req> delegate, ThreadContext threadContext) {
            super(delegate);
            this.threadContext = threadContext;
        }

        @Override
        public void onReady() {
            doInContext(super::onReady);
        }

        @Override
        public void onMessage(Req message) {
            doInContext(() -> super.onMessage(message));
        }

        @Override
        public void onHalfClose() {
            doInContext(super::onHalfClose);
        }

        @Override
        public void onCancel() {
            doInContext(super::onCancel);
        }

        @Override
        public void onComplete() {
            doInContext(super::onComplete);
        }

        private void doInContext(Runnable runnable) {
            ThreadContextHolder.set(threadContext);
            try {
                runnable.run();
            } finally {
                ThreadContextHolder.remove();
            }
        }
    }
}
