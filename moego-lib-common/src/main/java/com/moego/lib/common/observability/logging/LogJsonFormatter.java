package com.moego.lib.common.observability.logging;

import ch.qos.logback.contrib.json.JsonFormatter;
import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.util.JsonUtil;
import java.util.Map;
import java.util.regex.Pattern;

public class LogJsonFormatter implements Json<PERSON>ormatter {

    private static final String LOG_PATTER_KEY = "message";
    private static final String PASSWORD_PATTER = "\\\"\\w+ssword\\\"\\s*:\\s*\\\"(.*?)\\\"";
    private static final String PASSWORD_REPLACE_RESULT = "password:******";
    private static final String EMAIL_PATTERN = "(\\w)[\\w.-]*?(\\w)@([\\w.-]+(?:\\.\\w+)+)";
    private static final String EMAIL_REPLACE_SIMPLE = "$1****$2@$3";

    private static final Pattern PASSWORD_PATTERN = Pattern.compile(PASSWORD_PATTER);
    private static final Pattern EMAIL_PATTERN_COMPILED = Pattern.compile(EMAIL_PATTERN);

    @SuppressWarnings("unchecked")
    @Override
    public String toJsonString(Map m) {
        String message = String.valueOf(m.get(LOG_PATTER_KEY));
        if (message != null && !message.isEmpty()) {
            message = PASSWORD_PATTERN.matcher(message).replaceAll(PASSWORD_REPLACE_RESULT);

            message = EMAIL_PATTERN_COMPILED.matcher(message).replaceAll(EMAIL_REPLACE_SIMPLE);

            m.put(LOG_PATTER_KEY, message);
        }

        TracingContext tracingContext = TracingContext.get();
        if (tracingContext != null) {
            m.put(TracingContext.LK_REQUEST_ID, tracingContext.getRequestId());
        }

        return JsonUtil.toJson(m);
    }
}
