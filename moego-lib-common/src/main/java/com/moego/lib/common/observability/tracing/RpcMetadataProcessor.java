package com.moego.lib.common.observability.tracing;

import java.util.List;
import java.util.Map;
import org.springframework.util.AntPathMatcher;

/**
 * <AUTHOR>
 */
public class RpcMetadataProcessor implements MetadataProcessor<RpcContext> {

    private static final AntPathMatcher matcher = new AntPathMatcher();

    private final RpcProperties rpcProperties;

    public RpcMetadataProcessor(RpcProperties rpcProperties) {
        this.rpcProperties = rpcProperties;
    }

    @Override
    public String[] keys(RequestHolder holder) {
        List<String> headers = holder.headers();
        return rpcProperties.getTransferHeaders().stream()
                .flatMap(pattern -> headers.stream().filter(k -> matcher.match(pattern, k)))
                .distinct()
                .toArray(String[]::new);
    }

    @Override
    public RpcContext build(Map<String, String> entries) {
        RpcContext ctx = new RpcContext();
        entries.forEach(ctx::put);
        return ctx;
    }
}
