package com.moego.lib.common.grpc.server;

import com.moego.lib.common.thread.ThreadContextHolder;
import io.grpc.Metadata;

/**
 * Utility class to manipulate the gRPC response.
 *
 * <p> Examples:
 *
 * <p> Put key-value pair into response:
 * <pre>{@code
 * @GrpcService
 * public class MyGrpcService extends MyGrpcServiceGrpc.MyGrpcServiceImplBase {
 *    @Override
 *    public void myMethod(MyRequest request, StreamObserver<MyResponse> observer) {
 *        // do your logic ...
 *
 *        // put metadata
 *        GrpcResponseUtil.putMetadata("key", "value");
 *
 *        observer.onNext(MyResponse.newBuilder().build());
 *        observer.onCompleted();
 *    }
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since 2023/4/7
 */
public class GrpcResponseUtil {

    /**
     * Put a key-value pair into the {@link Metadata} of the current thread.
     *
     * <p> If key is already present, value will be added to the end.
     *
     * @param key    key
     * @param values values
     */
    public static void putMetadata(String key, String... values) {
        GrpcResponseModifier modifier = ThreadContextHolder.getContext(GrpcResponseModifier.class);
        if (modifier == null || values.length == 0) {
            return;
        }
        modifier.addMetadataAction(metadata -> {
            Metadata.Key<String> mk = Metadata.Key.of(key, Metadata.ASCII_STRING_MARSHALLER);
            for (String v : values) {
                metadata.put(mk, v);
            }
        });
    }

    /**
     * Remove specified keys.
     *
     * @param keys keys
     */
    public static void removeMetadata(String... keys) {
        GrpcResponseModifier modifier = ThreadContextHolder.getContext(GrpcResponseModifier.class);
        if (modifier == null || keys.length == 0) {
            return;
        }
        modifier.addMetadataAction(metadata -> {
            for (String k : keys) {
                Metadata.Key<String> mk = Metadata.Key.of(k, Metadata.ASCII_STRING_MARSHALLER);
                metadata.removeAll(mk);
            }
        });
    }
}
