package com.moego.lib.common.grpc.client;

import static io.grpc.internal.GrpcUtil.DEFAULT_MAX_HEADER_LIST_SIZE;
import static io.grpc.internal.GrpcUtil.DEFAULT_MAX_MESSAGE_SIZE;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.GrpcUtil;
import io.grpc.Channel;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.stub.AbstractStub;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.Ordered;
import org.springframework.core.PriorityOrdered;
import org.springframework.core.env.Environment;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;

/**
 * <AUTHOR>
 * @since 2022/10/11
 * @deprecated since 2023/4/17 by Freeman, prefer to use {@link GrpcStubsBeanDefinitionRegistry}.
 */
@Deprecated
public class GrpcClientBeanPostProcessor
        implements BeanPostProcessor,
                EnvironmentAware,
                BeanFactoryAware,
                BeanDefinitionRegistryPostProcessor,
                PriorityOrdered {

    private BeanFactory beanFactory;
    private final Map<String, String> channels = new HashMap<>();
    private DataSize grpcClientMaxInboundMessageSize;
    private DataSize grpcClientMaxInboundMetadataSize;

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        ReflectionUtils.doWithFields(bean.getClass(), field -> {
            GrpcClient anno = field.getAnnotation(GrpcClient.class);
            if (anno == null) {
                return;
            }
            if (!AbstractStub.class.isAssignableFrom(field.getType())) {
                throw new BeanInitializationException("GrpcClient field must be a subclass of AbstractStub!");
            }
            Class<? extends AbstractStub> clz = (Class<? extends AbstractStub>) field.getType();
            String name = anno.value();
            AbstractStub<?> stub = GrpcClientBeanHolder.get(name, clz);
            if (stub != null) {
                field.setAccessible(true);
                ReflectionUtils.setField(field, bean, stub);
                return;
            }
            createStubAndPut2BeanFactory(name, clz, field, bean);
        });
        return bean;
    }

    private void createStubAndPut2BeanFactory(
            String clientName, Class<? extends AbstractStub> clz, Field field, Object bean) {
        if (!channels.containsKey(clientName)) {
            throw new BeanInitializationException("No channel found for GrpcClient: " + clientName);
        }

        ManagedChannel channel = ManagedChannelBuilder.forTarget(channels.get(clientName))
                .usePlaintext()
                .intercept(beanFactory.getBean(CompositeClientInterceptor.class))
                .maxInboundMessageSize((int) grpcClientMaxInboundMessageSize.toBytes())
                .maxInboundMetadataSize((int) grpcClientMaxInboundMetadataSize.toBytes())
                .build();

        Method newStubMethod =
                ReflectionUtils.findMethod(clz.getEnclosingClass(), GrpcUtil.getNewStubMethodName(clz), Channel.class);
        if (newStubMethod == null) {
            throw new BeanInitializationException("No new stub method found for GrpcClient: " + clientName);
        }
        AbstractStub<?> grpcStub = (AbstractStub<?>) ReflectionUtils.invokeMethod(newStubMethod, null, channel);
        if (grpcStub == null) {
            throw new BeanInitializationException("Failed to create stub for GrpcClient: " + clientName);
        }
        field.setAccessible(true);
        ReflectionUtils.setField(field, bean, grpcStub);
        GrpcClientBeanHolder.put(clientName, grpcStub);
        ((ConfigurableBeanFactory) beanFactory)
                .registerSingleton(GrpcUtil.determineRealClientName(clientName, grpcStub.getClass()), grpcStub);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void setEnvironment(Environment environment) {
        Boolean grpcClientEnabled =
                environment.getProperty(GrpcProperties.PREFIX + ".client.enabled", Boolean.class, true);
        if (!grpcClientEnabled) {
            return;
        }
        Binder binder = Binder.get(environment);
        Map<String, String> properties =
                binder.bindOrCreate(GrpcProperties.PREFIX + ".client.channels", LinkedHashMap.class);
        properties.forEach((clientName, address) -> {
            if (!StringUtils.hasText(address)) {
                throw new IllegalStateException("GrpcClient address must not be empty!");
            }
            channels.put(clientName, address);
        });
        grpcClientMaxInboundMessageSize = binder.bind(
                        GrpcProperties.PREFIX + ".client.max-inbound-message-size", DataSize.class)
                .orElse(DataSize.ofBytes(DEFAULT_MAX_MESSAGE_SIZE));
        grpcClientMaxInboundMetadataSize = binder.bind(
                        GrpcProperties.PREFIX + ".client.max-inbound-metadata-size", DataSize.class)
                .orElse(DataSize.ofBytes(DEFAULT_MAX_HEADER_LIST_SIZE));
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {}

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(
                CompositeClientInterceptor.class, CompositeClientInterceptor::new);
        registry.registerBeanDefinition(
                StringUtils.uncapitalize(CompositeClientInterceptor.class.getSimpleName()),
                builder.getBeanDefinition());
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
