package com.moego.lib.common.grpc.server;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.springframework.stereotype.Component;

/**
 * Annotation for a gRPC service bean.
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
@Target({TYPE})
@Retention(RUNTIME)
@Component
public @interface GrpcService {}
