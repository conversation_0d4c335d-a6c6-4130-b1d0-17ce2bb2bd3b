package com.moego.lib.common.observability.tracing.http.mvc;

import com.moego.lib.common.observability.tracing.RequestHolder;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class MvcRequestHolder implements RequestHolder {

    private final HttpServletRequest request;

    public MvcRequestHolder(HttpServletRequest request) {
        this.request = request;
    }

    @Override
    public List<String> headers() {
        Iterator<String> names = request.getHeaderNames().asIterator();
        List<String> result = new ArrayList<>();
        while (names.hasNext()) {
            result.add(names.next());
        }
        return result;
    }

    @Override
    public String getHeader(String key) {
        return request.getHeader(key);
    }

    @Override
    public String remoteAddr() {
        return request.getRemoteAddr();
    }

    @Override
    public String host() {
        var host = getHeader("host");
        if (StringUtils.hasText(host)) {
            return host;
        }
        return getHeader("authority");
    }
}
