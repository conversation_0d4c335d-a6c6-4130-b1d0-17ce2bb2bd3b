package com.moego.lib.common.thread;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Adapt JDK thread pool {@link ThreadPoolExecutor}.
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
public class DefaultObservableThreadPool implements ObservableThreadPool<ThreadPoolExecutor> {

    private final ThreadPoolExecutor pool;

    public DefaultObservableThreadPool(ThreadPoolExecutor pool) {
        this.pool = pool;
    }

    @Override
    public int getCoreSize() {
        return pool.getCorePoolSize();
    }

    @Override
    public int getMaxSize() {
        return pool.getMaximumPoolSize();
    }

    @Override
    public int getCurrentPoolSize() {
        return pool.getPoolSize();
    }

    @Override
    public int getActiveCount() {
        return pool.getActiveCount();
    }

    @Override
    public int getQueueSize() {
        return pool.getQueue().size();
    }

    @Override
    public ThreadPoolExecutor getThreadPool() {
        return pool;
    }
}
