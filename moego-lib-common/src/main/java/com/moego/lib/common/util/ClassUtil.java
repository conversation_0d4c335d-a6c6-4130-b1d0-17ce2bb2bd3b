package com.moego.lib.common.util;

import java.io.File;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ClassUtil {

    /**
     * Get all classes in the base packages with specified predications.
     *
     * @param basePackages       base packages
     * @param classNamePredicate class name predicate
     * @param classPredicate     class predicate
     * @return all matched classes
     */
    @SneakyThrows
    public static List<Class<?>> getClasses(
            List<String> basePackages, Predicate<String> classNamePredicate, Predicate<Class<?>> classPredicate) {
        List<String> packagesToUse = getRootPackages(basePackages);
        List<String> classNames = new ArrayList<>();
        for (String basePackage : packagesToUse) {
            classNames.addAll(findClassesInPackage(basePackage));
        }

        Set<Class<?>> result = new HashSet<>();
        for (String className : classNames) {
            if (classNamePredicate.test(className)) {
                Class<?> clazz = Class.forName(className);
                if (classPredicate.test(clazz)) {
                    result.add(clazz);
                }
            }
        }
        return List.copyOf(result);
    }

    /**
     * Get root packages from base packages.
     *
     * @param basePackages base packages
     * @return root packages
     */
    static List<String> getRootPackages(List<String> basePackages) {
        return new HashSet<>(basePackages)
                .stream()
                        .filter(pkg -> basePackages.stream().noneMatch(p -> pkg.startsWith(p + ".")))
                        .toList();
    }

    /**
     * Find all classes in the package.
     *
     * @param packageName package name
     * @return all classes in the package
     */
    @SneakyThrows
    public static List<String> findClassesInPackage(String packageName) {
        List<String> classes = new ArrayList<>();

        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String path = packageName.replace('.', '/');
        Enumeration<URL> resources = classLoader.getResources(path);

        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            if ("file".equals(resource.getProtocol())) {
                classes.addAll(findClassesInFileSystemPackage(packageName, resource.getPath()));
            } else if ("jar".equals(resource.getProtocol())) {
                classes.addAll(findClassesInJarPackage(packageName, resource));
            }
        }

        return classes;
    }

    private static List<String> findClassesInFileSystemPackage(String packageName, String packagePath) {
        List<String> classes = new ArrayList<>();

        File directory = new File(packagePath);
        if (!directory.exists()) {
            return classes;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return classes;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                if (!file.getName().contains(".")) {
                    classes.addAll(
                            findClassesInFileSystemPackage(packageName + "." + file.getName(), file.getAbsolutePath()));
                }
            } else if (file.getName().endsWith(".class")) {
                classes.add(packageName
                        + '.'
                        + file.getName().substring(0, file.getName().length() - ".class".length()));
            }
        }

        return classes;
    }

    private static List<String> findClassesInJarPackage(String packageName, URL resource) throws IOException {
        List<String> classes = new ArrayList<>();

        JarFile jar = ((JarURLConnection) resource.openConnection()).getJarFile();
        Enumeration<JarEntry> entries = jar.entries();
        while (entries.hasMoreElements()) {
            JarEntry entry = entries.nextElement();
            String entryName = entry.getName();
            if (entryName.startsWith(packageName.replace('.', '/')) && entryName.endsWith(".class")) {
                classes.add(entryName.replace('/', '.').substring(0, entryName.length() - ".class".length()));
            }
        }

        jar.close();

        return classes;
    }
}
