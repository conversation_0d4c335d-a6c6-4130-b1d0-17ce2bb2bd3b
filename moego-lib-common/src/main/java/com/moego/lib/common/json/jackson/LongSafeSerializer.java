package com.moego.lib.common.json.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;

/**
 * When a Long exceeds 2^53-1, it will lose precision, so we need to serialize it as a string.
 *
 * <AUTHOR>
 * @since 2023/12/13
 */
public final class LongSafeSerializer extends StdSerializer<Long> {

    /**
     * @see <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MIN_SAFE_INTEGER">MIN_SAFE_INTEGER</a>
     */
    private static final long MIN_SAFE_INTEGER = -((1L << 53) - 1);
    /**
     * @see <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER">MAX_SAFE_INTEGER</a>
     */
    private static final long MAX_SAFE_INTEGER = (1L << 53) - 1;

    public LongSafeSerializer() {
        super(Long.class);
    }

    @Override
    public void serialize(Long aLong, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        if (MIN_SAFE_INTEGER <= aLong && aLong <= MAX_SAFE_INTEGER) {
            jsonGenerator.writeNumber(aLong);
        } else {
            jsonGenerator.writeString(aLong.toString());
        }
    }
}
