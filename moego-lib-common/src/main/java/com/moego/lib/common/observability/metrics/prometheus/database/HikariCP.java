package com.moego.lib.common.observability.metrics.prometheus.database;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.metrics.prometheus.PrometheusMetricsTrackerFactory;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/10/11
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(HikariDataSource.class)
public class HikariCP implements SmartInitializingSingleton {

    private final ApplicationContext ctx;

    public HikariCP(ApplicationContext ctx) {
        this.ctx = ctx;
    }

    @Override
    public void afterSingletonsInstantiated() {
        ctx.getBeanProvider(HikariDataSource.class).forEach(ds -> {
            ds.setMetricsTrackerFactory(new PrometheusMetricsTrackerFactory());
        });
    }
}
