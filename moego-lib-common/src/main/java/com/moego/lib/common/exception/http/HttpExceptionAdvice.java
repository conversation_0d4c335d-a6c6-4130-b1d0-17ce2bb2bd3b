package com.moego.lib.common.exception.http;

import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.grpc.GrpcUtil;
import com.moego.lib.common.http.util.Const;
import io.grpc.StatusRuntimeException;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Basic Http exception advice.
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
@Order(0) // before old ExceptionHandlerAdvice
@RestControllerAdvice
public class HttpExceptionAdvice {

    private static final Logger log = LoggerFactory.getLogger(HttpExceptionAdvice.class);

    @ExceptionHandler(BizException.class)
    public CommonResponse handleBizException(BizException bizException, HttpServletResponse resp) {
        log.error("Caught BizException:", bizException);
        resp.setHeader(Const.X_MOE_STATUS, bizException.getCode().toString());
        return HttpExceptionUtil.toCommonResponse(bizException);
    }

    /**
     * Handle gRPC exception in HTTP threads, convert to HTTP response.
     *
     * @see io.envoyproxy.pgv.grpc.ValidatingClientInterceptor
     */
    @ExceptionHandler(StatusRuntimeException.class)
    public CommonResponse handleStatusRuntimeException(
            StatusRuntimeException statusRuntimeException, HttpServletResponse resp) {
        log.error("Caught StatusRuntimeException:", statusRuntimeException);

        int httpCode = GrpcUtil.grpc2HttpCode(statusRuntimeException.getStatus().getCode());

        CommonResponse res = new CommonResponse();
        res.setCode(httpCode);
        res.setMessage(statusRuntimeException.getMessage());
        res.setSuccess(false);
        // FIXME(Freeman): front end uses this field to show error message :-)
        res.setData(statusRuntimeException.getMessage());

        resp.setHeader(Const.X_MOE_STATUS, String.valueOf(httpCode));
        // TODO: set http status code
        // resp.setStatus(httpCode);
        return res;
    }
}
