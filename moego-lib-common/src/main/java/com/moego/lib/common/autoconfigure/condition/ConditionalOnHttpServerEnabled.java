package com.moego.lib.common.autoconfigure.condition;

import com.moego.lib.common.autoconfigure.http.HttpProperties;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ConditionalOnHttpEnabled
@ConditionalOnProperty(prefix = HttpProperties.PREFIX + ".server", name = "enabled", matchIfMissing = true)
public @interface ConditionalOnHttpServerEnabled {}
