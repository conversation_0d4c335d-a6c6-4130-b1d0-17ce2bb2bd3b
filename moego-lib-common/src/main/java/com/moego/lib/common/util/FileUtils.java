package com.moego.lib.common.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import lombok.experimental.UtilityClass;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2023/12/15
 */
@UtilityClass
public class FileUtils {
    public static File convertMultiPartToFile(MultipartFile file) throws IOException {
        File convertFile = new File(file.getName());
        try (FileOutputStream fos = new FileOutputStream(convertFile)) {
            fos.write(file.getBytes());
        }
        return convertFile;
    }
}
