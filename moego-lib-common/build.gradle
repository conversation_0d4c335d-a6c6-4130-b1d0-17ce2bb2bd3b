plugins {
    id "com.google.protobuf"
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
    api 'org.springframework.boot:spring-boot-starter'
    api 'org.springframework.boot:spring-boot-starter-json'
    api 'org.springframework.boot:spring-boot-starter-aop'
    api("ch.qos.logback.contrib:logback-json-classic:0.1.5") {
        // use version provided by Spring Boot
        exclude(group: "ch.qos.logback", module: "logback-core")
        exclude(group: "ch.qos.logback", module: "logback-classic")
    }
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    compileOnly "io.grpc:grpc-services"
    compileOnly "io.grpc:grpc-inprocess"

    // prometheus dependencies
    api 'io.prometheus:simpleclient_servlet_jakarta'
    api "io.prometheus:simpleclient_hotspot"
    api "io.prometheus:simpleclient_httpserver"

    // for health check
    compileOnly 'org.springframework.boot:spring-boot-starter-jdbc'
    compileOnly 'org.springframework.boot:spring-boot-starter-data-redis'

    // dynamic DataSource
    compileOnly "org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}"
    compileOnly "com.github.pagehelper:pagehelper-spring-boot-starter:${pagehelperSpringBootStarterVersion}"

    // mybatis TypeHandler
    compileOnly "com.mysql:mysql-connector-j"
    compileOnly "org.postgresql:postgresql"

    // support http thread invoke grpc client and grpc thread invoke http client
    compileOnly 'org.springframework.boot:spring-boot-starter-web'
    compileOnly 'org.springframework.boot:spring-boot-starter-validation'
    compileOnly 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // test support
    compileOnly 'org.springframework.boot:spring-boot-starter-test'

    // cache
    api 'com.github.ben-manes.caffeine:caffeine'
    // okhttp
    api 'io.github.openfeign:feign-okhttp'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // test support, https://github.com/DanielLiu1123/classpath-replacer
    testImplementation("com.freemanan:classpath-replacer-junit5:${classpathReplacerVersion}")

    // integration tests
    testImplementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation("io.grpc:grpc-services")
    testImplementation("io.grpc:grpc-testing-proto")
    testImplementation("org.mybatis:mybatis:${mybatisVersion}")
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
        javapgv {
            artifact = "build.buf.protoc-gen-validate:protoc-gen-validate:${pgvVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
            javapgv { option "lang=java" }
        }
    }
}
