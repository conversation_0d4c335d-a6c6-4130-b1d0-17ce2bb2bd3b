package example.mapper.mysql;

import static example.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import example.entity.mysql.MoeGroomingAppointment;
import example.mapper.typehandler.AppointmentStatusTypeHandler;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MoeGroomingAppointmentMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MoeGroomingAppointmentMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    BasicColumn[] selectList = BasicColumn.columnList(id, orderId, businessId, customerId, appointmentDate, appointmentStartTime, appointmentEndTime, isWaitingList, moveWaitingBy, confirmedTime, checkInTime, checkOutTime, canceledTime, status, isBlock, bookOnlineStatus, customerAddressId, repeatId, isPaid, colorCode, noShow, noShowFee, isPustNotification, cancelByType, cancelBy, confirmByType, confirmBy, createdById, outOfArea, isDeprecate, createTime, updateTime, source, oldAppointmentDate, oldAppointmentStartTime, oldAppointmentEndTime, oldApptId, scheduleType, sourcePlatform, readyTime, pickupNotificationSendStatus, pickupNotificationFailedReason, statusBeforeCheckin, statusBeforeReady, statusBeforeFinish, noStartTime, updatedById, companyId, isAutoAccept, waitListStatus, appointmentEndDate, serviceTypeInclude);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Integer.class)
    int insert(InsertStatementProvider<MoeGroomingAppointment> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MoeGroomingAppointmentResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="order_id", property="orderId", jdbcType=JdbcType.CHAR),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.INTEGER),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.INTEGER),
        @Result(column="appointment_date", property="appointmentDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="appointment_start_time", property="appointmentStartTime", jdbcType=JdbcType.INTEGER),
        @Result(column="appointment_end_time", property="appointmentEndTime", jdbcType=JdbcType.INTEGER),
        @Result(column="is_waiting_list", property="isWaitingList", jdbcType=JdbcType.TINYINT),
        @Result(column="move_waiting_by", property="moveWaitingBy", jdbcType=JdbcType.INTEGER),
        @Result(column="confirmed_time", property="confirmedTime", jdbcType=JdbcType.BIGINT),
        @Result(column="check_in_time", property="checkInTime", jdbcType=JdbcType.BIGINT),
        @Result(column="check_out_time", property="checkOutTime", jdbcType=JdbcType.BIGINT),
        @Result(column="canceled_time", property="canceledTime", jdbcType=JdbcType.BIGINT),
        @Result(column="status", property="status", typeHandler=AppointmentStatusTypeHandler.class, jdbcType=JdbcType.TINYINT),
        @Result(column="is_block", property="isBlock", jdbcType=JdbcType.BIT),
        @Result(column="book_online_status", property="bookOnlineStatus", jdbcType=JdbcType.TINYINT),
        @Result(column="customer_address_id", property="customerAddressId", jdbcType=JdbcType.INTEGER),
        @Result(column="repeat_id", property="repeatId", jdbcType=JdbcType.INTEGER),
        @Result(column="is_paid", property="isPaid", jdbcType=JdbcType.TINYINT),
        @Result(column="color_code", property="colorCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="no_show", property="noShow", jdbcType=JdbcType.TINYINT),
        @Result(column="no_show_fee", property="noShowFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_pust_notification", property="isPustNotification", jdbcType=JdbcType.TINYINT),
        @Result(column="cancel_by_type", property="cancelByType", jdbcType=JdbcType.TINYINT),
        @Result(column="cancel_by", property="cancelBy", jdbcType=JdbcType.INTEGER),
        @Result(column="confirm_by_type", property="confirmByType", jdbcType=JdbcType.TINYINT),
        @Result(column="confirm_by", property="confirmBy", jdbcType=JdbcType.INTEGER),
        @Result(column="created_by_id", property="createdById", jdbcType=JdbcType.INTEGER),
        @Result(column="out_of_area", property="outOfArea", jdbcType=JdbcType.TINYINT),
        @Result(column="is_deprecate", property="isDeprecate", jdbcType=JdbcType.TINYINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.BIGINT),
        @Result(column="source", property="source", jdbcType=JdbcType.INTEGER),
        @Result(column="old_appointment_date", property="oldAppointmentDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="old_appointment_start_time", property="oldAppointmentStartTime", jdbcType=JdbcType.INTEGER),
        @Result(column="old_appointment_end_time", property="oldAppointmentEndTime", jdbcType=JdbcType.INTEGER),
        @Result(column="old_appt_id", property="oldApptId", jdbcType=JdbcType.INTEGER),
        @Result(column="schedule_type", property="scheduleType", jdbcType=JdbcType.TINYINT),
        @Result(column="source_platform", property="sourcePlatform", jdbcType=JdbcType.VARCHAR),
        @Result(column="ready_time", property="readyTime", jdbcType=JdbcType.BIGINT),
        @Result(column="pickup_notification_send_status", property="pickupNotificationSendStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="pickup_notification_failed_reason", property="pickupNotificationFailedReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="status_before_checkin", property="statusBeforeCheckin", jdbcType=JdbcType.TINYINT),
        @Result(column="status_before_ready", property="statusBeforeReady", jdbcType=JdbcType.TINYINT),
        @Result(column="status_before_finish", property="statusBeforeFinish", jdbcType=JdbcType.TINYINT),
        @Result(column="no_start_time", property="noStartTime", jdbcType=JdbcType.BIT),
        @Result(column="updated_by_id", property="updatedById", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="is_auto_accept", property="isAutoAccept", jdbcType=JdbcType.BIT),
        @Result(column="wait_list_status", property="waitListStatus", jdbcType=JdbcType.TINYINT),
        @Result(column="appointment_end_date", property="appointmentEndDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="service_type_include", property="serviceTypeInclude", jdbcType=JdbcType.INTEGER)
    })
    List<MoeGroomingAppointment> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MoeGroomingAppointmentResult")
    Optional<MoeGroomingAppointment> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default int insertSelective(MoeGroomingAppointment row) {
        return MyBatis3Utils.insert(this::insert, row, moeGroomingAppointment, c ->
            c.map(orderId).toPropertyWhenPresent("orderId", row::getOrderId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(customerId).toPropertyWhenPresent("customerId", row::getCustomerId)
            .map(appointmentDate).toPropertyWhenPresent("appointmentDate", row::getAppointmentDate)
            .map(appointmentStartTime).toPropertyWhenPresent("appointmentStartTime", row::getAppointmentStartTime)
            .map(appointmentEndTime).toPropertyWhenPresent("appointmentEndTime", row::getAppointmentEndTime)
            .map(isWaitingList).toPropertyWhenPresent("isWaitingList", row::getIsWaitingList)
            .map(moveWaitingBy).toPropertyWhenPresent("moveWaitingBy", row::getMoveWaitingBy)
            .map(confirmedTime).toPropertyWhenPresent("confirmedTime", row::getConfirmedTime)
            .map(checkInTime).toPropertyWhenPresent("checkInTime", row::getCheckInTime)
            .map(checkOutTime).toPropertyWhenPresent("checkOutTime", row::getCheckOutTime)
            .map(canceledTime).toPropertyWhenPresent("canceledTime", row::getCanceledTime)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(isBlock).toPropertyWhenPresent("isBlock", row::getIsBlock)
            .map(bookOnlineStatus).toPropertyWhenPresent("bookOnlineStatus", row::getBookOnlineStatus)
            .map(customerAddressId).toPropertyWhenPresent("customerAddressId", row::getCustomerAddressId)
            .map(repeatId).toPropertyWhenPresent("repeatId", row::getRepeatId)
            .map(isPaid).toPropertyWhenPresent("isPaid", row::getIsPaid)
            .map(colorCode).toPropertyWhenPresent("colorCode", row::getColorCode)
            .map(noShow).toPropertyWhenPresent("noShow", row::getNoShow)
            .map(noShowFee).toPropertyWhenPresent("noShowFee", row::getNoShowFee)
            .map(isPustNotification).toPropertyWhenPresent("isPustNotification", row::getIsPustNotification)
            .map(cancelByType).toPropertyWhenPresent("cancelByType", row::getCancelByType)
            .map(cancelBy).toPropertyWhenPresent("cancelBy", row::getCancelBy)
            .map(confirmByType).toPropertyWhenPresent("confirmByType", row::getConfirmByType)
            .map(confirmBy).toPropertyWhenPresent("confirmBy", row::getConfirmBy)
            .map(createdById).toPropertyWhenPresent("createdById", row::getCreatedById)
            .map(outOfArea).toPropertyWhenPresent("outOfArea", row::getOutOfArea)
            .map(isDeprecate).toPropertyWhenPresent("isDeprecate", row::getIsDeprecate)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(source).toPropertyWhenPresent("source", row::getSource)
            .map(oldAppointmentDate).toPropertyWhenPresent("oldAppointmentDate", row::getOldAppointmentDate)
            .map(oldAppointmentStartTime).toPropertyWhenPresent("oldAppointmentStartTime", row::getOldAppointmentStartTime)
            .map(oldAppointmentEndTime).toPropertyWhenPresent("oldAppointmentEndTime", row::getOldAppointmentEndTime)
            .map(oldApptId).toPropertyWhenPresent("oldApptId", row::getOldApptId)
            .map(scheduleType).toPropertyWhenPresent("scheduleType", row::getScheduleType)
            .map(sourcePlatform).toPropertyWhenPresent("sourcePlatform", row::getSourcePlatform)
            .map(readyTime).toPropertyWhenPresent("readyTime", row::getReadyTime)
            .map(pickupNotificationSendStatus).toPropertyWhenPresent("pickupNotificationSendStatus", row::getPickupNotificationSendStatus)
            .map(pickupNotificationFailedReason).toPropertyWhenPresent("pickupNotificationFailedReason", row::getPickupNotificationFailedReason)
            .map(statusBeforeCheckin).toPropertyWhenPresent("statusBeforeCheckin", row::getStatusBeforeCheckin)
            .map(statusBeforeReady).toPropertyWhenPresent("statusBeforeReady", row::getStatusBeforeReady)
            .map(statusBeforeFinish).toPropertyWhenPresent("statusBeforeFinish", row::getStatusBeforeFinish)
            .map(noStartTime).toPropertyWhenPresent("noStartTime", row::getNoStartTime)
            .map(updatedById).toPropertyWhenPresent("updatedById", row::getUpdatedById)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(isAutoAccept).toPropertyWhenPresent("isAutoAccept", row::getIsAutoAccept)
            .map(waitListStatus).toPropertyWhenPresent("waitListStatus", row::getWaitListStatus)
            .map(appointmentEndDate).toPropertyWhenPresent("appointmentEndDate", row::getAppointmentEndDate)
            .map(serviceTypeInclude).toPropertyWhenPresent("serviceTypeInclude", row::getServiceTypeInclude)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default Optional<MoeGroomingAppointment> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default List<MoeGroomingAppointment> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default List<MoeGroomingAppointment> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default Optional<MoeGroomingAppointment> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, moeGroomingAppointment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    static UpdateDSL<UpdateModel> updateAllColumns(MoeGroomingAppointment row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(orderId).equalTo(row::getOrderId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(customerId).equalTo(row::getCustomerId)
                .set(appointmentDate).equalTo(row::getAppointmentDate)
                .set(appointmentStartTime).equalTo(row::getAppointmentStartTime)
                .set(appointmentEndTime).equalTo(row::getAppointmentEndTime)
                .set(isWaitingList).equalTo(row::getIsWaitingList)
                .set(moveWaitingBy).equalTo(row::getMoveWaitingBy)
                .set(confirmedTime).equalTo(row::getConfirmedTime)
                .set(checkInTime).equalTo(row::getCheckInTime)
                .set(checkOutTime).equalTo(row::getCheckOutTime)
                .set(canceledTime).equalTo(row::getCanceledTime)
                .set(status).equalTo(row::getStatus)
                .set(isBlock).equalTo(row::getIsBlock)
                .set(bookOnlineStatus).equalTo(row::getBookOnlineStatus)
                .set(customerAddressId).equalTo(row::getCustomerAddressId)
                .set(repeatId).equalTo(row::getRepeatId)
                .set(isPaid).equalTo(row::getIsPaid)
                .set(colorCode).equalTo(row::getColorCode)
                .set(noShow).equalTo(row::getNoShow)
                .set(noShowFee).equalTo(row::getNoShowFee)
                .set(isPustNotification).equalTo(row::getIsPustNotification)
                .set(cancelByType).equalTo(row::getCancelByType)
                .set(cancelBy).equalTo(row::getCancelBy)
                .set(confirmByType).equalTo(row::getConfirmByType)
                .set(confirmBy).equalTo(row::getConfirmBy)
                .set(createdById).equalTo(row::getCreatedById)
                .set(outOfArea).equalTo(row::getOutOfArea)
                .set(isDeprecate).equalTo(row::getIsDeprecate)
                .set(createTime).equalTo(row::getCreateTime)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(source).equalTo(row::getSource)
                .set(oldAppointmentDate).equalTo(row::getOldAppointmentDate)
                .set(oldAppointmentStartTime).equalTo(row::getOldAppointmentStartTime)
                .set(oldAppointmentEndTime).equalTo(row::getOldAppointmentEndTime)
                .set(oldApptId).equalTo(row::getOldApptId)
                .set(scheduleType).equalTo(row::getScheduleType)
                .set(sourcePlatform).equalTo(row::getSourcePlatform)
                .set(readyTime).equalTo(row::getReadyTime)
                .set(pickupNotificationSendStatus).equalTo(row::getPickupNotificationSendStatus)
                .set(pickupNotificationFailedReason).equalTo(row::getPickupNotificationFailedReason)
                .set(statusBeforeCheckin).equalTo(row::getStatusBeforeCheckin)
                .set(statusBeforeReady).equalTo(row::getStatusBeforeReady)
                .set(statusBeforeFinish).equalTo(row::getStatusBeforeFinish)
                .set(noStartTime).equalTo(row::getNoStartTime)
                .set(updatedById).equalTo(row::getUpdatedById)
                .set(companyId).equalTo(row::getCompanyId)
                .set(isAutoAccept).equalTo(row::getIsAutoAccept)
                .set(waitListStatus).equalTo(row::getWaitListStatus)
                .set(appointmentEndDate).equalTo(row::getAppointmentEndDate)
                .set(serviceTypeInclude).equalTo(row::getServiceTypeInclude);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MoeGroomingAppointment row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(orderId).equalToWhenPresent(row::getOrderId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(customerId).equalToWhenPresent(row::getCustomerId)
                .set(appointmentDate).equalToWhenPresent(row::getAppointmentDate)
                .set(appointmentStartTime).equalToWhenPresent(row::getAppointmentStartTime)
                .set(appointmentEndTime).equalToWhenPresent(row::getAppointmentEndTime)
                .set(isWaitingList).equalToWhenPresent(row::getIsWaitingList)
                .set(moveWaitingBy).equalToWhenPresent(row::getMoveWaitingBy)
                .set(confirmedTime).equalToWhenPresent(row::getConfirmedTime)
                .set(checkInTime).equalToWhenPresent(row::getCheckInTime)
                .set(checkOutTime).equalToWhenPresent(row::getCheckOutTime)
                .set(canceledTime).equalToWhenPresent(row::getCanceledTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(isBlock).equalToWhenPresent(row::getIsBlock)
                .set(bookOnlineStatus).equalToWhenPresent(row::getBookOnlineStatus)
                .set(customerAddressId).equalToWhenPresent(row::getCustomerAddressId)
                .set(repeatId).equalToWhenPresent(row::getRepeatId)
                .set(isPaid).equalToWhenPresent(row::getIsPaid)
                .set(colorCode).equalToWhenPresent(row::getColorCode)
                .set(noShow).equalToWhenPresent(row::getNoShow)
                .set(noShowFee).equalToWhenPresent(row::getNoShowFee)
                .set(isPustNotification).equalToWhenPresent(row::getIsPustNotification)
                .set(cancelByType).equalToWhenPresent(row::getCancelByType)
                .set(cancelBy).equalToWhenPresent(row::getCancelBy)
                .set(confirmByType).equalToWhenPresent(row::getConfirmByType)
                .set(confirmBy).equalToWhenPresent(row::getConfirmBy)
                .set(createdById).equalToWhenPresent(row::getCreatedById)
                .set(outOfArea).equalToWhenPresent(row::getOutOfArea)
                .set(isDeprecate).equalToWhenPresent(row::getIsDeprecate)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(source).equalToWhenPresent(row::getSource)
                .set(oldAppointmentDate).equalToWhenPresent(row::getOldAppointmentDate)
                .set(oldAppointmentStartTime).equalToWhenPresent(row::getOldAppointmentStartTime)
                .set(oldAppointmentEndTime).equalToWhenPresent(row::getOldAppointmentEndTime)
                .set(oldApptId).equalToWhenPresent(row::getOldApptId)
                .set(scheduleType).equalToWhenPresent(row::getScheduleType)
                .set(sourcePlatform).equalToWhenPresent(row::getSourcePlatform)
                .set(readyTime).equalToWhenPresent(row::getReadyTime)
                .set(pickupNotificationSendStatus).equalToWhenPresent(row::getPickupNotificationSendStatus)
                .set(pickupNotificationFailedReason).equalToWhenPresent(row::getPickupNotificationFailedReason)
                .set(statusBeforeCheckin).equalToWhenPresent(row::getStatusBeforeCheckin)
                .set(statusBeforeReady).equalToWhenPresent(row::getStatusBeforeReady)
                .set(statusBeforeFinish).equalToWhenPresent(row::getStatusBeforeFinish)
                .set(noStartTime).equalToWhenPresent(row::getNoStartTime)
                .set(updatedById).equalToWhenPresent(row::getUpdatedById)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(isAutoAccept).equalToWhenPresent(row::getIsAutoAccept)
                .set(waitListStatus).equalToWhenPresent(row::getWaitListStatus)
                .set(appointmentEndDate).equalToWhenPresent(row::getAppointmentEndDate)
                .set(serviceTypeInclude).equalToWhenPresent(row::getServiceTypeInclude);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    default int updateByPrimaryKeySelective(MoeGroomingAppointment row) {
        return update(c ->
            c.set(orderId).equalToWhenPresent(row::getOrderId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(customerId).equalToWhenPresent(row::getCustomerId)
            .set(appointmentDate).equalToWhenPresent(row::getAppointmentDate)
            .set(appointmentStartTime).equalToWhenPresent(row::getAppointmentStartTime)
            .set(appointmentEndTime).equalToWhenPresent(row::getAppointmentEndTime)
            .set(isWaitingList).equalToWhenPresent(row::getIsWaitingList)
            .set(moveWaitingBy).equalToWhenPresent(row::getMoveWaitingBy)
            .set(confirmedTime).equalToWhenPresent(row::getConfirmedTime)
            .set(checkInTime).equalToWhenPresent(row::getCheckInTime)
            .set(checkOutTime).equalToWhenPresent(row::getCheckOutTime)
            .set(canceledTime).equalToWhenPresent(row::getCanceledTime)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(isBlock).equalToWhenPresent(row::getIsBlock)
            .set(bookOnlineStatus).equalToWhenPresent(row::getBookOnlineStatus)
            .set(customerAddressId).equalToWhenPresent(row::getCustomerAddressId)
            .set(repeatId).equalToWhenPresent(row::getRepeatId)
            .set(isPaid).equalToWhenPresent(row::getIsPaid)
            .set(colorCode).equalToWhenPresent(row::getColorCode)
            .set(noShow).equalToWhenPresent(row::getNoShow)
            .set(noShowFee).equalToWhenPresent(row::getNoShowFee)
            .set(isPustNotification).equalToWhenPresent(row::getIsPustNotification)
            .set(cancelByType).equalToWhenPresent(row::getCancelByType)
            .set(cancelBy).equalToWhenPresent(row::getCancelBy)
            .set(confirmByType).equalToWhenPresent(row::getConfirmByType)
            .set(confirmBy).equalToWhenPresent(row::getConfirmBy)
            .set(createdById).equalToWhenPresent(row::getCreatedById)
            .set(outOfArea).equalToWhenPresent(row::getOutOfArea)
            .set(isDeprecate).equalToWhenPresent(row::getIsDeprecate)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(source).equalToWhenPresent(row::getSource)
            .set(oldAppointmentDate).equalToWhenPresent(row::getOldAppointmentDate)
            .set(oldAppointmentStartTime).equalToWhenPresent(row::getOldAppointmentStartTime)
            .set(oldAppointmentEndTime).equalToWhenPresent(row::getOldAppointmentEndTime)
            .set(oldApptId).equalToWhenPresent(row::getOldApptId)
            .set(scheduleType).equalToWhenPresent(row::getScheduleType)
            .set(sourcePlatform).equalToWhenPresent(row::getSourcePlatform)
            .set(readyTime).equalToWhenPresent(row::getReadyTime)
            .set(pickupNotificationSendStatus).equalToWhenPresent(row::getPickupNotificationSendStatus)
            .set(pickupNotificationFailedReason).equalToWhenPresent(row::getPickupNotificationFailedReason)
            .set(statusBeforeCheckin).equalToWhenPresent(row::getStatusBeforeCheckin)
            .set(statusBeforeReady).equalToWhenPresent(row::getStatusBeforeReady)
            .set(statusBeforeFinish).equalToWhenPresent(row::getStatusBeforeFinish)
            .set(noStartTime).equalToWhenPresent(row::getNoStartTime)
            .set(updatedById).equalToWhenPresent(row::getUpdatedById)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(isAutoAccept).equalToWhenPresent(row::getIsAutoAccept)
            .set(waitListStatus).equalToWhenPresent(row::getWaitListStatus)
            .set(appointmentEndDate).equalToWhenPresent(row::getAppointmentEndDate)
            .set(serviceTypeInclude).equalToWhenPresent(row::getServiceTypeInclude)
            .where(id, isEqualTo(row::getId))
        );
    }
}