// @since {{> date }}
// <AUTHOR> user }} <{{> email }}>

syntax = "proto3";

package moego.models.{{ domain }};

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/{{ domainPath domain }};{{ goPackage domain 'pb' }}";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.{{ domain }}";

// The {{ service }} Full Definition
message {{ service }}CreateDef {
  // name
  string name = 1 [(validate.rules).string = { min_len: 1, max_len: 100 }];
}

// The {{ service }} Partial Definition
message {{ service }}UpdateDef {
  // name
  optional string name = 1 [(validate.rules).string = { min_len: 1, max_len: 100 }];
}
