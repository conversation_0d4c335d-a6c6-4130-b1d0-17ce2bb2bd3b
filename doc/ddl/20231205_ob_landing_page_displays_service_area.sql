create table if not exists moe_grooming.service_area_pic_cache
(
  id           int auto_increment comment 'id'
    primary key,
  business_id  int                                     not null comment 'business id',
  url          varchar(255)                            not null comment '图片地址',
  factors_hash varchar(1024) default ''                not null comment '影响图片生成的所有因素的 hash 值',
  created_at   datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updated_at   datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  constraint service_area_pic_cache_pk
    unique (business_id)
)
  comment 'service area 图片缓存';

