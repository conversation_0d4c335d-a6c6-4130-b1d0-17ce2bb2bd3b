create table moe_grooming.moe_book_online_pet_limit
(
    id          bigint unsigned auto_increment
        primary key,
    business_id int unsigned      not null comment 'business id',
    type        tinyint default 1 not null comment 'type 1-size 2-breed',
    find_id     bigint  default 0 not null comment 'find 1-moe_pet_size 2-moe_book_online_pet_limit_breed_binding',
    max_number  int     default 0 not null comment 'max number limit',
    status      tinyint default 1 not null comment 'status 1-normal 2-deleted',
    create_time datetime          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    update_time datetime          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    index index_business (business_id)
)
    collate = utf8mb4_general_ci;