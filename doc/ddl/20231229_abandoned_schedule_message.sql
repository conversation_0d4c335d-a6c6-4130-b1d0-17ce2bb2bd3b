INSERT INTO moe_payment.moe_feature (name, code, enable)
VALUES ('OB abandoned schedule message', 'abandonedScheduleMessage', 0);

INSERT INTO moe_payment.moe_plan_feature_relation (level, code, enable)
VALUES (1101, 'abandonedScheduleMessage', 1),
       (1201, 'abandonedScheduleMessage', 1);

alter table moe_grooming.moe_book_online_abandon_record
  add is_send_schedule_message boolean default false not null comment '是否发送过 schedule message';

create table if not exists moe_grooming.abandoned_schedule_message_setting
(
  id                   int auto_increment comment 'id'
    primary key,
  business_id          int                                     not null comment 'business id',
  client_types         json          default (json_array())    not null comment 'client types to send messages, e.g. ["new_visitors", "existing_clients"]',
  abandoned_steps      json          default (json_array())    not null comment 'Abandoned steps to send messages, e.g. ["select_groomer", "select_time"]',
  send_out_type        varchar(50)   default ''                not null comment 'send out type',
  on_type_days         json          default (json_array())    not null comment '需要发送的 day of week，当 send out type 使用 on 时使用的配置',
  on_type_minute       int           default 0                 not null comment '发送的时间，当 send out type 使用 on 时使用的配置',
  wait_for_type_minute int           default 60                not null comment 'delay 时间，当 send out type 使用 wait for 时使用的配置',
  message              varchar(2048) default ''                not null comment '消息内容',
  is_enabled           tinyint(1)    default 0                 not null comment '是否启用',
  created_at           datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updated_at           datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  constraint abandoned_schedule_message_setting_business_id_uindex
    unique (business_id)
);

create table if not exists moe_message.abandoned_schedule_message
(
  id                  int auto_increment comment 'id'
    primary key,
  business_id         int                                     not null,
  abandon_record_id   int                                     not null comment 'abandon record id',
  content             varchar(2000) default ''                not null comment 'message content',
  customer_phone      varchar(50)                             not null,
  customer_first_name varchar(100)  default ''                not null,
  customer_last_name  varchar(100)  default ''                not null,
  customer_reply_time datetime                                null comment 'customer 回复时间',
  created_at          datetime      default CURRENT_TIMESTAMP not null,
  updated_at          datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
  comment '记录 abandoned schedule message 的发送记录';

create index abandoned_schedule_message_business_id_index
  on moe_message.abandoned_schedule_message (business_id);

create index abandoned_schedule_message_customer_phone_index
  on moe_message.abandoned_schedule_message (customer_phone);

