# moe_business_book_online 表修改字段注释、新增字段
# enable_no_show_fee 修改注释
ALTER TABLE moe_grooming.moe_business_book_online MODIFY COLUMN `enable_no_show_fee` tinyint NOT NULL DEFAULT '0' COMMENT 'no show protection, 0-closed, 1-required credit card, 2-required prepay, full pay/deposit, depend on prepay_type';

# 新增字段 for prepay
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `prepay_type` tinyint NOT NULL DEFAULT '1' COMMENT 'prepay type, 0-full pay, 1-deposit';
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `prepay_tip_enable` tinyint NOT NULL DEFAULT '1' COMMENT 'prepay add-tips enable, 0-disable, 1-enable';
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `deposit_type` tinyint NOT NULL DEFAULT '0' COMMENT 'prepay deposit type, 0-by fixed amount, 1-by percentage';
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `deposit_percentage` int NOT NULL DEFAULT '30' COMMENT 'deposit percentage, default 30, range: 1-100';
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `deposit_amount` decimal(20,2) NOT NULL DEFAULT '20.00' COMMENT 'deposit amount, default 0, min: 1';
ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN `prepay_policy` text COMMENT 'Prepay policy';

#新增OB deposit表，用于存放OB定金信息
CREATE TABLE `moe_book_online_deposit`
(
  `id`              int unsigned   NOT NULL AUTO_INCREMENT,
  `business_id`     int            NOT NULL DEFAULT '0',
  `grooming_id`     int            NOT NULL DEFAULT '0',
  `guid`            varchar(100)   NOT NULL DEFAULT '' COMMENT 'payment guid for prepay, deposit is start with de_, and other is full pay',
  `payment_id`      int            NOT NULL DEFAULT '0',
  `amount`          decimal(20, 2) NOT NULL DEFAULT '0.00' COMMENT 'prepay amount, total exclude booking fee',
  `booking_fee`     decimal(20, 2) NOT NULL DEFAULT '0.00' COMMENT 'extra fee when client prepay',
  `tips_amount`     decimal(20, 2) NOT NULL DEFAULT '0.00' COMMENT 'tips amount, only in full pay',
  `convenience_fee` decimal(20, 2) NOT NULL DEFAULT '0.00' COMMENT 'convenience fee of current deposit record',
  `status`          tinyint        NOT NULL DEFAULT '1' COMMENT 'PROCESSING = 1; REQUIRE_PAYMENT_METHOD = 2; REQUIRE_CONFIRM = 3; REQUIRE_CAPTURE = 4; PROCESSING_CAPTURE = 5; PAID = 6; REFUNDED = 7; CANCEL = 8; FAILED = -1;',
  `create_time`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_guid` (`guid`) USING BTREE,
  KEY `idx_bid_pid` (`payment_id`,  `business_id`) USING BTREE,
  KEY `idx_bid_gid` (`business_id`, `grooming_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='OB Prepay record, include deposit and full-pay';
