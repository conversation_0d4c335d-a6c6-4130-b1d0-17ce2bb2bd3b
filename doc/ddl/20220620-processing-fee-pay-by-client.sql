ALTER TABLE moe_grooming.moe_grooming_invoice ADD COLUMN convenience_fee decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'processing fee pay by client total amount';

CREATE TABLE `moe_grooming`.`moe_grooming_online_fee_invoice` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `business_id` int NOT NULL DEFAULT '0' COMMENT 'business id',
    `invoice_id` int NOT NULL DEFAULT '0' COMMENT 'invoice id',
    `type` tinyint NOT NULL DEFAULT '1' COMMENT 'online link type: 1-invoice, 2-deposit',
    `required_fee` tinyint NOT NULL DEFAULT '1' COMMENT 'required convenience fee pay by client, 0-no, 1-yes',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    PRIMARY KEY (`id`) USING BTREE,
    KEY 'idx_bizid_invoiceid' (`business_id`, invoice_id) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='online pay invoice convenience fee switch record';
