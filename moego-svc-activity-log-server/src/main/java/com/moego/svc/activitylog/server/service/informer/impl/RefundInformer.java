package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class RefundInformer extends AbstractStaffOperatorCustomerOwnerInformer<List<RefundDTO>> {

    private final IPaymentRefundClient paymentRefundClient;

    @Override
    public String resourceType() {
        return ResourceType.REFUND.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public List<RefundDTO> resource(String resourceId) {
        return paymentRefundClient.getRefunds(Integer.valueOf(resourceId));
    }

    @Override
    public String resourceName(List<RefundDTO> refundDTOS) {
        return "Refund";
    }

    @Override
    public String getOwnerId(List<RefundDTO> refundDTOS) {
        if (CollectionUtils.isEmpty(refundDTOS)) {
            return null;
        } else {
            return String.valueOf(refundDTOS.get(0).getCustomerId());
        }
    }
}
