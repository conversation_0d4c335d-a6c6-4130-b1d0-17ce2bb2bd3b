package com.moego.svc.activitylog.server.service.mapper;

import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentCancelByTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentConfirmByTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentPayStatusMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentRepeatApplyTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentRepeatEndTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentRepeatFrequencyTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentScheduleTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentSourceMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentStatusMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AppointmentWorkModeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AutoMessageIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.AutoMessageTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.BooleanMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerAddressIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerContactIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerContactTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerFrequencyTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerPreferredTipTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.CustomerReminderReceiveTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.GroomingCustomerServiceSaveTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.GroomingServiceFilterScopeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.GroomingServiceIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.GroomingServiceShowPriceMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.GroomingServiceTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.MinuteToTimeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.OrderIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.OrderStatusMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.PetGenderMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.PetIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.PetLifeStatusMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.PetTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.PetVaccineTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.ReminderBeforeDayMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.ReminderTypeMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.StaffIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.StatusMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.TaxIdMapper;
import com.moego.svc.activitylog.server.service.mapper.impl.TimestampMapper;
import lombok.Getter;

/**
 * MapperName 枚举，用于注册 Mapper 实现类
 * 枚举名可作为 application.yaml 中的配置值
 * mapperClass 为具体执行映射逻辑的 Mapper 实现类
 */
public enum MapperName {
    // 布尔值映射
    BOOLEAN(BooleanMapper.class),
    // 分钟数映射为时间
    MINUTE_TO_TIME(MinuteToTimeMapper.class),
    // 时间戳映射
    TIMESTAMP(TimestampMapper.class),

    // 通用 status 映射
    STATUS(StatusMapper.class),

    /* pet 相关字段的映射 */
    // pet id
    PET_ID(PetIdMapper.class),
    // pet type
    PET_TYPE(PetTypeMapper.class),
    // pet vaccine type
    PET_VACCINE_TYPE(PetVaccineTypeMapper.class),
    // pet gender
    PET_GENDER(PetGenderMapper.class),
    // pet life status
    PET_LIFE_STATUS(PetLifeStatusMapper.class),

    /* customer 相关字段的映射 */
    // customer id
    CUSTOMER_ID(CustomerIdMapper.class),
    // customer address id
    CUSTOMER_ADDRESS_ID(CustomerAddressIdMapper.class),
    // customer contact id
    CUSTOMER_CONTACT_ID(CustomerContactIdMapper.class),
    // customer preferred tip type
    CUSTOMER_PREFERRED_TIP_TYPE(CustomerPreferredTipTypeMapper.class),
    // customer frequency type
    CUSTOMER_FREQUENCY_TYPE(CustomerFrequencyTypeMapper.class),
    // customer contact type
    CUSTOMER_CONTACT_TYPE(CustomerContactTypeMapper.class),
    // customer reminder receive type
    CUSTOMER_REMINDER_RECEIVE_TYPE(CustomerReminderReceiveTypeMapper.class),

    /* staff 相关字段的映射 */
    // staff id
    STAFF_ID(StaffIdMapper.class),

    /* grooming service 相关字段的映射 */
    // grooming service id
    GROOMING_SERVICE_ID(GroomingServiceIdMapper.class),
    // grooming service type
    GROOMING_SERVICE_TYPE(GroomingServiceTypeMapper.class),
    // grooming service filter scope
    GROOMING_SERVICE_FILTER_SCOPE(GroomingServiceFilterScopeMapper.class),
    // grooming service show price
    GROOMING_SERVICE_SHOW_PRICE(GroomingServiceShowPriceMapper.class),
    // grooming customer service save type
    GROOMING_CUSTOMER_SERVICE_SAVE_TYPE(GroomingCustomerServiceSaveTypeMapper.class),

    /* appointment 相关字段的映射 */
    // appointment schedule type
    APPOINTMENT_SCHEDULE_TYPE(AppointmentScheduleTypeMapper.class),
    // appointment work mode
    APPOINTMENT_WORK_MODE(AppointmentWorkModeMapper.class),
    // appointment repeat frequency type
    APPOINTMENT_REPEAT_FREQUENCY_TYPE(AppointmentRepeatFrequencyTypeMapper.class),
    // appointment repeat apply type
    APPOINTMENT_REPEAT_APPLY_TYPE(AppointmentRepeatApplyTypeMapper.class),
    // appointment repeat end type
    APPOINTMENT_REPEAT_END_TYPE(AppointmentRepeatEndTypeMapper.class),
    // appointment status
    APPOINTMENT_STATUS(AppointmentStatusMapper.class),
    // appointment pay status
    APPOINTMENT_PAY_STATUS(AppointmentPayStatusMapper.class),
    // appointment cancel by type
    APPOINTMENT_CANCEL_BY_TYPE(AppointmentCancelByTypeMapper.class),
    // appointment confirm by type
    APPOINTMENT_CONFIRM_BY_TYPE(AppointmentConfirmByTypeMapper.class),
    // appointment source
    APPOINTMENT_SOURCE(AppointmentSourceMapper.class),

    /* message 相关字段的映射 */
    // reminder type
    REMINDER_TYPE(ReminderTypeMapper.class),
    // reminder before day
    REMINDER_BEFORE_DAY(ReminderBeforeDayMapper.class),
    // auto message type
    AUTO_MESSAGE_TYPE(AutoMessageTypeMapper.class),
    // auto message id
    AUTO_MESSAGE_ID(AutoMessageIdMapper.class),

    /* payment 相关的字段映射 */
    // tax id
    TAX_ID(TaxIdMapper.class),
    ORDER_ID(OrderIdMapper.class),
    // order status
    ORDER_STATUS(OrderStatusMapper.class),
    ;

    @Getter
    private final Class<? extends Mapper<?>> mapperClass;

    MapperName(Class<? extends Mapper<?>> mapperClass) {
        this.mapperClass = mapperClass;
    }
}
