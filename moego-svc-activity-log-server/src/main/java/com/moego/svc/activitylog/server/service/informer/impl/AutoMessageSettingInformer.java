package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#AUTO_MESSAGE_SETTING}.
 *
 * <AUTHOR>
 */
@Component
public class AutoMessageSettingInformer extends AbstractStaffOperatorInformer<AutoMessageTemplateEnum> {

    @Override
    public AutoMessageTemplateEnum resource(String type) {
        return AutoMessageTemplateEnum.fromValue(Integer.parseInt(type));
    }

    @Override
    public String resourceName(AutoMessageTemplateEnum autoMessageTemplateEnum) {
        return autoMessageTemplateEnum.getDesc();
    }

    @Override
    public String resourceType() {
        return ResourceType.AUTO_MESSAGE_SETTING.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
