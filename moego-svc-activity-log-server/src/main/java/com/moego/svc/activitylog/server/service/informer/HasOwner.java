package com.moego.svc.activitylog.server.service.informer;

/**
 * @param <O> owner type
 * <AUTHOR>
 */
public interface HasOwner<O> {

    /**
     * Get owner.
     *
     * @param ownerId owner id
     * @return owner
     */
    O owner(String ownerId);

    /**
     * Get human-readable owner name.
     *
     * @param owner owner
     * @return human-readable owner name
     */
    String ownerName(O owner);
}
