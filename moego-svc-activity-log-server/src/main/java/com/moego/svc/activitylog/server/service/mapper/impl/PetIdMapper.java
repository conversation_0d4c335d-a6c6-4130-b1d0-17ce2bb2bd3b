package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@AllArgsConstructor
public class PetIdMapper implements Mapper<CustomerPetDetailDTO> {

    private final IPetService petApi;

    /**
     * Map pet ids to names.
     * @param petIds pet ids
     * @return map of pet id -> name
     */
    @Override
    public Map<String, String> map(Set<String> petIds) {
        Map<String, String> result = new HashMap<>(petIds.size());

        var intPetIdList =
                petIds.stream().map(Integer::parseInt).filter(id -> id > 0).toList();

        try {
            List<CustomerPetDetailDTO> pets = petApi.getCustomerPetListByIdList(intPetIdList);
            if (CollectionUtils.isEmpty(pets)) {
                return result;
            }

            pets.forEach(pet -> {
                var id = String.valueOf(pet.getPetId());
                var name = getName(pet);
                result.put(id, name);
            });

        } catch (Exception e) {
            log.error("Failed to get pet name for pet id: {}", petIds, e);
        }
        return result;
    }

    @Override
    public String getName(CustomerPetDetailDTO pet) {
        return pet.getPetName();
    }
}
