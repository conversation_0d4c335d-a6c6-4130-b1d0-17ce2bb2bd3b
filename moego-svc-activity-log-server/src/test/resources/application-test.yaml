spring:
  jpa:
    generate-ddl: true
    database-platform: org.hibernate.dialect.H2Dialect
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
moego:
  messaging:
    enabled: true
    pulsar:
      service-url: pulsar.t2.moego.pet:40650
      authentication: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      tenant: test2
