package com.moego.lib.messaging.pulsar;

import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.messaging.Msg;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.PulsarClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 * Pulsar implementation of {@link Msg}.
 *
 * @param <T> event type
 */
public class PulsarMsg<T> implements Msg<T> {

    private static final Logger log = LoggerFactory.getLogger(PulsarMsg.class);

    private final Message<String> msg;
    private final Consumer<String> consumer;
    private final Class<?> eventType;
    private final AtomicBoolean manuallyAcked = new AtomicBoolean(false);

    public PulsarMsg(Message<String> msg, Consumer<String> consumer, Class<?> eventType) {
        Assert.notNull(msg, "msg cannot be null");
        Assert.notNull(consumer, "consumer cannot be null");
        Assert.notNull(eventType, "eventType cannot be null");
        this.msg = msg;
        this.consumer = consumer;
        this.eventType = eventType;
    }

    @Override
    @SuppressWarnings("unchecked")
    public T getBody() {
        String json = msg.getValue();
        Object event = JsonUtil.toBean(json, eventType);
        return (T) event;
    }

    @Override
    public Map<String, String> getHeaders() {
        return Map.copyOf(msg.getProperties());
    }

    @Override
    public void ack() {
        try {
            consumer.acknowledge(msg);
            manuallyAcked.set(true);
            if (log.isDebugEnabled()) {
                log.debug("Ack message: {}", msg.getMessageId());
            }
        } catch (PulsarClientException.AlreadyClosedException e) {
            throw new RuntimeException("Consumer closed when ack", e);
        } catch (PulsarClientException e) {
            throw new RuntimeException("Occurred a unknown exception when ack message, should never happen!", e);
        }
    }

    @Override
    public void nack() {
        consumer.negativeAcknowledge(msg);
        manuallyAcked.set(true);
        if (log.isDebugEnabled()) {
            log.debug("Negative ack message: {}", msg.getMessageId());
        }
    }

    /**
     * @return true if message is manually acked
     */
    boolean manuallyAcked() {
        return manuallyAcked.get();
    }
}
