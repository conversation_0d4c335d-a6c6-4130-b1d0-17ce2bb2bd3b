package com.moego.lib.messaging.pulsar;

import com.moego.lib.messaging.InitialPosition;
import com.moego.lib.messaging.SubscribeType;
import java.util.List;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public record PulsarConsumeModel(
        String consumerName,
        List<String> topics,
        String subscriptionName,
        int threadCount,
        InitialPosition initialPosition,
        SubscribeType subscribeType) {
    public PulsarConsumeModel {
        if (!StringUtils.hasText(consumerName)) {
            throw new IllegalArgumentException("Consumer name must not be empty");
        }
        if (CollectionUtils.isEmpty(topics)) {
            throw new IllegalArgumentException("Topics must not be empty");
        }
        if (!StringUtils.hasText(subscriptionName)) {
            throw new IllegalArgumentException("Subscription name must not be empty");
        }
        if (threadCount <= 0) {
            throw new IllegalArgumentException("Thread count must be greater than 0");
        }
        Assert.notNull(initialPosition, "Initial position must not be null");
        Assert.notNull(subscribeType, "Subscribe type must not be null");
    }
}
