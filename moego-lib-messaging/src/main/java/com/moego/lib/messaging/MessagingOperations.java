package com.moego.lib.messaging;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Messaging operations.
 *
 * <p> Example:
 * <pre>{@code
 * // Blocking send event.
 * messagingOperations.send(FooEvent.TOPIC, new FooEvent());
 *
 * // Event will be consumed after 10 second.
 * messagingOperations.sendDelayed(FooEvent.TOPIC, new FooEvent(), 10000L);
 * }</pre>
 *
 * <AUTHOR>
 * @since 2022/11/30
 */
public interface MessagingOperations {
    /**
     * Send a message to the specified topic.
     *
     * @param topic   topic
     * @param message message
     */
    void send(String topic, Object message);

    /**
     * Send a message with headers to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param headers headers
     */
    void send(String topic, Object message, Map<String, String> headers);

    /**
     * Asynchronously Send a message to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @return {@link CompletableFuture}
     */
    CompletableFuture<?> asyncSend(String topic, Object message);

    /**
     * Asynchronously Send a message with headers to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param headers headers
     * @return {@link CompletableFuture}
     */
    CompletableFuture<?> asyncSend(String topic, Object message, Map<String, String> headers);

    /**
     * Send a delayed message to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param delay   delay in milliseconds
     */
    void sendDelayed(String topic, Object message, long delay);

    /**
     * Send a delayed message with headers to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param delay   delay in milliseconds
     * @param headers headers
     */
    void sendDelayed(String topic, Object message, long delay, Map<String, String> headers);

    /**
     * Asynchronously send a delayed message to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param delay   delay in milliseconds
     * @return {@link CompletableFuture}
     */
    CompletableFuture<?> asyncSendDelayed(String topic, Object message, long delay);

    /**
     * Asynchronously send a delayed message with headers to the specified topic.
     *
     * @param topic   topic
     * @param message message
     * @param delay   delay in milliseconds
     * @param headers headers
     * @return {@link CompletableFuture}
     */
    CompletableFuture<?> asyncSendDelayed(String topic, Object message, long delay, Map<String, String> headers);
}
