package com.moego.lib.messaging;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;

/**
 * {@link ConsumerExecutor} use to start all {@link MessagingConsume} instances when application ready.
 *
 * <AUTHOR>
 * @since 2022/11/30
 */
public class ConsumerExecutor
        implements SmartInitializingSingleton, DisposableBean, ApplicationListener<ApplicationReadyEvent> {

    private final MessagingConsumeCollector messagingConsumeCollector;
    private final List<MessagingConsume> consumes = new ArrayList<>();

    public ConsumerExecutor(MessagingConsumeCollector messagingConsumeCollector) {
        this.messagingConsumeCollector = messagingConsumeCollector;
    }

    @Override
    public void afterSingletonsInstantiated() {
        // NOTE: application not ready yet, may not pass the health check,
        // we can't consume any messages at this phase.
        // So just create the consumers, start them when application ready
        consumes.addAll(messagingConsumeCollector.collect());
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // application ready, start consuming messages
        consumes.forEach(MessagingConsume::start);
    }

    @Override
    public void destroy() {
        // Wait for all consumes to finish
        CompletableFuture.allOf(consumes.stream()
                        .map(consume -> CompletableFuture.runAsync(consume::stop))
                        .toArray(CompletableFuture[]::new))
                .join();
    }
}
