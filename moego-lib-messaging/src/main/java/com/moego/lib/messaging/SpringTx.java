package com.moego.lib.messaging;

import static org.springframework.transaction.support.TransactionSynchronizationManager.isActualTransactionActive;
import static org.springframework.transaction.support.TransactionSynchronizationManager.isSynchronizationActive;
import static org.springframework.transaction.support.TransactionSynchronizationManager.registerSynchronization;

import com.moego.lib.common.util.Tx;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class SpringTx {

    /**
     * Add a callback to be invoked after transaction commit, or run immediately if not in a transaction.
     *
     * @param runnable runnable
     * @deprecated by <PERSON>, use {@link Tx#doAfterCommit(Runnable)} instead
     */
    @Deprecated(since = "2024/8/1")
    public static void addAfterCommitCallback(Runnable runnable) {
        if (isSynchronizationActive() && isActualTransactionActive()) {
            registerSynchronization(new MessagingTransactionSynchronization(runnable));
        } else {
            // not in a transaction, run immediately
            runnable.run();
        }
    }
}
