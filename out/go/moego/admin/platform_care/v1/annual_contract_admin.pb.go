// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/platform_care/v1/annual_contract_admin.proto

package platformcareapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/platform_sales/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create annual contract params
type CreateAnnualContractParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// subscription plan
	SubscriptionPlan v1.SubscriptionPlan `protobuf:"varint,2,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=moego.admin.platform_sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	// subscription term months
	// e.g., 12 means 12 months (1 year)
	SubscriptionTermMonths int32 `protobuf:"varint,3,opt,name=subscription_term_months,json=subscriptionTermMonths,proto3" json:"subscription_term_months,omitempty"`
	// discount percentage of subscription
	// e.g., "10" means 10%
	DiscountPercentage string `protobuf:"bytes,4,opt,name=discount_percentage,json=discountPercentage,proto3" json:"discount_percentage,omitempty"`
	// number of boarding & daycare locations
	BdLocationCount int32 `protobuf:"varint,5,opt,name=bd_location_count,json=bdLocationCount,proto3" json:"bd_location_count,omitempty"`
	// number of grooming locations
	GroomingLocationCount int32 `protobuf:"varint,6,opt,name=grooming_location_count,json=groomingLocationCount,proto3" json:"grooming_location_count,omitempty"`
	// number of grooming vans
	GroomingVanCount int32 `protobuf:"varint,7,opt,name=grooming_van_count,json=groomingVanCount,proto3" json:"grooming_van_count,omitempty"`
}

func (x *CreateAnnualContractParams) Reset() {
	*x = CreateAnnualContractParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnualContractParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnualContractParams) ProtoMessage() {}

func (x *CreateAnnualContractParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnualContractParams.ProtoReflect.Descriptor instead.
func (*CreateAnnualContractParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAnnualContractParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAnnualContractParams) GetSubscriptionPlan() v1.SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return v1.SubscriptionPlan(0)
}

func (x *CreateAnnualContractParams) GetSubscriptionTermMonths() int32 {
	if x != nil {
		return x.SubscriptionTermMonths
	}
	return 0
}

func (x *CreateAnnualContractParams) GetDiscountPercentage() string {
	if x != nil {
		return x.DiscountPercentage
	}
	return ""
}

func (x *CreateAnnualContractParams) GetBdLocationCount() int32 {
	if x != nil {
		return x.BdLocationCount
	}
	return 0
}

func (x *CreateAnnualContractParams) GetGroomingLocationCount() int32 {
	if x != nil {
		return x.GroomingLocationCount
	}
	return 0
}

func (x *CreateAnnualContractParams) GetGroomingVanCount() int32 {
	if x != nil {
		return x.GroomingVanCount
	}
	return 0
}

// create annual contract result
type CreateAnnualContractResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contract id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateAnnualContractResult) Reset() {
	*x = CreateAnnualContractResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAnnualContractResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnualContractResult) ProtoMessage() {}

func (x *CreateAnnualContractResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnualContractResult.ProtoReflect.Descriptor instead.
func (*CreateAnnualContractResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAnnualContractResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// list annual contracts params
type ListAnnualContractsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// owner email
	OwnerEmail *string `protobuf:"bytes,2,opt,name=owner_email,json=ownerEmail,proto3,oneof" json:"owner_email,omitempty"`
	// creator
	Creator *string `protobuf:"bytes,3,opt,name=creator,proto3,oneof" json:"creator,omitempty"`
}

func (x *ListAnnualContractsParams) Reset() {
	*x = ListAnnualContractsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnualContractsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnualContractsParams) ProtoMessage() {}

func (x *ListAnnualContractsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnualContractsParams.ProtoReflect.Descriptor instead.
func (*ListAnnualContractsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{2}
}

func (x *ListAnnualContractsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAnnualContractsParams) GetOwnerEmail() string {
	if x != nil && x.OwnerEmail != nil {
		return *x.OwnerEmail
	}
	return ""
}

func (x *ListAnnualContractsParams) GetCreator() string {
	if x != nil && x.Creator != nil {
		return *x.Creator
	}
	return ""
}

// list annual contracts result
type ListAnnualContractsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// annual contracts
	AnnualContracts []*AnnualContract `protobuf:"bytes,1,rep,name=annual_contracts,json=annualContracts,proto3" json:"annual_contracts,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListAnnualContractsResult) Reset() {
	*x = ListAnnualContractsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAnnualContractsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnualContractsResult) ProtoMessage() {}

func (x *ListAnnualContractsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnualContractsResult.ProtoReflect.Descriptor instead.
func (*ListAnnualContractsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{3}
}

func (x *ListAnnualContractsResult) GetAnnualContracts() []*AnnualContract {
	if x != nil {
		return x.AnnualContracts
	}
	return nil
}

func (x *ListAnnualContractsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// annual contract
type AnnualContract struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// template id
	TemplateId string `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// metadata
	Metadata *AnnualContract_Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// params
	Parameters *AnnualContract_Parameters `protobuf:"bytes,4,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// content
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// creator
	Creator string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// sign time
	SignTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=sign_time,json=signTime,proto3,oneof" json:"sign_time,omitempty"`
}

func (x *AnnualContract) Reset() {
	*x = AnnualContract{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualContract) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract) ProtoMessage() {}

func (x *AnnualContract) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract.ProtoReflect.Descriptor instead.
func (*AnnualContract) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{4}
}

func (x *AnnualContract) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnnualContract) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *AnnualContract) GetMetadata() *AnnualContract_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AnnualContract) GetParameters() *AnnualContract_Parameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *AnnualContract) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AnnualContract) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *AnnualContract) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AnnualContract) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AnnualContract) GetSignTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SignTime
	}
	return nil
}

// metadata
type AnnualContract_Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// subscription plan
	SubscriptionPlan v1.SubscriptionPlan `protobuf:"varint,3,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=moego.admin.platform_sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
}

func (x *AnnualContract_Metadata) Reset() {
	*x = AnnualContract_Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualContract_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_Metadata) ProtoMessage() {}

func (x *AnnualContract_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_Metadata.ProtoReflect.Descriptor instead.
func (*AnnualContract_Metadata) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{4, 0}
}

func (x *AnnualContract_Metadata) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AnnualContract_Metadata) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AnnualContract_Metadata) GetSubscriptionPlan() v1.SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return v1.SubscriptionPlan(0)
}

// parameters for rendering
type AnnualContract_Parameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// company name
	CompanyName string `protobuf:"bytes,1,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// owner name
	OwnerName string `protobuf:"bytes,2,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// owner email
	OwnerEmail string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// address
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// subscription plan name
	SubscriptionPlanName string `protobuf:"bytes,5,opt,name=subscription_plan_name,json=subscriptionPlanName,proto3" json:"subscription_plan_name,omitempty"`
	// subscription term months
	SubscriptionTermMonths int32 `protobuf:"varint,6,opt,name=subscription_term_months,json=subscriptionTermMonths,proto3" json:"subscription_term_months,omitempty"`
	// discount percentage
	DiscountPercentage string `protobuf:"bytes,7,opt,name=discount_percentage,json=discountPercentage,proto3" json:"discount_percentage,omitempty"`
	// total amount
	TotalAmount string `protobuf:"bytes,8,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// grooming location
	GroomingLocation *AnnualContract_ProductLineItem `protobuf:"bytes,9,opt,name=grooming_location,json=groomingLocation,proto3,oneof" json:"grooming_location,omitempty"`
	// boarding & daycare location
	BdLocation *AnnualContract_ProductLineItem `protobuf:"bytes,10,opt,name=bd_location,json=bdLocation,proto3,oneof" json:"bd_location,omitempty"`
	// grooming van
	GroomingVan *AnnualContract_ProductLineItem `protobuf:"bytes,11,opt,name=grooming_van,json=groomingVan,proto3,oneof" json:"grooming_van,omitempty"`
}

func (x *AnnualContract_Parameters) Reset() {
	*x = AnnualContract_Parameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualContract_Parameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_Parameters) ProtoMessage() {}

func (x *AnnualContract_Parameters) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_Parameters.ProtoReflect.Descriptor instead.
func (*AnnualContract_Parameters) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{4, 1}
}

func (x *AnnualContract_Parameters) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *AnnualContract_Parameters) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AnnualContract_Parameters) GetSubscriptionPlanName() string {
	if x != nil {
		return x.SubscriptionPlanName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetSubscriptionTermMonths() int32 {
	if x != nil {
		return x.SubscriptionTermMonths
	}
	return 0
}

func (x *AnnualContract_Parameters) GetDiscountPercentage() string {
	if x != nil {
		return x.DiscountPercentage
	}
	return ""
}

func (x *AnnualContract_Parameters) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *AnnualContract_Parameters) GetGroomingLocation() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.GroomingLocation
	}
	return nil
}

func (x *AnnualContract_Parameters) GetBdLocation() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.BdLocation
	}
	return nil
}

func (x *AnnualContract_Parameters) GetGroomingVan() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.GroomingVan
	}
	return nil
}

// product line item
type AnnualContract_ProductLineItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of units.
	Quantity int32 `protobuf:"varint,1,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// The price per unit before any discounts.
	UnitPrice string `protobuf:"bytes,2,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// The final price per unit after discounts have been applied.
	DiscountedUnitPrice string `protobuf:"bytes,3,opt,name=discounted_unit_price,json=discountedUnitPrice,proto3" json:"discounted_unit_price,omitempty"`
}

func (x *AnnualContract_ProductLineItem) Reset() {
	*x = AnnualContract_ProductLineItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnualContract_ProductLineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_ProductLineItem) ProtoMessage() {}

func (x *AnnualContract_ProductLineItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_ProductLineItem.ProtoReflect.Descriptor instead.
func (*AnnualContract_ProductLineItem) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP(), []int{4, 2}
}

func (x *AnnualContract_ProductLineItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *AnnualContract_ProductLineItem) GetUnitPrice() string {
	if x != nil {
		return x.UnitPrice
	}
	return ""
}

func (x *AnnualContract_ProductLineItem) GetDiscountedUnitPrice() string {
	if x != nil {
		return x.DiscountedUnitPrice
	}
	return ""
}

var File_moego_admin_platform_care_v1_annual_contract_admin_proto protoreflect.FileDescriptor

var file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
	0x73, 0x61, 0x6c, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x03, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x68, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
	0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x41, 0x0a, 0x18, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x5f, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x16, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x65, 0x72, 0x6d, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x38, 0x0a, 0x13, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x12, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x11, 0x62, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x0f, 0x62, 0x64, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x17, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x52, 0x15, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x12, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52,
	0x10, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x2c, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xbf, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0xb8, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x57, 0x0a, 0x10, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x0f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd4, 0x0b, 0x0a,
	0x0e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x51, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63,
	0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x73, 0x69, 0x67,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xa6, 0x01, 0x0a, 0x08, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61,
	0x6e, 0x1a, 0xbe, 0x05, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a,
	0x16, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x2f, 0x0a,
	0x13, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x6e, 0x0a, 0x11, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x10, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x62, 0x0a, 0x0b, 0x62, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x6e, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x64, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x76, 0x61, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x02, 0x52, 0x0b, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x61, 0x6e, 0x1a, 0x80, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x55, 0x6e, 0x69, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x32, 0xaa, 0x02, 0x0a, 0x11, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x70, 0x69, 0x12, 0x8a, 0x01, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63,
	0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6e, 0x6e, 0x75, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x8d, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x63, 0x61, 0x72, 0x65, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescOnce sync.Once
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescData = file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDesc
)

func file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescData)
	})
	return file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDescData
}

var file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_admin_platform_care_v1_annual_contract_admin_proto_goTypes = []interface{}{
	(*CreateAnnualContractParams)(nil),     // 0: moego.admin.platform_care.v1.CreateAnnualContractParams
	(*CreateAnnualContractResult)(nil),     // 1: moego.admin.platform_care.v1.CreateAnnualContractResult
	(*ListAnnualContractsParams)(nil),      // 2: moego.admin.platform_care.v1.ListAnnualContractsParams
	(*ListAnnualContractsResult)(nil),      // 3: moego.admin.platform_care.v1.ListAnnualContractsResult
	(*AnnualContract)(nil),                 // 4: moego.admin.platform_care.v1.AnnualContract
	(*AnnualContract_Metadata)(nil),        // 5: moego.admin.platform_care.v1.AnnualContract.Metadata
	(*AnnualContract_Parameters)(nil),      // 6: moego.admin.platform_care.v1.AnnualContract.Parameters
	(*AnnualContract_ProductLineItem)(nil), // 7: moego.admin.platform_care.v1.AnnualContract.ProductLineItem
	(v1.SubscriptionPlan)(0),               // 8: moego.admin.platform_sales.v1.SubscriptionPlan
	(*v2.PaginationRequest)(nil),           // 9: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),          // 10: moego.utils.v2.PaginationResponse
	(*timestamppb.Timestamp)(nil),          // 11: google.protobuf.Timestamp
}
var file_moego_admin_platform_care_v1_annual_contract_admin_proto_depIdxs = []int32{
	8,  // 0: moego.admin.platform_care.v1.CreateAnnualContractParams.subscription_plan:type_name -> moego.admin.platform_sales.v1.SubscriptionPlan
	9,  // 1: moego.admin.platform_care.v1.ListAnnualContractsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4,  // 2: moego.admin.platform_care.v1.ListAnnualContractsResult.annual_contracts:type_name -> moego.admin.platform_care.v1.AnnualContract
	10, // 3: moego.admin.platform_care.v1.ListAnnualContractsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	5,  // 4: moego.admin.platform_care.v1.AnnualContract.metadata:type_name -> moego.admin.platform_care.v1.AnnualContract.Metadata
	6,  // 5: moego.admin.platform_care.v1.AnnualContract.parameters:type_name -> moego.admin.platform_care.v1.AnnualContract.Parameters
	11, // 6: moego.admin.platform_care.v1.AnnualContract.create_time:type_name -> google.protobuf.Timestamp
	11, // 7: moego.admin.platform_care.v1.AnnualContract.update_time:type_name -> google.protobuf.Timestamp
	11, // 8: moego.admin.platform_care.v1.AnnualContract.sign_time:type_name -> google.protobuf.Timestamp
	8,  // 9: moego.admin.platform_care.v1.AnnualContract.Metadata.subscription_plan:type_name -> moego.admin.platform_sales.v1.SubscriptionPlan
	7,  // 10: moego.admin.platform_care.v1.AnnualContract.Parameters.grooming_location:type_name -> moego.admin.platform_care.v1.AnnualContract.ProductLineItem
	7,  // 11: moego.admin.platform_care.v1.AnnualContract.Parameters.bd_location:type_name -> moego.admin.platform_care.v1.AnnualContract.ProductLineItem
	7,  // 12: moego.admin.platform_care.v1.AnnualContract.Parameters.grooming_van:type_name -> moego.admin.platform_care.v1.AnnualContract.ProductLineItem
	0,  // 13: moego.admin.platform_care.v1.AnnualContractApi.CreateAnnualContract:input_type -> moego.admin.platform_care.v1.CreateAnnualContractParams
	2,  // 14: moego.admin.platform_care.v1.AnnualContractApi.ListAnnualContracts:input_type -> moego.admin.platform_care.v1.ListAnnualContractsParams
	1,  // 15: moego.admin.platform_care.v1.AnnualContractApi.CreateAnnualContract:output_type -> moego.admin.platform_care.v1.CreateAnnualContractResult
	3,  // 16: moego.admin.platform_care.v1.AnnualContractApi.ListAnnualContracts:output_type -> moego.admin.platform_care.v1.ListAnnualContractsResult
	15, // [15:17] is the sub-list for method output_type
	13, // [13:15] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_admin_platform_care_v1_annual_contract_admin_proto_init() }
func file_moego_admin_platform_care_v1_annual_contract_admin_proto_init() {
	if File_moego_admin_platform_care_v1_annual_contract_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnualContractParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAnnualContractResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnualContractsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAnnualContractsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualContract); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualContract_Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualContract_Parameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnualContract_ProductLineItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_platform_care_v1_annual_contract_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_platform_care_v1_annual_contract_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_platform_care_v1_annual_contract_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_platform_care_v1_annual_contract_admin_proto = out.File
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_rawDesc = nil
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_goTypes = nil
	file_moego_admin_platform_care_v1_annual_contract_admin_proto_depIdxs = nil
}
