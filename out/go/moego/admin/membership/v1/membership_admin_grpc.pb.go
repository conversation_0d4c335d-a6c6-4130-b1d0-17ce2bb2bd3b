// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/membership/v1/membership_admin.proto

package membershipapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MembershipServiceClient is the client API for MembershipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MembershipServiceClient interface {
	// create membership
	CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error)
	// get membership
	GetMembership(ctx context.Context, in *GetMembershipParams, opts ...grpc.CallOption) (*GetMembershipResult, error)
	// list membership
	ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error)
	// delete membership
	DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error)
}

type membershipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMembershipServiceClient(cc grpc.ClientConnInterface) MembershipServiceClient {
	return &membershipServiceClient{cc}
}

func (c *membershipServiceClient) CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error) {
	out := new(CreateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.admin.membership.v1.MembershipService/CreateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) GetMembership(ctx context.Context, in *GetMembershipParams, opts ...grpc.CallOption) (*GetMembershipResult, error) {
	out := new(GetMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.admin.membership.v1.MembershipService/GetMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error) {
	out := new(ListMembershipsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.membership.v1.MembershipService/ListMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error) {
	out := new(UpdateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.admin.membership.v1.MembershipService/UpdateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error) {
	out := new(DeleteMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.admin.membership.v1.MembershipService/DeleteMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MembershipServiceServer is the server API for MembershipService service.
// All implementations must embed UnimplementedMembershipServiceServer
// for forward compatibility
type MembershipServiceServer interface {
	// create membership
	CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error)
	// get membership
	GetMembership(context.Context, *GetMembershipParams) (*GetMembershipResult, error)
	// list membership
	ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error)
	// delete membership
	DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error)
	mustEmbedUnimplementedMembershipServiceServer()
}

// UnimplementedMembershipServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMembershipServiceServer struct {
}

func (UnimplementedMembershipServiceServer) CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMembership not implemented")
}
func (UnimplementedMembershipServiceServer) GetMembership(context.Context, *GetMembershipParams) (*GetMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMembership not implemented")
}
func (UnimplementedMembershipServiceServer) ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemberships not implemented")
}
func (UnimplementedMembershipServiceServer) UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembership not implemented")
}
func (UnimplementedMembershipServiceServer) DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMembership not implemented")
}
func (UnimplementedMembershipServiceServer) mustEmbedUnimplementedMembershipServiceServer() {}

// UnsafeMembershipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MembershipServiceServer will
// result in compilation errors.
type UnsafeMembershipServiceServer interface {
	mustEmbedUnimplementedMembershipServiceServer()
}

func RegisterMembershipServiceServer(s grpc.ServiceRegistrar, srv MembershipServiceServer) {
	s.RegisterService(&MembershipService_ServiceDesc, srv)
}

func _MembershipService_CreateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).CreateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.membership.v1.MembershipService/CreateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).CreateMembership(ctx, req.(*CreateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_GetMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).GetMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.membership.v1.MembershipService/GetMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).GetMembership(ctx, req.(*GetMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.membership.v1.MembershipService/ListMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMemberships(ctx, req.(*ListMembershipsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_UpdateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).UpdateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.membership.v1.MembershipService/UpdateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).UpdateMembership(ctx, req.(*UpdateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_DeleteMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).DeleteMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.membership.v1.MembershipService/DeleteMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).DeleteMembership(ctx, req.(*DeleteMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MembershipService_ServiceDesc is the grpc.ServiceDesc for MembershipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MembershipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.membership.v1.MembershipService",
	HandlerType: (*MembershipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMembership",
			Handler:    _MembershipService_CreateMembership_Handler,
		},
		{
			MethodName: "GetMembership",
			Handler:    _MembershipService_GetMembership_Handler,
		},
		{
			MethodName: "ListMemberships",
			Handler:    _MembershipService_ListMemberships_Handler,
		},
		{
			MethodName: "UpdateMembership",
			Handler:    _MembershipService_UpdateMembership_Handler,
		},
		{
			MethodName: "DeleteMembership",
			Handler:    _MembershipService_DeleteMembership_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/membership/v1/membership_admin.proto",
}
