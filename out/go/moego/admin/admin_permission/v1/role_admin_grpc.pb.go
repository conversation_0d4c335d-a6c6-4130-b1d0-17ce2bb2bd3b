// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/admin_permission/v1/role_admin.proto

package adminpermissionapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RoleServiceClient is the client API for RoleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoleServiceClient interface {
	// create role
	CreateRole(ctx context.Context, in *CreateRoleParams, opts ...grpc.CallOption) (*CreateRoleResult, error)
	// get roles
	DescribeRoles(ctx context.Context, in *DescribeRolesParams, opts ...grpc.CallOption) (*DescribeRolesResult, error)
	// get role detail
	GetRole(ctx context.Context, in *GetRoleParams, opts ...grpc.CallOption) (*GetRoleResult, error)
	// update role
	UpdateRole(ctx context.Context, in *UpdateRoleParams, opts ...grpc.CallOption) (*UpdateRoleResult, error)
	// delete role
	DeleteRole(ctx context.Context, in *DeleteRoleParams, opts ...grpc.CallOption) (*DeleteRoleResult, error)
	// create role perm point
	// disallowed state: the perm point or its mutually exclusive points is already in use
	CreateRolePermission(ctx context.Context, in *CreateRolePermissionParams, opts ...grpc.CallOption) (*CreateRolePermissionResult, error)
	// get role perm point list
	DescribeRolePermissions(ctx context.Context, in *DescribeRolePermissionsParams, opts ...grpc.CallOption) (*DescribeRolePermissionsResult, error)
	// update role perm point
	// disallowed state: the perm point or its mutually exclusive points is already in use by others
	UpdateRolePermission(ctx context.Context, in *UpdateRolePermissionParams, opts ...grpc.CallOption) (*UpdateRolePermissionResult, error)
	// delete rol perm point
	DeleteRolePermission(ctx context.Context, in *DeleteRolePermissionParams, opts ...grpc.CallOption) (*DeleteRolePermissionResult, error)
	// describe permissions
	DescribePermissions(ctx context.Context, in *DescribePermissionsParams, opts ...grpc.CallOption) (*DescribePermissionsResult, error)
}

type roleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRoleServiceClient(cc grpc.ClientConnInterface) RoleServiceClient {
	return &roleServiceClient{cc}
}

func (c *roleServiceClient) CreateRole(ctx context.Context, in *CreateRoleParams, opts ...grpc.CallOption) (*CreateRoleResult, error) {
	out := new(CreateRoleResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/CreateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) DescribeRoles(ctx context.Context, in *DescribeRolesParams, opts ...grpc.CallOption) (*DescribeRolesResult, error) {
	out := new(DescribeRolesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/DescribeRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) GetRole(ctx context.Context, in *GetRoleParams, opts ...grpc.CallOption) (*GetRoleResult, error) {
	out := new(GetRoleResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/GetRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) UpdateRole(ctx context.Context, in *UpdateRoleParams, opts ...grpc.CallOption) (*UpdateRoleResult, error) {
	out := new(UpdateRoleResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/UpdateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) DeleteRole(ctx context.Context, in *DeleteRoleParams, opts ...grpc.CallOption) (*DeleteRoleResult, error) {
	out := new(DeleteRoleResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/DeleteRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) CreateRolePermission(ctx context.Context, in *CreateRolePermissionParams, opts ...grpc.CallOption) (*CreateRolePermissionResult, error) {
	out := new(CreateRolePermissionResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/CreateRolePermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) DescribeRolePermissions(ctx context.Context, in *DescribeRolePermissionsParams, opts ...grpc.CallOption) (*DescribeRolePermissionsResult, error) {
	out := new(DescribeRolePermissionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/DescribeRolePermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) UpdateRolePermission(ctx context.Context, in *UpdateRolePermissionParams, opts ...grpc.CallOption) (*UpdateRolePermissionResult, error) {
	out := new(UpdateRolePermissionResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/UpdateRolePermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) DeleteRolePermission(ctx context.Context, in *DeleteRolePermissionParams, opts ...grpc.CallOption) (*DeleteRolePermissionResult, error) {
	out := new(DeleteRolePermissionResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/DeleteRolePermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roleServiceClient) DescribePermissions(ctx context.Context, in *DescribePermissionsParams, opts ...grpc.CallOption) (*DescribePermissionsResult, error) {
	out := new(DescribePermissionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.admin_permission.v1.RoleService/DescribePermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoleServiceServer is the server API for RoleService service.
// All implementations must embed UnimplementedRoleServiceServer
// for forward compatibility
type RoleServiceServer interface {
	// create role
	CreateRole(context.Context, *CreateRoleParams) (*CreateRoleResult, error)
	// get roles
	DescribeRoles(context.Context, *DescribeRolesParams) (*DescribeRolesResult, error)
	// get role detail
	GetRole(context.Context, *GetRoleParams) (*GetRoleResult, error)
	// update role
	UpdateRole(context.Context, *UpdateRoleParams) (*UpdateRoleResult, error)
	// delete role
	DeleteRole(context.Context, *DeleteRoleParams) (*DeleteRoleResult, error)
	// create role perm point
	// disallowed state: the perm point or its mutually exclusive points is already in use
	CreateRolePermission(context.Context, *CreateRolePermissionParams) (*CreateRolePermissionResult, error)
	// get role perm point list
	DescribeRolePermissions(context.Context, *DescribeRolePermissionsParams) (*DescribeRolePermissionsResult, error)
	// update role perm point
	// disallowed state: the perm point or its mutually exclusive points is already in use by others
	UpdateRolePermission(context.Context, *UpdateRolePermissionParams) (*UpdateRolePermissionResult, error)
	// delete rol perm point
	DeleteRolePermission(context.Context, *DeleteRolePermissionParams) (*DeleteRolePermissionResult, error)
	// describe permissions
	DescribePermissions(context.Context, *DescribePermissionsParams) (*DescribePermissionsResult, error)
	mustEmbedUnimplementedRoleServiceServer()
}

// UnimplementedRoleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRoleServiceServer struct {
}

func (UnimplementedRoleServiceServer) CreateRole(context.Context, *CreateRoleParams) (*CreateRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRole not implemented")
}
func (UnimplementedRoleServiceServer) DescribeRoles(context.Context, *DescribeRolesParams) (*DescribeRolesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeRoles not implemented")
}
func (UnimplementedRoleServiceServer) GetRole(context.Context, *GetRoleParams) (*GetRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRole not implemented")
}
func (UnimplementedRoleServiceServer) UpdateRole(context.Context, *UpdateRoleParams) (*UpdateRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRole not implemented")
}
func (UnimplementedRoleServiceServer) DeleteRole(context.Context, *DeleteRoleParams) (*DeleteRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRole not implemented")
}
func (UnimplementedRoleServiceServer) CreateRolePermission(context.Context, *CreateRolePermissionParams) (*CreateRolePermissionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRolePermission not implemented")
}
func (UnimplementedRoleServiceServer) DescribeRolePermissions(context.Context, *DescribeRolePermissionsParams) (*DescribeRolePermissionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeRolePermissions not implemented")
}
func (UnimplementedRoleServiceServer) UpdateRolePermission(context.Context, *UpdateRolePermissionParams) (*UpdateRolePermissionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRolePermission not implemented")
}
func (UnimplementedRoleServiceServer) DeleteRolePermission(context.Context, *DeleteRolePermissionParams) (*DeleteRolePermissionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRolePermission not implemented")
}
func (UnimplementedRoleServiceServer) DescribePermissions(context.Context, *DescribePermissionsParams) (*DescribePermissionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribePermissions not implemented")
}
func (UnimplementedRoleServiceServer) mustEmbedUnimplementedRoleServiceServer() {}

// UnsafeRoleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoleServiceServer will
// result in compilation errors.
type UnsafeRoleServiceServer interface {
	mustEmbedUnimplementedRoleServiceServer()
}

func RegisterRoleServiceServer(s grpc.ServiceRegistrar, srv RoleServiceServer) {
	s.RegisterService(&RoleService_ServiceDesc, srv)
}

func _RoleService_CreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).CreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/CreateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).CreateRole(ctx, req.(*CreateRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_DescribeRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeRolesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).DescribeRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/DescribeRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).DescribeRoles(ctx, req.(*DescribeRolesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_GetRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).GetRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/GetRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).GetRole(ctx, req.(*GetRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_UpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).UpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/UpdateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).UpdateRole(ctx, req.(*UpdateRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_DeleteRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).DeleteRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/DeleteRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).DeleteRole(ctx, req.(*DeleteRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_CreateRolePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRolePermissionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).CreateRolePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/CreateRolePermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).CreateRolePermission(ctx, req.(*CreateRolePermissionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_DescribeRolePermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeRolePermissionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).DescribeRolePermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/DescribeRolePermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).DescribeRolePermissions(ctx, req.(*DescribeRolePermissionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_UpdateRolePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRolePermissionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).UpdateRolePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/UpdateRolePermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).UpdateRolePermission(ctx, req.(*UpdateRolePermissionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_DeleteRolePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRolePermissionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).DeleteRolePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/DeleteRolePermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).DeleteRolePermission(ctx, req.(*DeleteRolePermissionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoleService_DescribePermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribePermissionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoleServiceServer).DescribePermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.admin_permission.v1.RoleService/DescribePermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoleServiceServer).DescribePermissions(ctx, req.(*DescribePermissionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// RoleService_ServiceDesc is the grpc.ServiceDesc for RoleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.admin_permission.v1.RoleService",
	HandlerType: (*RoleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRole",
			Handler:    _RoleService_CreateRole_Handler,
		},
		{
			MethodName: "DescribeRoles",
			Handler:    _RoleService_DescribeRoles_Handler,
		},
		{
			MethodName: "GetRole",
			Handler:    _RoleService_GetRole_Handler,
		},
		{
			MethodName: "UpdateRole",
			Handler:    _RoleService_UpdateRole_Handler,
		},
		{
			MethodName: "DeleteRole",
			Handler:    _RoleService_DeleteRole_Handler,
		},
		{
			MethodName: "CreateRolePermission",
			Handler:    _RoleService_CreateRolePermission_Handler,
		},
		{
			MethodName: "DescribeRolePermissions",
			Handler:    _RoleService_DescribeRolePermissions_Handler,
		},
		{
			MethodName: "UpdateRolePermission",
			Handler:    _RoleService_UpdateRolePermission_Handler,
		},
		{
			MethodName: "DeleteRolePermission",
			Handler:    _RoleService_DeleteRolePermission_Handler,
		},
		{
			MethodName: "DescribePermissions",
			Handler:    _RoleService_DescribePermissions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/admin_permission/v1/role_admin.proto",
}
