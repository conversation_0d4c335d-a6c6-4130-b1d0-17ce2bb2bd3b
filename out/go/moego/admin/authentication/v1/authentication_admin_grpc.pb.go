// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/authentication/v1/authentication_admin.proto

package authenticationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AuthenticationServiceClient is the client API for AuthenticationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Deprecated: Do not use.
type AuthenticationServiceClient interface {
	// GetAuthenticationAccountInfo
	GetAccountInfo(ctx context.Context, in *GetAccountInfoParams, opts ...grpc.CallOption) (*GetAccountInfoResult, error)
	// GetLoginUrl
	GetLoginUrl(ctx context.Context, in *GetLoginUrlParams, opts ...grpc.CallOption) (*GetLoginUrlResult, error)
	// Login
	Login(ctx context.Context, in *LoginParams, opts ...grpc.CallOption) (*LoginResult, error)
}

type authenticationServiceClient struct {
	cc grpc.ClientConnInterface
}

// Deprecated: Do not use.
func NewAuthenticationServiceClient(cc grpc.ClientConnInterface) AuthenticationServiceClient {
	return &authenticationServiceClient{cc}
}

func (c *authenticationServiceClient) GetAccountInfo(ctx context.Context, in *GetAccountInfoParams, opts ...grpc.CallOption) (*GetAccountInfoResult, error) {
	out := new(GetAccountInfoResult)
	err := c.cc.Invoke(ctx, "/moego.admin.authentication.v1.AuthenticationService/GetAccountInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authenticationServiceClient) GetLoginUrl(ctx context.Context, in *GetLoginUrlParams, opts ...grpc.CallOption) (*GetLoginUrlResult, error) {
	out := new(GetLoginUrlResult)
	err := c.cc.Invoke(ctx, "/moego.admin.authentication.v1.AuthenticationService/GetLoginUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authenticationServiceClient) Login(ctx context.Context, in *LoginParams, opts ...grpc.CallOption) (*LoginResult, error) {
	out := new(LoginResult)
	err := c.cc.Invoke(ctx, "/moego.admin.authentication.v1.AuthenticationService/Login", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthenticationServiceServer is the server API for AuthenticationService service.
// All implementations must embed UnimplementedAuthenticationServiceServer
// for forward compatibility
//
// Deprecated: Do not use.
type AuthenticationServiceServer interface {
	// GetAuthenticationAccountInfo
	GetAccountInfo(context.Context, *GetAccountInfoParams) (*GetAccountInfoResult, error)
	// GetLoginUrl
	GetLoginUrl(context.Context, *GetLoginUrlParams) (*GetLoginUrlResult, error)
	// Login
	Login(context.Context, *LoginParams) (*LoginResult, error)
	mustEmbedUnimplementedAuthenticationServiceServer()
}

// UnimplementedAuthenticationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAuthenticationServiceServer struct {
}

func (UnimplementedAuthenticationServiceServer) GetAccountInfo(context.Context, *GetAccountInfoParams) (*GetAccountInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountInfo not implemented")
}
func (UnimplementedAuthenticationServiceServer) GetLoginUrl(context.Context, *GetLoginUrlParams) (*GetLoginUrlResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoginUrl not implemented")
}
func (UnimplementedAuthenticationServiceServer) Login(context.Context, *LoginParams) (*LoginResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAuthenticationServiceServer) mustEmbedUnimplementedAuthenticationServiceServer() {}

// UnsafeAuthenticationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthenticationServiceServer will
// result in compilation errors.
type UnsafeAuthenticationServiceServer interface {
	mustEmbedUnimplementedAuthenticationServiceServer()
}

// Deprecated: Do not use.
func RegisterAuthenticationServiceServer(s grpc.ServiceRegistrar, srv AuthenticationServiceServer) {
	s.RegisterService(&AuthenticationService_ServiceDesc, srv)
}

func _AuthenticationService_GetAccountInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthenticationServiceServer).GetAccountInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.authentication.v1.AuthenticationService/GetAccountInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthenticationServiceServer).GetAccountInfo(ctx, req.(*GetAccountInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthenticationService_GetLoginUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoginUrlParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthenticationServiceServer).GetLoginUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.authentication.v1.AuthenticationService/GetLoginUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthenticationServiceServer).GetLoginUrl(ctx, req.(*GetLoginUrlParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthenticationService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthenticationServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.authentication.v1.AuthenticationService/Login",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthenticationServiceServer).Login(ctx, req.(*LoginParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthenticationService_ServiceDesc is the grpc.ServiceDesc for AuthenticationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthenticationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.authentication.v1.AuthenticationService",
	HandlerType: (*AuthenticationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccountInfo",
			Handler:    _AuthenticationService_GetAccountInfo_Handler,
		},
		{
			MethodName: "GetLoginUrl",
			Handler:    _AuthenticationService_GetLoginUrl_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _AuthenticationService_Login_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/authentication/v1/authentication_admin.proto",
}
