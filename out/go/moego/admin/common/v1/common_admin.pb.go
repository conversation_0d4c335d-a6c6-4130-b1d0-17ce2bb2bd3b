// @since 2023-07-06 14:28:04
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/common/v1/common_admin.proto

package commonapipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// describe enums params
type DescribeEnumsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DescribeEnumsParams) Reset() {
	*x = DescribeEnumsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEnumsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEnumsParams) ProtoMessage() {}

func (x *DescribeEnumsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEnumsParams.ProtoReflect.Descriptor instead.
func (*DescribeEnumsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{0}
}

// describe enums result value
type EnumOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enum value
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	// enum label
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *EnumOption) Reset() {
	*x = EnumOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumOption) ProtoMessage() {}

func (x *EnumOption) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumOption.ProtoReflect.Descriptor instead.
func (*EnumOption) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{1}
}

func (x *EnumOption) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *EnumOption) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

// status option
type StatusOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status icon
	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// status label
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// status color
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	// status value
	Value int32 `protobuf:"varint,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StatusOption) Reset() {
	*x = StatusOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusOption) ProtoMessage() {}

func (x *StatusOption) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusOption.ProtoReflect.Descriptor instead.
func (*StatusOption) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{2}
}

func (x *StatusOption) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *StatusOption) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *StatusOption) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *StatusOption) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

// enum model
type EnumModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enum name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// enum options
	Options []*EnumOption `protobuf:"bytes,2,rep,name=options,proto3" json:"options,omitempty"`
	// label map
	LabelMap map[int32]string `protobuf:"bytes,3,rep,name=label_map,json=labelMap,proto3" json:"label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// status map
	StatusMap map[int32]*StatusOption `protobuf:"bytes,4,rep,name=status_map,json=statusMap,proto3" json:"status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *EnumModel) Reset() {
	*x = EnumModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumModel) ProtoMessage() {}

func (x *EnumModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumModel.ProtoReflect.Descriptor instead.
func (*EnumModel) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{3}
}

func (x *EnumModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnumModel) GetOptions() []*EnumOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *EnumModel) GetLabelMap() map[int32]string {
	if x != nil {
		return x.LabelMap
	}
	return nil
}

func (x *EnumModel) GetStatusMap() map[int32]*StatusOption {
	if x != nil {
		return x.StatusMap
	}
	return nil
}

// describe enums result
type DescribeEnumsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message module
	Message *DescribeEnumsResult_MessageModule `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// grooming module
	Grooming *DescribeEnumsResult_GroomingModule `protobuf:"bytes,2,opt,name=grooming,proto3" json:"grooming,omitempty"`
	// common module
	Common *DescribeEnumsResult_CommonModule `protobuf:"bytes,3,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *DescribeEnumsResult) Reset() {
	*x = DescribeEnumsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEnumsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEnumsResult) ProtoMessage() {}

func (x *DescribeEnumsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEnumsResult.ProtoReflect.Descriptor instead.
func (*DescribeEnumsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeEnumsResult) GetMessage() *DescribeEnumsResult_MessageModule {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *DescribeEnumsResult) GetGrooming() *DescribeEnumsResult_GroomingModule {
	if x != nil {
		return x.Grooming
	}
	return nil
}

func (x *DescribeEnumsResult) GetCommon() *DescribeEnumsResult_CommonModule {
	if x != nil {
		return x.Common
	}
	return nil
}

// message module
type DescribeEnumsResult_MessageModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target type
	TargetType *EnumModel `protobuf:"bytes,1,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`
	// auto message type
	AutoMessageType *EnumModel `protobuf:"bytes,2,opt,name=auto_message_type,json=autoMessageType,proto3" json:"auto_message_type,omitempty"`
	// source
	Source *EnumModel `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	// reminder type
	ReminderType *EnumModel `protobuf:"bytes,4,opt,name=reminder_type,json=reminderType,proto3" json:"reminder_type,omitempty"`
	// send status
	SendStatus *EnumModel `protobuf:"bytes,5,opt,name=send_status,json=sendStatus,proto3" json:"send_status,omitempty"`
	// message type
	MessageType *EnumModel `protobuf:"bytes,6,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	// sender type
	SenderType *EnumModel `protobuf:"bytes,7,opt,name=sender_type,json=senderType,proto3" json:"sender_type,omitempty"`
	// method
	Method *EnumModel `protobuf:"bytes,8,opt,name=method,proto3" json:"method,omitempty"`
}

func (x *DescribeEnumsResult_MessageModule) Reset() {
	*x = DescribeEnumsResult_MessageModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEnumsResult_MessageModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEnumsResult_MessageModule) ProtoMessage() {}

func (x *DescribeEnumsResult_MessageModule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEnumsResult_MessageModule.ProtoReflect.Descriptor instead.
func (*DescribeEnumsResult_MessageModule) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DescribeEnumsResult_MessageModule) GetTargetType() *EnumModel {
	if x != nil {
		return x.TargetType
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetAutoMessageType() *EnumModel {
	if x != nil {
		return x.AutoMessageType
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetSource() *EnumModel {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetReminderType() *EnumModel {
	if x != nil {
		return x.ReminderType
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetSendStatus() *EnumModel {
	if x != nil {
		return x.SendStatus
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetMessageType() *EnumModel {
	if x != nil {
		return x.MessageType
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetSenderType() *EnumModel {
	if x != nil {
		return x.SenderType
	}
	return nil
}

func (x *DescribeEnumsResult_MessageModule) GetMethod() *EnumModel {
	if x != nil {
		return x.Method
	}
	return nil
}

// grooming module
type DescribeEnumsResult_GroomingModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment status
	AppointmentStatus *EnumModel `protobuf:"bytes,1,opt,name=appointment_status,json=appointmentStatus,proto3" json:"appointment_status,omitempty"`
	// source
	Source *EnumModel `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *DescribeEnumsResult_GroomingModule) Reset() {
	*x = DescribeEnumsResult_GroomingModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEnumsResult_GroomingModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEnumsResult_GroomingModule) ProtoMessage() {}

func (x *DescribeEnumsResult_GroomingModule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEnumsResult_GroomingModule.ProtoReflect.Descriptor instead.
func (*DescribeEnumsResult_GroomingModule) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{4, 1}
}

func (x *DescribeEnumsResult_GroomingModule) GetAppointmentStatus() *EnumModel {
	if x != nil {
		return x.AppointmentStatus
	}
	return nil
}

func (x *DescribeEnumsResult_GroomingModule) GetSource() *EnumModel {
	if x != nil {
		return x.Source
	}
	return nil
}

// common module
type DescribeEnumsResult_CommonModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common status
	Status *EnumModel `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// bool status
	Bool *EnumModel `protobuf:"bytes,2,opt,name=bool,proto3" json:"bool,omitempty"`
}

func (x *DescribeEnumsResult_CommonModule) Reset() {
	*x = DescribeEnumsResult_CommonModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEnumsResult_CommonModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEnumsResult_CommonModule) ProtoMessage() {}

func (x *DescribeEnumsResult_CommonModule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_common_v1_common_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEnumsResult_CommonModule.ProtoReflect.Descriptor instead.
func (*DescribeEnumsResult_CommonModule) Descriptor() ([]byte, []int) {
	return file_moego_admin_common_v1_common_admin_proto_rawDescGZIP(), []int{4, 2}
}

func (x *DescribeEnumsResult_CommonModule) GetStatus() *EnumModel {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DescribeEnumsResult_CommonModule) GetBool() *EnumModel {
	if x != nil {
		return x.Bool
	}
	return nil
}

var File_moego_admin_common_v1_common_admin_proto protoreflect.FileDescriptor

var file_moego_admin_common_v1_common_admin_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x38, 0x0a, 0x0a, 0x45, 0x6e, 0x75, 0x6d,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x22, 0x64, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x99, 0x03, 0x0a, 0x09, 0x45, 0x6e, 0x75,
	0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4b, 0x0a, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x4d, 0x61, 0x70, 0x12, 0x4e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a, 0x0d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x61, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xd8, 0x08, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x55, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x4f, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0xa6, 0x04, 0x0a, 0x0d, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a,
	0x11, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c,
	0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x43, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x1a, 0x9b, 0x01, 0x0a, 0x0e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a,
	0x7e, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x04, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x32,
	0x78, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x67, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x73, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x79, 0x0a, 0x1d, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_common_v1_common_admin_proto_rawDescOnce sync.Once
	file_moego_admin_common_v1_common_admin_proto_rawDescData = file_moego_admin_common_v1_common_admin_proto_rawDesc
)

func file_moego_admin_common_v1_common_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_common_v1_common_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_common_v1_common_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_common_v1_common_admin_proto_rawDescData)
	})
	return file_moego_admin_common_v1_common_admin_proto_rawDescData
}

var file_moego_admin_common_v1_common_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_admin_common_v1_common_admin_proto_goTypes = []interface{}{
	(*DescribeEnumsParams)(nil), // 0: moego.admin.common.v1.DescribeEnumsParams
	(*EnumOption)(nil),          // 1: moego.admin.common.v1.EnumOption
	(*StatusOption)(nil),        // 2: moego.admin.common.v1.StatusOption
	(*EnumModel)(nil),           // 3: moego.admin.common.v1.EnumModel
	(*DescribeEnumsResult)(nil), // 4: moego.admin.common.v1.DescribeEnumsResult
	nil,                         // 5: moego.admin.common.v1.EnumModel.LabelMapEntry
	nil,                         // 6: moego.admin.common.v1.EnumModel.StatusMapEntry
	(*DescribeEnumsResult_MessageModule)(nil),  // 7: moego.admin.common.v1.DescribeEnumsResult.MessageModule
	(*DescribeEnumsResult_GroomingModule)(nil), // 8: moego.admin.common.v1.DescribeEnumsResult.GroomingModule
	(*DescribeEnumsResult_CommonModule)(nil),   // 9: moego.admin.common.v1.DescribeEnumsResult.CommonModule
}
var file_moego_admin_common_v1_common_admin_proto_depIdxs = []int32{
	1,  // 0: moego.admin.common.v1.EnumModel.options:type_name -> moego.admin.common.v1.EnumOption
	5,  // 1: moego.admin.common.v1.EnumModel.label_map:type_name -> moego.admin.common.v1.EnumModel.LabelMapEntry
	6,  // 2: moego.admin.common.v1.EnumModel.status_map:type_name -> moego.admin.common.v1.EnumModel.StatusMapEntry
	7,  // 3: moego.admin.common.v1.DescribeEnumsResult.message:type_name -> moego.admin.common.v1.DescribeEnumsResult.MessageModule
	8,  // 4: moego.admin.common.v1.DescribeEnumsResult.grooming:type_name -> moego.admin.common.v1.DescribeEnumsResult.GroomingModule
	9,  // 5: moego.admin.common.v1.DescribeEnumsResult.common:type_name -> moego.admin.common.v1.DescribeEnumsResult.CommonModule
	2,  // 6: moego.admin.common.v1.EnumModel.StatusMapEntry.value:type_name -> moego.admin.common.v1.StatusOption
	3,  // 7: moego.admin.common.v1.DescribeEnumsResult.MessageModule.target_type:type_name -> moego.admin.common.v1.EnumModel
	3,  // 8: moego.admin.common.v1.DescribeEnumsResult.MessageModule.auto_message_type:type_name -> moego.admin.common.v1.EnumModel
	3,  // 9: moego.admin.common.v1.DescribeEnumsResult.MessageModule.source:type_name -> moego.admin.common.v1.EnumModel
	3,  // 10: moego.admin.common.v1.DescribeEnumsResult.MessageModule.reminder_type:type_name -> moego.admin.common.v1.EnumModel
	3,  // 11: moego.admin.common.v1.DescribeEnumsResult.MessageModule.send_status:type_name -> moego.admin.common.v1.EnumModel
	3,  // 12: moego.admin.common.v1.DescribeEnumsResult.MessageModule.message_type:type_name -> moego.admin.common.v1.EnumModel
	3,  // 13: moego.admin.common.v1.DescribeEnumsResult.MessageModule.sender_type:type_name -> moego.admin.common.v1.EnumModel
	3,  // 14: moego.admin.common.v1.DescribeEnumsResult.MessageModule.method:type_name -> moego.admin.common.v1.EnumModel
	3,  // 15: moego.admin.common.v1.DescribeEnumsResult.GroomingModule.appointment_status:type_name -> moego.admin.common.v1.EnumModel
	3,  // 16: moego.admin.common.v1.DescribeEnumsResult.GroomingModule.source:type_name -> moego.admin.common.v1.EnumModel
	3,  // 17: moego.admin.common.v1.DescribeEnumsResult.CommonModule.status:type_name -> moego.admin.common.v1.EnumModel
	3,  // 18: moego.admin.common.v1.DescribeEnumsResult.CommonModule.bool:type_name -> moego.admin.common.v1.EnumModel
	0,  // 19: moego.admin.common.v1.CommonService.DescribeEnums:input_type -> moego.admin.common.v1.DescribeEnumsParams
	4,  // 20: moego.admin.common.v1.CommonService.DescribeEnums:output_type -> moego.admin.common.v1.DescribeEnumsResult
	20, // [20:21] is the sub-list for method output_type
	19, // [19:20] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_admin_common_v1_common_admin_proto_init() }
func file_moego_admin_common_v1_common_admin_proto_init() {
	if File_moego_admin_common_v1_common_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_common_v1_common_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEnumsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnumOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnumModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEnumsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEnumsResult_MessageModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEnumsResult_GroomingModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_common_v1_common_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEnumsResult_CommonModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_common_v1_common_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_common_v1_common_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_common_v1_common_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_common_v1_common_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_common_v1_common_admin_proto = out.File
	file_moego_admin_common_v1_common_admin_proto_rawDesc = nil
	file_moego_admin_common_v1_common_admin_proto_goTypes = nil
	file_moego_admin_common_v1_common_admin_proto_depIdxs = nil
}
