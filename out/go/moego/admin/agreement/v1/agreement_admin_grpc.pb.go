// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/agreement/v1/agreement_admin.proto

package agreementapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementServiceClient is the client API for AgreementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementServiceClient interface {
	// get agreement
	GetAgreement(ctx context.Context, in *GetAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error)
	// get agreement list
	GetAgreementList(ctx context.Context, in *GetAgreementListParams, opts ...grpc.CallOption) (*GetAgreementListResult, error)
	// delete agreement
	DeleteAgreement(ctx context.Context, in *DeleteAgreementParams, opts ...grpc.CallOption) (*DeleteAgreementResult, error)
	// update a agreement
	UpdateAgreement(ctx context.Context, in *UpdateAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error)
	// add a agreement
	AddAgreement(ctx context.Context, in *AddAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error)
}

type agreementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementServiceClient(cc grpc.ClientConnInterface) AgreementServiceClient {
	return &agreementServiceClient{cc}
}

func (c *agreementServiceClient) GetAgreement(ctx context.Context, in *GetAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error) {
	out := new(v1.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementService/GetAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) GetAgreementList(ctx context.Context, in *GetAgreementListParams, opts ...grpc.CallOption) (*GetAgreementListResult, error) {
	out := new(GetAgreementListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementService/GetAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) DeleteAgreement(ctx context.Context, in *DeleteAgreementParams, opts ...grpc.CallOption) (*DeleteAgreementResult, error) {
	out := new(DeleteAgreementResult)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementService/DeleteAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) UpdateAgreement(ctx context.Context, in *UpdateAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error) {
	out := new(v1.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementService/UpdateAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) AddAgreement(ctx context.Context, in *AddAgreementParams, opts ...grpc.CallOption) (*v1.AgreementModel, error) {
	out := new(v1.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementService/AddAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementServiceServer is the server API for AgreementService service.
// All implementations must embed UnimplementedAgreementServiceServer
// for forward compatibility
type AgreementServiceServer interface {
	// get agreement
	GetAgreement(context.Context, *GetAgreementParams) (*v1.AgreementModel, error)
	// get agreement list
	GetAgreementList(context.Context, *GetAgreementListParams) (*GetAgreementListResult, error)
	// delete agreement
	DeleteAgreement(context.Context, *DeleteAgreementParams) (*DeleteAgreementResult, error)
	// update a agreement
	UpdateAgreement(context.Context, *UpdateAgreementParams) (*v1.AgreementModel, error)
	// add a agreement
	AddAgreement(context.Context, *AddAgreementParams) (*v1.AgreementModel, error)
	mustEmbedUnimplementedAgreementServiceServer()
}

// UnimplementedAgreementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementServiceServer struct {
}

func (UnimplementedAgreementServiceServer) GetAgreement(context.Context, *GetAgreementParams) (*v1.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) GetAgreementList(context.Context, *GetAgreementListParams) (*GetAgreementListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreementList not implemented")
}
func (UnimplementedAgreementServiceServer) DeleteAgreement(context.Context, *DeleteAgreementParams) (*DeleteAgreementResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) UpdateAgreement(context.Context, *UpdateAgreementParams) (*v1.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) AddAgreement(context.Context, *AddAgreementParams) (*v1.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) mustEmbedUnimplementedAgreementServiceServer() {}

// UnsafeAgreementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementServiceServer will
// result in compilation errors.
type UnsafeAgreementServiceServer interface {
	mustEmbedUnimplementedAgreementServiceServer()
}

func RegisterAgreementServiceServer(s grpc.ServiceRegistrar, srv AgreementServiceServer) {
	s.RegisterService(&AgreementService_ServiceDesc, srv)
}

func _AgreementService_GetAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgreementParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementService/GetAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreement(ctx, req.(*GetAgreementParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_GetAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgreementListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementService/GetAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreementList(ctx, req.(*GetAgreementListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_DeleteAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAgreementParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).DeleteAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementService/DeleteAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).DeleteAgreement(ctx, req.(*DeleteAgreementParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_UpdateAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAgreementParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).UpdateAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementService/UpdateAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).UpdateAgreement(ctx, req.(*UpdateAgreementParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_AddAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAgreementParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).AddAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementService/AddAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).AddAgreement(ctx, req.(*AddAgreementParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementService_ServiceDesc is the grpc.ServiceDesc for AgreementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.agreement.v1.AgreementService",
	HandlerType: (*AgreementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAgreement",
			Handler:    _AgreementService_GetAgreement_Handler,
		},
		{
			MethodName: "GetAgreementList",
			Handler:    _AgreementService_GetAgreementList_Handler,
		},
		{
			MethodName: "DeleteAgreement",
			Handler:    _AgreementService_DeleteAgreement_Handler,
		},
		{
			MethodName: "UpdateAgreement",
			Handler:    _AgreementService_UpdateAgreement_Handler,
		},
		{
			MethodName: "AddAgreement",
			Handler:    _AgreementService_AddAgreement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/agreement/v1/agreement_admin.proto",
}
