// @since 2-23-11-14
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/agreement/v1/agreement_record_admin.proto

package agreementapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get agreement record list for customer Params
type GetRecordListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// agreement id
	AgreementId *int64 `protobuf:"varint,3,opt,name=agreement_id,json=agreementId,proto3,oneof" json:"agreement_id,omitempty"`
	// target id by service type
	TargetId *int64 `protobuf:"varint,4,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`
	// associated service type: see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,5,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
	// status: normal, deleted
	Status *v1.Status `protobuf:"varint,6,opt,name=status,proto3,enum=moego.utils.v1.Status,oneof" json:"status,omitempty"`
	// signed status
	SignedStatus *v11.SignedStatus `protobuf:"varint,7,opt,name=signed_status,json=signedStatus,proto3,enum=moego.models.agreement.v1.SignedStatus,oneof" json:"signed_status,omitempty"`
	// signed type
	SignedType *v11.SignedType `protobuf:"varint,8,opt,name=signed_type,json=signedType,proto3,enum=moego.models.agreement.v1.SignedType,oneof" json:"signed_type,omitempty"`
	// source type
	SourceType *v11.SourceType `protobuf:"varint,9,opt,name=source_type,json=sourceType,proto3,enum=moego.models.agreement.v1.SourceType,oneof" json:"source_type,omitempty"`
	// the page info
	Pagination *v2.PaginationRequest `protobuf:"bytes,10,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetRecordListParams) Reset() {
	*x = GetRecordListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordListParams) ProtoMessage() {}

func (x *GetRecordListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordListParams.ProtoReflect.Descriptor instead.
func (*GetRecordListParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP(), []int{0}
}

func (x *GetRecordListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRecordListParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *GetRecordListParams) GetAgreementId() int64 {
	if x != nil && x.AgreementId != nil {
		return *x.AgreementId
	}
	return 0
}

func (x *GetRecordListParams) GetTargetId() int64 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *GetRecordListParams) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

func (x *GetRecordListParams) GetStatus() v1.Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Status(0)
}

func (x *GetRecordListParams) GetSignedStatus() v11.SignedStatus {
	if x != nil && x.SignedStatus != nil {
		return *x.SignedStatus
	}
	return v11.SignedStatus(0)
}

func (x *GetRecordListParams) GetSignedType() v11.SignedType {
	if x != nil && x.SignedType != nil {
		return *x.SignedType
	}
	return v11.SignedType(0)
}

func (x *GetRecordListParams) GetSourceType() v11.SourceType {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return v11.SourceType(0)
}

func (x *GetRecordListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// query record simple view Result
type GetRecordListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// record list
	AgreementRecordSimpleView []*v11.AgreementRecordSimpleView `protobuf:"bytes,1,rep,name=agreement_record_simple_view,json=agreementRecordSimpleView,proto3" json:"agreement_record_simple_view,omitempty"`
	// page info
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// signed status map
	SignedStatusMap map[int32]string `protobuf:"bytes,3,rep,name=signed_status_map,json=signedStatusMap,proto3" json:"signed_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// service type map
	ServiceTypeMap map[int32]string `protobuf:"bytes,4,rep,name=service_type_map,json=serviceTypeMap,proto3" json:"service_type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// agreement map, key is agreement id, value: agreement title
	AgreementMap map[int64]string `protobuf:"bytes,5,rep,name=agreement_map,json=agreementMap,proto3" json:"agreement_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// status type map
	SignedTypeMap map[int32]string `protobuf:"bytes,6,rep,name=signed_type_map,json=signedTypeMap,proto3" json:"signed_type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// status type map
	SourceTypeMap map[int32]string `protobuf:"bytes,7,rep,name=source_type_map,json=sourceTypeMap,proto3" json:"source_type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRecordListResult) Reset() {
	*x = GetRecordListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordListResult) ProtoMessage() {}

func (x *GetRecordListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordListResult.ProtoReflect.Descriptor instead.
func (*GetRecordListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP(), []int{1}
}

func (x *GetRecordListResult) GetAgreementRecordSimpleView() []*v11.AgreementRecordSimpleView {
	if x != nil {
		return x.AgreementRecordSimpleView
	}
	return nil
}

func (x *GetRecordListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetRecordListResult) GetSignedStatusMap() map[int32]string {
	if x != nil {
		return x.SignedStatusMap
	}
	return nil
}

func (x *GetRecordListResult) GetServiceTypeMap() map[int32]string {
	if x != nil {
		return x.ServiceTypeMap
	}
	return nil
}

func (x *GetRecordListResult) GetAgreementMap() map[int64]string {
	if x != nil {
		return x.AgreementMap
	}
	return nil
}

func (x *GetRecordListResult) GetSignedTypeMap() map[int32]string {
	if x != nil {
		return x.SignedTypeMap
	}
	return nil
}

func (x *GetRecordListResult) GetSourceTypeMap() map[int32]string {
	if x != nil {
		return x.SourceTypeMap
	}
	return nil
}

// get record input
type GetRecordParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// agreement record id
	Id *int64 `protobuf:"varint,2,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// agreement record uuid
	Uuid *string `protobuf:"bytes,3,opt,name=uuid,proto3,oneof" json:"uuid,omitempty"`
}

func (x *GetRecordParams) Reset() {
	*x = GetRecordParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordParams) ProtoMessage() {}

func (x *GetRecordParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordParams.ProtoReflect.Descriptor instead.
func (*GetRecordParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP(), []int{2}
}

func (x *GetRecordParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetRecordParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetRecordParams) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

// GetRecentSignedAgreementListParams
type GetRecentSignedAgreementListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// sign type
	SignedType *v11.SignedType `protobuf:"varint,3,opt,name=signed_type,json=signedType,proto3,enum=moego.models.agreement.v1.SignedType,oneof" json:"signed_type,omitempty"`
	// associated service type: see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
}

func (x *GetRecentSignedAgreementListParams) Reset() {
	*x = GetRecentSignedAgreementListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecentSignedAgreementListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecentSignedAgreementListParams) ProtoMessage() {}

func (x *GetRecentSignedAgreementListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecentSignedAgreementListParams.ProtoReflect.Descriptor instead.
func (*GetRecentSignedAgreementListParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP(), []int{3}
}

func (x *GetRecentSignedAgreementListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRecentSignedAgreementListParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetRecentSignedAgreementListParams) GetSignedType() v11.SignedType {
	if x != nil && x.SignedType != nil {
		return *x.SignedType
	}
	return v11.SignedType(0)
}

func (x *GetRecentSignedAgreementListParams) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

// GetRecentSignedAgreementListResult
type GetRecentSignedAgreementListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement with recent signed record list
	AgreementRecentView []*v11.AgreementWithRecentRecordsView `protobuf:"bytes,1,rep,name=agreement_recent_view,json=agreementRecentView,proto3" json:"agreement_recent_view,omitempty"`
}

func (x *GetRecentSignedAgreementListResult) Reset() {
	*x = GetRecentSignedAgreementListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecentSignedAgreementListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecentSignedAgreementListResult) ProtoMessage() {}

func (x *GetRecentSignedAgreementListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecentSignedAgreementListResult.ProtoReflect.Descriptor instead.
func (*GetRecentSignedAgreementListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP(), []int{4}
}

func (x *GetRecentSignedAgreementListResult) GetAgreementRecentView() []*v11.AgreementWithRecentRecordsView {
	if x != nil {
		return x.AgreementRecentView
	}
	return nil
}

var File_moego_admin_agreement_v1_agreement_record_admin_proto protoreflect.FileDescriptor

var file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xec, 0x05, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02,
	0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x5d, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x05, 0x52,
	0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x57, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x06, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x48, 0x07, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xb3, 0x08, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x75, 0x0a, 0x1c, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x19, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6e, 0x0a, 0x11, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x6b, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x4d, 0x61, 0x70, 0x12, 0x64, 0x0a, 0x0d, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x68, 0x0a, 0x0f, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x4d, 0x61, 0x70, 0x12, 0x68, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x42, 0x0a,
	0x14, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x41, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a, 0x11, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9c, 0x01, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x01, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72, 0x10, 0x32, 0x0e, 0x5e,
	0x5b, 0x30, 0x2d, 0x39, 0x61, 0x2d, 0x66, 0x5d, 0x7b, 0x33, 0x32, 0x7d, 0x24, 0x48, 0x02, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x22, 0x94, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x57, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22,
	0x93, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x32, 0x85, 0x04, 0x0a, 0x16, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x9a, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x09,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x76, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x42, 0x82, 0x01,
	0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescOnce sync.Once
	file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescData = file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDesc
)

func file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescData)
	})
	return file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDescData
}

var file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_admin_agreement_v1_agreement_record_admin_proto_goTypes = []interface{}{
	(*GetRecordListParams)(nil),                // 0: moego.admin.agreement.v1.GetRecordListParams
	(*GetRecordListResult)(nil),                // 1: moego.admin.agreement.v1.GetRecordListResult
	(*GetRecordParams)(nil),                    // 2: moego.admin.agreement.v1.GetRecordParams
	(*GetRecentSignedAgreementListParams)(nil), // 3: moego.admin.agreement.v1.GetRecentSignedAgreementListParams
	(*GetRecentSignedAgreementListResult)(nil), // 4: moego.admin.agreement.v1.GetRecentSignedAgreementListResult
	nil,                                   // 5: moego.admin.agreement.v1.GetRecordListResult.SignedStatusMapEntry
	nil,                                   // 6: moego.admin.agreement.v1.GetRecordListResult.ServiceTypeMapEntry
	nil,                                   // 7: moego.admin.agreement.v1.GetRecordListResult.AgreementMapEntry
	nil,                                   // 8: moego.admin.agreement.v1.GetRecordListResult.SignedTypeMapEntry
	nil,                                   // 9: moego.admin.agreement.v1.GetRecordListResult.SourceTypeMapEntry
	(v1.Status)(0),                        // 10: moego.utils.v1.Status
	(v11.SignedStatus)(0),                 // 11: moego.models.agreement.v1.SignedStatus
	(v11.SignedType)(0),                   // 12: moego.models.agreement.v1.SignedType
	(v11.SourceType)(0),                   // 13: moego.models.agreement.v1.SourceType
	(*v2.PaginationRequest)(nil),          // 14: moego.utils.v2.PaginationRequest
	(*v11.AgreementRecordSimpleView)(nil), // 15: moego.models.agreement.v1.AgreementRecordSimpleView
	(*v2.PaginationResponse)(nil),         // 16: moego.utils.v2.PaginationResponse
	(*v11.AgreementWithRecentRecordsView)(nil), // 17: moego.models.agreement.v1.AgreementWithRecentRecordsView
	(*v11.AgreementRecordModel)(nil),           // 18: moego.models.agreement.v1.AgreementRecordModel
}
var file_moego_admin_agreement_v1_agreement_record_admin_proto_depIdxs = []int32{
	10, // 0: moego.admin.agreement.v1.GetRecordListParams.status:type_name -> moego.utils.v1.Status
	11, // 1: moego.admin.agreement.v1.GetRecordListParams.signed_status:type_name -> moego.models.agreement.v1.SignedStatus
	12, // 2: moego.admin.agreement.v1.GetRecordListParams.signed_type:type_name -> moego.models.agreement.v1.SignedType
	13, // 3: moego.admin.agreement.v1.GetRecordListParams.source_type:type_name -> moego.models.agreement.v1.SourceType
	14, // 4: moego.admin.agreement.v1.GetRecordListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	15, // 5: moego.admin.agreement.v1.GetRecordListResult.agreement_record_simple_view:type_name -> moego.models.agreement.v1.AgreementRecordSimpleView
	16, // 6: moego.admin.agreement.v1.GetRecordListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	5,  // 7: moego.admin.agreement.v1.GetRecordListResult.signed_status_map:type_name -> moego.admin.agreement.v1.GetRecordListResult.SignedStatusMapEntry
	6,  // 8: moego.admin.agreement.v1.GetRecordListResult.service_type_map:type_name -> moego.admin.agreement.v1.GetRecordListResult.ServiceTypeMapEntry
	7,  // 9: moego.admin.agreement.v1.GetRecordListResult.agreement_map:type_name -> moego.admin.agreement.v1.GetRecordListResult.AgreementMapEntry
	8,  // 10: moego.admin.agreement.v1.GetRecordListResult.signed_type_map:type_name -> moego.admin.agreement.v1.GetRecordListResult.SignedTypeMapEntry
	9,  // 11: moego.admin.agreement.v1.GetRecordListResult.source_type_map:type_name -> moego.admin.agreement.v1.GetRecordListResult.SourceTypeMapEntry
	12, // 12: moego.admin.agreement.v1.GetRecentSignedAgreementListParams.signed_type:type_name -> moego.models.agreement.v1.SignedType
	17, // 13: moego.admin.agreement.v1.GetRecentSignedAgreementListResult.agreement_recent_view:type_name -> moego.models.agreement.v1.AgreementWithRecentRecordsView
	3,  // 14: moego.admin.agreement.v1.AgreementRecordService.GetRecentSignedAgreementList:input_type -> moego.admin.agreement.v1.GetRecentSignedAgreementListParams
	0,  // 15: moego.admin.agreement.v1.AgreementRecordService.GetRecordList:input_type -> moego.admin.agreement.v1.GetRecordListParams
	2,  // 16: moego.admin.agreement.v1.AgreementRecordService.GetRecord:input_type -> moego.admin.agreement.v1.GetRecordParams
	2,  // 17: moego.admin.agreement.v1.AgreementRecordService.GetRecordSimpleView:input_type -> moego.admin.agreement.v1.GetRecordParams
	4,  // 18: moego.admin.agreement.v1.AgreementRecordService.GetRecentSignedAgreementList:output_type -> moego.admin.agreement.v1.GetRecentSignedAgreementListResult
	1,  // 19: moego.admin.agreement.v1.AgreementRecordService.GetRecordList:output_type -> moego.admin.agreement.v1.GetRecordListResult
	18, // 20: moego.admin.agreement.v1.AgreementRecordService.GetRecord:output_type -> moego.models.agreement.v1.AgreementRecordModel
	15, // 21: moego.admin.agreement.v1.AgreementRecordService.GetRecordSimpleView:output_type -> moego.models.agreement.v1.AgreementRecordSimpleView
	18, // [18:22] is the sub-list for method output_type
	14, // [14:18] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_admin_agreement_v1_agreement_record_admin_proto_init() }
func file_moego_admin_agreement_v1_agreement_record_admin_proto_init() {
	if File_moego_admin_agreement_v1_agreement_record_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecentSignedAgreementListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecentSignedAgreementListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_agreement_v1_agreement_record_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_agreement_v1_agreement_record_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_agreement_v1_agreement_record_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_agreement_v1_agreement_record_admin_proto = out.File
	file_moego_admin_agreement_v1_agreement_record_admin_proto_rawDesc = nil
	file_moego_admin_agreement_v1_agreement_record_admin_proto_goTypes = nil
	file_moego_admin_agreement_v1_agreement_record_admin_proto_depIdxs = nil
}
