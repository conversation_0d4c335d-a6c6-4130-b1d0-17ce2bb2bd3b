// @since 2024-08-12 17:31:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/pay_ops/v1/traffic_switch.proto

package payopsapipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// traffic switch params, for split payment/invoice
type TrafficSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int32 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// ratio
	Ratio *int32 `protobuf:"varint,2,opt,name=ratio,proto3,oneof" json:"ratio,omitempty"`
	// loan switch
	LoanSwitch *string `protobuf:"bytes,3,opt,name=loan_switch,json=loanSwitch,proto3,oneof" json:"loan_switch,omitempty"`
}

func (x *TrafficSwitchRequest) Reset() {
	*x = TrafficSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficSwitchRequest) ProtoMessage() {}

func (x *TrafficSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficSwitchRequest.ProtoReflect.Descriptor instead.
func (*TrafficSwitchRequest) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescGZIP(), []int{0}
}

func (x *TrafficSwitchRequest) GetBusinessId() int32 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *TrafficSwitchRequest) GetRatio() int32 {
	if x != nil && x.Ratio != nil {
		return *x.Ratio
	}
	return 0
}

func (x *TrafficSwitchRequest) GetLoanSwitch() string {
	if x != nil && x.LoanSwitch != nil {
		return *x.LoanSwitch
	}
	return ""
}

// traffic switch response
type TrafficSwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// traffic switch result
	Result string `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *TrafficSwitchResponse) Reset() {
	*x = TrafficSwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficSwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficSwitchResponse) ProtoMessage() {}

func (x *TrafficSwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficSwitchResponse.ProtoReflect.Descriptor instead.
func (*TrafficSwitchResponse) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescGZIP(), []int{1}
}

func (x *TrafficSwitchResponse) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

// all split traffic switch config
type GetAllSplitTrafficSwitchConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// json value
	TrafficSwitchConfig string `protobuf:"bytes,1,opt,name=traffic_switch_config,json=trafficSwitchConfig,proto3" json:"traffic_switch_config,omitempty"`
}

func (x *GetAllSplitTrafficSwitchConfigResponse) Reset() {
	*x = GetAllSplitTrafficSwitchConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllSplitTrafficSwitchConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllSplitTrafficSwitchConfigResponse) ProtoMessage() {}

func (x *GetAllSplitTrafficSwitchConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllSplitTrafficSwitchConfigResponse.ProtoReflect.Descriptor instead.
func (*GetAllSplitTrafficSwitchConfigResponse) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllSplitTrafficSwitchConfigResponse) GetTrafficSwitchConfig() string {
	if x != nil {
		return x.TrafficSwitchConfig
	}
	return ""
}

// all invoice reinvent traffic switch config
type GetAllInvoiceReinventTrafficSwitchConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// json value
	TrafficSwitchConfig string `protobuf:"bytes,1,opt,name=traffic_switch_config,json=trafficSwitchConfig,proto3" json:"traffic_switch_config,omitempty"`
}

func (x *GetAllInvoiceReinventTrafficSwitchConfigResponse) Reset() {
	*x = GetAllInvoiceReinventTrafficSwitchConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllInvoiceReinventTrafficSwitchConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllInvoiceReinventTrafficSwitchConfigResponse) ProtoMessage() {}

func (x *GetAllInvoiceReinventTrafficSwitchConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllInvoiceReinventTrafficSwitchConfigResponse.ProtoReflect.Descriptor instead.
func (*GetAllInvoiceReinventTrafficSwitchConfigResponse) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllInvoiceReinventTrafficSwitchConfigResponse) GetTrafficSwitchConfig() string {
	if x != nil {
		return x.TrafficSwitchConfig
	}
	return ""
}

var File_moego_admin_pay_ops_v1_traffic_switch_proto protoreflect.FileDescriptor

var file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa7, 0x01, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x19, 0x0a, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x01, 0x52, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x2f, 0x0a, 0x15,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5c, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61, 0x66,
	0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x66, 0x66,
	0x69, 0x63, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x66, 0x0a, 0x30, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x32, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x32, 0x89, 0x09, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x1a,
	0x41, 0x64, 0x64, 0x54, 0x6f, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x1a, 0x41, 0x64, 0x64, 0x54, 0x6f,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7e, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7a, 0x0a, 0x1b, 0x53, 0x65, 0x74, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66,
	0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7c, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66,
	0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8c, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x7b, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x57, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31,
	0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescOnce sync.Once
	file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescData = file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDesc
)

func file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescGZIP() []byte {
	file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescOnce.Do(func() {
		file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescData)
	})
	return file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDescData
}

var file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_admin_pay_ops_v1_traffic_switch_proto_goTypes = []interface{}{
	(*TrafficSwitchRequest)(nil),                             // 0: moego.admin.pay_ops.v1.TrafficSwitchRequest
	(*TrafficSwitchResponse)(nil),                            // 1: moego.admin.pay_ops.v1.TrafficSwitchResponse
	(*GetAllSplitTrafficSwitchConfigResponse)(nil),           // 2: moego.admin.pay_ops.v1.GetAllSplitTrafficSwitchConfigResponse
	(*GetAllInvoiceReinventTrafficSwitchConfigResponse)(nil), // 3: moego.admin.pay_ops.v1.GetAllInvoiceReinventTrafficSwitchConfigResponse
	(*emptypb.Empty)(nil),                                    // 4: google.protobuf.Empty
}
var file_moego_admin_pay_ops_v1_traffic_switch_proto_depIdxs = []int32{
	0, // 0: moego.admin.pay_ops.v1.TrafficSwitchService.AddToSplitPaymentWhitelist:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	0, // 1: moego.admin.pay_ops.v1.TrafficSwitchService.DeleteFromSplitPaymentWhitelist:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	0, // 2: moego.admin.pay_ops.v1.TrafficSwitchService.AddToSplitPaymentBlacklist:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	0, // 3: moego.admin.pay_ops.v1.TrafficSwitchService.DeleteFromSplitPaymentBlacklist:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	0, // 4: moego.admin.pay_ops.v1.TrafficSwitchService.SetSplitPaymentTrafficRatio:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	4, // 5: moego.admin.pay_ops.v1.TrafficSwitchService.GetAllSplitTrafficSwitchConfig:input_type -> google.protobuf.Empty
	0, // 6: moego.admin.pay_ops.v1.TrafficSwitchService.SetSplitPaymentLoanSwitch:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	0, // 7: moego.admin.pay_ops.v1.TrafficSwitchService.AddToInvoiceReinventWhitelist:input_type -> moego.admin.pay_ops.v1.TrafficSwitchRequest
	4, // 8: moego.admin.pay_ops.v1.TrafficSwitchService.GetAllInvoiceReinventTrafficSwitchConfig:input_type -> google.protobuf.Empty
	1, // 9: moego.admin.pay_ops.v1.TrafficSwitchService.AddToSplitPaymentWhitelist:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	1, // 10: moego.admin.pay_ops.v1.TrafficSwitchService.DeleteFromSplitPaymentWhitelist:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	1, // 11: moego.admin.pay_ops.v1.TrafficSwitchService.AddToSplitPaymentBlacklist:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	1, // 12: moego.admin.pay_ops.v1.TrafficSwitchService.DeleteFromSplitPaymentBlacklist:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	1, // 13: moego.admin.pay_ops.v1.TrafficSwitchService.SetSplitPaymentTrafficRatio:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	2, // 14: moego.admin.pay_ops.v1.TrafficSwitchService.GetAllSplitTrafficSwitchConfig:output_type -> moego.admin.pay_ops.v1.GetAllSplitTrafficSwitchConfigResponse
	1, // 15: moego.admin.pay_ops.v1.TrafficSwitchService.SetSplitPaymentLoanSwitch:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	1, // 16: moego.admin.pay_ops.v1.TrafficSwitchService.AddToInvoiceReinventWhitelist:output_type -> moego.admin.pay_ops.v1.TrafficSwitchResponse
	3, // 17: moego.admin.pay_ops.v1.TrafficSwitchService.GetAllInvoiceReinventTrafficSwitchConfig:output_type -> moego.admin.pay_ops.v1.GetAllInvoiceReinventTrafficSwitchConfigResponse
	9, // [9:18] is the sub-list for method output_type
	0, // [0:9] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_admin_pay_ops_v1_traffic_switch_proto_init() }
func file_moego_admin_pay_ops_v1_traffic_switch_proto_init() {
	if File_moego_admin_pay_ops_v1_traffic_switch_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficSwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllSplitTrafficSwitchConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllInvoiceReinventTrafficSwitchConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_pay_ops_v1_traffic_switch_proto_goTypes,
		DependencyIndexes: file_moego_admin_pay_ops_v1_traffic_switch_proto_depIdxs,
		MessageInfos:      file_moego_admin_pay_ops_v1_traffic_switch_proto_msgTypes,
	}.Build()
	File_moego_admin_pay_ops_v1_traffic_switch_proto = out.File
	file_moego_admin_pay_ops_v1_traffic_switch_proto_rawDesc = nil
	file_moego_admin_pay_ops_v1_traffic_switch_proto_goTypes = nil
	file_moego_admin_pay_ops_v1_traffic_switch_proto_depIdxs = nil
}
