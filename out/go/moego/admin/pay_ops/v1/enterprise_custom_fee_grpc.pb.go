// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/enterprise_custom_fee.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EnterpriseCustomFeeServiceClient is the client API for EnterpriseCustomFeeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EnterpriseCustomFeeServiceClient interface {
	// get company custom fee list
	ListEnterpriseCustomFees(ctx context.Context, in *ListEnterpriseCustomFeesParams, opts ...grpc.CallOption) (*ListEnterpriseCustomFeesResult, error)
	// create company custom fee
	CreateEnterpriseCustomFee(ctx context.Context, in *CreateEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*EnterpriseCustomFee, error)
	// update company custom fee
	UpdateEnterpriseCustomFee(ctx context.Context, in *UpdateEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*EnterpriseCustomFee, error)
	// delete company custom fee
	DeleteEnterpriseCustomFee(ctx context.Context, in *DeleteEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*DeleteEnterpriseCustomFeeResult, error)
}

type enterpriseCustomFeeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnterpriseCustomFeeServiceClient(cc grpc.ClientConnInterface) EnterpriseCustomFeeServiceClient {
	return &enterpriseCustomFeeServiceClient{cc}
}

func (c *enterpriseCustomFeeServiceClient) ListEnterpriseCustomFees(ctx context.Context, in *ListEnterpriseCustomFeesParams, opts ...grpc.CallOption) (*ListEnterpriseCustomFeesResult, error) {
	out := new(ListEnterpriseCustomFeesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/ListEnterpriseCustomFees", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseCustomFeeServiceClient) CreateEnterpriseCustomFee(ctx context.Context, in *CreateEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*EnterpriseCustomFee, error) {
	out := new(EnterpriseCustomFee)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/CreateEnterpriseCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseCustomFeeServiceClient) UpdateEnterpriseCustomFee(ctx context.Context, in *UpdateEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*EnterpriseCustomFee, error) {
	out := new(EnterpriseCustomFee)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/UpdateEnterpriseCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseCustomFeeServiceClient) DeleteEnterpriseCustomFee(ctx context.Context, in *DeleteEnterpriseCustomFeeParams, opts ...grpc.CallOption) (*DeleteEnterpriseCustomFeeResult, error) {
	out := new(DeleteEnterpriseCustomFeeResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/DeleteEnterpriseCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnterpriseCustomFeeServiceServer is the server API for EnterpriseCustomFeeService service.
// All implementations must embed UnimplementedEnterpriseCustomFeeServiceServer
// for forward compatibility
type EnterpriseCustomFeeServiceServer interface {
	// get company custom fee list
	ListEnterpriseCustomFees(context.Context, *ListEnterpriseCustomFeesParams) (*ListEnterpriseCustomFeesResult, error)
	// create company custom fee
	CreateEnterpriseCustomFee(context.Context, *CreateEnterpriseCustomFeeParams) (*EnterpriseCustomFee, error)
	// update company custom fee
	UpdateEnterpriseCustomFee(context.Context, *UpdateEnterpriseCustomFeeParams) (*EnterpriseCustomFee, error)
	// delete company custom fee
	DeleteEnterpriseCustomFee(context.Context, *DeleteEnterpriseCustomFeeParams) (*DeleteEnterpriseCustomFeeResult, error)
	mustEmbedUnimplementedEnterpriseCustomFeeServiceServer()
}

// UnimplementedEnterpriseCustomFeeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEnterpriseCustomFeeServiceServer struct {
}

func (UnimplementedEnterpriseCustomFeeServiceServer) ListEnterpriseCustomFees(context.Context, *ListEnterpriseCustomFeesParams) (*ListEnterpriseCustomFeesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnterpriseCustomFees not implemented")
}
func (UnimplementedEnterpriseCustomFeeServiceServer) CreateEnterpriseCustomFee(context.Context, *CreateEnterpriseCustomFeeParams) (*EnterpriseCustomFee, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEnterpriseCustomFee not implemented")
}
func (UnimplementedEnterpriseCustomFeeServiceServer) UpdateEnterpriseCustomFee(context.Context, *UpdateEnterpriseCustomFeeParams) (*EnterpriseCustomFee, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnterpriseCustomFee not implemented")
}
func (UnimplementedEnterpriseCustomFeeServiceServer) DeleteEnterpriseCustomFee(context.Context, *DeleteEnterpriseCustomFeeParams) (*DeleteEnterpriseCustomFeeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEnterpriseCustomFee not implemented")
}
func (UnimplementedEnterpriseCustomFeeServiceServer) mustEmbedUnimplementedEnterpriseCustomFeeServiceServer() {
}

// UnsafeEnterpriseCustomFeeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnterpriseCustomFeeServiceServer will
// result in compilation errors.
type UnsafeEnterpriseCustomFeeServiceServer interface {
	mustEmbedUnimplementedEnterpriseCustomFeeServiceServer()
}

func RegisterEnterpriseCustomFeeServiceServer(s grpc.ServiceRegistrar, srv EnterpriseCustomFeeServiceServer) {
	s.RegisterService(&EnterpriseCustomFeeService_ServiceDesc, srv)
}

func _EnterpriseCustomFeeService_ListEnterpriseCustomFees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnterpriseCustomFeesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseCustomFeeServiceServer).ListEnterpriseCustomFees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/ListEnterpriseCustomFees",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseCustomFeeServiceServer).ListEnterpriseCustomFees(ctx, req.(*ListEnterpriseCustomFeesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseCustomFeeService_CreateEnterpriseCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEnterpriseCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseCustomFeeServiceServer).CreateEnterpriseCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/CreateEnterpriseCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseCustomFeeServiceServer).CreateEnterpriseCustomFee(ctx, req.(*CreateEnterpriseCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseCustomFeeService_UpdateEnterpriseCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnterpriseCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseCustomFeeServiceServer).UpdateEnterpriseCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/UpdateEnterpriseCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseCustomFeeServiceServer).UpdateEnterpriseCustomFee(ctx, req.(*UpdateEnterpriseCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseCustomFeeService_DeleteEnterpriseCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEnterpriseCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseCustomFeeServiceServer).DeleteEnterpriseCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.EnterpriseCustomFeeService/DeleteEnterpriseCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseCustomFeeServiceServer).DeleteEnterpriseCustomFee(ctx, req.(*DeleteEnterpriseCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// EnterpriseCustomFeeService_ServiceDesc is the grpc.ServiceDesc for EnterpriseCustomFeeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnterpriseCustomFeeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.EnterpriseCustomFeeService",
	HandlerType: (*EnterpriseCustomFeeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListEnterpriseCustomFees",
			Handler:    _EnterpriseCustomFeeService_ListEnterpriseCustomFees_Handler,
		},
		{
			MethodName: "CreateEnterpriseCustomFee",
			Handler:    _EnterpriseCustomFeeService_CreateEnterpriseCustomFee_Handler,
		},
		{
			MethodName: "UpdateEnterpriseCustomFee",
			Handler:    _EnterpriseCustomFeeService_UpdateEnterpriseCustomFee_Handler,
		},
		{
			MethodName: "DeleteEnterpriseCustomFee",
			Handler:    _EnterpriseCustomFeeService_DeleteEnterpriseCustomFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/enterprise_custom_fee.proto",
}
