// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/message/v1/message_credit_admin.proto

package messageapipb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetMessageCreditParams is the request message for getting the message credit of a company.
type GetMessageCreditParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetMessageCreditParams) Reset() {
	*x = GetMessageCreditParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageCreditParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageCreditParams) ProtoMessage() {}

func (x *GetMessageCreditParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageCreditParams.ProtoReflect.Descriptor instead.
func (*GetMessageCreditParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{0}
}

func (x *GetMessageCreditParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetMessageCreditResult is the response message for getting the message credit of a company.
type GetMessageCreditResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Message credit details.
	MessageCredit *MessageCredit `protobuf:"bytes,1,opt,name=message_credit,json=messageCredit,proto3" json:"message_credit,omitempty"`
}

func (x *GetMessageCreditResult) Reset() {
	*x = GetMessageCreditResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageCreditResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageCreditResult) ProtoMessage() {}

func (x *GetMessageCreditResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageCreditResult.ProtoReflect.Descriptor instead.
func (*GetMessageCreditResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{1}
}

func (x *GetMessageCreditResult) GetMessageCredit() *MessageCredit {
	if x != nil {
		return x.MessageCredit
	}
	return nil
}

// AddMessageCreditParams is the request message for adding message credit to a company.
type AddMessageCreditParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The email credits to add.
	EmailCredit *EmailCreditUpdateDef `protobuf:"bytes,2,opt,name=email_credit,json=emailCredit,proto3,oneof" json:"email_credit,omitempty"`
	// The SMS credits to add.
	SmsCredit *SmsCreditUpdateDef `protobuf:"bytes,3,opt,name=sms_credit,json=smsCredit,proto3,oneof" json:"sms_credit,omitempty"`
}

func (x *AddMessageCreditParams) Reset() {
	*x = AddMessageCreditParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMessageCreditParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMessageCreditParams) ProtoMessage() {}

func (x *AddMessageCreditParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMessageCreditParams.ProtoReflect.Descriptor instead.
func (*AddMessageCreditParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{2}
}

func (x *AddMessageCreditParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AddMessageCreditParams) GetEmailCredit() *EmailCreditUpdateDef {
	if x != nil {
		return x.EmailCredit
	}
	return nil
}

func (x *AddMessageCreditParams) GetSmsCredit() *SmsCreditUpdateDef {
	if x != nil {
		return x.SmsCredit
	}
	return nil
}

// Defines the values to add to the email credits.
type EmailCreditUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The amount of purchased email credits to add.
	PurchasedCredits *int32 `protobuf:"varint,1,opt,name=purchased_credits,json=purchasedCredits,proto3,oneof" json:"purchased_credits,omitempty"`
}

func (x *EmailCreditUpdateDef) Reset() {
	*x = EmailCreditUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailCreditUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailCreditUpdateDef) ProtoMessage() {}

func (x *EmailCreditUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailCreditUpdateDef.ProtoReflect.Descriptor instead.
func (*EmailCreditUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{3}
}

func (x *EmailCreditUpdateDef) GetPurchasedCredits() int32 {
	if x != nil && x.PurchasedCredits != nil {
		return *x.PurchasedCredits
	}
	return 0
}

// Defines the values to add to the SMS credits.
type SmsCreditUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The amount of purchased SMS credits to add.
	PurchasedCredits *int32 `protobuf:"varint,1,opt,name=purchased_credits,json=purchasedCredits,proto3,oneof" json:"purchased_credits,omitempty"`
}

func (x *SmsCreditUpdateDef) Reset() {
	*x = SmsCreditUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsCreditUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsCreditUpdateDef) ProtoMessage() {}

func (x *SmsCreditUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsCreditUpdateDef.ProtoReflect.Descriptor instead.
func (*SmsCreditUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{4}
}

func (x *SmsCreditUpdateDef) GetPurchasedCredits() int32 {
	if x != nil && x.PurchasedCredits != nil {
		return *x.PurchasedCredits
	}
	return 0
}

// AddMessageCreditResult is the response message for adding message credit to a company.
type AddMessageCreditResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Message credit details.
	MessageCredit *MessageCredit `protobuf:"bytes,1,opt,name=message_credit,json=messageCredit,proto3" json:"message_credit,omitempty"`
}

func (x *AddMessageCreditResult) Reset() {
	*x = AddMessageCreditResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMessageCreditResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMessageCreditResult) ProtoMessage() {}

func (x *AddMessageCreditResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMessageCreditResult.ProtoReflect.Descriptor instead.
func (*AddMessageCreditResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{5}
}

func (x *AddMessageCreditResult) GetMessageCredit() *MessageCredit {
	if x != nil {
		return x.MessageCredit
	}
	return nil
}

// MessageCredit defines the message credit details for a company.
type MessageCredit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID.
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Company ID.
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Cycle begin time.
	CycleBeginTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=cycle_begin_time,json=cycleBeginTime,proto3" json:"cycle_begin_time,omitempty"`
	// Cycle end time.
	CycleEndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=cycle_end_time,json=cycleEndTime,proto3" json:"cycle_end_time,omitempty"`
	// Email credit details.
	EmailCredit *EmailCredit `protobuf:"bytes,5,opt,name=email_credit,json=emailCredit,proto3" json:"email_credit,omitempty"`
	// SMS credit details.
	SmsCredit *SmsCredit `protobuf:"bytes,6,opt,name=sms_credit,json=smsCredit,proto3" json:"sms_credit,omitempty"`
}

func (x *MessageCredit) Reset() {
	*x = MessageCredit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageCredit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageCredit) ProtoMessage() {}

func (x *MessageCredit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageCredit.ProtoReflect.Descriptor instead.
func (*MessageCredit) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{6}
}

func (x *MessageCredit) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MessageCredit) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MessageCredit) GetCycleBeginTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CycleBeginTime
	}
	return nil
}

func (x *MessageCredit) GetCycleEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CycleEndTime
	}
	return nil
}

func (x *MessageCredit) GetEmailCredit() *EmailCredit {
	if x != nil {
		return x.EmailCredit
	}
	return nil
}

func (x *MessageCredit) GetSmsCredit() *SmsCredit {
	if x != nil {
		return x.SmsCredit
	}
	return nil
}

// EmailCredit defines the email credit details.
type EmailCredit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of remained email credits.
	// remained_credits = subscription_credits + purchased_credits + leftover_purchased_credits - used_credits
	RemainedCredits int32 `protobuf:"varint,1,opt,name=remained_credits,json=remainedCredits,proto3" json:"remained_credits,omitempty"`
	// The number of credits from the subscription for the current cycle.
	// These credits do not roll over to the next cycle.
	SubscriptionCredits int32 `protobuf:"varint,2,opt,name=subscription_credits,json=subscriptionCredits,proto3" json:"subscription_credits,omitempty"`
	// The number of additionally purchased credits for the current cycle.
	PurchasedCredits int32 `protobuf:"varint,3,opt,name=purchased_credits,json=purchasedCredits,proto3" json:"purchased_credits,omitempty"`
	// The number of unused purchased credits rolled over from the previous cycle.
	LeftoverPurchasedCredits int32 `protobuf:"varint,4,opt,name=leftover_purchased_credits,json=leftoverPurchasedCredits,proto3" json:"leftover_purchased_credits,omitempty"`
	// The total number of used credits in the current cycle.
	UsedCredits int32 `protobuf:"varint,5,opt,name=used_credits,json=usedCredits,proto3" json:"used_credits,omitempty"`
}

func (x *EmailCredit) Reset() {
	*x = EmailCredit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailCredit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailCredit) ProtoMessage() {}

func (x *EmailCredit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailCredit.ProtoReflect.Descriptor instead.
func (*EmailCredit) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{7}
}

func (x *EmailCredit) GetRemainedCredits() int32 {
	if x != nil {
		return x.RemainedCredits
	}
	return 0
}

func (x *EmailCredit) GetSubscriptionCredits() int32 {
	if x != nil {
		return x.SubscriptionCredits
	}
	return 0
}

func (x *EmailCredit) GetPurchasedCredits() int32 {
	if x != nil {
		return x.PurchasedCredits
	}
	return 0
}

func (x *EmailCredit) GetLeftoverPurchasedCredits() int32 {
	if x != nil {
		return x.LeftoverPurchasedCredits
	}
	return 0
}

func (x *EmailCredit) GetUsedCredits() int32 {
	if x != nil {
		return x.UsedCredits
	}
	return 0
}

// SmsCredit defines the SMS credit details.
type SmsCredit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of remained SMS credits.
	// remained_credits = (subscription_credits + purchased_credits + leftover_purchased_credits) - (used_two_way_credits + used_auto_message_credits + used_call_credits)
	RemainedCredits int32 `protobuf:"varint,1,opt,name=remained_credits,json=remainedCredits,proto3" json:"remained_credits,omitempty"`
	// The number of credits from the subscription for the current cycle.
	// These credits do not roll over to the next cycle.
	SubscriptionCredits int32 `protobuf:"varint,2,opt,name=subscription_credits,json=subscriptionCredits,proto3" json:"subscription_credits,omitempty"`
	// The number of additionally purchased credits for the current cycle.
	PurchasedCredits int32 `protobuf:"varint,3,opt,name=purchased_credits,json=purchasedCredits,proto3" json:"purchased_credits,omitempty"`
	// The number of unused purchased credits rolled over from the previous cycle.
	LeftoverPurchasedCredits int32 `protobuf:"varint,4,opt,name=leftover_purchased_credits,json=leftoverPurchasedCredits,proto3" json:"leftover_purchased_credits,omitempty"`
	// The number of used credits for two-way messaging.
	UsedTwoWayCredits int32 `protobuf:"varint,5,opt,name=used_two_way_credits,json=usedTwoWayCredits,proto3" json:"used_two_way_credits,omitempty"`
	// The number of used credits for automated messages.
	UsedAutoMessageCredits int32 `protobuf:"varint,6,opt,name=used_auto_message_credits,json=usedAutoMessageCredits,proto3" json:"used_auto_message_credits,omitempty"`
	// The number of used credits for calls.
	UsedCallCredits int32 `protobuf:"varint,7,opt,name=used_call_credits,json=usedCallCredits,proto3" json:"used_call_credits,omitempty"`
}

func (x *SmsCredit) Reset() {
	*x = SmsCredit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsCredit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsCredit) ProtoMessage() {}

func (x *SmsCredit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsCredit.ProtoReflect.Descriptor instead.
func (*SmsCredit) Descriptor() ([]byte, []int) {
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP(), []int{8}
}

func (x *SmsCredit) GetRemainedCredits() int32 {
	if x != nil {
		return x.RemainedCredits
	}
	return 0
}

func (x *SmsCredit) GetSubscriptionCredits() int32 {
	if x != nil {
		return x.SubscriptionCredits
	}
	return 0
}

func (x *SmsCredit) GetPurchasedCredits() int32 {
	if x != nil {
		return x.PurchasedCredits
	}
	return 0
}

func (x *SmsCredit) GetLeftoverPurchasedCredits() int32 {
	if x != nil {
		return x.LeftoverPurchasedCredits
	}
	return 0
}

func (x *SmsCredit) GetUsedTwoWayCredits() int32 {
	if x != nil {
		return x.UsedTwoWayCredits
	}
	return 0
}

func (x *SmsCredit) GetUsedAutoMessageCredits() int32 {
	if x != nil {
		return x.UsedAutoMessageCredits
	}
	return 0
}

func (x *SmsCredit) GetUsedCallCredits() int32 {
	if x != nil {
		return x.UsedCallCredits
	}
	return 0
}

var File_moego_admin_message_v1_message_credit_admin_proto protoreflect.FileDescriptor

var file_moego_admin_message_v1_message_credit_admin_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x40, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x66, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4c, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x52, 0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x22,
	0x86, 0x02, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x54, 0x0a, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0b, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0a, 0x73, 0x6d, 0x73, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6d, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x09, 0x73, 0x6d, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x6d,
	0x73, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x22, 0x5e, 0x0a, 0x14, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x30, 0x0a, 0x11, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x10, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x22, 0x5c, 0x0a, 0x12, 0x53, 0x6d, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x30,
	0x0a, 0x11, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x10, 0x70, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01,
	0x42, 0x14, 0x0a, 0x12, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x22, 0x66, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4c, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x22, 0xd0,
	0x02, 0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x44, 0x0a, 0x10, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x67, 0x69,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x0b, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12,
	0x40, 0x0a, 0x0a, 0x73, 0x6d, 0x73, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6d, 0x73,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x09, 0x73, 0x6d, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x22, 0xf9, 0x01, 0x0a, 0x0b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x14,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12,
	0x2b, 0x0a, 0x11, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x1a,
	0x6c, 0x65, 0x66, 0x74, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x18, 0x6c, 0x65, 0x66, 0x74, 0x6f, 0x76, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x73,
	0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x22, 0xec, 0x02,
	0x0a, 0x09, 0x53, 0x6d, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x72,
	0x65, 0x6d, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x1a, 0x6c, 0x65, 0x66, 0x74, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6c, 0x65, 0x66, 0x74,
	0x6f, 0x76, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x77, 0x6f,
	0x5f, 0x77, 0x61, 0x79, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x11, 0x75, 0x73, 0x65, 0x64, 0x54, 0x77, 0x6f, 0x57, 0x61, 0x79, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x75, 0x73, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x75, 0x73, 0x65,
	0x64, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x32, 0xff, 0x01, 0x0a,
	0x15, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x72, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x72, 0x0a, 0x10, 0x41, 0x64,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7c,
	0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_message_v1_message_credit_admin_proto_rawDescOnce sync.Once
	file_moego_admin_message_v1_message_credit_admin_proto_rawDescData = file_moego_admin_message_v1_message_credit_admin_proto_rawDesc
)

func file_moego_admin_message_v1_message_credit_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_message_v1_message_credit_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_message_v1_message_credit_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_message_v1_message_credit_admin_proto_rawDescData)
	})
	return file_moego_admin_message_v1_message_credit_admin_proto_rawDescData
}

var file_moego_admin_message_v1_message_credit_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_admin_message_v1_message_credit_admin_proto_goTypes = []interface{}{
	(*GetMessageCreditParams)(nil), // 0: moego.admin.message.v1.GetMessageCreditParams
	(*GetMessageCreditResult)(nil), // 1: moego.admin.message.v1.GetMessageCreditResult
	(*AddMessageCreditParams)(nil), // 2: moego.admin.message.v1.AddMessageCreditParams
	(*EmailCreditUpdateDef)(nil),   // 3: moego.admin.message.v1.EmailCreditUpdateDef
	(*SmsCreditUpdateDef)(nil),     // 4: moego.admin.message.v1.SmsCreditUpdateDef
	(*AddMessageCreditResult)(nil), // 5: moego.admin.message.v1.AddMessageCreditResult
	(*MessageCredit)(nil),          // 6: moego.admin.message.v1.MessageCredit
	(*EmailCredit)(nil),            // 7: moego.admin.message.v1.EmailCredit
	(*SmsCredit)(nil),              // 8: moego.admin.message.v1.SmsCredit
	(*timestamppb.Timestamp)(nil),  // 9: google.protobuf.Timestamp
}
var file_moego_admin_message_v1_message_credit_admin_proto_depIdxs = []int32{
	6,  // 0: moego.admin.message.v1.GetMessageCreditResult.message_credit:type_name -> moego.admin.message.v1.MessageCredit
	3,  // 1: moego.admin.message.v1.AddMessageCreditParams.email_credit:type_name -> moego.admin.message.v1.EmailCreditUpdateDef
	4,  // 2: moego.admin.message.v1.AddMessageCreditParams.sms_credit:type_name -> moego.admin.message.v1.SmsCreditUpdateDef
	6,  // 3: moego.admin.message.v1.AddMessageCreditResult.message_credit:type_name -> moego.admin.message.v1.MessageCredit
	9,  // 4: moego.admin.message.v1.MessageCredit.cycle_begin_time:type_name -> google.protobuf.Timestamp
	9,  // 5: moego.admin.message.v1.MessageCredit.cycle_end_time:type_name -> google.protobuf.Timestamp
	7,  // 6: moego.admin.message.v1.MessageCredit.email_credit:type_name -> moego.admin.message.v1.EmailCredit
	8,  // 7: moego.admin.message.v1.MessageCredit.sms_credit:type_name -> moego.admin.message.v1.SmsCredit
	0,  // 8: moego.admin.message.v1.MessageCreditAdminApi.GetMessageCredit:input_type -> moego.admin.message.v1.GetMessageCreditParams
	2,  // 9: moego.admin.message.v1.MessageCreditAdminApi.AddMessageCredit:input_type -> moego.admin.message.v1.AddMessageCreditParams
	1,  // 10: moego.admin.message.v1.MessageCreditAdminApi.GetMessageCredit:output_type -> moego.admin.message.v1.GetMessageCreditResult
	5,  // 11: moego.admin.message.v1.MessageCreditAdminApi.AddMessageCredit:output_type -> moego.admin.message.v1.AddMessageCreditResult
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_admin_message_v1_message_credit_admin_proto_init() }
func file_moego_admin_message_v1_message_credit_admin_proto_init() {
	if File_moego_admin_message_v1_message_credit_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageCreditParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageCreditResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMessageCreditParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailCreditUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsCreditUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMessageCreditResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageCredit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailCredit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsCredit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_admin_message_v1_message_credit_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_message_v1_message_credit_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_message_v1_message_credit_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_message_v1_message_credit_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_message_v1_message_credit_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_message_v1_message_credit_admin_proto = out.File
	file_moego_admin_message_v1_message_credit_admin_proto_rawDesc = nil
	file_moego_admin_message_v1_message_credit_admin_proto_goTypes = nil
	file_moego_admin_message_v1_message_credit_admin_proto_depIdxs = nil
}
