// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/company/v1/company_admin.proto

package companyapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CompanyServiceClient is the client API for CompanyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CompanyServiceClient interface {
	// search company
	SearchCompany(ctx context.Context, in *SearchCompanyParams, opts ...grpc.CallOption) (*SearchCompanyResult, error)
	// query company list
	ListCompany(ctx context.Context, in *ListCompanyParams, opts ...grpc.CallOption) (*ListCompanyResult, error)
	// update company
	UpdateCompany(ctx context.Context, in *UpdateCompanyParams, opts ...grpc.CallOption) (*UpdateCompanyResult, error)
	// remove free account
	RemoveFreeAccount(ctx context.Context, in *RemoveFreeAccountParams, opts ...grpc.CallOption) (*RemoveFreeAccountResult, error)
	// reactivate company
	ReactivateCompany(ctx context.Context, in *ReactivateCompanyParams, opts ...grpc.CallOption) (*ReactivateCompanyResult, error)
	// deactivate company
	DeactivateCompany(ctx context.Context, in *DeactivateCompanyParams, opts ...grpc.CallOption) (*DeactivateCompanyResult, error)
}

type companyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCompanyServiceClient(cc grpc.ClientConnInterface) CompanyServiceClient {
	return &companyServiceClient{cc}
}

func (c *companyServiceClient) SearchCompany(ctx context.Context, in *SearchCompanyParams, opts ...grpc.CallOption) (*SearchCompanyResult, error) {
	out := new(SearchCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/SearchCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) ListCompany(ctx context.Context, in *ListCompanyParams, opts ...grpc.CallOption) (*ListCompanyResult, error) {
	out := new(ListCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/ListCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) UpdateCompany(ctx context.Context, in *UpdateCompanyParams, opts ...grpc.CallOption) (*UpdateCompanyResult, error) {
	out := new(UpdateCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/UpdateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) RemoveFreeAccount(ctx context.Context, in *RemoveFreeAccountParams, opts ...grpc.CallOption) (*RemoveFreeAccountResult, error) {
	out := new(RemoveFreeAccountResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/RemoveFreeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) ReactivateCompany(ctx context.Context, in *ReactivateCompanyParams, opts ...grpc.CallOption) (*ReactivateCompanyResult, error) {
	out := new(ReactivateCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/ReactivateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *companyServiceClient) DeactivateCompany(ctx context.Context, in *DeactivateCompanyParams, opts ...grpc.CallOption) (*DeactivateCompanyResult, error) {
	out := new(DeactivateCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.company.v1.CompanyService/DeactivateCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CompanyServiceServer is the server API for CompanyService service.
// All implementations must embed UnimplementedCompanyServiceServer
// for forward compatibility
type CompanyServiceServer interface {
	// search company
	SearchCompany(context.Context, *SearchCompanyParams) (*SearchCompanyResult, error)
	// query company list
	ListCompany(context.Context, *ListCompanyParams) (*ListCompanyResult, error)
	// update company
	UpdateCompany(context.Context, *UpdateCompanyParams) (*UpdateCompanyResult, error)
	// remove free account
	RemoveFreeAccount(context.Context, *RemoveFreeAccountParams) (*RemoveFreeAccountResult, error)
	// reactivate company
	ReactivateCompany(context.Context, *ReactivateCompanyParams) (*ReactivateCompanyResult, error)
	// deactivate company
	DeactivateCompany(context.Context, *DeactivateCompanyParams) (*DeactivateCompanyResult, error)
	mustEmbedUnimplementedCompanyServiceServer()
}

// UnimplementedCompanyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCompanyServiceServer struct {
}

func (UnimplementedCompanyServiceServer) SearchCompany(context.Context, *SearchCompanyParams) (*SearchCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCompany not implemented")
}
func (UnimplementedCompanyServiceServer) ListCompany(context.Context, *ListCompanyParams) (*ListCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCompany not implemented")
}
func (UnimplementedCompanyServiceServer) UpdateCompany(context.Context, *UpdateCompanyParams) (*UpdateCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) RemoveFreeAccount(context.Context, *RemoveFreeAccountParams) (*RemoveFreeAccountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveFreeAccount not implemented")
}
func (UnimplementedCompanyServiceServer) ReactivateCompany(context.Context, *ReactivateCompanyParams) (*ReactivateCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReactivateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) DeactivateCompany(context.Context, *DeactivateCompanyParams) (*DeactivateCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateCompany not implemented")
}
func (UnimplementedCompanyServiceServer) mustEmbedUnimplementedCompanyServiceServer() {}

// UnsafeCompanyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CompanyServiceServer will
// result in compilation errors.
type UnsafeCompanyServiceServer interface {
	mustEmbedUnimplementedCompanyServiceServer()
}

func RegisterCompanyServiceServer(s grpc.ServiceRegistrar, srv CompanyServiceServer) {
	s.RegisterService(&CompanyService_ServiceDesc, srv)
}

func _CompanyService_SearchCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).SearchCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/SearchCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).SearchCompany(ctx, req.(*SearchCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_ListCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).ListCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/ListCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).ListCompany(ctx, req.(*ListCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_UpdateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).UpdateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/UpdateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).UpdateCompany(ctx, req.(*UpdateCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_RemoveFreeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFreeAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).RemoveFreeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/RemoveFreeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).RemoveFreeAccount(ctx, req.(*RemoveFreeAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_ReactivateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReactivateCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).ReactivateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/ReactivateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).ReactivateCompany(ctx, req.(*ReactivateCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CompanyService_DeactivateCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CompanyServiceServer).DeactivateCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.company.v1.CompanyService/DeactivateCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CompanyServiceServer).DeactivateCompany(ctx, req.(*DeactivateCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CompanyService_ServiceDesc is the grpc.ServiceDesc for CompanyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CompanyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.company.v1.CompanyService",
	HandlerType: (*CompanyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchCompany",
			Handler:    _CompanyService_SearchCompany_Handler,
		},
		{
			MethodName: "ListCompany",
			Handler:    _CompanyService_ListCompany_Handler,
		},
		{
			MethodName: "UpdateCompany",
			Handler:    _CompanyService_UpdateCompany_Handler,
		},
		{
			MethodName: "RemoveFreeAccount",
			Handler:    _CompanyService_RemoveFreeAccount_Handler,
		},
		{
			MethodName: "ReactivateCompany",
			Handler:    _CompanyService_ReactivateCompany_Handler,
		},
		{
			MethodName: "DeactivateCompany",
			Handler:    _CompanyService_DeactivateCompany_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/company/v1/company_admin.proto",
}
