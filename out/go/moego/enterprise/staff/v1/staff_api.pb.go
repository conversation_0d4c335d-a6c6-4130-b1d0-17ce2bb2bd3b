// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/staff/v1/staff_api.proto

package staffapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create staff params
type CreateStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff profile
	Profile *v1.CreateStaffProfile `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	// resources
	Resources *v1.ResourceDef `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	// send invite link params
	InviteLink *v11.SendInviteLinkParamsDef `protobuf:"bytes,3,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
}

func (x *CreateStaffParams) Reset() {
	*x = CreateStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffParams) ProtoMessage() {}

func (x *CreateStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffParams.ProtoReflect.Descriptor instead.
func (*CreateStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateStaffParams) GetProfile() *v1.CreateStaffProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *CreateStaffParams) GetResources() *v1.ResourceDef {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *CreateStaffParams) GetInviteLink() *v11.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

// create staff response
type CreateStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// resources
	Resources *v1.ResourceDef `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	// staff email
	StaffEmail *v11.StaffEmailDef `protobuf:"bytes,3,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
}

func (x *CreateStaffResult) Reset() {
	*x = CreateStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffResult) ProtoMessage() {}

func (x *CreateStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffResult.ProtoReflect.Descriptor instead.
func (*CreateStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateStaffResult) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *CreateStaffResult) GetResources() *v1.ResourceDef {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *CreateStaffResult) GetStaffEmail() *v11.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// list staff params
type ListStaffsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// order by
	OrderBy *v2.OrderBy `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// filter
	Filter *ListStaffsParams_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListStaffsParams) Reset() {
	*x = ListStaffsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffsParams) ProtoMessage() {}

func (x *ListStaffsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffsParams.ProtoReflect.Descriptor instead.
func (*ListStaffsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListStaffsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListStaffsParams) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ListStaffsParams) GetFilter() *ListStaffsParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list staff
type ListStaffsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffModel `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// staff email
	IdToStaffEmails map[int64]*v11.StaffEmailDef `protobuf:"bytes,3,rep,name=id_to_staff_emails,json=idToStaffEmails,proto3" json:"id_to_staff_emails,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// staff assign tenant nums
	StaffToAssignTenantNums map[int64]int64 `protobuf:"bytes,4,rep,name=staff_to_assign_tenant_nums,json=staffToAssignTenantNums,proto3" json:"staff_to_assign_tenant_nums,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *ListStaffsResult) Reset() {
	*x = ListStaffsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffsResult) ProtoMessage() {}

func (x *ListStaffsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffsResult.ProtoReflect.Descriptor instead.
func (*ListStaffsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListStaffsResult) GetStaffs() []*v1.StaffModel {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *ListStaffsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListStaffsResult) GetIdToStaffEmails() map[int64]*v11.StaffEmailDef {
	if x != nil {
		return x.IdToStaffEmails
	}
	return nil
}

func (x *ListStaffsResult) GetStaffToAssignTenantNums() map[int64]int64 {
	if x != nil {
		return x.StaffToAssignTenantNums
	}
	return nil
}

// update staff params
type UpdateStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff profile
	Profile *v1.UpdateStaffProfile `protobuf:"bytes,2,opt,name=profile,proto3,oneof" json:"profile,omitempty"`
	// resources
	Resources *v1.ResourceDef `protobuf:"bytes,3,opt,name=resources,proto3,oneof" json:"resources,omitempty"`
	// send invite link params
	InviteLink *v11.SendInviteLinkParamsDef `protobuf:"bytes,4,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
}

func (x *UpdateStaffParams) Reset() {
	*x = UpdateStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffParams) ProtoMessage() {}

func (x *UpdateStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateStaffParams) GetProfile() *v1.UpdateStaffProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *UpdateStaffParams) GetResources() *v1.ResourceDef {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *UpdateStaffParams) GetInviteLink() *v11.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

// update staff response
type UpdateStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff profile
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// resources
	Resources *v1.ResourceDef `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	// staff email
	StaffEmail *v11.StaffEmailDef `protobuf:"bytes,3,opt,name=staff_email,json=staffEmail,proto3,oneof" json:"staff_email,omitempty"`
}

func (x *UpdateStaffResult) Reset() {
	*x = UpdateStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffResult) ProtoMessage() {}

func (x *UpdateStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffResult.ProtoReflect.Descriptor instead.
func (*UpdateStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateStaffResult) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *UpdateStaffResult) GetResources() *v1.ResourceDef {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *UpdateStaffResult) GetStaffEmail() *v11.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// get staff
type GetStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetStaffParams) Reset() {
	*x = GetStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffParams) ProtoMessage() {}

func (x *GetStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffParams.ProtoReflect.Descriptor instead.
func (*GetStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get staff result
type GetStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff profile
	Staff *v1.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// resources
	Resources *v1.ResourceDef `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	// staff email
	StaffEmail *v11.StaffEmailDef `protobuf:"bytes,3,opt,name=staff_email,json=staffEmail,proto3,oneof" json:"staff_email,omitempty"`
}

func (x *GetStaffResult) Reset() {
	*x = GetStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffResult) ProtoMessage() {}

func (x *GetStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffResult.ProtoReflect.Descriptor instead.
func (*GetStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetStaffResult) GetStaff() *v1.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *GetStaffResult) GetResources() *v1.ResourceDef {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *GetStaffResult) GetStaffEmail() *v11.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

// delete staff
type DeleteStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteStaffParams) Reset() {
	*x = DeleteStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffParams) ProtoMessage() {}

func (x *DeleteStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffParams.ProtoReflect.Descriptor instead.
func (*DeleteStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete staff result
type DeleteStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteStaffResult) Reset() {
	*x = DeleteStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffResult) ProtoMessage() {}

func (x *DeleteStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffResult.ProtoReflect.Descriptor instead.
func (*DeleteStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{9}
}

// send invite link staff
type SendStaffInviteLinkParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// send invite link def
	InviteLink *v11.SendInviteLinkDef `protobuf:"bytes,1,opt,name=invite_link,json=inviteLink,proto3" json:"invite_link,omitempty"`
}

func (x *SendStaffInviteLinkParams) Reset() {
	*x = SendStaffInviteLinkParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendStaffInviteLinkParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendStaffInviteLinkParams) ProtoMessage() {}

func (x *SendStaffInviteLinkParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendStaffInviteLinkParams.ProtoReflect.Descriptor instead.
func (*SendStaffInviteLinkParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{10}
}

func (x *SendStaffInviteLinkParams) GetInviteLink() *v11.SendInviteLinkDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

// send invite link staff result
type SendStaffInviteLinkResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendStaffInviteLinkResult) Reset() {
	*x = SendStaffInviteLinkResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendStaffInviteLinkResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendStaffInviteLinkResult) ProtoMessage() {}

func (x *SendStaffInviteLinkResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendStaffInviteLinkResult.ProtoReflect.Descriptor instead.
func (*SendStaffInviteLinkResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{11}
}

// unlink staff
type UnlinkStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UnlinkStaffParams) Reset() {
	*x = UnlinkStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlinkStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkStaffParams) ProtoMessage() {}

func (x *UnlinkStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkStaffParams.ProtoReflect.Descriptor instead.
func (*UnlinkStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{12}
}

func (x *UnlinkStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// unlink staff result
type UnlinkStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnlinkStaffResult) Reset() {
	*x = UnlinkStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlinkStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkStaffResult) ProtoMessage() {}

func (x *UnlinkStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkStaffResult.ProtoReflect.Descriptor instead.
func (*UnlinkStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{13}
}

// filter
type ListStaffsParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleIds []int64 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// keyword
	Keyword *string `protobuf:"bytes,2,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
}

func (x *ListStaffsParams_Filter) Reset() {
	*x = ListStaffsParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffsParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffsParams_Filter) ProtoMessage() {}

func (x *ListStaffsParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffsParams_Filter.ProtoReflect.Descriptor instead.
func (*ListStaffsParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListStaffsParams_Filter) GetRoleIds() []int64 {
	if x != nil {
		return x.RoleIds
	}
	return nil
}

func (x *ListStaffsParams_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

var File_moego_enterprise_staff_v1_staff_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_staff_v1_staff_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x45, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01,
	0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x22, 0xe6, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x45, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65,
	0x66, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x0b,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xde, 0x02, 0x0a, 0x10, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0x5b, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x52,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xcb, 0x04, 0x0a, 0x10,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6d, 0x0a, 0x12, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x64,
	0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0f, 0x69, 0x64, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x1b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x74, 0x6f,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x73, 0x1a, 0x6f, 0x0a, 0x14,
	0x49, 0x64, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44,
	0x65, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4a, 0x0a,
	0x1c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xce, 0x02, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65, 0x66, 0x48,
	0x02, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xfb, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x45,
	0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x29, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x12, 0x45, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x2c,
	0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x6d, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x50,
	0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x22, 0x1b, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2c, 0x0a,
	0x11, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x55,
	0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x32, 0x88, 0x06, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x69, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x0b,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x0b, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x66, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01, 0x0a,
	0x13, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x69, 0x0a, 0x0b, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x6c, 0x69,
	0x6e, 0x6b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x80, 0x01, 0x0a, 0x21,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x2f, 0x76, 0x31, 0x3b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_staff_v1_staff_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_staff_v1_staff_api_proto_rawDescData = file_moego_enterprise_staff_v1_staff_api_proto_rawDesc
)

func file_moego_enterprise_staff_v1_staff_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_staff_v1_staff_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_staff_v1_staff_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_staff_v1_staff_api_proto_rawDescData)
	})
	return file_moego_enterprise_staff_v1_staff_api_proto_rawDescData
}

var file_moego_enterprise_staff_v1_staff_api_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_moego_enterprise_staff_v1_staff_api_proto_goTypes = []interface{}{
	(*CreateStaffParams)(nil),           // 0: moego.enterprise.staff.v1.CreateStaffParams
	(*CreateStaffResult)(nil),           // 1: moego.enterprise.staff.v1.CreateStaffResult
	(*ListStaffsParams)(nil),            // 2: moego.enterprise.staff.v1.ListStaffsParams
	(*ListStaffsResult)(nil),            // 3: moego.enterprise.staff.v1.ListStaffsResult
	(*UpdateStaffParams)(nil),           // 4: moego.enterprise.staff.v1.UpdateStaffParams
	(*UpdateStaffResult)(nil),           // 5: moego.enterprise.staff.v1.UpdateStaffResult
	(*GetStaffParams)(nil),              // 6: moego.enterprise.staff.v1.GetStaffParams
	(*GetStaffResult)(nil),              // 7: moego.enterprise.staff.v1.GetStaffResult
	(*DeleteStaffParams)(nil),           // 8: moego.enterprise.staff.v1.DeleteStaffParams
	(*DeleteStaffResult)(nil),           // 9: moego.enterprise.staff.v1.DeleteStaffResult
	(*SendStaffInviteLinkParams)(nil),   // 10: moego.enterprise.staff.v1.SendStaffInviteLinkParams
	(*SendStaffInviteLinkResult)(nil),   // 11: moego.enterprise.staff.v1.SendStaffInviteLinkResult
	(*UnlinkStaffParams)(nil),           // 12: moego.enterprise.staff.v1.UnlinkStaffParams
	(*UnlinkStaffResult)(nil),           // 13: moego.enterprise.staff.v1.UnlinkStaffResult
	(*ListStaffsParams_Filter)(nil),     // 14: moego.enterprise.staff.v1.ListStaffsParams.Filter
	nil,                                 // 15: moego.enterprise.staff.v1.ListStaffsResult.IdToStaffEmailsEntry
	nil,                                 // 16: moego.enterprise.staff.v1.ListStaffsResult.StaffToAssignTenantNumsEntry
	(*v1.CreateStaffProfile)(nil),       // 17: moego.models.enterprise.v1.CreateStaffProfile
	(*v1.ResourceDef)(nil),              // 18: moego.models.enterprise.v1.ResourceDef
	(*v11.SendInviteLinkParamsDef)(nil), // 19: moego.models.organization.v1.SendInviteLinkParamsDef
	(*v1.StaffModel)(nil),               // 20: moego.models.enterprise.v1.StaffModel
	(*v11.StaffEmailDef)(nil),           // 21: moego.models.organization.v1.StaffEmailDef
	(*v2.PaginationRequest)(nil),        // 22: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),                  // 23: moego.utils.v2.OrderBy
	(*v2.PaginationResponse)(nil),       // 24: moego.utils.v2.PaginationResponse
	(*v1.UpdateStaffProfile)(nil),       // 25: moego.models.enterprise.v1.UpdateStaffProfile
	(*v11.SendInviteLinkDef)(nil),       // 26: moego.models.organization.v1.SendInviteLinkDef
}
var file_moego_enterprise_staff_v1_staff_api_proto_depIdxs = []int32{
	17, // 0: moego.enterprise.staff.v1.CreateStaffParams.profile:type_name -> moego.models.enterprise.v1.CreateStaffProfile
	18, // 1: moego.enterprise.staff.v1.CreateStaffParams.resources:type_name -> moego.models.enterprise.v1.ResourceDef
	19, // 2: moego.enterprise.staff.v1.CreateStaffParams.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	20, // 3: moego.enterprise.staff.v1.CreateStaffResult.staff:type_name -> moego.models.enterprise.v1.StaffModel
	18, // 4: moego.enterprise.staff.v1.CreateStaffResult.resources:type_name -> moego.models.enterprise.v1.ResourceDef
	21, // 5: moego.enterprise.staff.v1.CreateStaffResult.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	22, // 6: moego.enterprise.staff.v1.ListStaffsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	23, // 7: moego.enterprise.staff.v1.ListStaffsParams.order_by:type_name -> moego.utils.v2.OrderBy
	14, // 8: moego.enterprise.staff.v1.ListStaffsParams.filter:type_name -> moego.enterprise.staff.v1.ListStaffsParams.Filter
	20, // 9: moego.enterprise.staff.v1.ListStaffsResult.staffs:type_name -> moego.models.enterprise.v1.StaffModel
	24, // 10: moego.enterprise.staff.v1.ListStaffsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	15, // 11: moego.enterprise.staff.v1.ListStaffsResult.id_to_staff_emails:type_name -> moego.enterprise.staff.v1.ListStaffsResult.IdToStaffEmailsEntry
	16, // 12: moego.enterprise.staff.v1.ListStaffsResult.staff_to_assign_tenant_nums:type_name -> moego.enterprise.staff.v1.ListStaffsResult.StaffToAssignTenantNumsEntry
	25, // 13: moego.enterprise.staff.v1.UpdateStaffParams.profile:type_name -> moego.models.enterprise.v1.UpdateStaffProfile
	18, // 14: moego.enterprise.staff.v1.UpdateStaffParams.resources:type_name -> moego.models.enterprise.v1.ResourceDef
	19, // 15: moego.enterprise.staff.v1.UpdateStaffParams.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	20, // 16: moego.enterprise.staff.v1.UpdateStaffResult.staff:type_name -> moego.models.enterprise.v1.StaffModel
	18, // 17: moego.enterprise.staff.v1.UpdateStaffResult.resources:type_name -> moego.models.enterprise.v1.ResourceDef
	21, // 18: moego.enterprise.staff.v1.UpdateStaffResult.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	20, // 19: moego.enterprise.staff.v1.GetStaffResult.staff:type_name -> moego.models.enterprise.v1.StaffModel
	18, // 20: moego.enterprise.staff.v1.GetStaffResult.resources:type_name -> moego.models.enterprise.v1.ResourceDef
	21, // 21: moego.enterprise.staff.v1.GetStaffResult.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	26, // 22: moego.enterprise.staff.v1.SendStaffInviteLinkParams.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkDef
	21, // 23: moego.enterprise.staff.v1.ListStaffsResult.IdToStaffEmailsEntry.value:type_name -> moego.models.organization.v1.StaffEmailDef
	0,  // 24: moego.enterprise.staff.v1.StaffService.CreateStaff:input_type -> moego.enterprise.staff.v1.CreateStaffParams
	4,  // 25: moego.enterprise.staff.v1.StaffService.UpdateStaff:input_type -> moego.enterprise.staff.v1.UpdateStaffParams
	6,  // 26: moego.enterprise.staff.v1.StaffService.GetStaff:input_type -> moego.enterprise.staff.v1.GetStaffParams
	8,  // 27: moego.enterprise.staff.v1.StaffService.DeleteStaff:input_type -> moego.enterprise.staff.v1.DeleteStaffParams
	2,  // 28: moego.enterprise.staff.v1.StaffService.ListStaffs:input_type -> moego.enterprise.staff.v1.ListStaffsParams
	10, // 29: moego.enterprise.staff.v1.StaffService.SendStaffInviteLink:input_type -> moego.enterprise.staff.v1.SendStaffInviteLinkParams
	12, // 30: moego.enterprise.staff.v1.StaffService.UnlinkStaff:input_type -> moego.enterprise.staff.v1.UnlinkStaffParams
	1,  // 31: moego.enterprise.staff.v1.StaffService.CreateStaff:output_type -> moego.enterprise.staff.v1.CreateStaffResult
	5,  // 32: moego.enterprise.staff.v1.StaffService.UpdateStaff:output_type -> moego.enterprise.staff.v1.UpdateStaffResult
	7,  // 33: moego.enterprise.staff.v1.StaffService.GetStaff:output_type -> moego.enterprise.staff.v1.GetStaffResult
	9,  // 34: moego.enterprise.staff.v1.StaffService.DeleteStaff:output_type -> moego.enterprise.staff.v1.DeleteStaffResult
	3,  // 35: moego.enterprise.staff.v1.StaffService.ListStaffs:output_type -> moego.enterprise.staff.v1.ListStaffsResult
	11, // 36: moego.enterprise.staff.v1.StaffService.SendStaffInviteLink:output_type -> moego.enterprise.staff.v1.SendStaffInviteLinkResult
	13, // 37: moego.enterprise.staff.v1.StaffService.UnlinkStaff:output_type -> moego.enterprise.staff.v1.UnlinkStaffResult
	31, // [31:38] is the sub-list for method output_type
	24, // [24:31] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_moego_enterprise_staff_v1_staff_api_proto_init() }
func file_moego_enterprise_staff_v1_staff_api_proto_init() {
	if File_moego_enterprise_staff_v1_staff_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendStaffInviteLinkParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendStaffInviteLinkResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlinkStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlinkStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffsParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_enterprise_staff_v1_staff_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_staff_v1_staff_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_staff_v1_staff_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_staff_v1_staff_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_staff_v1_staff_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_staff_v1_staff_api_proto = out.File
	file_moego_enterprise_staff_v1_staff_api_proto_rawDesc = nil
	file_moego_enterprise_staff_v1_staff_api_proto_goTypes = nil
	file_moego_enterprise_staff_v1_staff_api_proto_depIdxs = nil
}
