// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/account/v1/account_access_api.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountAccessServiceClient is the client API for AccountAccessService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountAccessServiceClient interface {
	// Logout the current account.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
	Logout(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accountAccessServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountAccessServiceClient(cc grpc.ClientConnInterface) AccountAccessServiceClient {
	return &accountAccessServiceClient{cc}
}

func (c *accountAccessServiceClient) Logout(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.enterprise.account.v1.AccountAccessService/Logout", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountAccessServiceServer is the server API for AccountAccessService service.
// All implementations must embed UnimplementedAccountAccessServiceServer
// for forward compatibility
type AccountAccessServiceServer interface {
	// Logout the current account.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
	Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccountAccessServiceServer()
}

// UnimplementedAccountAccessServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountAccessServiceServer struct {
}

func (UnimplementedAccountAccessServiceServer) Logout(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedAccountAccessServiceServer) mustEmbedUnimplementedAccountAccessServiceServer() {}

// UnsafeAccountAccessServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountAccessServiceServer will
// result in compilation errors.
type UnsafeAccountAccessServiceServer interface {
	mustEmbedUnimplementedAccountAccessServiceServer()
}

func RegisterAccountAccessServiceServer(s grpc.ServiceRegistrar, srv AccountAccessServiceServer) {
	s.RegisterService(&AccountAccessService_ServiceDesc, srv)
}

func _AccountAccessService_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountAccessServiceServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.account.v1.AccountAccessService/Logout",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountAccessServiceServer).Logout(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountAccessService_ServiceDesc is the grpc.ServiceDesc for AccountAccessService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountAccessService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.account.v1.AccountAccessService",
	HandlerType: (*AccountAccessServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Logout",
			Handler:    _AccountAccessService_Logout_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/account/v1/account_access_api.proto",
}
