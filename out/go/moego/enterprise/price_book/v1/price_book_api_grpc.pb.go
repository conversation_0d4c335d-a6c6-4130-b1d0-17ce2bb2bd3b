// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/price_book/v1/price_book_api.proto

package pricebookapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PriceBookServiceClient is the client API for PriceBookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PriceBookServiceClient interface {
	// list price books
	ListPriceBooks(ctx context.Context, in *ListPriceBooksParams, opts ...grpc.CallOption) (*ListPriceBooksResult, error)
	// create price book
	CreatePriceBook(ctx context.Context, in *CreatePriceBookParams, opts ...grpc.CallOption) (*CreatePriceBookResult, error)
	// update price book
	UpdatePriceBook(ctx context.Context, in *UpdatePriceBookParams, opts ...grpc.CallOption) (*UpdatePriceBookResult, error)
	// delete price book
	DeletePriceBook(ctx context.Context, in *DeletePriceBookParams, opts ...grpc.CallOption) (*DeletePriceBookResult, error)
	// duplicate price book
	DuplicatePriceBook(ctx context.Context, in *DuplicatePriceBookParams, opts ...grpc.CallOption) (*DuplicatePriceBookResult, error)
	// create and update existed categories
	SaveServiceCategories(ctx context.Context, in *SaveServiceCategoriesParams, opts ...grpc.CallOption) (*SaveServiceCategoriesResult, error)
	// list service categories
	ListServiceCategories(ctx context.Context, in *ListServiceCategoriesParams, opts ...grpc.CallOption) (*ListServiceCategoriesResult, error)
	// list pet breeds
	ListPetBreeds(ctx context.Context, in *ListPetBreedsParams, opts ...grpc.CallOption) (*ListPetBreedsResult, error)
	// list pet types
	ListPetTypes(ctx context.Context, in *ListPetTypesParams, opts ...grpc.CallOption) (*ListPetTypesResult, error)
	// create a service
	CreateService(ctx context.Context, in *CreateServiceParams, opts ...grpc.CallOption) (*CreateServiceResult, error)
	// get a service
	GetService(ctx context.Context, in *GetServiceParams, opts ...grpc.CallOption) (*GetServiceResult, error)
	// list services
	ListServices(ctx context.Context, in *ListServicesParams, opts ...grpc.CallOption) (*ListServicesResult, error)
	// update a service
	UpdateService(ctx context.Context, in *UpdateServiceParams, opts ...grpc.CallOption) (*UpdateServiceResult, error)
	// delete a service
	DeleteService(ctx context.Context, in *DeleteServiceParams, opts ...grpc.CallOption) (*DeleteServiceResult, error)
	// sort services
	SortServices(ctx context.Context, in *SortServicesParams, opts ...grpc.CallOption) (*SortServicesResult, error)
	// list service change histories
	ListServiceChangeHistories(ctx context.Context, in *ListServiceChangeHistoriesParams, opts ...grpc.CallOption) (*ListServiceChangeHistoriesResult, error)
	// list service changes
	ListServiceChanges(ctx context.Context, in *ListServiceChangesParams, opts ...grpc.CallOption) (*ListServiceChangesResult, error)
	// push service changes
	PushServiceChanges(ctx context.Context, in *PushServiceChangesParams, opts ...grpc.CallOption) (*PushServiceChangesResult, error)
	// create evaluation
	CreateEvaluation(ctx context.Context, in *CreateEvaluationParams, opts ...grpc.CallOption) (*CreateEvaluationResult, error)
	// update evaluation
	UpdateEvaluation(ctx context.Context, in *UpdateEvaluationParams, opts ...grpc.CallOption) (*UpdateEvaluationResult, error)
	// list evaluations
	ListEvaluations(ctx context.Context, in *ListEvaluationsParams, opts ...grpc.CallOption) (*ListEvaluationsResult, error)
	// sort evaluations
	SortEvaluations(ctx context.Context, in *SortEvaluationsParams, opts ...grpc.CallOption) (*SortEvaluationsResult, error)
	// push evaluation changes
	PushEvaluationChanges(ctx context.Context, in *PushEvaluationChangesParams, opts ...grpc.CallOption) (*PushEvaluationChangesResult, error)
	// create pricing rule
	CreatePricingRule(ctx context.Context, in *CreatePricingRuleParams, opts ...grpc.CallOption) (*CreatePricingRuleResult, error)
	// update pricing rule
	UpdatePricingRule(ctx context.Context, in *UpdatePricingRuleParams, opts ...grpc.CallOption) (*UpdatePricingRuleResult, error)
	// list pricing rules
	ListPricingRules(ctx context.Context, in *ListPricingRulesParams, opts ...grpc.CallOption) (*ListPricingRulesResult, error)
	// sort pricing rules
	SortPricingRules(ctx context.Context, in *SortPricingRulesParams, opts ...grpc.CallOption) (*SortPricingRulesResult, error)
	// push pricing rule changes
	PushPricingRuleChanges(ctx context.Context, in *PushPricingRuleChangesParams, opts ...grpc.CallOption) (*PushPricingRuleChangesResult, error)
	// create service charge
	CreateServiceCharge(ctx context.Context, in *CreateServiceChargeParams, opts ...grpc.CallOption) (*CreateServiceChargeResult, error)
	// update service charge
	UpdateServiceCharge(ctx context.Context, in *UpdateServiceChargeParams, opts ...grpc.CallOption) (*UpdateServiceChargeResult, error)
	// list service charges
	ListServiceCharges(ctx context.Context, in *ListServiceChargesParams, opts ...grpc.CallOption) (*ListServiceChargesResult, error)
	// todo delete service charge
	// rpc DeleteServiceCharge(DeleteServiceChargeParams) returns (DeleteServiceChargeResult);
	// sort service charges
	SortServiceCharges(ctx context.Context, in *SortServiceChargesParams, opts ...grpc.CallOption) (*SortServiceChargesResult, error)
	// push service charge changes
	PushServiceChargeChanges(ctx context.Context, in *PushServiceChargeChangesParams, opts ...grpc.CallOption) (*PushServiceChargeChangesResult, error)
}

type priceBookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPriceBookServiceClient(cc grpc.ClientConnInterface) PriceBookServiceClient {
	return &priceBookServiceClient{cc}
}

func (c *priceBookServiceClient) ListPriceBooks(ctx context.Context, in *ListPriceBooksParams, opts ...grpc.CallOption) (*ListPriceBooksResult, error) {
	out := new(ListPriceBooksResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListPriceBooks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreatePriceBook(ctx context.Context, in *CreatePriceBookParams, opts ...grpc.CallOption) (*CreatePriceBookResult, error) {
	out := new(CreatePriceBookResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/CreatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdatePriceBook(ctx context.Context, in *UpdatePriceBookParams, opts ...grpc.CallOption) (*UpdatePriceBookResult, error) {
	out := new(UpdatePriceBookResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/UpdatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DeletePriceBook(ctx context.Context, in *DeletePriceBookParams, opts ...grpc.CallOption) (*DeletePriceBookResult, error) {
	out := new(DeletePriceBookResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/DeletePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DuplicatePriceBook(ctx context.Context, in *DuplicatePriceBookParams, opts ...grpc.CallOption) (*DuplicatePriceBookResult, error) {
	out := new(DuplicatePriceBookResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/DuplicatePriceBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SaveServiceCategories(ctx context.Context, in *SaveServiceCategoriesParams, opts ...grpc.CallOption) (*SaveServiceCategoriesResult, error) {
	out := new(SaveServiceCategoriesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/SaveServiceCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceCategories(ctx context.Context, in *ListServiceCategoriesParams, opts ...grpc.CallOption) (*ListServiceCategoriesResult, error) {
	out := new(ListServiceCategoriesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPetBreeds(ctx context.Context, in *ListPetBreedsParams, opts ...grpc.CallOption) (*ListPetBreedsResult, error) {
	out := new(ListPetBreedsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListPetBreeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPetTypes(ctx context.Context, in *ListPetTypesParams, opts ...grpc.CallOption) (*ListPetTypesResult, error) {
	out := new(ListPetTypesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListPetTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateService(ctx context.Context, in *CreateServiceParams, opts ...grpc.CallOption) (*CreateServiceResult, error) {
	out := new(CreateServiceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/CreateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) GetService(ctx context.Context, in *GetServiceParams, opts ...grpc.CallOption) (*GetServiceResult, error) {
	out := new(GetServiceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/GetService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServices(ctx context.Context, in *ListServicesParams, opts ...grpc.CallOption) (*ListServicesResult, error) {
	out := new(ListServicesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateService(ctx context.Context, in *UpdateServiceParams, opts ...grpc.CallOption) (*UpdateServiceResult, error) {
	out := new(UpdateServiceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/UpdateService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) DeleteService(ctx context.Context, in *DeleteServiceParams, opts ...grpc.CallOption) (*DeleteServiceResult, error) {
	out := new(DeleteServiceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/DeleteService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortServices(ctx context.Context, in *SortServicesParams, opts ...grpc.CallOption) (*SortServicesResult, error) {
	out := new(SortServicesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/SortServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceChangeHistories(ctx context.Context, in *ListServiceChangeHistoriesParams, opts ...grpc.CallOption) (*ListServiceChangeHistoriesResult, error) {
	out := new(ListServiceChangeHistoriesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChangeHistories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceChanges(ctx context.Context, in *ListServiceChangesParams, opts ...grpc.CallOption) (*ListServiceChangesResult, error) {
	out := new(ListServiceChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushServiceChanges(ctx context.Context, in *PushServiceChangesParams, opts ...grpc.CallOption) (*PushServiceChangesResult, error) {
	out := new(PushServiceChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/PushServiceChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateEvaluation(ctx context.Context, in *CreateEvaluationParams, opts ...grpc.CallOption) (*CreateEvaluationResult, error) {
	out := new(CreateEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/CreateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateEvaluation(ctx context.Context, in *UpdateEvaluationParams, opts ...grpc.CallOption) (*UpdateEvaluationResult, error) {
	out := new(UpdateEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/UpdateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListEvaluations(ctx context.Context, in *ListEvaluationsParams, opts ...grpc.CallOption) (*ListEvaluationsResult, error) {
	out := new(ListEvaluationsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListEvaluations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortEvaluations(ctx context.Context, in *SortEvaluationsParams, opts ...grpc.CallOption) (*SortEvaluationsResult, error) {
	out := new(SortEvaluationsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/SortEvaluations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushEvaluationChanges(ctx context.Context, in *PushEvaluationChangesParams, opts ...grpc.CallOption) (*PushEvaluationChangesResult, error) {
	out := new(PushEvaluationChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/PushEvaluationChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreatePricingRule(ctx context.Context, in *CreatePricingRuleParams, opts ...grpc.CallOption) (*CreatePricingRuleResult, error) {
	out := new(CreatePricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/CreatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdatePricingRule(ctx context.Context, in *UpdatePricingRuleParams, opts ...grpc.CallOption) (*UpdatePricingRuleResult, error) {
	out := new(UpdatePricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/UpdatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListPricingRules(ctx context.Context, in *ListPricingRulesParams, opts ...grpc.CallOption) (*ListPricingRulesResult, error) {
	out := new(ListPricingRulesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortPricingRules(ctx context.Context, in *SortPricingRulesParams, opts ...grpc.CallOption) (*SortPricingRulesResult, error) {
	out := new(SortPricingRulesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/SortPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushPricingRuleChanges(ctx context.Context, in *PushPricingRuleChangesParams, opts ...grpc.CallOption) (*PushPricingRuleChangesResult, error) {
	out := new(PushPricingRuleChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/PushPricingRuleChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) CreateServiceCharge(ctx context.Context, in *CreateServiceChargeParams, opts ...grpc.CallOption) (*CreateServiceChargeResult, error) {
	out := new(CreateServiceChargeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/CreateServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) UpdateServiceCharge(ctx context.Context, in *UpdateServiceChargeParams, opts ...grpc.CallOption) (*UpdateServiceChargeResult, error) {
	out := new(UpdateServiceChargeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/UpdateServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) ListServiceCharges(ctx context.Context, in *ListServiceChargesParams, opts ...grpc.CallOption) (*ListServiceChargesResult, error) {
	out := new(ListServiceChargesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceCharges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) SortServiceCharges(ctx context.Context, in *SortServiceChargesParams, opts ...grpc.CallOption) (*SortServiceChargesResult, error) {
	out := new(SortServiceChargesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/SortServiceCharges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceBookServiceClient) PushServiceChargeChanges(ctx context.Context, in *PushServiceChargeChangesParams, opts ...grpc.CallOption) (*PushServiceChargeChangesResult, error) {
	out := new(PushServiceChargeChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.price_book.v1.PriceBookService/PushServiceChargeChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PriceBookServiceServer is the server API for PriceBookService service.
// All implementations must embed UnimplementedPriceBookServiceServer
// for forward compatibility
type PriceBookServiceServer interface {
	// list price books
	ListPriceBooks(context.Context, *ListPriceBooksParams) (*ListPriceBooksResult, error)
	// create price book
	CreatePriceBook(context.Context, *CreatePriceBookParams) (*CreatePriceBookResult, error)
	// update price book
	UpdatePriceBook(context.Context, *UpdatePriceBookParams) (*UpdatePriceBookResult, error)
	// delete price book
	DeletePriceBook(context.Context, *DeletePriceBookParams) (*DeletePriceBookResult, error)
	// duplicate price book
	DuplicatePriceBook(context.Context, *DuplicatePriceBookParams) (*DuplicatePriceBookResult, error)
	// create and update existed categories
	SaveServiceCategories(context.Context, *SaveServiceCategoriesParams) (*SaveServiceCategoriesResult, error)
	// list service categories
	ListServiceCategories(context.Context, *ListServiceCategoriesParams) (*ListServiceCategoriesResult, error)
	// list pet breeds
	ListPetBreeds(context.Context, *ListPetBreedsParams) (*ListPetBreedsResult, error)
	// list pet types
	ListPetTypes(context.Context, *ListPetTypesParams) (*ListPetTypesResult, error)
	// create a service
	CreateService(context.Context, *CreateServiceParams) (*CreateServiceResult, error)
	// get a service
	GetService(context.Context, *GetServiceParams) (*GetServiceResult, error)
	// list services
	ListServices(context.Context, *ListServicesParams) (*ListServicesResult, error)
	// update a service
	UpdateService(context.Context, *UpdateServiceParams) (*UpdateServiceResult, error)
	// delete a service
	DeleteService(context.Context, *DeleteServiceParams) (*DeleteServiceResult, error)
	// sort services
	SortServices(context.Context, *SortServicesParams) (*SortServicesResult, error)
	// list service change histories
	ListServiceChangeHistories(context.Context, *ListServiceChangeHistoriesParams) (*ListServiceChangeHistoriesResult, error)
	// list service changes
	ListServiceChanges(context.Context, *ListServiceChangesParams) (*ListServiceChangesResult, error)
	// push service changes
	PushServiceChanges(context.Context, *PushServiceChangesParams) (*PushServiceChangesResult, error)
	// create evaluation
	CreateEvaluation(context.Context, *CreateEvaluationParams) (*CreateEvaluationResult, error)
	// update evaluation
	UpdateEvaluation(context.Context, *UpdateEvaluationParams) (*UpdateEvaluationResult, error)
	// list evaluations
	ListEvaluations(context.Context, *ListEvaluationsParams) (*ListEvaluationsResult, error)
	// sort evaluations
	SortEvaluations(context.Context, *SortEvaluationsParams) (*SortEvaluationsResult, error)
	// push evaluation changes
	PushEvaluationChanges(context.Context, *PushEvaluationChangesParams) (*PushEvaluationChangesResult, error)
	// create pricing rule
	CreatePricingRule(context.Context, *CreatePricingRuleParams) (*CreatePricingRuleResult, error)
	// update pricing rule
	UpdatePricingRule(context.Context, *UpdatePricingRuleParams) (*UpdatePricingRuleResult, error)
	// list pricing rules
	ListPricingRules(context.Context, *ListPricingRulesParams) (*ListPricingRulesResult, error)
	// sort pricing rules
	SortPricingRules(context.Context, *SortPricingRulesParams) (*SortPricingRulesResult, error)
	// push pricing rule changes
	PushPricingRuleChanges(context.Context, *PushPricingRuleChangesParams) (*PushPricingRuleChangesResult, error)
	// create service charge
	CreateServiceCharge(context.Context, *CreateServiceChargeParams) (*CreateServiceChargeResult, error)
	// update service charge
	UpdateServiceCharge(context.Context, *UpdateServiceChargeParams) (*UpdateServiceChargeResult, error)
	// list service charges
	ListServiceCharges(context.Context, *ListServiceChargesParams) (*ListServiceChargesResult, error)
	// todo delete service charge
	// rpc DeleteServiceCharge(DeleteServiceChargeParams) returns (DeleteServiceChargeResult);
	// sort service charges
	SortServiceCharges(context.Context, *SortServiceChargesParams) (*SortServiceChargesResult, error)
	// push service charge changes
	PushServiceChargeChanges(context.Context, *PushServiceChargeChangesParams) (*PushServiceChargeChangesResult, error)
	mustEmbedUnimplementedPriceBookServiceServer()
}

// UnimplementedPriceBookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPriceBookServiceServer struct {
}

func (UnimplementedPriceBookServiceServer) ListPriceBooks(context.Context, *ListPriceBooksParams) (*ListPriceBooksResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPriceBooks not implemented")
}
func (UnimplementedPriceBookServiceServer) CreatePriceBook(context.Context, *CreatePriceBookParams) (*CreatePriceBookResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdatePriceBook(context.Context, *UpdatePriceBookParams) (*UpdatePriceBookResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) DeletePriceBook(context.Context, *DeletePriceBookParams) (*DeletePriceBookResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) DuplicatePriceBook(context.Context, *DuplicatePriceBookParams) (*DuplicatePriceBookResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DuplicatePriceBook not implemented")
}
func (UnimplementedPriceBookServiceServer) SaveServiceCategories(context.Context, *SaveServiceCategoriesParams) (*SaveServiceCategoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveServiceCategories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceCategories(context.Context, *ListServiceCategoriesParams) (*ListServiceCategoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceCategories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPetBreeds(context.Context, *ListPetBreedsParams) (*ListPetBreedsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBreeds not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPetTypes(context.Context, *ListPetTypesParams) (*ListPetTypesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetTypes not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateService(context.Context, *CreateServiceParams) (*CreateServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedPriceBookServiceServer) GetService(context.Context, *GetServiceParams) (*GetServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetService not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServices(context.Context, *ListServicesParams) (*ListServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServices not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateService(context.Context, *UpdateServiceParams) (*UpdateServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedPriceBookServiceServer) DeleteService(context.Context, *DeleteServiceParams) (*DeleteServiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedPriceBookServiceServer) SortServices(context.Context, *SortServicesParams) (*SortServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortServices not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceChangeHistories(context.Context, *ListServiceChangeHistoriesParams) (*ListServiceChangeHistoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceChangeHistories not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceChanges(context.Context, *ListServiceChangesParams) (*ListServiceChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) PushServiceChanges(context.Context, *PushServiceChangesParams) (*PushServiceChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushServiceChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateEvaluation(context.Context, *CreateEvaluationParams) (*CreateEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluation not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateEvaluation(context.Context, *UpdateEvaluationParams) (*UpdateEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluation not implemented")
}
func (UnimplementedPriceBookServiceServer) ListEvaluations(context.Context, *ListEvaluationsParams) (*ListEvaluationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEvaluations not implemented")
}
func (UnimplementedPriceBookServiceServer) SortEvaluations(context.Context, *SortEvaluationsParams) (*SortEvaluationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortEvaluations not implemented")
}
func (UnimplementedPriceBookServiceServer) PushEvaluationChanges(context.Context, *PushEvaluationChangesParams) (*PushEvaluationChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushEvaluationChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreatePricingRule(context.Context, *CreatePricingRuleParams) (*CreatePricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePricingRule not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdatePricingRule(context.Context, *UpdatePricingRuleParams) (*UpdatePricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePricingRule not implemented")
}
func (UnimplementedPriceBookServiceServer) ListPricingRules(context.Context, *ListPricingRulesParams) (*ListPricingRulesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPricingRules not implemented")
}
func (UnimplementedPriceBookServiceServer) SortPricingRules(context.Context, *SortPricingRulesParams) (*SortPricingRulesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPricingRules not implemented")
}
func (UnimplementedPriceBookServiceServer) PushPricingRuleChanges(context.Context, *PushPricingRuleChangesParams) (*PushPricingRuleChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPricingRuleChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) CreateServiceCharge(context.Context, *CreateServiceChargeParams) (*CreateServiceChargeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceCharge not implemented")
}
func (UnimplementedPriceBookServiceServer) UpdateServiceCharge(context.Context, *UpdateServiceChargeParams) (*UpdateServiceChargeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceCharge not implemented")
}
func (UnimplementedPriceBookServiceServer) ListServiceCharges(context.Context, *ListServiceChargesParams) (*ListServiceChargesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceCharges not implemented")
}
func (UnimplementedPriceBookServiceServer) SortServiceCharges(context.Context, *SortServiceChargesParams) (*SortServiceChargesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortServiceCharges not implemented")
}
func (UnimplementedPriceBookServiceServer) PushServiceChargeChanges(context.Context, *PushServiceChargeChangesParams) (*PushServiceChargeChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushServiceChargeChanges not implemented")
}
func (UnimplementedPriceBookServiceServer) mustEmbedUnimplementedPriceBookServiceServer() {}

// UnsafePriceBookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PriceBookServiceServer will
// result in compilation errors.
type UnsafePriceBookServiceServer interface {
	mustEmbedUnimplementedPriceBookServiceServer()
}

func RegisterPriceBookServiceServer(s grpc.ServiceRegistrar, srv PriceBookServiceServer) {
	s.RegisterService(&PriceBookService_ServiceDesc, srv)
}

func _PriceBookService_ListPriceBooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPriceBooksParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPriceBooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListPriceBooks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPriceBooks(ctx, req.(*ListPriceBooksParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePriceBookParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/CreatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreatePriceBook(ctx, req.(*CreatePriceBookParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePriceBookParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/UpdatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdatePriceBook(ctx, req.(*UpdatePriceBookParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DeletePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePriceBookParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DeletePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/DeletePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DeletePriceBook(ctx, req.(*DeletePriceBookParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DuplicatePriceBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DuplicatePriceBookParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DuplicatePriceBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/DuplicatePriceBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DuplicatePriceBook(ctx, req.(*DuplicatePriceBookParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SaveServiceCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveServiceCategoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SaveServiceCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/SaveServiceCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SaveServiceCategories(ctx, req.(*SaveServiceCategoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceCategoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListServiceCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceCategories(ctx, req.(*ListServiceCategoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPetBreeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBreedsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPetBreeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListPetBreeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPetBreeds(ctx, req.(*ListPetBreedsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPetTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetTypesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPetTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListPetTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPetTypes(ctx, req.(*ListPetTypesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/CreateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateService(ctx, req.(*CreateServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_GetService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).GetService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/GetService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).GetService(ctx, req.(*GetServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServices(ctx, req.(*ListServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/UpdateService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateService(ctx, req.(*UpdateServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/DeleteService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).DeleteService(ctx, req.(*DeleteServiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/SortServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortServices(ctx, req.(*SortServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceChangeHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChangeHistoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceChangeHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChangeHistories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceChangeHistories(ctx, req.(*ListServiceChangeHistoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceChanges(ctx, req.(*ListServiceChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushServiceChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushServiceChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushServiceChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/PushServiceChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushServiceChanges(ctx, req.(*PushServiceChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/CreateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateEvaluation(ctx, req.(*CreateEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/UpdateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateEvaluation(ctx, req.(*UpdateEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEvaluationsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListEvaluations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListEvaluations(ctx, req.(*ListEvaluationsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortEvaluations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortEvaluationsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortEvaluations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/SortEvaluations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortEvaluations(ctx, req.(*SortEvaluationsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushEvaluationChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushEvaluationChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushEvaluationChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/PushEvaluationChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushEvaluationChanges(ctx, req.(*PushEvaluationChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/CreatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreatePricingRule(ctx, req.(*CreatePricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/UpdatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdatePricingRule(ctx, req.(*UpdatePricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricingRulesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListPricingRules(ctx, req.(*ListPricingRulesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPricingRulesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/SortPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortPricingRules(ctx, req.(*SortPricingRulesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushPricingRuleChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPricingRuleChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushPricingRuleChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/PushPricingRuleChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushPricingRuleChanges(ctx, req.(*PushPricingRuleChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_CreateServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceChargeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).CreateServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/CreateServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).CreateServiceCharge(ctx, req.(*CreateServiceChargeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_UpdateServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceChargeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).UpdateServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/UpdateServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).UpdateServiceCharge(ctx, req.(*UpdateServiceChargeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_ListServiceCharges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChargesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).ListServiceCharges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/ListServiceCharges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).ListServiceCharges(ctx, req.(*ListServiceChargesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_SortServiceCharges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortServiceChargesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).SortServiceCharges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/SortServiceCharges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).SortServiceCharges(ctx, req.(*SortServiceChargesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceBookService_PushServiceChargeChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushServiceChargeChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceBookServiceServer).PushServiceChargeChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.price_book.v1.PriceBookService/PushServiceChargeChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceBookServiceServer).PushServiceChargeChanges(ctx, req.(*PushServiceChargeChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PriceBookService_ServiceDesc is the grpc.ServiceDesc for PriceBookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PriceBookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.price_book.v1.PriceBookService",
	HandlerType: (*PriceBookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPriceBooks",
			Handler:    _PriceBookService_ListPriceBooks_Handler,
		},
		{
			MethodName: "CreatePriceBook",
			Handler:    _PriceBookService_CreatePriceBook_Handler,
		},
		{
			MethodName: "UpdatePriceBook",
			Handler:    _PriceBookService_UpdatePriceBook_Handler,
		},
		{
			MethodName: "DeletePriceBook",
			Handler:    _PriceBookService_DeletePriceBook_Handler,
		},
		{
			MethodName: "DuplicatePriceBook",
			Handler:    _PriceBookService_DuplicatePriceBook_Handler,
		},
		{
			MethodName: "SaveServiceCategories",
			Handler:    _PriceBookService_SaveServiceCategories_Handler,
		},
		{
			MethodName: "ListServiceCategories",
			Handler:    _PriceBookService_ListServiceCategories_Handler,
		},
		{
			MethodName: "ListPetBreeds",
			Handler:    _PriceBookService_ListPetBreeds_Handler,
		},
		{
			MethodName: "ListPetTypes",
			Handler:    _PriceBookService_ListPetTypes_Handler,
		},
		{
			MethodName: "CreateService",
			Handler:    _PriceBookService_CreateService_Handler,
		},
		{
			MethodName: "GetService",
			Handler:    _PriceBookService_GetService_Handler,
		},
		{
			MethodName: "ListServices",
			Handler:    _PriceBookService_ListServices_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _PriceBookService_UpdateService_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _PriceBookService_DeleteService_Handler,
		},
		{
			MethodName: "SortServices",
			Handler:    _PriceBookService_SortServices_Handler,
		},
		{
			MethodName: "ListServiceChangeHistories",
			Handler:    _PriceBookService_ListServiceChangeHistories_Handler,
		},
		{
			MethodName: "ListServiceChanges",
			Handler:    _PriceBookService_ListServiceChanges_Handler,
		},
		{
			MethodName: "PushServiceChanges",
			Handler:    _PriceBookService_PushServiceChanges_Handler,
		},
		{
			MethodName: "CreateEvaluation",
			Handler:    _PriceBookService_CreateEvaluation_Handler,
		},
		{
			MethodName: "UpdateEvaluation",
			Handler:    _PriceBookService_UpdateEvaluation_Handler,
		},
		{
			MethodName: "ListEvaluations",
			Handler:    _PriceBookService_ListEvaluations_Handler,
		},
		{
			MethodName: "SortEvaluations",
			Handler:    _PriceBookService_SortEvaluations_Handler,
		},
		{
			MethodName: "PushEvaluationChanges",
			Handler:    _PriceBookService_PushEvaluationChanges_Handler,
		},
		{
			MethodName: "CreatePricingRule",
			Handler:    _PriceBookService_CreatePricingRule_Handler,
		},
		{
			MethodName: "UpdatePricingRule",
			Handler:    _PriceBookService_UpdatePricingRule_Handler,
		},
		{
			MethodName: "ListPricingRules",
			Handler:    _PriceBookService_ListPricingRules_Handler,
		},
		{
			MethodName: "SortPricingRules",
			Handler:    _PriceBookService_SortPricingRules_Handler,
		},
		{
			MethodName: "PushPricingRuleChanges",
			Handler:    _PriceBookService_PushPricingRuleChanges_Handler,
		},
		{
			MethodName: "CreateServiceCharge",
			Handler:    _PriceBookService_CreateServiceCharge_Handler,
		},
		{
			MethodName: "UpdateServiceCharge",
			Handler:    _PriceBookService_UpdateServiceCharge_Handler,
		},
		{
			MethodName: "ListServiceCharges",
			Handler:    _PriceBookService_ListServiceCharges_Handler,
		},
		{
			MethodName: "SortServiceCharges",
			Handler:    _PriceBookService_SortServiceCharges_Handler,
		},
		{
			MethodName: "PushServiceChargeChanges",
			Handler:    _PriceBookService_PushServiceChargeChanges_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/price_book/v1/price_book_api.proto",
}
