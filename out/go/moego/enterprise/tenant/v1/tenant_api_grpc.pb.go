// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/tenant/v1/tenant_api.proto

package tenantapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TenantServiceClient is the client API for TenantService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TenantServiceClient interface {
	// create tenant
	CreateTenant(ctx context.Context, in *CreateTenantParams, opts ...grpc.CallOption) (*CreateTenantResult, error)
	// update tenant
	UpdateTenant(ctx context.Context, in *UpdateTenantParams, opts ...grpc.CallOption) (*UpdateTenantResult, error)
	// get tenant
	GetTenant(ctx context.Context, in *GetTenantParams, opts ...grpc.CallOption) (*GetTenantResult, error)
	// get tenant list
	ListTenant(ctx context.Context, in *ListTenantParams, opts ...grpc.CallOption) (*ListTenantResult, error)
	// delete tenant
	DeleteTenant(ctx context.Context, in *DeleteTenantParams, opts ...grpc.CallOption) (*DeleteTenantResult, error)
	// list tenant groups
	ListTenantGroups(ctx context.Context, in *ListTenantGroupParams, opts ...grpc.CallOption) (*ListTenantGroupResult, error)
	// delete tenant group
	DeleteTenantGroup(ctx context.Context, in *DeleteTenantGroupParams, opts ...grpc.CallOption) (*DeleteTenantGroupResult, error)
	// list all tenant and group
	ListAllTenantAndGroup(ctx context.Context, in *ListAllTenantAndGroupParams, opts ...grpc.CallOption) (*ListAllTenantAndGroupResult, error)
}

type tenantServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTenantServiceClient(cc grpc.ClientConnInterface) TenantServiceClient {
	return &tenantServiceClient{cc}
}

func (c *tenantServiceClient) CreateTenant(ctx context.Context, in *CreateTenantParams, opts ...grpc.CallOption) (*CreateTenantResult, error) {
	out := new(CreateTenantResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/CreateTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) UpdateTenant(ctx context.Context, in *UpdateTenantParams, opts ...grpc.CallOption) (*UpdateTenantResult, error) {
	out := new(UpdateTenantResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/UpdateTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) GetTenant(ctx context.Context, in *GetTenantParams, opts ...grpc.CallOption) (*GetTenantResult, error) {
	out := new(GetTenantResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/GetTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListTenant(ctx context.Context, in *ListTenantParams, opts ...grpc.CallOption) (*ListTenantResult, error) {
	out := new(ListTenantResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/ListTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) DeleteTenant(ctx context.Context, in *DeleteTenantParams, opts ...grpc.CallOption) (*DeleteTenantResult, error) {
	out := new(DeleteTenantResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/DeleteTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListTenantGroups(ctx context.Context, in *ListTenantGroupParams, opts ...grpc.CallOption) (*ListTenantGroupResult, error) {
	out := new(ListTenantGroupResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/ListTenantGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) DeleteTenantGroup(ctx context.Context, in *DeleteTenantGroupParams, opts ...grpc.CallOption) (*DeleteTenantGroupResult, error) {
	out := new(DeleteTenantGroupResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/DeleteTenantGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListAllTenantAndGroup(ctx context.Context, in *ListAllTenantAndGroupParams, opts ...grpc.CallOption) (*ListAllTenantAndGroupResult, error) {
	out := new(ListAllTenantAndGroupResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.tenant.v1.TenantService/ListAllTenantAndGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TenantServiceServer is the server API for TenantService service.
// All implementations must embed UnimplementedTenantServiceServer
// for forward compatibility
type TenantServiceServer interface {
	// create tenant
	CreateTenant(context.Context, *CreateTenantParams) (*CreateTenantResult, error)
	// update tenant
	UpdateTenant(context.Context, *UpdateTenantParams) (*UpdateTenantResult, error)
	// get tenant
	GetTenant(context.Context, *GetTenantParams) (*GetTenantResult, error)
	// get tenant list
	ListTenant(context.Context, *ListTenantParams) (*ListTenantResult, error)
	// delete tenant
	DeleteTenant(context.Context, *DeleteTenantParams) (*DeleteTenantResult, error)
	// list tenant groups
	ListTenantGroups(context.Context, *ListTenantGroupParams) (*ListTenantGroupResult, error)
	// delete tenant group
	DeleteTenantGroup(context.Context, *DeleteTenantGroupParams) (*DeleteTenantGroupResult, error)
	// list all tenant and group
	ListAllTenantAndGroup(context.Context, *ListAllTenantAndGroupParams) (*ListAllTenantAndGroupResult, error)
	mustEmbedUnimplementedTenantServiceServer()
}

// UnimplementedTenantServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTenantServiceServer struct {
}

func (UnimplementedTenantServiceServer) CreateTenant(context.Context, *CreateTenantParams) (*CreateTenantResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTenant not implemented")
}
func (UnimplementedTenantServiceServer) UpdateTenant(context.Context, *UpdateTenantParams) (*UpdateTenantResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTenant not implemented")
}
func (UnimplementedTenantServiceServer) GetTenant(context.Context, *GetTenantParams) (*GetTenantResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTenant not implemented")
}
func (UnimplementedTenantServiceServer) ListTenant(context.Context, *ListTenantParams) (*ListTenantResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenant not implemented")
}
func (UnimplementedTenantServiceServer) DeleteTenant(context.Context, *DeleteTenantParams) (*DeleteTenantResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTenant not implemented")
}
func (UnimplementedTenantServiceServer) ListTenantGroups(context.Context, *ListTenantGroupParams) (*ListTenantGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantGroups not implemented")
}
func (UnimplementedTenantServiceServer) DeleteTenantGroup(context.Context, *DeleteTenantGroupParams) (*DeleteTenantGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTenantGroup not implemented")
}
func (UnimplementedTenantServiceServer) ListAllTenantAndGroup(context.Context, *ListAllTenantAndGroupParams) (*ListAllTenantAndGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllTenantAndGroup not implemented")
}
func (UnimplementedTenantServiceServer) mustEmbedUnimplementedTenantServiceServer() {}

// UnsafeTenantServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TenantServiceServer will
// result in compilation errors.
type UnsafeTenantServiceServer interface {
	mustEmbedUnimplementedTenantServiceServer()
}

func RegisterTenantServiceServer(s grpc.ServiceRegistrar, srv TenantServiceServer) {
	s.RegisterService(&TenantService_ServiceDesc, srv)
}

func _TenantService_CreateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).CreateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/CreateTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).CreateTenant(ctx, req.(*CreateTenantParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_UpdateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTenantParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).UpdateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/UpdateTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).UpdateTenant(ctx, req.(*UpdateTenantParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_GetTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTenantParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).GetTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/GetTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).GetTenant(ctx, req.(*GetTenantParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/ListTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListTenant(ctx, req.(*ListTenantParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_DeleteTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTenantParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).DeleteTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/DeleteTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).DeleteTenant(ctx, req.(*DeleteTenantParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListTenantGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantGroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListTenantGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/ListTenantGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListTenantGroups(ctx, req.(*ListTenantGroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_DeleteTenantGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTenantGroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).DeleteTenantGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/DeleteTenantGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).DeleteTenantGroup(ctx, req.(*DeleteTenantGroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListAllTenantAndGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllTenantAndGroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListAllTenantAndGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.tenant.v1.TenantService/ListAllTenantAndGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListAllTenantAndGroup(ctx, req.(*ListAllTenantAndGroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

// TenantService_ServiceDesc is the grpc.ServiceDesc for TenantService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TenantService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.tenant.v1.TenantService",
	HandlerType: (*TenantServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTenant",
			Handler:    _TenantService_CreateTenant_Handler,
		},
		{
			MethodName: "UpdateTenant",
			Handler:    _TenantService_UpdateTenant_Handler,
		},
		{
			MethodName: "GetTenant",
			Handler:    _TenantService_GetTenant_Handler,
		},
		{
			MethodName: "ListTenant",
			Handler:    _TenantService_ListTenant_Handler,
		},
		{
			MethodName: "DeleteTenant",
			Handler:    _TenantService_DeleteTenant_Handler,
		},
		{
			MethodName: "ListTenantGroups",
			Handler:    _TenantService_ListTenantGroups_Handler,
		},
		{
			MethodName: "DeleteTenantGroup",
			Handler:    _TenantService_DeleteTenantGroup_Handler,
		},
		{
			MethodName: "ListAllTenantAndGroup",
			Handler:    _TenantService_ListAllTenantAndGroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/tenant/v1/tenant_api.proto",
}
