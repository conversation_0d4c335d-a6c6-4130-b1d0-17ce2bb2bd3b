// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/report/v1/dashboard_api.proto

package reportapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Describe pages of dashboard
type QueryDashboardPagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tabs of query dashboard page
	Tabs []v2.DashboardPage_Tab `protobuf:"varint,1,rep,packed,name=tabs,proto3,enum=moego.models.reporting.v2.DashboardPage_Tab" json:"tabs,omitempty"`
	// tenant ids
	TenantsIds []uint64 `protobuf:"varint,2,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// Whether to fetch data for all tenants
	AllTenants bool `protobuf:"varint,3,opt,name=all_tenants,json=allTenants,proto3" json:"all_tenants,omitempty"`
}

func (x *QueryDashboardPagesRequest) Reset() {
	*x = QueryDashboardPagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDashboardPagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDashboardPagesRequest) ProtoMessage() {}

func (x *QueryDashboardPagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDashboardPagesRequest.ProtoReflect.Descriptor instead.
func (*QueryDashboardPagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_report_v1_dashboard_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryDashboardPagesRequest) GetTabs() []v2.DashboardPage_Tab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *QueryDashboardPagesRequest) GetTenantsIds() []uint64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *QueryDashboardPagesRequest) GetAllTenants() bool {
	if x != nil {
		return x.AllTenants
	}
	return false
}

// Describe pages of dashboard
type QueryDashboardPagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of dashboard pages
	DashboardPages []*v2.DashboardPage `protobuf:"bytes,1,rep,name=dashboard_pages,json=dashboardPages,proto3" json:"dashboard_pages,omitempty"`
	// The customized config
	CustomizedConfig *v2.TableCustomizedConfig `protobuf:"bytes,2,opt,name=customized_config,json=customizedConfig,proto3" json:"customized_config,omitempty"`
}

func (x *QueryDashboardPagesResponse) Reset() {
	*x = QueryDashboardPagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDashboardPagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDashboardPagesResponse) ProtoMessage() {}

func (x *QueryDashboardPagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDashboardPagesResponse.ProtoReflect.Descriptor instead.
func (*QueryDashboardPagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_report_v1_dashboard_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryDashboardPagesResponse) GetDashboardPages() []*v2.DashboardPage {
	if x != nil {
		return x.DashboardPages
	}
	return nil
}

func (x *QueryDashboardPagesResponse) GetCustomizedConfig() *v2.TableCustomizedConfig {
	if x != nil {
		return x.CustomizedConfig
	}
	return nil
}

// Describe a request to fetch dashboard diagram data
type FetchDashboardDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// current interval
	CurrentPeriod *interval.Interval `protobuf:"bytes,4,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous interval
	PreviousPeriod *interval.Interval `protobuf:"bytes,5,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// The tenant id
	TenantsIds []uint64 `protobuf:"varint,6,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// Whether to fetch data for all tenants
	AllTenants bool `protobuf:"varint,7,opt,name=all_tenants,json=allTenants,proto3" json:"all_tenants,omitempty"`
}

func (x *FetchDashboardDataRequest) Reset() {
	*x = FetchDashboardDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDashboardDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDashboardDataRequest) ProtoMessage() {}

func (x *FetchDashboardDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDashboardDataRequest.ProtoReflect.Descriptor instead.
func (*FetchDashboardDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_report_v1_dashboard_api_proto_rawDescGZIP(), []int{2}
}

func (x *FetchDashboardDataRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetTenantsIds() []uint64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetAllTenants() bool {
	if x != nil {
		return x.AllTenants
	}
	return false
}

// Describe a response to fetch dashboard diagram data
type FetchDashboardDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The dashboard diagram data
	DiagramData []*v2.DiagramData `protobuf:"bytes,1,rep,name=diagram_data,json=diagramData,proto3" json:"diagram_data,omitempty"`
}

func (x *FetchDashboardDataResponse) Reset() {
	*x = FetchDashboardDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDashboardDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDashboardDataResponse) ProtoMessage() {}

func (x *FetchDashboardDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDashboardDataResponse.ProtoReflect.Descriptor instead.
func (*FetchDashboardDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_report_v1_dashboard_api_proto_rawDescGZIP(), []int{3}
}

func (x *FetchDashboardDataResponse) GetDiagramData() []*v2.DiagramData {
	if x != nil {
		return x.DiagramData
	}
	return nil
}

var File_moego_enterprise_report_v1_dashboard_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_report_v1_dashboard_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc3, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x53, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x61, 0x62, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x04, 0x74, 0x61, 0x62, 0x73, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92,
	0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x6c, 0x6c,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0f, 0x64, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xbf, 0x02, 0x0a, 0x19, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x64, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92,
	0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x6c, 0x6c,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x67, 0x0a, 0x1a, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x32, 0xab, 0x02, 0x0a, 0x1a, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a,
	0x12, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_report_v1_dashboard_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_report_v1_dashboard_api_proto_rawDescData = file_moego_enterprise_report_v1_dashboard_api_proto_rawDesc
)

func file_moego_enterprise_report_v1_dashboard_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_report_v1_dashboard_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_report_v1_dashboard_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_report_v1_dashboard_api_proto_rawDescData)
	})
	return file_moego_enterprise_report_v1_dashboard_api_proto_rawDescData
}

var file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_enterprise_report_v1_dashboard_api_proto_goTypes = []interface{}{
	(*QueryDashboardPagesRequest)(nil),  // 0: moego.enterprise.report.v1.QueryDashboardPagesRequest
	(*QueryDashboardPagesResponse)(nil), // 1: moego.enterprise.report.v1.QueryDashboardPagesResponse
	(*FetchDashboardDataRequest)(nil),   // 2: moego.enterprise.report.v1.FetchDashboardDataRequest
	(*FetchDashboardDataResponse)(nil),  // 3: moego.enterprise.report.v1.FetchDashboardDataResponse
	(v2.DashboardPage_Tab)(0),           // 4: moego.models.reporting.v2.DashboardPage.Tab
	(*v2.DashboardPage)(nil),            // 5: moego.models.reporting.v2.DashboardPage
	(*v2.TableCustomizedConfig)(nil),    // 6: moego.models.reporting.v2.TableCustomizedConfig
	(*interval.Interval)(nil),           // 7: google.type.Interval
	(*v2.DiagramData)(nil),              // 8: moego.models.reporting.v2.DiagramData
}
var file_moego_enterprise_report_v1_dashboard_api_proto_depIdxs = []int32{
	4, // 0: moego.enterprise.report.v1.QueryDashboardPagesRequest.tabs:type_name -> moego.models.reporting.v2.DashboardPage.Tab
	5, // 1: moego.enterprise.report.v1.QueryDashboardPagesResponse.dashboard_pages:type_name -> moego.models.reporting.v2.DashboardPage
	6, // 2: moego.enterprise.report.v1.QueryDashboardPagesResponse.customized_config:type_name -> moego.models.reporting.v2.TableCustomizedConfig
	7, // 3: moego.enterprise.report.v1.FetchDashboardDataRequest.current_period:type_name -> google.type.Interval
	7, // 4: moego.enterprise.report.v1.FetchDashboardDataRequest.previous_period:type_name -> google.type.Interval
	8, // 5: moego.enterprise.report.v1.FetchDashboardDataResponse.diagram_data:type_name -> moego.models.reporting.v2.DiagramData
	0, // 6: moego.enterprise.report.v1.EnterpriseDashboardService.QueryDashboardPages:input_type -> moego.enterprise.report.v1.QueryDashboardPagesRequest
	2, // 7: moego.enterprise.report.v1.EnterpriseDashboardService.FetchDashboardData:input_type -> moego.enterprise.report.v1.FetchDashboardDataRequest
	1, // 8: moego.enterprise.report.v1.EnterpriseDashboardService.QueryDashboardPages:output_type -> moego.enterprise.report.v1.QueryDashboardPagesResponse
	3, // 9: moego.enterprise.report.v1.EnterpriseDashboardService.FetchDashboardData:output_type -> moego.enterprise.report.v1.FetchDashboardDataResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_enterprise_report_v1_dashboard_api_proto_init() }
func file_moego_enterprise_report_v1_dashboard_api_proto_init() {
	if File_moego_enterprise_report_v1_dashboard_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDashboardPagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDashboardPagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDashboardDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDashboardDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_report_v1_dashboard_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_report_v1_dashboard_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_report_v1_dashboard_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_report_v1_dashboard_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_report_v1_dashboard_api_proto = out.File
	file_moego_enterprise_report_v1_dashboard_api_proto_rawDesc = nil
	file_moego_enterprise_report_v1_dashboard_api_proto_goTypes = nil
	file_moego_enterprise_report_v1_dashboard_api_proto_depIdxs = nil
}
