// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/loyalty/v1/loyalty_api.proto

package loyaltyapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// LoyaltyServiceClient is the client API for LoyaltyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LoyaltyServiceClient interface {
	// create membership
	CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error)
	// list memberships
	ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error)
	// delete membership
	DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error)
	// push membership changes
	PushMembershipChanges(ctx context.Context, in *PushMembershipChangesParams, opts ...grpc.CallOption) (*PushMembershipChangesResult, error)
	// create package
	CreatePackage(ctx context.Context, in *CreatePackageParams, opts ...grpc.CallOption) (*CreatePackageResult, error)
	// list packages
	ListPackages(ctx context.Context, in *ListPackagesParams, opts ...grpc.CallOption) (*ListPackagesResult, error)
	// update package
	UpdatePackage(ctx context.Context, in *UpdatePackageParams, opts ...grpc.CallOption) (*UpdatePackageResult, error)
	// delete package
	DeletePackage(ctx context.Context, in *DeletePackageParams, opts ...grpc.CallOption) (*DeletePackageResult, error)
	// push package changes
	PushPackageChanges(ctx context.Context, in *PushPackageChangesParams, opts ...grpc.CallOption) (*PushPackageChangesResult, error)
	// create discount
	CreateDiscount(ctx context.Context, in *CreateDiscountParams, opts ...grpc.CallOption) (*CreateDiscountResult, error)
	// list discounts
	ListDiscounts(ctx context.Context, in *ListDiscountsParams, opts ...grpc.CallOption) (*ListDiscountsResult, error)
	// update discount
	UpdateDiscount(ctx context.Context, in *UpdateDiscountParams, opts ...grpc.CallOption) (*UpdateDiscountResult, error)
	// delete discount
	DeleteDiscount(ctx context.Context, in *DeleteDiscountParams, opts ...grpc.CallOption) (*DeleteDiscountResult, error)
	// generate discount
	GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeParams, opts ...grpc.CallOption) (*GenerateDiscountCodeResult, error)
	// push discount changes
	PushDiscountChanges(ctx context.Context, in *PushDiscountChangesParams, opts ...grpc.CallOption) (*PushDiscountChangesResult, error)
}

type loyaltyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLoyaltyServiceClient(cc grpc.ClientConnInterface) LoyaltyServiceClient {
	return &loyaltyServiceClient{cc}
}

func (c *loyaltyServiceClient) CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error) {
	out := new(CreateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/CreateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error) {
	out := new(ListMembershipsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/ListMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error) {
	out := new(UpdateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/UpdateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error) {
	out := new(DeleteMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/DeleteMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushMembershipChanges(ctx context.Context, in *PushMembershipChangesParams, opts ...grpc.CallOption) (*PushMembershipChangesResult, error) {
	out := new(PushMembershipChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/PushMembershipChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) CreatePackage(ctx context.Context, in *CreatePackageParams, opts ...grpc.CallOption) (*CreatePackageResult, error) {
	out := new(CreatePackageResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/CreatePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListPackages(ctx context.Context, in *ListPackagesParams, opts ...grpc.CallOption) (*ListPackagesResult, error) {
	out := new(ListPackagesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/ListPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdatePackage(ctx context.Context, in *UpdatePackageParams, opts ...grpc.CallOption) (*UpdatePackageResult, error) {
	out := new(UpdatePackageResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/UpdatePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeletePackage(ctx context.Context, in *DeletePackageParams, opts ...grpc.CallOption) (*DeletePackageResult, error) {
	out := new(DeletePackageResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/DeletePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushPackageChanges(ctx context.Context, in *PushPackageChangesParams, opts ...grpc.CallOption) (*PushPackageChangesResult, error) {
	out := new(PushPackageChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/PushPackageChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) CreateDiscount(ctx context.Context, in *CreateDiscountParams, opts ...grpc.CallOption) (*CreateDiscountResult, error) {
	out := new(CreateDiscountResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/CreateDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) ListDiscounts(ctx context.Context, in *ListDiscountsParams, opts ...grpc.CallOption) (*ListDiscountsResult, error) {
	out := new(ListDiscountsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/ListDiscounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) UpdateDiscount(ctx context.Context, in *UpdateDiscountParams, opts ...grpc.CallOption) (*UpdateDiscountResult, error) {
	out := new(UpdateDiscountResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/UpdateDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) DeleteDiscount(ctx context.Context, in *DeleteDiscountParams, opts ...grpc.CallOption) (*DeleteDiscountResult, error) {
	out := new(DeleteDiscountResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/DeleteDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeParams, opts ...grpc.CallOption) (*GenerateDiscountCodeResult, error) {
	out := new(GenerateDiscountCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/GenerateDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loyaltyServiceClient) PushDiscountChanges(ctx context.Context, in *PushDiscountChangesParams, opts ...grpc.CallOption) (*PushDiscountChangesResult, error) {
	out := new(PushDiscountChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.loyalty.v1.LoyaltyService/PushDiscountChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LoyaltyServiceServer is the server API for LoyaltyService service.
// All implementations must embed UnimplementedLoyaltyServiceServer
// for forward compatibility
type LoyaltyServiceServer interface {
	// create membership
	CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error)
	// list memberships
	ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error)
	// delete membership
	DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error)
	// push membership changes
	PushMembershipChanges(context.Context, *PushMembershipChangesParams) (*PushMembershipChangesResult, error)
	// create package
	CreatePackage(context.Context, *CreatePackageParams) (*CreatePackageResult, error)
	// list packages
	ListPackages(context.Context, *ListPackagesParams) (*ListPackagesResult, error)
	// update package
	UpdatePackage(context.Context, *UpdatePackageParams) (*UpdatePackageResult, error)
	// delete package
	DeletePackage(context.Context, *DeletePackageParams) (*DeletePackageResult, error)
	// push package changes
	PushPackageChanges(context.Context, *PushPackageChangesParams) (*PushPackageChangesResult, error)
	// create discount
	CreateDiscount(context.Context, *CreateDiscountParams) (*CreateDiscountResult, error)
	// list discounts
	ListDiscounts(context.Context, *ListDiscountsParams) (*ListDiscountsResult, error)
	// update discount
	UpdateDiscount(context.Context, *UpdateDiscountParams) (*UpdateDiscountResult, error)
	// delete discount
	DeleteDiscount(context.Context, *DeleteDiscountParams) (*DeleteDiscountResult, error)
	// generate discount
	GenerateDiscountCode(context.Context, *GenerateDiscountCodeParams) (*GenerateDiscountCodeResult, error)
	// push discount changes
	PushDiscountChanges(context.Context, *PushDiscountChangesParams) (*PushDiscountChangesResult, error)
	mustEmbedUnimplementedLoyaltyServiceServer()
}

// UnimplementedLoyaltyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLoyaltyServiceServer struct {
}

func (UnimplementedLoyaltyServiceServer) CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemberships not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMembership not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushMembershipChanges(context.Context, *PushMembershipChangesParams) (*PushMembershipChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMembershipChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) CreatePackage(context.Context, *CreatePackageParams) (*CreatePackageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListPackages(context.Context, *ListPackagesParams) (*ListPackagesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPackages not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdatePackage(context.Context, *UpdatePackageParams) (*UpdatePackageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeletePackage(context.Context, *DeletePackageParams) (*DeletePackageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePackage not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushPackageChanges(context.Context, *PushPackageChangesParams) (*PushPackageChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPackageChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) CreateDiscount(context.Context, *CreateDiscountParams) (*CreateDiscountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) ListDiscounts(context.Context, *ListDiscountsParams) (*ListDiscountsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDiscounts not implemented")
}
func (UnimplementedLoyaltyServiceServer) UpdateDiscount(context.Context, *UpdateDiscountParams) (*UpdateDiscountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) DeleteDiscount(context.Context, *DeleteDiscountParams) (*DeleteDiscountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDiscount not implemented")
}
func (UnimplementedLoyaltyServiceServer) GenerateDiscountCode(context.Context, *GenerateDiscountCodeParams) (*GenerateDiscountCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateDiscountCode not implemented")
}
func (UnimplementedLoyaltyServiceServer) PushDiscountChanges(context.Context, *PushDiscountChangesParams) (*PushDiscountChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushDiscountChanges not implemented")
}
func (UnimplementedLoyaltyServiceServer) mustEmbedUnimplementedLoyaltyServiceServer() {}

// UnsafeLoyaltyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LoyaltyServiceServer will
// result in compilation errors.
type UnsafeLoyaltyServiceServer interface {
	mustEmbedUnimplementedLoyaltyServiceServer()
}

func RegisterLoyaltyServiceServer(s grpc.ServiceRegistrar, srv LoyaltyServiceServer) {
	s.RegisterService(&LoyaltyService_ServiceDesc, srv)
}

func _LoyaltyService_CreateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/CreateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreateMembership(ctx, req.(*CreateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/ListMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListMemberships(ctx, req.(*ListMembershipsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/UpdateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdateMembership(ctx, req.(*UpdateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeleteMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeleteMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/DeleteMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeleteMembership(ctx, req.(*DeleteMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushMembershipChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMembershipChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushMembershipChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/PushMembershipChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushMembershipChanges(ctx, req.(*PushMembershipChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_CreatePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePackageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreatePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/CreatePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreatePackage(ctx, req.(*CreatePackageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPackagesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/ListPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListPackages(ctx, req.(*ListPackagesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdatePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePackageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdatePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/UpdatePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdatePackage(ctx, req.(*UpdatePackageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeletePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePackageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeletePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/DeletePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeletePackage(ctx, req.(*DeletePackageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushPackageChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPackageChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushPackageChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/PushPackageChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushPackageChanges(ctx, req.(*PushPackageChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_CreateDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDiscountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).CreateDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/CreateDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).CreateDiscount(ctx, req.(*CreateDiscountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_ListDiscounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDiscountsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).ListDiscounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/ListDiscounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).ListDiscounts(ctx, req.(*ListDiscountsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_UpdateDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDiscountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).UpdateDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/UpdateDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).UpdateDiscount(ctx, req.(*UpdateDiscountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_DeleteDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDiscountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).DeleteDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/DeleteDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).DeleteDiscount(ctx, req.(*DeleteDiscountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_GenerateDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateDiscountCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).GenerateDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/GenerateDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).GenerateDiscountCode(ctx, req.(*GenerateDiscountCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoyaltyService_PushDiscountChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushDiscountChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoyaltyServiceServer).PushDiscountChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.loyalty.v1.LoyaltyService/PushDiscountChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoyaltyServiceServer).PushDiscountChanges(ctx, req.(*PushDiscountChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// LoyaltyService_ServiceDesc is the grpc.ServiceDesc for LoyaltyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LoyaltyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.loyalty.v1.LoyaltyService",
	HandlerType: (*LoyaltyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMembership",
			Handler:    _LoyaltyService_CreateMembership_Handler,
		},
		{
			MethodName: "ListMemberships",
			Handler:    _LoyaltyService_ListMemberships_Handler,
		},
		{
			MethodName: "UpdateMembership",
			Handler:    _LoyaltyService_UpdateMembership_Handler,
		},
		{
			MethodName: "DeleteMembership",
			Handler:    _LoyaltyService_DeleteMembership_Handler,
		},
		{
			MethodName: "PushMembershipChanges",
			Handler:    _LoyaltyService_PushMembershipChanges_Handler,
		},
		{
			MethodName: "CreatePackage",
			Handler:    _LoyaltyService_CreatePackage_Handler,
		},
		{
			MethodName: "ListPackages",
			Handler:    _LoyaltyService_ListPackages_Handler,
		},
		{
			MethodName: "UpdatePackage",
			Handler:    _LoyaltyService_UpdatePackage_Handler,
		},
		{
			MethodName: "DeletePackage",
			Handler:    _LoyaltyService_DeletePackage_Handler,
		},
		{
			MethodName: "PushPackageChanges",
			Handler:    _LoyaltyService_PushPackageChanges_Handler,
		},
		{
			MethodName: "CreateDiscount",
			Handler:    _LoyaltyService_CreateDiscount_Handler,
		},
		{
			MethodName: "ListDiscounts",
			Handler:    _LoyaltyService_ListDiscounts_Handler,
		},
		{
			MethodName: "UpdateDiscount",
			Handler:    _LoyaltyService_UpdateDiscount_Handler,
		},
		{
			MethodName: "DeleteDiscount",
			Handler:    _LoyaltyService_DeleteDiscount_Handler,
		},
		{
			MethodName: "GenerateDiscountCode",
			Handler:    _LoyaltyService_GenerateDiscountCode_Handler,
		},
		{
			MethodName: "PushDiscountChanges",
			Handler:    _LoyaltyService_PushDiscountChanges_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/loyalty/v1/loyalty_api.proto",
}
