// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/appointment/v1/appointment_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentServiceClient is the client API for AppointmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentServiceClient interface {
	// List appointments by filter, required c-side account session
	ListAppointments(ctx context.Context, in *ListAppointmentsParams, opts ...grpc.CallOption) (*ListAppointmentsResult, error)
	// Get last finished appointment
	// Only the most recent appointment prior to the current time is given
	GetLastFinishedAppointment(ctx context.Context, in *GetLastFinishedAppointmentParams, opts ...grpc.CallOption) (*GetLastFinishedAppointmentResult, error)
	// List upcoming day appointments by day for the day closest to the current time.
	ListUpcomingDayAppointments(ctx context.Context, in *ListUpcomingDayAppointmentsParams, opts ...grpc.CallOption) (*ListUpcomingDayAppointmentsResult, error)
	// List all appointments for today
	// Contains status: pending, ongoing, finished
	ListTodayAppointments(ctx context.Context, in *ListTodayAppointmentsParams, opts ...grpc.CallOption) (*ListTodayAppointmentsResult, error)
	// List pending appointments
	// Includes only appointments that have been pending for the next day
	ListPendingDayAppointments(ctx context.Context, in *ListPendingDayAppointmentsParams, opts ...grpc.CallOption) (*ListPendingDayAppointmentsResult, error)
	// Get appointment detail
	GetAppointment(ctx context.Context, in *GetAppointmentParams, opts ...grpc.CallOption) (*GetAppointmentResult, error)
	// Reschedule appointment
	RescheduleAppointment(ctx context.Context, in *RescheduleAppointmentParams, opts ...grpc.CallOption) (*RescheduleAppointmentResult, error)
	// Cancel appointment
	CancelAppointment(ctx context.Context, in *CancelAppointmentParams, opts ...grpc.CallOption) (*CancelAppointmentResult, error)
	// Confirm appointment
	ConfirmAppointment(ctx context.Context, in *ConfirmAppointmentParams, opts ...grpc.CallOption) (*ConfirmAppointmentResult, error)
}

type appointmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentServiceClient(cc grpc.ClientConnInterface) AppointmentServiceClient {
	return &appointmentServiceClient{cc}
}

func (c *appointmentServiceClient) ListAppointments(ctx context.Context, in *ListAppointmentsParams, opts ...grpc.CallOption) (*ListAppointmentsResult, error) {
	out := new(ListAppointmentsResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/ListAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetLastFinishedAppointment(ctx context.Context, in *GetLastFinishedAppointmentParams, opts ...grpc.CallOption) (*GetLastFinishedAppointmentResult, error) {
	out := new(GetLastFinishedAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/GetLastFinishedAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListUpcomingDayAppointments(ctx context.Context, in *ListUpcomingDayAppointmentsParams, opts ...grpc.CallOption) (*ListUpcomingDayAppointmentsResult, error) {
	out := new(ListUpcomingDayAppointmentsResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/ListUpcomingDayAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListTodayAppointments(ctx context.Context, in *ListTodayAppointmentsParams, opts ...grpc.CallOption) (*ListTodayAppointmentsResult, error) {
	out := new(ListTodayAppointmentsResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/ListTodayAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListPendingDayAppointments(ctx context.Context, in *ListPendingDayAppointmentsParams, opts ...grpc.CallOption) (*ListPendingDayAppointmentsResult, error) {
	out := new(ListPendingDayAppointmentsResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/ListPendingDayAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointment(ctx context.Context, in *GetAppointmentParams, opts ...grpc.CallOption) (*GetAppointmentResult, error) {
	out := new(GetAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/GetAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) RescheduleAppointment(ctx context.Context, in *RescheduleAppointmentParams, opts ...grpc.CallOption) (*RescheduleAppointmentResult, error) {
	out := new(RescheduleAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/RescheduleAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CancelAppointment(ctx context.Context, in *CancelAppointmentParams, opts ...grpc.CallOption) (*CancelAppointmentResult, error) {
	out := new(CancelAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/CancelAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ConfirmAppointment(ctx context.Context, in *ConfirmAppointmentParams, opts ...grpc.CallOption) (*ConfirmAppointmentResult, error) {
	out := new(ConfirmAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.AppointmentService/ConfirmAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentServiceServer is the server API for AppointmentService service.
// All implementations must embed UnimplementedAppointmentServiceServer
// for forward compatibility
type AppointmentServiceServer interface {
	// List appointments by filter, required c-side account session
	ListAppointments(context.Context, *ListAppointmentsParams) (*ListAppointmentsResult, error)
	// Get last finished appointment
	// Only the most recent appointment prior to the current time is given
	GetLastFinishedAppointment(context.Context, *GetLastFinishedAppointmentParams) (*GetLastFinishedAppointmentResult, error)
	// List upcoming day appointments by day for the day closest to the current time.
	ListUpcomingDayAppointments(context.Context, *ListUpcomingDayAppointmentsParams) (*ListUpcomingDayAppointmentsResult, error)
	// List all appointments for today
	// Contains status: pending, ongoing, finished
	ListTodayAppointments(context.Context, *ListTodayAppointmentsParams) (*ListTodayAppointmentsResult, error)
	// List pending appointments
	// Includes only appointments that have been pending for the next day
	ListPendingDayAppointments(context.Context, *ListPendingDayAppointmentsParams) (*ListPendingDayAppointmentsResult, error)
	// Get appointment detail
	GetAppointment(context.Context, *GetAppointmentParams) (*GetAppointmentResult, error)
	// Reschedule appointment
	RescheduleAppointment(context.Context, *RescheduleAppointmentParams) (*RescheduleAppointmentResult, error)
	// Cancel appointment
	CancelAppointment(context.Context, *CancelAppointmentParams) (*CancelAppointmentResult, error)
	// Confirm appointment
	ConfirmAppointment(context.Context, *ConfirmAppointmentParams) (*ConfirmAppointmentResult, error)
	mustEmbedUnimplementedAppointmentServiceServer()
}

// UnimplementedAppointmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentServiceServer struct {
}

func (UnimplementedAppointmentServiceServer) ListAppointments(context.Context, *ListAppointmentsParams) (*ListAppointmentsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) GetLastFinishedAppointment(context.Context, *GetLastFinishedAppointmentParams) (*GetLastFinishedAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastFinishedAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) ListUpcomingDayAppointments(context.Context, *ListUpcomingDayAppointmentsParams) (*ListUpcomingDayAppointmentsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUpcomingDayAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) ListTodayAppointments(context.Context, *ListTodayAppointmentsParams) (*ListTodayAppointmentsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTodayAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) ListPendingDayAppointments(context.Context, *ListPendingDayAppointmentsParams) (*ListPendingDayAppointmentsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPendingDayAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointment(context.Context, *GetAppointmentParams) (*GetAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) RescheduleAppointment(context.Context, *RescheduleAppointmentParams) (*RescheduleAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) CancelAppointment(context.Context, *CancelAppointmentParams) (*CancelAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) ConfirmAppointment(context.Context, *ConfirmAppointmentParams) (*ConfirmAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) mustEmbedUnimplementedAppointmentServiceServer() {}

// UnsafeAppointmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentServiceServer will
// result in compilation errors.
type UnsafeAppointmentServiceServer interface {
	mustEmbedUnimplementedAppointmentServiceServer()
}

func RegisterAppointmentServiceServer(s grpc.ServiceRegistrar, srv AppointmentServiceServer) {
	s.RegisterService(&AppointmentService_ServiceDesc, srv)
}

func _AppointmentService_ListAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/ListAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListAppointments(ctx, req.(*ListAppointmentsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetLastFinishedAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastFinishedAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetLastFinishedAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/GetLastFinishedAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetLastFinishedAppointment(ctx, req.(*GetLastFinishedAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListUpcomingDayAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUpcomingDayAppointmentsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListUpcomingDayAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/ListUpcomingDayAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListUpcomingDayAppointments(ctx, req.(*ListUpcomingDayAppointmentsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListTodayAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTodayAppointmentsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListTodayAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/ListTodayAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListTodayAppointments(ctx, req.(*ListTodayAppointmentsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListPendingDayAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPendingDayAppointmentsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListPendingDayAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/ListPendingDayAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListPendingDayAppointments(ctx, req.(*ListPendingDayAppointmentsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/GetAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, req.(*GetAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_RescheduleAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).RescheduleAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/RescheduleAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).RescheduleAppointment(ctx, req.(*RescheduleAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CancelAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CancelAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/CancelAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CancelAppointment(ctx, req.(*CancelAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ConfirmAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ConfirmAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.AppointmentService/ConfirmAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ConfirmAppointment(ctx, req.(*ConfirmAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentService_ServiceDesc is the grpc.ServiceDesc for AppointmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.appointment.v1.AppointmentService",
	HandlerType: (*AppointmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAppointments",
			Handler:    _AppointmentService_ListAppointments_Handler,
		},
		{
			MethodName: "GetLastFinishedAppointment",
			Handler:    _AppointmentService_GetLastFinishedAppointment_Handler,
		},
		{
			MethodName: "ListUpcomingDayAppointments",
			Handler:    _AppointmentService_ListUpcomingDayAppointments_Handler,
		},
		{
			MethodName: "ListTodayAppointments",
			Handler:    _AppointmentService_ListTodayAppointments_Handler,
		},
		{
			MethodName: "ListPendingDayAppointments",
			Handler:    _AppointmentService_ListPendingDayAppointments_Handler,
		},
		{
			MethodName: "GetAppointment",
			Handler:    _AppointmentService_GetAppointment_Handler,
		},
		{
			MethodName: "RescheduleAppointment",
			Handler:    _AppointmentService_RescheduleAppointment_Handler,
		},
		{
			MethodName: "CancelAppointment",
			Handler:    _AppointmentService_CancelAppointment_Handler,
		},
		{
			MethodName: "ConfirmAppointment",
			Handler:    _AppointmentService_ConfirmAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/appointment/v1/appointment_api.proto",
}
