// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/marketing/v1/discount_code_api.proto

package marketingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DiscountCodeServiceClient is the client API for DiscountCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DiscountCodeServiceClient interface {
	// Get the OB Discount Code available for the specified business
	GetDiscountCodeConfig(ctx context.Context, in *GetDiscountCodeConfigParams, opts ...grpc.CallOption) (*GetDiscountCodeConfigResult, error)
	// Verify whether the discount code is available
	CheckDiscountCode(ctx context.Context, in *CheckDiscountCodeParams, opts ...grpc.CallOption) (*CheckDiscountCodeResult, error)
}

type discountCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiscountCodeServiceClient(cc grpc.ClientConnInterface) DiscountCodeServiceClient {
	return &discountCodeServiceClient{cc}
}

func (c *discountCodeServiceClient) GetDiscountCodeConfig(ctx context.Context, in *GetDiscountCodeConfigParams, opts ...grpc.CallOption) (*GetDiscountCodeConfigResult, error) {
	out := new(GetDiscountCodeConfigResult)
	err := c.cc.Invoke(ctx, "/moego.client.marketing.v1.DiscountCodeService/GetDiscountCodeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) CheckDiscountCode(ctx context.Context, in *CheckDiscountCodeParams, opts ...grpc.CallOption) (*CheckDiscountCodeResult, error) {
	out := new(CheckDiscountCodeResult)
	err := c.cc.Invoke(ctx, "/moego.client.marketing.v1.DiscountCodeService/CheckDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiscountCodeServiceServer is the server API for DiscountCodeService service.
// All implementations must embed UnimplementedDiscountCodeServiceServer
// for forward compatibility
type DiscountCodeServiceServer interface {
	// Get the OB Discount Code available for the specified business
	GetDiscountCodeConfig(context.Context, *GetDiscountCodeConfigParams) (*GetDiscountCodeConfigResult, error)
	// Verify whether the discount code is available
	CheckDiscountCode(context.Context, *CheckDiscountCodeParams) (*CheckDiscountCodeResult, error)
	mustEmbedUnimplementedDiscountCodeServiceServer()
}

// UnimplementedDiscountCodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDiscountCodeServiceServer struct {
}

func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeConfig(context.Context, *GetDiscountCodeConfigParams) (*GetDiscountCodeConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeConfig not implemented")
}
func (UnimplementedDiscountCodeServiceServer) CheckDiscountCode(context.Context, *CheckDiscountCodeParams) (*CheckDiscountCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) mustEmbedUnimplementedDiscountCodeServiceServer() {}

// UnsafeDiscountCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiscountCodeServiceServer will
// result in compilation errors.
type UnsafeDiscountCodeServiceServer interface {
	mustEmbedUnimplementedDiscountCodeServiceServer()
}

func RegisterDiscountCodeServiceServer(s grpc.ServiceRegistrar, srv DiscountCodeServiceServer) {
	s.RegisterService(&DiscountCodeService_ServiceDesc, srv)
}

func _DiscountCodeService_GetDiscountCodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.marketing.v1.DiscountCodeService/GetDiscountCodeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeConfig(ctx, req.(*GetDiscountCodeConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_CheckDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDiscountCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).CheckDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.marketing.v1.DiscountCodeService/CheckDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).CheckDiscountCode(ctx, req.(*CheckDiscountCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// DiscountCodeService_ServiceDesc is the grpc.ServiceDesc for DiscountCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiscountCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.marketing.v1.DiscountCodeService",
	HandlerType: (*DiscountCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDiscountCodeConfig",
			Handler:    _DiscountCodeService_GetDiscountCodeConfig_Handler,
		},
		{
			MethodName: "CheckDiscountCode",
			Handler:    _DiscountCodeService_CheckDiscountCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/marketing/v1/discount_code_api.proto",
}
