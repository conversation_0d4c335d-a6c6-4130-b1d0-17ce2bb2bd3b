// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/package/v1/package_api.proto

package packageapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PackageServiceClient is the client API for PackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PackageServiceClient interface {
	// list packages
	ListPackages(ctx context.Context, in *ListPackagesParams, opts ...grpc.CallOption) (*ListPackagesResult, error)
	// list customer packages, only return the packages that the customer has purchased
	ListCustomerPackages(ctx context.Context, in *ListCustomerPackagesParams, opts ...grpc.CallOption) (*ListCustomerPackagesResult, error)
	// create sell link
	CreateSellLink(ctx context.Context, in *CreateSellLinkParams, opts ...grpc.CallOption) (*CreateSellLinkResult, error)
	// get sell link public info
	GetSellLinkPublicInfo(ctx context.Context, in *GetSellLinkPublicInfoParams, opts ...grpc.CallOption) (*GetSellLinkPublicInfoResult, error)
}

type packageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPackageServiceClient(cc grpc.ClientConnInterface) PackageServiceClient {
	return &packageServiceClient{cc}
}

func (c *packageServiceClient) ListPackages(ctx context.Context, in *ListPackagesParams, opts ...grpc.CallOption) (*ListPackagesResult, error) {
	out := new(ListPackagesResult)
	err := c.cc.Invoke(ctx, "/moego.client.package.v1.PackageService/ListPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) ListCustomerPackages(ctx context.Context, in *ListCustomerPackagesParams, opts ...grpc.CallOption) (*ListCustomerPackagesResult, error) {
	out := new(ListCustomerPackagesResult)
	err := c.cc.Invoke(ctx, "/moego.client.package.v1.PackageService/ListCustomerPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) CreateSellLink(ctx context.Context, in *CreateSellLinkParams, opts ...grpc.CallOption) (*CreateSellLinkResult, error) {
	out := new(CreateSellLinkResult)
	err := c.cc.Invoke(ctx, "/moego.client.package.v1.PackageService/CreateSellLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) GetSellLinkPublicInfo(ctx context.Context, in *GetSellLinkPublicInfoParams, opts ...grpc.CallOption) (*GetSellLinkPublicInfoResult, error) {
	out := new(GetSellLinkPublicInfoResult)
	err := c.cc.Invoke(ctx, "/moego.client.package.v1.PackageService/GetSellLinkPublicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PackageServiceServer is the server API for PackageService service.
// All implementations must embed UnimplementedPackageServiceServer
// for forward compatibility
type PackageServiceServer interface {
	// list packages
	ListPackages(context.Context, *ListPackagesParams) (*ListPackagesResult, error)
	// list customer packages, only return the packages that the customer has purchased
	ListCustomerPackages(context.Context, *ListCustomerPackagesParams) (*ListCustomerPackagesResult, error)
	// create sell link
	CreateSellLink(context.Context, *CreateSellLinkParams) (*CreateSellLinkResult, error)
	// get sell link public info
	GetSellLinkPublicInfo(context.Context, *GetSellLinkPublicInfoParams) (*GetSellLinkPublicInfoResult, error)
	mustEmbedUnimplementedPackageServiceServer()
}

// UnimplementedPackageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPackageServiceServer struct {
}

func (UnimplementedPackageServiceServer) ListPackages(context.Context, *ListPackagesParams) (*ListPackagesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPackages not implemented")
}
func (UnimplementedPackageServiceServer) ListCustomerPackages(context.Context, *ListCustomerPackagesParams) (*ListCustomerPackagesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerPackages not implemented")
}
func (UnimplementedPackageServiceServer) CreateSellLink(context.Context, *CreateSellLinkParams) (*CreateSellLinkResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSellLink not implemented")
}
func (UnimplementedPackageServiceServer) GetSellLinkPublicInfo(context.Context, *GetSellLinkPublicInfoParams) (*GetSellLinkPublicInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSellLinkPublicInfo not implemented")
}
func (UnimplementedPackageServiceServer) mustEmbedUnimplementedPackageServiceServer() {}

// UnsafePackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PackageServiceServer will
// result in compilation errors.
type UnsafePackageServiceServer interface {
	mustEmbedUnimplementedPackageServiceServer()
}

func RegisterPackageServiceServer(s grpc.ServiceRegistrar, srv PackageServiceServer) {
	s.RegisterService(&PackageService_ServiceDesc, srv)
}

func _PackageService_ListPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPackagesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ListPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.package.v1.PackageService/ListPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ListPackages(ctx, req.(*ListPackagesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_ListCustomerPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerPackagesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ListCustomerPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.package.v1.PackageService/ListCustomerPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ListCustomerPackages(ctx, req.(*ListCustomerPackagesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_CreateSellLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSellLinkParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).CreateSellLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.package.v1.PackageService/CreateSellLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).CreateSellLink(ctx, req.(*CreateSellLinkParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_GetSellLinkPublicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSellLinkPublicInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).GetSellLinkPublicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.package.v1.PackageService/GetSellLinkPublicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).GetSellLinkPublicInfo(ctx, req.(*GetSellLinkPublicInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PackageService_ServiceDesc is the grpc.ServiceDesc for PackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.package.v1.PackageService",
	HandlerType: (*PackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPackages",
			Handler:    _PackageService_ListPackages_Handler,
		},
		{
			MethodName: "ListCustomerPackages",
			Handler:    _PackageService_ListCustomerPackages_Handler,
		},
		{
			MethodName: "CreateSellLink",
			Handler:    _PackageService_CreateSellLink_Handler,
		},
		{
			MethodName: "GetSellLinkPublicInfo",
			Handler:    _PackageService_GetSellLinkPublicInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/package/v1/package_api.proto",
}
