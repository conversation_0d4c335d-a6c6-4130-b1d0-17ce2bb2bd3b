// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/file/v1/file_api.proto

package fileapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FileServiceClient is the client API for FileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileServiceClient interface {
	// get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.
	// set header 'x-amz-acl: public-read' to let the file be public
	GetUploadPresignedUrl(ctx context.Context, in *GetUploadPresignedUrlParams, opts ...grpc.CallOption) (*GetUploadPresignedUrlResult, error)
}

type fileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileServiceClient(cc grpc.ClientConnInterface) FileServiceClient {
	return &fileServiceClient{cc}
}

func (c *fileServiceClient) GetUploadPresignedUrl(ctx context.Context, in *GetUploadPresignedUrlParams, opts ...grpc.CallOption) (*GetUploadPresignedUrlResult, error) {
	out := new(GetUploadPresignedUrlResult)
	err := c.cc.Invoke(ctx, "/moego.client.file.v1.FileService/GetUploadPresignedUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileServiceServer is the server API for FileService service.
// All implementations must embed UnimplementedFileServiceServer
// for forward compatibility
type FileServiceServer interface {
	// get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.
	// set header 'x-amz-acl: public-read' to let the file be public
	GetUploadPresignedUrl(context.Context, *GetUploadPresignedUrlParams) (*GetUploadPresignedUrlResult, error)
	mustEmbedUnimplementedFileServiceServer()
}

// UnimplementedFileServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFileServiceServer struct {
}

func (UnimplementedFileServiceServer) GetUploadPresignedUrl(context.Context, *GetUploadPresignedUrlParams) (*GetUploadPresignedUrlResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUploadPresignedUrl not implemented")
}
func (UnimplementedFileServiceServer) mustEmbedUnimplementedFileServiceServer() {}

// UnsafeFileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileServiceServer will
// result in compilation errors.
type UnsafeFileServiceServer interface {
	mustEmbedUnimplementedFileServiceServer()
}

func RegisterFileServiceServer(s grpc.ServiceRegistrar, srv FileServiceServer) {
	s.RegisterService(&FileService_ServiceDesc, srv)
}

func _FileService_GetUploadPresignedUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUploadPresignedUrlParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).GetUploadPresignedUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.file.v1.FileService/GetUploadPresignedUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).GetUploadPresignedUrl(ctx, req.(*GetUploadPresignedUrlParams))
	}
	return interceptor(ctx, in, info, handler)
}

// FileService_ServiceDesc is the grpc.ServiceDesc for FileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.file.v1.FileService",
	HandlerType: (*FileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUploadPresignedUrl",
			Handler:    _FileService_GetUploadPresignedUrl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/file/v1/file_api.proto",
}
