// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/branded_app/v1/branded_app_api.proto

package brandedAppApiV1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The params for GetBrandedAppConfig
type GetBrandedAppConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the branded app id
	BrandedAppId string `protobuf:"bytes,1,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
}

func (x *GetBrandedAppConfigParams) Reset() {
	*x = GetBrandedAppConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigParams) ProtoMessage() {}

func (x *GetBrandedAppConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigParams.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetBrandedAppConfigParams) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

// The result for GetBrandedAppConfig
type GetBrandedAppConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the branded app config
	Config *GetBrandedAppConfigResult_BrandedAppConfig `protobuf:"bytes,3,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetBrandedAppConfigResult) Reset() {
	*x = GetBrandedAppConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigResult) ProtoMessage() {}

func (x *GetBrandedAppConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigResult.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetBrandedAppConfigResult) GetConfig() *GetBrandedAppConfigResult_BrandedAppConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// get branded config for OB params
type GetBrandedAppConfigForOBParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetBrandedAppConfigForOBParams_Name
	//	*GetBrandedAppConfigForOBParams_Domain
	Anonymous isGetBrandedAppConfigForOBParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetBrandedAppConfigForOBParams) Reset() {
	*x = GetBrandedAppConfigForOBParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigForOBParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigForOBParams) ProtoMessage() {}

func (x *GetBrandedAppConfigForOBParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigForOBParams.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigForOBParams) Descriptor() ([]byte, []int) {
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{2}
}

func (m *GetBrandedAppConfigForOBParams) GetAnonymous() isGetBrandedAppConfigForOBParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetBrandedAppConfigForOBParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetBrandedAppConfigForOBParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetBrandedAppConfigForOBParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetBrandedAppConfigForOBParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetBrandedAppConfigForOBParams_Anonymous interface {
	isGetBrandedAppConfigForOBParams_Anonymous()
}

type GetBrandedAppConfigForOBParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetBrandedAppConfigForOBParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetBrandedAppConfigForOBParams_Name) isGetBrandedAppConfigForOBParams_Anonymous() {}

func (*GetBrandedAppConfigForOBParams_Domain) isGetBrandedAppConfigForOBParams_Anonymous() {}

// get branded config for OB result
type GetBrandedAppConfigForOBResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// branded app id
	BrandedAppId string `protobuf:"bytes,1,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
}

func (x *GetBrandedAppConfigForOBResult) Reset() {
	*x = GetBrandedAppConfigForOBResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigForOBResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigForOBResult) ProtoMessage() {}

func (x *GetBrandedAppConfigForOBResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigForOBResult.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigForOBResult) Descriptor() ([]byte, []int) {
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetBrandedAppConfigForOBResult) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

// The branded app config
type GetBrandedAppConfigResult_BrandedAppConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Android download link
	AndroidDownloadLink string `protobuf:"bytes,1,opt,name=android_download_link,json=androidDownloadLink,proto3" json:"android_download_link,omitempty"`
	// iOS download link
	IosDownloadLink string `protobuf:"bytes,2,opt,name=ios_download_link,json=iosDownloadLink,proto3" json:"ios_download_link,omitempty"`
}

func (x *GetBrandedAppConfigResult_BrandedAppConfig) Reset() {
	*x = GetBrandedAppConfigResult_BrandedAppConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigResult_BrandedAppConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigResult_BrandedAppConfig) ProtoMessage() {}

func (x *GetBrandedAppConfigResult_BrandedAppConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigResult_BrandedAppConfig.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigResult_BrandedAppConfig) Descriptor() ([]byte, []int) {
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetBrandedAppConfigResult_BrandedAppConfig) GetAndroidDownloadLink() string {
	if x != nil {
		return x.AndroidDownloadLink
	}
	return ""
}

func (x *GetBrandedAppConfigResult_BrandedAppConfig) GetIosDownloadLink() string {
	if x != nil {
		return x.IosDownloadLink
	}
	return ""
}

var File_moego_client_branded_app_v1_branded_app_api_proto protoreflect.FileDescriptor

var file_moego_client_branded_app_v1_branded_app_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4b, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x22, 0xf0, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x72, 0x0a, 0x10, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x2a, 0x0a,
	0x11, 0x69, 0x6f, 0x73, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6f, 0x73, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x22, 0x62, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x46, 0x6f, 0x72, 0x4f, 0x42, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x46, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x46, 0x6f, 0x72, 0x4f, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x24, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x32, 0xb2, 0x02, 0x0a, 0x11, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x6f, 0x72, 0x4f, 0x42,
	0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x46, 0x6f, 0x72, 0x4f, 0x42, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46,
	0x6f, 0x72, 0x4f, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x41, 0x70, 0x69, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_branded_app_v1_branded_app_api_proto_rawDescOnce sync.Once
	file_moego_client_branded_app_v1_branded_app_api_proto_rawDescData = file_moego_client_branded_app_v1_branded_app_api_proto_rawDesc
)

func file_moego_client_branded_app_v1_branded_app_api_proto_rawDescGZIP() []byte {
	file_moego_client_branded_app_v1_branded_app_api_proto_rawDescOnce.Do(func() {
		file_moego_client_branded_app_v1_branded_app_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_branded_app_v1_branded_app_api_proto_rawDescData)
	})
	return file_moego_client_branded_app_v1_branded_app_api_proto_rawDescData
}

var file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_client_branded_app_v1_branded_app_api_proto_goTypes = []interface{}{
	(*GetBrandedAppConfigParams)(nil),                  // 0: moego.client.branded_app.v1.GetBrandedAppConfigParams
	(*GetBrandedAppConfigResult)(nil),                  // 1: moego.client.branded_app.v1.GetBrandedAppConfigResult
	(*GetBrandedAppConfigForOBParams)(nil),             // 2: moego.client.branded_app.v1.GetBrandedAppConfigForOBParams
	(*GetBrandedAppConfigForOBResult)(nil),             // 3: moego.client.branded_app.v1.GetBrandedAppConfigForOBResult
	(*GetBrandedAppConfigResult_BrandedAppConfig)(nil), // 4: moego.client.branded_app.v1.GetBrandedAppConfigResult.BrandedAppConfig
}
var file_moego_client_branded_app_v1_branded_app_api_proto_depIdxs = []int32{
	4, // 0: moego.client.branded_app.v1.GetBrandedAppConfigResult.config:type_name -> moego.client.branded_app.v1.GetBrandedAppConfigResult.BrandedAppConfig
	0, // 1: moego.client.branded_app.v1.BrandedAppService.GetBrandedAppConfig:input_type -> moego.client.branded_app.v1.GetBrandedAppConfigParams
	2, // 2: moego.client.branded_app.v1.BrandedAppService.GetBrandedAppConfigForOB:input_type -> moego.client.branded_app.v1.GetBrandedAppConfigForOBParams
	1, // 3: moego.client.branded_app.v1.BrandedAppService.GetBrandedAppConfig:output_type -> moego.client.branded_app.v1.GetBrandedAppConfigResult
	3, // 4: moego.client.branded_app.v1.BrandedAppService.GetBrandedAppConfigForOB:output_type -> moego.client.branded_app.v1.GetBrandedAppConfigForOBResult
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_client_branded_app_v1_branded_app_api_proto_init() }
func file_moego_client_branded_app_v1_branded_app_api_proto_init() {
	if File_moego_client_branded_app_v1_branded_app_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigForOBParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigForOBResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigResult_BrandedAppConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetBrandedAppConfigForOBParams_Name)(nil),
		(*GetBrandedAppConfigForOBParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_branded_app_v1_branded_app_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_branded_app_v1_branded_app_api_proto_goTypes,
		DependencyIndexes: file_moego_client_branded_app_v1_branded_app_api_proto_depIdxs,
		MessageInfos:      file_moego_client_branded_app_v1_branded_app_api_proto_msgTypes,
	}.Build()
	File_moego_client_branded_app_v1_branded_app_api_proto = out.File
	file_moego_client_branded_app_v1_branded_app_api_proto_rawDesc = nil
	file_moego_client_branded_app_v1_branded_app_api_proto_goTypes = nil
	file_moego_client_branded_app_v1_branded_app_api_proto_depIdxs = nil
}
