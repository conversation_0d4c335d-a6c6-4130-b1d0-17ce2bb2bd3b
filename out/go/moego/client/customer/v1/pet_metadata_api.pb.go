// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/customer/v1/pet_metadata_api.proto

package customerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get pet metadata list request
type GetPetMetadataListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPetMetadataListRequest) Reset() {
	*x = GetPetMetadataListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetMetadataListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetMetadataListRequest) ProtoMessage() {}

func (x *GetPetMetadataListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetMetadataListRequest.ProtoReflect.Descriptor instead.
func (*GetPetMetadataListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_pet_metadata_api_proto_rawDescGZIP(), []int{0}
}

// get pet metadata list response
type GetPetMetadataListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type list
	PetTypes []*v1.PetMetadataModel `protobuf:"bytes,1,rep,name=pet_types,json=petTypes,proto3" json:"pet_types,omitempty"`
	// hair length list
	HairLengths []*v1.PetMetadataModel `protobuf:"bytes,2,rep,name=hair_lengths,json=hairLengths,proto3" json:"hair_lengths,omitempty"`
	// behavior list
	Behaviors []*v1.PetMetadataModel `protobuf:"bytes,3,rep,name=behaviors,proto3" json:"behaviors,omitempty"`
	// fixed list
	Fixed []*v1.PetMetadataModel `protobuf:"bytes,4,rep,name=fixed,proto3" json:"fixed,omitempty"`
	// vaccine list
	Vaccines []*v1.PetMetadataModel `protobuf:"bytes,5,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// weight unit list
	WeightUnits []*v1.PetMetadataModel `protobuf:"bytes,6,rep,name=weight_units,json=weightUnits,proto3" json:"weight_units,omitempty"`
}

func (x *GetPetMetadataListResponse) Reset() {
	*x = GetPetMetadataListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetMetadataListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetMetadataListResponse) ProtoMessage() {}

func (x *GetPetMetadataListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetMetadataListResponse.ProtoReflect.Descriptor instead.
func (*GetPetMetadataListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_pet_metadata_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetPetMetadataListResponse) GetPetTypes() []*v1.PetMetadataModel {
	if x != nil {
		return x.PetTypes
	}
	return nil
}

func (x *GetPetMetadataListResponse) GetHairLengths() []*v1.PetMetadataModel {
	if x != nil {
		return x.HairLengths
	}
	return nil
}

func (x *GetPetMetadataListResponse) GetBehaviors() []*v1.PetMetadataModel {
	if x != nil {
		return x.Behaviors
	}
	return nil
}

func (x *GetPetMetadataListResponse) GetFixed() []*v1.PetMetadataModel {
	if x != nil {
		return x.Fixed
	}
	return nil
}

func (x *GetPetMetadataListResponse) GetVaccines() []*v1.PetMetadataModel {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *GetPetMetadataListResponse) GetWeightUnits() []*v1.PetMetadataModel {
	if x != nil {
		return x.WeightUnits
	}
	return nil
}

// pet breed list response
type GetPetBreedListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPetBreedListRequest) Reset() {
	*x = GetPetBreedListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetBreedListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetBreedListRequest) ProtoMessage() {}

func (x *GetPetBreedListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetBreedListRequest.ProtoReflect.Descriptor instead.
func (*GetPetBreedListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_pet_metadata_api_proto_rawDescGZIP(), []int{2}
}

// pet breed list response
type GetPetBreedListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed list
	Breeds []*v1.PetBreedModel `protobuf:"bytes,1,rep,name=breeds,proto3" json:"breeds,omitempty"`
}

func (x *GetPetBreedListResponse) Reset() {
	*x = GetPetBreedListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetBreedListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetBreedListResponse) ProtoMessage() {}

func (x *GetPetBreedListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetBreedListResponse.ProtoReflect.Descriptor instead.
func (*GetPetBreedListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_pet_metadata_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPetBreedListResponse) GetBreeds() []*v1.PetBreedModel {
	if x != nil {
		return x.Breeds
	}
	return nil
}

var File_moego_client_customer_v1_pet_metadata_api_proto protoreflect.FileDescriptor

var file_moego_client_customer_v1_pet_metadata_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0xd7, 0x03, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47,
	0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x70,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x0c, 0x68, 0x61, 0x69, 0x72, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x68, 0x61, 0x69, 0x72, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x73, 0x12, 0x48, 0x0a, 0x09, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x73,
	0x12, 0x40, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x12, 0x46, 0x0a, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x0c, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x5a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f,
	0x0a, 0x06, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x32,
	0x8d, 0x02, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x81, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_customer_v1_pet_metadata_api_proto_rawDescOnce sync.Once
	file_moego_client_customer_v1_pet_metadata_api_proto_rawDescData = file_moego_client_customer_v1_pet_metadata_api_proto_rawDesc
)

func file_moego_client_customer_v1_pet_metadata_api_proto_rawDescGZIP() []byte {
	file_moego_client_customer_v1_pet_metadata_api_proto_rawDescOnce.Do(func() {
		file_moego_client_customer_v1_pet_metadata_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_customer_v1_pet_metadata_api_proto_rawDescData)
	})
	return file_moego_client_customer_v1_pet_metadata_api_proto_rawDescData
}

var file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_client_customer_v1_pet_metadata_api_proto_goTypes = []interface{}{
	(*GetPetMetadataListRequest)(nil),  // 0: moego.client.customer.v1.GetPetMetadataListRequest
	(*GetPetMetadataListResponse)(nil), // 1: moego.client.customer.v1.GetPetMetadataListResponse
	(*GetPetBreedListRequest)(nil),     // 2: moego.client.customer.v1.GetPetBreedListRequest
	(*GetPetBreedListResponse)(nil),    // 3: moego.client.customer.v1.GetPetBreedListResponse
	(*v1.PetMetadataModel)(nil),        // 4: moego.models.customer.v1.PetMetadataModel
	(*v1.PetBreedModel)(nil),           // 5: moego.models.customer.v1.PetBreedModel
}
var file_moego_client_customer_v1_pet_metadata_api_proto_depIdxs = []int32{
	4, // 0: moego.client.customer.v1.GetPetMetadataListResponse.pet_types:type_name -> moego.models.customer.v1.PetMetadataModel
	4, // 1: moego.client.customer.v1.GetPetMetadataListResponse.hair_lengths:type_name -> moego.models.customer.v1.PetMetadataModel
	4, // 2: moego.client.customer.v1.GetPetMetadataListResponse.behaviors:type_name -> moego.models.customer.v1.PetMetadataModel
	4, // 3: moego.client.customer.v1.GetPetMetadataListResponse.fixed:type_name -> moego.models.customer.v1.PetMetadataModel
	4, // 4: moego.client.customer.v1.GetPetMetadataListResponse.vaccines:type_name -> moego.models.customer.v1.PetMetadataModel
	4, // 5: moego.client.customer.v1.GetPetMetadataListResponse.weight_units:type_name -> moego.models.customer.v1.PetMetadataModel
	5, // 6: moego.client.customer.v1.GetPetBreedListResponse.breeds:type_name -> moego.models.customer.v1.PetBreedModel
	0, // 7: moego.client.customer.v1.PetMetadataService.GetPetMetadataList:input_type -> moego.client.customer.v1.GetPetMetadataListRequest
	2, // 8: moego.client.customer.v1.PetMetadataService.GetPetBreedList:input_type -> moego.client.customer.v1.GetPetBreedListRequest
	1, // 9: moego.client.customer.v1.PetMetadataService.GetPetMetadataList:output_type -> moego.client.customer.v1.GetPetMetadataListResponse
	3, // 10: moego.client.customer.v1.PetMetadataService.GetPetBreedList:output_type -> moego.client.customer.v1.GetPetBreedListResponse
	9, // [9:11] is the sub-list for method output_type
	7, // [7:9] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_client_customer_v1_pet_metadata_api_proto_init() }
func file_moego_client_customer_v1_pet_metadata_api_proto_init() {
	if File_moego_client_customer_v1_pet_metadata_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetMetadataListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetMetadataListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetBreedListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetBreedListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_customer_v1_pet_metadata_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_customer_v1_pet_metadata_api_proto_goTypes,
		DependencyIndexes: file_moego_client_customer_v1_pet_metadata_api_proto_depIdxs,
		MessageInfos:      file_moego_client_customer_v1_pet_metadata_api_proto_msgTypes,
	}.Build()
	File_moego_client_customer_v1_pet_metadata_api_proto = out.File
	file_moego_client_customer_v1_pet_metadata_api_proto_rawDesc = nil
	file_moego_client_customer_v1_pet_metadata_api_proto_goTypes = nil
	file_moego_client_customer_v1_pet_metadata_api_proto_depIdxs = nil
}
