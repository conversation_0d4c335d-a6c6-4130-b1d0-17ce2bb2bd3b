// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/grooming/v1/grooming_report_api.proto

package groomingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get grooming report list request
type GetGroomingReportListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetGroomingReportListRequest) Reset() {
	*x = GetGroomingReportListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingReportListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingReportListRequest) ProtoMessage() {}

func (x *GetGroomingReportListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingReportListRequest.ProtoReflect.Descriptor instead.
func (*GetGroomingReportListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetGroomingReportListRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// get grooming report list response
type GetGroomingReportListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming report
	Reports []*v1.GroomingReportModelClientView `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
}

func (x *GetGroomingReportListResponse) Reset() {
	*x = GetGroomingReportListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingReportListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingReportListResponse) ProtoMessage() {}

func (x *GetGroomingReportListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingReportListResponse.ProtoReflect.Descriptor instead.
func (*GetGroomingReportListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetGroomingReportListResponse) GetReports() []*v1.GroomingReportModelClientView {
	if x != nil {
		return x.Reports
	}
	return nil
}

// list grooming report card params
type ListGroomingReportCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListGroomingReportCardParams_Name
	//	*ListGroomingReportCardParams_Domain
	Anonymous isListGroomingReportCardParams_Anonymous `protobuf_oneof:"anonymous"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListGroomingReportCardParams) Reset() {
	*x = ListGroomingReportCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGroomingReportCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroomingReportCardParams) ProtoMessage() {}

func (x *ListGroomingReportCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroomingReportCardParams.ProtoReflect.Descriptor instead.
func (*ListGroomingReportCardParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{2}
}

func (m *ListGroomingReportCardParams) GetAnonymous() isListGroomingReportCardParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListGroomingReportCardParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListGroomingReportCardParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListGroomingReportCardParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListGroomingReportCardParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListGroomingReportCardParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListGroomingReportCardParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isListGroomingReportCardParams_Anonymous interface {
	isListGroomingReportCardParams_Anonymous()
}

type ListGroomingReportCardParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListGroomingReportCardParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListGroomingReportCardParams_Name) isListGroomingReportCardParams_Anonymous() {}

func (*ListGroomingReportCardParams_Domain) isListGroomingReportCardParams_Anonymous() {}

// list grooming report card result
type ListGroomingReportCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming report card list
	ReportConfigs []*GroomingReportCardDef `protobuf:"bytes,1,rep,name=report_configs,json=reportConfigs,proto3" json:"report_configs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListGroomingReportCardResult) Reset() {
	*x = ListGroomingReportCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGroomingReportCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroomingReportCardResult) ProtoMessage() {}

func (x *ListGroomingReportCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroomingReportCardResult.ProtoReflect.Descriptor instead.
func (*ListGroomingReportCardResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListGroomingReportCardResult) GetReportConfigs() []*GroomingReportCardDef {
	if x != nil {
		return x.ReportConfigs
	}
	return nil
}

func (x *ListGroomingReportCardResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// grooming report card def
type GroomingReportCardDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report card id
	ReportCardId int64 `protobuf:"varint,1,opt,name=report_card_id,json=reportCardId,proto3" json:"report_card_id,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// service date, date of report
	ServiceDate *date.Date `protobuf:"bytes,6,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
}

func (x *GroomingReportCardDef) Reset() {
	*x = GroomingReportCardDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingReportCardDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingReportCardDef) ProtoMessage() {}

func (x *GroomingReportCardDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingReportCardDef.ProtoReflect.Descriptor instead.
func (*GroomingReportCardDef) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP(), []int{4}
}

func (x *GroomingReportCardDef) GetReportCardId() int64 {
	if x != nil {
		return x.ReportCardId
	}
	return 0
}

func (x *GroomingReportCardDef) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *GroomingReportCardDef) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *GroomingReportCardDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingReportCardDef) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GroomingReportCardDef) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

var File_moego_client_grooming_v1_grooming_report_api_proto protoreflect.FileDescriptor

var file_moego_client_grooming_v1_grooming_report_api_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x4e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x72, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x51, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0xc3, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f,
	0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xba, 0x01, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x0e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xfe, 0x01, 0x0a, 0x15, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x66, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x32, 0xad, 0x02, 0x0a, 0x15, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88,
	0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81, 0x01, 0x0a, 0x20, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_grooming_v1_grooming_report_api_proto_rawDescOnce sync.Once
	file_moego_client_grooming_v1_grooming_report_api_proto_rawDescData = file_moego_client_grooming_v1_grooming_report_api_proto_rawDesc
)

func file_moego_client_grooming_v1_grooming_report_api_proto_rawDescGZIP() []byte {
	file_moego_client_grooming_v1_grooming_report_api_proto_rawDescOnce.Do(func() {
		file_moego_client_grooming_v1_grooming_report_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_grooming_v1_grooming_report_api_proto_rawDescData)
	})
	return file_moego_client_grooming_v1_grooming_report_api_proto_rawDescData
}

var file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_client_grooming_v1_grooming_report_api_proto_goTypes = []interface{}{
	(*GetGroomingReportListRequest)(nil),     // 0: moego.client.grooming.v1.GetGroomingReportListRequest
	(*GetGroomingReportListResponse)(nil),    // 1: moego.client.grooming.v1.GetGroomingReportListResponse
	(*ListGroomingReportCardParams)(nil),     // 2: moego.client.grooming.v1.ListGroomingReportCardParams
	(*ListGroomingReportCardResult)(nil),     // 3: moego.client.grooming.v1.ListGroomingReportCardResult
	(*GroomingReportCardDef)(nil),            // 4: moego.client.grooming.v1.GroomingReportCardDef
	(*v1.GroomingReportModelClientView)(nil), // 5: moego.models.grooming.v1.GroomingReportModelClientView
	(*v2.PaginationRequest)(nil),             // 6: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),            // 7: moego.utils.v2.PaginationResponse
	(*timestamppb.Timestamp)(nil),            // 8: google.protobuf.Timestamp
	(*date.Date)(nil),                        // 9: google.type.Date
}
var file_moego_client_grooming_v1_grooming_report_api_proto_depIdxs = []int32{
	5, // 0: moego.client.grooming.v1.GetGroomingReportListResponse.reports:type_name -> moego.models.grooming.v1.GroomingReportModelClientView
	6, // 1: moego.client.grooming.v1.ListGroomingReportCardParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4, // 2: moego.client.grooming.v1.ListGroomingReportCardResult.report_configs:type_name -> moego.client.grooming.v1.GroomingReportCardDef
	7, // 3: moego.client.grooming.v1.ListGroomingReportCardResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8, // 4: moego.client.grooming.v1.GroomingReportCardDef.send_time:type_name -> google.protobuf.Timestamp
	9, // 5: moego.client.grooming.v1.GroomingReportCardDef.service_date:type_name -> google.type.Date
	0, // 6: moego.client.grooming.v1.GroomingReportService.GetGroomingReportList:input_type -> moego.client.grooming.v1.GetGroomingReportListRequest
	2, // 7: moego.client.grooming.v1.GroomingReportService.ListGroomingReportCard:input_type -> moego.client.grooming.v1.ListGroomingReportCardParams
	1, // 8: moego.client.grooming.v1.GroomingReportService.GetGroomingReportList:output_type -> moego.client.grooming.v1.GetGroomingReportListResponse
	3, // 9: moego.client.grooming.v1.GroomingReportService.ListGroomingReportCard:output_type -> moego.client.grooming.v1.ListGroomingReportCardResult
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_client_grooming_v1_grooming_report_api_proto_init() }
func file_moego_client_grooming_v1_grooming_report_api_proto_init() {
	if File_moego_client_grooming_v1_grooming_report_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingReportListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingReportListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGroomingReportCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGroomingReportCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingReportCardDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ListGroomingReportCardParams_Name)(nil),
		(*ListGroomingReportCardParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_grooming_v1_grooming_report_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_grooming_v1_grooming_report_api_proto_goTypes,
		DependencyIndexes: file_moego_client_grooming_v1_grooming_report_api_proto_depIdxs,
		MessageInfos:      file_moego_client_grooming_v1_grooming_report_api_proto_msgTypes,
	}.Build()
	File_moego_client_grooming_v1_grooming_report_api_proto = out.File
	file_moego_client_grooming_v1_grooming_report_api_proto_rawDesc = nil
	file_moego_client_grooming_v1_grooming_report_api_proto_goTypes = nil
	file_moego_client_grooming_v1_grooming_report_api_proto_depIdxs = nil
}
