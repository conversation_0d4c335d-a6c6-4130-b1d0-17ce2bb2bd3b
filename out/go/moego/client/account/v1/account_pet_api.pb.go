// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/account/v1/account_pet_api.proto

package accountapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The params message for ListAccountPets
type ListAccountPetsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *ListAccountPetsParams) Reset() {
	*x = ListAccountPetsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountPetsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountPetsParams) ProtoMessage() {}

func (x *ListAccountPetsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountPetsParams.ProtoReflect.Descriptor instead.
func (*ListAccountPetsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListAccountPetsParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// The result message for ListAccountPets
type ListAccountPetsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of account pets
	Pets []*ListAccountPetsResult_AccountPetItem `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListAccountPetsResult) Reset() {
	*x = ListAccountPetsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountPetsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountPetsResult) ProtoMessage() {}

func (x *ListAccountPetsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountPetsResult.ProtoReflect.Descriptor instead.
func (*ListAccountPetsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListAccountPetsResult) GetPets() []*ListAccountPetsResult_AccountPetItem {
	if x != nil {
		return x.Pets
	}
	return nil
}

// The params message for CreateAccountPet
type CreateAccountPetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// The pet profile
	Pet *AccountPetCreateDef `protobuf:"bytes,2,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *CreateAccountPetParams) Reset() {
	*x = CreateAccountPetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountPetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountPetParams) ProtoMessage() {}

func (x *CreateAccountPetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountPetParams.ProtoReflect.Descriptor instead.
func (*CreateAccountPetParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAccountPetParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *CreateAccountPetParams) GetPet() *AccountPetCreateDef {
	if x != nil {
		return x.Pet
	}
	return nil
}

// The account pet create definition
type AccountPetCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// avatar path, default is empty
	AvatarPath string `protobuf:"bytes,2,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type id, required
	PetType v1.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// breed, default is empty
	Breed *string `protobuf:"bytes,4,opt,name=breed,proto3,oneof" json:"breed,omitempty"`
	// breed mixed
	BreedMixed *bool `protobuf:"varint,5,opt,name=breed_mixed,json=breedMixed,proto3,oneof" json:"breed_mixed,omitempty"`
	// weight, optional
	// should be a number string
	Weight *string `protobuf:"bytes,7,opt,name=weight,proto3,oneof" json:"weight,omitempty"`
	// coat type, optional
	CoatType *string `protobuf:"bytes,8,opt,name=coat_type,json=coatType,proto3,oneof" json:"coat_type,omitempty"`
}

func (x *AccountPetCreateDef) Reset() {
	*x = AccountPetCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountPetCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountPetCreateDef) ProtoMessage() {}

func (x *AccountPetCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountPetCreateDef.ProtoReflect.Descriptor instead.
func (*AccountPetCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{3}
}

func (x *AccountPetCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountPetCreateDef) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *AccountPetCreateDef) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *AccountPetCreateDef) GetBreed() string {
	if x != nil && x.Breed != nil {
		return *x.Breed
	}
	return ""
}

func (x *AccountPetCreateDef) GetBreedMixed() bool {
	if x != nil && x.BreedMixed != nil {
		return *x.BreedMixed
	}
	return false
}

func (x *AccountPetCreateDef) GetWeight() string {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return ""
}

func (x *AccountPetCreateDef) GetCoatType() string {
	if x != nil && x.CoatType != nil {
		return *x.CoatType
	}
	return ""
}

// The result message for CreateAccountPet
type CreateAccountPetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *CreateAccountPetResult) Reset() {
	*x = CreateAccountPetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountPetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountPetResult) ProtoMessage() {}

func (x *CreateAccountPetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountPetResult.ProtoReflect.Descriptor instead.
func (*CreateAccountPetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAccountPetResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// Account pet item
type ListAccountPetsResult_AccountPetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business pet profile
	BusinessPet *v11.BusinessCustomerPetBrandedAppView `protobuf:"bytes,1,opt,name=business_pet,json=businessPet,proto3" json:"business_pet,omitempty"`
	// The business pet vaccine profile
	VaccineRecords []*ListAccountPetsResult_AccountPetVaccineRecordItem `protobuf:"bytes,2,rep,name=vaccine_records,json=vaccineRecords,proto3" json:"vaccine_records,omitempty"`
}

func (x *ListAccountPetsResult_AccountPetItem) Reset() {
	*x = ListAccountPetsResult_AccountPetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountPetsResult_AccountPetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountPetsResult_AccountPetItem) ProtoMessage() {}

func (x *ListAccountPetsResult_AccountPetItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountPetsResult_AccountPetItem.ProtoReflect.Descriptor instead.
func (*ListAccountPetsResult_AccountPetItem) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListAccountPetsResult_AccountPetItem) GetBusinessPet() *v11.BusinessCustomerPetBrandedAppView {
	if x != nil {
		return x.BusinessPet
	}
	return nil
}

func (x *ListAccountPetsResult_AccountPetItem) GetVaccineRecords() []*ListAccountPetsResult_AccountPetVaccineRecordItem {
	if x != nil {
		return x.VaccineRecords
	}
	return nil
}

// pet vaccine record item
type ListAccountPetsResult_AccountPetVaccineRecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,5,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,6,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) Reset() {
	*x = ListAccountPetsResult_AccountPetVaccineRecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountPetsResult_AccountPetVaccineRecordItem) ProtoMessage() {}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_pet_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountPetsResult_AccountPetVaccineRecordItem.ProtoReflect.Descriptor instead.
func (*ListAccountPetsResult_AccountPetVaccineRecordItem) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *ListAccountPetsResult_AccountPetVaccineRecordItem) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

var File_moego_client_account_v1_account_pet_api_proto protoreflect.FileDescriptor

var file_moego_client_account_v1_account_pet_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x53, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xde, 0x04, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x51, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04,
	0x70, 0x65, 0x74, 0x73, 0x1a, 0xee, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x67, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x12, 0x73, 0x0a, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x1a, 0x80, 0x02, 0x0a, 0x1b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x48, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x70, 0x65, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xfb, 0x02, 0x0a, 0x13, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f,
	0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x72, 0x09, 0x18, 0xff, 0x01, 0xd0, 0x01, 0x01,
	0x88, 0x01, 0x01, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x48, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x05, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x32, 0x48, 0x00, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x69, 0x78, 0x65, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x06,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x6f, 0x61,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78, 0x65, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f,
	0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x2f, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x32, 0xfc, 0x01, 0x0a, 0x11, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71,
	0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x74, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x65, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7e, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_account_v1_account_pet_api_proto_rawDescOnce sync.Once
	file_moego_client_account_v1_account_pet_api_proto_rawDescData = file_moego_client_account_v1_account_pet_api_proto_rawDesc
)

func file_moego_client_account_v1_account_pet_api_proto_rawDescGZIP() []byte {
	file_moego_client_account_v1_account_pet_api_proto_rawDescOnce.Do(func() {
		file_moego_client_account_v1_account_pet_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_account_v1_account_pet_api_proto_rawDescData)
	})
	return file_moego_client_account_v1_account_pet_api_proto_rawDescData
}

var file_moego_client_account_v1_account_pet_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_client_account_v1_account_pet_api_proto_goTypes = []interface{}{
	(*ListAccountPetsParams)(nil),                             // 0: moego.client.account.v1.ListAccountPetsParams
	(*ListAccountPetsResult)(nil),                             // 1: moego.client.account.v1.ListAccountPetsResult
	(*CreateAccountPetParams)(nil),                            // 2: moego.client.account.v1.CreateAccountPetParams
	(*AccountPetCreateDef)(nil),                               // 3: moego.client.account.v1.AccountPetCreateDef
	(*CreateAccountPetResult)(nil),                            // 4: moego.client.account.v1.CreateAccountPetResult
	(*ListAccountPetsResult_AccountPetItem)(nil),              // 5: moego.client.account.v1.ListAccountPetsResult.AccountPetItem
	(*ListAccountPetsResult_AccountPetVaccineRecordItem)(nil), // 6: moego.client.account.v1.ListAccountPetsResult.AccountPetVaccineRecordItem
	(v1.PetType)(0),                                           // 7: moego.models.customer.v1.PetType
	(*v11.BusinessCustomerPetBrandedAppView)(nil),             // 8: moego.models.business_customer.v1.BusinessCustomerPetBrandedAppView
	(*date.Date)(nil),                                         // 9: google.type.Date
}
var file_moego_client_account_v1_account_pet_api_proto_depIdxs = []int32{
	5, // 0: moego.client.account.v1.ListAccountPetsResult.pets:type_name -> moego.client.account.v1.ListAccountPetsResult.AccountPetItem
	3, // 1: moego.client.account.v1.CreateAccountPetParams.pet:type_name -> moego.client.account.v1.AccountPetCreateDef
	7, // 2: moego.client.account.v1.AccountPetCreateDef.pet_type:type_name -> moego.models.customer.v1.PetType
	8, // 3: moego.client.account.v1.ListAccountPetsResult.AccountPetItem.business_pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetBrandedAppView
	6, // 4: moego.client.account.v1.ListAccountPetsResult.AccountPetItem.vaccine_records:type_name -> moego.client.account.v1.ListAccountPetsResult.AccountPetVaccineRecordItem
	9, // 5: moego.client.account.v1.ListAccountPetsResult.AccountPetVaccineRecordItem.expiration_date:type_name -> google.type.Date
	0, // 6: moego.client.account.v1.AccountPetService.ListAccountPets:input_type -> moego.client.account.v1.ListAccountPetsParams
	2, // 7: moego.client.account.v1.AccountPetService.CreateAccountPet:input_type -> moego.client.account.v1.CreateAccountPetParams
	1, // 8: moego.client.account.v1.AccountPetService.ListAccountPets:output_type -> moego.client.account.v1.ListAccountPetsResult
	4, // 9: moego.client.account.v1.AccountPetService.CreateAccountPet:output_type -> moego.client.account.v1.CreateAccountPetResult
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_client_account_v1_account_pet_api_proto_init() }
func file_moego_client_account_v1_account_pet_api_proto_init() {
	if File_moego_client_account_v1_account_pet_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountPetsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountPetsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountPetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountPetCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountPetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountPetsResult_AccountPetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_pet_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountPetsResult_AccountPetVaccineRecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_account_v1_account_pet_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_pet_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_pet_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_pet_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_account_v1_account_pet_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_account_v1_account_pet_api_proto_goTypes,
		DependencyIndexes: file_moego_client_account_v1_account_pet_api_proto_depIdxs,
		MessageInfos:      file_moego_client_account_v1_account_pet_api_proto_msgTypes,
	}.Build()
	File_moego_client_account_v1_account_pet_api_proto = out.File
	file_moego_client_account_v1_account_pet_api_proto_rawDesc = nil
	file_moego_client_account_v1_account_pet_api_proto_goTypes = nil
	file_moego_client_account_v1_account_pet_api_proto_depIdxs = nil
}
