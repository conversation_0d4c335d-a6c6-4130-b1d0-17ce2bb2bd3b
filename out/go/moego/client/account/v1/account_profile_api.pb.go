// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/account/v1/account_profile_api.proto

package accountapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The result of linked client enum
type LinkAccountToBrandedClientResult_LinkResult int32

const (
	// unspecified
	LinkAccountToBrandedClientResult_LINK_RESULT_UNSPECIFIED LinkAccountToBrandedClientResult_LinkResult = 0
	// linked existing client
	LinkAccountToBrandedClientResult_LINKED_EXISTING_CLIENT LinkAccountToBrandedClientResult_LinkResult = 1
	// created new client and linked
	LinkAccountToBrandedClientResult_CREATED_NEW_CLIENT LinkAccountToBrandedClientResult_LinkResult = 2
	// already linked
	LinkAccountToBrandedClientResult_ALREADY_LINKED LinkAccountToBrandedClientResult_LinkResult = 3
	// unknown error
	LinkAccountToBrandedClientResult_UNKNOWN_ERROR LinkAccountToBrandedClientResult_LinkResult = 4
)

// Enum value maps for LinkAccountToBrandedClientResult_LinkResult.
var (
	LinkAccountToBrandedClientResult_LinkResult_name = map[int32]string{
		0: "LINK_RESULT_UNSPECIFIED",
		1: "LINKED_EXISTING_CLIENT",
		2: "CREATED_NEW_CLIENT",
		3: "ALREADY_LINKED",
		4: "UNKNOWN_ERROR",
	}
	LinkAccountToBrandedClientResult_LinkResult_value = map[string]int32{
		"LINK_RESULT_UNSPECIFIED": 0,
		"LINKED_EXISTING_CLIENT":  1,
		"CREATED_NEW_CLIENT":      2,
		"ALREADY_LINKED":          3,
		"UNKNOWN_ERROR":           4,
	}
)

func (x LinkAccountToBrandedClientResult_LinkResult) Enum() *LinkAccountToBrandedClientResult_LinkResult {
	p := new(LinkAccountToBrandedClientResult_LinkResult)
	*p = x
	return p
}

func (x LinkAccountToBrandedClientResult_LinkResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LinkAccountToBrandedClientResult_LinkResult) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_account_v1_account_profile_api_proto_enumTypes[0].Descriptor()
}

func (LinkAccountToBrandedClientResult_LinkResult) Type() protoreflect.EnumType {
	return &file_moego_client_account_v1_account_profile_api_proto_enumTypes[0]
}

func (x LinkAccountToBrandedClientResult_LinkResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LinkAccountToBrandedClientResult_LinkResult.Descriptor instead.
func (LinkAccountToBrandedClientResult_LinkResult) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{5, 0}
}

// The params message for GetAccountProfile
type GetAccountProfileParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetAccountProfileParams) Reset() {
	*x = GetAccountProfileParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountProfileParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountProfileParams) ProtoMessage() {}

func (x *GetAccountProfileParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountProfileParams.ProtoReflect.Descriptor instead.
func (*GetAccountProfileParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAccountProfileParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// The result message for GetAccountProfile
type GetAccountProfileResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business customer profile
	BusinessCustomer *v1.BusinessCustomerBrandedAppView `protobuf:"bytes,1,opt,name=business_customer,json=businessCustomer,proto3,oneof" json:"business_customer,omitempty"`
}

func (x *GetAccountProfileResult) Reset() {
	*x = GetAccountProfileResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountProfileResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountProfileResult) ProtoMessage() {}

func (x *GetAccountProfileResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountProfileResult.ProtoReflect.Descriptor instead.
func (*GetAccountProfileResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAccountProfileResult) GetBusinessCustomer() *v1.BusinessCustomerBrandedAppView {
	if x != nil {
		return x.BusinessCustomer
	}
	return nil
}

// The params message for GetAccountLinkStatus
type GetAccountLinkStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetAccountLinkStatusParams) Reset() {
	*x = GetAccountLinkStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountLinkStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLinkStatusParams) ProtoMessage() {}

func (x *GetAccountLinkStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLinkStatusParams.ProtoReflect.Descriptor instead.
func (*GetAccountLinkStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetAccountLinkStatusParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// The result message for GetAccountLinkStatus
type GetAccountLinkStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The link status
	// Identifies whether the current account has a customer associated with it
	IsLinked bool `protobuf:"varint,1,opt,name=is_linked,json=isLinked,proto3" json:"is_linked,omitempty"`
	// Has business customer profile
	// Retrieve if there is a customer under company by phone number
	HasBusinessCustomer bool `protobuf:"varint,2,opt,name=has_business_customer,json=hasBusinessCustomer,proto3" json:"has_business_customer,omitempty"`
}

func (x *GetAccountLinkStatusResult) Reset() {
	*x = GetAccountLinkStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountLinkStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLinkStatusResult) ProtoMessage() {}

func (x *GetAccountLinkStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLinkStatusResult.ProtoReflect.Descriptor instead.
func (*GetAccountLinkStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAccountLinkStatusResult) GetIsLinked() bool {
	if x != nil {
		return x.IsLinked
	}
	return false
}

func (x *GetAccountLinkStatusResult) GetHasBusinessCustomer() bool {
	if x != nil {
		return x.HasBusinessCustomer
	}
	return false
}

// The params message for LinkAccountToBrandedClient
type LinkAccountToBrandedClientParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// The account profile
	Profile *LinkAccountToBrandedClientParams_AccountProfileCreateDef `protobuf:"bytes,2,opt,name=profile,proto3,oneof" json:"profile,omitempty"`
}

func (x *LinkAccountToBrandedClientParams) Reset() {
	*x = LinkAccountToBrandedClientParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkAccountToBrandedClientParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkAccountToBrandedClientParams) ProtoMessage() {}

func (x *LinkAccountToBrandedClientParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkAccountToBrandedClientParams.ProtoReflect.Descriptor instead.
func (*LinkAccountToBrandedClientParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{4}
}

func (x *LinkAccountToBrandedClientParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *LinkAccountToBrandedClientParams) GetProfile() *LinkAccountToBrandedClientParams_AccountProfileCreateDef {
	if x != nil {
		return x.Profile
	}
	return nil
}

// The result message for LinkAccountToBrandedClient
type LinkAccountToBrandedClientResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The link result
	LinkResult LinkAccountToBrandedClientResult_LinkResult `protobuf:"varint,1,opt,name=link_result,json=linkResult,proto3,enum=moego.client.account.v1.LinkAccountToBrandedClientResult_LinkResult" json:"link_result,omitempty"`
}

func (x *LinkAccountToBrandedClientResult) Reset() {
	*x = LinkAccountToBrandedClientResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkAccountToBrandedClientResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkAccountToBrandedClientResult) ProtoMessage() {}

func (x *LinkAccountToBrandedClientResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkAccountToBrandedClientResult.ProtoReflect.Descriptor instead.
func (*LinkAccountToBrandedClientResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{5}
}

func (x *LinkAccountToBrandedClientResult) GetLinkResult() LinkAccountToBrandedClientResult_LinkResult {
	if x != nil {
		return x.LinkResult
	}
	return LinkAccountToBrandedClientResult_LINK_RESULT_UNSPECIFIED
}

// create def for account profile
type LinkAccountToBrandedClientParams_AccountProfileCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath *string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// email
	Email *string `protobuf:"bytes,5,opt,name=email,proto3,oneof" json:"email,omitempty"`
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) Reset() {
	*x = LinkAccountToBrandedClientParams_AccountProfileCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkAccountToBrandedClientParams_AccountProfileCreateDef) ProtoMessage() {}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_profile_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkAccountToBrandedClientParams_AccountProfileCreateDef.ProtoReflect.Descriptor instead.
func (*LinkAccountToBrandedClientParams_AccountProfileCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *LinkAccountToBrandedClientParams_AccountProfileCreateDef) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

var File_moego_client_account_v1_account_profile_api_proto protoreflect.FileDescriptor

var file_moego_client_account_v1_account_profile_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x40, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x55, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xa4,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x11, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x22, 0x58, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22,
	0x6d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6e,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x68, 0x61,
	0x73, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x68, 0x61, 0x73, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x22, 0xbd,
	0x03, 0x0a, 0x20, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x70, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x88,
	0x01, 0x01, 0x1a, 0xde, 0x01, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x28,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x31, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18, 0xff, 0x01, 0x88,
	0x01, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x32, 0x60, 0x01, 0x48, 0x01, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x90,
	0x02, 0x0a, 0x20, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x65, 0x0a, 0x0b, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x0a, 0x4c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x4e,
	0x4b, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44,
	0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54,
	0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x03, 0x12, 0x11,
	0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x04, 0x32, 0xa8, 0x03, 0x0a, 0x15, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x77, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7e, 0x0a, 0x1f,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_account_v1_account_profile_api_proto_rawDescOnce sync.Once
	file_moego_client_account_v1_account_profile_api_proto_rawDescData = file_moego_client_account_v1_account_profile_api_proto_rawDesc
)

func file_moego_client_account_v1_account_profile_api_proto_rawDescGZIP() []byte {
	file_moego_client_account_v1_account_profile_api_proto_rawDescOnce.Do(func() {
		file_moego_client_account_v1_account_profile_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_account_v1_account_profile_api_proto_rawDescData)
	})
	return file_moego_client_account_v1_account_profile_api_proto_rawDescData
}

var file_moego_client_account_v1_account_profile_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_client_account_v1_account_profile_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_client_account_v1_account_profile_api_proto_goTypes = []interface{}{
	(LinkAccountToBrandedClientResult_LinkResult)(0),                 // 0: moego.client.account.v1.LinkAccountToBrandedClientResult.LinkResult
	(*GetAccountProfileParams)(nil),                                  // 1: moego.client.account.v1.GetAccountProfileParams
	(*GetAccountProfileResult)(nil),                                  // 2: moego.client.account.v1.GetAccountProfileResult
	(*GetAccountLinkStatusParams)(nil),                               // 3: moego.client.account.v1.GetAccountLinkStatusParams
	(*GetAccountLinkStatusResult)(nil),                               // 4: moego.client.account.v1.GetAccountLinkStatusResult
	(*LinkAccountToBrandedClientParams)(nil),                         // 5: moego.client.account.v1.LinkAccountToBrandedClientParams
	(*LinkAccountToBrandedClientResult)(nil),                         // 6: moego.client.account.v1.LinkAccountToBrandedClientResult
	(*LinkAccountToBrandedClientParams_AccountProfileCreateDef)(nil), // 7: moego.client.account.v1.LinkAccountToBrandedClientParams.AccountProfileCreateDef
	(*v1.BusinessCustomerBrandedAppView)(nil),                        // 8: moego.models.business_customer.v1.BusinessCustomerBrandedAppView
}
var file_moego_client_account_v1_account_profile_api_proto_depIdxs = []int32{
	8, // 0: moego.client.account.v1.GetAccountProfileResult.business_customer:type_name -> moego.models.business_customer.v1.BusinessCustomerBrandedAppView
	7, // 1: moego.client.account.v1.LinkAccountToBrandedClientParams.profile:type_name -> moego.client.account.v1.LinkAccountToBrandedClientParams.AccountProfileCreateDef
	0, // 2: moego.client.account.v1.LinkAccountToBrandedClientResult.link_result:type_name -> moego.client.account.v1.LinkAccountToBrandedClientResult.LinkResult
	1, // 3: moego.client.account.v1.AccountProfileService.GetAccountProfile:input_type -> moego.client.account.v1.GetAccountProfileParams
	3, // 4: moego.client.account.v1.AccountProfileService.GetAccountLinkStatus:input_type -> moego.client.account.v1.GetAccountLinkStatusParams
	5, // 5: moego.client.account.v1.AccountProfileService.LinkAccountToBrandedClient:input_type -> moego.client.account.v1.LinkAccountToBrandedClientParams
	2, // 6: moego.client.account.v1.AccountProfileService.GetAccountProfile:output_type -> moego.client.account.v1.GetAccountProfileResult
	4, // 7: moego.client.account.v1.AccountProfileService.GetAccountLinkStatus:output_type -> moego.client.account.v1.GetAccountLinkStatusResult
	6, // 8: moego.client.account.v1.AccountProfileService.LinkAccountToBrandedClient:output_type -> moego.client.account.v1.LinkAccountToBrandedClientResult
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_client_account_v1_account_profile_api_proto_init() }
func file_moego_client_account_v1_account_profile_api_proto_init() {
	if File_moego_client_account_v1_account_profile_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountProfileParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountProfileResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountLinkStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountLinkStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkAccountToBrandedClientParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkAccountToBrandedClientResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_profile_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkAccountToBrandedClientParams_AccountProfileCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_account_v1_account_profile_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_profile_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_profile_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_profile_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_client_account_v1_account_profile_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_account_v1_account_profile_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_account_v1_account_profile_api_proto_goTypes,
		DependencyIndexes: file_moego_client_account_v1_account_profile_api_proto_depIdxs,
		EnumInfos:         file_moego_client_account_v1_account_profile_api_proto_enumTypes,
		MessageInfos:      file_moego_client_account_v1_account_profile_api_proto_msgTypes,
	}.Build()
	File_moego_client_account_v1_account_profile_api_proto = out.File
	file_moego_client_account_v1_account_profile_api_proto_rawDesc = nil
	file_moego_client_account_v1_account_profile_api_proto_goTypes = nil
	file_moego_client_account_v1_account_profile_api_proto_depIdxs = nil
}
