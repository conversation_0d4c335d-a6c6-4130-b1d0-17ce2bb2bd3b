// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/business_customer/v1/business_pet_evaluation_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetEvaluationServiceClient is the client API for BusinessPetEvaluationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetEvaluationServiceClient interface {
	// List pet evaluation
	ListPetEvaluation(ctx context.Context, in *ListPetEvaluationParams, opts ...grpc.CallOption) (*ListPetEvaluationResult, error)
	// Check evaluation for services
	CheckEvaluationForServices(ctx context.Context, in *CheckEvaluationForServicesParams, opts ...grpc.CallOption) (*CheckEvaluationForServicesResult, error)
}

type businessPetEvaluationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetEvaluationServiceClient(cc grpc.ClientConnInterface) BusinessPetEvaluationServiceClient {
	return &businessPetEvaluationServiceClient{cc}
}

func (c *businessPetEvaluationServiceClient) ListPetEvaluation(ctx context.Context, in *ListPetEvaluationParams, opts ...grpc.CallOption) (*ListPetEvaluationResult, error) {
	out := new(ListPetEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.client.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetEvaluationServiceClient) CheckEvaluationForServices(ctx context.Context, in *CheckEvaluationForServicesParams, opts ...grpc.CallOption) (*CheckEvaluationForServicesResult, error) {
	out := new(CheckEvaluationForServicesResult)
	err := c.cc.Invoke(ctx, "/moego.client.business_customer.v1.BusinessPetEvaluationService/CheckEvaluationForServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetEvaluationServiceServer is the server API for BusinessPetEvaluationService service.
// All implementations must embed UnimplementedBusinessPetEvaluationServiceServer
// for forward compatibility
type BusinessPetEvaluationServiceServer interface {
	// List pet evaluation
	ListPetEvaluation(context.Context, *ListPetEvaluationParams) (*ListPetEvaluationResult, error)
	// Check evaluation for services
	CheckEvaluationForServices(context.Context, *CheckEvaluationForServicesParams) (*CheckEvaluationForServicesResult, error)
	mustEmbedUnimplementedBusinessPetEvaluationServiceServer()
}

// UnimplementedBusinessPetEvaluationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetEvaluationServiceServer struct {
}

func (UnimplementedBusinessPetEvaluationServiceServer) ListPetEvaluation(context.Context, *ListPetEvaluationParams) (*ListPetEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetEvaluation not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) CheckEvaluationForServices(context.Context, *CheckEvaluationForServicesParams) (*CheckEvaluationForServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEvaluationForServices not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) mustEmbedUnimplementedBusinessPetEvaluationServiceServer() {
}

// UnsafeBusinessPetEvaluationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetEvaluationServiceServer will
// result in compilation errors.
type UnsafeBusinessPetEvaluationServiceServer interface {
	mustEmbedUnimplementedBusinessPetEvaluationServiceServer()
}

func RegisterBusinessPetEvaluationServiceServer(s grpc.ServiceRegistrar, srv BusinessPetEvaluationServiceServer) {
	s.RegisterService(&BusinessPetEvaluationService_ServiceDesc, srv)
}

func _BusinessPetEvaluationService_ListPetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluation(ctx, req.(*ListPetEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetEvaluationService_CheckEvaluationForServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEvaluationForServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).CheckEvaluationForServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.business_customer.v1.BusinessPetEvaluationService/CheckEvaluationForServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).CheckEvaluationForServices(ctx, req.(*CheckEvaluationForServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetEvaluationService_ServiceDesc is the grpc.ServiceDesc for BusinessPetEvaluationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetEvaluationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.business_customer.v1.BusinessPetEvaluationService",
	HandlerType: (*BusinessPetEvaluationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPetEvaluation",
			Handler:    _BusinessPetEvaluationService_ListPetEvaluation_Handler,
		},
		{
			MethodName: "CheckEvaluationForServices",
			Handler:    _BusinessPetEvaluationService_CheckEvaluationForServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/business_customer/v1/business_pet_evaluation_api.proto",
}
