// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/business_customer/v1/business_pet_metadata_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// List pet metadata params
type ListPetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListPetMetadataParams_Name
	//	*ListPetMetadataParams_Domain
	Anonymous isListPetMetadataParams_Anonymous `protobuf_oneof:"anonymous"`
	// metadata name
	MetadataNames []v1.BusinessPetMetadataName `protobuf:"varint,3,rep,packed,name=metadata_names,json=metadataNames,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"metadata_names,omitempty"`
}

func (x *ListPetMetadataParams) Reset() {
	*x = ListPetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataParams) ProtoMessage() {}

func (x *ListPetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataParams.ProtoReflect.Descriptor instead.
func (*ListPetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{0}
}

func (m *ListPetMetadataParams) GetAnonymous() isListPetMetadataParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListPetMetadataParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListPetMetadataParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListPetMetadataParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListPetMetadataParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListPetMetadataParams) GetMetadataNames() []v1.BusinessPetMetadataName {
	if x != nil {
		return x.MetadataNames
	}
	return nil
}

type isListPetMetadataParams_Anonymous interface {
	isListPetMetadataParams_Anonymous()
}

type ListPetMetadataParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListPetMetadataParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListPetMetadataParams_Name) isListPetMetadataParams_Anonymous() {}

func (*ListPetMetadataParams_Domain) isListPetMetadataParams_Anonymous() {}

// List pet metadata result
type ListPetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet metadata
	Metadata []*v1.BusinessPetMetadataView `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *ListPetMetadataResult) Reset() {
	*x = ListPetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataResult) ProtoMessage() {}

func (x *ListPetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataResult.ProtoReflect.Descriptor instead.
func (*ListPetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetMetadataResult) GetMetadata() []*v1.BusinessPetMetadataView {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// List pet types params
type ListPetTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *ListPetTypesParams) Reset() {
	*x = ListPetTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesParams) ProtoMessage() {}

func (x *ListPetTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesParams.ProtoReflect.Descriptor instead.
func (*ListPetTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListPetTypesParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// List pet types result
type ListPetTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type list
	Types []*v1.BusinessPetTypeModel `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
}

func (x *ListPetTypesResult) Reset() {
	*x = ListPetTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesResult) ProtoMessage() {}

func (x *ListPetTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesResult.ProtoReflect.Descriptor instead.
func (*ListPetTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetTypesResult) GetTypes() []*v1.BusinessPetTypeModel {
	if x != nil {
		return x.Types
	}
	return nil
}

// List pet breeds params
type ListPetBreedsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// Selected pet type id
	PetType v11.PetType `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
}

func (x *ListPetBreedsParams) Reset() {
	*x = ListPetBreedsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsParams) ProtoMessage() {}

func (x *ListPetBreedsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsParams.ProtoReflect.Descriptor instead.
func (*ListPetBreedsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListPetBreedsParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListPetBreedsParams) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

// List pet breeds result
type ListPetBreedsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed list
	Breeds []*v1.BusinessPetBreedModel `protobuf:"bytes,1,rep,name=breeds,proto3" json:"breeds,omitempty"`
}

func (x *ListPetBreedsResult) Reset() {
	*x = ListPetBreedsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsResult) ProtoMessage() {}

func (x *ListPetBreedsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsResult.ProtoReflect.Descriptor instead.
func (*ListPetBreedsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListPetBreedsResult) GetBreeds() []*v1.BusinessPetBreedModel {
	if x != nil {
		return x.Breeds
	}
	return nil
}

// List pet coat types params
type ListPetCoatTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *ListPetCoatTypesParams) Reset() {
	*x = ListPetCoatTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypesParams) ProtoMessage() {}

func (x *ListPetCoatTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypesParams.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{6}
}

func (x *ListPetCoatTypesParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// List pet coat types result
type ListPetCoatTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type list
	CoatTypes []*v1.BusinessPetCoatTypeModel `protobuf:"bytes,1,rep,name=coat_types,json=coatTypes,proto3" json:"coat_types,omitempty"`
}

func (x *ListPetCoatTypesResult) Reset() {
	*x = ListPetCoatTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypesResult) ProtoMessage() {}

func (x *ListPetCoatTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypesResult.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListPetCoatTypesResult) GetCoatTypes() []*v1.BusinessPetCoatTypeModel {
	if x != nil {
		return x.CoatTypes
	}
	return nil
}

var File_moego_client_business_customer_v1_business_pet_metadata_api_proto protoreflect.FileDescriptor

var file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd1, 0x01, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x76, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08,
	0x01, 0x10, 0x64, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0d, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x6f, 0x0a,
	0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x50,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x22, 0x63, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x22, 0x67, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22, 0x54, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x22, 0x74, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x0a,
	0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x63,
	0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x32, 0xae, 0x04, 0x0a, 0x1a, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7c, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88,
	0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x9b, 0x01, 0x0a, 0x29, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x6c, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescOnce sync.Once
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescData = file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDesc
)

func file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescGZIP() []byte {
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescOnce.Do(func() {
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescData)
	})
	return file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDescData
}

var file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_client_business_customer_v1_business_pet_metadata_api_proto_goTypes = []interface{}{
	(*ListPetMetadataParams)(nil),       // 0: moego.client.business_customer.v1.ListPetMetadataParams
	(*ListPetMetadataResult)(nil),       // 1: moego.client.business_customer.v1.ListPetMetadataResult
	(*ListPetTypesParams)(nil),          // 2: moego.client.business_customer.v1.ListPetTypesParams
	(*ListPetTypesResult)(nil),          // 3: moego.client.business_customer.v1.ListPetTypesResult
	(*ListPetBreedsParams)(nil),         // 4: moego.client.business_customer.v1.ListPetBreedsParams
	(*ListPetBreedsResult)(nil),         // 5: moego.client.business_customer.v1.ListPetBreedsResult
	(*ListPetCoatTypesParams)(nil),      // 6: moego.client.business_customer.v1.ListPetCoatTypesParams
	(*ListPetCoatTypesResult)(nil),      // 7: moego.client.business_customer.v1.ListPetCoatTypesResult
	(v1.BusinessPetMetadataName)(0),     // 8: moego.models.business_customer.v1.BusinessPetMetadataName
	(*v1.BusinessPetMetadataView)(nil),  // 9: moego.models.business_customer.v1.BusinessPetMetadataView
	(*v1.BusinessPetTypeModel)(nil),     // 10: moego.models.business_customer.v1.BusinessPetTypeModel
	(v11.PetType)(0),                    // 11: moego.models.customer.v1.PetType
	(*v1.BusinessPetBreedModel)(nil),    // 12: moego.models.business_customer.v1.BusinessPetBreedModel
	(*v1.BusinessPetCoatTypeModel)(nil), // 13: moego.models.business_customer.v1.BusinessPetCoatTypeModel
}
var file_moego_client_business_customer_v1_business_pet_metadata_api_proto_depIdxs = []int32{
	8,  // 0: moego.client.business_customer.v1.ListPetMetadataParams.metadata_names:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	9,  // 1: moego.client.business_customer.v1.ListPetMetadataResult.metadata:type_name -> moego.models.business_customer.v1.BusinessPetMetadataView
	10, // 2: moego.client.business_customer.v1.ListPetTypesResult.types:type_name -> moego.models.business_customer.v1.BusinessPetTypeModel
	11, // 3: moego.client.business_customer.v1.ListPetBreedsParams.pet_type:type_name -> moego.models.customer.v1.PetType
	12, // 4: moego.client.business_customer.v1.ListPetBreedsResult.breeds:type_name -> moego.models.business_customer.v1.BusinessPetBreedModel
	13, // 5: moego.client.business_customer.v1.ListPetCoatTypesResult.coat_types:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeModel
	0,  // 6: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetMetadata:input_type -> moego.client.business_customer.v1.ListPetMetadataParams
	2,  // 7: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetTypes:input_type -> moego.client.business_customer.v1.ListPetTypesParams
	4,  // 8: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetBreeds:input_type -> moego.client.business_customer.v1.ListPetBreedsParams
	6,  // 9: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetCoatTypes:input_type -> moego.client.business_customer.v1.ListPetCoatTypesParams
	1,  // 10: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetMetadata:output_type -> moego.client.business_customer.v1.ListPetMetadataResult
	3,  // 11: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetTypes:output_type -> moego.client.business_customer.v1.ListPetTypesResult
	5,  // 12: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetBreeds:output_type -> moego.client.business_customer.v1.ListPetBreedsResult
	7,  // 13: moego.client.business_customer.v1.BusinessPetMetadataService.ListPetCoatTypes:output_type -> moego.client.business_customer.v1.ListPetCoatTypesResult
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_client_business_customer_v1_business_pet_metadata_api_proto_init() }
func file_moego_client_business_customer_v1_business_pet_metadata_api_proto_init() {
	if File_moego_client_business_customer_v1_business_pet_metadata_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ListPetMetadataParams_Name)(nil),
		(*ListPetMetadataParams_Domain)(nil),
	}
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_business_customer_v1_business_pet_metadata_api_proto_goTypes,
		DependencyIndexes: file_moego_client_business_customer_v1_business_pet_metadata_api_proto_depIdxs,
		MessageInfos:      file_moego_client_business_customer_v1_business_pet_metadata_api_proto_msgTypes,
	}.Build()
	File_moego_client_business_customer_v1_business_pet_metadata_api_proto = out.File
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_rawDesc = nil
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_goTypes = nil
	file_moego_client_business_customer_v1_business_pet_metadata_api_proto_depIdxs = nil
}
