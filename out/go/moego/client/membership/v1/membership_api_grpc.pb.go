// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/membership/v1/membership_api.proto

package membershipapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MembershipServiceClient is the client API for MembershipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MembershipServiceClient interface {
	// list membership
	ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error)
	// create sell link
	CreateSellLink(ctx context.Context, in *CreateSellLinkParams, opts ...grpc.CallOption) (*CreateSellLinkResult, error)
	// list membership for sale
	ListMembershipsForSale(ctx context.Context, in *ListMembershipsForSaleParams, opts ...grpc.CallOption) (*ListMembershipsForSaleResult, error)
	// create sell link for branded app
	CreateSellLinkForApp(ctx context.Context, in *CreateSellLinkForAppParams, opts ...grpc.CallOption) (*CreateSellLinkForAppResult, error)
	// list membership for sale for branded app
	ListMembershipsForSaleForApp(ctx context.Context, in *ListMembershipsForSaleForAppParams, opts ...grpc.CallOption) (*ListMembershipsForSaleForAppResult, error)
	// list memberships without not applicable service perks
	ListMembershipsWithoutNotApplicableServicePerks(ctx context.Context, in *ListMembershipsWithoutNotApplicableServicePerksParams, opts ...grpc.CallOption) (*ListMembershipsWithoutNotApplicableServicePerksResult, error)
}

type membershipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMembershipServiceClient(cc grpc.ClientConnInterface) MembershipServiceClient {
	return &membershipServiceClient{cc}
}

func (c *membershipServiceClient) ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error) {
	out := new(ListMembershipsResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/ListMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) CreateSellLink(ctx context.Context, in *CreateSellLinkParams, opts ...grpc.CallOption) (*CreateSellLinkResult, error) {
	out := new(CreateSellLinkResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/CreateSellLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMembershipsForSale(ctx context.Context, in *ListMembershipsForSaleParams, opts ...grpc.CallOption) (*ListMembershipsForSaleResult, error) {
	out := new(ListMembershipsForSaleResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/ListMembershipsForSale", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) CreateSellLinkForApp(ctx context.Context, in *CreateSellLinkForAppParams, opts ...grpc.CallOption) (*CreateSellLinkForAppResult, error) {
	out := new(CreateSellLinkForAppResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/CreateSellLinkForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMembershipsForSaleForApp(ctx context.Context, in *ListMembershipsForSaleForAppParams, opts ...grpc.CallOption) (*ListMembershipsForSaleForAppResult, error) {
	out := new(ListMembershipsForSaleForAppResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/ListMembershipsForSaleForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMembershipsWithoutNotApplicableServicePerks(ctx context.Context, in *ListMembershipsWithoutNotApplicableServicePerksParams, opts ...grpc.CallOption) (*ListMembershipsWithoutNotApplicableServicePerksResult, error) {
	out := new(ListMembershipsWithoutNotApplicableServicePerksResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.MembershipService/ListMembershipsWithoutNotApplicableServicePerks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MembershipServiceServer is the server API for MembershipService service.
// All implementations must embed UnimplementedMembershipServiceServer
// for forward compatibility
type MembershipServiceServer interface {
	// list membership
	ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error)
	// create sell link
	CreateSellLink(context.Context, *CreateSellLinkParams) (*CreateSellLinkResult, error)
	// list membership for sale
	ListMembershipsForSale(context.Context, *ListMembershipsForSaleParams) (*ListMembershipsForSaleResult, error)
	// create sell link for branded app
	CreateSellLinkForApp(context.Context, *CreateSellLinkForAppParams) (*CreateSellLinkForAppResult, error)
	// list membership for sale for branded app
	ListMembershipsForSaleForApp(context.Context, *ListMembershipsForSaleForAppParams) (*ListMembershipsForSaleForAppResult, error)
	// list memberships without not applicable service perks
	ListMembershipsWithoutNotApplicableServicePerks(context.Context, *ListMembershipsWithoutNotApplicableServicePerksParams) (*ListMembershipsWithoutNotApplicableServicePerksResult, error)
	mustEmbedUnimplementedMembershipServiceServer()
}

// UnimplementedMembershipServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMembershipServiceServer struct {
}

func (UnimplementedMembershipServiceServer) ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemberships not implemented")
}
func (UnimplementedMembershipServiceServer) CreateSellLink(context.Context, *CreateSellLinkParams) (*CreateSellLinkResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSellLink not implemented")
}
func (UnimplementedMembershipServiceServer) ListMembershipsForSale(context.Context, *ListMembershipsForSaleParams) (*ListMembershipsForSaleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsForSale not implemented")
}
func (UnimplementedMembershipServiceServer) CreateSellLinkForApp(context.Context, *CreateSellLinkForAppParams) (*CreateSellLinkForAppResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSellLinkForApp not implemented")
}
func (UnimplementedMembershipServiceServer) ListMembershipsForSaleForApp(context.Context, *ListMembershipsForSaleForAppParams) (*ListMembershipsForSaleForAppResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsForSaleForApp not implemented")
}
func (UnimplementedMembershipServiceServer) ListMembershipsWithoutNotApplicableServicePerks(context.Context, *ListMembershipsWithoutNotApplicableServicePerksParams) (*ListMembershipsWithoutNotApplicableServicePerksResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsWithoutNotApplicableServicePerks not implemented")
}
func (UnimplementedMembershipServiceServer) mustEmbedUnimplementedMembershipServiceServer() {}

// UnsafeMembershipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MembershipServiceServer will
// result in compilation errors.
type UnsafeMembershipServiceServer interface {
	mustEmbedUnimplementedMembershipServiceServer()
}

func RegisterMembershipServiceServer(s grpc.ServiceRegistrar, srv MembershipServiceServer) {
	s.RegisterService(&MembershipService_ServiceDesc, srv)
}

func _MembershipService_ListMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/ListMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMemberships(ctx, req.(*ListMembershipsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_CreateSellLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSellLinkParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).CreateSellLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/CreateSellLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).CreateSellLink(ctx, req.(*CreateSellLinkParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMembershipsForSale_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsForSaleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMembershipsForSale(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/ListMembershipsForSale",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMembershipsForSale(ctx, req.(*ListMembershipsForSaleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_CreateSellLinkForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSellLinkForAppParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).CreateSellLinkForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/CreateSellLinkForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).CreateSellLinkForApp(ctx, req.(*CreateSellLinkForAppParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMembershipsForSaleForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsForSaleForAppParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMembershipsForSaleForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/ListMembershipsForSaleForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMembershipsForSaleForApp(ctx, req.(*ListMembershipsForSaleForAppParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMembershipsWithoutNotApplicableServicePerks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsWithoutNotApplicableServicePerksParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMembershipsWithoutNotApplicableServicePerks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.MembershipService/ListMembershipsWithoutNotApplicableServicePerks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMembershipsWithoutNotApplicableServicePerks(ctx, req.(*ListMembershipsWithoutNotApplicableServicePerksParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MembershipService_ServiceDesc is the grpc.ServiceDesc for MembershipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MembershipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.membership.v1.MembershipService",
	HandlerType: (*MembershipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListMemberships",
			Handler:    _MembershipService_ListMemberships_Handler,
		},
		{
			MethodName: "CreateSellLink",
			Handler:    _MembershipService_CreateSellLink_Handler,
		},
		{
			MethodName: "ListMembershipsForSale",
			Handler:    _MembershipService_ListMembershipsForSale_Handler,
		},
		{
			MethodName: "CreateSellLinkForApp",
			Handler:    _MembershipService_CreateSellLinkForApp_Handler,
		},
		{
			MethodName: "ListMembershipsForSaleForApp",
			Handler:    _MembershipService_ListMembershipsForSaleForApp_Handler,
		},
		{
			MethodName: "ListMembershipsWithoutNotApplicableServicePerks",
			Handler:    _MembershipService_ListMembershipsWithoutNotApplicableServicePerks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/membership/v1/membership_api.proto",
}
