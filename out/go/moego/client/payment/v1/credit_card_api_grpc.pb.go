// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/payment/v1/credit_card_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CreditCardServiceClient is the client API for CreditCardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CreditCardServiceClient interface {
	// get credit card list
	GetCreditCardList(ctx context.Context, in *GetCreditCardListRequest, opts ...grpc.CallOption) (*GetCreditCardListResponse, error)
	// List credit cards
	// Applies to account structure after migration
	ListCreditCards(ctx context.Context, in *ListCreditCardsParams, opts ...grpc.CallOption) (*ListCreditCardsResult, error)
	// Create a new credit card
	// Applies to account structure after migration
	CreateCreditCard(ctx context.Context, in *CreateCreditCardParams, opts ...grpc.CallOption) (*CreateCreditCardResult, error)
}

type creditCardServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCreditCardServiceClient(cc grpc.ClientConnInterface) CreditCardServiceClient {
	return &creditCardServiceClient{cc}
}

func (c *creditCardServiceClient) GetCreditCardList(ctx context.Context, in *GetCreditCardListRequest, opts ...grpc.CallOption) (*GetCreditCardListResponse, error) {
	out := new(GetCreditCardListResponse)
	err := c.cc.Invoke(ctx, "/moego.client.payment.v1.CreditCardService/GetCreditCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) ListCreditCards(ctx context.Context, in *ListCreditCardsParams, opts ...grpc.CallOption) (*ListCreditCardsResult, error) {
	out := new(ListCreditCardsResult)
	err := c.cc.Invoke(ctx, "/moego.client.payment.v1.CreditCardService/ListCreditCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *creditCardServiceClient) CreateCreditCard(ctx context.Context, in *CreateCreditCardParams, opts ...grpc.CallOption) (*CreateCreditCardResult, error) {
	out := new(CreateCreditCardResult)
	err := c.cc.Invoke(ctx, "/moego.client.payment.v1.CreditCardService/CreateCreditCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CreditCardServiceServer is the server API for CreditCardService service.
// All implementations must embed UnimplementedCreditCardServiceServer
// for forward compatibility
type CreditCardServiceServer interface {
	// get credit card list
	GetCreditCardList(context.Context, *GetCreditCardListRequest) (*GetCreditCardListResponse, error)
	// List credit cards
	// Applies to account structure after migration
	ListCreditCards(context.Context, *ListCreditCardsParams) (*ListCreditCardsResult, error)
	// Create a new credit card
	// Applies to account structure after migration
	CreateCreditCard(context.Context, *CreateCreditCardParams) (*CreateCreditCardResult, error)
	mustEmbedUnimplementedCreditCardServiceServer()
}

// UnimplementedCreditCardServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCreditCardServiceServer struct {
}

func (UnimplementedCreditCardServiceServer) GetCreditCardList(context.Context, *GetCreditCardListRequest) (*GetCreditCardListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCreditCardList not implemented")
}
func (UnimplementedCreditCardServiceServer) ListCreditCards(context.Context, *ListCreditCardsParams) (*ListCreditCardsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCreditCards not implemented")
}
func (UnimplementedCreditCardServiceServer) CreateCreditCard(context.Context, *CreateCreditCardParams) (*CreateCreditCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCreditCard not implemented")
}
func (UnimplementedCreditCardServiceServer) mustEmbedUnimplementedCreditCardServiceServer() {}

// UnsafeCreditCardServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CreditCardServiceServer will
// result in compilation errors.
type UnsafeCreditCardServiceServer interface {
	mustEmbedUnimplementedCreditCardServiceServer()
}

func RegisterCreditCardServiceServer(s grpc.ServiceRegistrar, srv CreditCardServiceServer) {
	s.RegisterService(&CreditCardService_ServiceDesc, srv)
}

func _CreditCardService_GetCreditCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCreditCardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).GetCreditCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.payment.v1.CreditCardService/GetCreditCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).GetCreditCardList(ctx, req.(*GetCreditCardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_ListCreditCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCreditCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).ListCreditCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.payment.v1.CreditCardService/ListCreditCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).ListCreditCards(ctx, req.(*ListCreditCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CreditCardService_CreateCreditCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCreditCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CreditCardServiceServer).CreateCreditCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.payment.v1.CreditCardService/CreateCreditCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CreditCardServiceServer).CreateCreditCard(ctx, req.(*CreateCreditCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CreditCardService_ServiceDesc is the grpc.ServiceDesc for CreditCardService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CreditCardService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.payment.v1.CreditCardService",
	HandlerType: (*CreditCardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCreditCardList",
			Handler:    _CreditCardService_GetCreditCardList_Handler,
		},
		{
			MethodName: "ListCreditCards",
			Handler:    _CreditCardService_ListCreditCards_Handler,
		},
		{
			MethodName: "CreateCreditCard",
			Handler:    _CreditCardService_CreateCreditCard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/payment/v1/credit_card_api.proto",
}
