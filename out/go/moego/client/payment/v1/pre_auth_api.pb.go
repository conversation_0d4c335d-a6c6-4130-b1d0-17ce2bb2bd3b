// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/payment/v1/pre_auth_api.proto

package paymentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get pre-auth amount request
type GetPreAuthAmountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v1.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// discount code
	DiscountCode *string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
}

func (x *GetPreAuthAmountRequest) Reset() {
	*x = GetPreAuthAmountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreAuthAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreAuthAmountRequest) ProtoMessage() {}

func (x *GetPreAuthAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreAuthAmountRequest.ProtoReflect.Descriptor instead.
func (*GetPreAuthAmountRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_payment_v1_pre_auth_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetPreAuthAmountRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetPreAuthAmountRequest) GetSelectedPetServices() []*v1.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *GetPreAuthAmountRequest) GetDiscountCode() string {
	if x != nil && x.DiscountCode != nil {
		return *x.DiscountCode
	}
	return ""
}

// get pre-auth amount response
type GetPreAuthAmountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pre-auth amount
	PreAuth *v1.PreAuthDef `protobuf:"bytes,1,opt,name=pre_auth,json=preAuth,proto3" json:"pre_auth,omitempty"`
	// service charge list
	ServiceCharges []*v11.ServiceChargeOnlineBookingView `protobuf:"bytes,2,rep,name=service_charges,json=serviceCharges,proto3" json:"service_charges,omitempty"`
}

func (x *GetPreAuthAmountResponse) Reset() {
	*x = GetPreAuthAmountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreAuthAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreAuthAmountResponse) ProtoMessage() {}

func (x *GetPreAuthAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreAuthAmountResponse.ProtoReflect.Descriptor instead.
func (*GetPreAuthAmountResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_payment_v1_pre_auth_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetPreAuthAmountResponse) GetPreAuth() *v1.PreAuthDef {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *GetPreAuthAmountResponse) GetServiceCharges() []*v11.ServiceChargeOnlineBookingView {
	if x != nil {
		return x.ServiceCharges
	}
	return nil
}

var File_moego_client_payment_v1_pre_auth_api_proto protoreflect.FileDescriptor

var file_moego_client_payment_v1_pre_auth_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69,
	0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xc1,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x44, 0x65, 0x66, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x5e, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x32, 0x89, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x77, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x41,
	0x75, 0x74, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7e,
	0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_payment_v1_pre_auth_api_proto_rawDescOnce sync.Once
	file_moego_client_payment_v1_pre_auth_api_proto_rawDescData = file_moego_client_payment_v1_pre_auth_api_proto_rawDesc
)

func file_moego_client_payment_v1_pre_auth_api_proto_rawDescGZIP() []byte {
	file_moego_client_payment_v1_pre_auth_api_proto_rawDescOnce.Do(func() {
		file_moego_client_payment_v1_pre_auth_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_payment_v1_pre_auth_api_proto_rawDescData)
	})
	return file_moego_client_payment_v1_pre_auth_api_proto_rawDescData
}

var file_moego_client_payment_v1_pre_auth_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_payment_v1_pre_auth_api_proto_goTypes = []interface{}{
	(*GetPreAuthAmountRequest)(nil),            // 0: moego.client.payment.v1.GetPreAuthAmountRequest
	(*GetPreAuthAmountResponse)(nil),           // 1: moego.client.payment.v1.GetPreAuthAmountResponse
	(*v1.SelectedPetServiceDef)(nil),           // 2: moego.models.online_booking.v1.SelectedPetServiceDef
	(*v1.PreAuthDef)(nil),                      // 3: moego.models.online_booking.v1.PreAuthDef
	(*v11.ServiceChargeOnlineBookingView)(nil), // 4: moego.models.order.v1.ServiceChargeOnlineBookingView
}
var file_moego_client_payment_v1_pre_auth_api_proto_depIdxs = []int32{
	2, // 0: moego.client.payment.v1.GetPreAuthAmountRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	3, // 1: moego.client.payment.v1.GetPreAuthAmountResponse.pre_auth:type_name -> moego.models.online_booking.v1.PreAuthDef
	4, // 2: moego.client.payment.v1.GetPreAuthAmountResponse.service_charges:type_name -> moego.models.order.v1.ServiceChargeOnlineBookingView
	0, // 3: moego.client.payment.v1.PreAuthService.GetPreAuthAmount:input_type -> moego.client.payment.v1.GetPreAuthAmountRequest
	1, // 4: moego.client.payment.v1.PreAuthService.GetPreAuthAmount:output_type -> moego.client.payment.v1.GetPreAuthAmountResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_client_payment_v1_pre_auth_api_proto_init() }
func file_moego_client_payment_v1_pre_auth_api_proto_init() {
	if File_moego_client_payment_v1_pre_auth_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreAuthAmountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreAuthAmountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_payment_v1_pre_auth_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_payment_v1_pre_auth_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_payment_v1_pre_auth_api_proto_goTypes,
		DependencyIndexes: file_moego_client_payment_v1_pre_auth_api_proto_depIdxs,
		MessageInfos:      file_moego_client_payment_v1_pre_auth_api_proto_msgTypes,
	}.Build()
	File_moego_client_payment_v1_pre_auth_api_proto = out.File
	file_moego_client_payment_v1_pre_auth_api_proto_rawDesc = nil
	file_moego_client_payment_v1_pre_auth_api_proto_goTypes = nil
	file_moego_client_payment_v1_pre_auth_api_proto_depIdxs = nil
}
