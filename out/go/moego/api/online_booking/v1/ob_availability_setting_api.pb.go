// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_availability_setting_api.proto

package onlinebookingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get boarding service availability params
type GetBoardingServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBoardingServiceAvailabilityParams) Reset() {
	*x = GetBoardingServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBoardingServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBoardingServiceAvailabilityParams) ProtoMessage() {}

func (x *GetBoardingServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBoardingServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetBoardingServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetBoardingServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get boarding service availability result
type GetBoardingServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding service availability
	BoardingServiceAvailability *v1.BoardingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=boarding_service_availability,json=boardingServiceAvailability,proto3" json:"boarding_service_availability,omitempty"`
}

func (x *GetBoardingServiceAvailabilityResult) Reset() {
	*x = GetBoardingServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBoardingServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBoardingServiceAvailabilityResult) ProtoMessage() {}

func (x *GetBoardingServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBoardingServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetBoardingServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetBoardingServiceAvailabilityResult) GetBoardingServiceAvailability() *v1.BoardingServiceAvailabilityModel {
	if x != nil {
		return x.BoardingServiceAvailability
	}
	return nil
}

// update boarding service availability params
type UpdateBoardingServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// boarding service availability
	BoardingServiceAvailability *v1.BoardingServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=boarding_service_availability,json=boardingServiceAvailability,proto3" json:"boarding_service_availability,omitempty"`
}

func (x *UpdateBoardingServiceAvailabilityParams) Reset() {
	*x = UpdateBoardingServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingServiceAvailabilityParams) ProtoMessage() {}

func (x *UpdateBoardingServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateBoardingServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBoardingServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateBoardingServiceAvailabilityParams) GetBoardingServiceAvailability() *v1.BoardingServiceAvailabilityUpdateDef {
	if x != nil {
		return x.BoardingServiceAvailability
	}
	return nil
}

// update boarding service availability result
type UpdateBoardingServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding service availability
	BoardingServiceAvailability *v1.BoardingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=boarding_service_availability,json=boardingServiceAvailability,proto3" json:"boarding_service_availability,omitempty"`
}

func (x *UpdateBoardingServiceAvailabilityResult) Reset() {
	*x = UpdateBoardingServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingServiceAvailabilityResult) ProtoMessage() {}

func (x *UpdateBoardingServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateBoardingServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBoardingServiceAvailabilityResult) GetBoardingServiceAvailability() *v1.BoardingServiceAvailabilityModel {
	if x != nil {
		return x.BoardingServiceAvailability
	}
	return nil
}

// get daycare service availability params
type GetDaycareServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetDaycareServiceAvailabilityParams) Reset() {
	*x = GetDaycareServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaycareServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaycareServiceAvailabilityParams) ProtoMessage() {}

func (x *GetDaycareServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaycareServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetDaycareServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetDaycareServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get daycare service availability result
type GetDaycareServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare service availability
	DaycareServiceAvailability *v1.DaycareServiceAvailabilityModel `protobuf:"bytes,1,opt,name=daycare_service_availability,json=daycareServiceAvailability,proto3" json:"daycare_service_availability,omitempty"`
}

func (x *GetDaycareServiceAvailabilityResult) Reset() {
	*x = GetDaycareServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaycareServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaycareServiceAvailabilityResult) ProtoMessage() {}

func (x *GetDaycareServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaycareServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetDaycareServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetDaycareServiceAvailabilityResult) GetDaycareServiceAvailability() *v1.DaycareServiceAvailabilityModel {
	if x != nil {
		return x.DaycareServiceAvailability
	}
	return nil
}

// update daycare service availability params
type UpdateDaycareServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// daycare service availability
	DaycareServiceAvailability *v1.DaycareServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=daycare_service_availability,json=daycareServiceAvailability,proto3" json:"daycare_service_availability,omitempty"`
}

func (x *UpdateDaycareServiceAvailabilityParams) Reset() {
	*x = UpdateDaycareServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDaycareServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDaycareServiceAvailabilityParams) ProtoMessage() {}

func (x *UpdateDaycareServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDaycareServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateDaycareServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateDaycareServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateDaycareServiceAvailabilityParams) GetDaycareServiceAvailability() *v1.DaycareServiceAvailabilityUpdateDef {
	if x != nil {
		return x.DaycareServiceAvailability
	}
	return nil
}

// update daycare service availability result
type UpdateDaycareServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daycare service availability
	DaycareServiceAvailability *v1.DaycareServiceAvailabilityModel `protobuf:"bytes,1,opt,name=daycare_service_availability,json=daycareServiceAvailability,proto3" json:"daycare_service_availability,omitempty"`
}

func (x *UpdateDaycareServiceAvailabilityResult) Reset() {
	*x = UpdateDaycareServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDaycareServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDaycareServiceAvailabilityResult) ProtoMessage() {}

func (x *UpdateDaycareServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDaycareServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateDaycareServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateDaycareServiceAvailabilityResult) GetDaycareServiceAvailability() *v1.DaycareServiceAvailabilityModel {
	if x != nil {
		return x.DaycareServiceAvailability
	}
	return nil
}

// get evaluation service availability params
type GetEvaluationServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetEvaluationServiceAvailabilityParams) Reset() {
	*x = GetEvaluationServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationServiceAvailabilityParams) ProtoMessage() {}

func (x *GetEvaluationServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetEvaluationServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetEvaluationServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get evaluation service availability result
type GetEvaluationServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation service availability
	Availability *v1.EvaluationServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *GetEvaluationServiceAvailabilityResult) Reset() {
	*x = GetEvaluationServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationServiceAvailabilityResult) ProtoMessage() {}

func (x *GetEvaluationServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetEvaluationServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetEvaluationServiceAvailabilityResult) GetAvailability() *v1.EvaluationServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update evaluation service availability params
type UpdateEvaluationServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// evaluation service availability
	Availability *v1.EvaluationServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateEvaluationServiceAvailabilityParams) Reset() {
	*x = UpdateEvaluationServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationServiceAvailabilityParams) ProtoMessage() {}

func (x *UpdateEvaluationServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateEvaluationServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateEvaluationServiceAvailabilityParams) GetAvailability() *v1.EvaluationServiceAvailabilityUpdateDef {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update evaluation service availability result
type UpdateEvaluationServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation service availability
	Availability *v1.EvaluationServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateEvaluationServiceAvailabilityResult) Reset() {
	*x = UpdateEvaluationServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationServiceAvailabilityResult) ProtoMessage() {}

func (x *UpdateEvaluationServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateEvaluationServiceAvailabilityResult) GetAvailability() *v1.EvaluationServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// get grooming service availability params
type GetGroomingServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetGroomingServiceAvailabilityParams) Reset() {
	*x = GetGroomingServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingServiceAvailabilityParams) ProtoMessage() {}

func (x *GetGroomingServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetGroomingServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetGroomingServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get grooming service availability result
type GetGroomingServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming service availability
	Availability *v1.GroomingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *GetGroomingServiceAvailabilityResult) Reset() {
	*x = GetGroomingServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingServiceAvailabilityResult) ProtoMessage() {}

func (x *GetGroomingServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetGroomingServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetGroomingServiceAvailabilityResult) GetAvailability() *v1.GroomingServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update grooming service availability params
type UpdateGroomingServiceAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// grooming service availability
	Availability *v1.GroomingServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateGroomingServiceAvailabilityParams) Reset() {
	*x = UpdateGroomingServiceAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroomingServiceAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroomingServiceAvailabilityParams) ProtoMessage() {}

func (x *UpdateGroomingServiceAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroomingServiceAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateGroomingServiceAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateGroomingServiceAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateGroomingServiceAvailabilityParams) GetAvailability() *v1.GroomingServiceAvailabilityUpdateDef {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update grooming service availability result
type UpdateGroomingServiceAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming service availability
	Availability *v1.GroomingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateGroomingServiceAvailabilityResult) Reset() {
	*x = UpdateGroomingServiceAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroomingServiceAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroomingServiceAvailabilityResult) ProtoMessage() {}

func (x *UpdateGroomingServiceAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroomingServiceAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateGroomingServiceAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateGroomingServiceAvailabilityResult) GetAvailability() *v1.GroomingServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// arrival/pick up time model
type ArrivalPickUpTimeOverrideView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// arrival time range
	DayTimeRanges []*v1.DayTimeRangeDef `protobuf:"bytes,5,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
	// is_active
	IsActive bool `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
}

func (x *ArrivalPickUpTimeOverrideView) Reset() {
	*x = ArrivalPickUpTimeOverrideView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrivalPickUpTimeOverrideView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrivalPickUpTimeOverrideView) ProtoMessage() {}

func (x *ArrivalPickUpTimeOverrideView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrivalPickUpTimeOverrideView.ProtoReflect.Descriptor instead.
func (*ArrivalPickUpTimeOverrideView) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{16}
}

func (x *ArrivalPickUpTimeOverrideView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArrivalPickUpTimeOverrideView) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideView) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideView) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *ArrivalPickUpTimeOverrideView) GetDayTimeRanges() []*v1.DayTimeRangeDef {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideView) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// List arrival pick up time overrides params
type ListArrivalPickUpTimeOverridesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// service item types, optional
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ListArrivalPickUpTimeOverridesParams) Reset() {
	*x = ListArrivalPickUpTimeOverridesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArrivalPickUpTimeOverridesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArrivalPickUpTimeOverridesParams) ProtoMessage() {}

func (x *ListArrivalPickUpTimeOverridesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArrivalPickUpTimeOverridesParams.ProtoReflect.Descriptor instead.
func (*ListArrivalPickUpTimeOverridesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{17}
}

func (x *ListArrivalPickUpTimeOverridesParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ListArrivalPickUpTimeOverridesParams) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// List arrival pick up time overrides  result
type ListArrivalPickUpTimeOverridesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival time override
	ArrivalTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,1,rep,name=arrival_time_overrides,json=arrivalTimeOverrides,proto3" json:"arrival_time_overrides,omitempty"`
	// pick up time override
	PickUpTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,2,rep,name=pick_up_time_overrides,json=pickUpTimeOverrides,proto3" json:"pick_up_time_overrides,omitempty"`
}

func (x *ListArrivalPickUpTimeOverridesResult) Reset() {
	*x = ListArrivalPickUpTimeOverridesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArrivalPickUpTimeOverridesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArrivalPickUpTimeOverridesResult) ProtoMessage() {}

func (x *ListArrivalPickUpTimeOverridesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArrivalPickUpTimeOverridesResult.ProtoReflect.Descriptor instead.
func (*ListArrivalPickUpTimeOverridesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{18}
}

func (x *ListArrivalPickUpTimeOverridesResult) GetArrivalTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.ArrivalTimeOverrides
	}
	return nil
}

func (x *ListArrivalPickUpTimeOverridesResult) GetPickUpTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.PickUpTimeOverrides
	}
	return nil
}

// batch create arrival pick up time override params
type BatchCreateArrivalPickUpTimeOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// arrival time overrides
	ArrivalTimeOverrides []*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef `protobuf:"bytes,3,rep,name=arrival_time_overrides,json=arrivalTimeOverrides,proto3" json:"arrival_time_overrides,omitempty"`
	// pick up time overrides
	PickUpTimeOverrides []*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef `protobuf:"bytes,4,rep,name=pick_up_time_overrides,json=pickUpTimeOverrides,proto3" json:"pick_up_time_overrides,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideParams) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideParams.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{19}
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) GetArrivalTimeOverrides() []*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef {
	if x != nil {
		return x.ArrivalTimeOverrides
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams) GetPickUpTimeOverrides() []*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef {
	if x != nil {
		return x.PickUpTimeOverrides
	}
	return nil
}

// batch create arrival pick up time override result
type BatchCreateArrivalPickUpTimeOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival time overrides
	ArrivalTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,1,rep,name=arrival_time_overrides,json=arrivalTimeOverrides,proto3" json:"arrival_time_overrides,omitempty"`
	// pick up time overrides
	PickUpTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,2,rep,name=pick_up_time_overrides,json=pickUpTimeOverrides,proto3" json:"pick_up_time_overrides,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideResult) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideResult) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideResult.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{20}
}

func (x *BatchCreateArrivalPickUpTimeOverrideResult) GetArrivalTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.ArrivalTimeOverrides
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideResult) GetPickUpTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.PickUpTimeOverrides
	}
	return nil
}

// batch delete arrival pick up time override params
type BatchDeleteArrivalPickUpTimeOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// arrival pick up time override ids
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BatchDeleteArrivalPickUpTimeOverrideParams) Reset() {
	*x = BatchDeleteArrivalPickUpTimeOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteArrivalPickUpTimeOverrideParams) ProtoMessage() {}

func (x *BatchDeleteArrivalPickUpTimeOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteArrivalPickUpTimeOverrideParams.ProtoReflect.Descriptor instead.
func (*BatchDeleteArrivalPickUpTimeOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{21}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchDeleteArrivalPickUpTimeOverrideParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// batch delete arrival pick up time override result
type BatchDeleteArrivalPickUpTimeOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteArrivalPickUpTimeOverrideResult) Reset() {
	*x = BatchDeleteArrivalPickUpTimeOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteArrivalPickUpTimeOverrideResult) ProtoMessage() {}

func (x *BatchDeleteArrivalPickUpTimeOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteArrivalPickUpTimeOverrideResult.ProtoReflect.Descriptor instead.
func (*BatchDeleteArrivalPickUpTimeOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{22}
}

// batch update arrival pick up time override params
type BatchUpdateArrivalPickUpTimeOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// arrival time overrides
	ArrivalTimeOverrides []*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef `protobuf:"bytes,2,rep,name=arrival_time_overrides,json=arrivalTimeOverrides,proto3" json:"arrival_time_overrides,omitempty"`
	// pick up time overrides
	PickUpTimeOverrides []*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef `protobuf:"bytes,3,rep,name=pick_up_time_overrides,json=pickUpTimeOverrides,proto3" json:"pick_up_time_overrides,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideParams) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideParams.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{23}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) GetArrivalTimeOverrides() []*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef {
	if x != nil {
		return x.ArrivalTimeOverrides
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams) GetPickUpTimeOverrides() []*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef {
	if x != nil {
		return x.PickUpTimeOverrides
	}
	return nil
}

// batch update arrival pick up time override result
type BatchUpdateArrivalPickUpTimeOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival time overrides
	ArrivalTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,1,rep,name=arrival_time_overrides,json=arrivalTimeOverrides,proto3" json:"arrival_time_overrides,omitempty"`
	// pick up time overrides
	PickUpTimeOverrides []*ArrivalPickUpTimeOverrideView `protobuf:"bytes,2,rep,name=pick_up_time_overrides,json=pickUpTimeOverrides,proto3" json:"pick_up_time_overrides,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResult) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideResult) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideResult.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{24}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResult) GetArrivalTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.ArrivalTimeOverrides
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResult) GetPickUpTimeOverrides() []*ArrivalPickUpTimeOverrideView {
	if x != nil {
		return x.PickUpTimeOverrides
	}
	return nil
}

// create capacity override params
type CreateCapacityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// capacity override
	CapacityOverride *v1.CapacityOverrideDef `protobuf:"bytes,3,opt,name=capacity_override,json=capacityOverride,proto3" json:"capacity_override,omitempty"`
}

func (x *CreateCapacityOverrideParams) Reset() {
	*x = CreateCapacityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCapacityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCapacityOverrideParams) ProtoMessage() {}

func (x *CreateCapacityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCapacityOverrideParams.ProtoReflect.Descriptor instead.
func (*CreateCapacityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{25}
}

func (x *CreateCapacityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCapacityOverrideParams) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *CreateCapacityOverrideParams) GetCapacityOverride() *v1.CapacityOverrideDef {
	if x != nil {
		return x.CapacityOverride
	}
	return nil
}

// create capacity override result
type CreateCapacityOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v1.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *CreateCapacityOverrideResult) Reset() {
	*x = CreateCapacityOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCapacityOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCapacityOverrideResult) ProtoMessage() {}

func (x *CreateCapacityOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCapacityOverrideResult.ProtoReflect.Descriptor instead.
func (*CreateCapacityOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{26}
}

func (x *CreateCapacityOverrideResult) GetCapacityOverrides() []*v1.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// delete capacity override params
type DeleteCapacityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// capacity override id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteCapacityOverrideParams) Reset() {
	*x = DeleteCapacityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCapacityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCapacityOverrideParams) ProtoMessage() {}

func (x *DeleteCapacityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCapacityOverrideParams.ProtoReflect.Descriptor instead.
func (*DeleteCapacityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteCapacityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteCapacityOverrideParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete capacity override result
type DeleteCapacityOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v1.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *DeleteCapacityOverrideResult) Reset() {
	*x = DeleteCapacityOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCapacityOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCapacityOverrideResult) ProtoMessage() {}

func (x *DeleteCapacityOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCapacityOverrideResult.ProtoReflect.Descriptor instead.
func (*DeleteCapacityOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteCapacityOverrideResult) GetCapacityOverrides() []*v1.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// update capacity override params
type UpdateCapacityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// capacity override
	CapacityOverride *v1.CapacityOverrideDef `protobuf:"bytes,2,opt,name=capacity_override,json=capacityOverride,proto3" json:"capacity_override,omitempty"`
}

func (x *UpdateCapacityOverrideParams) Reset() {
	*x = UpdateCapacityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityOverrideParams) ProtoMessage() {}

func (x *UpdateCapacityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityOverrideParams.ProtoReflect.Descriptor instead.
func (*UpdateCapacityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateCapacityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateCapacityOverrideParams) GetCapacityOverride() *v1.CapacityOverrideDef {
	if x != nil {
		return x.CapacityOverride
	}
	return nil
}

// update capacity override result
type UpdateCapacityOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v1.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *UpdateCapacityOverrideResult) Reset() {
	*x = UpdateCapacityOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityOverrideResult) ProtoMessage() {}

func (x *UpdateCapacityOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityOverrideResult.ProtoReflect.Descriptor instead.
func (*UpdateCapacityOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateCapacityOverrideResult) GetCapacityOverrides() []*v1.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// arrival/pick up time override crate params
type BatchCreateArrivalPickUpTimeOverrideParams_CreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,3,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// arrival time range
	DayTimeRanges []*v1.DayTimeRangeDef `protobuf:"bytes,4,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideParams_CreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideParams_CreateDef.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{19, 0}
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *BatchCreateArrivalPickUpTimeOverrideParams_CreateDef) GetDayTimeRanges() []*v1.DayTimeRangeDef {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

// arrival/pick up time override update params
type BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// is available
	IsAvailable *bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// day time range list
	DayTimeRanges *v1.DayTimeRangeDefList `protobuf:"bytes,5,opt,name=day_time_ranges,json=dayTimeRanges,proto3,oneof" json:"day_time_ranges,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP(), []int{23, 0}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef) GetDayTimeRanges() *v1.DayTimeRangeDefList {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

var File_moego_api_online_booking_v1_ob_availability_setting_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x50, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01,
	0x0a, 0x1d, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x1b, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x22, 0xde, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x88, 0x01, 0x0a, 0x1d, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x1b, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xb0, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x84, 0x01, 0x0a, 0x1d, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x1b, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x4f, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xa9, 0x01, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x81, 0x01, 0x0a, 0x1c, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x1a, 0x64, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xda, 0x01, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x85, 0x01, 0x0a, 0x1c, 0x64,
	0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x1a, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x22, 0xac, 0x01, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01,
	0x0a, 0x1c, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x1a, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x22, 0x52, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x66, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xc1, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x6a, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0c,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x93, 0x01, 0x0a,
	0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x66, 0x0a, 0x0c, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x22, 0x50, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a,
	0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x22, 0xbd, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x0c, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x22, 0x8f, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x64, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xa8, 0x02, 0x0a, 0x1d, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x57, 0x0a, 0x0f, 0x64, 0x61,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x22, 0xcf, 0x01, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x22, 0x89, 0x02, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x16, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x14, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x6f, 0x0a,
	0x16, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x70, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0xb6,
	0x05, 0x0a, 0x2a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x14,
	0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x70, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x1a, 0xe7, 0x01,
	0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x30, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x57,
	0x0a, 0x0f, 0x64, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0x8f, 0x02, 0x0a, 0x2a, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x14, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x6f, 0x0a, 0x16, 0x70, 0x69, 0x63, 0x6b,
	0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x70, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x2a, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13,
	0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x2c, 0x0a, 0x2a, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc5, 0x05, 0x0a, 0x2a, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63,
	0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x87, 0x01, 0x0a, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x14, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x70, 0x69,
	0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x70,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x73, 0x1a, 0xd9, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x69, 0x73, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x0f, 0x64,
	0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x03, 0x52, 0x0d, 0x64, 0x61, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x64,
	0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0x8f,
	0x02, 0x0a, 0x2a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a,
	0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x14, 0x61, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12,
	0x6f, 0x0a, 0x16, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x70, 0x69, 0x63,
	0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73,
	0x22, 0x97, 0x02, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6a,
	0x0a, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a, 0x12, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x22, 0x61, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x1c,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x10, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x32, 0xbb, 0x14, 0x0a, 0x1c, 0x4f, 0x42,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xb1, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xa5, 0x01, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x40, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0xae, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0xae, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0xb7, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xa8, 0x01,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xb1, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x44,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a,
	0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xba, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x12, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0xba, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x47, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0xba, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x47, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x90,
	0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x8c, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_moego_api_online_booking_v1_ob_availability_setting_api_proto_goTypes = []interface{}{
	(*GetBoardingServiceAvailabilityParams)(nil),                 // 0: moego.api.online_booking.v1.GetBoardingServiceAvailabilityParams
	(*GetBoardingServiceAvailabilityResult)(nil),                 // 1: moego.api.online_booking.v1.GetBoardingServiceAvailabilityResult
	(*UpdateBoardingServiceAvailabilityParams)(nil),              // 2: moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityParams
	(*UpdateBoardingServiceAvailabilityResult)(nil),              // 3: moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityResult
	(*GetDaycareServiceAvailabilityParams)(nil),                  // 4: moego.api.online_booking.v1.GetDaycareServiceAvailabilityParams
	(*GetDaycareServiceAvailabilityResult)(nil),                  // 5: moego.api.online_booking.v1.GetDaycareServiceAvailabilityResult
	(*UpdateDaycareServiceAvailabilityParams)(nil),               // 6: moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityParams
	(*UpdateDaycareServiceAvailabilityResult)(nil),               // 7: moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityResult
	(*GetEvaluationServiceAvailabilityParams)(nil),               // 8: moego.api.online_booking.v1.GetEvaluationServiceAvailabilityParams
	(*GetEvaluationServiceAvailabilityResult)(nil),               // 9: moego.api.online_booking.v1.GetEvaluationServiceAvailabilityResult
	(*UpdateEvaluationServiceAvailabilityParams)(nil),            // 10: moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityParams
	(*UpdateEvaluationServiceAvailabilityResult)(nil),            // 11: moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityResult
	(*GetGroomingServiceAvailabilityParams)(nil),                 // 12: moego.api.online_booking.v1.GetGroomingServiceAvailabilityParams
	(*GetGroomingServiceAvailabilityResult)(nil),                 // 13: moego.api.online_booking.v1.GetGroomingServiceAvailabilityResult
	(*UpdateGroomingServiceAvailabilityParams)(nil),              // 14: moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityParams
	(*UpdateGroomingServiceAvailabilityResult)(nil),              // 15: moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityResult
	(*ArrivalPickUpTimeOverrideView)(nil),                        // 16: moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	(*ListArrivalPickUpTimeOverridesParams)(nil),                 // 17: moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesParams
	(*ListArrivalPickUpTimeOverridesResult)(nil),                 // 18: moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesResult
	(*BatchCreateArrivalPickUpTimeOverrideParams)(nil),           // 19: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams
	(*BatchCreateArrivalPickUpTimeOverrideResult)(nil),           // 20: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResult
	(*BatchDeleteArrivalPickUpTimeOverrideParams)(nil),           // 21: moego.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideParams
	(*BatchDeleteArrivalPickUpTimeOverrideResult)(nil),           // 22: moego.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResult
	(*BatchUpdateArrivalPickUpTimeOverrideParams)(nil),           // 23: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams
	(*BatchUpdateArrivalPickUpTimeOverrideResult)(nil),           // 24: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResult
	(*CreateCapacityOverrideParams)(nil),                         // 25: moego.api.online_booking.v1.CreateCapacityOverrideParams
	(*CreateCapacityOverrideResult)(nil),                         // 26: moego.api.online_booking.v1.CreateCapacityOverrideResult
	(*DeleteCapacityOverrideParams)(nil),                         // 27: moego.api.online_booking.v1.DeleteCapacityOverrideParams
	(*DeleteCapacityOverrideResult)(nil),                         // 28: moego.api.online_booking.v1.DeleteCapacityOverrideResult
	(*UpdateCapacityOverrideParams)(nil),                         // 29: moego.api.online_booking.v1.UpdateCapacityOverrideParams
	(*UpdateCapacityOverrideResult)(nil),                         // 30: moego.api.online_booking.v1.UpdateCapacityOverrideResult
	(*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef)(nil), // 31: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef
	(*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef)(nil), // 32: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef
	(*v1.BoardingServiceAvailabilityModel)(nil),                  // 33: moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	(*v1.BoardingServiceAvailabilityUpdateDef)(nil),              // 34: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef
	(*v1.DaycareServiceAvailabilityModel)(nil),                   // 35: moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	(*v1.DaycareServiceAvailabilityUpdateDef)(nil),               // 36: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef
	(*v1.EvaluationServiceAvailabilityModel)(nil),                // 37: moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	(*v1.EvaluationServiceAvailabilityUpdateDef)(nil),            // 38: moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef
	(*v1.GroomingServiceAvailabilityModel)(nil),                  // 39: moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	(*v1.GroomingServiceAvailabilityUpdateDef)(nil),              // 40: moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef
	(*date.Date)(nil),                // 41: google.type.Date
	(*v1.DayTimeRangeDef)(nil),       // 42: moego.models.online_booking.v1.DayTimeRangeDef
	(v11.ServiceItemType)(0),         // 43: moego.models.offering.v1.ServiceItemType
	(*v1.CapacityOverrideDef)(nil),   // 44: moego.models.online_booking.v1.CapacityOverrideDef
	(*v1.CapacityOverrideModel)(nil), // 45: moego.models.online_booking.v1.CapacityOverrideModel
	(*v1.DayTimeRangeDefList)(nil),   // 46: moego.models.online_booking.v1.DayTimeRangeDefList
}
var file_moego_api_online_booking_v1_ob_availability_setting_api_proto_depIdxs = []int32{
	33, // 0: moego.api.online_booking.v1.GetBoardingServiceAvailabilityResult.boarding_service_availability:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	34, // 1: moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityParams.boarding_service_availability:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef
	33, // 2: moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityResult.boarding_service_availability:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	35, // 3: moego.api.online_booking.v1.GetDaycareServiceAvailabilityResult.daycare_service_availability:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	36, // 4: moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityParams.daycare_service_availability:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef
	35, // 5: moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityResult.daycare_service_availability:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	37, // 6: moego.api.online_booking.v1.GetEvaluationServiceAvailabilityResult.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	38, // 7: moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityParams.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef
	37, // 8: moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityResult.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	39, // 9: moego.api.online_booking.v1.GetGroomingServiceAvailabilityResult.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	40, // 10: moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityParams.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef
	39, // 11: moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityResult.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	41, // 12: moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView.start_date:type_name -> google.type.Date
	41, // 13: moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView.end_date:type_name -> google.type.Date
	42, // 14: moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	43, // 15: moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesParams.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	16, // 16: moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesResult.arrival_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	16, // 17: moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesResult.pick_up_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	43, // 18: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	31, // 19: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.arrival_time_overrides:type_name -> moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef
	31, // 20: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.pick_up_time_overrides:type_name -> moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef
	16, // 21: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResult.arrival_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	16, // 22: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResult.pick_up_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	32, // 23: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.arrival_time_overrides:type_name -> moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef
	32, // 24: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.pick_up_time_overrides:type_name -> moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef
	16, // 25: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResult.arrival_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	16, // 26: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResult.pick_up_time_overrides:type_name -> moego.api.online_booking.v1.ArrivalPickUpTimeOverrideView
	43, // 27: moego.api.online_booking.v1.CreateCapacityOverrideParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	44, // 28: moego.api.online_booking.v1.CreateCapacityOverrideParams.capacity_override:type_name -> moego.models.online_booking.v1.CapacityOverrideDef
	45, // 29: moego.api.online_booking.v1.CreateCapacityOverrideResult.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	45, // 30: moego.api.online_booking.v1.DeleteCapacityOverrideResult.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	44, // 31: moego.api.online_booking.v1.UpdateCapacityOverrideParams.capacity_override:type_name -> moego.models.online_booking.v1.CapacityOverrideDef
	45, // 32: moego.api.online_booking.v1.UpdateCapacityOverrideResult.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	41, // 33: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef.start_date:type_name -> google.type.Date
	41, // 34: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef.end_date:type_name -> google.type.Date
	42, // 35: moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams.CreateDef.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	41, // 36: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef.start_date:type_name -> google.type.Date
	41, // 37: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef.end_date:type_name -> google.type.Date
	46, // 38: moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDefList
	0,  // 39: moego.api.online_booking.v1.OBAvailabilitySettingService.GetBoardingServiceAvailability:input_type -> moego.api.online_booking.v1.GetBoardingServiceAvailabilityParams
	2,  // 40: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateBoardingServiceAvailability:input_type -> moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityParams
	4,  // 41: moego.api.online_booking.v1.OBAvailabilitySettingService.GetDaycareServiceAvailability:input_type -> moego.api.online_booking.v1.GetDaycareServiceAvailabilityParams
	6,  // 42: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateDaycareServiceAvailability:input_type -> moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityParams
	8,  // 43: moego.api.online_booking.v1.OBAvailabilitySettingService.GetEvaluationServiceAvailability:input_type -> moego.api.online_booking.v1.GetEvaluationServiceAvailabilityParams
	10, // 44: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateEvaluationServiceAvailability:input_type -> moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityParams
	12, // 45: moego.api.online_booking.v1.OBAvailabilitySettingService.GetGroomingServiceAvailability:input_type -> moego.api.online_booking.v1.GetGroomingServiceAvailabilityParams
	14, // 46: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateGroomingServiceAvailability:input_type -> moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityParams
	17, // 47: moego.api.online_booking.v1.OBAvailabilitySettingService.ListArrivalPickUpTimeOverrides:input_type -> moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesParams
	19, // 48: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchCreateArrivalPickUpTimeOverride:input_type -> moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams
	21, // 49: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchDeleteArrivalPickUpTimeOverride:input_type -> moego.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideParams
	23, // 50: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchUpdateArrivalPickUpTimeOverride:input_type -> moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams
	25, // 51: moego.api.online_booking.v1.OBAvailabilitySettingService.CreateCapacityOverride:input_type -> moego.api.online_booking.v1.CreateCapacityOverrideParams
	27, // 52: moego.api.online_booking.v1.OBAvailabilitySettingService.DeleteCapacityOverride:input_type -> moego.api.online_booking.v1.DeleteCapacityOverrideParams
	29, // 53: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateCapacityOverride:input_type -> moego.api.online_booking.v1.UpdateCapacityOverrideParams
	1,  // 54: moego.api.online_booking.v1.OBAvailabilitySettingService.GetBoardingServiceAvailability:output_type -> moego.api.online_booking.v1.GetBoardingServiceAvailabilityResult
	3,  // 55: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateBoardingServiceAvailability:output_type -> moego.api.online_booking.v1.UpdateBoardingServiceAvailabilityResult
	5,  // 56: moego.api.online_booking.v1.OBAvailabilitySettingService.GetDaycareServiceAvailability:output_type -> moego.api.online_booking.v1.GetDaycareServiceAvailabilityResult
	7,  // 57: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateDaycareServiceAvailability:output_type -> moego.api.online_booking.v1.UpdateDaycareServiceAvailabilityResult
	9,  // 58: moego.api.online_booking.v1.OBAvailabilitySettingService.GetEvaluationServiceAvailability:output_type -> moego.api.online_booking.v1.GetEvaluationServiceAvailabilityResult
	11, // 59: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateEvaluationServiceAvailability:output_type -> moego.api.online_booking.v1.UpdateEvaluationServiceAvailabilityResult
	13, // 60: moego.api.online_booking.v1.OBAvailabilitySettingService.GetGroomingServiceAvailability:output_type -> moego.api.online_booking.v1.GetGroomingServiceAvailabilityResult
	15, // 61: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateGroomingServiceAvailability:output_type -> moego.api.online_booking.v1.UpdateGroomingServiceAvailabilityResult
	18, // 62: moego.api.online_booking.v1.OBAvailabilitySettingService.ListArrivalPickUpTimeOverrides:output_type -> moego.api.online_booking.v1.ListArrivalPickUpTimeOverridesResult
	20, // 63: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchCreateArrivalPickUpTimeOverride:output_type -> moego.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResult
	22, // 64: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchDeleteArrivalPickUpTimeOverride:output_type -> moego.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResult
	24, // 65: moego.api.online_booking.v1.OBAvailabilitySettingService.BatchUpdateArrivalPickUpTimeOverride:output_type -> moego.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResult
	26, // 66: moego.api.online_booking.v1.OBAvailabilitySettingService.CreateCapacityOverride:output_type -> moego.api.online_booking.v1.CreateCapacityOverrideResult
	28, // 67: moego.api.online_booking.v1.OBAvailabilitySettingService.DeleteCapacityOverride:output_type -> moego.api.online_booking.v1.DeleteCapacityOverrideResult
	30, // 68: moego.api.online_booking.v1.OBAvailabilitySettingService.UpdateCapacityOverride:output_type -> moego.api.online_booking.v1.UpdateCapacityOverrideResult
	54, // [54:69] is the sub-list for method output_type
	39, // [39:54] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_availability_setting_api_proto_init() }
func file_moego_api_online_booking_v1_ob_availability_setting_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_availability_setting_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBoardingServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBoardingServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaycareServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaycareServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDaycareServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDaycareServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroomingServiceAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroomingServiceAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrivalPickUpTimeOverrideView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArrivalPickUpTimeOverridesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArrivalPickUpTimeOverridesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteArrivalPickUpTimeOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteArrivalPickUpTimeOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCapacityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCapacityOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCapacityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCapacityOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideParams_CreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideParams_UpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes[32].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_availability_setting_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_availability_setting_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_availability_setting_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_availability_setting_api_proto = out.File
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_availability_setting_api_proto_depIdxs = nil
}
