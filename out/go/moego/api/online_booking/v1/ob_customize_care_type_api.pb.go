// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_customize_care_type_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create booking care type params
type CreateBookingCareTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// booking care type def
	BookingCareType *v1.CreateBookingCareTypeDef `protobuf:"bytes,2,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *CreateBookingCareTypeParams) Reset() {
	*x = CreateBookingCareTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingCareTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingCareTypeParams) ProtoMessage() {}

func (x *CreateBookingCareTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingCareTypeParams.ProtoReflect.Descriptor instead.
func (*CreateBookingCareTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBookingCareTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBookingCareTypeParams) GetBookingCareType() *v1.CreateBookingCareTypeDef {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Create booking care type result
type CreateBookingCareTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created booking care type
	BookingCareType *v1.BookingCareTypeView `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *CreateBookingCareTypeResult) Reset() {
	*x = CreateBookingCareTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingCareTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingCareTypeResult) ProtoMessage() {}

func (x *CreateBookingCareTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingCareTypeResult.ProtoReflect.Descriptor instead.
func (*CreateBookingCareTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBookingCareTypeResult) GetBookingCareType() *v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Update booking care type params
type UpdateBookingCareTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type def
	BookingCareType *v1.UpdateBookingCareTypeDef `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *UpdateBookingCareTypeParams) Reset() {
	*x = UpdateBookingCareTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingCareTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingCareTypeParams) ProtoMessage() {}

func (x *UpdateBookingCareTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingCareTypeParams.ProtoReflect.Descriptor instead.
func (*UpdateBookingCareTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBookingCareTypeParams) GetBookingCareType() *v1.UpdateBookingCareTypeDef {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Update booking care type result
type UpdateBookingCareTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated booking care type
	BookingCareType *v1.BookingCareTypeView `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *UpdateBookingCareTypeResult) Reset() {
	*x = UpdateBookingCareTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingCareTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingCareTypeResult) ProtoMessage() {}

func (x *UpdateBookingCareTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingCareTypeResult.ProtoReflect.Descriptor instead.
func (*UpdateBookingCareTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBookingCareTypeResult) GetBookingCareType() *v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// List booking care types params
type ListBookingCareTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *ListBookingCareTypesParams) Reset() {
	*x = ListBookingCareTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingCareTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingCareTypesParams) ProtoMessage() {}

func (x *ListBookingCareTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingCareTypesParams.ProtoReflect.Descriptor instead.
func (*ListBookingCareTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListBookingCareTypesParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// List booking care types result
type ListBookingCareTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the booking care type list
	BookingCareTypes []*v1.BookingCareTypeView `protobuf:"bytes,1,rep,name=booking_care_types,json=bookingCareTypes,proto3" json:"booking_care_types,omitempty"`
}

func (x *ListBookingCareTypesResult) Reset() {
	*x = ListBookingCareTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingCareTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingCareTypesResult) ProtoMessage() {}

func (x *ListBookingCareTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingCareTypesResult.ProtoReflect.Descriptor instead.
func (*ListBookingCareTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListBookingCareTypesResult) GetBookingCareTypes() []*v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareTypes
	}
	return nil
}

// Get booking care type params
type GetBookingCareTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBookingCareTypeParams) Reset() {
	*x = GetBookingCareTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingCareTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingCareTypeParams) ProtoMessage() {}

func (x *GetBookingCareTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingCareTypeParams.ProtoReflect.Descriptor instead.
func (*GetBookingCareTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetBookingCareTypeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetBookingCareTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Get booking care type result
type GetBookingCareTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the booking care type
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *GetBookingCareTypeResult) Reset() {
	*x = GetBookingCareTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingCareTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingCareTypeResult) ProtoMessage() {}

func (x *GetBookingCareTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingCareTypeResult.ProtoReflect.Descriptor instead.
func (*GetBookingCareTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetBookingCareTypeResult) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Delete booking care type params
type DeleteBookingCareTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteBookingCareTypeParams) Reset() {
	*x = DeleteBookingCareTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBookingCareTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookingCareTypeParams) ProtoMessage() {}

func (x *DeleteBookingCareTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookingCareTypeParams.ProtoReflect.Descriptor instead.
func (*DeleteBookingCareTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteBookingCareTypeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Delete booking care type result
type DeleteBookingCareTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteBookingCareTypeResult) Reset() {
	*x = DeleteBookingCareTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBookingCareTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookingCareTypeResult) ProtoMessage() {}

func (x *DeleteBookingCareTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookingCareTypeResult.ProtoReflect.Descriptor instead.
func (*DeleteBookingCareTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{9}
}

// sort booking care type params
type SortBookingCareTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type ids
	BookingCareTypeIds []int64 `protobuf:"varint,1,rep,packed,name=booking_care_type_ids,json=bookingCareTypeIds,proto3" json:"booking_care_type_ids,omitempty"`
}

func (x *SortBookingCareTypeParams) Reset() {
	*x = SortBookingCareTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortBookingCareTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortBookingCareTypeParams) ProtoMessage() {}

func (x *SortBookingCareTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortBookingCareTypeParams.ProtoReflect.Descriptor instead.
func (*SortBookingCareTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{10}
}

func (x *SortBookingCareTypeParams) GetBookingCareTypeIds() []int64 {
	if x != nil {
		return x.BookingCareTypeIds
	}
	return nil
}

// sort booking care type result
type SortBookingCareTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the sorted booking care type
	BookingCareTypes []*v1.BookingCareTypeView `protobuf:"bytes,1,rep,name=booking_care_types,json=bookingCareTypes,proto3" json:"booking_care_types,omitempty"`
}

func (x *SortBookingCareTypeResult) Reset() {
	*x = SortBookingCareTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortBookingCareTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortBookingCareTypeResult) ProtoMessage() {}

func (x *SortBookingCareTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortBookingCareTypeResult.ProtoReflect.Descriptor instead.
func (*SortBookingCareTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP(), []int{11}
}

func (x *SortBookingCareTypeResult) GetBookingCareTypes() []*v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareTypes
	}
	return nil
}

var File_moego_api_online_booking_v1_ob_customize_care_type_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x40, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x01, 0x0a, 0x1b, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x64, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x7e, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x11, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x1b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x64, 0x0a, 0x11, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x7e, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x5f, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x46, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x5d, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x36, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4e, 0x0a, 0x19, 0x53, 0x6f, 0x72, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x19, 0x53, 0x6f, 0x72, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x32, 0xda, 0x06, 0x0a, 0x16, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x8b, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x88, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x8b, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01,
	0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8c, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_goTypes = []interface{}{
	(*CreateBookingCareTypeParams)(nil), // 0: moego.api.online_booking.v1.CreateBookingCareTypeParams
	(*CreateBookingCareTypeResult)(nil), // 1: moego.api.online_booking.v1.CreateBookingCareTypeResult
	(*UpdateBookingCareTypeParams)(nil), // 2: moego.api.online_booking.v1.UpdateBookingCareTypeParams
	(*UpdateBookingCareTypeResult)(nil), // 3: moego.api.online_booking.v1.UpdateBookingCareTypeResult
	(*ListBookingCareTypesParams)(nil),  // 4: moego.api.online_booking.v1.ListBookingCareTypesParams
	(*ListBookingCareTypesResult)(nil),  // 5: moego.api.online_booking.v1.ListBookingCareTypesResult
	(*GetBookingCareTypeParams)(nil),    // 6: moego.api.online_booking.v1.GetBookingCareTypeParams
	(*GetBookingCareTypeResult)(nil),    // 7: moego.api.online_booking.v1.GetBookingCareTypeResult
	(*DeleteBookingCareTypeParams)(nil), // 8: moego.api.online_booking.v1.DeleteBookingCareTypeParams
	(*DeleteBookingCareTypeResult)(nil), // 9: moego.api.online_booking.v1.DeleteBookingCareTypeResult
	(*SortBookingCareTypeParams)(nil),   // 10: moego.api.online_booking.v1.SortBookingCareTypeParams
	(*SortBookingCareTypeResult)(nil),   // 11: moego.api.online_booking.v1.SortBookingCareTypeResult
	(*v1.CreateBookingCareTypeDef)(nil), // 12: moego.models.online_booking.v1.CreateBookingCareTypeDef
	(*v1.BookingCareTypeView)(nil),      // 13: moego.models.online_booking.v1.BookingCareTypeView
	(*v1.UpdateBookingCareTypeDef)(nil), // 14: moego.models.online_booking.v1.UpdateBookingCareTypeDef
	(*v1.BookingCareType)(nil),          // 15: moego.models.online_booking.v1.BookingCareType
}
var file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_depIdxs = []int32{
	12, // 0: moego.api.online_booking.v1.CreateBookingCareTypeParams.booking_care_type:type_name -> moego.models.online_booking.v1.CreateBookingCareTypeDef
	13, // 1: moego.api.online_booking.v1.CreateBookingCareTypeResult.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	14, // 2: moego.api.online_booking.v1.UpdateBookingCareTypeParams.booking_care_type:type_name -> moego.models.online_booking.v1.UpdateBookingCareTypeDef
	13, // 3: moego.api.online_booking.v1.UpdateBookingCareTypeResult.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	13, // 4: moego.api.online_booking.v1.ListBookingCareTypesResult.booking_care_types:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	15, // 5: moego.api.online_booking.v1.GetBookingCareTypeResult.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	13, // 6: moego.api.online_booking.v1.SortBookingCareTypeResult.booking_care_types:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	0,  // 7: moego.api.online_booking.v1.BookingCareTypeService.CreateBookingCareType:input_type -> moego.api.online_booking.v1.CreateBookingCareTypeParams
	2,  // 8: moego.api.online_booking.v1.BookingCareTypeService.UpdateBookingCareType:input_type -> moego.api.online_booking.v1.UpdateBookingCareTypeParams
	4,  // 9: moego.api.online_booking.v1.BookingCareTypeService.ListBookingCareTypes:input_type -> moego.api.online_booking.v1.ListBookingCareTypesParams
	6,  // 10: moego.api.online_booking.v1.BookingCareTypeService.GetBookingCareType:input_type -> moego.api.online_booking.v1.GetBookingCareTypeParams
	8,  // 11: moego.api.online_booking.v1.BookingCareTypeService.DeleteBookingCareType:input_type -> moego.api.online_booking.v1.DeleteBookingCareTypeParams
	10, // 12: moego.api.online_booking.v1.BookingCareTypeService.SortBookingCareType:input_type -> moego.api.online_booking.v1.SortBookingCareTypeParams
	1,  // 13: moego.api.online_booking.v1.BookingCareTypeService.CreateBookingCareType:output_type -> moego.api.online_booking.v1.CreateBookingCareTypeResult
	3,  // 14: moego.api.online_booking.v1.BookingCareTypeService.UpdateBookingCareType:output_type -> moego.api.online_booking.v1.UpdateBookingCareTypeResult
	5,  // 15: moego.api.online_booking.v1.BookingCareTypeService.ListBookingCareTypes:output_type -> moego.api.online_booking.v1.ListBookingCareTypesResult
	7,  // 16: moego.api.online_booking.v1.BookingCareTypeService.GetBookingCareType:output_type -> moego.api.online_booking.v1.GetBookingCareTypeResult
	9,  // 17: moego.api.online_booking.v1.BookingCareTypeService.DeleteBookingCareType:output_type -> moego.api.online_booking.v1.DeleteBookingCareTypeResult
	11, // 18: moego.api.online_booking.v1.BookingCareTypeService.SortBookingCareType:output_type -> moego.api.online_booking.v1.SortBookingCareTypeResult
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_init() }
func file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_customize_care_type_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingCareTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingCareTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingCareTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingCareTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingCareTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingCareTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingCareTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingCareTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBookingCareTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBookingCareTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortBookingCareTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortBookingCareTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_customize_care_type_api_proto = out.File
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_customize_care_type_api_proto_depIdxs = nil
}
