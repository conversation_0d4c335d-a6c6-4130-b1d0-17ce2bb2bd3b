// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v2/order_api.proto

package orderapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v2"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v22 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list orders for combined payment params
type ListOrdersForCombinedPaymentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *ListOrdersForCombinedPaymentParams) Reset() {
	*x = ListOrdersForCombinedPaymentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersForCombinedPaymentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCombinedPaymentParams) ProtoMessage() {}

func (x *ListOrdersForCombinedPaymentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCombinedPaymentParams.ProtoReflect.Descriptor instead.
func (*ListOrdersForCombinedPaymentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListOrdersForCombinedPaymentParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// list orders for combined payment result
type ListOrdersForCombinedPaymentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer
	Customer *ListOrdersForCombinedPaymentResult_CustomerBrief `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// orders
	Orders []*v1.OrderDetailView `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
	// appointments
	Appointments []*ListOrdersForCombinedPaymentResult_AppointmentBrief `protobuf:"bytes,3,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// init cvf info
	ConvenienceFees []*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief `protobuf:"bytes,4,rep,name=convenience_fees,json=convenienceFees,proto3" json:"convenience_fees,omitempty"`
}

func (x *ListOrdersForCombinedPaymentResult) Reset() {
	*x = ListOrdersForCombinedPaymentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersForCombinedPaymentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCombinedPaymentResult) ProtoMessage() {}

func (x *ListOrdersForCombinedPaymentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCombinedPaymentResult.ProtoReflect.Descriptor instead.
func (*ListOrdersForCombinedPaymentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListOrdersForCombinedPaymentResult) GetCustomer() *ListOrdersForCombinedPaymentResult_CustomerBrief {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *ListOrdersForCombinedPaymentResult) GetOrders() []*v1.OrderDetailView {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForCombinedPaymentResult) GetAppointments() []*ListOrdersForCombinedPaymentResult_AppointmentBrief {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListOrdersForCombinedPaymentResult) GetConvenienceFees() []*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief {
	if x != nil {
		return x.ConvenienceFees
	}
	return nil
}

// pay order params
type PayOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订单层所需要的参数
	// 支付的订单
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// total amount：  total_amount = amount + payment_tips
	// 支付金额 = 填写的金额
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// payment tips before create = 支付前选择的tips
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: 想不出更好的命名。 --)
	PaymentTipsBeforeCreate *money.Money `protobuf:"bytes,3,opt,name=payment_tips_before_create,json=paymentTipsBeforeCreate,proto3,oneof" json:"payment_tips_before_create,omitempty"`
	// 支付层所需要的参数
	// 支付类型
	PaymentType v2.PaymentModel_PaymentType `protobuf:"varint,11,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
	// 支付方式
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,12,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	PaymentMethodDetail *v2.PaymentMethod_Detail `protobuf:"bytes,13,opt,name=payment_method_detail,json=paymentMethodDetail,proto3" json:"payment_method_detail,omitempty"`
	// 是否添加cv fee,不传的时候后端判断
	AddConvenienceFee *bool `protobuf:"varint,14,opt,name=add_convenience_fee,json=addConvenienceFee,proto3,oneof" json:"add_convenience_fee,omitempty"`
	// paid by, 一般是 customer name
	Payer string `protobuf:"bytes,15,opt,name=payer,proto3" json:"payer,omitempty"`
	// payment description
	PaymentDescription string `protobuf:"bytes,16,opt,name=payment_description,json=paymentDescription,proto3" json:"payment_description,omitempty"`
}

func (x *PayOrderParams) Reset() {
	*x = PayOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderParams) ProtoMessage() {}

func (x *PayOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderParams.ProtoReflect.Descriptor instead.
func (*PayOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{2}
}

func (x *PayOrderParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PayOrderParams) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PayOrderParams) GetPaymentTipsBeforeCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsBeforeCreate
	}
	return nil
}

func (x *PayOrderParams) GetPaymentType() v2.PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v2.PaymentModel_PaymentType(0)
}

func (x *PayOrderParams) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *PayOrderParams) GetPaymentMethodDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.PaymentMethodDetail
	}
	return nil
}

func (x *PayOrderParams) GetAddConvenienceFee() bool {
	if x != nil && x.AddConvenienceFee != nil {
		return *x.AddConvenienceFee
	}
	return false
}

func (x *PayOrderParams) GetPayer() string {
	if x != nil {
		return x.Payer
	}
	return ""
}

func (x *PayOrderParams) GetPaymentDescription() string {
	if x != nil {
		return x.PaymentDescription
	}
	return ""
}

// pay order result
type PayOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment.
	OrderPayment *v1.OrderPaymentModel `protobuf:"bytes,1,opt,name=order_payment,json=orderPayment,proto3" json:"order_payment,omitempty"`
	// raw payment result，渠道返回的原始数据，用于前端加载第三方支付组件
	// e.g. adyen 3ds2:
	//
	//	`{
	//	  "resultCode": "IdentifyShopper",
	//	  "action": {
	//	    "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
	//	    "paymentMethodType": "scheme",
	//	    "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
	//	    "subtype": "fingerprint",
	//	    "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
	//	    "type": "threeDS2"
	//	  }
	//	}`
	RawPaymentResult string `protobuf:"bytes,2,opt,name=raw_payment_result,json=rawPaymentResult,proto3" json:"raw_payment_result,omitempty"`
}

func (x *PayOrderResult) Reset() {
	*x = PayOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderResult) ProtoMessage() {}

func (x *PayOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderResult.ProtoReflect.Descriptor instead.
func (*PayOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{3}
}

func (x *PayOrderResult) GetOrderPayment() *v1.OrderPaymentModel {
	if x != nil {
		return x.OrderPayment
	}
	return nil
}

func (x *PayOrderResult) GetRawPaymentResult() string {
	if x != nil {
		return x.RawPaymentResult
	}
	return ""
}

// combined pay order params
type CombinedPayOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 合单请求列表
	CombinedItems []*v21.CombinedItem `protobuf:"bytes,1,rep,name=combined_items,json=combinedItems,proto3" json:"combined_items,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 支付层所需要的参数
	PayDef *v21.PayDef `protobuf:"bytes,4,opt,name=pay_def,json=payDef,proto3" json:"pay_def,omitempty"`
}

func (x *CombinedPayOrderParams) Reset() {
	*x = CombinedPayOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombinedPayOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombinedPayOrderParams) ProtoMessage() {}

func (x *CombinedPayOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombinedPayOrderParams.ProtoReflect.Descriptor instead.
func (*CombinedPayOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{4}
}

func (x *CombinedPayOrderParams) GetCombinedItems() []*v21.CombinedItem {
	if x != nil {
		return x.CombinedItems
	}
	return nil
}

func (x *CombinedPayOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CombinedPayOrderParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CombinedPayOrderParams) GetPayDef() *v21.PayDef {
	if x != nil {
		return x.PayDef
	}
	return nil
}

// combined pay order result
type CombinedPayOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment 列表
	OrderPayments []*v1.OrderPaymentModel `protobuf:"bytes,1,rep,name=order_payments,json=orderPayments,proto3" json:"order_payments,omitempty"`
	// 合单 transaction id
	TransactionId int64 `protobuf:"varint,2,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// raw payment result，渠道返回的原始数据，用于前端加载第三方支付组件
	// e.g. adyen 3ds2:
	//
	//	`{
	//	  "resultCode": "IdentifyShopper",
	//	  "action": {
	//	    "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
	//	    "paymentMethodType": "scheme",
	//	    "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
	//	    "subtype": "fingerprint",
	//	    "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
	//	    "type": "threeDS2"
	//	  }
	//	}`
	//
	// e.g. stripe 3ds2:
	// require_actions
	RawPaymentResult string `protobuf:"bytes,3,opt,name=raw_payment_result,json=rawPaymentResult,proto3" json:"raw_payment_result,omitempty"`
	// channel payment
	ChannelPayment *v2.ChannelPayment `protobuf:"bytes,4,opt,name=channel_payment,json=channelPayment,proto3" json:"channel_payment,omitempty"`
}

func (x *CombinedPayOrderResult) Reset() {
	*x = CombinedPayOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombinedPayOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombinedPayOrderResult) ProtoMessage() {}

func (x *CombinedPayOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombinedPayOrderResult.ProtoReflect.Descriptor instead.
func (*CombinedPayOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{5}
}

func (x *CombinedPayOrderResult) GetOrderPayments() []*v1.OrderPaymentModel {
	if x != nil {
		return x.OrderPayments
	}
	return nil
}

func (x *CombinedPayOrderResult) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *CombinedPayOrderResult) GetRawPaymentResult() string {
	if x != nil {
		return x.RawPaymentResult
	}
	return ""
}

func (x *CombinedPayOrderResult) GetChannelPayment() *v2.ChannelPayment {
	if x != nil {
		return x.ChannelPayment
	}
	return nil
}

// get order guid params
type GetOrderGuidParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 合单的 order id 列表
	OrderIds []int64 `protobuf:"varint,1,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// 是否需要 cvf
	RequiredCvf bool `protobuf:"varint,2,opt,name=required_cvf,json=requiredCvf,proto3" json:"required_cvf,omitempty"`
}

func (x *GetOrderGuidParams) Reset() {
	*x = GetOrderGuidParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderGuidParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderGuidParams) ProtoMessage() {}

func (x *GetOrderGuidParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderGuidParams.ProtoReflect.Descriptor instead.
func (*GetOrderGuidParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetOrderGuidParams) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *GetOrderGuidParams) GetRequiredCvf() bool {
	if x != nil {
		return x.RequiredCvf
	}
	return false
}

// get order guid result
type GetOrderGuidResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// guid
	Guid string `protobuf:"bytes,1,opt,name=guid,proto3" json:"guid,omitempty"`
}

func (x *GetOrderGuidResult) Reset() {
	*x = GetOrderGuidResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderGuidResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderGuidResult) ProtoMessage() {}

func (x *GetOrderGuidResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderGuidResult.ProtoReflect.Descriptor instead.
func (*GetOrderGuidResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetOrderGuidResult) GetGuid() string {
	if x != nil {
		return x.Guid
	}
	return ""
}

// Preview create order request.
type PreviewCreateOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SourceType
	SourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// SourceID
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Order items.
	Items []*v22.PreviewCreateOrderRequest_CartItem `protobuf:"bytes,11,rep,name=items,proto3" json:"items,omitempty"`
	// tips amount.
	TipsAmount *money.Money `protobuf:"bytes,12,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// Promotions.
	//
	// Types that are assignable to Promotions:
	//
	//	*PreviewCreateOrderParams_AutoApplyPromotions
	//	*PreviewCreateOrderParams_AppliedPromotions
	Promotions isPreviewCreateOrderParams_Promotions `protobuf_oneof:"promotions"`
}

func (x *PreviewCreateOrderParams) Reset() {
	*x = PreviewCreateOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCreateOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCreateOrderParams) ProtoMessage() {}

func (x *PreviewCreateOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCreateOrderParams.ProtoReflect.Descriptor instead.
func (*PreviewCreateOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewCreateOrderParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *PreviewCreateOrderParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *PreviewCreateOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewCreateOrderParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PreviewCreateOrderParams) GetItems() []*v22.PreviewCreateOrderRequest_CartItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *PreviewCreateOrderParams) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (m *PreviewCreateOrderParams) GetPromotions() isPreviewCreateOrderParams_Promotions {
	if m != nil {
		return m.Promotions
	}
	return nil
}

func (x *PreviewCreateOrderParams) GetAutoApplyPromotions() bool {
	if x, ok := x.GetPromotions().(*PreviewCreateOrderParams_AutoApplyPromotions); ok {
		return x.AutoApplyPromotions
	}
	return false
}

func (x *PreviewCreateOrderParams) GetAppliedPromotions() *v22.PreviewCreateOrderRequest_AppliedPromotions {
	if x, ok := x.GetPromotions().(*PreviewCreateOrderParams_AppliedPromotions); ok {
		return x.AppliedPromotions
	}
	return nil
}

type isPreviewCreateOrderParams_Promotions interface {
	isPreviewCreateOrderParams_Promotions()
}

type PreviewCreateOrderParams_AutoApplyPromotions struct {
	// 自动 apply 可用的优惠. 不含 store credit.
	AutoApplyPromotions bool `protobuf:"varint,13,opt,name=auto_apply_promotions,json=autoApplyPromotions,proto3,oneof"`
}

type PreviewCreateOrderParams_AppliedPromotions struct {
	// 手动指定的优惠.
	AppliedPromotions *v22.PreviewCreateOrderRequest_AppliedPromotions `protobuf:"bytes,14,opt,name=applied_promotions,json=appliedPromotions,proto3,oneof"`
}

func (*PreviewCreateOrderParams_AutoApplyPromotions) isPreviewCreateOrderParams_Promotions() {}

func (*PreviewCreateOrderParams_AppliedPromotions) isPreviewCreateOrderParams_Promotions() {}

// Preview create order result.
type PreviewCreateOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// applied promotions list.
	AppliedPromotions *v22.PreviewCreateOrderRequest_AppliedPromotions `protobuf:"bytes,2,opt,name=applied_promotions,json=appliedPromotions,proto3" json:"applied_promotions,omitempty"`
}

func (x *PreviewCreateOrderResult) Reset() {
	*x = PreviewCreateOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCreateOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCreateOrderResult) ProtoMessage() {}

func (x *PreviewCreateOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCreateOrderResult.ProtoReflect.Descriptor instead.
func (*PreviewCreateOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{9}
}

func (x *PreviewCreateOrderResult) GetOrder() *v1.OrderDetailView {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *PreviewCreateOrderResult) GetAppliedPromotions() *v22.PreviewCreateOrderRequest_AppliedPromotions {
	if x != nil {
		return x.AppliedPromotions
	}
	return nil
}

// Create order params.
type CreateOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pre-defined source type.
	SourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID. 结合 source type 表示不同的 ID.
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID. 0 for pay online
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// order title
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// Order items.
	Items []*v22.PreviewCreateOrderRequest_CartItem `protobuf:"bytes,11,rep,name=items,proto3" json:"items,omitempty"`
	// Tips amount.
	TipsAmount *money.Money `protobuf:"bytes,12,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// Applied promotions.
	AppliedPromotions *v22.PreviewCreateOrderRequest_AppliedPromotions `protobuf:"bytes,13,opt,name=applied_promotions,json=appliedPromotions,proto3" json:"applied_promotions,omitempty"`
}

func (x *CreateOrderParams) Reset() {
	*x = CreateOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderParams) ProtoMessage() {}

func (x *CreateOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderParams.ProtoReflect.Descriptor instead.
func (*CreateOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{10}
}

func (x *CreateOrderParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateOrderParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateOrderParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateOrderParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateOrderParams) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateOrderParams) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateOrderParams) GetItems() []*v22.PreviewCreateOrderRequest_CartItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *CreateOrderParams) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *CreateOrderParams) GetAppliedPromotions() *v22.PreviewCreateOrderRequest_AppliedPromotions {
	if x != nil {
		return x.AppliedPromotions
	}
	return nil
}

// Create order result.
type CreateOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateOrderResult) Reset() {
	*x = CreateOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResult) ProtoMessage() {}

func (x *CreateOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResult.ProtoReflect.Descriptor instead.
func (*CreateOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{11}
}

func (x *CreateOrderResult) GetOrder() *v1.OrderDetailView {
	if x != nil {
		return x.Order
	}
	return nil
}

// 关联的 appointment
type ListOrdersForCombinedPaymentResult_AppointmentBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment/grooming id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment date
	AppointmentDate string `protobuf:"bytes,2,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time
	AppointmentStartTime int32 `protobuf:"varint,3,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end date
	AppointmentEndDate string `protobuf:"bytes,4,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// appointment end time
	AppointmentEndTime int32 `protobuf:"varint,5,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// appointment check in time
	CheckInTime int64 `protobuf:"varint,6,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// appointment check out time
	CheckOutTime int64 `protobuf:"varint,7,opt,name=check_out_time,json=checkOutTime,proto3" json:"check_out_time,omitempty"`
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) Reset() {
	*x = ListOrdersForCombinedPaymentResult_AppointmentBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCombinedPaymentResult_AppointmentBrief) ProtoMessage() {}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCombinedPaymentResult_AppointmentBrief.ProtoReflect.Descriptor instead.
func (*ListOrdersForCombinedPaymentResult_AppointmentBrief) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetCheckInTime() int64 {
	if x != nil {
		return x.CheckInTime
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_AppointmentBrief) GetCheckOutTime() int64 {
	if x != nil {
		return x.CheckOutTime
	}
	return 0
}

// 关联的 customer 信息
type ListOrdersForCombinedPaymentResult_CustomerBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// customer first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// customer last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) Reset() {
	*x = ListOrdersForCombinedPaymentResult_CustomerBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCombinedPaymentResult_CustomerBrief) ProtoMessage() {}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCombinedPaymentResult_CustomerBrief.ProtoReflect.Descriptor instead.
func (*ListOrdersForCombinedPaymentResult_CustomerBrief) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListOrdersForCombinedPaymentResult_CustomerBrief) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// Convenience fee
type ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 是否添加 cv fee
	AddCvf bool `protobuf:"varint,2,opt,name=add_cvf,json=addCvf,proto3" json:"add_cvf,omitempty"`
	// convenience fee
	Fee *money.Money `protobuf:"bytes,3,opt,name=fee,proto3" json:"fee,omitempty"`
}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) Reset() {
	*x = ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v2_order_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) ProtoMessage() {}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v2_order_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief.ProtoReflect.Descriptor instead.
func (*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v2_order_api_proto_rawDescGZIP(), []int{1, 2}
}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) GetAddCvf() bool {
	if x != nil {
		return x.AddCvf
	}
	return false
}

func (x *ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief) GetFee() *money.Money {
	if x != nil {
		return x.Fee
	}
	return nil
}

var File_moego_api_order_v2_order_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v2_order_api_proto_rawDesc = []byte{
	0x0a, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x22, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xca, 0x07, 0x0a, 0x22, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x60, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x6b, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x79, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x65, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x73, 0x1a, 0xb1, 0x02, 0x0a, 0x10,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69,
	0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a,
	0x71, 0x0a, 0x0d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x1a, 0x73, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x5f,
	0x63, 0x76, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x64, 0x64, 0x43, 0x76,
	0x66, 0x12, 0x24, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x03, 0x66, 0x65, 0x65, 0x22, 0x8f, 0x05, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x34,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x1a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x17,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x61, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x11, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65,
	0x72, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x0e, 0x50, 0x61,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x0d,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x61, 0x77, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x72, 0x61, 0x77, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64,
	0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x56,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x1e, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x44,
	0x65, 0x66, 0x52, 0x06, 0x70, 0x61, 0x79, 0x44, 0x65, 0x66, 0x22, 0x90, 0x02, 0x0a, 0x16, 0x43,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x61, 0x77, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x61, 0x77, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x0f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x68, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x01,
	0x10, 0x1e, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x63, 0x76, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x43, 0x76, 0x66, 0x22, 0x28, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x75, 0x69,
	0x64, 0x22, 0xb2, 0x04, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x51,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61,
	0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x15,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x61,
	0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x74, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xcc, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x72, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc3, 0x04, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x69, 0x70,
	0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x72, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65,
	0x64, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x51, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x32, 0x8e,
	0x05, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x70, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x5b, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52,
	0x0a, 0x08, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x10, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64,
	0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5e,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e,
	0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e,
	0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42,
	0x72, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a,
	0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_order_v2_order_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v2_order_api_proto_rawDescData = file_moego_api_order_v2_order_api_proto_rawDesc
)

func file_moego_api_order_v2_order_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v2_order_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v2_order_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v2_order_api_proto_rawDescData)
	})
	return file_moego_api_order_v2_order_api_proto_rawDescData
}

var file_moego_api_order_v2_order_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_api_order_v2_order_api_proto_goTypes = []interface{}{
	(*ListOrdersForCombinedPaymentParams)(nil),                         // 0: moego.api.order.v2.ListOrdersForCombinedPaymentParams
	(*ListOrdersForCombinedPaymentResult)(nil),                         // 1: moego.api.order.v2.ListOrdersForCombinedPaymentResult
	(*PayOrderParams)(nil),                                             // 2: moego.api.order.v2.PayOrderParams
	(*PayOrderResult)(nil),                                             // 3: moego.api.order.v2.PayOrderResult
	(*CombinedPayOrderParams)(nil),                                     // 4: moego.api.order.v2.CombinedPayOrderParams
	(*CombinedPayOrderResult)(nil),                                     // 5: moego.api.order.v2.CombinedPayOrderResult
	(*GetOrderGuidParams)(nil),                                         // 6: moego.api.order.v2.GetOrderGuidParams
	(*GetOrderGuidResult)(nil),                                         // 7: moego.api.order.v2.GetOrderGuidResult
	(*PreviewCreateOrderParams)(nil),                                   // 8: moego.api.order.v2.PreviewCreateOrderParams
	(*PreviewCreateOrderResult)(nil),                                   // 9: moego.api.order.v2.PreviewCreateOrderResult
	(*CreateOrderParams)(nil),                                          // 10: moego.api.order.v2.CreateOrderParams
	(*CreateOrderResult)(nil),                                          // 11: moego.api.order.v2.CreateOrderResult
	(*ListOrdersForCombinedPaymentResult_AppointmentBrief)(nil),        // 12: moego.api.order.v2.ListOrdersForCombinedPaymentResult.AppointmentBrief
	(*ListOrdersForCombinedPaymentResult_CustomerBrief)(nil),           // 13: moego.api.order.v2.ListOrdersForCombinedPaymentResult.CustomerBrief
	(*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief)(nil), // 14: moego.api.order.v2.ListOrdersForCombinedPaymentResult.InitConvenienceFeeBrief
	(*v1.OrderDetailView)(nil),                                         // 15: moego.models.order.v1.OrderDetailView
	(*money.Money)(nil),                                                // 16: google.type.Money
	(v2.PaymentModel_PaymentType)(0),                                   // 17: moego.models.payment.v2.PaymentModel.PaymentType
	(v2.PaymentMethod_MethodType)(0),                                   // 18: moego.models.payment.v2.PaymentMethod.MethodType
	(*v2.PaymentMethod_Detail)(nil),                                    // 19: moego.models.payment.v2.PaymentMethod.Detail
	(*v1.OrderPaymentModel)(nil),                                       // 20: moego.models.order.v1.OrderPaymentModel
	(*v21.CombinedItem)(nil),                                           // 21: moego.models.order.v2.CombinedItem
	(*v21.PayDef)(nil),                                                 // 22: moego.models.order.v2.PayDef
	(*v2.ChannelPayment)(nil),                                          // 23: moego.models.payment.v2.ChannelPayment
	(v1.OrderSourceType)(0),                                            // 24: moego.models.order.v1.OrderSourceType
	(*v22.PreviewCreateOrderRequest_CartItem)(nil),                     // 25: moego.service.order.v2.PreviewCreateOrderRequest.CartItem
	(*v22.PreviewCreateOrderRequest_AppliedPromotions)(nil),            // 26: moego.service.order.v2.PreviewCreateOrderRequest.AppliedPromotions
}
var file_moego_api_order_v2_order_api_proto_depIdxs = []int32{
	13, // 0: moego.api.order.v2.ListOrdersForCombinedPaymentResult.customer:type_name -> moego.api.order.v2.ListOrdersForCombinedPaymentResult.CustomerBrief
	15, // 1: moego.api.order.v2.ListOrdersForCombinedPaymentResult.orders:type_name -> moego.models.order.v1.OrderDetailView
	12, // 2: moego.api.order.v2.ListOrdersForCombinedPaymentResult.appointments:type_name -> moego.api.order.v2.ListOrdersForCombinedPaymentResult.AppointmentBrief
	14, // 3: moego.api.order.v2.ListOrdersForCombinedPaymentResult.convenience_fees:type_name -> moego.api.order.v2.ListOrdersForCombinedPaymentResult.InitConvenienceFeeBrief
	16, // 4: moego.api.order.v2.PayOrderParams.amount:type_name -> google.type.Money
	16, // 5: moego.api.order.v2.PayOrderParams.payment_tips_before_create:type_name -> google.type.Money
	17, // 6: moego.api.order.v2.PayOrderParams.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	18, // 7: moego.api.order.v2.PayOrderParams.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	19, // 8: moego.api.order.v2.PayOrderParams.payment_method_detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	20, // 9: moego.api.order.v2.PayOrderResult.order_payment:type_name -> moego.models.order.v1.OrderPaymentModel
	21, // 10: moego.api.order.v2.CombinedPayOrderParams.combined_items:type_name -> moego.models.order.v2.CombinedItem
	22, // 11: moego.api.order.v2.CombinedPayOrderParams.pay_def:type_name -> moego.models.order.v2.PayDef
	20, // 12: moego.api.order.v2.CombinedPayOrderResult.order_payments:type_name -> moego.models.order.v1.OrderPaymentModel
	23, // 13: moego.api.order.v2.CombinedPayOrderResult.channel_payment:type_name -> moego.models.payment.v2.ChannelPayment
	24, // 14: moego.api.order.v2.PreviewCreateOrderParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	25, // 15: moego.api.order.v2.PreviewCreateOrderParams.items:type_name -> moego.service.order.v2.PreviewCreateOrderRequest.CartItem
	16, // 16: moego.api.order.v2.PreviewCreateOrderParams.tips_amount:type_name -> google.type.Money
	26, // 17: moego.api.order.v2.PreviewCreateOrderParams.applied_promotions:type_name -> moego.service.order.v2.PreviewCreateOrderRequest.AppliedPromotions
	15, // 18: moego.api.order.v2.PreviewCreateOrderResult.order:type_name -> moego.models.order.v1.OrderDetailView
	26, // 19: moego.api.order.v2.PreviewCreateOrderResult.applied_promotions:type_name -> moego.service.order.v2.PreviewCreateOrderRequest.AppliedPromotions
	24, // 20: moego.api.order.v2.CreateOrderParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	25, // 21: moego.api.order.v2.CreateOrderParams.items:type_name -> moego.service.order.v2.PreviewCreateOrderRequest.CartItem
	16, // 22: moego.api.order.v2.CreateOrderParams.tips_amount:type_name -> google.type.Money
	26, // 23: moego.api.order.v2.CreateOrderParams.applied_promotions:type_name -> moego.service.order.v2.PreviewCreateOrderRequest.AppliedPromotions
	15, // 24: moego.api.order.v2.CreateOrderResult.order:type_name -> moego.models.order.v1.OrderDetailView
	16, // 25: moego.api.order.v2.ListOrdersForCombinedPaymentResult.InitConvenienceFeeBrief.fee:type_name -> google.type.Money
	8,  // 26: moego.api.order.v2.OrderService.PreviewCreateOrder:input_type -> moego.api.order.v2.PreviewCreateOrderParams
	10, // 27: moego.api.order.v2.OrderService.CreateOrder:input_type -> moego.api.order.v2.CreateOrderParams
	2,  // 28: moego.api.order.v2.OrderService.PayOrder:input_type -> moego.api.order.v2.PayOrderParams
	4,  // 29: moego.api.order.v2.OrderService.CombinedPayOrder:input_type -> moego.api.order.v2.CombinedPayOrderParams
	6,  // 30: moego.api.order.v2.OrderService.GetOrderGuid:input_type -> moego.api.order.v2.GetOrderGuidParams
	0,  // 31: moego.api.order.v2.OrderService.ListOrdersForCombinedPayment:input_type -> moego.api.order.v2.ListOrdersForCombinedPaymentParams
	9,  // 32: moego.api.order.v2.OrderService.PreviewCreateOrder:output_type -> moego.api.order.v2.PreviewCreateOrderResult
	11, // 33: moego.api.order.v2.OrderService.CreateOrder:output_type -> moego.api.order.v2.CreateOrderResult
	3,  // 34: moego.api.order.v2.OrderService.PayOrder:output_type -> moego.api.order.v2.PayOrderResult
	5,  // 35: moego.api.order.v2.OrderService.CombinedPayOrder:output_type -> moego.api.order.v2.CombinedPayOrderResult
	7,  // 36: moego.api.order.v2.OrderService.GetOrderGuid:output_type -> moego.api.order.v2.GetOrderGuidResult
	1,  // 37: moego.api.order.v2.OrderService.ListOrdersForCombinedPayment:output_type -> moego.api.order.v2.ListOrdersForCombinedPaymentResult
	32, // [32:38] is the sub-list for method output_type
	26, // [26:32] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_moego_api_order_v2_order_api_proto_init() }
func file_moego_api_order_v2_order_api_proto_init() {
	if File_moego_api_order_v2_order_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v2_order_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersForCombinedPaymentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersForCombinedPaymentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombinedPayOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombinedPayOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderGuidParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderGuidResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCreateOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCreateOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersForCombinedPaymentResult_AppointmentBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersForCombinedPaymentResult_CustomerBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v2_order_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersForCombinedPaymentResult_InitConvenienceFeeBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_order_v2_order_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_order_v2_order_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_order_v2_order_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*PreviewCreateOrderParams_AutoApplyPromotions)(nil),
		(*PreviewCreateOrderParams_AppliedPromotions)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v2_order_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v2_order_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v2_order_api_proto_depIdxs,
		MessageInfos:      file_moego_api_order_v2_order_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v2_order_api_proto = out.File
	file_moego_api_order_v2_order_api_proto_rawDesc = nil
	file_moego_api_order_v2_order_api_proto_goTypes = nil
	file_moego_api_order_v2_order_api_proto_depIdxs = nil
}
