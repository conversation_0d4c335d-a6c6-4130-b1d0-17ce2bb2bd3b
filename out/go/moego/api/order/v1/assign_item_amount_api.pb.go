// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v1/assign_item_amount_api.proto

package orderapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Assign item paid amount request
type AssignItemPaidAmountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// List of items to assign paid amount
	Items []*v1.ItemPaidAmountAssignment `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	// business_id of the order
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *AssignItemPaidAmountParams) Reset() {
	*x = AssignItemPaidAmountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignItemPaidAmountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignItemPaidAmountParams) ProtoMessage() {}

func (x *AssignItemPaidAmountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignItemPaidAmountParams.ProtoReflect.Descriptor instead.
func (*AssignItemPaidAmountParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_assign_item_amount_api_proto_rawDescGZIP(), []int{0}
}

func (x *AssignItemPaidAmountParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *AssignItemPaidAmountParams) GetItems() []*v1.ItemPaidAmountAssignment {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *AssignItemPaidAmountParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Assign item paid amount response
type AssignItemPaidAmountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the operation was successful
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *AssignItemPaidAmountResult) Reset() {
	*x = AssignItemPaidAmountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignItemPaidAmountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignItemPaidAmountResult) ProtoMessage() {}

func (x *AssignItemPaidAmountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignItemPaidAmountResult.ProtoReflect.Descriptor instead.
func (*AssignItemPaidAmountResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_assign_item_amount_api_proto_rawDescGZIP(), []int{1}
}

func (x *AssignItemPaidAmountResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Get assigned item paid amount request
type GetAssignedItemPaidAmountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// business_id of the order
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAssignedItemPaidAmountParams) Reset() {
	*x = GetAssignedItemPaidAmountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedItemPaidAmountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedItemPaidAmountParams) ProtoMessage() {}

func (x *GetAssignedItemPaidAmountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedItemPaidAmountParams.ProtoReflect.Descriptor instead.
func (*GetAssignedItemPaidAmountParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_assign_item_amount_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetAssignedItemPaidAmountParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetAssignedItemPaidAmountParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Get assigned item paid amount response
type GetAssignedItemPaidAmountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// List of item payment assignments
	Items []*v1.ItemPaidAmountAssignment `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetAssignedItemPaidAmountResult) Reset() {
	*x = GetAssignedItemPaidAmountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedItemPaidAmountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedItemPaidAmountResult) ProtoMessage() {}

func (x *GetAssignedItemPaidAmountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedItemPaidAmountResult.ProtoReflect.Descriptor instead.
func (*GetAssignedItemPaidAmountResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_assign_item_amount_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAssignedItemPaidAmountResult) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetAssignedItemPaidAmountResult) GetItems() []*v1.ItemPaidAmountAssignment {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_moego_api_order_v1_assign_item_amount_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v1_assign_item_amount_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbd,
	0x01, 0x0a, 0x1a, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x51, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x14, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x36,
	0x0a, 0x1a, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x6f, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x32, 0x9c, 0x02, 0x0a, 0x1a, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x69, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x14, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x72, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_order_v1_assign_item_amount_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v1_assign_item_amount_api_proto_rawDescData = file_moego_api_order_v1_assign_item_amount_api_proto_rawDesc
)

func file_moego_api_order_v1_assign_item_amount_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v1_assign_item_amount_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v1_assign_item_amount_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v1_assign_item_amount_api_proto_rawDescData)
	})
	return file_moego_api_order_v1_assign_item_amount_api_proto_rawDescData
}

var file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_order_v1_assign_item_amount_api_proto_goTypes = []interface{}{
	(*AssignItemPaidAmountParams)(nil),      // 0: moego.api.order.v1.AssignItemPaidAmountParams
	(*AssignItemPaidAmountResult)(nil),      // 1: moego.api.order.v1.AssignItemPaidAmountResult
	(*GetAssignedItemPaidAmountParams)(nil), // 2: moego.api.order.v1.GetAssignedItemPaidAmountParams
	(*GetAssignedItemPaidAmountResult)(nil), // 3: moego.api.order.v1.GetAssignedItemPaidAmountResult
	(*v1.ItemPaidAmountAssignment)(nil),     // 4: moego.models.order.v1.ItemPaidAmountAssignment
}
var file_moego_api_order_v1_assign_item_amount_api_proto_depIdxs = []int32{
	4, // 0: moego.api.order.v1.AssignItemPaidAmountParams.items:type_name -> moego.models.order.v1.ItemPaidAmountAssignment
	4, // 1: moego.api.order.v1.GetAssignedItemPaidAmountResult.items:type_name -> moego.models.order.v1.ItemPaidAmountAssignment
	0, // 2: moego.api.order.v1.AssignItemAmountApiService.AssignItemPaidAmount:input_type -> moego.api.order.v1.AssignItemPaidAmountParams
	2, // 3: moego.api.order.v1.AssignItemAmountApiService.GetAssignedItemPaidAmount:input_type -> moego.api.order.v1.GetAssignedItemPaidAmountParams
	1, // 4: moego.api.order.v1.AssignItemAmountApiService.AssignItemPaidAmount:output_type -> moego.api.order.v1.AssignItemPaidAmountResult
	3, // 5: moego.api.order.v1.AssignItemAmountApiService.GetAssignedItemPaidAmount:output_type -> moego.api.order.v1.GetAssignedItemPaidAmountResult
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_api_order_v1_assign_item_amount_api_proto_init() }
func file_moego_api_order_v1_assign_item_amount_api_proto_init() {
	if File_moego_api_order_v1_assign_item_amount_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignItemPaidAmountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignItemPaidAmountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedItemPaidAmountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedItemPaidAmountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v1_assign_item_amount_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v1_assign_item_amount_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v1_assign_item_amount_api_proto_depIdxs,
		MessageInfos:      file_moego_api_order_v1_assign_item_amount_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v1_assign_item_amount_api_proto = out.File
	file_moego_api_order_v1_assign_item_amount_api_proto_rawDesc = nil
	file_moego_api_order_v1_assign_item_amount_api_proto_goTypes = nil
	file_moego_api_order_v1_assign_item_amount_api_proto_depIdxs = nil
}
