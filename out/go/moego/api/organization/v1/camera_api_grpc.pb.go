// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/organization/v1/camera_api.proto

package organizationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CameraServiceClient is the client API for CameraService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CameraServiceClient interface {
	// get camera list
	GetCameraList(ctx context.Context, in *GetCameraListParams, opts ...grpc.CallOption) (*GetCameraListResult, error)
	// update camera
	UpdateCamera(ctx context.Context, in *UpdateCameraParams, opts ...grpc.CallOption) (*UpdateCameraResult, error)
}

type cameraServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCameraServiceClient(cc grpc.ClientConnInterface) CameraServiceClient {
	return &cameraServiceClient{cc}
}

func (c *cameraServiceClient) GetCameraList(ctx context.Context, in *GetCameraListParams, opts ...grpc.CallOption) (*GetCameraListResult, error) {
	out := new(GetCameraListResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CameraService/GetCameraList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cameraServiceClient) UpdateCamera(ctx context.Context, in *UpdateCameraParams, opts ...grpc.CallOption) (*UpdateCameraResult, error) {
	out := new(UpdateCameraResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.CameraService/UpdateCamera", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CameraServiceServer is the server API for CameraService service.
// All implementations must embed UnimplementedCameraServiceServer
// for forward compatibility
type CameraServiceServer interface {
	// get camera list
	GetCameraList(context.Context, *GetCameraListParams) (*GetCameraListResult, error)
	// update camera
	UpdateCamera(context.Context, *UpdateCameraParams) (*UpdateCameraResult, error)
	mustEmbedUnimplementedCameraServiceServer()
}

// UnimplementedCameraServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCameraServiceServer struct {
}

func (UnimplementedCameraServiceServer) GetCameraList(context.Context, *GetCameraListParams) (*GetCameraListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraList not implemented")
}
func (UnimplementedCameraServiceServer) UpdateCamera(context.Context, *UpdateCameraParams) (*UpdateCameraResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCamera not implemented")
}
func (UnimplementedCameraServiceServer) mustEmbedUnimplementedCameraServiceServer() {}

// UnsafeCameraServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CameraServiceServer will
// result in compilation errors.
type UnsafeCameraServiceServer interface {
	mustEmbedUnimplementedCameraServiceServer()
}

func RegisterCameraServiceServer(s grpc.ServiceRegistrar, srv CameraServiceServer) {
	s.RegisterService(&CameraService_ServiceDesc, srv)
}

func _CameraService_GetCameraList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCameraListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CameraServiceServer).GetCameraList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CameraService/GetCameraList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CameraServiceServer).GetCameraList(ctx, req.(*GetCameraListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CameraService_UpdateCamera_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCameraParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CameraServiceServer).UpdateCamera(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.CameraService/UpdateCamera",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CameraServiceServer).UpdateCamera(ctx, req.(*UpdateCameraParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CameraService_ServiceDesc is the grpc.ServiceDesc for CameraService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CameraService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.organization.v1.CameraService",
	HandlerType: (*CameraServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCameraList",
			Handler:    _CameraService_GetCameraList_Handler,
		},
		{
			MethodName: "UpdateCamera",
			Handler:    _CameraService_UpdateCamera_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/organization/v1/camera_api.proto",
}
