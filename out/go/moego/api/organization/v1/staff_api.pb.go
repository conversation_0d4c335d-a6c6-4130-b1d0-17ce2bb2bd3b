// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/organization/v1/staff_api.proto

package organizationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// request for create a new staff
type CreateStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff profile def
	StaffProfile *v1.CreateStaffDef `protobuf:"bytes,1,opt,name=staff_profile,json=staffProfile,proto3" json:"staff_profile,omitempty"`
	// working business def
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,2,opt,name=working_location,json=workingLocation,proto3,oneof" json:"working_location,omitempty"`
	// access control def
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,3,opt,name=access_control,json=accessControl,proto3,oneof" json:"access_control,omitempty"`
	// notification def
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,4,opt,name=notification_setting,json=notificationSetting,proto3,oneof" json:"notification_setting,omitempty"`
	// payroll setting def
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,5,opt,name=payroll_setting,json=payrollSetting,proto3,oneof" json:"payroll_setting,omitempty"`
	// send invite link params
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,6,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,7,opt,name=login_time,json=loginTime,proto3,oneof" json:"login_time,omitempty"`
}

func (x *CreateStaffParams) Reset() {
	*x = CreateStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffParams) ProtoMessage() {}

func (x *CreateStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffParams.ProtoReflect.Descriptor instead.
func (*CreateStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateStaffParams) GetStaffProfile() *v1.CreateStaffDef {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *CreateStaffParams) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *CreateStaffParams) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *CreateStaffParams) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *CreateStaffParams) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *CreateStaffParams) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

func (x *CreateStaffParams) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// response for create a new staff
type CreateStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// generated staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateStaffResult) Reset() {
	*x = CreateStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffResult) ProtoMessage() {}

func (x *CreateStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffResult.ProtoReflect.Descriptor instead.
func (*CreateStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateStaffResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// request for get staff detail
type GetStaffFullDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetStaffFullDetailParams) Reset() {
	*x = GetStaffFullDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffFullDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffFullDetailParams) ProtoMessage() {}

func (x *GetStaffFullDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffFullDetailParams.ProtoReflect.Descriptor instead.
func (*GetStaffFullDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetStaffFullDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// response for get staff detail
type GetStaffFullDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff info
	StaffProfile *v1.StaffBasicView `protobuf:"bytes,2,opt,name=staff_profile,json=staffProfile,proto3" json:"staff_profile,omitempty"`
	// working location
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,3,opt,name=working_location,json=workingLocation,proto3" json:"working_location,omitempty"`
	// access control
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,4,opt,name=access_control,json=accessControl,proto3" json:"access_control,omitempty"`
	// notification setting
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,5,opt,name=notification_setting,json=notificationSetting,proto3" json:"notification_setting,omitempty"`
	// payroll setting
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,6,opt,name=payroll_setting,json=payrollSetting,proto3" json:"payroll_setting,omitempty"`
	// staff email
	StaffEmail *v1.StaffEmailDef `protobuf:"bytes,7,opt,name=staff_email,json=staffEmail,proto3" json:"staff_email,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,8,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
}

func (x *GetStaffFullDetailResult) Reset() {
	*x = GetStaffFullDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffFullDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffFullDetailResult) ProtoMessage() {}

func (x *GetStaffFullDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffFullDetailResult.ProtoReflect.Descriptor instead.
func (*GetStaffFullDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetStaffFullDetailResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetStaffFullDetailResult) GetStaffProfile() *v1.StaffBasicView {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetStaffEmail() *v1.StaffEmailDef {
	if x != nil {
		return x.StaffEmail
	}
	return nil
}

func (x *GetStaffFullDetailResult) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// request for update staff
type UpdateStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff profile def
	StaffProfile *v1.UpdateStaffDef `protobuf:"bytes,2,opt,name=staff_profile,json=staffProfile,proto3,oneof" json:"staff_profile,omitempty"`
	// working business def
	WorkingLocation *v1.StaffWorkingLocationDef `protobuf:"bytes,3,opt,name=working_location,json=workingLocation,proto3,oneof" json:"working_location,omitempty"`
	// access control def
	AccessControl *v1.StaffAccessControlDef `protobuf:"bytes,4,opt,name=access_control,json=accessControl,proto3,oneof" json:"access_control,omitempty"`
	// notification def
	NotificationSetting *v1.StaffNotificationDef `protobuf:"bytes,5,opt,name=notification_setting,json=notificationSetting,proto3,oneof" json:"notification_setting,omitempty"`
	// payroll setting def
	PayrollSetting *v1.StaffPayrollSettingDef `protobuf:"bytes,6,opt,name=payroll_setting,json=payrollSetting,proto3,oneof" json:"payroll_setting,omitempty"`
	// send invite link params
	InviteLink *v1.SendInviteLinkParamsDef `protobuf:"bytes,7,opt,name=invite_link,json=inviteLink,proto3,oneof" json:"invite_link,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,8,opt,name=login_time,json=loginTime,proto3,oneof" json:"login_time,omitempty"`
}

func (x *UpdateStaffParams) Reset() {
	*x = UpdateStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffParams) ProtoMessage() {}

func (x *UpdateStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateStaffParams) GetStaffProfile() *v1.UpdateStaffDef {
	if x != nil {
		return x.StaffProfile
	}
	return nil
}

func (x *UpdateStaffParams) GetWorkingLocation() *v1.StaffWorkingLocationDef {
	if x != nil {
		return x.WorkingLocation
	}
	return nil
}

func (x *UpdateStaffParams) GetAccessControl() *v1.StaffAccessControlDef {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *UpdateStaffParams) GetNotificationSetting() *v1.StaffNotificationDef {
	if x != nil {
		return x.NotificationSetting
	}
	return nil
}

func (x *UpdateStaffParams) GetPayrollSetting() *v1.StaffPayrollSettingDef {
	if x != nil {
		return x.PayrollSetting
	}
	return nil
}

func (x *UpdateStaffParams) GetInviteLink() *v1.SendInviteLinkParamsDef {
	if x != nil {
		return x.InviteLink
	}
	return nil
}

func (x *UpdateStaffParams) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// response for update staff
type UpdateStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateStaffResult) Reset() {
	*x = UpdateStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffResult) ProtoMessage() {}

func (x *UpdateStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffResult.ProtoReflect.Descriptor instead.
func (*UpdateStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateStaffResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// request for delete staff
type DeleteStaffParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteStaffParams) Reset() {
	*x = DeleteStaffParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffParams) ProtoMessage() {}

func (x *DeleteStaffParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffParams.ProtoReflect.Descriptor instead.
func (*DeleteStaffParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteStaffParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// response for delete staff
type DeleteStaffResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deleted result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteStaffResult) Reset() {
	*x = DeleteStaffResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffResult) ProtoMessage() {}

func (x *DeleteStaffResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffResult.ProtoReflect.Descriptor instead.
func (*DeleteStaffResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteStaffResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// request for query staff list by pagination
type QueryStaffListByPaginationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// preserved 2-13 for future usage, for example: query by keyword, status, role, etc.
	// order by params
	OrderBys []*v2.OrderBy `protobuf:"bytes,14,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination params
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffListByPaginationParams) Reset() {
	*x = QueryStaffListByPaginationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffListByPaginationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffListByPaginationParams) ProtoMessage() {}

func (x *QueryStaffListByPaginationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffListByPaginationParams.ProtoReflect.Descriptor instead.
func (*QueryStaffListByPaginationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{8}
}

func (x *QueryStaffListByPaginationParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *QueryStaffListByPaginationParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *QueryStaffListByPaginationParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// response for query staff list by pagination
type QueryStaffListByPaginationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*v1.StaffInfoDef `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryStaffListByPaginationResult) Reset() {
	*x = QueryStaffListByPaginationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStaffListByPaginationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStaffListByPaginationResult) ProtoMessage() {}

func (x *QueryStaffListByPaginationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStaffListByPaginationResult.ProtoReflect.Descriptor instead.
func (*QueryStaffListByPaginationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{9}
}

func (x *QueryStaffListByPaginationResult) GetStaffs() []*v1.StaffInfoDef {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *QueryStaffListByPaginationResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// request for get staff list by business id
type GetAllWorkingLocationStaffsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAllWorkingLocationStaffsParams) Reset() {
	*x = GetAllWorkingLocationStaffsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllWorkingLocationStaffsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllWorkingLocationStaffsParams) ProtoMessage() {}

func (x *GetAllWorkingLocationStaffsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllWorkingLocationStaffsParams.ProtoReflect.Descriptor instead.
func (*GetAllWorkingLocationStaffsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{10}
}

// response for get working location staff list by business id
type GetAllWorkingLocationStaffsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
	// total location count
	TotalLocationCount int64 `protobuf:"varint,2,opt,name=total_location_count,json=totalLocationCount,proto3" json:"total_location_count,omitempty"`
	// total staff count
	TotalStaffCount int64 `protobuf:"varint,3,opt,name=total_staff_count,json=totalStaffCount,proto3" json:"total_staff_count,omitempty"`
}

func (x *GetAllWorkingLocationStaffsResult) Reset() {
	*x = GetAllWorkingLocationStaffsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllWorkingLocationStaffsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllWorkingLocationStaffsResult) ProtoMessage() {}

func (x *GetAllWorkingLocationStaffsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllWorkingLocationStaffsResult.ProtoReflect.Descriptor instead.
func (*GetAllWorkingLocationStaffsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetAllWorkingLocationStaffsResult) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

func (x *GetAllWorkingLocationStaffsResult) GetTotalLocationCount() int64 {
	if x != nil {
		return x.TotalLocationCount
	}
	return 0
}

func (x *GetAllWorkingLocationStaffsResult) GetTotalStaffCount() int64 {
	if x != nil {
		return x.TotalStaffCount
	}
	return 0
}

// request for get staff list by business ids
type GetStaffsByWorkingLocationIdsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids, if empty, will get all working location staffs
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetStaffsByWorkingLocationIdsParams) Reset() {
	*x = GetStaffsByWorkingLocationIdsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationIdsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationIdsParams) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationIdsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationIdsParams.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationIdsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetStaffsByWorkingLocationIdsParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// response for get working staff list by business ids
type GetStaffsByWorkingLocationIdsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
	// total location count
	TotalLocationCount int64 `protobuf:"varint,2,opt,name=total_location_count,json=totalLocationCount,proto3" json:"total_location_count,omitempty"`
	// total staff count
	TotalStaffCount int64 `protobuf:"varint,3,opt,name=total_staff_count,json=totalStaffCount,proto3" json:"total_staff_count,omitempty"`
}

func (x *GetStaffsByWorkingLocationIdsResult) Reset() {
	*x = GetStaffsByWorkingLocationIdsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffsByWorkingLocationIdsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffsByWorkingLocationIdsResult) ProtoMessage() {}

func (x *GetStaffsByWorkingLocationIdsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffsByWorkingLocationIdsResult.ProtoReflect.Descriptor instead.
func (*GetStaffsByWorkingLocationIdsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetStaffsByWorkingLocationIdsResult) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

func (x *GetStaffsByWorkingLocationIdsResult) GetTotalLocationCount() int64 {
	if x != nil {
		return x.TotalLocationCount
	}
	return 0
}

func (x *GetStaffsByWorkingLocationIdsResult) GetTotalStaffCount() int64 {
	if x != nil {
		return x.TotalStaffCount
	}
	return 0
}

// request for get clock in out staff list
type GetClockInOutStaffsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// clock in/out date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *GetClockInOutStaffsParams) Reset() {
	*x = GetClockInOutStaffsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutStaffsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutStaffsParams) ProtoMessage() {}

func (x *GetClockInOutStaffsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutStaffsParams.ProtoReflect.Descriptor instead.
func (*GetClockInOutStaffsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetClockInOutStaffsParams) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetClockInOutStaffsParams) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// response for get clock in out staff list
type GetClockInOutStaffsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	ClockInOutStaffs []*v1.ClockInOutStaffDef `protobuf:"bytes,1,rep,name=clock_in_out_staffs,json=clockInOutStaffs,proto3" json:"clock_in_out_staffs,omitempty"`
}

func (x *GetClockInOutStaffsResult) Reset() {
	*x = GetClockInOutStaffsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutStaffsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutStaffsResult) ProtoMessage() {}

func (x *GetClockInOutStaffsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutStaffsResult.ProtoReflect.Descriptor instead.
func (*GetClockInOutStaffsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetClockInOutStaffsResult) GetClockInOutStaffs() []*v1.ClockInOutStaffDef {
	if x != nil {
		return x.ClockInOutStaffs
	}
	return nil
}

// request for get enterprise staff list by working location ids
type GetEnterpriseStaffsByWorkingLocationIdsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids, if empty, will get all working location enterprise staffs
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsParams) Reset() {
	*x = GetEnterpriseStaffsByWorkingLocationIdsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByWorkingLocationIdsParams) ProtoMessage() {}

func (x *GetEnterpriseStaffsByWorkingLocationIdsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByWorkingLocationIdsParams.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByWorkingLocationIdsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// response for get working staff list by business ids
type GetEnterpriseStaffsByWorkingLocationIdsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	LocationStaffs []*v1.LocationStaffsDef `protobuf:"bytes,1,rep,name=location_staffs,json=locationStaffs,proto3" json:"location_staffs,omitempty"`
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResult) Reset() {
	*x = GetEnterpriseStaffsByWorkingLocationIdsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseStaffsByWorkingLocationIdsResult) ProtoMessage() {}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseStaffsByWorkingLocationIdsResult.ProtoReflect.Descriptor instead.
func (*GetEnterpriseStaffsByWorkingLocationIdsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetEnterpriseStaffsByWorkingLocationIdsResult) GetLocationStaffs() []*v1.LocationStaffsDef {
	if x != nil {
		return x.LocationStaffs
	}
	return nil
}

// get staff login time params
type GetStaffLoginTimeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *GetStaffLoginTimeParams) Reset() {
	*x = GetStaffLoginTimeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffLoginTimeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffLoginTimeParams) ProtoMessage() {}

func (x *GetStaffLoginTimeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffLoginTimeParams.ProtoReflect.Descriptor instead.
func (*GetStaffLoginTimeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetStaffLoginTimeParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// get staff login time result
type GetStaffLoginTimeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff login time
	LoginTime *v1.StaffLoginTimeModel `protobuf:"bytes,1,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
}

func (x *GetStaffLoginTimeResult) Reset() {
	*x = GetStaffLoginTimeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffLoginTimeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffLoginTimeResult) ProtoMessage() {}

func (x *GetStaffLoginTimeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffLoginTimeResult.ProtoReflect.Descriptor instead.
func (*GetStaffLoginTimeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{19}
}

func (x *GetStaffLoginTimeResult) GetLoginTime() *v1.StaffLoginTimeModel {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// update staff login time params
type UpdateStaffLoginTimeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,2,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
}

func (x *UpdateStaffLoginTimeParams) Reset() {
	*x = UpdateStaffLoginTimeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffLoginTimeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffLoginTimeParams) ProtoMessage() {}

func (x *UpdateStaffLoginTimeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffLoginTimeParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffLoginTimeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateStaffLoginTimeParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateStaffLoginTimeParams) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// update staff login time result
type UpdateStaffLoginTimeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateStaffLoginTimeResult) Reset() {
	*x = UpdateStaffLoginTimeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffLoginTimeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffLoginTimeResult) ProtoMessage() {}

func (x *UpdateStaffLoginTimeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffLoginTimeResult.ProtoReflect.Descriptor instead.
func (*UpdateStaffLoginTimeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateStaffLoginTimeResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get recommended staff login time in company
type GetRecommendedStaffLoginTimeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRecommendedStaffLoginTimeParams) Reset() {
	*x = GetRecommendedStaffLoginTimeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendedStaffLoginTimeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendedStaffLoginTimeParams) ProtoMessage() {}

func (x *GetRecommendedStaffLoginTimeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendedStaffLoginTimeParams.ProtoReflect.Descriptor instead.
func (*GetRecommendedStaffLoginTimeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{22}
}

// get recommended staff login time result
type GetRecommendedStaffLoginTimeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recommended staff login time
	LoginTime *v1.StaffLoginTimeDef `protobuf:"bytes,1,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
}

func (x *GetRecommendedStaffLoginTimeResult) Reset() {
	*x = GetRecommendedStaffLoginTimeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendedStaffLoginTimeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendedStaffLoginTimeResult) ProtoMessage() {}

func (x *GetRecommendedStaffLoginTimeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendedStaffLoginTimeResult.ProtoReflect.Descriptor instead.
func (*GetRecommendedStaffLoginTimeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetRecommendedStaffLoginTimeResult) GetLoginTime() *v1.StaffLoginTimeDef {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

// The params of list staff group by role
type ListStaffGroupByRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff working date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *ListStaffGroupByRoleParams) Reset() {
	*x = ListStaffGroupByRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffGroupByRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffGroupByRoleParams) ProtoMessage() {}

func (x *ListStaffGroupByRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffGroupByRoleParams.ProtoReflect.Descriptor instead.
func (*ListStaffGroupByRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{24}
}

func (x *ListStaffGroupByRoleParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListStaffGroupByRoleParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

// The result of list staff group by role
type ListStaffGroupByRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff group by role
	RoleStaffGroups []*ListStaffGroupByRoleResult_RoleStaffGroup `protobuf:"bytes,1,rep,name=role_staff_groups,json=roleStaffGroups,proto3" json:"role_staff_groups,omitempty"`
	// Working staff ids
	WorkingStaffIds []int64 `protobuf:"varint,2,rep,packed,name=working_staff_ids,json=workingStaffIds,proto3" json:"working_staff_ids,omitempty"`
}

func (x *ListStaffGroupByRoleResult) Reset() {
	*x = ListStaffGroupByRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffGroupByRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffGroupByRoleResult) ProtoMessage() {}

func (x *ListStaffGroupByRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffGroupByRoleResult.ProtoReflect.Descriptor instead.
func (*ListStaffGroupByRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{25}
}

func (x *ListStaffGroupByRoleResult) GetRoleStaffGroups() []*ListStaffGroupByRoleResult_RoleStaffGroup {
	if x != nil {
		return x.RoleStaffGroups
	}
	return nil
}

func (x *ListStaffGroupByRoleResult) GetWorkingStaffIds() []int64 {
	if x != nil {
		return x.WorkingStaffIds
	}
	return nil
}

// GetBusinessStaffAvailabilityTypeRequest staff availability 配置
type GetBusinessStaffAvailabilityTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBusinessStaffAvailabilityTypeParams) Reset() {
	*x = GetBusinessStaffAvailabilityTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessStaffAvailabilityTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessStaffAvailabilityTypeParams) ProtoMessage() {}

func (x *GetBusinessStaffAvailabilityTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessStaffAvailabilityTypeParams.ProtoReflect.Descriptor instead.
func (*GetBusinessStaffAvailabilityTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{26}
}

func (x *GetBusinessStaffAvailabilityTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// UpdateBusinessStaffAvailabilityTypeRequest update staff availability 配置
type UpdateBusinessStaffAvailabilityTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// availability type
	AvailabilityType v1.AvailabilityType `protobuf:"varint,2,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType" json:"availability_type,omitempty"`
}

func (x *UpdateBusinessStaffAvailabilityTypeParams) Reset() {
	*x = UpdateBusinessStaffAvailabilityTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBusinessStaffAvailabilityTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBusinessStaffAvailabilityTypeParams) ProtoMessage() {}

func (x *UpdateBusinessStaffAvailabilityTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBusinessStaffAvailabilityTypeParams.ProtoReflect.Descriptor instead.
func (*UpdateBusinessStaffAvailabilityTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateBusinessStaffAvailabilityTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateBusinessStaffAvailabilityTypeParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil {
		return x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// UpdateBusinessStaffAvailabilityTypeResponse update staff availability 配置
type UpdateBusinessStaffAvailabilityTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateBusinessStaffAvailabilityTypeResult) Reset() {
	*x = UpdateBusinessStaffAvailabilityTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBusinessStaffAvailabilityTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBusinessStaffAvailabilityTypeResult) ProtoMessage() {}

func (x *UpdateBusinessStaffAvailabilityTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBusinessStaffAvailabilityTypeResult.ProtoReflect.Descriptor instead.
func (*UpdateBusinessStaffAvailabilityTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{28}
}

// GetBusinessStaffAvailabilityTypeResponse 返回staff 类型的结果
type GetBusinessStaffAvailabilityTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// availability type
	AvailabilityType v1.AvailabilityType `protobuf:"varint,1,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType" json:"availability_type,omitempty"`
}

func (x *GetBusinessStaffAvailabilityTypeResult) Reset() {
	*x = GetBusinessStaffAvailabilityTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessStaffAvailabilityTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessStaffAvailabilityTypeResult) ProtoMessage() {}

func (x *GetBusinessStaffAvailabilityTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessStaffAvailabilityTypeResult.ProtoReflect.Descriptor instead.
func (*GetBusinessStaffAvailabilityTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{29}
}

func (x *GetBusinessStaffAvailabilityTypeResult) GetAvailabilityType() v1.AvailabilityType {
	if x != nil {
		return x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// GetStaffCalenderViewRequest
type GetStaffCalenderViewParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,5,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffCalenderViewParams) Reset() {
	*x = GetStaffCalenderViewParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffCalenderViewParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffCalenderViewParams) ProtoMessage() {}

func (x *GetStaffCalenderViewParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffCalenderViewParams.ProtoReflect.Descriptor instead.
func (*GetStaffCalenderViewParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{30}
}

func (x *GetStaffCalenderViewParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffCalenderViewParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetStaffCalenderViewParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// CalenderStaff
type CalenderStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType v1.ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// slot daily setting
	SlotAvailabilityDayMap map[string]*v1.SlotAvailabilityDay `protobuf:"bytes,4,rep,name=slot_availability_day_map,json=slotAvailabilityDayMap,proto3" json:"slot_availability_day_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// slot start sunday
	SlotStartSunday string `protobuf:"bytes,6,opt,name=slot_start_sunday,json=slotStartSunday,proto3" json:"slot_start_sunday,omitempty"`
	// schedule type
	TimeScheduleType v1.ScheduleType `protobuf:"varint,7,opt,name=time_schedule_type,json=timeScheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"time_schedule_type,omitempty"`
	// time daily setting
	TimeAvailabilityDayMap map[string]*v1.TimeAvailabilityDay `protobuf:"bytes,8,rep,name=time_availability_day_map,json=timeAvailabilityDayMap,proto3" json:"time_availability_day_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// time start sunday
	TimeStartSunday string `protobuf:"bytes,9,opt,name=time_start_sunday,json=timeStartSunday,proto3" json:"time_start_sunday,omitempty"`
}

func (x *CalenderStaff) Reset() {
	*x = CalenderStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalenderStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalenderStaff) ProtoMessage() {}

func (x *CalenderStaff) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalenderStaff.ProtoReflect.Descriptor instead.
func (*CalenderStaff) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{31}
}

func (x *CalenderStaff) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CalenderStaff) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *CalenderStaff) GetScheduleType() v1.ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *CalenderStaff) GetSlotAvailabilityDayMap() map[string]*v1.SlotAvailabilityDay {
	if x != nil {
		return x.SlotAvailabilityDayMap
	}
	return nil
}

func (x *CalenderStaff) GetSlotStartSunday() string {
	if x != nil {
		return x.SlotStartSunday
	}
	return ""
}

func (x *CalenderStaff) GetTimeScheduleType() v1.ScheduleType {
	if x != nil {
		return x.TimeScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *CalenderStaff) GetTimeAvailabilityDayMap() map[string]*v1.TimeAvailabilityDay {
	if x != nil {
		return x.TimeAvailabilityDayMap
	}
	return nil
}

func (x *CalenderStaff) GetTimeStartSunday() string {
	if x != nil {
		return x.TimeStartSunday
	}
	return ""
}

// GetStaffCalenderViewResponse
type GetStaffCalenderViewResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffAvailabilityList []*CalenderStaff `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *GetStaffCalenderViewResult) Reset() {
	*x = GetStaffCalenderViewResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffCalenderViewResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffCalenderViewResult) ProtoMessage() {}

func (x *GetStaffCalenderViewResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffCalenderViewResult.ProtoReflect.Descriptor instead.
func (*GetStaffCalenderViewResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{32}
}

func (x *GetStaffCalenderViewResult) GetStaffAvailabilityList() []*CalenderStaff {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// GetStaffAvailabilityRequest 获取staff 的 availability
type GetStaffAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list, staff will init when staff_id no exist
	StaffIdList []int64 `protobuf:"varint,3,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityParams) Reset() {
	*x = GetStaffAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityParams) ProtoMessage() {}

func (x *GetStaffAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{33}
}

func (x *GetStaffAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityParams) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

func (x *GetStaffAvailabilityParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// get staff availability result
type GetStaffAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff availability info
	//
	// Deprecated: Do not use.
	StaffList []*StaffAvailabilityInfo `protobuf:"bytes,3,rep,name=staff_list,json=staffList,proto3" json:"staff_list,omitempty"`
	// staff available list
	StaffAvailabilityList []*v1.StaffAvailability `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *GetStaffAvailabilityResult) Reset() {
	*x = GetStaffAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityResult) ProtoMessage() {}

func (x *GetStaffAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{34}
}

// Deprecated: Do not use.
func (x *GetStaffAvailabilityResult) GetStaffList() []*StaffAvailabilityInfo {
	if x != nil {
		return x.StaffList
	}
	return nil
}

func (x *GetStaffAvailabilityResult) GetStaffAvailabilityList() []*v1.StaffAvailability {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// StaffAvailabilityInfo
type StaffAvailabilityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// schedule type
	ScheduleType v1.ScheduleType `protobuf:"varint,4,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// weeks availability, key: "firstWeek", "secondWeek", "thirdWeek", "forthWeek"
	Weeks map[string]*WeekAvailability `protobuf:"bytes,5,rep,name=weeks,proto3" json:"weeks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StaffAvailabilityInfo) Reset() {
	*x = StaffAvailabilityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAvailabilityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAvailabilityInfo) ProtoMessage() {}

func (x *StaffAvailabilityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAvailabilityInfo.ProtoReflect.Descriptor instead.
func (*StaffAvailabilityInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{35}
}

func (x *StaffAvailabilityInfo) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffAvailabilityInfo) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *StaffAvailabilityInfo) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *StaffAvailabilityInfo) GetScheduleType() v1.ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *StaffAvailabilityInfo) GetWeeks() map[string]*WeekAvailability {
	if x != nil {
		return x.Weeks
	}
	return nil
}

// WeekAvailability
type WeekAvailability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// by slot, key: "sunday", "monday", ..., "saturday"
	Days map[string]*v1.SlotAvailabilityDay `protobuf:"bytes,1,rep,name=days,proto3" json:"days,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WeekAvailability) Reset() {
	*x = WeekAvailability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeekAvailability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekAvailability) ProtoMessage() {}

func (x *WeekAvailability) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekAvailability.ProtoReflect.Descriptor instead.
func (*WeekAvailability) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{36}
}

func (x *WeekAvailability) GetDays() map[string]*v1.SlotAvailabilityDay {
	if x != nil {
		return x.Days
	}
	return nil
}

// UpdateStaffAvailabilityParams
type UpdateStaffAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff availability info
	//
	// Deprecated: Do not use.
	StaffList []*StaffAvailabilityInfo `protobuf:"bytes,3,rep,name=staff_list,json=staffList,proto3" json:"staff_list,omitempty"`
	// staff available list
	StaffAvailabilityList []*v1.StaffAvailabilityDef `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *UpdateStaffAvailabilityParams) Reset() {
	*x = UpdateStaffAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityParams) ProtoMessage() {}

func (x *UpdateStaffAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateStaffAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Deprecated: Do not use.
func (x *UpdateStaffAvailabilityParams) GetStaffList() []*StaffAvailabilityInfo {
	if x != nil {
		return x.StaffList
	}
	return nil
}

func (x *UpdateStaffAvailabilityParams) GetStaffAvailabilityList() []*v1.StaffAvailabilityDef {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// UpdateStaffAvailabilityOverrideParams
type UpdateStaffAvailabilityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// slot override days
	OverrideDays []*v1.SlotAvailabilityDay `protobuf:"bytes,4,rep,name=override_days,json=overrideDays,proto3" json:"override_days,omitempty"`
	// slot override days
	SlotOverrideDays []*v1.SlotAvailabilityDayDef `protobuf:"bytes,5,rep,name=slot_override_days,json=slotOverrideDays,proto3" json:"slot_override_days,omitempty"`
	// time override days
	TimeOverrideDays []*v1.TimeAvailabilityDayDef `protobuf:"bytes,6,rep,name=time_override_days,json=timeOverrideDays,proto3" json:"time_override_days,omitempty"`
}

func (x *UpdateStaffAvailabilityOverrideParams) Reset() {
	*x = UpdateStaffAvailabilityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityOverrideParams) ProtoMessage() {}

func (x *UpdateStaffAvailabilityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityOverrideParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateStaffAvailabilityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateStaffAvailabilityOverrideParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateStaffAvailabilityOverrideParams) GetOverrideDays() []*v1.SlotAvailabilityDay {
	if x != nil {
		return x.OverrideDays
	}
	return nil
}

func (x *UpdateStaffAvailabilityOverrideParams) GetSlotOverrideDays() []*v1.SlotAvailabilityDayDef {
	if x != nil {
		return x.SlotOverrideDays
	}
	return nil
}

func (x *UpdateStaffAvailabilityOverrideParams) GetTimeOverrideDays() []*v1.TimeAvailabilityDayDef {
	if x != nil {
		return x.TimeOverrideDays
	}
	return nil
}

// UpdateStaffAvailabilityResponse
type UpdateStaffAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStaffAvailabilityResult) Reset() {
	*x = UpdateStaffAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityResult) ProtoMessage() {}

func (x *UpdateStaffAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{39}
}

// GetStaffAvailabilityOverrideParams
type GetStaffAvailabilityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff ids
	StaffIdList []int64 `protobuf:"varint,3,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityOverrideParams) Reset() {
	*x = GetStaffAvailabilityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityOverrideParams) ProtoMessage() {}

func (x *GetStaffAvailabilityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityOverrideParams.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{40}
}

func (x *GetStaffAvailabilityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityOverrideParams) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

func (x *GetStaffAvailabilityOverrideParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// SlotAvailabilityDayOngoingHistoryList
type SlotAvailabilityDayOngoingHistoryList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot availability day
	Ongoing []*v1.SlotAvailabilityDay `protobuf:"bytes,1,rep,name=ongoing,proto3" json:"ongoing,omitempty"`
	// staff availability info history
	History []*v1.SlotAvailabilityDay `protobuf:"bytes,2,rep,name=history,proto3" json:"history,omitempty"`
}

func (x *SlotAvailabilityDayOngoingHistoryList) Reset() {
	*x = SlotAvailabilityDayOngoingHistoryList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotAvailabilityDayOngoingHistoryList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotAvailabilityDayOngoingHistoryList) ProtoMessage() {}

func (x *SlotAvailabilityDayOngoingHistoryList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotAvailabilityDayOngoingHistoryList.ProtoReflect.Descriptor instead.
func (*SlotAvailabilityDayOngoingHistoryList) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{41}
}

func (x *SlotAvailabilityDayOngoingHistoryList) GetOngoing() []*v1.SlotAvailabilityDay {
	if x != nil {
		return x.Ongoing
	}
	return nil
}

func (x *SlotAvailabilityDayOngoingHistoryList) GetHistory() []*v1.SlotAvailabilityDay {
	if x != nil {
		return x.History
	}
	return nil
}

// TimeAvailabilityDayOngoingHistoryList
type TimeAvailabilityDayOngoingHistoryList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time availability day
	Ongoing []*v1.TimeAvailabilityDay `protobuf:"bytes,1,rep,name=ongoing,proto3" json:"ongoing,omitempty"`
	// staff availability info history
	History []*v1.TimeAvailabilityDay `protobuf:"bytes,2,rep,name=history,proto3" json:"history,omitempty"`
}

func (x *TimeAvailabilityDayOngoingHistoryList) Reset() {
	*x = TimeAvailabilityDayOngoingHistoryList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAvailabilityDayOngoingHistoryList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAvailabilityDayOngoingHistoryList) ProtoMessage() {}

func (x *TimeAvailabilityDayOngoingHistoryList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAvailabilityDayOngoingHistoryList.ProtoReflect.Descriptor instead.
func (*TimeAvailabilityDayOngoingHistoryList) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{42}
}

func (x *TimeAvailabilityDayOngoingHistoryList) GetOngoing() []*v1.TimeAvailabilityDay {
	if x != nil {
		return x.Ongoing
	}
	return nil
}

func (x *TimeAvailabilityDayOngoingHistoryList) GetHistory() []*v1.TimeAvailabilityDay {
	if x != nil {
		return x.History
	}
	return nil
}

// GetStaffAvailabilityOverrideResult
type GetStaffAvailabilityOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff availability info
	StaffMap map[int64]*SlotAvailabilityDayOngoingHistoryList `protobuf:"bytes,1,rep,name=staff_map,json=staffMap,proto3" json:"staff_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// time availability info
	TimeStaffMap map[int64]*TimeAvailabilityDayOngoingHistoryList `protobuf:"bytes,2,rep,name=time_staff_map,json=timeStaffMap,proto3" json:"time_staff_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetStaffAvailabilityOverrideResult) Reset() {
	*x = GetStaffAvailabilityOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityOverrideResult) ProtoMessage() {}

func (x *GetStaffAvailabilityOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityOverrideResult.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{43}
}

func (x *GetStaffAvailabilityOverrideResult) GetStaffMap() map[int64]*SlotAvailabilityDayOngoingHistoryList {
	if x != nil {
		return x.StaffMap
	}
	return nil
}

func (x *GetStaffAvailabilityOverrideResult) GetTimeStaffMap() map[int64]*TimeAvailabilityDayOngoingHistoryList {
	if x != nil {
		return x.TimeStaffMap
	}
	return nil
}

// DeleteStaffAvailabilityOverrideParams
type DeleteStaffAvailabilityOverrideParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff availability override day list
	OverrideDays []string `protobuf:"bytes,4,rep,name=override_days,json=overrideDays,proto3" json:"override_days,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,5,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *DeleteStaffAvailabilityOverrideParams) Reset() {
	*x = DeleteStaffAvailabilityOverrideParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffAvailabilityOverrideParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffAvailabilityOverrideParams) ProtoMessage() {}

func (x *DeleteStaffAvailabilityOverrideParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffAvailabilityOverrideParams.ProtoReflect.Descriptor instead.
func (*DeleteStaffAvailabilityOverrideParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{44}
}

func (x *DeleteStaffAvailabilityOverrideParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteStaffAvailabilityOverrideParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *DeleteStaffAvailabilityOverrideParams) GetOverrideDays() []string {
	if x != nil {
		return x.OverrideDays
	}
	return nil
}

func (x *DeleteStaffAvailabilityOverrideParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// DeleteStaffAvailabilityOverrideResult
type DeleteStaffAvailabilityOverrideResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteStaffAvailabilityOverrideResult) Reset() {
	*x = DeleteStaffAvailabilityOverrideResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStaffAvailabilityOverrideResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStaffAvailabilityOverrideResult) ProtoMessage() {}

func (x *DeleteStaffAvailabilityOverrideResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStaffAvailabilityOverrideResult.ProtoReflect.Descriptor instead.
func (*DeleteStaffAvailabilityOverrideResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{45}
}

// init staff availability params
type InitStaffAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// start business id
	StartBusinessId *int64 `protobuf:"varint,4,opt,name=start_business_id,json=startBusinessId,proto3,oneof" json:"start_business_id,omitempty"`
	// end business id
	EndBusinessId *int64 `protobuf:"varint,5,opt,name=end_business_id,json=endBusinessId,proto3,oneof" json:"end_business_id,omitempty"`
}

func (x *InitStaffAvailabilityParams) Reset() {
	*x = InitStaffAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitStaffAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitStaffAvailabilityParams) ProtoMessage() {}

func (x *InitStaffAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitStaffAvailabilityParams.ProtoReflect.Descriptor instead.
func (*InitStaffAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{46}
}

func (x *InitStaffAvailabilityParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *InitStaffAvailabilityParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *InitStaffAvailabilityParams) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *InitStaffAvailabilityParams) GetStartBusinessId() int64 {
	if x != nil && x.StartBusinessId != nil {
		return *x.StartBusinessId
	}
	return 0
}

func (x *InitStaffAvailabilityParams) GetEndBusinessId() int64 {
	if x != nil && x.EndBusinessId != nil {
		return *x.EndBusinessId
	}
	return 0
}

// init staff availability result
type InitStaffAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InitStaffAvailabilityResult) Reset() {
	*x = InitStaffAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitStaffAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitStaffAvailabilityResult) ProtoMessage() {}

func (x *InitStaffAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitStaffAvailabilityResult.ProtoReflect.Descriptor instead.
func (*InitStaffAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{47}
}

// ListSlotFreeServicesParams
type ListSlotFreeServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *ListSlotFreeServicesParams) Reset() {
	*x = ListSlotFreeServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesParams) ProtoMessage() {}

func (x *ListSlotFreeServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesParams.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{48}
}

func (x *ListSlotFreeServicesParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListSlotFreeServicesParams) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// ListSlotFreeServicesResult
type ListSlotFreeServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,1,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *ListSlotFreeServicesResult) Reset() {
	*x = ListSlotFreeServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesResult) ProtoMessage() {}

func (x *ListSlotFreeServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesResult.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{49}
}

func (x *ListSlotFreeServicesResult) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesParams
type UpdateSlotFreeServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,2,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *UpdateSlotFreeServicesParams) Reset() {
	*x = UpdateSlotFreeServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesParams) ProtoMessage() {}

func (x *UpdateSlotFreeServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesParams.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateSlotFreeServicesParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateSlotFreeServicesParams) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesResult
type UpdateSlotFreeServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSlotFreeServicesResult) Reset() {
	*x = UpdateSlotFreeServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesResult) ProtoMessage() {}

func (x *UpdateSlotFreeServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesResult.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{51}
}

// Role staff group
type ListStaffGroupByRoleResult_RoleStaffGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Role name
	RoleName string `protobuf:"bytes,1,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	// Staffs
	Staffs []*v1.StaffBasicView `protobuf:"bytes,2,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *ListStaffGroupByRoleResult_RoleStaffGroup) Reset() {
	*x = ListStaffGroupByRoleResult_RoleStaffGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffGroupByRoleResult_RoleStaffGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffGroupByRoleResult_RoleStaffGroup) ProtoMessage() {}

func (x *ListStaffGroupByRoleResult_RoleStaffGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffGroupByRoleResult_RoleStaffGroup.ProtoReflect.Descriptor instead.
func (*ListStaffGroupByRoleResult_RoleStaffGroup) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_api_proto_rawDescGZIP(), []int{25, 0}
}

func (x *ListStaffGroupByRoleResult_RoleStaffGroup) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *ListStaffGroupByRoleResult_RoleStaffGroup) GetStaffs() []*v1.StaffBasicView {
	if x != nil {
		return x.Staffs
	}
	return nil
}

var File_moego_api_organization_v1_staff_api_proto protoreflect.FileDescriptor

var file_moego_api_organization_v1_staff_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x06, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x65, 0x0a, 0x10,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52,
	0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66,
	0x48, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x62, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61,
	0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x48,
	0x03, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65, 0x66,
	0x48, 0x04, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01,
	0x01, 0x12, 0x53, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x48, 0x05, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x42, 0x17,
	0x0a, 0x15, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x61, 0x79, 0x72,
	0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x23, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x9f, 0x05, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x51, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x60, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x44, 0x65, 0x66, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x12, 0x65, 0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x5d, 0x0a, 0x0f, 0x70, 0x61, 0x79,
	0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c,
	0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd4, 0x06, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a,
	0x10, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x01,
	0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65,
	0x66, 0x48, 0x02, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x03, 0x52, 0x13, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01,
	0x01, 0x12, 0x62, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50,
	0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x48, 0x04, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x44, 0x65,
	0x66, 0x48, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88,
	0x01, 0x01, 0x12, 0x53, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x48, 0x06, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70,
	0x61, 0x79, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x2d, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x2c, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2d, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xce, 0x01, 0x0a, 0x20, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31,
	0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x73, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x0e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x20, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x23, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xdb, 0x01, 0x0a,
	0x21, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x58, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x73, 0x22, 0xdd, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x78, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0x7c,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x13, 0x63,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f,
	0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x10, 0x63, 0x6c, 0x6f, 0x63,
	0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x62, 0x0a, 0x2d,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31, 0x0a,
	0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73,
	0x22, 0x89, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x3d, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0a, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x1a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x24, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x74, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x4e, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x77, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0xaf, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x11, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x73, 0x1a, 0x73, 0x0a, 0x0e, 0x52, 0x6f, 0x6c, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x52, 0x0a, 0x26, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xbe,
	0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x67, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x2b, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x85, 0x01, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x6c, 0x0a, 0x11,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xce, 0x06, 0x0a, 0x0d, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x7f, 0x0a, 0x19, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x53,
	0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x61, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x73, 0x6c, 0x6f, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d,
	0x61, 0x70, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73,
	0x6c, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x58,
	0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x7f, 0x0a, 0x19, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61,
	0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53,
	0x75, 0x6e, 0x64, 0x61, 0x79, 0x1a, 0x7c, 0x0a, 0x1b, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x7c, 0x0a, 0x1b, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x7e, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x60, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0xf8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xda, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x67, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xff, 0x02, 0x0a, 0x15, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x51, 0x0a, 0x05, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x77,
	0x65, 0x65, 0x6b, 0x73, 0x1a, 0x65, 0x0a, 0x0a, 0x57, 0x65, 0x65, 0x6b, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x65, 0x6b, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc9, 0x01, 0x0a, 0x10,
	0x57, 0x65, 0x65, 0x6b, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x49, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x61, 0x79, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x1a, 0x6a, 0x0a, 0x09, 0x44,
	0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x94, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb3,
	0x03, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x6c, 0x0a, 0x12, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x10, 0x64, 0x52, 0x10, 0x73, 0x6c, 0x6f, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x6c, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x10, 0x64, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x44, 0x61, 0x79, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x80, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x25, 0x53, 0x6c, 0x6f,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79,
	0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x4b, 0x0a, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x12,
	0x4b, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x61, 0x79, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0xc1, 0x01, 0x0a,
	0x25, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x44, 0x61, 0x79, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x07, 0x6f, 0x6e, 0x67, 0x6f, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x07, 0x6f, 0x6e, 0x67, 0x6f,
	0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x22, 0x88, 0x04, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61,
	0x70, 0x12, 0x75, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x1a, 0x7d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x56, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e,
	0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x81, 0x01, 0x0a, 0x11, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x56, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4f, 0x6e,
	0x67, 0x6f, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xaa, 0x02, 0x0a, 0x25,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f,
	0x64, 0x61, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92,
	0x01, 0x04, 0x08, 0x01, 0x18, 0x01, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x44, 0x61, 0x79, 0x73, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x27, 0x0a, 0x25, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0xd9, 0x02, 0x0a, 0x1b, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x34,
	0x0a, 0x0f, 0x65, 0x6e, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x1d, 0x0a,
	0x1b, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x6d, 0x0a, 0x1a,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0x67, 0x0a, 0x1a, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x04, 0x64, 0x65, 0x66,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x04,
	0x64, 0x65, 0x66, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x53, 0x0a, 0x04, 0x64, 0x65, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f,
	0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x04,
	0x64, 0x65, 0x66, 0x73, 0x22, 0x1e, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c,
	0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x32, 0xa4, 0x1b, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x69, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x75, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x69, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x99, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9f, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x3e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0xbd, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x48, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x7b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84,
	0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9c, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42,
	0x79, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xaa, 0x01, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xb3, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x86,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8f, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x1f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xa7, 0x01, 0x0a,
	0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x87, 0x01,
	0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8a,
	0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74,
	0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x21,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_organization_v1_staff_api_proto_rawDescOnce sync.Once
	file_moego_api_organization_v1_staff_api_proto_rawDescData = file_moego_api_organization_v1_staff_api_proto_rawDesc
)

func file_moego_api_organization_v1_staff_api_proto_rawDescGZIP() []byte {
	file_moego_api_organization_v1_staff_api_proto_rawDescOnce.Do(func() {
		file_moego_api_organization_v1_staff_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_organization_v1_staff_api_proto_rawDescData)
	})
	return file_moego_api_organization_v1_staff_api_proto_rawDescData
}

var file_moego_api_organization_v1_staff_api_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_moego_api_organization_v1_staff_api_proto_goTypes = []interface{}{
	(*CreateStaffParams)(nil),                             // 0: moego.api.organization.v1.CreateStaffParams
	(*CreateStaffResult)(nil),                             // 1: moego.api.organization.v1.CreateStaffResult
	(*GetStaffFullDetailParams)(nil),                      // 2: moego.api.organization.v1.GetStaffFullDetailParams
	(*GetStaffFullDetailResult)(nil),                      // 3: moego.api.organization.v1.GetStaffFullDetailResult
	(*UpdateStaffParams)(nil),                             // 4: moego.api.organization.v1.UpdateStaffParams
	(*UpdateStaffResult)(nil),                             // 5: moego.api.organization.v1.UpdateStaffResult
	(*DeleteStaffParams)(nil),                             // 6: moego.api.organization.v1.DeleteStaffParams
	(*DeleteStaffResult)(nil),                             // 7: moego.api.organization.v1.DeleteStaffResult
	(*QueryStaffListByPaginationParams)(nil),              // 8: moego.api.organization.v1.QueryStaffListByPaginationParams
	(*QueryStaffListByPaginationResult)(nil),              // 9: moego.api.organization.v1.QueryStaffListByPaginationResult
	(*GetAllWorkingLocationStaffsParams)(nil),             // 10: moego.api.organization.v1.GetAllWorkingLocationStaffsParams
	(*GetAllWorkingLocationStaffsResult)(nil),             // 11: moego.api.organization.v1.GetAllWorkingLocationStaffsResult
	(*GetStaffsByWorkingLocationIdsParams)(nil),           // 12: moego.api.organization.v1.GetStaffsByWorkingLocationIdsParams
	(*GetStaffsByWorkingLocationIdsResult)(nil),           // 13: moego.api.organization.v1.GetStaffsByWorkingLocationIdsResult
	(*GetClockInOutStaffsParams)(nil),                     // 14: moego.api.organization.v1.GetClockInOutStaffsParams
	(*GetClockInOutStaffsResult)(nil),                     // 15: moego.api.organization.v1.GetClockInOutStaffsResult
	(*GetEnterpriseStaffsByWorkingLocationIdsParams)(nil), // 16: moego.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsParams
	(*GetEnterpriseStaffsByWorkingLocationIdsResult)(nil), // 17: moego.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResult
	(*GetStaffLoginTimeParams)(nil),                       // 18: moego.api.organization.v1.GetStaffLoginTimeParams
	(*GetStaffLoginTimeResult)(nil),                       // 19: moego.api.organization.v1.GetStaffLoginTimeResult
	(*UpdateStaffLoginTimeParams)(nil),                    // 20: moego.api.organization.v1.UpdateStaffLoginTimeParams
	(*UpdateStaffLoginTimeResult)(nil),                    // 21: moego.api.organization.v1.UpdateStaffLoginTimeResult
	(*GetRecommendedStaffLoginTimeParams)(nil),            // 22: moego.api.organization.v1.GetRecommendedStaffLoginTimeParams
	(*GetRecommendedStaffLoginTimeResult)(nil),            // 23: moego.api.organization.v1.GetRecommendedStaffLoginTimeResult
	(*ListStaffGroupByRoleParams)(nil),                    // 24: moego.api.organization.v1.ListStaffGroupByRoleParams
	(*ListStaffGroupByRoleResult)(nil),                    // 25: moego.api.organization.v1.ListStaffGroupByRoleResult
	(*GetBusinessStaffAvailabilityTypeParams)(nil),        // 26: moego.api.organization.v1.GetBusinessStaffAvailabilityTypeParams
	(*UpdateBusinessStaffAvailabilityTypeParams)(nil),     // 27: moego.api.organization.v1.UpdateBusinessStaffAvailabilityTypeParams
	(*UpdateBusinessStaffAvailabilityTypeResult)(nil),     // 28: moego.api.organization.v1.UpdateBusinessStaffAvailabilityTypeResult
	(*GetBusinessStaffAvailabilityTypeResult)(nil),        // 29: moego.api.organization.v1.GetBusinessStaffAvailabilityTypeResult
	(*GetStaffCalenderViewParams)(nil),                    // 30: moego.api.organization.v1.GetStaffCalenderViewParams
	(*CalenderStaff)(nil),                                 // 31: moego.api.organization.v1.CalenderStaff
	(*GetStaffCalenderViewResult)(nil),                    // 32: moego.api.organization.v1.GetStaffCalenderViewResult
	(*GetStaffAvailabilityParams)(nil),                    // 33: moego.api.organization.v1.GetStaffAvailabilityParams
	(*GetStaffAvailabilityResult)(nil),                    // 34: moego.api.organization.v1.GetStaffAvailabilityResult
	(*StaffAvailabilityInfo)(nil),                         // 35: moego.api.organization.v1.StaffAvailabilityInfo
	(*WeekAvailability)(nil),                              // 36: moego.api.organization.v1.WeekAvailability
	(*UpdateStaffAvailabilityParams)(nil),                 // 37: moego.api.organization.v1.UpdateStaffAvailabilityParams
	(*UpdateStaffAvailabilityOverrideParams)(nil),         // 38: moego.api.organization.v1.UpdateStaffAvailabilityOverrideParams
	(*UpdateStaffAvailabilityResult)(nil),                 // 39: moego.api.organization.v1.UpdateStaffAvailabilityResult
	(*GetStaffAvailabilityOverrideParams)(nil),            // 40: moego.api.organization.v1.GetStaffAvailabilityOverrideParams
	(*SlotAvailabilityDayOngoingHistoryList)(nil),         // 41: moego.api.organization.v1.SlotAvailabilityDayOngoingHistoryList
	(*TimeAvailabilityDayOngoingHistoryList)(nil),         // 42: moego.api.organization.v1.TimeAvailabilityDayOngoingHistoryList
	(*GetStaffAvailabilityOverrideResult)(nil),            // 43: moego.api.organization.v1.GetStaffAvailabilityOverrideResult
	(*DeleteStaffAvailabilityOverrideParams)(nil),         // 44: moego.api.organization.v1.DeleteStaffAvailabilityOverrideParams
	(*DeleteStaffAvailabilityOverrideResult)(nil),         // 45: moego.api.organization.v1.DeleteStaffAvailabilityOverrideResult
	(*InitStaffAvailabilityParams)(nil),                   // 46: moego.api.organization.v1.InitStaffAvailabilityParams
	(*InitStaffAvailabilityResult)(nil),                   // 47: moego.api.organization.v1.InitStaffAvailabilityResult
	(*ListSlotFreeServicesParams)(nil),                    // 48: moego.api.organization.v1.ListSlotFreeServicesParams
	(*ListSlotFreeServicesResult)(nil),                    // 49: moego.api.organization.v1.ListSlotFreeServicesResult
	(*UpdateSlotFreeServicesParams)(nil),                  // 50: moego.api.organization.v1.UpdateSlotFreeServicesParams
	(*UpdateSlotFreeServicesResult)(nil),                  // 51: moego.api.organization.v1.UpdateSlotFreeServicesResult
	(*ListStaffGroupByRoleResult_RoleStaffGroup)(nil),     // 52: moego.api.organization.v1.ListStaffGroupByRoleResult.RoleStaffGroup
	nil,                                // 53: moego.api.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry
	nil,                                // 54: moego.api.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry
	nil,                                // 55: moego.api.organization.v1.StaffAvailabilityInfo.WeeksEntry
	nil,                                // 56: moego.api.organization.v1.WeekAvailability.DaysEntry
	nil,                                // 57: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.StaffMapEntry
	nil,                                // 58: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.TimeStaffMapEntry
	(*v1.CreateStaffDef)(nil),          // 59: moego.models.organization.v1.CreateStaffDef
	(*v1.StaffWorkingLocationDef)(nil), // 60: moego.models.organization.v1.StaffWorkingLocationDef
	(*v1.StaffAccessControlDef)(nil),   // 61: moego.models.organization.v1.StaffAccessControlDef
	(*v1.StaffNotificationDef)(nil),    // 62: moego.models.organization.v1.StaffNotificationDef
	(*v1.StaffPayrollSettingDef)(nil),  // 63: moego.models.organization.v1.StaffPayrollSettingDef
	(*v1.SendInviteLinkParamsDef)(nil), // 64: moego.models.organization.v1.SendInviteLinkParamsDef
	(*v1.StaffLoginTimeDef)(nil),       // 65: moego.models.organization.v1.StaffLoginTimeDef
	(*v1.StaffBasicView)(nil),          // 66: moego.models.organization.v1.StaffBasicView
	(*v1.StaffEmailDef)(nil),           // 67: moego.models.organization.v1.StaffEmailDef
	(*v1.UpdateStaffDef)(nil),          // 68: moego.models.organization.v1.UpdateStaffDef
	(*v2.OrderBy)(nil),                 // 69: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),       // 70: moego.utils.v2.PaginationRequest
	(*v1.StaffInfoDef)(nil),            // 71: moego.models.organization.v1.StaffInfoDef
	(*v2.PaginationResponse)(nil),      // 72: moego.utils.v2.PaginationResponse
	(*v1.LocationStaffsDef)(nil),       // 73: moego.models.organization.v1.LocationStaffsDef
	(*v1.ClockInOutStaffDef)(nil),      // 74: moego.models.organization.v1.ClockInOutStaffDef
	(*v1.StaffLoginTimeModel)(nil),     // 75: moego.models.organization.v1.StaffLoginTimeModel
	(*date.Date)(nil),                  // 76: google.type.Date
	(v1.AvailabilityType)(0),           // 77: moego.models.organization.v1.AvailabilityType
	(v1.ScheduleType)(0),               // 78: moego.models.organization.v1.ScheduleType
	(*v1.StaffAvailability)(nil),       // 79: moego.models.organization.v1.StaffAvailability
	(*v1.StaffAvailabilityDef)(nil),    // 80: moego.models.organization.v1.StaffAvailabilityDef
	(*v1.SlotAvailabilityDay)(nil),     // 81: moego.models.organization.v1.SlotAvailabilityDay
	(*v1.SlotAvailabilityDayDef)(nil),  // 82: moego.models.organization.v1.SlotAvailabilityDayDef
	(*v1.TimeAvailabilityDayDef)(nil),  // 83: moego.models.organization.v1.TimeAvailabilityDayDef
	(*v1.TimeAvailabilityDay)(nil),     // 84: moego.models.organization.v1.TimeAvailabilityDay
	(*v1.SlotFreeStaffServiceDef)(nil), // 85: moego.models.organization.v1.SlotFreeStaffServiceDef
}
var file_moego_api_organization_v1_staff_api_proto_depIdxs = []int32{
	59, // 0: moego.api.organization.v1.CreateStaffParams.staff_profile:type_name -> moego.models.organization.v1.CreateStaffDef
	60, // 1: moego.api.organization.v1.CreateStaffParams.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	61, // 2: moego.api.organization.v1.CreateStaffParams.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	62, // 3: moego.api.organization.v1.CreateStaffParams.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	63, // 4: moego.api.organization.v1.CreateStaffParams.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	64, // 5: moego.api.organization.v1.CreateStaffParams.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	65, // 6: moego.api.organization.v1.CreateStaffParams.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	66, // 7: moego.api.organization.v1.GetStaffFullDetailResult.staff_profile:type_name -> moego.models.organization.v1.StaffBasicView
	60, // 8: moego.api.organization.v1.GetStaffFullDetailResult.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	61, // 9: moego.api.organization.v1.GetStaffFullDetailResult.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	62, // 10: moego.api.organization.v1.GetStaffFullDetailResult.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	63, // 11: moego.api.organization.v1.GetStaffFullDetailResult.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	67, // 12: moego.api.organization.v1.GetStaffFullDetailResult.staff_email:type_name -> moego.models.organization.v1.StaffEmailDef
	65, // 13: moego.api.organization.v1.GetStaffFullDetailResult.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	68, // 14: moego.api.organization.v1.UpdateStaffParams.staff_profile:type_name -> moego.models.organization.v1.UpdateStaffDef
	60, // 15: moego.api.organization.v1.UpdateStaffParams.working_location:type_name -> moego.models.organization.v1.StaffWorkingLocationDef
	61, // 16: moego.api.organization.v1.UpdateStaffParams.access_control:type_name -> moego.models.organization.v1.StaffAccessControlDef
	62, // 17: moego.api.organization.v1.UpdateStaffParams.notification_setting:type_name -> moego.models.organization.v1.StaffNotificationDef
	63, // 18: moego.api.organization.v1.UpdateStaffParams.payroll_setting:type_name -> moego.models.organization.v1.StaffPayrollSettingDef
	64, // 19: moego.api.organization.v1.UpdateStaffParams.invite_link:type_name -> moego.models.organization.v1.SendInviteLinkParamsDef
	65, // 20: moego.api.organization.v1.UpdateStaffParams.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	69, // 21: moego.api.organization.v1.QueryStaffListByPaginationParams.order_bys:type_name -> moego.utils.v2.OrderBy
	70, // 22: moego.api.organization.v1.QueryStaffListByPaginationParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	71, // 23: moego.api.organization.v1.QueryStaffListByPaginationResult.staffs:type_name -> moego.models.organization.v1.StaffInfoDef
	72, // 24: moego.api.organization.v1.QueryStaffListByPaginationResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	73, // 25: moego.api.organization.v1.GetAllWorkingLocationStaffsResult.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	73, // 26: moego.api.organization.v1.GetStaffsByWorkingLocationIdsResult.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	74, // 27: moego.api.organization.v1.GetClockInOutStaffsResult.clock_in_out_staffs:type_name -> moego.models.organization.v1.ClockInOutStaffDef
	73, // 28: moego.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResult.location_staffs:type_name -> moego.models.organization.v1.LocationStaffsDef
	75, // 29: moego.api.organization.v1.GetStaffLoginTimeResult.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeModel
	65, // 30: moego.api.organization.v1.UpdateStaffLoginTimeParams.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	65, // 31: moego.api.organization.v1.GetRecommendedStaffLoginTimeResult.login_time:type_name -> moego.models.organization.v1.StaffLoginTimeDef
	76, // 32: moego.api.organization.v1.ListStaffGroupByRoleParams.date:type_name -> google.type.Date
	52, // 33: moego.api.organization.v1.ListStaffGroupByRoleResult.role_staff_groups:type_name -> moego.api.organization.v1.ListStaffGroupByRoleResult.RoleStaffGroup
	77, // 34: moego.api.organization.v1.UpdateBusinessStaffAvailabilityTypeParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	77, // 35: moego.api.organization.v1.GetBusinessStaffAvailabilityTypeResult.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	77, // 36: moego.api.organization.v1.GetStaffCalenderViewParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	78, // 37: moego.api.organization.v1.CalenderStaff.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	53, // 38: moego.api.organization.v1.CalenderStaff.slot_availability_day_map:type_name -> moego.api.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry
	78, // 39: moego.api.organization.v1.CalenderStaff.time_schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	54, // 40: moego.api.organization.v1.CalenderStaff.time_availability_day_map:type_name -> moego.api.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry
	31, // 41: moego.api.organization.v1.GetStaffCalenderViewResult.staff_availability_list:type_name -> moego.api.organization.v1.CalenderStaff
	77, // 42: moego.api.organization.v1.GetStaffAvailabilityParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	35, // 43: moego.api.organization.v1.GetStaffAvailabilityResult.staff_list:type_name -> moego.api.organization.v1.StaffAvailabilityInfo
	79, // 44: moego.api.organization.v1.GetStaffAvailabilityResult.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailability
	78, // 45: moego.api.organization.v1.StaffAvailabilityInfo.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	55, // 46: moego.api.organization.v1.StaffAvailabilityInfo.weeks:type_name -> moego.api.organization.v1.StaffAvailabilityInfo.WeeksEntry
	56, // 47: moego.api.organization.v1.WeekAvailability.days:type_name -> moego.api.organization.v1.WeekAvailability.DaysEntry
	35, // 48: moego.api.organization.v1.UpdateStaffAvailabilityParams.staff_list:type_name -> moego.api.organization.v1.StaffAvailabilityInfo
	80, // 49: moego.api.organization.v1.UpdateStaffAvailabilityParams.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailabilityDef
	81, // 50: moego.api.organization.v1.UpdateStaffAvailabilityOverrideParams.override_days:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	82, // 51: moego.api.organization.v1.UpdateStaffAvailabilityOverrideParams.slot_override_days:type_name -> moego.models.organization.v1.SlotAvailabilityDayDef
	83, // 52: moego.api.organization.v1.UpdateStaffAvailabilityOverrideParams.time_override_days:type_name -> moego.models.organization.v1.TimeAvailabilityDayDef
	77, // 53: moego.api.organization.v1.GetStaffAvailabilityOverrideParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	81, // 54: moego.api.organization.v1.SlotAvailabilityDayOngoingHistoryList.ongoing:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	81, // 55: moego.api.organization.v1.SlotAvailabilityDayOngoingHistoryList.history:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	84, // 56: moego.api.organization.v1.TimeAvailabilityDayOngoingHistoryList.ongoing:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	84, // 57: moego.api.organization.v1.TimeAvailabilityDayOngoingHistoryList.history:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	57, // 58: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.staff_map:type_name -> moego.api.organization.v1.GetStaffAvailabilityOverrideResult.StaffMapEntry
	58, // 59: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.time_staff_map:type_name -> moego.api.organization.v1.GetStaffAvailabilityOverrideResult.TimeStaffMapEntry
	77, // 60: moego.api.organization.v1.DeleteStaffAvailabilityOverrideParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	85, // 61: moego.api.organization.v1.ListSlotFreeServicesResult.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	85, // 62: moego.api.organization.v1.UpdateSlotFreeServicesParams.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	66, // 63: moego.api.organization.v1.ListStaffGroupByRoleResult.RoleStaffGroup.staffs:type_name -> moego.models.organization.v1.StaffBasicView
	81, // 64: moego.api.organization.v1.CalenderStaff.SlotAvailabilityDayMapEntry.value:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	84, // 65: moego.api.organization.v1.CalenderStaff.TimeAvailabilityDayMapEntry.value:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	36, // 66: moego.api.organization.v1.StaffAvailabilityInfo.WeeksEntry.value:type_name -> moego.api.organization.v1.WeekAvailability
	81, // 67: moego.api.organization.v1.WeekAvailability.DaysEntry.value:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	41, // 68: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.StaffMapEntry.value:type_name -> moego.api.organization.v1.SlotAvailabilityDayOngoingHistoryList
	42, // 69: moego.api.organization.v1.GetStaffAvailabilityOverrideResult.TimeStaffMapEntry.value:type_name -> moego.api.organization.v1.TimeAvailabilityDayOngoingHistoryList
	0,  // 70: moego.api.organization.v1.StaffService.CreateStaff:input_type -> moego.api.organization.v1.CreateStaffParams
	2,  // 71: moego.api.organization.v1.StaffService.GetStaffFullDetail:input_type -> moego.api.organization.v1.GetStaffFullDetailParams
	4,  // 72: moego.api.organization.v1.StaffService.UpdateStaff:input_type -> moego.api.organization.v1.UpdateStaffParams
	6,  // 73: moego.api.organization.v1.StaffService.DeleteStaff:input_type -> moego.api.organization.v1.DeleteStaffParams
	8,  // 74: moego.api.organization.v1.StaffService.QueryStaffListByPagination:input_type -> moego.api.organization.v1.QueryStaffListByPaginationParams
	10, // 75: moego.api.organization.v1.StaffService.GetAllWorkingLocationStaffs:input_type -> moego.api.organization.v1.GetAllWorkingLocationStaffsParams
	12, // 76: moego.api.organization.v1.StaffService.GetStaffsByWorkingLocationIds:input_type -> moego.api.organization.v1.GetStaffsByWorkingLocationIdsParams
	14, // 77: moego.api.organization.v1.StaffService.GetClockInOutStaffs:input_type -> moego.api.organization.v1.GetClockInOutStaffsParams
	16, // 78: moego.api.organization.v1.StaffService.GetEnterpriseStaffsByWorkingLocationIds:input_type -> moego.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsParams
	18, // 79: moego.api.organization.v1.StaffService.GetStaffLoginTime:input_type -> moego.api.organization.v1.GetStaffLoginTimeParams
	20, // 80: moego.api.organization.v1.StaffService.UpdateStaffLoginTime:input_type -> moego.api.organization.v1.UpdateStaffLoginTimeParams
	22, // 81: moego.api.organization.v1.StaffService.GetRecommendedStaffLoginTime:input_type -> moego.api.organization.v1.GetRecommendedStaffLoginTimeParams
	24, // 82: moego.api.organization.v1.StaffService.ListStaffGroupByRole:input_type -> moego.api.organization.v1.ListStaffGroupByRoleParams
	26, // 83: moego.api.organization.v1.StaffService.GetBusinessStaffAvailabilityType:input_type -> moego.api.organization.v1.GetBusinessStaffAvailabilityTypeParams
	27, // 84: moego.api.organization.v1.StaffService.UpdateBusinessStaffAvailabilityType:input_type -> moego.api.organization.v1.UpdateBusinessStaffAvailabilityTypeParams
	33, // 85: moego.api.organization.v1.StaffService.GetStaffAvailability:input_type -> moego.api.organization.v1.GetStaffAvailabilityParams
	37, // 86: moego.api.organization.v1.StaffService.UpdateStaffAvailability:input_type -> moego.api.organization.v1.UpdateStaffAvailabilityParams
	38, // 87: moego.api.organization.v1.StaffService.UpdateStaffAvailabilityOverride:input_type -> moego.api.organization.v1.UpdateStaffAvailabilityOverrideParams
	40, // 88: moego.api.organization.v1.StaffService.GetStaffAvailabilityOverride:input_type -> moego.api.organization.v1.GetStaffAvailabilityOverrideParams
	44, // 89: moego.api.organization.v1.StaffService.DeleteStaffAvailabilityOverride:input_type -> moego.api.organization.v1.DeleteStaffAvailabilityOverrideParams
	30, // 90: moego.api.organization.v1.StaffService.GetStaffCalenderView:input_type -> moego.api.organization.v1.GetStaffCalenderViewParams
	46, // 91: moego.api.organization.v1.StaffService.InitStaffAvailability:input_type -> moego.api.organization.v1.InitStaffAvailabilityParams
	48, // 92: moego.api.organization.v1.StaffService.ListSlotFreeServices:input_type -> moego.api.organization.v1.ListSlotFreeServicesParams
	50, // 93: moego.api.organization.v1.StaffService.UpdateSlotFreeServices:input_type -> moego.api.organization.v1.UpdateSlotFreeServicesParams
	1,  // 94: moego.api.organization.v1.StaffService.CreateStaff:output_type -> moego.api.organization.v1.CreateStaffResult
	3,  // 95: moego.api.organization.v1.StaffService.GetStaffFullDetail:output_type -> moego.api.organization.v1.GetStaffFullDetailResult
	5,  // 96: moego.api.organization.v1.StaffService.UpdateStaff:output_type -> moego.api.organization.v1.UpdateStaffResult
	7,  // 97: moego.api.organization.v1.StaffService.DeleteStaff:output_type -> moego.api.organization.v1.DeleteStaffResult
	9,  // 98: moego.api.organization.v1.StaffService.QueryStaffListByPagination:output_type -> moego.api.organization.v1.QueryStaffListByPaginationResult
	11, // 99: moego.api.organization.v1.StaffService.GetAllWorkingLocationStaffs:output_type -> moego.api.organization.v1.GetAllWorkingLocationStaffsResult
	13, // 100: moego.api.organization.v1.StaffService.GetStaffsByWorkingLocationIds:output_type -> moego.api.organization.v1.GetStaffsByWorkingLocationIdsResult
	15, // 101: moego.api.organization.v1.StaffService.GetClockInOutStaffs:output_type -> moego.api.organization.v1.GetClockInOutStaffsResult
	17, // 102: moego.api.organization.v1.StaffService.GetEnterpriseStaffsByWorkingLocationIds:output_type -> moego.api.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsResult
	19, // 103: moego.api.organization.v1.StaffService.GetStaffLoginTime:output_type -> moego.api.organization.v1.GetStaffLoginTimeResult
	21, // 104: moego.api.organization.v1.StaffService.UpdateStaffLoginTime:output_type -> moego.api.organization.v1.UpdateStaffLoginTimeResult
	23, // 105: moego.api.organization.v1.StaffService.GetRecommendedStaffLoginTime:output_type -> moego.api.organization.v1.GetRecommendedStaffLoginTimeResult
	25, // 106: moego.api.organization.v1.StaffService.ListStaffGroupByRole:output_type -> moego.api.organization.v1.ListStaffGroupByRoleResult
	29, // 107: moego.api.organization.v1.StaffService.GetBusinessStaffAvailabilityType:output_type -> moego.api.organization.v1.GetBusinessStaffAvailabilityTypeResult
	28, // 108: moego.api.organization.v1.StaffService.UpdateBusinessStaffAvailabilityType:output_type -> moego.api.organization.v1.UpdateBusinessStaffAvailabilityTypeResult
	34, // 109: moego.api.organization.v1.StaffService.GetStaffAvailability:output_type -> moego.api.organization.v1.GetStaffAvailabilityResult
	39, // 110: moego.api.organization.v1.StaffService.UpdateStaffAvailability:output_type -> moego.api.organization.v1.UpdateStaffAvailabilityResult
	39, // 111: moego.api.organization.v1.StaffService.UpdateStaffAvailabilityOverride:output_type -> moego.api.organization.v1.UpdateStaffAvailabilityResult
	43, // 112: moego.api.organization.v1.StaffService.GetStaffAvailabilityOverride:output_type -> moego.api.organization.v1.GetStaffAvailabilityOverrideResult
	45, // 113: moego.api.organization.v1.StaffService.DeleteStaffAvailabilityOverride:output_type -> moego.api.organization.v1.DeleteStaffAvailabilityOverrideResult
	32, // 114: moego.api.organization.v1.StaffService.GetStaffCalenderView:output_type -> moego.api.organization.v1.GetStaffCalenderViewResult
	47, // 115: moego.api.organization.v1.StaffService.InitStaffAvailability:output_type -> moego.api.organization.v1.InitStaffAvailabilityResult
	49, // 116: moego.api.organization.v1.StaffService.ListSlotFreeServices:output_type -> moego.api.organization.v1.ListSlotFreeServicesResult
	51, // 117: moego.api.organization.v1.StaffService.UpdateSlotFreeServices:output_type -> moego.api.organization.v1.UpdateSlotFreeServicesResult
	94, // [94:118] is the sub-list for method output_type
	70, // [70:94] is the sub-list for method input_type
	70, // [70:70] is the sub-list for extension type_name
	70, // [70:70] is the sub-list for extension extendee
	0,  // [0:70] is the sub-list for field type_name
}

func init() { file_moego_api_organization_v1_staff_api_proto_init() }
func file_moego_api_organization_v1_staff_api_proto_init() {
	if File_moego_api_organization_v1_staff_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_organization_v1_staff_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffFullDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffFullDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffListByPaginationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStaffListByPaginationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllWorkingLocationStaffsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllWorkingLocationStaffsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationIdsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffsByWorkingLocationIdsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutStaffsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutStaffsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByWorkingLocationIdsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseStaffsByWorkingLocationIdsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffLoginTimeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffLoginTimeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffLoginTimeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffLoginTimeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendedStaffLoginTimeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendedStaffLoginTimeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffGroupByRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffGroupByRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessStaffAvailabilityTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBusinessStaffAvailabilityTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBusinessStaffAvailabilityTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessStaffAvailabilityTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffCalenderViewParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalenderStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffCalenderViewResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAvailabilityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeekAvailability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotAvailabilityDayOngoingHistoryList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAvailabilityDayOngoingHistoryList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffAvailabilityOverrideParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStaffAvailabilityOverrideResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitStaffAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitStaffAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffGroupByRoleResult_RoleStaffGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[44].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_staff_api_proto_msgTypes[46].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_organization_v1_staff_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_organization_v1_staff_api_proto_goTypes,
		DependencyIndexes: file_moego_api_organization_v1_staff_api_proto_depIdxs,
		MessageInfos:      file_moego_api_organization_v1_staff_api_proto_msgTypes,
	}.Build()
	File_moego_api_organization_v1_staff_api_proto = out.File
	file_moego_api_organization_v1_staff_api_proto_rawDesc = nil
	file_moego_api_organization_v1_staff_api_proto_goTypes = nil
	file_moego_api_organization_v1_staff_api_proto_depIdxs = nil
}
