// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/promotion/v1/package_api.proto

package promotionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/package/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListPackagesRequest
type ListPackagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business IDs, empty = all
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *ListPackagesRequest) Reset() {
	*x = ListPackagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_package_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPackagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPackagesRequest) ProtoMessage() {}

func (x *ListPackagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_package_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPackagesRequest.ProtoReflect.Descriptor instead.
func (*ListPackagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_package_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPackagesRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// ListPackagesResponse
type ListPackagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// packages
	Packages []*v1.PackageModel `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`
}

func (x *ListPackagesResponse) Reset() {
	*x = ListPackagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_package_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPackagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPackagesResponse) ProtoMessage() {}

func (x *ListPackagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_package_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPackagesResponse.ProtoReflect.Descriptor instead.
func (*ListPackagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_package_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPackagesResponse) GetPackages() []*v1.PackageModel {
	if x != nil {
		return x.Packages
	}
	return nil
}

var File_moego_api_promotion_v1_package_api_proto protoreflect.FileDescriptor

var file_moego_api_promotion_v1_package_api_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x38, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x59, 0x0a, 0x14, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x73, 0x32, 0x7b, 0x0a, 0x0e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x69, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x7e, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_promotion_v1_package_api_proto_rawDescOnce sync.Once
	file_moego_api_promotion_v1_package_api_proto_rawDescData = file_moego_api_promotion_v1_package_api_proto_rawDesc
)

func file_moego_api_promotion_v1_package_api_proto_rawDescGZIP() []byte {
	file_moego_api_promotion_v1_package_api_proto_rawDescOnce.Do(func() {
		file_moego_api_promotion_v1_package_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_promotion_v1_package_api_proto_rawDescData)
	})
	return file_moego_api_promotion_v1_package_api_proto_rawDescData
}

var file_moego_api_promotion_v1_package_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_promotion_v1_package_api_proto_goTypes = []interface{}{
	(*ListPackagesRequest)(nil),  // 0: moego.api.promotion.v1.ListPackagesRequest
	(*ListPackagesResponse)(nil), // 1: moego.api.promotion.v1.ListPackagesResponse
	(*v1.PackageModel)(nil),      // 2: moego.models.package.v1.PackageModel
}
var file_moego_api_promotion_v1_package_api_proto_depIdxs = []int32{
	2, // 0: moego.api.promotion.v1.ListPackagesResponse.packages:type_name -> moego.models.package.v1.PackageModel
	0, // 1: moego.api.promotion.v1.PackageService.ListPackages:input_type -> moego.api.promotion.v1.ListPackagesRequest
	1, // 2: moego.api.promotion.v1.PackageService.ListPackages:output_type -> moego.api.promotion.v1.ListPackagesResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_promotion_v1_package_api_proto_init() }
func file_moego_api_promotion_v1_package_api_proto_init() {
	if File_moego_api_promotion_v1_package_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_promotion_v1_package_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPackagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_promotion_v1_package_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPackagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_promotion_v1_package_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_promotion_v1_package_api_proto_goTypes,
		DependencyIndexes: file_moego_api_promotion_v1_package_api_proto_depIdxs,
		MessageInfos:      file_moego_api_promotion_v1_package_api_proto_msgTypes,
	}.Build()
	File_moego_api_promotion_v1_package_api_proto = out.File
	file_moego_api_promotion_v1_package_api_proto_rawDesc = nil
	file_moego_api_promotion_v1_package_api_proto_goTypes = nil
	file_moego_api_promotion_v1_package_api_proto_depIdxs = nil
}
