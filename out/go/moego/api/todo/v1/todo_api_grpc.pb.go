// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/todo/v1/todo_api.proto

package todoapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/todo/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/universal/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TodoServiceClient is the client API for TodoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TodoServiceClient interface {
	// add todo
	AddTodo(ctx context.Context, in *AddTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// get single todo by id
	GetTodo(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// list all todos
	ListTodo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*v12.EntityListModel, error)
	// update single todo
	UpdateTodo(ctx context.Context, in *UpdateTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error)
	// delete single todo
	DeleteTodo(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// hello world
	HelloChanny(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloChannyServiceResponse, error)
	// newcomer hello world API by zhangdong
	HelloDong(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloDongServiceResponse, error)
	// hello world api by jett
	HelloJett(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloJettServiceResponse, error)
	// echo api by haozhi
	EchoHz(ctx context.Context, in *EchoHzParams, opts ...grpc.CallOption) (*EchoHzResult, error)
	// hello world api by ark
	HelloArk(ctx context.Context, in *HelloArkParams, opts ...grpc.CallOption) (*HelloArkResult, error)
	// hello world api by better
	HelloBetter(ctx context.Context, in *HelloBetterParams, opts ...grpc.CallOption) (*HelloBetterResult, error)
	// hello world api by perqin
	HelloPerqin(ctx context.Context, in *HelloPerqinParams, opts ...grpc.CallOption) (*HelloPerqinResult, error)
	// hello world api by yueyue
	HelloYueyue(ctx context.Context, in *HelloYueyueParams, opts ...grpc.CallOption) (*HelloYueyueResult, error)
	// hello world api by kai
	HelloKai(ctx context.Context, in *HelloKaiParams, opts ...grpc.CallOption) (*HelloKaiResult, error)
	// hello world api by kuroko
	HelloKuroko(ctx context.Context, in *HelloKurokoParams, opts ...grpc.CallOption) (*HelloKurokoResult, error)
	// hello world api by Bryson
	HelloBryson(ctx context.Context, in *HelloBrysonParams, opts ...grpc.CallOption) (*HelloBrysonResult, error)
	// hello world api by Harvie
	HelloHarvie(ctx context.Context, in *HelloHarvieParams, opts ...grpc.CallOption) (*HelloHarvieResult, error)
}

type todoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTodoServiceClient(cc grpc.ClientConnInterface) TodoServiceClient {
	return &todoServiceClient{cc}
}

func (c *todoServiceClient) AddTodo(ctx context.Context, in *AddTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/AddTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) GetTodo(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/GetTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) ListTodo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*v12.EntityListModel, error) {
	out := new(v12.EntityListModel)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/ListTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) UpdateTodo(ctx context.Context, in *UpdateTodoRequest, opts ...grpc.CallOption) (*v1.TodoModel, error) {
	out := new(v1.TodoModel)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/UpdateTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) DeleteTodo(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/DeleteTodo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloChanny(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloChannyServiceResponse, error) {
	out := new(HelloChannyServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloChanny", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloDong(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloDongServiceResponse, error) {
	out := new(HelloDongServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloDong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloJett(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HelloJettServiceResponse, error) {
	out := new(HelloJettServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloJett", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) EchoHz(ctx context.Context, in *EchoHzParams, opts ...grpc.CallOption) (*EchoHzResult, error) {
	out := new(EchoHzResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/EchoHz", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloArk(ctx context.Context, in *HelloArkParams, opts ...grpc.CallOption) (*HelloArkResult, error) {
	out := new(HelloArkResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloArk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloBetter(ctx context.Context, in *HelloBetterParams, opts ...grpc.CallOption) (*HelloBetterResult, error) {
	out := new(HelloBetterResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloBetter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloPerqin(ctx context.Context, in *HelloPerqinParams, opts ...grpc.CallOption) (*HelloPerqinResult, error) {
	out := new(HelloPerqinResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloPerqin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloYueyue(ctx context.Context, in *HelloYueyueParams, opts ...grpc.CallOption) (*HelloYueyueResult, error) {
	out := new(HelloYueyueResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloYueyue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloKai(ctx context.Context, in *HelloKaiParams, opts ...grpc.CallOption) (*HelloKaiResult, error) {
	out := new(HelloKaiResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloKai", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloKuroko(ctx context.Context, in *HelloKurokoParams, opts ...grpc.CallOption) (*HelloKurokoResult, error) {
	out := new(HelloKurokoResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloKuroko", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloBryson(ctx context.Context, in *HelloBrysonParams, opts ...grpc.CallOption) (*HelloBrysonResult, error) {
	out := new(HelloBrysonResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloBryson", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *todoServiceClient) HelloHarvie(ctx context.Context, in *HelloHarvieParams, opts ...grpc.CallOption) (*HelloHarvieResult, error) {
	out := new(HelloHarvieResult)
	err := c.cc.Invoke(ctx, "/moego.api.todo.v1.TodoService/HelloHarvie", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TodoServiceServer is the server API for TodoService service.
// All implementations must embed UnimplementedTodoServiceServer
// for forward compatibility
type TodoServiceServer interface {
	// add todo
	AddTodo(context.Context, *AddTodoRequest) (*v1.TodoModel, error)
	// get single todo by id
	GetTodo(context.Context, *v11.Id) (*v1.TodoModel, error)
	// list all todos
	ListTodo(context.Context, *emptypb.Empty) (*v12.EntityListModel, error)
	// update single todo
	UpdateTodo(context.Context, *UpdateTodoRequest) (*v1.TodoModel, error)
	// delete single todo
	DeleteTodo(context.Context, *v11.Id) (*emptypb.Empty, error)
	// hello world
	HelloChanny(context.Context, *emptypb.Empty) (*HelloChannyServiceResponse, error)
	// newcomer hello world API by zhangdong
	HelloDong(context.Context, *emptypb.Empty) (*HelloDongServiceResponse, error)
	// hello world api by jett
	HelloJett(context.Context, *emptypb.Empty) (*HelloJettServiceResponse, error)
	// echo api by haozhi
	EchoHz(context.Context, *EchoHzParams) (*EchoHzResult, error)
	// hello world api by ark
	HelloArk(context.Context, *HelloArkParams) (*HelloArkResult, error)
	// hello world api by better
	HelloBetter(context.Context, *HelloBetterParams) (*HelloBetterResult, error)
	// hello world api by perqin
	HelloPerqin(context.Context, *HelloPerqinParams) (*HelloPerqinResult, error)
	// hello world api by yueyue
	HelloYueyue(context.Context, *HelloYueyueParams) (*HelloYueyueResult, error)
	// hello world api by kai
	HelloKai(context.Context, *HelloKaiParams) (*HelloKaiResult, error)
	// hello world api by kuroko
	HelloKuroko(context.Context, *HelloKurokoParams) (*HelloKurokoResult, error)
	// hello world api by Bryson
	HelloBryson(context.Context, *HelloBrysonParams) (*HelloBrysonResult, error)
	// hello world api by Harvie
	HelloHarvie(context.Context, *HelloHarvieParams) (*HelloHarvieResult, error)
	mustEmbedUnimplementedTodoServiceServer()
}

// UnimplementedTodoServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTodoServiceServer struct {
}

func (UnimplementedTodoServiceServer) AddTodo(context.Context, *AddTodoRequest) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTodo not implemented")
}
func (UnimplementedTodoServiceServer) GetTodo(context.Context, *v11.Id) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTodo not implemented")
}
func (UnimplementedTodoServiceServer) ListTodo(context.Context, *emptypb.Empty) (*v12.EntityListModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTodo not implemented")
}
func (UnimplementedTodoServiceServer) UpdateTodo(context.Context, *UpdateTodoRequest) (*v1.TodoModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTodo not implemented")
}
func (UnimplementedTodoServiceServer) DeleteTodo(context.Context, *v11.Id) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTodo not implemented")
}
func (UnimplementedTodoServiceServer) HelloChanny(context.Context, *emptypb.Empty) (*HelloChannyServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloChanny not implemented")
}
func (UnimplementedTodoServiceServer) HelloDong(context.Context, *emptypb.Empty) (*HelloDongServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloDong not implemented")
}
func (UnimplementedTodoServiceServer) HelloJett(context.Context, *emptypb.Empty) (*HelloJettServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloJett not implemented")
}
func (UnimplementedTodoServiceServer) EchoHz(context.Context, *EchoHzParams) (*EchoHzResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EchoHz not implemented")
}
func (UnimplementedTodoServiceServer) HelloArk(context.Context, *HelloArkParams) (*HelloArkResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloArk not implemented")
}
func (UnimplementedTodoServiceServer) HelloBetter(context.Context, *HelloBetterParams) (*HelloBetterResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloBetter not implemented")
}
func (UnimplementedTodoServiceServer) HelloPerqin(context.Context, *HelloPerqinParams) (*HelloPerqinResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloPerqin not implemented")
}
func (UnimplementedTodoServiceServer) HelloYueyue(context.Context, *HelloYueyueParams) (*HelloYueyueResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloYueyue not implemented")
}
func (UnimplementedTodoServiceServer) HelloKai(context.Context, *HelloKaiParams) (*HelloKaiResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloKai not implemented")
}
func (UnimplementedTodoServiceServer) HelloKuroko(context.Context, *HelloKurokoParams) (*HelloKurokoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloKuroko not implemented")
}
func (UnimplementedTodoServiceServer) HelloBryson(context.Context, *HelloBrysonParams) (*HelloBrysonResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloBryson not implemented")
}
func (UnimplementedTodoServiceServer) HelloHarvie(context.Context, *HelloHarvieParams) (*HelloHarvieResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HelloHarvie not implemented")
}
func (UnimplementedTodoServiceServer) mustEmbedUnimplementedTodoServiceServer() {}

// UnsafeTodoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TodoServiceServer will
// result in compilation errors.
type UnsafeTodoServiceServer interface {
	mustEmbedUnimplementedTodoServiceServer()
}

func RegisterTodoServiceServer(s grpc.ServiceRegistrar, srv TodoServiceServer) {
	s.RegisterService(&TodoService_ServiceDesc, srv)
}

func _TodoService_AddTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTodoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).AddTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/AddTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).AddTodo(ctx, req.(*AddTodoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_GetTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).GetTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/GetTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).GetTodo(ctx, req.(*v11.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_ListTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).ListTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/ListTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).ListTodo(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_UpdateTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTodoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).UpdateTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/UpdateTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).UpdateTodo(ctx, req.(*UpdateTodoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_DeleteTodo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).DeleteTodo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/DeleteTodo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).DeleteTodo(ctx, req.(*v11.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloChanny_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloChanny(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloChanny",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloChanny(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloDong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloDong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloDong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloDong(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloJett_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloJett(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloJett",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloJett(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_EchoHz_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EchoHzParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).EchoHz(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/EchoHz",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).EchoHz(ctx, req.(*EchoHzParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloArk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloArkParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloArk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloArk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloArk(ctx, req.(*HelloArkParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloBetter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloBetterParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloBetter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloBetter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloBetter(ctx, req.(*HelloBetterParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloPerqin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloPerqinParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloPerqin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloPerqin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloPerqin(ctx, req.(*HelloPerqinParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloYueyue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloYueyueParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloYueyue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloYueyue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloYueyue(ctx, req.(*HelloYueyueParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloKai_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloKaiParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloKai(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloKai",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloKai(ctx, req.(*HelloKaiParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloKuroko_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloKurokoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloKuroko(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloKuroko",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloKuroko(ctx, req.(*HelloKurokoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloBryson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloBrysonParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloBryson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloBryson",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloBryson(ctx, req.(*HelloBrysonParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TodoService_HelloHarvie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloHarvieParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TodoServiceServer).HelloHarvie(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.todo.v1.TodoService/HelloHarvie",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TodoServiceServer).HelloHarvie(ctx, req.(*HelloHarvieParams))
	}
	return interceptor(ctx, in, info, handler)
}

// TodoService_ServiceDesc is the grpc.ServiceDesc for TodoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TodoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.todo.v1.TodoService",
	HandlerType: (*TodoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddTodo",
			Handler:    _TodoService_AddTodo_Handler,
		},
		{
			MethodName: "GetTodo",
			Handler:    _TodoService_GetTodo_Handler,
		},
		{
			MethodName: "ListTodo",
			Handler:    _TodoService_ListTodo_Handler,
		},
		{
			MethodName: "UpdateTodo",
			Handler:    _TodoService_UpdateTodo_Handler,
		},
		{
			MethodName: "DeleteTodo",
			Handler:    _TodoService_DeleteTodo_Handler,
		},
		{
			MethodName: "HelloChanny",
			Handler:    _TodoService_HelloChanny_Handler,
		},
		{
			MethodName: "HelloDong",
			Handler:    _TodoService_HelloDong_Handler,
		},
		{
			MethodName: "HelloJett",
			Handler:    _TodoService_HelloJett_Handler,
		},
		{
			MethodName: "EchoHz",
			Handler:    _TodoService_EchoHz_Handler,
		},
		{
			MethodName: "HelloArk",
			Handler:    _TodoService_HelloArk_Handler,
		},
		{
			MethodName: "HelloBetter",
			Handler:    _TodoService_HelloBetter_Handler,
		},
		{
			MethodName: "HelloPerqin",
			Handler:    _TodoService_HelloPerqin_Handler,
		},
		{
			MethodName: "HelloYueyue",
			Handler:    _TodoService_HelloYueyue_Handler,
		},
		{
			MethodName: "HelloKai",
			Handler:    _TodoService_HelloKai_Handler,
		},
		{
			MethodName: "HelloKuroko",
			Handler:    _TodoService_HelloKuroko_Handler,
		},
		{
			MethodName: "HelloBryson",
			Handler:    _TodoService_HelloBryson_Handler,
		},
		{
			MethodName: "HelloHarvie",
			Handler:    _TodoService_HelloHarvie_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/todo/v1/todo_api.proto",
}
