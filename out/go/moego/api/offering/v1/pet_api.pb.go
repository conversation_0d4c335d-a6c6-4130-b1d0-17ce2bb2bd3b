// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/pet_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// search pet for quick check-in params
type SearchPetForQuickCheckInParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// keyword, search by client or pet name
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,4,opt,name=date,proto3,oneof" json:"date,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *SearchPetForQuickCheckInParams) Reset() {
	*x = SearchPetForQuickCheckInParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForQuickCheckInParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForQuickCheckInParams) ProtoMessage() {}

func (x *SearchPetForQuickCheckInParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForQuickCheckInParams.ProtoReflect.Descriptor instead.
func (*SearchPetForQuickCheckInParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{0}
}

func (x *SearchPetForQuickCheckInParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *SearchPetForQuickCheckInParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchPetForQuickCheckInParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *SearchPetForQuickCheckInParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *SearchPetForQuickCheckInParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// search pet for quick check-in result
type SearchPetForQuickCheckInResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet client view list
	Pets []*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchPetForQuickCheckInResult) Reset() {
	*x = SearchPetForQuickCheckInResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForQuickCheckInResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForQuickCheckInResult) ProtoMessage() {}

func (x *SearchPetForQuickCheckInResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForQuickCheckInResult.ProtoReflect.Descriptor instead.
func (*SearchPetForQuickCheckInResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{1}
}

func (x *SearchPetForQuickCheckInResult) GetPets() []*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *SearchPetForQuickCheckInResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// search association information
type SearchAssociationInformationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer ids
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// pet ids
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,4,opt,name=date,proto3,oneof" json:"date,omitempty"`
}

func (x *SearchAssociationInformationParams) Reset() {
	*x = SearchAssociationInformationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssociationInformationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssociationInformationParams) ProtoMessage() {}

func (x *SearchAssociationInformationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssociationInformationParams.ProtoReflect.Descriptor instead.
func (*SearchAssociationInformationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{2}
}

func (x *SearchAssociationInformationParams) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchAssociationInformationParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *SearchAssociationInformationParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *SearchAssociationInformationParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

// SearchAssociationInformationResult
type SearchAssociationInformationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pets
	PetAppointments []*SearchAssociationInformationResult_HasAppointment `protobuf:"bytes,1,rep,name=pet_appointments,json=petAppointments,proto3" json:"pet_appointments,omitempty"`
	// client membership list
	ClientMembershipSubscriptions []*SearchAssociationInformationResult_ClientMemberShipSubscription `protobuf:"bytes,2,rep,name=client_membership_subscriptions,json=clientMembershipSubscriptions,proto3" json:"client_membership_subscriptions,omitempty"`
}

func (x *SearchAssociationInformationResult) Reset() {
	*x = SearchAssociationInformationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssociationInformationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssociationInformationResult) ProtoMessage() {}

func (x *SearchAssociationInformationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssociationInformationResult.ProtoReflect.Descriptor instead.
func (*SearchAssociationInformationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{3}
}

func (x *SearchAssociationInformationResult) GetPetAppointments() []*SearchAssociationInformationResult_HasAppointment {
	if x != nil {
		return x.PetAppointments
	}
	return nil
}

func (x *SearchAssociationInformationResult) GetClientMembershipSubscriptions() []*SearchAssociationInformationResult_ClientMemberShipSubscription {
	if x != nil {
		return x.ClientMembershipSubscriptions
	}
	return nil
}

// The params message for SearchEnrollmentInfo
type SearchEnrollmentInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The training group class instance id
	GroupClassInstanceId int64 `protobuf:"varint,2,opt,name=group_class_instance_id,json=groupClassInstanceId,proto3" json:"group_class_instance_id,omitempty"`
	// The enrolled pet ids or keyword and pagination
	//
	// Types that are assignable to EnrollmentType:
	//
	//	*SearchEnrollmentInfoParams_ByPetIds
	//	*SearchEnrollmentInfoParams_ByKeyword
	EnrollmentType isSearchEnrollmentInfoParams_EnrollmentType `protobuf_oneof:"enrollment_type"`
}

func (x *SearchEnrollmentInfoParams) Reset() {
	*x = SearchEnrollmentInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchEnrollmentInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchEnrollmentInfoParams) ProtoMessage() {}

func (x *SearchEnrollmentInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchEnrollmentInfoParams.ProtoReflect.Descriptor instead.
func (*SearchEnrollmentInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{4}
}

func (x *SearchEnrollmentInfoParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SearchEnrollmentInfoParams) GetGroupClassInstanceId() int64 {
	if x != nil {
		return x.GroupClassInstanceId
	}
	return 0
}

func (m *SearchEnrollmentInfoParams) GetEnrollmentType() isSearchEnrollmentInfoParams_EnrollmentType {
	if m != nil {
		return m.EnrollmentType
	}
	return nil
}

func (x *SearchEnrollmentInfoParams) GetByPetIds() *EnrollmentByPetIds {
	if x, ok := x.GetEnrollmentType().(*SearchEnrollmentInfoParams_ByPetIds); ok {
		return x.ByPetIds
	}
	return nil
}

func (x *SearchEnrollmentInfoParams) GetByKeyword() *EnrollmentByKeyword {
	if x, ok := x.GetEnrollmentType().(*SearchEnrollmentInfoParams_ByKeyword); ok {
		return x.ByKeyword
	}
	return nil
}

type isSearchEnrollmentInfoParams_EnrollmentType interface {
	isSearchEnrollmentInfoParams_EnrollmentType()
}

type SearchEnrollmentInfoParams_ByPetIds struct {
	// The enrolled pet ids
	ByPetIds *EnrollmentByPetIds `protobuf:"bytes,3,opt,name=by_pet_ids,json=byPetIds,proto3,oneof"`
}

type SearchEnrollmentInfoParams_ByKeyword struct {
	// The keyword and pagination for search
	ByKeyword *EnrollmentByKeyword `protobuf:"bytes,4,opt,name=by_keyword,json=byKeyword,proto3,oneof"`
}

func (*SearchEnrollmentInfoParams_ByPetIds) isSearchEnrollmentInfoParams_EnrollmentType() {}

func (*SearchEnrollmentInfoParams_ByKeyword) isSearchEnrollmentInfoParams_EnrollmentType() {}

// The enrollment pet ids
type EnrollmentByPetIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The enrolled pet ids
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *EnrollmentByPetIds) Reset() {
	*x = EnrollmentByPetIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollmentByPetIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollmentByPetIds) ProtoMessage() {}

func (x *EnrollmentByPetIds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollmentByPetIds.ProtoReflect.Descriptor instead.
func (*EnrollmentByPetIds) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{5}
}

func (x *EnrollmentByPetIds) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// The keyword and pagination message for SearchEnrollmentInfo
type EnrollmentByKeyword struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The keyword, search by client or pet name
	Keyword *string `protobuf:"bytes,1,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// The pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *EnrollmentByKeyword) Reset() {
	*x = EnrollmentByKeyword{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollmentByKeyword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollmentByKeyword) ProtoMessage() {}

func (x *EnrollmentByKeyword) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollmentByKeyword.ProtoReflect.Descriptor instead.
func (*EnrollmentByKeyword) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{6}
}

func (x *EnrollmentByKeyword) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *EnrollmentByKeyword) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The result message for SearchEnrollmentInfo
type SearchEnrollmentInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet list
	Pets []*SearchEnrollmentInfoResult_EnrollmentPetView `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// The pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchEnrollmentInfoResult) Reset() {
	*x = SearchEnrollmentInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchEnrollmentInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchEnrollmentInfoResult) ProtoMessage() {}

func (x *SearchEnrollmentInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchEnrollmentInfoResult.ProtoReflect.Descriptor instead.
func (*SearchEnrollmentInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{7}
}

func (x *SearchEnrollmentInfoResult) GetPets() []*SearchEnrollmentInfoResult_EnrollmentPetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *SearchEnrollmentInfoResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The params message for SearchPetForGroupClassCheckIn
type SearchPetForGroupClassCheckInParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The group class session id
	GroupClassSessionId *int64 `protobuf:"varint,2,opt,name=group_class_session_id,json=groupClassSessionId,proto3,oneof" json:"group_class_session_id,omitempty"`
	// keyword, search by client or pet name
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchPetForGroupClassCheckInParams) Reset() {
	*x = SearchPetForGroupClassCheckInParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForGroupClassCheckInParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForGroupClassCheckInParams) ProtoMessage() {}

func (x *SearchPetForGroupClassCheckInParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForGroupClassCheckInParams.ProtoReflect.Descriptor instead.
func (*SearchPetForGroupClassCheckInParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{8}
}

func (x *SearchPetForGroupClassCheckInParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SearchPetForGroupClassCheckInParams) GetGroupClassSessionId() int64 {
	if x != nil && x.GroupClassSessionId != nil {
		return *x.GroupClassSessionId
	}
	return 0
}

func (x *SearchPetForGroupClassCheckInParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *SearchPetForGroupClassCheckInParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The result message for SearchPetForGroupClassCheckIn
type SearchPetForGroupClassCheckInResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet list
	Pets []*SearchPetForGroupClassCheckInResult_CheckInPetView `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// The pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchPetForGroupClassCheckInResult) Reset() {
	*x = SearchPetForGroupClassCheckInResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForGroupClassCheckInResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForGroupClassCheckInResult) ProtoMessage() {}

func (x *SearchPetForGroupClassCheckInResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForGroupClassCheckInResult.ProtoReflect.Descriptor instead.
func (*SearchPetForGroupClassCheckInResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{9}
}

func (x *SearchPetForGroupClassCheckInResult) GetPets() []*SearchPetForGroupClassCheckInResult_CheckInPetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *SearchPetForGroupClassCheckInResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// pet and client view
type SearchPetForQuickCheckInResult_PetViewForQuickCheckIn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *v1.BusinessCustomerPetCalendarView `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// client
	Client *v1.BusinessCustomerModelNameView `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	// whether the pet has appointment today
	HasAppointmentToday *bool `protobuf:"varint,3,opt,name=has_appointment_today,json=hasAppointmentToday,proto3,oneof" json:"has_appointment_today,omitempty"`
	// memberships
	MembershipSubscriptions *v11.MembershipSubscriptionListModel `protobuf:"bytes,4,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) Reset() {
	*x = SearchPetForQuickCheckInResult_PetViewForQuickCheckIn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) ProtoMessage() {}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForQuickCheckInResult_PetViewForQuickCheckIn.ProtoReflect.Descriptor instead.
func (*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) GetPet() *v1.BusinessCustomerPetCalendarView {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) GetClient() *v1.BusinessCustomerModelNameView {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) GetHasAppointmentToday() bool {
	if x != nil && x.HasAppointmentToday != nil {
		return *x.HasAppointmentToday
	}
	return false
}

func (x *SearchPetForQuickCheckInResult_PetViewForQuickCheckIn) GetMembershipSubscriptions() *v11.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// has appointment
type SearchAssociationInformationResult_HasAppointment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// whether the pet has appointment
	HasAppointment bool `protobuf:"varint,2,opt,name=has_appointment,json=hasAppointment,proto3" json:"has_appointment,omitempty"`
}

func (x *SearchAssociationInformationResult_HasAppointment) Reset() {
	*x = SearchAssociationInformationResult_HasAppointment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssociationInformationResult_HasAppointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssociationInformationResult_HasAppointment) ProtoMessage() {}

func (x *SearchAssociationInformationResult_HasAppointment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssociationInformationResult_HasAppointment.ProtoReflect.Descriptor instead.
func (*SearchAssociationInformationResult_HasAppointment) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *SearchAssociationInformationResult_HasAppointment) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *SearchAssociationInformationResult_HasAppointment) GetHasAppointment() bool {
	if x != nil {
		return x.HasAppointment
	}
	return false
}

// client membership subscription
type SearchAssociationInformationResult_ClientMemberShipSubscription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// memberships
	MembershipSubscriptions *v11.MembershipSubscriptionListModel `protobuf:"bytes,2,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
}

func (x *SearchAssociationInformationResult_ClientMemberShipSubscription) Reset() {
	*x = SearchAssociationInformationResult_ClientMemberShipSubscription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssociationInformationResult_ClientMemberShipSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssociationInformationResult_ClientMemberShipSubscription) ProtoMessage() {}

func (x *SearchAssociationInformationResult_ClientMemberShipSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssociationInformationResult_ClientMemberShipSubscription.ProtoReflect.Descriptor instead.
func (*SearchAssociationInformationResult_ClientMemberShipSubscription) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{3, 1}
}

func (x *SearchAssociationInformationResult_ClientMemberShipSubscription) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SearchAssociationInformationResult_ClientMemberShipSubscription) GetMembershipSubscriptions() *v11.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// The pet view for enrollment
type SearchEnrollmentInfoResult_EnrollmentPetView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet view
	Pet *GroupClassPetView `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// The client view
	Client *GroupClassClientView `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	// The not completed prerequisite class view
	NotCompletedPrerequisites []*v12.GroupClassModel `protobuf:"bytes,3,rep,name=not_completed_prerequisites,json=notCompletedPrerequisites,proto3" json:"not_completed_prerequisites,omitempty"`
}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) Reset() {
	*x = SearchEnrollmentInfoResult_EnrollmentPetView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchEnrollmentInfoResult_EnrollmentPetView) ProtoMessage() {}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchEnrollmentInfoResult_EnrollmentPetView.ProtoReflect.Descriptor instead.
func (*SearchEnrollmentInfoResult_EnrollmentPetView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{7, 0}
}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) GetPet() *GroupClassPetView {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) GetClient() *GroupClassClientView {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *SearchEnrollmentInfoResult_EnrollmentPetView) GetNotCompletedPrerequisites() []*v12.GroupClassModel {
	if x != nil {
		return x.NotCompletedPrerequisites
	}
	return nil
}

// The pet view for group class check-in
type SearchPetForGroupClassCheckInResult_CheckInPetView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet view
	Pet *GroupClassPetView `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// The client view
	Client *GroupClassClientView `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	// The group class instance view
	GroupClassInstance *GroupClassInstanceView `protobuf:"bytes,3,opt,name=group_class_instance,json=groupClassInstance,proto3" json:"group_class_instance,omitempty"`
}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) Reset() {
	*x = SearchPetForGroupClassCheckInResult_CheckInPetView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetForGroupClassCheckInResult_CheckInPetView) ProtoMessage() {}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pet_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetForGroupClassCheckInResult_CheckInPetView.ProtoReflect.Descriptor instead.
func (*SearchPetForGroupClassCheckInResult_CheckInPetView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pet_api_proto_rawDescGZIP(), []int{9, 0}
}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) GetPet() *GroupClassPetView {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) GetClient() *GroupClassClientView {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *SearchPetForGroupClassCheckInResult_CheckInPetView) GetGroupClassInstance() *GroupClassInstanceView {
	if x != nil {
		return x.GroupClassInstance
	}
	return nil
}

var File_moego_api_offering_v1_pet_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_pet_api_proto_rawDesc = []byte{
	0x0a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x02, 0x0a, 0x1e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x02, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xdc, 0x04, 0x0a, 0x1e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x60, 0x0a, 0x04, 0x70, 0x65,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69,
	0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0x93, 0x03, 0x0a, 0x16, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x46, 0x6f, 0x72, 0x51,
	0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x54, 0x0a, 0x03, 0x70,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65,
	0x74, 0x12, 0x58, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x15, 0x68,
	0x61, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x68, 0x61,
	0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x64, 0x61,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x76, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x22, 0xce, 0x01, 0x0a, 0x22, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x12, 0x28, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xc6, 0x04, 0x0a, 0x22, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73,
	0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x9e, 0x01, 0x0a, 0x1f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x50, 0x0a, 0x0e, 0x48, 0x61, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xb7, 0x01, 0x0a, 0x1c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x76, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0xb1, 0x02, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x17, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0a, 0x62, 0x79, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x79, 0x50, 0x65, 0x74, 0x49, 0x64, 0x73, 0x48, 0x00, 0x52, 0x08, 0x62, 0x79, 0x50, 0x65,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x62, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x4b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x48, 0x00, 0x52, 0x09, 0x62, 0x79, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x42, 0x11, 0x0a, 0x0f, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x41, 0x0a, 0x12, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x79, 0x50, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f,
	0x92, 0x01, 0x0c, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x13, 0x45, 0x6e, 0x72, 0x6f,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0xbb, 0x03, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x57, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xff, 0x01, 0x0a,
	0x11, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x3a, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x43,
	0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x1b, 0x6e, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x19, 0x6e, 0x6f, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x73, 0x22, 0xae,
	0x02, 0x0a, 0x23, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x41, 0x0a, 0x16, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x13, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x01, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22,
	0xbd, 0x03, 0x0a, 0x23, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xf2, 0x01, 0x0a, 0x0e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x3a, 0x0a,
	0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x5f,
	0x0a, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x12, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x32,
	0xc4, 0x04, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8d,
	0x01, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x51,
	0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x8d,
	0x01, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c,
	0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a,
	0x1d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74,
	0x46, 0x6f, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_pet_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_pet_api_proto_rawDescData = file_moego_api_offering_v1_pet_api_proto_rawDesc
)

func file_moego_api_offering_v1_pet_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_pet_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_pet_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_pet_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_pet_api_proto_rawDescData
}

var file_moego_api_offering_v1_pet_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_api_offering_v1_pet_api_proto_goTypes = []interface{}{
	(*SearchPetForQuickCheckInParams)(nil),                                  // 0: moego.api.offering.v1.SearchPetForQuickCheckInParams
	(*SearchPetForQuickCheckInResult)(nil),                                  // 1: moego.api.offering.v1.SearchPetForQuickCheckInResult
	(*SearchAssociationInformationParams)(nil),                              // 2: moego.api.offering.v1.SearchAssociationInformationParams
	(*SearchAssociationInformationResult)(nil),                              // 3: moego.api.offering.v1.SearchAssociationInformationResult
	(*SearchEnrollmentInfoParams)(nil),                                      // 4: moego.api.offering.v1.SearchEnrollmentInfoParams
	(*EnrollmentByPetIds)(nil),                                              // 5: moego.api.offering.v1.EnrollmentByPetIds
	(*EnrollmentByKeyword)(nil),                                             // 6: moego.api.offering.v1.EnrollmentByKeyword
	(*SearchEnrollmentInfoResult)(nil),                                      // 7: moego.api.offering.v1.SearchEnrollmentInfoResult
	(*SearchPetForGroupClassCheckInParams)(nil),                             // 8: moego.api.offering.v1.SearchPetForGroupClassCheckInParams
	(*SearchPetForGroupClassCheckInResult)(nil),                             // 9: moego.api.offering.v1.SearchPetForGroupClassCheckInResult
	(*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn)(nil),           // 10: moego.api.offering.v1.SearchPetForQuickCheckInResult.PetViewForQuickCheckIn
	(*SearchAssociationInformationResult_HasAppointment)(nil),               // 11: moego.api.offering.v1.SearchAssociationInformationResult.HasAppointment
	(*SearchAssociationInformationResult_ClientMemberShipSubscription)(nil), // 12: moego.api.offering.v1.SearchAssociationInformationResult.ClientMemberShipSubscription
	(*SearchEnrollmentInfoResult_EnrollmentPetView)(nil),                    // 13: moego.api.offering.v1.SearchEnrollmentInfoResult.EnrollmentPetView
	(*SearchPetForGroupClassCheckInResult_CheckInPetView)(nil),              // 14: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.CheckInPetView
	(*v2.PaginationRequest)(nil),                                            // 15: moego.utils.v2.PaginationRequest
	(*date.Date)(nil),                                                       // 16: google.type.Date
	(*v2.PaginationResponse)(nil),                                           // 17: moego.utils.v2.PaginationResponse
	(*v1.BusinessCustomerPetCalendarView)(nil),                              // 18: moego.models.business_customer.v1.BusinessCustomerPetCalendarView
	(*v1.BusinessCustomerModelNameView)(nil),                                // 19: moego.models.business_customer.v1.BusinessCustomerModelNameView
	(*v11.MembershipSubscriptionListModel)(nil),                             // 20: moego.models.membership.v1.MembershipSubscriptionListModel
	(*GroupClassPetView)(nil),                                               // 21: moego.api.offering.v1.GroupClassPetView
	(*GroupClassClientView)(nil),                                            // 22: moego.api.offering.v1.GroupClassClientView
	(*v12.GroupClassModel)(nil),                                             // 23: moego.models.offering.v1.GroupClassModel
	(*GroupClassInstanceView)(nil),                                          // 24: moego.api.offering.v1.GroupClassInstanceView
}
var file_moego_api_offering_v1_pet_api_proto_depIdxs = []int32{
	15, // 0: moego.api.offering.v1.SearchPetForQuickCheckInParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 1: moego.api.offering.v1.SearchPetForQuickCheckInParams.date:type_name -> google.type.Date
	10, // 2: moego.api.offering.v1.SearchPetForQuickCheckInResult.pets:type_name -> moego.api.offering.v1.SearchPetForQuickCheckInResult.PetViewForQuickCheckIn
	17, // 3: moego.api.offering.v1.SearchPetForQuickCheckInResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	16, // 4: moego.api.offering.v1.SearchAssociationInformationParams.date:type_name -> google.type.Date
	11, // 5: moego.api.offering.v1.SearchAssociationInformationResult.pet_appointments:type_name -> moego.api.offering.v1.SearchAssociationInformationResult.HasAppointment
	12, // 6: moego.api.offering.v1.SearchAssociationInformationResult.client_membership_subscriptions:type_name -> moego.api.offering.v1.SearchAssociationInformationResult.ClientMemberShipSubscription
	5,  // 7: moego.api.offering.v1.SearchEnrollmentInfoParams.by_pet_ids:type_name -> moego.api.offering.v1.EnrollmentByPetIds
	6,  // 8: moego.api.offering.v1.SearchEnrollmentInfoParams.by_keyword:type_name -> moego.api.offering.v1.EnrollmentByKeyword
	15, // 9: moego.api.offering.v1.EnrollmentByKeyword.pagination:type_name -> moego.utils.v2.PaginationRequest
	13, // 10: moego.api.offering.v1.SearchEnrollmentInfoResult.pets:type_name -> moego.api.offering.v1.SearchEnrollmentInfoResult.EnrollmentPetView
	17, // 11: moego.api.offering.v1.SearchEnrollmentInfoResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	15, // 12: moego.api.offering.v1.SearchPetForGroupClassCheckInParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	14, // 13: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.pets:type_name -> moego.api.offering.v1.SearchPetForGroupClassCheckInResult.CheckInPetView
	17, // 14: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	18, // 15: moego.api.offering.v1.SearchPetForQuickCheckInResult.PetViewForQuickCheckIn.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetCalendarView
	19, // 16: moego.api.offering.v1.SearchPetForQuickCheckInResult.PetViewForQuickCheckIn.client:type_name -> moego.models.business_customer.v1.BusinessCustomerModelNameView
	20, // 17: moego.api.offering.v1.SearchPetForQuickCheckInResult.PetViewForQuickCheckIn.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	20, // 18: moego.api.offering.v1.SearchAssociationInformationResult.ClientMemberShipSubscription.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	21, // 19: moego.api.offering.v1.SearchEnrollmentInfoResult.EnrollmentPetView.pet:type_name -> moego.api.offering.v1.GroupClassPetView
	22, // 20: moego.api.offering.v1.SearchEnrollmentInfoResult.EnrollmentPetView.client:type_name -> moego.api.offering.v1.GroupClassClientView
	23, // 21: moego.api.offering.v1.SearchEnrollmentInfoResult.EnrollmentPetView.not_completed_prerequisites:type_name -> moego.models.offering.v1.GroupClassModel
	21, // 22: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.CheckInPetView.pet:type_name -> moego.api.offering.v1.GroupClassPetView
	22, // 23: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.CheckInPetView.client:type_name -> moego.api.offering.v1.GroupClassClientView
	24, // 24: moego.api.offering.v1.SearchPetForGroupClassCheckInResult.CheckInPetView.group_class_instance:type_name -> moego.api.offering.v1.GroupClassInstanceView
	0,  // 25: moego.api.offering.v1.PetService.SearchPetForQuickCheckIn:input_type -> moego.api.offering.v1.SearchPetForQuickCheckInParams
	2,  // 26: moego.api.offering.v1.PetService.SearchAssociationInfo:input_type -> moego.api.offering.v1.SearchAssociationInformationParams
	4,  // 27: moego.api.offering.v1.PetService.SearchEnrollmentInfo:input_type -> moego.api.offering.v1.SearchEnrollmentInfoParams
	8,  // 28: moego.api.offering.v1.PetService.SearchPetForGroupClassCheckIn:input_type -> moego.api.offering.v1.SearchPetForGroupClassCheckInParams
	1,  // 29: moego.api.offering.v1.PetService.SearchPetForQuickCheckIn:output_type -> moego.api.offering.v1.SearchPetForQuickCheckInResult
	3,  // 30: moego.api.offering.v1.PetService.SearchAssociationInfo:output_type -> moego.api.offering.v1.SearchAssociationInformationResult
	7,  // 31: moego.api.offering.v1.PetService.SearchEnrollmentInfo:output_type -> moego.api.offering.v1.SearchEnrollmentInfoResult
	9,  // 32: moego.api.offering.v1.PetService.SearchPetForGroupClassCheckIn:output_type -> moego.api.offering.v1.SearchPetForGroupClassCheckInResult
	29, // [29:33] is the sub-list for method output_type
	25, // [25:29] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_pet_api_proto_init() }
func file_moego_api_offering_v1_pet_api_proto_init() {
	if File_moego_api_offering_v1_pet_api_proto != nil {
		return
	}
	file_moego_api_offering_v1_group_class_api_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_pet_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForQuickCheckInParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForQuickCheckInResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssociationInformationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssociationInformationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchEnrollmentInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollmentByPetIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollmentByKeyword); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchEnrollmentInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForGroupClassCheckInParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForGroupClassCheckInResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForQuickCheckInResult_PetViewForQuickCheckIn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssociationInformationResult_HasAppointment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssociationInformationResult_ClientMemberShipSubscription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchEnrollmentInfoResult_EnrollmentPetView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pet_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPetForGroupClassCheckInResult_CheckInPetView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*SearchEnrollmentInfoParams_ByPetIds)(nil),
		(*SearchEnrollmentInfoParams_ByKeyword)(nil),
	}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pet_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_pet_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_pet_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_pet_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_pet_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_pet_api_proto = out.File
	file_moego_api_offering_v1_pet_api_proto_rawDesc = nil
	file_moego_api_offering_v1_pet_api_proto_goTypes = nil
	file_moego_api_offering_v1_pet_api_proto_depIdxs = nil
}
