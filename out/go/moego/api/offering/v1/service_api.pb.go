// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/service_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create service params
type CreateServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service to create
	Service *v1.CreateServiceDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateServiceParams) Reset() {
	*x = CreateServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceParams) ProtoMessage() {}

func (x *CreateServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceParams.ProtoReflect.Descriptor instead.
func (*CreateServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateServiceParams) GetService() *v1.CreateServiceDef {
	if x != nil {
		return x.Service
	}
	return nil
}

// create service result
type CreateServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service created
	Service *v1.ServiceModel `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateServiceResult) Reset() {
	*x = CreateServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResult) ProtoMessage() {}

func (x *CreateServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResult.ProtoReflect.Descriptor instead.
func (*CreateServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateServiceResult) GetService() *v1.ServiceModel {
	if x != nil {
		return x.Service
	}
	return nil
}

// update service params
type UpdateServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service to update
	Service *v1.UpdateServiceDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// whether to apply to upcoming bookings
	ApplyUpcomingAppt *bool `protobuf:"varint,2,opt,name=apply_upcoming_appt,json=applyUpcomingAppt,proto3,oneof" json:"apply_upcoming_appt,omitempty"`
}

func (x *UpdateServiceParams) Reset() {
	*x = UpdateServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceParams) ProtoMessage() {}

func (x *UpdateServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceParams.ProtoReflect.Descriptor instead.
func (*UpdateServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceParams) GetService() *v1.UpdateServiceDef {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *UpdateServiceParams) GetApplyUpcomingAppt() bool {
	if x != nil && x.ApplyUpcomingAppt != nil {
		return *x.ApplyUpcomingAppt
	}
	return false
}

// update service result
type UpdateServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service updated
	Service *v1.ServiceModel `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *UpdateServiceResult) Reset() {
	*x = UpdateServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResult) ProtoMessage() {}

func (x *UpdateServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResult.ProtoReflect.Descriptor instead.
func (*UpdateServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateServiceResult) GetService() *v1.ServiceModel {
	if x != nil {
		return x.Service
	}
	return nil
}

// get service list
type GetServiceListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// business id list, empty for all
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,4,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
	// service type
	ServiceType *v1.ServiceType `protobuf:"varint,5,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"service_type,omitempty"`
	// keyword, search by name
	Keyword *string `protobuf:"bytes,6,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// staff id list, empty for all
	StaffIds []int64 `protobuf:"varint,7,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *GetServiceListParams) Reset() {
	*x = GetServiceListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceListParams) ProtoMessage() {}

func (x *GetServiceListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceListParams.ProtoReflect.Descriptor instead.
func (*GetServiceListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetServiceListParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetServiceListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetServiceListParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *GetServiceListParams) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

func (x *GetServiceListParams) GetServiceType() v1.ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *GetServiceListParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetServiceListParams) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// get service list result, grouped by category
type GetServiceListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service list
	CategoryList []*v1.ServiceCategoryModel `protobuf:"bytes,1,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *GetServiceListResult) Reset() {
	*x = GetServiceListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceListResult) ProtoMessage() {}

func (x *GetServiceListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceListResult.ProtoReflect.Descriptor instead.
func (*GetServiceListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetServiceListResult) GetCategoryList() []*v1.ServiceCategoryModel {
	if x != nil {
		return x.CategoryList
	}
	return nil
}

func (x *GetServiceListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get applicable service list
type GetApplicableServiceListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type, grooming/boarding/daycare, only when service type is service
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// business id, empty for all business in company
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// only return available services
	OnlyAvailable bool `protobuf:"varint,3,opt,name=only_available,json=onlyAvailable,proto3" json:"only_available,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// service type, service/add-on
	ServiceType v1.ServiceType `protobuf:"varint,5,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// selected service id list, only when service type is add-on
	SelectedServiceIds []int64 `protobuf:"varint,6,rep,packed,name=selected_service_ids,json=selectedServiceIds,proto3" json:"selected_service_ids,omitempty"`
	// selected lodging unit id
	SelectedLodgingUnitId *int64 `protobuf:"varint,7,opt,name=selected_lodging_unit_id,json=selectedLodgingUnitId,proto3,oneof" json:"selected_lodging_unit_id,omitempty"`
	// keyword, search by name
	Keyword *string `protobuf:"bytes,8,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,9,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// selected service item type, only when service type is add-on
	SelectedServiceItemType *v1.ServiceItemType `protobuf:"varint,10,opt,name=selected_service_item_type,json=selectedServiceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"selected_service_item_type,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,11,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
	// multi pets id
	PetIds []int64 `protobuf:"varint,12,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *GetApplicableServiceListParams) Reset() {
	*x = GetApplicableServiceListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableServiceListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableServiceListParams) ProtoMessage() {}

func (x *GetApplicableServiceListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableServiceListParams.ProtoReflect.Descriptor instead.
func (*GetApplicableServiceListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetApplicableServiceListParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetApplicableServiceListParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetApplicableServiceListParams) GetOnlyAvailable() bool {
	if x != nil {
		return x.OnlyAvailable
	}
	return false
}

func (x *GetApplicableServiceListParams) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *GetApplicableServiceListParams) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *GetApplicableServiceListParams) GetSelectedServiceIds() []int64 {
	if x != nil {
		return x.SelectedServiceIds
	}
	return nil
}

func (x *GetApplicableServiceListParams) GetSelectedLodgingUnitId() int64 {
	if x != nil && x.SelectedLodgingUnitId != nil {
		return *x.SelectedLodgingUnitId
	}
	return 0
}

func (x *GetApplicableServiceListParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetApplicableServiceListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetApplicableServiceListParams) GetSelectedServiceItemType() v1.ServiceItemType {
	if x != nil && x.SelectedServiceItemType != nil {
		return *x.SelectedServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetApplicableServiceListParams) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

func (x *GetApplicableServiceListParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// get applicable service list result
type GetApplicableServiceListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service list
	CategoryList []*v1.CustomizedServiceCategoryView `protobuf:"bytes,1,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// service list by pet
	PetServices []*CustomizedServiceByPet `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// common service and category list
	CommonCategories []*CommonServiceCategoryView `protobuf:"bytes,4,rep,name=common_categories,json=commonCategories,proto3" json:"common_categories,omitempty"`
}

func (x *GetApplicableServiceListResult) Reset() {
	*x = GetApplicableServiceListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableServiceListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableServiceListResult) ProtoMessage() {}

func (x *GetApplicableServiceListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableServiceListResult.ProtoReflect.Descriptor instead.
func (*GetApplicableServiceListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetApplicableServiceListResult) GetCategoryList() []*v1.CustomizedServiceCategoryView {
	if x != nil {
		return x.CategoryList
	}
	return nil
}

func (x *GetApplicableServiceListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetApplicableServiceListResult) GetPetServices() []*CustomizedServiceByPet {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *GetApplicableServiceListResult) GetCommonCategories() []*CommonServiceCategoryView {
	if x != nil {
		return x.CommonCategories
	}
	return nil
}

// Service list by pet
type CustomizedServiceByPet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service list
	Categories []*v1.CustomizedServiceCategoryView `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *CustomizedServiceByPet) Reset() {
	*x = CustomizedServiceByPet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceByPet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceByPet) ProtoMessage() {}

func (x *CustomizedServiceByPet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceByPet.ProtoReflect.Descriptor instead.
func (*CustomizedServiceByPet) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{8}
}

func (x *CustomizedServiceByPet) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CustomizedServiceByPet) GetCategories() []*v1.CustomizedServiceCategoryView {
	if x != nil {
		return x.Categories
	}
	return nil
}

// common service category view
type CommonServiceCategoryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category id
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// category name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// common service view
	CommonServices []*CommonServiceView `protobuf:"bytes,3,rep,name=common_services,json=commonServices,proto3" json:"common_services,omitempty"`
}

func (x *CommonServiceCategoryView) Reset() {
	*x = CommonServiceCategoryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonServiceCategoryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonServiceCategoryView) ProtoMessage() {}

func (x *CommonServiceCategoryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonServiceCategoryView.ProtoReflect.Descriptor instead.
func (*CommonServiceCategoryView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{9}
}

func (x *CommonServiceCategoryView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CommonServiceCategoryView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CommonServiceCategoryView) GetCommonServices() []*CommonServiceView {
	if x != nil {
		return x.CommonServices
	}
	return nil
}

// Common service view
type CommonServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// different price for different pets
	PetSpecificPrices []*CommonServiceView_PetSpecificPrice `protobuf:"bytes,3,rep,name=pet_specific_prices,json=petSpecificPrices,proto3" json:"pet_specific_prices,omitempty"`
	// price unit
	PriceUnit v1.ServicePriceUnit `protobuf:"varint,4,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// different duration for different pets
	PetSpecificDurations []*CommonServiceView_PetSpecificDuration `protobuf:"bytes,5,rep,name=pet_specific_durations,json=petSpecificDurations,proto3" json:"pet_specific_durations,omitempty"`
	// type
	Type v1.ServiceType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType" json:"type,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,7,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,10,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,11,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// description
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,13,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// require dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,14,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,15,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// max duration
	MaxDuration int32 `protobuf:"varint,16,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// images
	Images []string `protobuf:"bytes,17,rep,name=images,proto3" json:"images,omitempty"`
	// staff overridden list
	StaffOverrideList []*v1.StaffOverrideRule `protobuf:"bytes,18,rep,name=staff_override_list,json=staffOverrideList,proto3" json:"staff_override_list,omitempty"`
	// available staff list
	AvailableStaffs *CommonServiceView_AvailableStaffs `protobuf:"bytes,19,opt,name=available_staffs,json=availableStaffs,proto3" json:"available_staffs,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,20,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if lodging_filter is true)
	CustomizedLodgings []int64 `protobuf:"varint,21,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,22,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// additional service rule
	AdditionalServiceRule *v1.AdditionalServiceRule `protobuf:"bytes,23,opt,name=additional_service_rule,json=additionalServiceRule,proto3" json:"additional_service_rule,omitempty"`
}

func (x *CommonServiceView) Reset() {
	*x = CommonServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonServiceView) ProtoMessage() {}

func (x *CommonServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonServiceView.ProtoReflect.Descriptor instead.
func (*CommonServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{10}
}

func (x *CommonServiceView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CommonServiceView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CommonServiceView) GetPetSpecificPrices() []*CommonServiceView_PetSpecificPrice {
	if x != nil {
		return x.PetSpecificPrices
	}
	return nil
}

func (x *CommonServiceView) GetPriceUnit() v1.ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return v1.ServicePriceUnit(0)
}

func (x *CommonServiceView) GetPetSpecificDurations() []*CommonServiceView_PetSpecificDuration {
	if x != nil {
		return x.PetSpecificDurations
	}
	return nil
}

func (x *CommonServiceView) GetType() v1.ServiceType {
	if x != nil {
		return x.Type
	}
	return v1.ServiceType(0)
}

func (x *CommonServiceView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CommonServiceView) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *CommonServiceView) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *CommonServiceView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CommonServiceView) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *CommonServiceView) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *CommonServiceView) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *CommonServiceView) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *CommonServiceView) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CommonServiceView) GetStaffOverrideList() []*v1.StaffOverrideRule {
	if x != nil {
		return x.StaffOverrideList
	}
	return nil
}

func (x *CommonServiceView) GetAvailableStaffs() *CommonServiceView_AvailableStaffs {
	if x != nil {
		return x.AvailableStaffs
	}
	return nil
}

func (x *CommonServiceView) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *CommonServiceView) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *CommonServiceView) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *CommonServiceView) GetAdditionalServiceRule() *v1.AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// params for customized service by pet
type CustomizedServiceByPetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *CustomizedServiceByPetParams) Reset() {
	*x = CustomizedServiceByPetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceByPetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceByPetParams) ProtoMessage() {}

func (x *CustomizedServiceByPetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceByPetParams.ProtoReflect.Descriptor instead.
func (*CustomizedServiceByPetParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{11}
}

func (x *CustomizedServiceByPetParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// result for customized service by pet
type CustomizedServiceByPetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customized service list
	ServiceList []*v1.ServiceWithPetCustomizedInfo `protobuf:"bytes,1,rep,name=service_list,json=serviceList,proto3" json:"service_list,omitempty"`
}

func (x *CustomizedServiceByPetResult) Reset() {
	*x = CustomizedServiceByPetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceByPetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceByPetResult) ProtoMessage() {}

func (x *CustomizedServiceByPetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceByPetResult.ProtoReflect.Descriptor instead.
func (*CustomizedServiceByPetResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{12}
}

func (x *CustomizedServiceByPetResult) GetServiceList() []*v1.ServiceWithPetCustomizedInfo {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

// get service editable detail params
type GetServiceEditableDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
}

func (x *GetServiceEditableDetailParams) Reset() {
	*x = GetServiceEditableDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceEditableDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceEditableDetailParams) ProtoMessage() {}

func (x *GetServiceEditableDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceEditableDetailParams.ProtoReflect.Descriptor instead.
func (*GetServiceEditableDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetServiceEditableDetailParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// get service editable detail result
type GetServiceEditableDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required dedicated staff editable
	RequiredDedicatedStaffEditable bool `protobuf:"varint,1,opt,name=required_dedicated_staff_editable,json=requiredDedicatedStaffEditable,proto3" json:"required_dedicated_staff_editable,omitempty"`
}

func (x *GetServiceEditableDetailResult) Reset() {
	*x = GetServiceEditableDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceEditableDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceEditableDetailResult) ProtoMessage() {}

func (x *GetServiceEditableDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceEditableDetailResult.ProtoReflect.Descriptor instead.
func (*GetServiceEditableDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetServiceEditableDetailResult) GetRequiredDedicatedStaffEditable() bool {
	if x != nil {
		return x.RequiredDedicatedStaffEditable
	}
	return false
}

// list services params
type ListServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id list, empty for all
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// service type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// service type
	ServiceType *v1.ServiceType `protobuf:"varint,3,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"service_type,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// order by
	OrderBy *v1.ServiceOrderByType `protobuf:"varint,5,opt,name=order_by,json=orderBy,proto3,enum=moego.models.offering.v1.ServiceOrderByType,oneof" json:"order_by,omitempty"`
	// inactive
	Inactive *bool `protobuf:"varint,6,opt,name=inactive,proto3,oneof" json:"inactive,omitempty"`
}

func (x *ListServicesParams) Reset() {
	*x = ListServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesParams) ProtoMessage() {}

func (x *ListServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesParams.ProtoReflect.Descriptor instead.
func (*ListServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListServicesParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListServicesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ListServicesParams) GetServiceType() v1.ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *ListServicesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesParams) GetOrderBy() v1.ServiceOrderByType {
	if x != nil && x.OrderBy != nil {
		return *x.OrderBy
	}
	return v1.ServiceOrderByType(0)
}

func (x *ListServicesParams) GetInactive() bool {
	if x != nil && x.Inactive != nil {
		return *x.Inactive
	}
	return false
}

// list services result
type ListServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service list
	Services []*v1.ServiceBriefView `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListServicesResult) Reset() {
	*x = ListServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResult) ProtoMessage() {}

func (x *ListServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResult.ProtoReflect.Descriptor instead.
func (*ListServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{16}
}

func (x *ListServicesResult) GetServices() []*v1.ServiceBriefView {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListServicesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// request for GetMaxServicePriceByLodgingType
type GetMaxServicePriceByLodgingTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type id
	LodgingTypeId int64 `protobuf:"varint,1,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetMaxServicePriceByLodgingTypeParams) Reset() {
	*x = GetMaxServicePriceByLodgingTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaxServicePriceByLodgingTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaxServicePriceByLodgingTypeParams) ProtoMessage() {}

func (x *GetMaxServicePriceByLodgingTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaxServicePriceByLodgingTypeParams.ProtoReflect.Descriptor instead.
func (*GetMaxServicePriceByLodgingTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetMaxServicePriceByLodgingTypeParams) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *GetMaxServicePriceByLodgingTypeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// response for GetMaxServicePriceByLodgingType
type GetMaxServicePriceByLodgingTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// max price
	MaxPrice *money.Money `protobuf:"bytes,1,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
}

func (x *GetMaxServicePriceByLodgingTypeResult) Reset() {
	*x = GetMaxServicePriceByLodgingTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaxServicePriceByLodgingTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaxServicePriceByLodgingTypeResult) ProtoMessage() {}

func (x *GetMaxServicePriceByLodgingTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaxServicePriceByLodgingTypeResult.ProtoReflect.Descriptor instead.
func (*GetMaxServicePriceByLodgingTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetMaxServicePriceByLodgingTypeResult) GetMaxPrice() *money.Money {
	if x != nil {
		return x.MaxPrice
	}
	return nil
}

// Available staffs
type CommonServiceView_AvailableStaffs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is all available for this service
	IsAllAvailable bool `protobuf:"varint,1,opt,name=is_all_available,json=isAllAvailable,proto3" json:"is_all_available,omitempty"`
	// available staff ids
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *CommonServiceView_AvailableStaffs) Reset() {
	*x = CommonServiceView_AvailableStaffs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonServiceView_AvailableStaffs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonServiceView_AvailableStaffs) ProtoMessage() {}

func (x *CommonServiceView_AvailableStaffs) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonServiceView_AvailableStaffs.ProtoReflect.Descriptor instead.
func (*CommonServiceView_AvailableStaffs) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *CommonServiceView_AvailableStaffs) GetIsAllAvailable() bool {
	if x != nil {
		return x.IsAllAvailable
	}
	return false
}

func (x *CommonServiceView_AvailableStaffs) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// Pet specific price
type CommonServiceView_PetSpecificPrice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,2,opt,name=price,proto3" json:"price,omitempty"`
	// price override type
	PriceOverrideType v1.ServiceOverrideType `protobuf:"varint,3,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"price_override_type,omitempty"`
}

func (x *CommonServiceView_PetSpecificPrice) Reset() {
	*x = CommonServiceView_PetSpecificPrice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonServiceView_PetSpecificPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonServiceView_PetSpecificPrice) ProtoMessage() {}

func (x *CommonServiceView_PetSpecificPrice) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonServiceView_PetSpecificPrice.ProtoReflect.Descriptor instead.
func (*CommonServiceView_PetSpecificPrice) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{10, 1}
}

func (x *CommonServiceView_PetSpecificPrice) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CommonServiceView_PetSpecificPrice) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CommonServiceView_PetSpecificPrice) GetPriceOverrideType() v1.ServiceOverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return v1.ServiceOverrideType(0)
}

// Pet specific duration
type CommonServiceView_PetSpecificDuration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	// duration override type
	DurationOverrideType v1.ServiceOverrideType `protobuf:"varint,3,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"duration_override_type,omitempty"`
}

func (x *CommonServiceView_PetSpecificDuration) Reset() {
	*x = CommonServiceView_PetSpecificDuration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonServiceView_PetSpecificDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonServiceView_PetSpecificDuration) ProtoMessage() {}

func (x *CommonServiceView_PetSpecificDuration) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonServiceView_PetSpecificDuration.ProtoReflect.Descriptor instead.
func (*CommonServiceView_PetSpecificDuration) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_api_proto_rawDescGZIP(), []int{10, 2}
}

func (x *CommonServiceView_PetSpecificDuration) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CommonServiceView_PetSpecificDuration) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *CommonServiceView_PetSpecificDuration) GetDurationOverrideType() v1.ServiceOverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return v1.ServiceOverrideType(0)
}

var File_moego_api_offering_v1_service_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_service_api_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5b, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x44, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x57, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x13,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x44, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x55,
	0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x74, 0x88, 0x01, 0x01, 0x42, 0x16,
	0x0a, 0x14, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x22, 0x57, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22,
	0xe2, 0x03, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00,
	0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12,
	0x1f, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x02, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x4d, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x03, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x27, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x04, 0x52, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69,
	0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0xc3, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a,
	0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x06, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5a, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x6e, 0x6c, 0x79,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x6f, 0x6e, 0x6c, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1a, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x02, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x3c, 0x0a, 0x18, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x15, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01,
	0x48, 0x04, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x46,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x05, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x6b, 0x0a, 0x1a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x48, 0x06, 0x52, 0x17, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x22, 0x87, 0x03, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x50,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x50, 0x65, 0x74, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x5d, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88,
	0x01, 0x0a, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x57, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x19, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x0f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22,
	0xe9, 0x0c, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x69, 0x0a, 0x13, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x2e, 0x50, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x52, 0x11, 0x70, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12,
	0x72, 0x0a, 0x16, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x70,
	0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3a, 0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x13, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x11, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x63, 0x0a, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x0f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x10, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x67, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x1a, 0x4d, 0x0a, 0x0f, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x28,
	0x0a, 0x10, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x1a, 0x9e, 0x01, 0x0a, 0x10, 0x50,
	0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x13,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xad, 0x01, 0x0a, 0x13,
	0x50, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3e, 0x0a, 0x1c, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x1c, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x50, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x48, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x6b, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x64,
	0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x49, 0x0a, 0x21, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x64,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65,
	0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x95, 0x04,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92,
	0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x66, 0x0a, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x59, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x82, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x58, 0x0a,
	0x25, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x6d,
	0x61, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x32, 0xf9, 0x07, 0x0a, 0x18, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01,
	0x0a, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x65, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x79, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x9d, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_service_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_service_api_proto_rawDescData = file_moego_api_offering_v1_service_api_proto_rawDesc
)

func file_moego_api_offering_v1_service_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_service_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_service_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_service_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_service_api_proto_rawDescData
}

var file_moego_api_offering_v1_service_api_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_moego_api_offering_v1_service_api_proto_goTypes = []interface{}{
	(*CreateServiceParams)(nil),                   // 0: moego.api.offering.v1.CreateServiceParams
	(*CreateServiceResult)(nil),                   // 1: moego.api.offering.v1.CreateServiceResult
	(*UpdateServiceParams)(nil),                   // 2: moego.api.offering.v1.UpdateServiceParams
	(*UpdateServiceResult)(nil),                   // 3: moego.api.offering.v1.UpdateServiceResult
	(*GetServiceListParams)(nil),                  // 4: moego.api.offering.v1.GetServiceListParams
	(*GetServiceListResult)(nil),                  // 5: moego.api.offering.v1.GetServiceListResult
	(*GetApplicableServiceListParams)(nil),        // 6: moego.api.offering.v1.GetApplicableServiceListParams
	(*GetApplicableServiceListResult)(nil),        // 7: moego.api.offering.v1.GetApplicableServiceListResult
	(*CustomizedServiceByPet)(nil),                // 8: moego.api.offering.v1.CustomizedServiceByPet
	(*CommonServiceCategoryView)(nil),             // 9: moego.api.offering.v1.CommonServiceCategoryView
	(*CommonServiceView)(nil),                     // 10: moego.api.offering.v1.CommonServiceView
	(*CustomizedServiceByPetParams)(nil),          // 11: moego.api.offering.v1.CustomizedServiceByPetParams
	(*CustomizedServiceByPetResult)(nil),          // 12: moego.api.offering.v1.CustomizedServiceByPetResult
	(*GetServiceEditableDetailParams)(nil),        // 13: moego.api.offering.v1.GetServiceEditableDetailParams
	(*GetServiceEditableDetailResult)(nil),        // 14: moego.api.offering.v1.GetServiceEditableDetailResult
	(*ListServicesParams)(nil),                    // 15: moego.api.offering.v1.ListServicesParams
	(*ListServicesResult)(nil),                    // 16: moego.api.offering.v1.ListServicesResult
	(*GetMaxServicePriceByLodgingTypeParams)(nil), // 17: moego.api.offering.v1.GetMaxServicePriceByLodgingTypeParams
	(*GetMaxServicePriceByLodgingTypeResult)(nil), // 18: moego.api.offering.v1.GetMaxServicePriceByLodgingTypeResult
	(*CommonServiceView_AvailableStaffs)(nil),     // 19: moego.api.offering.v1.CommonServiceView.AvailableStaffs
	(*CommonServiceView_PetSpecificPrice)(nil),    // 20: moego.api.offering.v1.CommonServiceView.PetSpecificPrice
	(*CommonServiceView_PetSpecificDuration)(nil), // 21: moego.api.offering.v1.CommonServiceView.PetSpecificDuration
	(*v1.CreateServiceDef)(nil),                   // 22: moego.models.offering.v1.CreateServiceDef
	(*v1.ServiceModel)(nil),                       // 23: moego.models.offering.v1.ServiceModel
	(*v1.UpdateServiceDef)(nil),                   // 24: moego.models.offering.v1.UpdateServiceDef
	(v1.ServiceItemType)(0),                       // 25: moego.models.offering.v1.ServiceItemType
	(*v2.PaginationRequest)(nil),                  // 26: moego.utils.v2.PaginationRequest
	(v1.ServiceType)(0),                           // 27: moego.models.offering.v1.ServiceType
	(*v1.ServiceCategoryModel)(nil),               // 28: moego.models.offering.v1.ServiceCategoryModel
	(*v2.PaginationResponse)(nil),                 // 29: moego.utils.v2.PaginationResponse
	(*v1.CustomizedServiceCategoryView)(nil),      // 30: moego.models.offering.v1.CustomizedServiceCategoryView
	(v1.ServicePriceUnit)(0),                      // 31: moego.models.offering.v1.ServicePriceUnit
	(*v1.StaffOverrideRule)(nil),                  // 32: moego.models.offering.v1.StaffOverrideRule
	(*v1.AdditionalServiceRule)(nil),              // 33: moego.models.offering.v1.AdditionalServiceRule
	(*v1.ServiceWithPetCustomizedInfo)(nil),       // 34: moego.models.offering.v1.ServiceWithPetCustomizedInfo
	(v1.ServiceOrderByType)(0),                    // 35: moego.models.offering.v1.ServiceOrderByType
	(*v1.ServiceBriefView)(nil),                   // 36: moego.models.offering.v1.ServiceBriefView
	(*money.Money)(nil),                           // 37: google.type.Money
	(v1.ServiceOverrideType)(0),                   // 38: moego.models.offering.v1.ServiceOverrideType
}
var file_moego_api_offering_v1_service_api_proto_depIdxs = []int32{
	22, // 0: moego.api.offering.v1.CreateServiceParams.service:type_name -> moego.models.offering.v1.CreateServiceDef
	23, // 1: moego.api.offering.v1.CreateServiceResult.service:type_name -> moego.models.offering.v1.ServiceModel
	24, // 2: moego.api.offering.v1.UpdateServiceParams.service:type_name -> moego.models.offering.v1.UpdateServiceDef
	23, // 3: moego.api.offering.v1.UpdateServiceResult.service:type_name -> moego.models.offering.v1.ServiceModel
	25, // 4: moego.api.offering.v1.GetServiceListParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	26, // 5: moego.api.offering.v1.GetServiceListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	27, // 6: moego.api.offering.v1.GetServiceListParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	28, // 7: moego.api.offering.v1.GetServiceListResult.category_list:type_name -> moego.models.offering.v1.ServiceCategoryModel
	29, // 8: moego.api.offering.v1.GetServiceListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	25, // 9: moego.api.offering.v1.GetApplicableServiceListParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	27, // 10: moego.api.offering.v1.GetApplicableServiceListParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	26, // 11: moego.api.offering.v1.GetApplicableServiceListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	25, // 12: moego.api.offering.v1.GetApplicableServiceListParams.selected_service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	30, // 13: moego.api.offering.v1.GetApplicableServiceListResult.category_list:type_name -> moego.models.offering.v1.CustomizedServiceCategoryView
	29, // 14: moego.api.offering.v1.GetApplicableServiceListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8,  // 15: moego.api.offering.v1.GetApplicableServiceListResult.pet_services:type_name -> moego.api.offering.v1.CustomizedServiceByPet
	9,  // 16: moego.api.offering.v1.GetApplicableServiceListResult.common_categories:type_name -> moego.api.offering.v1.CommonServiceCategoryView
	30, // 17: moego.api.offering.v1.CustomizedServiceByPet.categories:type_name -> moego.models.offering.v1.CustomizedServiceCategoryView
	10, // 18: moego.api.offering.v1.CommonServiceCategoryView.common_services:type_name -> moego.api.offering.v1.CommonServiceView
	20, // 19: moego.api.offering.v1.CommonServiceView.pet_specific_prices:type_name -> moego.api.offering.v1.CommonServiceView.PetSpecificPrice
	31, // 20: moego.api.offering.v1.CommonServiceView.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	21, // 21: moego.api.offering.v1.CommonServiceView.pet_specific_durations:type_name -> moego.api.offering.v1.CommonServiceView.PetSpecificDuration
	27, // 22: moego.api.offering.v1.CommonServiceView.type:type_name -> moego.models.offering.v1.ServiceType
	25, // 23: moego.api.offering.v1.CommonServiceView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	32, // 24: moego.api.offering.v1.CommonServiceView.staff_override_list:type_name -> moego.models.offering.v1.StaffOverrideRule
	19, // 25: moego.api.offering.v1.CommonServiceView.available_staffs:type_name -> moego.api.offering.v1.CommonServiceView.AvailableStaffs
	33, // 26: moego.api.offering.v1.CommonServiceView.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	34, // 27: moego.api.offering.v1.CustomizedServiceByPetResult.service_list:type_name -> moego.models.offering.v1.ServiceWithPetCustomizedInfo
	25, // 28: moego.api.offering.v1.ListServicesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	27, // 29: moego.api.offering.v1.ListServicesParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	26, // 30: moego.api.offering.v1.ListServicesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	35, // 31: moego.api.offering.v1.ListServicesParams.order_by:type_name -> moego.models.offering.v1.ServiceOrderByType
	36, // 32: moego.api.offering.v1.ListServicesResult.services:type_name -> moego.models.offering.v1.ServiceBriefView
	29, // 33: moego.api.offering.v1.ListServicesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	37, // 34: moego.api.offering.v1.GetMaxServicePriceByLodgingTypeResult.max_price:type_name -> google.type.Money
	38, // 35: moego.api.offering.v1.CommonServiceView.PetSpecificPrice.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	38, // 36: moego.api.offering.v1.CommonServiceView.PetSpecificDuration.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	0,  // 37: moego.api.offering.v1.ServiceManagementService.CreateService:input_type -> moego.api.offering.v1.CreateServiceParams
	2,  // 38: moego.api.offering.v1.ServiceManagementService.UpdateService:input_type -> moego.api.offering.v1.UpdateServiceParams
	4,  // 39: moego.api.offering.v1.ServiceManagementService.GetServiceList:input_type -> moego.api.offering.v1.GetServiceListParams
	6,  // 40: moego.api.offering.v1.ServiceManagementService.GetApplicableServiceList:input_type -> moego.api.offering.v1.GetApplicableServiceListParams
	11, // 41: moego.api.offering.v1.ServiceManagementService.CustomizedServiceByPet:input_type -> moego.api.offering.v1.CustomizedServiceByPetParams
	13, // 42: moego.api.offering.v1.ServiceManagementService.GetServiceEditableDetail:input_type -> moego.api.offering.v1.GetServiceEditableDetailParams
	15, // 43: moego.api.offering.v1.ServiceManagementService.ListServices:input_type -> moego.api.offering.v1.ListServicesParams
	17, // 44: moego.api.offering.v1.ServiceManagementService.GetMaxServicePriceByLodgingType:input_type -> moego.api.offering.v1.GetMaxServicePriceByLodgingTypeParams
	1,  // 45: moego.api.offering.v1.ServiceManagementService.CreateService:output_type -> moego.api.offering.v1.CreateServiceResult
	3,  // 46: moego.api.offering.v1.ServiceManagementService.UpdateService:output_type -> moego.api.offering.v1.UpdateServiceResult
	5,  // 47: moego.api.offering.v1.ServiceManagementService.GetServiceList:output_type -> moego.api.offering.v1.GetServiceListResult
	7,  // 48: moego.api.offering.v1.ServiceManagementService.GetApplicableServiceList:output_type -> moego.api.offering.v1.GetApplicableServiceListResult
	12, // 49: moego.api.offering.v1.ServiceManagementService.CustomizedServiceByPet:output_type -> moego.api.offering.v1.CustomizedServiceByPetResult
	14, // 50: moego.api.offering.v1.ServiceManagementService.GetServiceEditableDetail:output_type -> moego.api.offering.v1.GetServiceEditableDetailResult
	16, // 51: moego.api.offering.v1.ServiceManagementService.ListServices:output_type -> moego.api.offering.v1.ListServicesResult
	18, // 52: moego.api.offering.v1.ServiceManagementService.GetMaxServicePriceByLodgingType:output_type -> moego.api.offering.v1.GetMaxServicePriceByLodgingTypeResult
	45, // [45:53] is the sub-list for method output_type
	37, // [37:45] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_service_api_proto_init() }
func file_moego_api_offering_v1_service_api_proto_init() {
	if File_moego_api_offering_v1_service_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_service_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableServiceListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableServiceListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceByPet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonServiceCategoryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceByPetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceByPetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceEditableDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceEditableDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaxServicePriceByLodgingTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaxServicePriceByLodgingTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonServiceView_AvailableStaffs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonServiceView_PetSpecificPrice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonServiceView_PetSpecificDuration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_service_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_service_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_service_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_service_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_service_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_service_api_proto = out.File
	file_moego_api_offering_v1_service_api_proto_rawDesc = nil
	file_moego_api_offering_v1_service_api_proto_goTypes = nil
	file_moego_api_offering_v1_service_api_proto_depIdxs = nil
}
