// @since 2024-10-21
// <AUTHOR>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/debugging/v1/debugging_api.proto

package debuggingapipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListHeader params
type ListHeaderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListHeaderParams) Reset() {
	*x = ListHeaderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_debugging_v1_debugging_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHeaderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeaderParams) ProtoMessage() {}

func (x *ListHeaderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_debugging_v1_debugging_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeaderParams.ProtoReflect.Descriptor instead.
func (*ListHeaderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_debugging_v1_debugging_api_proto_rawDescGZIP(), []int{0}
}

// ListHeader result
type ListHeaderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// headers
	Headers map[string]string `protobuf:"bytes,1,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListHeaderResult) Reset() {
	*x = ListHeaderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_debugging_v1_debugging_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHeaderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHeaderResult) ProtoMessage() {}

func (x *ListHeaderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_debugging_v1_debugging_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHeaderResult.ProtoReflect.Descriptor instead.
func (*ListHeaderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_debugging_v1_debugging_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListHeaderResult) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

var File_moego_api_debugging_v1_debugging_api_proto protoreflect.FileDescriptor

var file_moego_api_debugging_v1_debugging_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x62, 0x75,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x22, 0x12, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x3a,
	0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x74, 0x0a, 0x10, 0x44, 0x65,
	0x62, 0x75, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60,
	0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x7e, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x64, 0x65, 0x62, 0x75, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_debugging_v1_debugging_api_proto_rawDescOnce sync.Once
	file_moego_api_debugging_v1_debugging_api_proto_rawDescData = file_moego_api_debugging_v1_debugging_api_proto_rawDesc
)

func file_moego_api_debugging_v1_debugging_api_proto_rawDescGZIP() []byte {
	file_moego_api_debugging_v1_debugging_api_proto_rawDescOnce.Do(func() {
		file_moego_api_debugging_v1_debugging_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_debugging_v1_debugging_api_proto_rawDescData)
	})
	return file_moego_api_debugging_v1_debugging_api_proto_rawDescData
}

var file_moego_api_debugging_v1_debugging_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_api_debugging_v1_debugging_api_proto_goTypes = []interface{}{
	(*ListHeaderParams)(nil), // 0: moego.api.debugging.v1.ListHeaderParams
	(*ListHeaderResult)(nil), // 1: moego.api.debugging.v1.ListHeaderResult
	nil,                      // 2: moego.api.debugging.v1.ListHeaderResult.HeadersEntry
}
var file_moego_api_debugging_v1_debugging_api_proto_depIdxs = []int32{
	2, // 0: moego.api.debugging.v1.ListHeaderResult.headers:type_name -> moego.api.debugging.v1.ListHeaderResult.HeadersEntry
	0, // 1: moego.api.debugging.v1.DebuggingService.ListHeader:input_type -> moego.api.debugging.v1.ListHeaderParams
	1, // 2: moego.api.debugging.v1.DebuggingService.ListHeader:output_type -> moego.api.debugging.v1.ListHeaderResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_debugging_v1_debugging_api_proto_init() }
func file_moego_api_debugging_v1_debugging_api_proto_init() {
	if File_moego_api_debugging_v1_debugging_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_debugging_v1_debugging_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListHeaderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_debugging_v1_debugging_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListHeaderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_debugging_v1_debugging_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_debugging_v1_debugging_api_proto_goTypes,
		DependencyIndexes: file_moego_api_debugging_v1_debugging_api_proto_depIdxs,
		MessageInfos:      file_moego_api_debugging_v1_debugging_api_proto_msgTypes,
	}.Build()
	File_moego_api_debugging_v1_debugging_api_proto = out.File
	file_moego_api_debugging_v1_debugging_api_proto_rawDesc = nil
	file_moego_api_debugging_v1_debugging_api_proto_goTypes = nil
	file_moego_api_debugging_v1_debugging_api_proto_depIdxs = nil
}
