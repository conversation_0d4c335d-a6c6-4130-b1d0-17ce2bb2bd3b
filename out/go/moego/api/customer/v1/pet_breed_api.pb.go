// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/customer/v1/pet_breed_api.proto

package customerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet breed list response
type PetBreedListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed list
	PetBreedList []*v1.PetBreedModel `protobuf:"bytes,1,rep,name=pet_breed_list,json=petBreedList,proto3" json:"pet_breed_list,omitempty"`
}

func (x *PetBreedListResponse) Reset() {
	*x = PetBreedListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_breed_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreedListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreedListResponse) ProtoMessage() {}

func (x *PetBreedListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_breed_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreedListResponse.ProtoReflect.Descriptor instead.
func (*PetBreedListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_breed_api_proto_rawDescGZIP(), []int{0}
}

func (x *PetBreedListResponse) GetPetBreedList() []*v1.PetBreedModel {
	if x != nil {
		return x.PetBreedList
	}
	return nil
}

var File_moego_api_customer_v1_pet_breed_api_proto protoreflect.FileDescriptor

var file_moego_api_customer_v1_pet_breed_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x65, 0x0a, 0x14, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4d, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x32, 0x69, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7b, 0x0a, 0x1d, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_customer_v1_pet_breed_api_proto_rawDescOnce sync.Once
	file_moego_api_customer_v1_pet_breed_api_proto_rawDescData = file_moego_api_customer_v1_pet_breed_api_proto_rawDesc
)

func file_moego_api_customer_v1_pet_breed_api_proto_rawDescGZIP() []byte {
	file_moego_api_customer_v1_pet_breed_api_proto_rawDescOnce.Do(func() {
		file_moego_api_customer_v1_pet_breed_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_customer_v1_pet_breed_api_proto_rawDescData)
	})
	return file_moego_api_customer_v1_pet_breed_api_proto_rawDescData
}

var file_moego_api_customer_v1_pet_breed_api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_api_customer_v1_pet_breed_api_proto_goTypes = []interface{}{
	(*PetBreedListResponse)(nil), // 0: moego.api.customer.v1.PetBreedListResponse
	(*v1.PetBreedModel)(nil),     // 1: moego.models.customer.v1.PetBreedModel
	(*emptypb.Empty)(nil),        // 2: google.protobuf.Empty
}
var file_moego_api_customer_v1_pet_breed_api_proto_depIdxs = []int32{
	1, // 0: moego.api.customer.v1.PetBreedListResponse.pet_breed_list:type_name -> moego.models.customer.v1.PetBreedModel
	2, // 1: moego.api.customer.v1.PetBreedService.GetPetBreedList:input_type -> google.protobuf.Empty
	0, // 2: moego.api.customer.v1.PetBreedService.GetPetBreedList:output_type -> moego.api.customer.v1.PetBreedListResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_customer_v1_pet_breed_api_proto_init() }
func file_moego_api_customer_v1_pet_breed_api_proto_init() {
	if File_moego_api_customer_v1_pet_breed_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_customer_v1_pet_breed_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreedListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_customer_v1_pet_breed_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_customer_v1_pet_breed_api_proto_goTypes,
		DependencyIndexes: file_moego_api_customer_v1_pet_breed_api_proto_depIdxs,
		MessageInfos:      file_moego_api_customer_v1_pet_breed_api_proto_msgTypes,
	}.Build()
	File_moego_api_customer_v1_pet_breed_api_proto = out.File
	file_moego_api_customer_v1_pet_breed_api_proto_rawDesc = nil
	file_moego_api_customer_v1_pet_breed_api_proto_goTypes = nil
	file_moego_api_customer_v1_pet_breed_api_proto_depIdxs = nil
}
