// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/customer/v1/pet_api.proto

package customerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet vaccine request
type PetVaccineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine metadata id
	VaccineMetadataId int32 `protobuf:"varint,1,opt,name=vaccine_metadata_id,json=vaccineMetadataId,proto3" json:"vaccine_metadata_id,omitempty"`
	// expiration date
	ExpirationDate string `protobuf:"bytes,2,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	// document url list
	DocumentUrlList []string `protobuf:"bytes,3,rep,name=document_url_list,json=documentUrlList,proto3" json:"document_url_list,omitempty"`
}

func (x *PetVaccineRequest) Reset() {
	*x = PetVaccineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineRequest) ProtoMessage() {}

func (x *PetVaccineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineRequest.ProtoReflect.Descriptor instead.
func (*PetVaccineRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{0}
}

func (x *PetVaccineRequest) GetVaccineMetadataId() int32 {
	if x != nil {
		return x.VaccineMetadataId
	}
	return 0
}

func (x *PetVaccineRequest) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

func (x *PetVaccineRequest) GetDocumentUrlList() []string {
	if x != nil {
		return x.DocumentUrlList
	}
	return nil
}

// pet request
type PetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet name
	PetName string `protobuf:"bytes,1,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType v1.PetType `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// breed id
	BreedId int32 `protobuf:"varint,4,opt,name=breed_id,json=breedId,proto3" json:"breed_id,omitempty"`
	// breed mix
	BreedMix bool `protobuf:"varint,5,opt,name=breed_mix,json=breedMix,proto3" json:"breed_mix,omitempty"`
	// birthday
	Birthday string `protobuf:"bytes,6,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// gender
	Gender v1.PetGender `protobuf:"varint,7,opt,name=gender,proto3,enum=moego.models.customer.v1.PetGender" json:"gender,omitempty"`
	// hair length metadata id
	HairLengthMetadataId int32 `protobuf:"varint,8,opt,name=hair_length_metadata_id,json=hairLengthMetadataId,proto3" json:"hair_length_metadata_id,omitempty"`
	// behavior metadata id
	BehaviorMetadataId int32 `protobuf:"varint,9,opt,name=behavior_metadata_id,json=behaviorMetadataId,proto3" json:"behavior_metadata_id,omitempty"`
	// weight
	Weight string `protobuf:"bytes,10,opt,name=weight,proto3" json:"weight,omitempty"`
	// weight unit metadata id
	WeightUnitMetadataId int32 `protobuf:"varint,12,opt,name=weight_unit_metadata_id,json=weightUnitMetadataId,proto3" json:"weight_unit_metadata_id,omitempty"`
	// fixed metadata id
	FixedMetadataId int32 `protobuf:"varint,13,opt,name=fixed_metadata_id,json=fixedMetadataId,proto3" json:"fixed_metadata_id,omitempty"`
}

func (x *PetRequest) Reset() {
	*x = PetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRequest) ProtoMessage() {}

func (x *PetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRequest.ProtoReflect.Descriptor instead.
func (*PetRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{1}
}

func (x *PetRequest) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetRequest) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *PetRequest) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *PetRequest) GetBreedId() int32 {
	if x != nil {
		return x.BreedId
	}
	return 0
}

func (x *PetRequest) GetBreedMix() bool {
	if x != nil {
		return x.BreedMix
	}
	return false
}

func (x *PetRequest) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *PetRequest) GetGender() v1.PetGender {
	if x != nil {
		return x.Gender
	}
	return v1.PetGender(0)
}

func (x *PetRequest) GetHairLengthMetadataId() int32 {
	if x != nil {
		return x.HairLengthMetadataId
	}
	return 0
}

func (x *PetRequest) GetBehaviorMetadataId() int32 {
	if x != nil {
		return x.BehaviorMetadataId
	}
	return 0
}

func (x *PetRequest) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *PetRequest) GetWeightUnitMetadataId() int32 {
	if x != nil {
		return x.WeightUnitMetadataId
	}
	return 0
}

func (x *PetRequest) GetFixedMetadataId() int32 {
	if x != nil {
		return x.FixedMetadataId
	}
	return 0
}

// add pet request
type AddPetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet request
	Pet *PetRequest `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// vaccine list
	VaccineList []*PetVaccineRequest `protobuf:"bytes,2,rep,name=vaccine_list,json=vaccineList,proto3" json:"vaccine_list,omitempty"`
}

func (x *AddPetRequest) Reset() {
	*x = AddPetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPetRequest) ProtoMessage() {}

func (x *AddPetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPetRequest.ProtoReflect.Descriptor instead.
func (*AddPetRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{2}
}

func (x *AddPetRequest) GetPet() *PetRequest {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *AddPetRequest) GetVaccineList() []*PetVaccineRequest {
	if x != nil {
		return x.VaccineList
	}
	return nil
}

// update pet request
type UpdatePetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet request
	Pet *PetRequest `protobuf:"bytes,2,opt,name=pet,proto3" json:"pet,omitempty"`
	// vaccine list
	VaccineList []*PetVaccineRequest `protobuf:"bytes,3,rep,name=vaccine_list,json=vaccineList,proto3" json:"vaccine_list,omitempty"`
}

func (x *UpdatePetRequest) Reset() {
	*x = UpdatePetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetRequest) ProtoMessage() {}

func (x *UpdatePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdatePetRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetRequest) GetPet() *PetRequest {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *UpdatePetRequest) GetVaccineList() []*PetVaccineRequest {
	if x != nil {
		return x.VaccineList
	}
	return nil
}

// get pet list response
type GetPetListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet list
	PetList []*GetPetResponse `protobuf:"bytes,1,rep,name=pet_list,json=petList,proto3" json:"pet_list,omitempty"`
}

func (x *GetPetListResponse) Reset() {
	*x = GetPetListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetListResponse) ProtoMessage() {}

func (x *GetPetListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetListResponse.ProtoReflect.Descriptor instead.
func (*GetPetListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetPetListResponse) GetPetList() []*GetPetResponse {
	if x != nil {
		return x.PetList
	}
	return nil
}

// get pet response
type GetPetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet model
	PetModel *v1.CustomerPetModel `protobuf:"bytes,1,opt,name=pet_model,json=petModel,proto3" json:"pet_model,omitempty"`
	// pet vaccine list
	VaccineList []*v1.PetVaccineSimpleView `protobuf:"bytes,2,rep,name=vaccine_list,json=vaccineList,proto3" json:"vaccine_list,omitempty"`
}

func (x *GetPetResponse) Reset() {
	*x = GetPetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetResponse) ProtoMessage() {}

func (x *GetPetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_customer_v1_pet_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetResponse.ProtoReflect.Descriptor instead.
func (*GetPetResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_customer_v1_pet_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetPetResponse) GetPetModel() *v1.CustomerPetModel {
	if x != nil {
		return x.PetModel
	}
	return nil
}

func (x *GetPetResponse) GetVaccineList() []*v1.PetVaccineSimpleView {
	if x != nil {
		return x.VaccineList
	}
	return nil
}

var File_moego_api_customer_v1_pet_api_proto protoreflect.FileDescriptor

var file_moego_api_customer_v1_pet_api_proto_rawDesc = []byte{
	0x0a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x64, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x01, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a,
	0x13, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x52, 0x11, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x20, 0xfa, 0x42, 0x1d, 0x72, 0x1b, 0x32, 0x16, 0x5e, 0x28, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x29, 0x3f, 0x24, 0xd0, 0x01,
	0x01, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x40, 0x0a, 0x11, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0xfa, 0x42,
	0x11, 0x92, 0x01, 0x0e, 0x08, 0x00, 0x22, 0x08, 0x72, 0x06, 0x18, 0xe8, 0x07, 0x88, 0x01, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x8f, 0x05, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x72, 0x09, 0x18, 0xff, 0x01, 0xd0,
	0x01, 0x01, 0x88, 0x01, 0x01, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x22, 0x0a, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d,
	0x69, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x4d,
	0x69, 0x78, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0xfa, 0x42, 0x1d, 0x72, 0x1b, 0x32, 0x16, 0x5e, 0x28, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x29, 0x3f, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x12, 0x45, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x47,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x17, 0x68, 0x61, 0x69, 0x72, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28,
	0x00, 0x40, 0x01, 0x52, 0x14, 0x68, 0x61, 0x69, 0x72, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x14, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x28, 0x00,
	0x40, 0x01, 0x52, 0x12, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01, 0x18,
	0x32, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x40, 0x0a, 0x17,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x1a, 0x04, 0x28, 0x00, 0x40, 0x01, 0x52, 0x14, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x55, 0x6e, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x11, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04,
	0x28, 0x00, 0x40, 0x01, 0x52, 0x0f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x4b, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x3d, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x70, 0x65, 0x74,
	0x12, 0x4b, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x56, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x70, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x51, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x32, 0xed, 0x02, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x50, 0x65, 0x74, 0x12, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x12, 0x12, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x43, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x12, 0x12, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x1a, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_customer_v1_pet_api_proto_rawDescOnce sync.Once
	file_moego_api_customer_v1_pet_api_proto_rawDescData = file_moego_api_customer_v1_pet_api_proto_rawDesc
)

func file_moego_api_customer_v1_pet_api_proto_rawDescGZIP() []byte {
	file_moego_api_customer_v1_pet_api_proto_rawDescOnce.Do(func() {
		file_moego_api_customer_v1_pet_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_customer_v1_pet_api_proto_rawDescData)
	})
	return file_moego_api_customer_v1_pet_api_proto_rawDescData
}

var file_moego_api_customer_v1_pet_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_api_customer_v1_pet_api_proto_goTypes = []interface{}{
	(*PetVaccineRequest)(nil),       // 0: moego.api.customer.v1.PetVaccineRequest
	(*PetRequest)(nil),              // 1: moego.api.customer.v1.PetRequest
	(*AddPetRequest)(nil),           // 2: moego.api.customer.v1.AddPetRequest
	(*UpdatePetRequest)(nil),        // 3: moego.api.customer.v1.UpdatePetRequest
	(*GetPetListResponse)(nil),      // 4: moego.api.customer.v1.GetPetListResponse
	(*GetPetResponse)(nil),          // 5: moego.api.customer.v1.GetPetResponse
	(v1.PetType)(0),                 // 6: moego.models.customer.v1.PetType
	(v1.PetGender)(0),               // 7: moego.models.customer.v1.PetGender
	(*v1.CustomerPetModel)(nil),     // 8: moego.models.customer.v1.CustomerPetModel
	(*v1.PetVaccineSimpleView)(nil), // 9: moego.models.customer.v1.PetVaccineSimpleView
	(*emptypb.Empty)(nil),           // 10: google.protobuf.Empty
	(*v11.Id)(nil),                  // 11: moego.utils.v1.Id
}
var file_moego_api_customer_v1_pet_api_proto_depIdxs = []int32{
	6,  // 0: moego.api.customer.v1.PetRequest.pet_type:type_name -> moego.models.customer.v1.PetType
	7,  // 1: moego.api.customer.v1.PetRequest.gender:type_name -> moego.models.customer.v1.PetGender
	1,  // 2: moego.api.customer.v1.AddPetRequest.pet:type_name -> moego.api.customer.v1.PetRequest
	0,  // 3: moego.api.customer.v1.AddPetRequest.vaccine_list:type_name -> moego.api.customer.v1.PetVaccineRequest
	1,  // 4: moego.api.customer.v1.UpdatePetRequest.pet:type_name -> moego.api.customer.v1.PetRequest
	0,  // 5: moego.api.customer.v1.UpdatePetRequest.vaccine_list:type_name -> moego.api.customer.v1.PetVaccineRequest
	5,  // 6: moego.api.customer.v1.GetPetListResponse.pet_list:type_name -> moego.api.customer.v1.GetPetResponse
	8,  // 7: moego.api.customer.v1.GetPetResponse.pet_model:type_name -> moego.models.customer.v1.CustomerPetModel
	9,  // 8: moego.api.customer.v1.GetPetResponse.vaccine_list:type_name -> moego.models.customer.v1.PetVaccineSimpleView
	10, // 9: moego.api.customer.v1.PetService.GetPetList:input_type -> google.protobuf.Empty
	2,  // 10: moego.api.customer.v1.PetService.AddPet:input_type -> moego.api.customer.v1.AddPetRequest
	3,  // 11: moego.api.customer.v1.PetService.UpdatePet:input_type -> moego.api.customer.v1.UpdatePetRequest
	11, // 12: moego.api.customer.v1.PetService.DeletePet:input_type -> moego.utils.v1.Id
	11, // 13: moego.api.customer.v1.PetService.GetPet:input_type -> moego.utils.v1.Id
	4,  // 14: moego.api.customer.v1.PetService.GetPetList:output_type -> moego.api.customer.v1.GetPetListResponse
	11, // 15: moego.api.customer.v1.PetService.AddPet:output_type -> moego.utils.v1.Id
	10, // 16: moego.api.customer.v1.PetService.UpdatePet:output_type -> google.protobuf.Empty
	10, // 17: moego.api.customer.v1.PetService.DeletePet:output_type -> google.protobuf.Empty
	5,  // 18: moego.api.customer.v1.PetService.GetPet:output_type -> moego.api.customer.v1.GetPetResponse
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_api_customer_v1_pet_api_proto_init() }
func file_moego_api_customer_v1_pet_api_proto_init() {
	if File_moego_api_customer_v1_pet_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_customer_v1_pet_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_customer_v1_pet_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_customer_v1_pet_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_customer_v1_pet_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_customer_v1_pet_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_customer_v1_pet_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_customer_v1_pet_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_customer_v1_pet_api_proto_goTypes,
		DependencyIndexes: file_moego_api_customer_v1_pet_api_proto_depIdxs,
		MessageInfos:      file_moego_api_customer_v1_pet_api_proto_msgTypes,
	}.Build()
	File_moego_api_customer_v1_pet_api_proto = out.File
	file_moego_api_customer_v1_pet_api_proto_rawDesc = nil
	file_moego_api_customer_v1_pet_api_proto_goTypes = nil
	file_moego_api_customer_v1_pet_api_proto_depIdxs = nil
}
