// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/message/v1/auto_message_template_api.proto

package messageapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AutoMessageTemplateServiceClient is the client API for AutoMessageTemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AutoMessageTemplateServiceClient interface {
	// Get preview message content
	GetPreviewTemplate(ctx context.Context, in *GetPreviewTemplateParams, opts ...grpc.CallOption) (*GetPreviewTemplateResult, error)
}

type autoMessageTemplateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAutoMessageTemplateServiceClient(cc grpc.ClientConnInterface) AutoMessageTemplateServiceClient {
	return &autoMessageTemplateServiceClient{cc}
}

func (c *autoMessageTemplateServiceClient) GetPreviewTemplate(ctx context.Context, in *GetPreviewTemplateParams, opts ...grpc.CallOption) (*GetPreviewTemplateResult, error) {
	out := new(GetPreviewTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.api.message.v1.AutoMessageTemplateService/GetPreviewTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AutoMessageTemplateServiceServer is the server API for AutoMessageTemplateService service.
// All implementations must embed UnimplementedAutoMessageTemplateServiceServer
// for forward compatibility
type AutoMessageTemplateServiceServer interface {
	// Get preview message content
	GetPreviewTemplate(context.Context, *GetPreviewTemplateParams) (*GetPreviewTemplateResult, error)
	mustEmbedUnimplementedAutoMessageTemplateServiceServer()
}

// UnimplementedAutoMessageTemplateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAutoMessageTemplateServiceServer struct {
}

func (UnimplementedAutoMessageTemplateServiceServer) GetPreviewTemplate(context.Context, *GetPreviewTemplateParams) (*GetPreviewTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreviewTemplate not implemented")
}
func (UnimplementedAutoMessageTemplateServiceServer) mustEmbedUnimplementedAutoMessageTemplateServiceServer() {
}

// UnsafeAutoMessageTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AutoMessageTemplateServiceServer will
// result in compilation errors.
type UnsafeAutoMessageTemplateServiceServer interface {
	mustEmbedUnimplementedAutoMessageTemplateServiceServer()
}

func RegisterAutoMessageTemplateServiceServer(s grpc.ServiceRegistrar, srv AutoMessageTemplateServiceServer) {
	s.RegisterService(&AutoMessageTemplateService_ServiceDesc, srv)
}

func _AutoMessageTemplateService_GetPreviewTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreviewTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageTemplateServiceServer).GetPreviewTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.message.v1.AutoMessageTemplateService/GetPreviewTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageTemplateServiceServer).GetPreviewTemplate(ctx, req.(*GetPreviewTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AutoMessageTemplateService_ServiceDesc is the grpc.ServiceDesc for AutoMessageTemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AutoMessageTemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.message.v1.AutoMessageTemplateService",
	HandlerType: (*AutoMessageTemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPreviewTemplate",
			Handler:    _AutoMessageTemplateService_GetPreviewTemplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/message/v1/auto_message_template_api.proto",
}
