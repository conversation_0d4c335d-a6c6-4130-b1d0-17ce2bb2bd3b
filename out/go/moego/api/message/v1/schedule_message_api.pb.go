// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/message/v1/schedule_message_api.proto

package messageapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create a schedule message for SMS type request
type CreateScheduleMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// receipt customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// message send method. It can be a custom message or an auto message
	//
	// Types that are assignable to SendMethod:
	//
	//	*CreateScheduleMessageParams_ByCustomContent
	//	*CreateScheduleMessageParams_ByAutoMessage
	SendMethod isCreateScheduleMessageParams_SendMethod `protobuf_oneof:"send_method"`
	// send out at, future delivery time
	SendOutAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=send_out_at,json=sendOutAt,proto3" json:"send_out_at,omitempty"`
	// method, default is SMS
	Method v1.Method `protobuf:"varint,5,opt,name=method,proto3,enum=moego.models.message.v1.Method" json:"method,omitempty"`
}

func (x *CreateScheduleMessageParams) Reset() {
	*x = CreateScheduleMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScheduleMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScheduleMessageParams) ProtoMessage() {}

func (x *CreateScheduleMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScheduleMessageParams.ProtoReflect.Descriptor instead.
func (*CreateScheduleMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateScheduleMessageParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (m *CreateScheduleMessageParams) GetSendMethod() isCreateScheduleMessageParams_SendMethod {
	if m != nil {
		return m.SendMethod
	}
	return nil
}

func (x *CreateScheduleMessageParams) GetByCustomContent() *v1.ScheduleMessageCustomDef {
	if x, ok := x.GetSendMethod().(*CreateScheduleMessageParams_ByCustomContent); ok {
		return x.ByCustomContent
	}
	return nil
}

func (x *CreateScheduleMessageParams) GetByAutoMessage() *v1.AutoMessageAppointmentDef {
	if x, ok := x.GetSendMethod().(*CreateScheduleMessageParams_ByAutoMessage); ok {
		return x.ByAutoMessage
	}
	return nil
}

func (x *CreateScheduleMessageParams) GetSendOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SendOutAt
	}
	return nil
}

func (x *CreateScheduleMessageParams) GetMethod() v1.Method {
	if x != nil {
		return x.Method
	}
	return v1.Method(0)
}

type isCreateScheduleMessageParams_SendMethod interface {
	isCreateScheduleMessageParams_SendMethod()
}

type CreateScheduleMessageParams_ByCustomContent struct {
	// custom content, the message manually entered by the user in the input box
	// the phone number to send can be specified.
	ByCustomContent *v1.ScheduleMessageCustomDef `protobuf:"bytes,2,opt,name=by_custom_content,json=byCustomContent,proto3,oneof"`
}

type CreateScheduleMessageParams_ByAutoMessage struct {
	// auto message template for appointment
	// the phone number to send cannot be specified. The default is primary phone number.
	ByAutoMessage *v1.AutoMessageAppointmentDef `protobuf:"bytes,3,opt,name=by_auto_message,json=byAutoMessage,proto3,oneof"`
}

func (*CreateScheduleMessageParams_ByCustomContent) isCreateScheduleMessageParams_SendMethod() {}

func (*CreateScheduleMessageParams_ByAutoMessage) isCreateScheduleMessageParams_SendMethod() {}

// Create a schedule message for SMS type response
type CreateScheduleMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	ScheduleMessageId int64 `protobuf:"varint,1,opt,name=schedule_message_id,json=scheduleMessageId,proto3" json:"schedule_message_id,omitempty"`
}

func (x *CreateScheduleMessageResult) Reset() {
	*x = CreateScheduleMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScheduleMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScheduleMessageResult) ProtoMessage() {}

func (x *CreateScheduleMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScheduleMessageResult.ProtoReflect.Descriptor instead.
func (*CreateScheduleMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateScheduleMessageResult) GetScheduleMessageId() int64 {
	if x != nil {
		return x.ScheduleMessageId
	}
	return 0
}

// Update a schedule message for SMS type params
type UpdateScheduleMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	ScheduleMessageId int64 `protobuf:"varint,1,opt,name=schedule_message_id,json=scheduleMessageId,proto3" json:"schedule_message_id,omitempty"`
	// custom content, the message manually entered by the user in the input box
	// It can only be custom content, and the auto message is not allowed to be modified.
	// the phone number and content to send can be specified
	ByCustomContent *v1.ScheduleMessageCustomDef `protobuf:"bytes,2,opt,name=by_custom_content,json=byCustomContent,proto3,oneof" json:"by_custom_content,omitempty"`
	// send out at, delivery time
	SendOutAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=send_out_at,json=sendOutAt,proto3,oneof" json:"send_out_at,omitempty"`
}

func (x *UpdateScheduleMessageParams) Reset() {
	*x = UpdateScheduleMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScheduleMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScheduleMessageParams) ProtoMessage() {}

func (x *UpdateScheduleMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScheduleMessageParams.ProtoReflect.Descriptor instead.
func (*UpdateScheduleMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateScheduleMessageParams) GetScheduleMessageId() int64 {
	if x != nil {
		return x.ScheduleMessageId
	}
	return 0
}

func (x *UpdateScheduleMessageParams) GetByCustomContent() *v1.ScheduleMessageCustomDef {
	if x != nil {
		return x.ByCustomContent
	}
	return nil
}

func (x *UpdateScheduleMessageParams) GetSendOutAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SendOutAt
	}
	return nil
}

// Update a schedule message for SMS type result
type UpdateScheduleMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateScheduleMessageResult) Reset() {
	*x = UpdateScheduleMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScheduleMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScheduleMessageResult) ProtoMessage() {}

func (x *UpdateScheduleMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScheduleMessageResult.ProtoReflect.Descriptor instead.
func (*UpdateScheduleMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{3}
}

// Delete a schedule message params
type DeleteScheduleMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	ScheduleMessageId int64 `protobuf:"varint,1,opt,name=schedule_message_id,json=scheduleMessageId,proto3" json:"schedule_message_id,omitempty"`
}

func (x *DeleteScheduleMessageParams) Reset() {
	*x = DeleteScheduleMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteScheduleMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScheduleMessageParams) ProtoMessage() {}

func (x *DeleteScheduleMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScheduleMessageParams.ProtoReflect.Descriptor instead.
func (*DeleteScheduleMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteScheduleMessageParams) GetScheduleMessageId() int64 {
	if x != nil {
		return x.ScheduleMessageId
	}
	return 0
}

// Delete a schedule message result
type DeleteScheduleMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteScheduleMessageResult) Reset() {
	*x = DeleteScheduleMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteScheduleMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScheduleMessageResult) ProtoMessage() {}

func (x *DeleteScheduleMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScheduleMessageResult.ProtoReflect.Descriptor instead.
func (*DeleteScheduleMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{5}
}

// Get a schedule message params
type GetScheduleMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	ScheduleMessageId int64 `protobuf:"varint,1,opt,name=schedule_message_id,json=scheduleMessageId,proto3" json:"schedule_message_id,omitempty"`
}

func (x *GetScheduleMessageParams) Reset() {
	*x = GetScheduleMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleMessageParams) ProtoMessage() {}

func (x *GetScheduleMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleMessageParams.ProtoReflect.Descriptor instead.
func (*GetScheduleMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetScheduleMessageParams) GetScheduleMessageId() int64 {
	if x != nil {
		return x.ScheduleMessageId
	}
	return 0
}

// Get a schedule message result
type GetScheduleMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message
	ScheduleMessage *v1.ScheduleMessagePublicView `protobuf:"bytes,1,opt,name=schedule_message,json=scheduleMessage,proto3" json:"schedule_message,omitempty"`
	// customer view
	Customer *v11.BusinessCustomerModelNameView `protobuf:"bytes,2,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *GetScheduleMessageResult) Reset() {
	*x = GetScheduleMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleMessageResult) ProtoMessage() {}

func (x *GetScheduleMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleMessageResult.ProtoReflect.Descriptor instead.
func (*GetScheduleMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetScheduleMessageResult) GetScheduleMessage() *v1.ScheduleMessagePublicView {
	if x != nil {
		return x.ScheduleMessage
	}
	return nil
}

func (x *GetScheduleMessageResult) GetCustomer() *v11.BusinessCustomerModelNameView {
	if x != nil {
		return x.Customer
	}
	return nil
}

// Get scheduled messages params
type GetScheduleMessagesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// receipt customer id
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// method filter
	Methods []v1.Method `protobuf:"varint,8,rep,packed,name=methods,proto3,enum=moego.models.message.v1.Method" json:"methods,omitempty"`
}

func (x *GetScheduleMessagesParams) Reset() {
	*x = GetScheduleMessagesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleMessagesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleMessagesParams) ProtoMessage() {}

func (x *GetScheduleMessagesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleMessagesParams.ProtoReflect.Descriptor instead.
func (*GetScheduleMessagesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetScheduleMessagesParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *GetScheduleMessagesParams) GetMethods() []v1.Method {
	if x != nil {
		return x.Methods
	}
	return nil
}

// Get scheduled messages result
type GetScheduleMessagesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message list
	ScheduleMessages []*v1.ScheduleMessagePublicView `protobuf:"bytes,1,rep,name=schedule_messages,json=scheduleMessages,proto3" json:"schedule_messages,omitempty"`
	// customer list
	Customers []*v11.BusinessCustomerModelNameView `protobuf:"bytes,2,rep,name=customers,proto3" json:"customers,omitempty"`
	// auto message template list
	AutoMessages []*v1.AutoMessageTemplatePublicView `protobuf:"bytes,3,rep,name=auto_messages,json=autoMessages,proto3" json:"auto_messages,omitempty"`
	// customer contact list
	Contacts []*v11.BusinessCustomerContactPublicView `protobuf:"bytes,4,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *GetScheduleMessagesResult) Reset() {
	*x = GetScheduleMessagesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleMessagesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleMessagesResult) ProtoMessage() {}

func (x *GetScheduleMessagesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleMessagesResult.ProtoReflect.Descriptor instead.
func (*GetScheduleMessagesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetScheduleMessagesResult) GetScheduleMessages() []*v1.ScheduleMessagePublicView {
	if x != nil {
		return x.ScheduleMessages
	}
	return nil
}

func (x *GetScheduleMessagesResult) GetCustomers() []*v11.BusinessCustomerModelNameView {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *GetScheduleMessagesResult) GetAutoMessages() []*v1.AutoMessageTemplatePublicView {
	if x != nil {
		return x.AutoMessages
	}
	return nil
}

func (x *GetScheduleMessagesResult) GetContacts() []*v11.BusinessCustomerContactPublicView {
	if x != nil {
		return x.Contacts
	}
	return nil
}

// Send scheduled message params
type SendScheduleMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// schedule message id
	ScheduleMessageId int64 `protobuf:"varint,1,opt,name=schedule_message_id,json=scheduleMessageId,proto3" json:"schedule_message_id,omitempty"`
}

func (x *SendScheduleMessageParams) Reset() {
	*x = SendScheduleMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendScheduleMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendScheduleMessageParams) ProtoMessage() {}

func (x *SendScheduleMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendScheduleMessageParams.ProtoReflect.Descriptor instead.
func (*SendScheduleMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{10}
}

func (x *SendScheduleMessageParams) GetScheduleMessageId() int64 {
	if x != nil {
		return x.ScheduleMessageId
	}
	return 0
}

// Send scheduled message result
type SendScheduleMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendScheduleMessageResult) Reset() {
	*x = SendScheduleMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendScheduleMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendScheduleMessageResult) ProtoMessage() {}

func (x *SendScheduleMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_schedule_message_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendScheduleMessageResult.ProtoReflect.Descriptor instead.
func (*SendScheduleMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_message_v1_schedule_message_api_proto protoreflect.FileDescriptor

var file_moego_api_message_v1_schedule_message_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x03, 0x0a,
	0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x11, 0x62, 0x79, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0f, 0x62, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x0f, 0x62, 0x79, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0d, 0x62, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x75, 0x74, 0x41,
	0x74, 0x12, 0x41, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x42, 0x12, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x56, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x37, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x22, 0xa1, 0x02, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x37, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x11, 0x62, 0x79, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0f, 0x62, 0x79, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a,
	0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01,
	0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x75, 0x74, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x61, 0x74, 0x22, 0x1d, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x56, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x37, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x53, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22,
	0xd7, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x10,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x08, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x22, 0xa6, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09,
	0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x22, 0x9b, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x5f, 0x0a, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x10, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x5e, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x12, 0x5b, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x60,
	0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x22, 0x54, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a,
	0x13, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x32, 0xfd, 0x05, 0x0a, 0x16, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7d,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7d, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7d, 0x0a, 0x15,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x77, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x77, 0x0a, 0x13, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_message_v1_schedule_message_api_proto_rawDescOnce sync.Once
	file_moego_api_message_v1_schedule_message_api_proto_rawDescData = file_moego_api_message_v1_schedule_message_api_proto_rawDesc
)

func file_moego_api_message_v1_schedule_message_api_proto_rawDescGZIP() []byte {
	file_moego_api_message_v1_schedule_message_api_proto_rawDescOnce.Do(func() {
		file_moego_api_message_v1_schedule_message_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_message_v1_schedule_message_api_proto_rawDescData)
	})
	return file_moego_api_message_v1_schedule_message_api_proto_rawDescData
}

var file_moego_api_message_v1_schedule_message_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_message_v1_schedule_message_api_proto_goTypes = []interface{}{
	(*CreateScheduleMessageParams)(nil),           // 0: moego.api.message.v1.CreateScheduleMessageParams
	(*CreateScheduleMessageResult)(nil),           // 1: moego.api.message.v1.CreateScheduleMessageResult
	(*UpdateScheduleMessageParams)(nil),           // 2: moego.api.message.v1.UpdateScheduleMessageParams
	(*UpdateScheduleMessageResult)(nil),           // 3: moego.api.message.v1.UpdateScheduleMessageResult
	(*DeleteScheduleMessageParams)(nil),           // 4: moego.api.message.v1.DeleteScheduleMessageParams
	(*DeleteScheduleMessageResult)(nil),           // 5: moego.api.message.v1.DeleteScheduleMessageResult
	(*GetScheduleMessageParams)(nil),              // 6: moego.api.message.v1.GetScheduleMessageParams
	(*GetScheduleMessageResult)(nil),              // 7: moego.api.message.v1.GetScheduleMessageResult
	(*GetScheduleMessagesParams)(nil),             // 8: moego.api.message.v1.GetScheduleMessagesParams
	(*GetScheduleMessagesResult)(nil),             // 9: moego.api.message.v1.GetScheduleMessagesResult
	(*SendScheduleMessageParams)(nil),             // 10: moego.api.message.v1.SendScheduleMessageParams
	(*SendScheduleMessageResult)(nil),             // 11: moego.api.message.v1.SendScheduleMessageResult
	(*v1.ScheduleMessageCustomDef)(nil),           // 12: moego.models.message.v1.ScheduleMessageCustomDef
	(*v1.AutoMessageAppointmentDef)(nil),          // 13: moego.models.message.v1.AutoMessageAppointmentDef
	(*timestamppb.Timestamp)(nil),                 // 14: google.protobuf.Timestamp
	(v1.Method)(0),                                // 15: moego.models.message.v1.Method
	(*v1.ScheduleMessagePublicView)(nil),          // 16: moego.models.message.v1.ScheduleMessagePublicView
	(*v11.BusinessCustomerModelNameView)(nil),     // 17: moego.models.business_customer.v1.BusinessCustomerModelNameView
	(*v1.AutoMessageTemplatePublicView)(nil),      // 18: moego.models.message.v1.AutoMessageTemplatePublicView
	(*v11.BusinessCustomerContactPublicView)(nil), // 19: moego.models.business_customer.v1.BusinessCustomerContactPublicView
}
var file_moego_api_message_v1_schedule_message_api_proto_depIdxs = []int32{
	12, // 0: moego.api.message.v1.CreateScheduleMessageParams.by_custom_content:type_name -> moego.models.message.v1.ScheduleMessageCustomDef
	13, // 1: moego.api.message.v1.CreateScheduleMessageParams.by_auto_message:type_name -> moego.models.message.v1.AutoMessageAppointmentDef
	14, // 2: moego.api.message.v1.CreateScheduleMessageParams.send_out_at:type_name -> google.protobuf.Timestamp
	15, // 3: moego.api.message.v1.CreateScheduleMessageParams.method:type_name -> moego.models.message.v1.Method
	12, // 4: moego.api.message.v1.UpdateScheduleMessageParams.by_custom_content:type_name -> moego.models.message.v1.ScheduleMessageCustomDef
	14, // 5: moego.api.message.v1.UpdateScheduleMessageParams.send_out_at:type_name -> google.protobuf.Timestamp
	16, // 6: moego.api.message.v1.GetScheduleMessageResult.schedule_message:type_name -> moego.models.message.v1.ScheduleMessagePublicView
	17, // 7: moego.api.message.v1.GetScheduleMessageResult.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerModelNameView
	15, // 8: moego.api.message.v1.GetScheduleMessagesParams.methods:type_name -> moego.models.message.v1.Method
	16, // 9: moego.api.message.v1.GetScheduleMessagesResult.schedule_messages:type_name -> moego.models.message.v1.ScheduleMessagePublicView
	17, // 10: moego.api.message.v1.GetScheduleMessagesResult.customers:type_name -> moego.models.business_customer.v1.BusinessCustomerModelNameView
	18, // 11: moego.api.message.v1.GetScheduleMessagesResult.auto_messages:type_name -> moego.models.message.v1.AutoMessageTemplatePublicView
	19, // 12: moego.api.message.v1.GetScheduleMessagesResult.contacts:type_name -> moego.models.business_customer.v1.BusinessCustomerContactPublicView
	0,  // 13: moego.api.message.v1.ScheduleMessageService.CreateScheduleMessage:input_type -> moego.api.message.v1.CreateScheduleMessageParams
	2,  // 14: moego.api.message.v1.ScheduleMessageService.UpdateScheduleMessage:input_type -> moego.api.message.v1.UpdateScheduleMessageParams
	4,  // 15: moego.api.message.v1.ScheduleMessageService.DeleteScheduleMessage:input_type -> moego.api.message.v1.DeleteScheduleMessageParams
	6,  // 16: moego.api.message.v1.ScheduleMessageService.GetScheduleMessage:input_type -> moego.api.message.v1.GetScheduleMessageParams
	8,  // 17: moego.api.message.v1.ScheduleMessageService.GetScheduleMessages:input_type -> moego.api.message.v1.GetScheduleMessagesParams
	10, // 18: moego.api.message.v1.ScheduleMessageService.SendScheduleMessage:input_type -> moego.api.message.v1.SendScheduleMessageParams
	1,  // 19: moego.api.message.v1.ScheduleMessageService.CreateScheduleMessage:output_type -> moego.api.message.v1.CreateScheduleMessageResult
	3,  // 20: moego.api.message.v1.ScheduleMessageService.UpdateScheduleMessage:output_type -> moego.api.message.v1.UpdateScheduleMessageResult
	5,  // 21: moego.api.message.v1.ScheduleMessageService.DeleteScheduleMessage:output_type -> moego.api.message.v1.DeleteScheduleMessageResult
	7,  // 22: moego.api.message.v1.ScheduleMessageService.GetScheduleMessage:output_type -> moego.api.message.v1.GetScheduleMessageResult
	9,  // 23: moego.api.message.v1.ScheduleMessageService.GetScheduleMessages:output_type -> moego.api.message.v1.GetScheduleMessagesResult
	11, // 24: moego.api.message.v1.ScheduleMessageService.SendScheduleMessage:output_type -> moego.api.message.v1.SendScheduleMessageResult
	19, // [19:25] is the sub-list for method output_type
	13, // [13:19] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_api_message_v1_schedule_message_api_proto_init() }
func file_moego_api_message_v1_schedule_message_api_proto_init() {
	if File_moego_api_message_v1_schedule_message_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScheduleMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScheduleMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScheduleMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScheduleMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteScheduleMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteScheduleMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleMessagesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleMessagesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendScheduleMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_schedule_message_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendScheduleMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_message_v1_schedule_message_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreateScheduleMessageParams_ByCustomContent)(nil),
		(*CreateScheduleMessageParams_ByAutoMessage)(nil),
	}
	file_moego_api_message_v1_schedule_message_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_message_v1_schedule_message_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_message_v1_schedule_message_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_message_v1_schedule_message_api_proto_goTypes,
		DependencyIndexes: file_moego_api_message_v1_schedule_message_api_proto_depIdxs,
		MessageInfos:      file_moego_api_message_v1_schedule_message_api_proto_msgTypes,
	}.Build()
	File_moego_api_message_v1_schedule_message_api_proto = out.File
	file_moego_api_message_v1_schedule_message_api_proto_rawDesc = nil
	file_moego_api_message_v1_schedule_message_api_proto_goTypes = nil
	file_moego_api_message_v1_schedule_message_api_proto_depIdxs = nil
}
