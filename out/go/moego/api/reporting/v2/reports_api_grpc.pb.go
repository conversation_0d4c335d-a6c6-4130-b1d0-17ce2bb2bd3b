// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/reporting/v2/reports_api.proto

package reportingapipb

import (
	context "context"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ReportServiceClient is the client API for ReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportServiceClient interface {
	// Query report pages returns a list of report pages
	QueryReportPages(ctx context.Context, in *QueryReportPagesRequest, opts ...grpc.CallOption) (*QueryReportPagesResponse, error)
	// Mark report favorite marks/removes a report as favorite, return the reports with new sequence
	MarkReportFavorite(ctx context.Context, in *MarkReportFavoriteRequest, opts ...grpc.CallOption) (*MarkReportFavoriteResponse, error)
	// Save report customized config
	SaveReportCustomizeConfig(ctx context.Context, in *SaveReportCustomizeConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Query metadata of reports
	QueryReportMetas(ctx context.Context, in *QueryReportMetasRequest, opts ...grpc.CallOption) (*QueryReportsMetasResponse, error)
	// Fetch report data
	FetchReportData(ctx context.Context, in *FetchReportDataRequest, opts ...grpc.CallOption) (*FetchReportDataResponse, error)
	// Export report data
	ExportReportData(ctx context.Context, in *ExportReportDataRequest, opts ...grpc.CallOption) (*ExportReportDataResponse, error)
	// Common page api
	QueryPages(ctx context.Context, in *v2.QueryPageMetaParams, opts ...grpc.CallOption) (*v2.QueryPageMetaResult, error)
	// Common meta api
	QueryMetas(ctx context.Context, in *v2.QueryMetasParams, opts ...grpc.CallOption) (*v2.QueryMetasResult, error)
	// Common fetch data api
	FetchData(ctx context.Context, in *v2.FetchDataParams, opts ...grpc.CallOption) (*v2.FetchDataResult, error)
	// Common export data api
	ExportData(ctx context.Context, in *v2.ExportDataParams, opts ...grpc.CallOption) (*v2.ExportDataResult, error)
}

type reportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportServiceClient(cc grpc.ClientConnInterface) ReportServiceClient {
	return &reportServiceClient{cc}
}

func (c *reportServiceClient) QueryReportPages(ctx context.Context, in *QueryReportPagesRequest, opts ...grpc.CallOption) (*QueryReportPagesResponse, error) {
	out := new(QueryReportPagesResponse)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/QueryReportPages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) MarkReportFavorite(ctx context.Context, in *MarkReportFavoriteRequest, opts ...grpc.CallOption) (*MarkReportFavoriteResponse, error) {
	out := new(MarkReportFavoriteResponse)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/MarkReportFavorite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) SaveReportCustomizeConfig(ctx context.Context, in *SaveReportCustomizeConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/SaveReportCustomizeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryReportMetas(ctx context.Context, in *QueryReportMetasRequest, opts ...grpc.CallOption) (*QueryReportsMetasResponse, error) {
	out := new(QueryReportsMetasResponse)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/QueryReportMetas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) FetchReportData(ctx context.Context, in *FetchReportDataRequest, opts ...grpc.CallOption) (*FetchReportDataResponse, error) {
	out := new(FetchReportDataResponse)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/FetchReportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) ExportReportData(ctx context.Context, in *ExportReportDataRequest, opts ...grpc.CallOption) (*ExportReportDataResponse, error) {
	out := new(ExportReportDataResponse)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/ExportReportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryPages(ctx context.Context, in *v2.QueryPageMetaParams, opts ...grpc.CallOption) (*v2.QueryPageMetaResult, error) {
	out := new(v2.QueryPageMetaResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/QueryPages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryMetas(ctx context.Context, in *v2.QueryMetasParams, opts ...grpc.CallOption) (*v2.QueryMetasResult, error) {
	out := new(v2.QueryMetasResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/QueryMetas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) FetchData(ctx context.Context, in *v2.FetchDataParams, opts ...grpc.CallOption) (*v2.FetchDataResult, error) {
	out := new(v2.FetchDataResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/FetchData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) ExportData(ctx context.Context, in *v2.ExportDataParams, opts ...grpc.CallOption) (*v2.ExportDataResult, error) {
	out := new(v2.ExportDataResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.ReportService/ExportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportServiceServer is the server API for ReportService service.
// All implementations must embed UnimplementedReportServiceServer
// for forward compatibility
type ReportServiceServer interface {
	// Query report pages returns a list of report pages
	QueryReportPages(context.Context, *QueryReportPagesRequest) (*QueryReportPagesResponse, error)
	// Mark report favorite marks/removes a report as favorite, return the reports with new sequence
	MarkReportFavorite(context.Context, *MarkReportFavoriteRequest) (*MarkReportFavoriteResponse, error)
	// Save report customized config
	SaveReportCustomizeConfig(context.Context, *SaveReportCustomizeConfigRequest) (*emptypb.Empty, error)
	// Query metadata of reports
	QueryReportMetas(context.Context, *QueryReportMetasRequest) (*QueryReportsMetasResponse, error)
	// Fetch report data
	FetchReportData(context.Context, *FetchReportDataRequest) (*FetchReportDataResponse, error)
	// Export report data
	ExportReportData(context.Context, *ExportReportDataRequest) (*ExportReportDataResponse, error)
	// Common page api
	QueryPages(context.Context, *v2.QueryPageMetaParams) (*v2.QueryPageMetaResult, error)
	// Common meta api
	QueryMetas(context.Context, *v2.QueryMetasParams) (*v2.QueryMetasResult, error)
	// Common fetch data api
	FetchData(context.Context, *v2.FetchDataParams) (*v2.FetchDataResult, error)
	// Common export data api
	ExportData(context.Context, *v2.ExportDataParams) (*v2.ExportDataResult, error)
	mustEmbedUnimplementedReportServiceServer()
}

// UnimplementedReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReportServiceServer struct {
}

func (UnimplementedReportServiceServer) QueryReportPages(context.Context, *QueryReportPagesRequest) (*QueryReportPagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryReportPages not implemented")
}
func (UnimplementedReportServiceServer) MarkReportFavorite(context.Context, *MarkReportFavoriteRequest) (*MarkReportFavoriteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkReportFavorite not implemented")
}
func (UnimplementedReportServiceServer) SaveReportCustomizeConfig(context.Context, *SaveReportCustomizeConfigRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveReportCustomizeConfig not implemented")
}
func (UnimplementedReportServiceServer) QueryReportMetas(context.Context, *QueryReportMetasRequest) (*QueryReportsMetasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryReportMetas not implemented")
}
func (UnimplementedReportServiceServer) FetchReportData(context.Context, *FetchReportDataRequest) (*FetchReportDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchReportData not implemented")
}
func (UnimplementedReportServiceServer) ExportReportData(context.Context, *ExportReportDataRequest) (*ExportReportDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportReportData not implemented")
}
func (UnimplementedReportServiceServer) QueryPages(context.Context, *v2.QueryPageMetaParams) (*v2.QueryPageMetaResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPages not implemented")
}
func (UnimplementedReportServiceServer) QueryMetas(context.Context, *v2.QueryMetasParams) (*v2.QueryMetasResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryMetas not implemented")
}
func (UnimplementedReportServiceServer) FetchData(context.Context, *v2.FetchDataParams) (*v2.FetchDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchData not implemented")
}
func (UnimplementedReportServiceServer) ExportData(context.Context, *v2.ExportDataParams) (*v2.ExportDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportData not implemented")
}
func (UnimplementedReportServiceServer) mustEmbedUnimplementedReportServiceServer() {}

// UnsafeReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportServiceServer will
// result in compilation errors.
type UnsafeReportServiceServer interface {
	mustEmbedUnimplementedReportServiceServer()
}

func RegisterReportServiceServer(s grpc.ServiceRegistrar, srv ReportServiceServer) {
	s.RegisterService(&ReportService_ServiceDesc, srv)
}

func _ReportService_QueryReportPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReportPagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryReportPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/QueryReportPages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryReportPages(ctx, req.(*QueryReportPagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_MarkReportFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReportFavoriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).MarkReportFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/MarkReportFavorite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).MarkReportFavorite(ctx, req.(*MarkReportFavoriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_SaveReportCustomizeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveReportCustomizeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).SaveReportCustomizeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/SaveReportCustomizeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).SaveReportCustomizeConfig(ctx, req.(*SaveReportCustomizeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryReportMetas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReportMetasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryReportMetas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/QueryReportMetas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryReportMetas(ctx, req.(*QueryReportMetasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_FetchReportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchReportDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).FetchReportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/FetchReportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).FetchReportData(ctx, req.(*FetchReportDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_ExportReportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportReportDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).ExportReportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/ExportReportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).ExportReportData(ctx, req.(*ExportReportDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.QueryPageMetaParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/QueryPages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryPages(ctx, req.(*v2.QueryPageMetaParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryMetas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.QueryMetasParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryMetas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/QueryMetas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryMetas(ctx, req.(*v2.QueryMetasParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_FetchData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.FetchDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).FetchData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/FetchData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).FetchData(ctx, req.(*v2.FetchDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_ExportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ExportDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).ExportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.ReportService/ExportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).ExportData(ctx, req.(*v2.ExportDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportService_ServiceDesc is the grpc.ServiceDesc for ReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.reporting.v2.ReportService",
	HandlerType: (*ReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryReportPages",
			Handler:    _ReportService_QueryReportPages_Handler,
		},
		{
			MethodName: "MarkReportFavorite",
			Handler:    _ReportService_MarkReportFavorite_Handler,
		},
		{
			MethodName: "SaveReportCustomizeConfig",
			Handler:    _ReportService_SaveReportCustomizeConfig_Handler,
		},
		{
			MethodName: "QueryReportMetas",
			Handler:    _ReportService_QueryReportMetas_Handler,
		},
		{
			MethodName: "FetchReportData",
			Handler:    _ReportService_FetchReportData_Handler,
		},
		{
			MethodName: "ExportReportData",
			Handler:    _ReportService_ExportReportData_Handler,
		},
		{
			MethodName: "QueryPages",
			Handler:    _ReportService_QueryPages_Handler,
		},
		{
			MethodName: "QueryMetas",
			Handler:    _ReportService_QueryMetas_Handler,
		},
		{
			MethodName: "FetchData",
			Handler:    _ReportService_FetchData_Handler,
		},
		{
			MethodName: "ExportData",
			Handler:    _ReportService_ExportData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/reporting/v2/reports_api.proto",
}
