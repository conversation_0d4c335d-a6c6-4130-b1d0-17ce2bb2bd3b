// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/metadata/v2/metadata_api.proto

package metadataapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get metadata request
type DescribeMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the specifier to filter keys
	//
	// Types that are assignable to Specifier:
	//
	//	*DescribeMetadataParams_Group
	//	*DescribeMetadataParams_Key
	Specifier isDescribeMetadataParams_Specifier `protobuf_oneof:"specifier"`
}

func (x *DescribeMetadataParams) Reset() {
	*x = DescribeMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeMetadataParams) ProtoMessage() {}

func (x *DescribeMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeMetadataParams.ProtoReflect.Descriptor instead.
func (*DescribeMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_api_metadata_v2_metadata_api_proto_rawDescGZIP(), []int{0}
}

func (m *DescribeMetadataParams) GetSpecifier() isDescribeMetadataParams_Specifier {
	if m != nil {
		return m.Specifier
	}
	return nil
}

func (x *DescribeMetadataParams) GetGroup() string {
	if x, ok := x.GetSpecifier().(*DescribeMetadataParams_Group); ok {
		return x.Group
	}
	return ""
}

func (x *DescribeMetadataParams) GetKey() string {
	if x, ok := x.GetSpecifier().(*DescribeMetadataParams_Key); ok {
		return x.Key
	}
	return ""
}

type isDescribeMetadataParams_Specifier interface {
	isDescribeMetadataParams_Specifier()
}

type DescribeMetadataParams_Group struct {
	// filter by group
	Group string `protobuf:"bytes,1,opt,name=group,proto3,oneof"`
}

type DescribeMetadataParams_Key struct {
	// filter by key
	Key string `protobuf:"bytes,2,opt,name=key,proto3,oneof"`
}

func (*DescribeMetadataParams_Group) isDescribeMetadataParams_Specifier() {}

func (*DescribeMetadataParams_Key) isDescribeMetadataParams_Specifier() {}

// get metadata response
type DescribeMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key map, key is name
	KeyMap map[string]*v1.KeyModel `protobuf:"bytes,1,rep,name=key_map,json=keyMap,proto3" json:"key_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// custom value map, key is name
	ValueMap map[string]*v1.ValueModel `protobuf:"bytes,2,rep,name=value_map,json=valueMap,proto3" json:"value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeMetadataResult) Reset() {
	*x = DescribeMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeMetadataResult) ProtoMessage() {}

func (x *DescribeMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeMetadataResult.ProtoReflect.Descriptor instead.
func (*DescribeMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_api_metadata_v2_metadata_api_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeMetadataResult) GetKeyMap() map[string]*v1.KeyModel {
	if x != nil {
		return x.KeyMap
	}
	return nil
}

func (x *DescribeMetadataResult) GetValueMap() map[string]*v1.ValueModel {
	if x != nil {
		return x.ValueMap
	}
	return nil
}

// update metadata request
type UpdateMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// the value, omit (null is different) this field will reset to default value
	Value *string `protobuf:"bytes,2,opt,name=value,proto3,oneof" json:"value,omitempty"`
}

func (x *UpdateMetadataParams) Reset() {
	*x = UpdateMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMetadataParams) ProtoMessage() {}

func (x *UpdateMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_metadata_v2_metadata_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMetadataParams.ProtoReflect.Descriptor instead.
func (*UpdateMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_api_metadata_v2_metadata_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateMetadataParams) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *UpdateMetadataParams) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

var File_moego_api_metadata_v2_metadata_api_proto protoreflect.FileDescriptor

var file_moego_api_metadata_v2_metadata_api_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6c, 0x0a, 0x16, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00,
	0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48,
	0x00, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x42, 0x10, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x88, 0x03, 0x0a, 0x16, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6b, 0x65, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x58, 0x0a, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x76, 0x32, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61,
	0x70, 0x1a, 0x5d, 0x0a, 0x0b, 0x4b, 0x65, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x61, 0x0a, 0x0d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x62, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x02, 0x18, 0x32, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80,
	0x08, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x83, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x70, 0x0a, 0x10, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a,
	0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x32, 0x50, 0x01,
	0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x32, 0x3b, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_api_metadata_v2_metadata_api_proto_rawDescOnce sync.Once
	file_moego_api_metadata_v2_metadata_api_proto_rawDescData = file_moego_api_metadata_v2_metadata_api_proto_rawDesc
)

func file_moego_api_metadata_v2_metadata_api_proto_rawDescGZIP() []byte {
	file_moego_api_metadata_v2_metadata_api_proto_rawDescOnce.Do(func() {
		file_moego_api_metadata_v2_metadata_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_metadata_v2_metadata_api_proto_rawDescData)
	})
	return file_moego_api_metadata_v2_metadata_api_proto_rawDescData
}

var file_moego_api_metadata_v2_metadata_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_api_metadata_v2_metadata_api_proto_goTypes = []interface{}{
	(*DescribeMetadataParams)(nil), // 0: moego.api.metadata.v2.DescribeMetadataParams
	(*DescribeMetadataResult)(nil), // 1: moego.api.metadata.v2.DescribeMetadataResult
	(*UpdateMetadataParams)(nil),   // 2: moego.api.metadata.v2.UpdateMetadataParams
	nil,                            // 3: moego.api.metadata.v2.DescribeMetadataResult.KeyMapEntry
	nil,                            // 4: moego.api.metadata.v2.DescribeMetadataResult.ValueMapEntry
	(*v1.KeyModel)(nil),            // 5: moego.models.metadata.v1.KeyModel
	(*v1.ValueModel)(nil),          // 6: moego.models.metadata.v1.ValueModel
}
var file_moego_api_metadata_v2_metadata_api_proto_depIdxs = []int32{
	3, // 0: moego.api.metadata.v2.DescribeMetadataResult.key_map:type_name -> moego.api.metadata.v2.DescribeMetadataResult.KeyMapEntry
	4, // 1: moego.api.metadata.v2.DescribeMetadataResult.value_map:type_name -> moego.api.metadata.v2.DescribeMetadataResult.ValueMapEntry
	5, // 2: moego.api.metadata.v2.DescribeMetadataResult.KeyMapEntry.value:type_name -> moego.models.metadata.v1.KeyModel
	6, // 3: moego.api.metadata.v2.DescribeMetadataResult.ValueMapEntry.value:type_name -> moego.models.metadata.v1.ValueModel
	0, // 4: moego.api.metadata.v2.MetadataService.DescribeMetadata:input_type -> moego.api.metadata.v2.DescribeMetadataParams
	1, // 5: moego.api.metadata.v2.MetadataService.DescribeMetadata:output_type -> moego.api.metadata.v2.DescribeMetadataResult
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_api_metadata_v2_metadata_api_proto_init() }
func file_moego_api_metadata_v2_metadata_api_proto_init() {
	if File_moego_api_metadata_v2_metadata_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_metadata_v2_metadata_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_metadata_v2_metadata_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_metadata_v2_metadata_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_metadata_v2_metadata_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*DescribeMetadataParams_Group)(nil),
		(*DescribeMetadataParams_Key)(nil),
	}
	file_moego_api_metadata_v2_metadata_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_metadata_v2_metadata_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_metadata_v2_metadata_api_proto_goTypes,
		DependencyIndexes: file_moego_api_metadata_v2_metadata_api_proto_depIdxs,
		MessageInfos:      file_moego_api_metadata_v2_metadata_api_proto_msgTypes,
	}.Build()
	File_moego_api_metadata_v2_metadata_api_proto = out.File
	file_moego_api_metadata_v2_metadata_api_proto_rawDesc = nil
	file_moego_api_metadata_v2_metadata_api_proto_goTypes = nil
	file_moego_api_metadata_v2_metadata_api_proto_depIdxs = nil
}
