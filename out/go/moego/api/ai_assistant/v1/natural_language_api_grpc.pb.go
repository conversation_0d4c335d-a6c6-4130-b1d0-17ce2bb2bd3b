// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/ai_assistant/v1/natural_language_api.proto

package aiassistantapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NaturalLanguageServiceClient is the client API for NaturalLanguageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NaturalLanguageServiceClient interface {
	// detect language
	DetectLanguage(ctx context.Context, in *DetectLanguageParams, opts ...grpc.CallOption) (*DetectLanguageResult, error)
}

type naturalLanguageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNaturalLanguageServiceClient(cc grpc.ClientConnInterface) NaturalLanguageServiceClient {
	return &naturalLanguageServiceClient{cc}
}

func (c *naturalLanguageServiceClient) DetectLanguage(ctx context.Context, in *DetectLanguageParams, opts ...grpc.CallOption) (*DetectLanguageResult, error) {
	out := new(DetectLanguageResult)
	err := c.cc.Invoke(ctx, "/moego.api.ai_assistant.v1.NaturalLanguageService/DetectLanguage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NaturalLanguageServiceServer is the server API for NaturalLanguageService service.
// All implementations must embed UnimplementedNaturalLanguageServiceServer
// for forward compatibility
type NaturalLanguageServiceServer interface {
	// detect language
	DetectLanguage(context.Context, *DetectLanguageParams) (*DetectLanguageResult, error)
	mustEmbedUnimplementedNaturalLanguageServiceServer()
}

// UnimplementedNaturalLanguageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNaturalLanguageServiceServer struct {
}

func (UnimplementedNaturalLanguageServiceServer) DetectLanguage(context.Context, *DetectLanguageParams) (*DetectLanguageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectLanguage not implemented")
}
func (UnimplementedNaturalLanguageServiceServer) mustEmbedUnimplementedNaturalLanguageServiceServer() {
}

// UnsafeNaturalLanguageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NaturalLanguageServiceServer will
// result in compilation errors.
type UnsafeNaturalLanguageServiceServer interface {
	mustEmbedUnimplementedNaturalLanguageServiceServer()
}

func RegisterNaturalLanguageServiceServer(s grpc.ServiceRegistrar, srv NaturalLanguageServiceServer) {
	s.RegisterService(&NaturalLanguageService_ServiceDesc, srv)
}

func _NaturalLanguageService_DetectLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectLanguageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NaturalLanguageServiceServer).DetectLanguage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.ai_assistant.v1.NaturalLanguageService/DetectLanguage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NaturalLanguageServiceServer).DetectLanguage(ctx, req.(*DetectLanguageParams))
	}
	return interceptor(ctx, in, info, handler)
}

// NaturalLanguageService_ServiceDesc is the grpc.ServiceDesc for NaturalLanguageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NaturalLanguageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.ai_assistant.v1.NaturalLanguageService",
	HandlerType: (*NaturalLanguageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DetectLanguage",
			Handler:    _NaturalLanguageService_DetectLanguage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/ai_assistant/v1/natural_language_api.proto",
}
