// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/membership/v1/membership_api.proto

package membershipapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MembershipServiceClient is the client API for MembershipService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MembershipServiceClient interface {
	// create membership
	CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error)
	// get membership
	GetMembership(ctx context.Context, in *GetMembershipParams, opts ...grpc.CallOption) (*GetMembershipResult, error)
	// list membership
	ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error)
	// delete membership (internal only)
	DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error)
	// 查询推荐会员内容
	ListRecommendedMemberships(ctx context.Context, in *ListRecommendMembershipsParams, opts ...grpc.CallOption) (*ListRecommendMembershipsResult, error)
	// 查询用户的会员
	ListMembershipsForCustomer(ctx context.Context, in *ListMembershipsForCustomerParams, opts ...grpc.CallOption) (*ListMembershipsForCustomerResult, error)
	// 查询核销会员权益历史
	GetRedeemHistory(ctx context.Context, in *GetRedeemHistoryParams, opts ...grpc.CallOption) (*GetRedeemHistoryResult, error)
	// apply membership
	ApplyMembership(ctx context.Context, in *ApplyMembershipParams, opts ...grpc.CallOption) (*ApplyMembershipResult, error)
	// remove membership
	RemoveMembership(ctx context.Context, in *RemoveMembershipParams, opts ...grpc.CallOption) (*RemoveMembershipResult, error)
	// 查询perk的周期值
	ListAllPerkCycle(ctx context.Context, in *ListAllPerkCycleParams, opts ...grpc.CallOption) (*ListAllPerkCycleResult, error)
	// get perk usage detail
	GetPerkUsageDetail(ctx context.Context, in *GetPerkUsageDetailParams, opts ...grpc.CallOption) (*GetPerkUsageDetailResult, error)
	// 权益转换
	TransferCredits(ctx context.Context, in *TransferCreditsParams, opts ...grpc.CallOption) (*TransferCreditsResult, error)
	// list membership for sale
	ListMembershipsForSale(ctx context.Context, in *ListMembershipsForSaleParams, opts ...grpc.CallOption) (*ListMembershipsForSaleResult, error)
}

type membershipServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMembershipServiceClient(cc grpc.ClientConnInterface) MembershipServiceClient {
	return &membershipServiceClient{cc}
}

func (c *membershipServiceClient) CreateMembership(ctx context.Context, in *CreateMembershipParams, opts ...grpc.CallOption) (*CreateMembershipResult, error) {
	out := new(CreateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/CreateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) GetMembership(ctx context.Context, in *GetMembershipParams, opts ...grpc.CallOption) (*GetMembershipResult, error) {
	out := new(GetMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/GetMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMemberships(ctx context.Context, in *ListMembershipsParams, opts ...grpc.CallOption) (*ListMembershipsResult, error) {
	out := new(ListMembershipsResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ListMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) UpdateMembership(ctx context.Context, in *UpdateMembershipParams, opts ...grpc.CallOption) (*UpdateMembershipResult, error) {
	out := new(UpdateMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/UpdateMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) DeleteMembership(ctx context.Context, in *DeleteMembershipParams, opts ...grpc.CallOption) (*DeleteMembershipResult, error) {
	out := new(DeleteMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/DeleteMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListRecommendedMemberships(ctx context.Context, in *ListRecommendMembershipsParams, opts ...grpc.CallOption) (*ListRecommendMembershipsResult, error) {
	out := new(ListRecommendMembershipsResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ListRecommendedMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMembershipsForCustomer(ctx context.Context, in *ListMembershipsForCustomerParams, opts ...grpc.CallOption) (*ListMembershipsForCustomerResult, error) {
	out := new(ListMembershipsForCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ListMembershipsForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) GetRedeemHistory(ctx context.Context, in *GetRedeemHistoryParams, opts ...grpc.CallOption) (*GetRedeemHistoryResult, error) {
	out := new(GetRedeemHistoryResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/GetRedeemHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ApplyMembership(ctx context.Context, in *ApplyMembershipParams, opts ...grpc.CallOption) (*ApplyMembershipResult, error) {
	out := new(ApplyMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ApplyMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) RemoveMembership(ctx context.Context, in *RemoveMembershipParams, opts ...grpc.CallOption) (*RemoveMembershipResult, error) {
	out := new(RemoveMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/RemoveMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListAllPerkCycle(ctx context.Context, in *ListAllPerkCycleParams, opts ...grpc.CallOption) (*ListAllPerkCycleResult, error) {
	out := new(ListAllPerkCycleResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ListAllPerkCycle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) GetPerkUsageDetail(ctx context.Context, in *GetPerkUsageDetailParams, opts ...grpc.CallOption) (*GetPerkUsageDetailResult, error) {
	out := new(GetPerkUsageDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/GetPerkUsageDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) TransferCredits(ctx context.Context, in *TransferCreditsParams, opts ...grpc.CallOption) (*TransferCreditsResult, error) {
	out := new(TransferCreditsResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/TransferCredits", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *membershipServiceClient) ListMembershipsForSale(ctx context.Context, in *ListMembershipsForSaleParams, opts ...grpc.CallOption) (*ListMembershipsForSaleResult, error) {
	out := new(ListMembershipsForSaleResult)
	err := c.cc.Invoke(ctx, "/moego.api.membership.v1.MembershipService/ListMembershipsForSale", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MembershipServiceServer is the server API for MembershipService service.
// All implementations must embed UnimplementedMembershipServiceServer
// for forward compatibility
type MembershipServiceServer interface {
	// create membership
	CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error)
	// get membership
	GetMembership(context.Context, *GetMembershipParams) (*GetMembershipResult, error)
	// list membership
	ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error)
	// update membership
	UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error)
	// delete membership (internal only)
	DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error)
	// 查询推荐会员内容
	ListRecommendedMemberships(context.Context, *ListRecommendMembershipsParams) (*ListRecommendMembershipsResult, error)
	// 查询用户的会员
	ListMembershipsForCustomer(context.Context, *ListMembershipsForCustomerParams) (*ListMembershipsForCustomerResult, error)
	// 查询核销会员权益历史
	GetRedeemHistory(context.Context, *GetRedeemHistoryParams) (*GetRedeemHistoryResult, error)
	// apply membership
	ApplyMembership(context.Context, *ApplyMembershipParams) (*ApplyMembershipResult, error)
	// remove membership
	RemoveMembership(context.Context, *RemoveMembershipParams) (*RemoveMembershipResult, error)
	// 查询perk的周期值
	ListAllPerkCycle(context.Context, *ListAllPerkCycleParams) (*ListAllPerkCycleResult, error)
	// get perk usage detail
	GetPerkUsageDetail(context.Context, *GetPerkUsageDetailParams) (*GetPerkUsageDetailResult, error)
	// 权益转换
	TransferCredits(context.Context, *TransferCreditsParams) (*TransferCreditsResult, error)
	// list membership for sale
	ListMembershipsForSale(context.Context, *ListMembershipsForSaleParams) (*ListMembershipsForSaleResult, error)
	mustEmbedUnimplementedMembershipServiceServer()
}

// UnimplementedMembershipServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMembershipServiceServer struct {
}

func (UnimplementedMembershipServiceServer) CreateMembership(context.Context, *CreateMembershipParams) (*CreateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMembership not implemented")
}
func (UnimplementedMembershipServiceServer) GetMembership(context.Context, *GetMembershipParams) (*GetMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMembership not implemented")
}
func (UnimplementedMembershipServiceServer) ListMemberships(context.Context, *ListMembershipsParams) (*ListMembershipsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemberships not implemented")
}
func (UnimplementedMembershipServiceServer) UpdateMembership(context.Context, *UpdateMembershipParams) (*UpdateMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMembership not implemented")
}
func (UnimplementedMembershipServiceServer) DeleteMembership(context.Context, *DeleteMembershipParams) (*DeleteMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMembership not implemented")
}
func (UnimplementedMembershipServiceServer) ListRecommendedMemberships(context.Context, *ListRecommendMembershipsParams) (*ListRecommendMembershipsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecommendedMemberships not implemented")
}
func (UnimplementedMembershipServiceServer) ListMembershipsForCustomer(context.Context, *ListMembershipsForCustomerParams) (*ListMembershipsForCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsForCustomer not implemented")
}
func (UnimplementedMembershipServiceServer) GetRedeemHistory(context.Context, *GetRedeemHistoryParams) (*GetRedeemHistoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemHistory not implemented")
}
func (UnimplementedMembershipServiceServer) ApplyMembership(context.Context, *ApplyMembershipParams) (*ApplyMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyMembership not implemented")
}
func (UnimplementedMembershipServiceServer) RemoveMembership(context.Context, *RemoveMembershipParams) (*RemoveMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveMembership not implemented")
}
func (UnimplementedMembershipServiceServer) ListAllPerkCycle(context.Context, *ListAllPerkCycleParams) (*ListAllPerkCycleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllPerkCycle not implemented")
}
func (UnimplementedMembershipServiceServer) GetPerkUsageDetail(context.Context, *GetPerkUsageDetailParams) (*GetPerkUsageDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerkUsageDetail not implemented")
}
func (UnimplementedMembershipServiceServer) TransferCredits(context.Context, *TransferCreditsParams) (*TransferCreditsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferCredits not implemented")
}
func (UnimplementedMembershipServiceServer) ListMembershipsForSale(context.Context, *ListMembershipsForSaleParams) (*ListMembershipsForSaleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMembershipsForSale not implemented")
}
func (UnimplementedMembershipServiceServer) mustEmbedUnimplementedMembershipServiceServer() {}

// UnsafeMembershipServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MembershipServiceServer will
// result in compilation errors.
type UnsafeMembershipServiceServer interface {
	mustEmbedUnimplementedMembershipServiceServer()
}

func RegisterMembershipServiceServer(s grpc.ServiceRegistrar, srv MembershipServiceServer) {
	s.RegisterService(&MembershipService_ServiceDesc, srv)
}

func _MembershipService_CreateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).CreateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/CreateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).CreateMembership(ctx, req.(*CreateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_GetMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).GetMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/GetMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).GetMembership(ctx, req.(*GetMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ListMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMemberships(ctx, req.(*ListMembershipsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_UpdateMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).UpdateMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/UpdateMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).UpdateMembership(ctx, req.(*UpdateMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_DeleteMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).DeleteMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/DeleteMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).DeleteMembership(ctx, req.(*DeleteMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListRecommendedMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecommendMembershipsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListRecommendedMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ListRecommendedMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListRecommendedMemberships(ctx, req.(*ListRecommendMembershipsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMembershipsForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsForCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMembershipsForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ListMembershipsForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMembershipsForCustomer(ctx, req.(*ListMembershipsForCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_GetRedeemHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemHistoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).GetRedeemHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/GetRedeemHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).GetRedeemHistory(ctx, req.(*GetRedeemHistoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ApplyMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ApplyMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ApplyMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ApplyMembership(ctx, req.(*ApplyMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_RemoveMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).RemoveMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/RemoveMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).RemoveMembership(ctx, req.(*RemoveMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListAllPerkCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllPerkCycleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListAllPerkCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ListAllPerkCycle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListAllPerkCycle(ctx, req.(*ListAllPerkCycleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_GetPerkUsageDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerkUsageDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).GetPerkUsageDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/GetPerkUsageDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).GetPerkUsageDetail(ctx, req.(*GetPerkUsageDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_TransferCredits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferCreditsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).TransferCredits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/TransferCredits",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).TransferCredits(ctx, req.(*TransferCreditsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MembershipService_ListMembershipsForSale_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMembershipsForSaleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MembershipServiceServer).ListMembershipsForSale(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.membership.v1.MembershipService/ListMembershipsForSale",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MembershipServiceServer).ListMembershipsForSale(ctx, req.(*ListMembershipsForSaleParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MembershipService_ServiceDesc is the grpc.ServiceDesc for MembershipService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MembershipService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.membership.v1.MembershipService",
	HandlerType: (*MembershipServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMembership",
			Handler:    _MembershipService_CreateMembership_Handler,
		},
		{
			MethodName: "GetMembership",
			Handler:    _MembershipService_GetMembership_Handler,
		},
		{
			MethodName: "ListMemberships",
			Handler:    _MembershipService_ListMemberships_Handler,
		},
		{
			MethodName: "UpdateMembership",
			Handler:    _MembershipService_UpdateMembership_Handler,
		},
		{
			MethodName: "DeleteMembership",
			Handler:    _MembershipService_DeleteMembership_Handler,
		},
		{
			MethodName: "ListRecommendedMemberships",
			Handler:    _MembershipService_ListRecommendedMemberships_Handler,
		},
		{
			MethodName: "ListMembershipsForCustomer",
			Handler:    _MembershipService_ListMembershipsForCustomer_Handler,
		},
		{
			MethodName: "GetRedeemHistory",
			Handler:    _MembershipService_GetRedeemHistory_Handler,
		},
		{
			MethodName: "ApplyMembership",
			Handler:    _MembershipService_ApplyMembership_Handler,
		},
		{
			MethodName: "RemoveMembership",
			Handler:    _MembershipService_RemoveMembership_Handler,
		},
		{
			MethodName: "ListAllPerkCycle",
			Handler:    _MembershipService_ListAllPerkCycle_Handler,
		},
		{
			MethodName: "GetPerkUsageDetail",
			Handler:    _MembershipService_GetPerkUsageDetail_Handler,
		},
		{
			MethodName: "TransferCredits",
			Handler:    _MembershipService_TransferCredits_Handler,
		},
		{
			MethodName: "ListMembershipsForSale",
			Handler:    _MembershipService_ListMembershipsForSale_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/membership/v1/membership_api.proto",
}
