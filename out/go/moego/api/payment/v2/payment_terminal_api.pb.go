// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/payment/v2/payment_terminal_api.proto

package paymentapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get reader params
type GetTerminalParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reader id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTerminalParams) Reset() {
	*x = GetTerminalParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTerminalParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalParams) ProtoMessage() {}

func (x *GetTerminalParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalParams.ProtoReflect.Descriptor instead.
func (*GetTerminalParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetTerminalParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get reader result
type GetTerminalResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付终端
	Terminal *v2.TerminalView `protobuf:"bytes,1,opt,name=terminal,proto3" json:"terminal,omitempty"`
}

func (x *GetTerminalResult) Reset() {
	*x = GetTerminalResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTerminalResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalResult) ProtoMessage() {}

func (x *GetTerminalResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalResult.ProtoReflect.Descriptor instead.
func (*GetTerminalResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetTerminalResult) GetTerminal() *v2.TerminalView {
	if x != nil {
		return x.Terminal
	}
	return nil
}

// list terminal params
type ListTerminalsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页参数
	Pagination *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListTerminalsParams) Reset() {
	*x = ListTerminalsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTerminalsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsParams) ProtoMessage() {}

func (x *ListTerminalsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsParams.ProtoReflect.Descriptor instead.
func (*ListTerminalsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListTerminalsParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list terminal result
type ListTerminalsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付终端列表
	Terminals []*v2.TerminalView `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
	// 分页信息
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListTerminalsResult) Reset() {
	*x = ListTerminalsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTerminalsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsResult) ProtoMessage() {}

func (x *ListTerminalsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsResult.ProtoReflect.Descriptor instead.
func (*ListTerminalsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListTerminalsResult) GetTerminals() []*v2.TerminalView {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *ListTerminalsResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_api_payment_v2_payment_terminal_api_proto protoreflect.FileDescriptor

var file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x23, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x56, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x41, 0x0a, 0x08, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0x58, 0x0a, 0x13,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9e, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x43,
	0x0a, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xe0, 0x01, 0x0a, 0x16, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x5f, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x65, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x73, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescOnce sync.Once
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData = file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc
)

func file_moego_api_payment_v2_payment_terminal_api_proto_rawDescGZIP() []byte {
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDescOnce.Do(func() {
		file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData)
	})
	return file_moego_api_payment_v2_payment_terminal_api_proto_rawDescData
}

var file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_payment_v2_payment_terminal_api_proto_goTypes = []interface{}{
	(*GetTerminalParams)(nil),      // 0: moego.api.payment.v2.GetTerminalParams
	(*GetTerminalResult)(nil),      // 1: moego.api.payment.v2.GetTerminalResult
	(*ListTerminalsParams)(nil),    // 2: moego.api.payment.v2.ListTerminalsParams
	(*ListTerminalsResult)(nil),    // 3: moego.api.payment.v2.ListTerminalsResult
	(*v2.TerminalView)(nil),        // 4: moego.models.payment.v2.TerminalView
	(*v21.PaginationRequest)(nil),  // 5: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil), // 6: moego.utils.v2.PaginationResponse
}
var file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs = []int32{
	4, // 0: moego.api.payment.v2.GetTerminalResult.terminal:type_name -> moego.models.payment.v2.TerminalView
	5, // 1: moego.api.payment.v2.ListTerminalsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4, // 2: moego.api.payment.v2.ListTerminalsResult.terminals:type_name -> moego.models.payment.v2.TerminalView
	6, // 3: moego.api.payment.v2.ListTerminalsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	0, // 4: moego.api.payment.v2.PaymentTerminalService.GetTerminal:input_type -> moego.api.payment.v2.GetTerminalParams
	2, // 5: moego.api.payment.v2.PaymentTerminalService.ListTerminals:input_type -> moego.api.payment.v2.ListTerminalsParams
	1, // 6: moego.api.payment.v2.PaymentTerminalService.GetTerminal:output_type -> moego.api.payment.v2.GetTerminalResult
	3, // 7: moego.api.payment.v2.PaymentTerminalService.ListTerminals:output_type -> moego.api.payment.v2.ListTerminalsResult
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_api_payment_v2_payment_terminal_api_proto_init() }
func file_moego_api_payment_v2_payment_terminal_api_proto_init() {
	if File_moego_api_payment_v2_payment_terminal_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTerminalParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTerminalResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTerminalsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTerminalsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_payment_v2_payment_terminal_api_proto_goTypes,
		DependencyIndexes: file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs,
		MessageInfos:      file_moego_api_payment_v2_payment_terminal_api_proto_msgTypes,
	}.Build()
	File_moego_api_payment_v2_payment_terminal_api_proto = out.File
	file_moego_api_payment_v2_payment_terminal_api_proto_rawDesc = nil
	file_moego_api_payment_v2_payment_terminal_api_proto_goTypes = nil
	file_moego_api_payment_v2_payment_terminal_api_proto_depIdxs = nil
}
