// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/payment/v2/payment_ops_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentOpsServiceClient is the client API for PaymentOpsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentOpsServiceClient interface {
	// Import external Square customers and cards on file to Stripe.
	ImportExternalSquareCofToStripe(ctx context.Context, in *ImportExternalSquareCofToStripeParams, opts ...grpc.CallOption) (*ImportExternalSquareCofToStripeResult, error)
}

type paymentOpsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentOpsServiceClient(cc grpc.ClientConnInterface) PaymentOpsServiceClient {
	return &paymentOpsServiceClient{cc}
}

func (c *paymentOpsServiceClient) ImportExternalSquareCofToStripe(ctx context.Context, in *ImportExternalSquareCofToStripeParams, opts ...grpc.CallOption) (*ImportExternalSquareCofToStripeResult, error) {
	out := new(ImportExternalSquareCofToStripeResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentOpsService/ImportExternalSquareCofToStripe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentOpsServiceServer is the server API for PaymentOpsService service.
// All implementations must embed UnimplementedPaymentOpsServiceServer
// for forward compatibility
type PaymentOpsServiceServer interface {
	// Import external Square customers and cards on file to Stripe.
	ImportExternalSquareCofToStripe(context.Context, *ImportExternalSquareCofToStripeParams) (*ImportExternalSquareCofToStripeResult, error)
	mustEmbedUnimplementedPaymentOpsServiceServer()
}

// UnimplementedPaymentOpsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentOpsServiceServer struct {
}

func (UnimplementedPaymentOpsServiceServer) ImportExternalSquareCofToStripe(context.Context, *ImportExternalSquareCofToStripeParams) (*ImportExternalSquareCofToStripeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportExternalSquareCofToStripe not implemented")
}
func (UnimplementedPaymentOpsServiceServer) mustEmbedUnimplementedPaymentOpsServiceServer() {}

// UnsafePaymentOpsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentOpsServiceServer will
// result in compilation errors.
type UnsafePaymentOpsServiceServer interface {
	mustEmbedUnimplementedPaymentOpsServiceServer()
}

func RegisterPaymentOpsServiceServer(s grpc.ServiceRegistrar, srv PaymentOpsServiceServer) {
	s.RegisterService(&PaymentOpsService_ServiceDesc, srv)
}

func _PaymentOpsService_ImportExternalSquareCofToStripe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportExternalSquareCofToStripeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentOpsServiceServer).ImportExternalSquareCofToStripe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentOpsService/ImportExternalSquareCofToStripe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentOpsServiceServer).ImportExternalSquareCofToStripe(ctx, req.(*ImportExternalSquareCofToStripeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentOpsService_ServiceDesc is the grpc.ServiceDesc for PaymentOpsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentOpsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.payment.v2.PaymentOpsService",
	HandlerType: (*PaymentOpsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImportExternalSquareCofToStripe",
			Handler:    _PaymentOpsService_ImportExternalSquareCofToStripe_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/payment/v2/payment_ops_api.proto",
}
