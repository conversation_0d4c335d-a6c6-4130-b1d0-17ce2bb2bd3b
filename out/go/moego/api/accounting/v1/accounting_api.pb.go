// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/accounting/v1/accounting_api.proto

package accountingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/accounting/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// sync status
type AccountingBusinessView_SyncStatus int32

const (
	// Unspecified
	AccountingBusinessView_SYNC_STATUS_UNSPECIFIED AccountingBusinessView_SyncStatus = 0
	// synced
	AccountingBusinessView_SYNCED AccountingBusinessView_SyncStatus = 1
	// not synced
	AccountingBusinessView_NOT_SYNCED AccountingBusinessView_SyncStatus = 2
)

// Enum value maps for AccountingBusinessView_SyncStatus.
var (
	AccountingBusinessView_SyncStatus_name = map[int32]string{
		0: "SYNC_STATUS_UNSPECIFIED",
		1: "SYNCED",
		2: "NOT_SYNCED",
	}
	AccountingBusinessView_SyncStatus_value = map[string]int32{
		"SYNC_STATUS_UNSPECIFIED": 0,
		"SYNCED":                  1,
		"NOT_SYNCED":              2,
	}
)

func (x AccountingBusinessView_SyncStatus) Enum() *AccountingBusinessView_SyncStatus {
	p := new(AccountingBusinessView_SyncStatus)
	*p = x
	return p
}

func (x AccountingBusinessView_SyncStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountingBusinessView_SyncStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_accounting_v1_accounting_api_proto_enumTypes[0].Descriptor()
}

func (AccountingBusinessView_SyncStatus) Type() protoreflect.EnumType {
	return &file_moego_api_accounting_v1_accounting_api_proto_enumTypes[0]
}

func (x AccountingBusinessView_SyncStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountingBusinessView_SyncStatus.Descriptor instead.
func (AccountingBusinessView_SyncStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{5, 0}
}

// GetVisibilityParams
type GetVisibilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetVisibilityParams) Reset() {
	*x = GetVisibilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisibilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisibilityParams) ProtoMessage() {}

func (x *GetVisibilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisibilityParams.ProtoReflect.Descriptor instead.
func (*GetVisibilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{0}
}

// GetVisibilityResult
type GetVisibilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// visibility
	Visible bool `protobuf:"varint,1,opt,name=visible,proto3" json:"visible,omitempty"`
	// visibility class
	VisibilityClass v1.VisibilityClass `protobuf:"varint,2,opt,name=visibility_class,json=visibilityClass,proto3,enum=moego.models.accounting.v1.VisibilityClass" json:"visibility_class,omitempty"`
}

func (x *GetVisibilityResult) Reset() {
	*x = GetVisibilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisibilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisibilityResult) ProtoMessage() {}

func (x *GetVisibilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisibilityResult.ProtoReflect.Descriptor instead.
func (*GetVisibilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetVisibilityResult) GetVisible() bool {
	if x != nil {
		return x.Visible
	}
	return false
}

func (x *GetVisibilityResult) GetVisibilityClass() v1.VisibilityClass {
	if x != nil {
		return x.VisibilityClass
	}
	return v1.VisibilityClass(0)
}

// GetOnboardingStatusParams
type GetOnboardingStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetOnboardingStatusParams) Reset() {
	*x = GetOnboardingStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusParams) ProtoMessage() {}

func (x *GetOnboardingStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusParams.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetOnboardingStatusParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

// GetOnboardingStatusResult
type GetOnboardingStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// onboarding status
	OnboardingStatus v1.OnboardingStatus `protobuf:"varint,1,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=moego.models.accounting.v1.OnboardingStatus" json:"onboarding_status,omitempty"`
}

func (x *GetOnboardingStatusResult) Reset() {
	*x = GetOnboardingStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusResult) ProtoMessage() {}

func (x *GetOnboardingStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusResult.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetOnboardingStatusResult) GetOnboardingStatus() v1.OnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return v1.OnboardingStatus(0)
}

// GetBusinessesParams
type GetBusinessesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetBusinessesParams) Reset() {
	*x = GetBusinessesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessesParams) ProtoMessage() {}

func (x *GetBusinessesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessesParams.ProtoReflect.Descriptor instead.
func (*GetBusinessesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetBusinessesParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

// Business
type AccountingBusinessView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// avatar
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// sync status
	SyncStatus AccountingBusinessView_SyncStatus `protobuf:"varint,4,opt,name=sync_status,json=syncStatus,proto3,enum=moego.api.accounting.v1.AccountingBusinessView_SyncStatus" json:"sync_status,omitempty"`
	// sync time
	SyncTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=sync_time,json=syncTime,proto3" json:"sync_time,omitempty"`
}

func (x *AccountingBusinessView) Reset() {
	*x = AccountingBusinessView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountingBusinessView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountingBusinessView) ProtoMessage() {}

func (x *AccountingBusinessView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountingBusinessView.ProtoReflect.Descriptor instead.
func (*AccountingBusinessView) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{5}
}

func (x *AccountingBusinessView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AccountingBusinessView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountingBusinessView) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AccountingBusinessView) GetSyncStatus() AccountingBusinessView_SyncStatus {
	if x != nil {
		return x.SyncStatus
	}
	return AccountingBusinessView_SYNC_STATUS_UNSPECIFIED
}

func (x *AccountingBusinessView) GetSyncTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SyncTime
	}
	return nil
}

// GetBusinessesResult
type GetBusinessesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if has completed selection
	HasCompletedSelection bool `protobuf:"varint,1,opt,name=has_completed_selection,json=hasCompletedSelection,proto3" json:"has_completed_selection,omitempty"`
	// businesses, only valid when has_completed_selection is true
	Businesses []*AccountingBusinessView `protobuf:"bytes,2,rep,name=businesses,proto3" json:"businesses,omitempty"`
}

func (x *GetBusinessesResult) Reset() {
	*x = GetBusinessesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessesResult) ProtoMessage() {}

func (x *GetBusinessesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessesResult.ProtoReflect.Descriptor instead.
func (*GetBusinessesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetBusinessesResult) GetHasCompletedSelection() bool {
	if x != nil {
		return x.HasCompletedSelection
	}
	return false
}

func (x *GetBusinessesResult) GetBusinesses() []*AccountingBusinessView {
	if x != nil {
		return x.Businesses
	}
	return nil
}

// SetBusinessesParams
type SetBusinessesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
	// business ids
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// legal name
	LegalName string `protobuf:"bytes,4,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	// us state
	State v1.USState `protobuf:"varint,5,opt,name=state,proto3,enum=moego.models.accounting.v1.USState" json:"state,omitempty"`
	// entity type
	EntityType v1.EntityType `protobuf:"varint,6,opt,name=entity_type,json=entityType,proto3,enum=moego.models.accounting.v1.EntityType" json:"entity_type,omitempty"`
}

func (x *SetBusinessesParams) Reset() {
	*x = SetBusinessesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBusinessesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBusinessesParams) ProtoMessage() {}

func (x *SetBusinessesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBusinessesParams.ProtoReflect.Descriptor instead.
func (*SetBusinessesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{7}
}

func (x *SetBusinessesParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

func (x *SetBusinessesParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *SetBusinessesParams) GetLegalName() string {
	if x != nil {
		return x.LegalName
	}
	return ""
}

func (x *SetBusinessesParams) GetState() v1.USState {
	if x != nil {
		return x.State
	}
	return v1.USState(0)
}

func (x *SetBusinessesParams) GetEntityType() v1.EntityType {
	if x != nil {
		return x.EntityType
	}
	return v1.EntityType(0)
}

// SetBusinessesResult
type SetBusinessesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetBusinessesResult) Reset() {
	*x = SetBusinessesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetBusinessesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetBusinessesResult) ProtoMessage() {}

func (x *SetBusinessesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetBusinessesResult.ProtoReflect.Descriptor instead.
func (*SetBusinessesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{8}
}

// AddBusinessesParams
type AddBusinessesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *AddBusinessesParams) Reset() {
	*x = AddBusinessesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBusinessesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBusinessesParams) ProtoMessage() {}

func (x *AddBusinessesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBusinessesParams.ProtoReflect.Descriptor instead.
func (*AddBusinessesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{9}
}

func (x *AddBusinessesParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

func (x *AddBusinessesParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// AddBusinessesResult
type AddBusinessesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddBusinessesResult) Reset() {
	*x = AddBusinessesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBusinessesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBusinessesResult) ProtoMessage() {}

func (x *AddBusinessesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBusinessesResult.ProtoReflect.Descriptor instead.
func (*AddBusinessesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{10}
}

// RemoveBusinessesParams
type RemoveBusinessesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *RemoveBusinessesParams) Reset() {
	*x = RemoveBusinessesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveBusinessesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveBusinessesParams) ProtoMessage() {}

func (x *RemoveBusinessesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveBusinessesParams.ProtoReflect.Descriptor instead.
func (*RemoveBusinessesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{11}
}

func (x *RemoveBusinessesParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

func (x *RemoveBusinessesParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// RemoveBusinessesResult
type RemoveBusinessesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *RemoveBusinessesResult) Reset() {
	*x = RemoveBusinessesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveBusinessesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveBusinessesResult) ProtoMessage() {}

func (x *RemoveBusinessesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveBusinessesResult.ProtoReflect.Descriptor instead.
func (*RemoveBusinessesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{12}
}

func (x *RemoveBusinessesResult) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// GetAuthTokenParams
type GetAuthTokenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel type
	ChannelType v1.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.accounting.v1.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetAuthTokenParams) Reset() {
	*x = GetAuthTokenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthTokenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthTokenParams) ProtoMessage() {}

func (x *GetAuthTokenParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthTokenParams.ProtoReflect.Descriptor instead.
func (*GetAuthTokenParams) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetAuthTokenParams) GetChannelType() v1.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v1.ChannelType(0)
}

// GetAuthTokenResult
type GetAuthTokenResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel business id
	ChannelBusinessId string `protobuf:"bytes,1,opt,name=channel_business_id,json=channelBusinessId,proto3" json:"channel_business_id,omitempty"`
	// token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// expiration time
	ExpirationTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiration_time,json=expirationTime,proto3" json:"expiration_time,omitempty"`
}

func (x *GetAuthTokenResult) Reset() {
	*x = GetAuthTokenResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthTokenResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthTokenResult) ProtoMessage() {}

func (x *GetAuthTokenResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_accounting_v1_accounting_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthTokenResult.ProtoReflect.Descriptor instead.
func (*GetAuthTokenResult) Descriptor() ([]byte, []int) {
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetAuthTokenResult) GetChannelBusinessId() string {
	if x != nil {
		return x.ChannelBusinessId
	}
	return ""
}

func (x *GetAuthTokenResult) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetAuthTokenResult) GetExpirationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpirationTime
	}
	return nil
}

var File_moego_api_accounting_v1_accounting_api_proto protoreflect.FileDescriptor

var file_moego_api_accounting_v1_accounting_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0x87, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x12, 0x56, 0x0a, 0x10, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x22, 0x67, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x76, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x59, 0x0a, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x61, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x4a, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x02,
	0x0a, 0x16, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x5b, 0x0a, 0x0b, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x45, 0x0a, 0x0a, 0x53, 0x79,
	0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x59, 0x4e, 0x43,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x59, 0x4e, 0x43, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x45, 0x44, 0x10,
	0x02, 0x22, 0x9e, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x68, 0x61, 0x73,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x68, 0x61, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4f, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x22, 0xa7, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x65, 0x67,
	0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x53, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x47, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x15, 0x0a, 0x13,
	0x53, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x64,
	0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x87, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x3b, 0x0a, 0x16, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x60, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x43, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x32, 0xe3, 0x06, 0x0a,
	0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x6b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x0d, 0x53,
	0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x52, 0x65, 0x74, 0x72, 0x79, 0x53, 0x79,
	0x6e, 0x63, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x42, 0x81, 0x01, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_accounting_v1_accounting_api_proto_rawDescOnce sync.Once
	file_moego_api_accounting_v1_accounting_api_proto_rawDescData = file_moego_api_accounting_v1_accounting_api_proto_rawDesc
)

func file_moego_api_accounting_v1_accounting_api_proto_rawDescGZIP() []byte {
	file_moego_api_accounting_v1_accounting_api_proto_rawDescOnce.Do(func() {
		file_moego_api_accounting_v1_accounting_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_accounting_v1_accounting_api_proto_rawDescData)
	})
	return file_moego_api_accounting_v1_accounting_api_proto_rawDescData
}

var file_moego_api_accounting_v1_accounting_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_accounting_v1_accounting_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_api_accounting_v1_accounting_api_proto_goTypes = []interface{}{
	(AccountingBusinessView_SyncStatus)(0), // 0: moego.api.accounting.v1.AccountingBusinessView.SyncStatus
	(*GetVisibilityParams)(nil),            // 1: moego.api.accounting.v1.GetVisibilityParams
	(*GetVisibilityResult)(nil),            // 2: moego.api.accounting.v1.GetVisibilityResult
	(*GetOnboardingStatusParams)(nil),      // 3: moego.api.accounting.v1.GetOnboardingStatusParams
	(*GetOnboardingStatusResult)(nil),      // 4: moego.api.accounting.v1.GetOnboardingStatusResult
	(*GetBusinessesParams)(nil),            // 5: moego.api.accounting.v1.GetBusinessesParams
	(*AccountingBusinessView)(nil),         // 6: moego.api.accounting.v1.AccountingBusinessView
	(*GetBusinessesResult)(nil),            // 7: moego.api.accounting.v1.GetBusinessesResult
	(*SetBusinessesParams)(nil),            // 8: moego.api.accounting.v1.SetBusinessesParams
	(*SetBusinessesResult)(nil),            // 9: moego.api.accounting.v1.SetBusinessesResult
	(*AddBusinessesParams)(nil),            // 10: moego.api.accounting.v1.AddBusinessesParams
	(*AddBusinessesResult)(nil),            // 11: moego.api.accounting.v1.AddBusinessesResult
	(*RemoveBusinessesParams)(nil),         // 12: moego.api.accounting.v1.RemoveBusinessesParams
	(*RemoveBusinessesResult)(nil),         // 13: moego.api.accounting.v1.RemoveBusinessesResult
	(*GetAuthTokenParams)(nil),             // 14: moego.api.accounting.v1.GetAuthTokenParams
	(*GetAuthTokenResult)(nil),             // 15: moego.api.accounting.v1.GetAuthTokenResult
	(v1.VisibilityClass)(0),                // 16: moego.models.accounting.v1.VisibilityClass
	(v1.ChannelType)(0),                    // 17: moego.models.accounting.v1.ChannelType
	(v1.OnboardingStatus)(0),               // 18: moego.models.accounting.v1.OnboardingStatus
	(*timestamppb.Timestamp)(nil),          // 19: google.protobuf.Timestamp
	(v1.USState)(0),                        // 20: moego.models.accounting.v1.USState
	(v1.EntityType)(0),                     // 21: moego.models.accounting.v1.EntityType
	(*emptypb.Empty)(nil),                  // 22: google.protobuf.Empty
}
var file_moego_api_accounting_v1_accounting_api_proto_depIdxs = []int32{
	16, // 0: moego.api.accounting.v1.GetVisibilityResult.visibility_class:type_name -> moego.models.accounting.v1.VisibilityClass
	17, // 1: moego.api.accounting.v1.GetOnboardingStatusParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	18, // 2: moego.api.accounting.v1.GetOnboardingStatusResult.onboarding_status:type_name -> moego.models.accounting.v1.OnboardingStatus
	17, // 3: moego.api.accounting.v1.GetBusinessesParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	0,  // 4: moego.api.accounting.v1.AccountingBusinessView.sync_status:type_name -> moego.api.accounting.v1.AccountingBusinessView.SyncStatus
	19, // 5: moego.api.accounting.v1.AccountingBusinessView.sync_time:type_name -> google.protobuf.Timestamp
	6,  // 6: moego.api.accounting.v1.GetBusinessesResult.businesses:type_name -> moego.api.accounting.v1.AccountingBusinessView
	17, // 7: moego.api.accounting.v1.SetBusinessesParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	20, // 8: moego.api.accounting.v1.SetBusinessesParams.state:type_name -> moego.models.accounting.v1.USState
	21, // 9: moego.api.accounting.v1.SetBusinessesParams.entity_type:type_name -> moego.models.accounting.v1.EntityType
	17, // 10: moego.api.accounting.v1.AddBusinessesParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	17, // 11: moego.api.accounting.v1.RemoveBusinessesParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	17, // 12: moego.api.accounting.v1.GetAuthTokenParams.channel_type:type_name -> moego.models.accounting.v1.ChannelType
	19, // 13: moego.api.accounting.v1.GetAuthTokenResult.expiration_time:type_name -> google.protobuf.Timestamp
	1,  // 14: moego.api.accounting.v1.AccountingService.GetVisibility:input_type -> moego.api.accounting.v1.GetVisibilityParams
	3,  // 15: moego.api.accounting.v1.AccountingService.GetOnboardingStatus:input_type -> moego.api.accounting.v1.GetOnboardingStatusParams
	5,  // 16: moego.api.accounting.v1.AccountingService.GetBusinesses:input_type -> moego.api.accounting.v1.GetBusinessesParams
	8,  // 17: moego.api.accounting.v1.AccountingService.SetBusinesses:input_type -> moego.api.accounting.v1.SetBusinessesParams
	10, // 18: moego.api.accounting.v1.AccountingService.AddBusinesses:input_type -> moego.api.accounting.v1.AddBusinessesParams
	12, // 19: moego.api.accounting.v1.AccountingService.RemoveBusinesses:input_type -> moego.api.accounting.v1.RemoveBusinessesParams
	14, // 20: moego.api.accounting.v1.AccountingService.GetAuthToken:input_type -> moego.api.accounting.v1.GetAuthTokenParams
	22, // 21: moego.api.accounting.v1.AccountingService.RetrySync:input_type -> google.protobuf.Empty
	2,  // 22: moego.api.accounting.v1.AccountingService.GetVisibility:output_type -> moego.api.accounting.v1.GetVisibilityResult
	4,  // 23: moego.api.accounting.v1.AccountingService.GetOnboardingStatus:output_type -> moego.api.accounting.v1.GetOnboardingStatusResult
	7,  // 24: moego.api.accounting.v1.AccountingService.GetBusinesses:output_type -> moego.api.accounting.v1.GetBusinessesResult
	9,  // 25: moego.api.accounting.v1.AccountingService.SetBusinesses:output_type -> moego.api.accounting.v1.SetBusinessesResult
	11, // 26: moego.api.accounting.v1.AccountingService.AddBusinesses:output_type -> moego.api.accounting.v1.AddBusinessesResult
	13, // 27: moego.api.accounting.v1.AccountingService.RemoveBusinesses:output_type -> moego.api.accounting.v1.RemoveBusinessesResult
	15, // 28: moego.api.accounting.v1.AccountingService.GetAuthToken:output_type -> moego.api.accounting.v1.GetAuthTokenResult
	22, // 29: moego.api.accounting.v1.AccountingService.RetrySync:output_type -> google.protobuf.Empty
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_api_accounting_v1_accounting_api_proto_init() }
func file_moego_api_accounting_v1_accounting_api_proto_init() {
	if File_moego_api_accounting_v1_accounting_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVisibilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVisibilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountingBusinessView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBusinessesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetBusinessesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBusinessesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBusinessesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveBusinessesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveBusinessesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthTokenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_accounting_v1_accounting_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthTokenResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_accounting_v1_accounting_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_accounting_v1_accounting_api_proto_goTypes,
		DependencyIndexes: file_moego_api_accounting_v1_accounting_api_proto_depIdxs,
		EnumInfos:         file_moego_api_accounting_v1_accounting_api_proto_enumTypes,
		MessageInfos:      file_moego_api_accounting_v1_accounting_api_proto_msgTypes,
	}.Build()
	File_moego_api_accounting_v1_accounting_api_proto = out.File
	file_moego_api_accounting_v1_accounting_api_proto_rawDesc = nil
	file_moego_api_accounting_v1_accounting_api_proto_goTypes = nil
	file_moego_api_accounting_v1_accounting_api_proto_depIdxs = nil
}
