// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_coat_type_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet coat type template params
type ListPetCoatTypeTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetCoatTypeTemplateParams) Reset() {
	*x = ListPetCoatTypeTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypeTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypeTemplateParams) ProtoMessage() {}

func (x *ListPetCoatTypeTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypeTemplateParams.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypeTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{0}
}

// list pet coat type template result
type ListPetCoatTypeTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat types
	CoatTypes []*v1.BusinessPetCoatTypeNameView `protobuf:"bytes,1,rep,name=coat_types,json=coatTypes,proto3" json:"coat_types,omitempty"`
}

func (x *ListPetCoatTypeTemplateResult) Reset() {
	*x = ListPetCoatTypeTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypeTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypeTemplateResult) ProtoMessage() {}

func (x *ListPetCoatTypeTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypeTemplateResult.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypeTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetCoatTypeTemplateResult) GetCoatTypes() []*v1.BusinessPetCoatTypeNameView {
	if x != nil {
		return x.CoatTypes
	}
	return nil
}

// list pet coat type params
type ListPetCoatTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetCoatTypeParams) Reset() {
	*x = ListPetCoatTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypeParams) ProtoMessage() {}

func (x *ListPetCoatTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypeParams.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{2}
}

// list pet coat type result
type ListPetCoatTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type list
	CoatTypes []*v1.BusinessPetCoatTypeModel `protobuf:"bytes,1,rep,name=coat_types,json=coatTypes,proto3" json:"coat_types,omitempty"`
}

func (x *ListPetCoatTypeResult) Reset() {
	*x = ListPetCoatTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCoatTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCoatTypeResult) ProtoMessage() {}

func (x *ListPetCoatTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCoatTypeResult.ProtoReflect.Descriptor instead.
func (*ListPetCoatTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetCoatTypeResult) GetCoatTypes() []*v1.BusinessPetCoatTypeModel {
	if x != nil {
		return x.CoatTypes
	}
	return nil
}

// create pet coat type params
type CreatePetCoatTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type
	CoatType *v1.BusinessPetCoatTypeCreateDef `protobuf:"bytes,1,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
}

func (x *CreatePetCoatTypeParams) Reset() {
	*x = CreatePetCoatTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCoatTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCoatTypeParams) ProtoMessage() {}

func (x *CreatePetCoatTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCoatTypeParams.ProtoReflect.Descriptor instead.
func (*CreatePetCoatTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetCoatTypeParams) GetCoatType() *v1.BusinessPetCoatTypeCreateDef {
	if x != nil {
		return x.CoatType
	}
	return nil
}

// create pet coat type result
type CreatePetCoatTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type
	CoatType *v1.BusinessPetCoatTypeModel `protobuf:"bytes,1,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
}

func (x *CreatePetCoatTypeResult) Reset() {
	*x = CreatePetCoatTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCoatTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCoatTypeResult) ProtoMessage() {}

func (x *CreatePetCoatTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCoatTypeResult.ProtoReflect.Descriptor instead.
func (*CreatePetCoatTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetCoatTypeResult) GetCoatType() *v1.BusinessPetCoatTypeModel {
	if x != nil {
		return x.CoatType
	}
	return nil
}

// update pet coat type params
type UpdatePetCoatTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet coat type
	CoatType *v1.BusinessPetCoatTypeUpdateDef `protobuf:"bytes,2,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
}

func (x *UpdatePetCoatTypeParams) Reset() {
	*x = UpdatePetCoatTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCoatTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCoatTypeParams) ProtoMessage() {}

func (x *UpdatePetCoatTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCoatTypeParams.ProtoReflect.Descriptor instead.
func (*UpdatePetCoatTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetCoatTypeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetCoatTypeParams) GetCoatType() *v1.BusinessPetCoatTypeUpdateDef {
	if x != nil {
		return x.CoatType
	}
	return nil
}

// update pet coat type result
type UpdatePetCoatTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetCoatTypeResult) Reset() {
	*x = UpdatePetCoatTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCoatTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCoatTypeResult) ProtoMessage() {}

func (x *UpdatePetCoatTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCoatTypeResult.ProtoReflect.Descriptor instead.
func (*UpdatePetCoatTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{7}
}

// sort pet coat type params
type SortPetCoatTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type id list, should contain all pet coat type ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetCoatTypeParams) Reset() {
	*x = SortPetCoatTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCoatTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCoatTypeParams) ProtoMessage() {}

func (x *SortPetCoatTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCoatTypeParams.ProtoReflect.Descriptor instead.
func (*SortPetCoatTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{8}
}

func (x *SortPetCoatTypeParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet coat type result
type SortPetCoatTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetCoatTypeResult) Reset() {
	*x = SortPetCoatTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCoatTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCoatTypeResult) ProtoMessage() {}

func (x *SortPetCoatTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCoatTypeResult.ProtoReflect.Descriptor instead.
func (*SortPetCoatTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{9}
}

// delete pet coat type params
type DeletePetCoatTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet coat type id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetCoatTypeParams) Reset() {
	*x = DeletePetCoatTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCoatTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCoatTypeParams) ProtoMessage() {}

func (x *DeletePetCoatTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCoatTypeParams.ProtoReflect.Descriptor instead.
func (*DeletePetCoatTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePetCoatTypeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet coat type result
type DeletePetCoatTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetCoatTypeResult) Reset() {
	*x = DeletePetCoatTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCoatTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCoatTypeResult) ProtoMessage() {}

func (x *DeletePetCoatTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCoatTypeResult.ProtoReflect.Descriptor instead.
func (*DeletePetCoatTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_business_customer_v1_business_pet_coat_type_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1f, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x7e, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x0a, 0x63, 0x6f, 0x61, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x63, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0x73, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x0a, 0x63, 0x6f, 0x61,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x63, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x66, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x73, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9a,
	0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x66, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x19, 0x0a, 0x17, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x39, 0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x32, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x19,
	0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xd0, 0x06, 0x0a, 0x1a, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a,
	0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_goTypes = []interface{}{
	(*ListPetCoatTypeTemplateParams)(nil),   // 0: moego.api.business_customer.v1.ListPetCoatTypeTemplateParams
	(*ListPetCoatTypeTemplateResult)(nil),   // 1: moego.api.business_customer.v1.ListPetCoatTypeTemplateResult
	(*ListPetCoatTypeParams)(nil),           // 2: moego.api.business_customer.v1.ListPetCoatTypeParams
	(*ListPetCoatTypeResult)(nil),           // 3: moego.api.business_customer.v1.ListPetCoatTypeResult
	(*CreatePetCoatTypeParams)(nil),         // 4: moego.api.business_customer.v1.CreatePetCoatTypeParams
	(*CreatePetCoatTypeResult)(nil),         // 5: moego.api.business_customer.v1.CreatePetCoatTypeResult
	(*UpdatePetCoatTypeParams)(nil),         // 6: moego.api.business_customer.v1.UpdatePetCoatTypeParams
	(*UpdatePetCoatTypeResult)(nil),         // 7: moego.api.business_customer.v1.UpdatePetCoatTypeResult
	(*SortPetCoatTypeParams)(nil),           // 8: moego.api.business_customer.v1.SortPetCoatTypeParams
	(*SortPetCoatTypeResult)(nil),           // 9: moego.api.business_customer.v1.SortPetCoatTypeResult
	(*DeletePetCoatTypeParams)(nil),         // 10: moego.api.business_customer.v1.DeletePetCoatTypeParams
	(*DeletePetCoatTypeResult)(nil),         // 11: moego.api.business_customer.v1.DeletePetCoatTypeResult
	(*v1.BusinessPetCoatTypeNameView)(nil),  // 12: moego.models.business_customer.v1.BusinessPetCoatTypeNameView
	(*v1.BusinessPetCoatTypeModel)(nil),     // 13: moego.models.business_customer.v1.BusinessPetCoatTypeModel
	(*v1.BusinessPetCoatTypeCreateDef)(nil), // 14: moego.models.business_customer.v1.BusinessPetCoatTypeCreateDef
	(*v1.BusinessPetCoatTypeUpdateDef)(nil), // 15: moego.models.business_customer.v1.BusinessPetCoatTypeUpdateDef
}
var file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_depIdxs = []int32{
	12, // 0: moego.api.business_customer.v1.ListPetCoatTypeTemplateResult.coat_types:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeNameView
	13, // 1: moego.api.business_customer.v1.ListPetCoatTypeResult.coat_types:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeModel
	14, // 2: moego.api.business_customer.v1.CreatePetCoatTypeParams.coat_type:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeCreateDef
	13, // 3: moego.api.business_customer.v1.CreatePetCoatTypeResult.coat_type:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeModel
	15, // 4: moego.api.business_customer.v1.UpdatePetCoatTypeParams.coat_type:type_name -> moego.models.business_customer.v1.BusinessPetCoatTypeUpdateDef
	0,  // 5: moego.api.business_customer.v1.BusinessPetCoatTypeService.ListPetCoatTypeTemplate:input_type -> moego.api.business_customer.v1.ListPetCoatTypeTemplateParams
	2,  // 6: moego.api.business_customer.v1.BusinessPetCoatTypeService.ListPetCoatType:input_type -> moego.api.business_customer.v1.ListPetCoatTypeParams
	4,  // 7: moego.api.business_customer.v1.BusinessPetCoatTypeService.CreatePetCoatType:input_type -> moego.api.business_customer.v1.CreatePetCoatTypeParams
	6,  // 8: moego.api.business_customer.v1.BusinessPetCoatTypeService.UpdatePetCoatType:input_type -> moego.api.business_customer.v1.UpdatePetCoatTypeParams
	8,  // 9: moego.api.business_customer.v1.BusinessPetCoatTypeService.SortPetCoatType:input_type -> moego.api.business_customer.v1.SortPetCoatTypeParams
	10, // 10: moego.api.business_customer.v1.BusinessPetCoatTypeService.DeletePetCoatType:input_type -> moego.api.business_customer.v1.DeletePetCoatTypeParams
	1,  // 11: moego.api.business_customer.v1.BusinessPetCoatTypeService.ListPetCoatTypeTemplate:output_type -> moego.api.business_customer.v1.ListPetCoatTypeTemplateResult
	3,  // 12: moego.api.business_customer.v1.BusinessPetCoatTypeService.ListPetCoatType:output_type -> moego.api.business_customer.v1.ListPetCoatTypeResult
	5,  // 13: moego.api.business_customer.v1.BusinessPetCoatTypeService.CreatePetCoatType:output_type -> moego.api.business_customer.v1.CreatePetCoatTypeResult
	7,  // 14: moego.api.business_customer.v1.BusinessPetCoatTypeService.UpdatePetCoatType:output_type -> moego.api.business_customer.v1.UpdatePetCoatTypeResult
	9,  // 15: moego.api.business_customer.v1.BusinessPetCoatTypeService.SortPetCoatType:output_type -> moego.api.business_customer.v1.SortPetCoatTypeResult
	11, // 16: moego.api.business_customer.v1.BusinessPetCoatTypeService.DeletePetCoatType:output_type -> moego.api.business_customer.v1.DeletePetCoatTypeResult
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_coat_type_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypeTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypeTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCoatTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCoatTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCoatTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCoatTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCoatTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCoatTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCoatTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCoatTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCoatTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_coat_type_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_coat_type_api_proto_depIdxs = nil
}
