// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_customer_tag_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerTagServiceClient is the client API for BusinessCustomerTagService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerTagServiceClient interface {
	// List customer tags of current company
	ListCustomerTag(ctx context.Context, in *ListCustomerTagParams, opts ...grpc.CallOption) (*ListCustomerTagResult, error)
	// List customer tag template
	ListCustomerTagTemplate(ctx context.Context, in *ListCustomerTagTemplateParams, opts ...grpc.CallOption) (*ListCustomerTagTemplateResult, error)
	// Create a customer tag
	CreateCustomerTag(ctx context.Context, in *CreateCustomerTagParams, opts ...grpc.CallOption) (*CreateCustomerTagResult, error)
	// Update a customer tag
	UpdateCustomerTag(ctx context.Context, in *UpdateCustomerTagParams, opts ...grpc.CallOption) (*UpdateCustomerTagResult, error)
	// Sort customer tags
	SortCustomerTag(ctx context.Context, in *SortCustomerTagParams, opts ...grpc.CallOption) (*SortCustomerTagResult, error)
	// Delete a customer tag
	DeleteCustomerTag(ctx context.Context, in *DeleteCustomerTagParams, opts ...grpc.CallOption) (*DeleteCustomerTagResult, error)
}

type businessCustomerTagServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerTagServiceClient(cc grpc.ClientConnInterface) BusinessCustomerTagServiceClient {
	return &businessCustomerTagServiceClient{cc}
}

func (c *businessCustomerTagServiceClient) ListCustomerTag(ctx context.Context, in *ListCustomerTagParams, opts ...grpc.CallOption) (*ListCustomerTagResult, error) {
	out := new(ListCustomerTagResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerTagServiceClient) ListCustomerTagTemplate(ctx context.Context, in *ListCustomerTagTemplateParams, opts ...grpc.CallOption) (*ListCustomerTagTemplateResult, error) {
	out := new(ListCustomerTagTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTagTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerTagServiceClient) CreateCustomerTag(ctx context.Context, in *CreateCustomerTagParams, opts ...grpc.CallOption) (*CreateCustomerTagResult, error) {
	out := new(CreateCustomerTagResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/CreateCustomerTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerTagServiceClient) UpdateCustomerTag(ctx context.Context, in *UpdateCustomerTagParams, opts ...grpc.CallOption) (*UpdateCustomerTagResult, error) {
	out := new(UpdateCustomerTagResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/UpdateCustomerTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerTagServiceClient) SortCustomerTag(ctx context.Context, in *SortCustomerTagParams, opts ...grpc.CallOption) (*SortCustomerTagResult, error) {
	out := new(SortCustomerTagResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/SortCustomerTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerTagServiceClient) DeleteCustomerTag(ctx context.Context, in *DeleteCustomerTagParams, opts ...grpc.CallOption) (*DeleteCustomerTagResult, error) {
	out := new(DeleteCustomerTagResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerTagService/DeleteCustomerTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerTagServiceServer is the server API for BusinessCustomerTagService service.
// All implementations must embed UnimplementedBusinessCustomerTagServiceServer
// for forward compatibility
type BusinessCustomerTagServiceServer interface {
	// List customer tags of current company
	ListCustomerTag(context.Context, *ListCustomerTagParams) (*ListCustomerTagResult, error)
	// List customer tag template
	ListCustomerTagTemplate(context.Context, *ListCustomerTagTemplateParams) (*ListCustomerTagTemplateResult, error)
	// Create a customer tag
	CreateCustomerTag(context.Context, *CreateCustomerTagParams) (*CreateCustomerTagResult, error)
	// Update a customer tag
	UpdateCustomerTag(context.Context, *UpdateCustomerTagParams) (*UpdateCustomerTagResult, error)
	// Sort customer tags
	SortCustomerTag(context.Context, *SortCustomerTagParams) (*SortCustomerTagResult, error)
	// Delete a customer tag
	DeleteCustomerTag(context.Context, *DeleteCustomerTagParams) (*DeleteCustomerTagResult, error)
	mustEmbedUnimplementedBusinessCustomerTagServiceServer()
}

// UnimplementedBusinessCustomerTagServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerTagServiceServer struct {
}

func (UnimplementedBusinessCustomerTagServiceServer) ListCustomerTag(context.Context, *ListCustomerTagParams) (*ListCustomerTagResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerTag not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) ListCustomerTagTemplate(context.Context, *ListCustomerTagTemplateParams) (*ListCustomerTagTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerTagTemplate not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) CreateCustomerTag(context.Context, *CreateCustomerTagParams) (*CreateCustomerTagResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerTag not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) UpdateCustomerTag(context.Context, *UpdateCustomerTagParams) (*UpdateCustomerTagResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerTag not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) SortCustomerTag(context.Context, *SortCustomerTagParams) (*SortCustomerTagResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortCustomerTag not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) DeleteCustomerTag(context.Context, *DeleteCustomerTagParams) (*DeleteCustomerTagResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerTag not implemented")
}
func (UnimplementedBusinessCustomerTagServiceServer) mustEmbedUnimplementedBusinessCustomerTagServiceServer() {
}

// UnsafeBusinessCustomerTagServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerTagServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerTagServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerTagServiceServer()
}

func RegisterBusinessCustomerTagServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerTagServiceServer) {
	s.RegisterService(&BusinessCustomerTagService_ServiceDesc, srv)
}

func _BusinessCustomerTagService_ListCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerTagParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).ListCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).ListCustomerTag(ctx, req.(*ListCustomerTagParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerTagService_ListCustomerTagTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerTagTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).ListCustomerTagTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTagTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).ListCustomerTagTemplate(ctx, req.(*ListCustomerTagTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerTagService_CreateCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerTagParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).CreateCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/CreateCustomerTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).CreateCustomerTag(ctx, req.(*CreateCustomerTagParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerTagService_UpdateCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerTagParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).UpdateCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/UpdateCustomerTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).UpdateCustomerTag(ctx, req.(*UpdateCustomerTagParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerTagService_SortCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortCustomerTagParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).SortCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/SortCustomerTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).SortCustomerTag(ctx, req.(*SortCustomerTagParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerTagService_DeleteCustomerTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerTagParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerTagServiceServer).DeleteCustomerTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerTagService/DeleteCustomerTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerTagServiceServer).DeleteCustomerTag(ctx, req.(*DeleteCustomerTagParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerTagService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerTagService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerTagService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessCustomerTagService",
	HandlerType: (*BusinessCustomerTagServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCustomerTag",
			Handler:    _BusinessCustomerTagService_ListCustomerTag_Handler,
		},
		{
			MethodName: "ListCustomerTagTemplate",
			Handler:    _BusinessCustomerTagService_ListCustomerTagTemplate_Handler,
		},
		{
			MethodName: "CreateCustomerTag",
			Handler:    _BusinessCustomerTagService_CreateCustomerTag_Handler,
		},
		{
			MethodName: "UpdateCustomerTag",
			Handler:    _BusinessCustomerTagService_UpdateCustomerTag_Handler,
		},
		{
			MethodName: "SortCustomerTag",
			Handler:    _BusinessCustomerTagService_SortCustomerTag_Handler,
		},
		{
			MethodName: "DeleteCustomerTag",
			Handler:    _BusinessCustomerTagService_DeleteCustomerTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_customer_tag_api.proto",
}
