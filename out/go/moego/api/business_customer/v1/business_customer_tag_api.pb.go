// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_customer_tag_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list customer tag params
type ListCustomerTagParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListCustomerTagParams) Reset() {
	*x = ListCustomerTagParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerTagParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagParams) ProtoMessage() {}

func (x *ListCustomerTagParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagParams.ProtoReflect.Descriptor instead.
func (*ListCustomerTagParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{0}
}

// list customer tag result
type ListCustomerTagResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tag list
	Tags []*v1.BusinessCustomerTagModel `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *ListCustomerTagResult) Reset() {
	*x = ListCustomerTagResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerTagResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagResult) ProtoMessage() {}

func (x *ListCustomerTagResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagResult.ProtoReflect.Descriptor instead.
func (*ListCustomerTagResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListCustomerTagResult) GetTags() []*v1.BusinessCustomerTagModel {
	if x != nil {
		return x.Tags
	}
	return nil
}

// list customer tag template params
type ListCustomerTagTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListCustomerTagTemplateParams) Reset() {
	*x = ListCustomerTagTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerTagTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagTemplateParams) ProtoMessage() {}

func (x *ListCustomerTagTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagTemplateParams.ProtoReflect.Descriptor instead.
func (*ListCustomerTagTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{2}
}

// list customer tag template result
type ListCustomerTagTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tag list
	Tags []*v1.BusinessCustomerTagNameView `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *ListCustomerTagTemplateResult) Reset() {
	*x = ListCustomerTagTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomerTagTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagTemplateResult) ProtoMessage() {}

func (x *ListCustomerTagTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagTemplateResult.ProtoReflect.Descriptor instead.
func (*ListCustomerTagTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListCustomerTagTemplateResult) GetTags() []*v1.BusinessCustomerTagNameView {
	if x != nil {
		return x.Tags
	}
	return nil
}

// create customer tag params
type CreateCustomerTagParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag
	Tag *v1.BusinessCustomerTagCreateDef `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *CreateCustomerTagParams) Reset() {
	*x = CreateCustomerTagParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerTagParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTagParams) ProtoMessage() {}

func (x *CreateCustomerTagParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTagParams.ProtoReflect.Descriptor instead.
func (*CreateCustomerTagParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateCustomerTagParams) GetTag() *v1.BusinessCustomerTagCreateDef {
	if x != nil {
		return x.Tag
	}
	return nil
}

// create customer tag result
type CreateCustomerTagResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag
	Tag *v1.BusinessCustomerTagModel `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *CreateCustomerTagResult) Reset() {
	*x = CreateCustomerTagResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerTagResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTagResult) ProtoMessage() {}

func (x *CreateCustomerTagResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTagResult.ProtoReflect.Descriptor instead.
func (*CreateCustomerTagResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateCustomerTagResult) GetTag() *v1.BusinessCustomerTagModel {
	if x != nil {
		return x.Tag
	}
	return nil
}

// update customer tag params
type UpdateCustomerTagParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer tag
	Tag *v1.BusinessCustomerTagUpdateDef `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *UpdateCustomerTagParams) Reset() {
	*x = UpdateCustomerTagParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerTagParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTagParams) ProtoMessage() {}

func (x *UpdateCustomerTagParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTagParams.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTagParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCustomerTagParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerTagParams) GetTag() *v1.BusinessCustomerTagUpdateDef {
	if x != nil {
		return x.Tag
	}
	return nil
}

// update customer tag result
type UpdateCustomerTagResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCustomerTagResult) Reset() {
	*x = UpdateCustomerTagResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerTagResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTagResult) ProtoMessage() {}

func (x *UpdateCustomerTagResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTagResult.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTagResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{7}
}

// sort customer tag params
type SortCustomerTagParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag id list, should contain all customer tag ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortCustomerTagParams) Reset() {
	*x = SortCustomerTagParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCustomerTagParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCustomerTagParams) ProtoMessage() {}

func (x *SortCustomerTagParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCustomerTagParams.ProtoReflect.Descriptor instead.
func (*SortCustomerTagParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{8}
}

func (x *SortCustomerTagParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort customer tag result
type SortCustomerTagResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortCustomerTagResult) Reset() {
	*x = SortCustomerTagResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCustomerTagResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCustomerTagResult) ProtoMessage() {}

func (x *SortCustomerTagResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCustomerTagResult.ProtoReflect.Descriptor instead.
func (*SortCustomerTagResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{9}
}

// delete customer tag params
type DeleteCustomerTagParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer tag id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteCustomerTagParams) Reset() {
	*x = DeleteCustomerTagParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomerTagParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerTagParams) ProtoMessage() {}

func (x *DeleteCustomerTagParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerTagParams.ProtoReflect.Descriptor instead.
func (*DeleteCustomerTagParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCustomerTagParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete customer tag result
type DeleteCustomerTagResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteCustomerTagResult) Reset() {
	*x = DeleteCustomerTagResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomerTagResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerTagResult) ProtoMessage() {}

func (x *DeleteCustomerTagResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerTagResult.ProtoReflect.Descriptor instead.
func (*DeleteCustomerTagResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_business_customer_v1_business_customer_tag_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x17, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x68, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x73, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x76, 0x0a, 0x17,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5b, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x74, 0x61, 0x67, 0x22, 0x68, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x4d, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0x8f,
	0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x5b, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x22, 0x19, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x39, 0x0a, 0x15, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x32, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xd0,
	0x06, 0x0a, 0x1a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a,
	0x0f, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97,
	0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x85, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x53, 0x6f, 0x72, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescData = file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_business_customer_v1_business_customer_tag_api_proto_goTypes = []interface{}{
	(*ListCustomerTagParams)(nil),           // 0: moego.api.business_customer.v1.ListCustomerTagParams
	(*ListCustomerTagResult)(nil),           // 1: moego.api.business_customer.v1.ListCustomerTagResult
	(*ListCustomerTagTemplateParams)(nil),   // 2: moego.api.business_customer.v1.ListCustomerTagTemplateParams
	(*ListCustomerTagTemplateResult)(nil),   // 3: moego.api.business_customer.v1.ListCustomerTagTemplateResult
	(*CreateCustomerTagParams)(nil),         // 4: moego.api.business_customer.v1.CreateCustomerTagParams
	(*CreateCustomerTagResult)(nil),         // 5: moego.api.business_customer.v1.CreateCustomerTagResult
	(*UpdateCustomerTagParams)(nil),         // 6: moego.api.business_customer.v1.UpdateCustomerTagParams
	(*UpdateCustomerTagResult)(nil),         // 7: moego.api.business_customer.v1.UpdateCustomerTagResult
	(*SortCustomerTagParams)(nil),           // 8: moego.api.business_customer.v1.SortCustomerTagParams
	(*SortCustomerTagResult)(nil),           // 9: moego.api.business_customer.v1.SortCustomerTagResult
	(*DeleteCustomerTagParams)(nil),         // 10: moego.api.business_customer.v1.DeleteCustomerTagParams
	(*DeleteCustomerTagResult)(nil),         // 11: moego.api.business_customer.v1.DeleteCustomerTagResult
	(*v1.BusinessCustomerTagModel)(nil),     // 12: moego.models.business_customer.v1.BusinessCustomerTagModel
	(*v1.BusinessCustomerTagNameView)(nil),  // 13: moego.models.business_customer.v1.BusinessCustomerTagNameView
	(*v1.BusinessCustomerTagCreateDef)(nil), // 14: moego.models.business_customer.v1.BusinessCustomerTagCreateDef
	(*v1.BusinessCustomerTagUpdateDef)(nil), // 15: moego.models.business_customer.v1.BusinessCustomerTagUpdateDef
}
var file_moego_api_business_customer_v1_business_customer_tag_api_proto_depIdxs = []int32{
	12, // 0: moego.api.business_customer.v1.ListCustomerTagResult.tags:type_name -> moego.models.business_customer.v1.BusinessCustomerTagModel
	13, // 1: moego.api.business_customer.v1.ListCustomerTagTemplateResult.tags:type_name -> moego.models.business_customer.v1.BusinessCustomerTagNameView
	14, // 2: moego.api.business_customer.v1.CreateCustomerTagParams.tag:type_name -> moego.models.business_customer.v1.BusinessCustomerTagCreateDef
	12, // 3: moego.api.business_customer.v1.CreateCustomerTagResult.tag:type_name -> moego.models.business_customer.v1.BusinessCustomerTagModel
	15, // 4: moego.api.business_customer.v1.UpdateCustomerTagParams.tag:type_name -> moego.models.business_customer.v1.BusinessCustomerTagUpdateDef
	0,  // 5: moego.api.business_customer.v1.BusinessCustomerTagService.ListCustomerTag:input_type -> moego.api.business_customer.v1.ListCustomerTagParams
	2,  // 6: moego.api.business_customer.v1.BusinessCustomerTagService.ListCustomerTagTemplate:input_type -> moego.api.business_customer.v1.ListCustomerTagTemplateParams
	4,  // 7: moego.api.business_customer.v1.BusinessCustomerTagService.CreateCustomerTag:input_type -> moego.api.business_customer.v1.CreateCustomerTagParams
	6,  // 8: moego.api.business_customer.v1.BusinessCustomerTagService.UpdateCustomerTag:input_type -> moego.api.business_customer.v1.UpdateCustomerTagParams
	8,  // 9: moego.api.business_customer.v1.BusinessCustomerTagService.SortCustomerTag:input_type -> moego.api.business_customer.v1.SortCustomerTagParams
	10, // 10: moego.api.business_customer.v1.BusinessCustomerTagService.DeleteCustomerTag:input_type -> moego.api.business_customer.v1.DeleteCustomerTagParams
	1,  // 11: moego.api.business_customer.v1.BusinessCustomerTagService.ListCustomerTag:output_type -> moego.api.business_customer.v1.ListCustomerTagResult
	3,  // 12: moego.api.business_customer.v1.BusinessCustomerTagService.ListCustomerTagTemplate:output_type -> moego.api.business_customer.v1.ListCustomerTagTemplateResult
	5,  // 13: moego.api.business_customer.v1.BusinessCustomerTagService.CreateCustomerTag:output_type -> moego.api.business_customer.v1.CreateCustomerTagResult
	7,  // 14: moego.api.business_customer.v1.BusinessCustomerTagService.UpdateCustomerTag:output_type -> moego.api.business_customer.v1.UpdateCustomerTagResult
	9,  // 15: moego.api.business_customer.v1.BusinessCustomerTagService.SortCustomerTag:output_type -> moego.api.business_customer.v1.SortCustomerTagResult
	11, // 16: moego.api.business_customer.v1.BusinessCustomerTagService.DeleteCustomerTag:output_type -> moego.api.business_customer.v1.DeleteCustomerTagResult
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_customer_tag_api_proto_init() }
func file_moego_api_business_customer_v1_business_customer_tag_api_proto_init() {
	if File_moego_api_business_customer_v1_business_customer_tag_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerTagParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerTagResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerTagTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomerTagTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerTagParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerTagResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerTagParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerTagResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCustomerTagParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCustomerTagResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomerTagParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomerTagResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_customer_tag_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_customer_tag_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_customer_tag_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_customer_tag_api_proto = out.File
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_customer_tag_api_proto_depIdxs = nil
}
