// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_coat_type_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetCoatTypeServiceClient is the client API for BusinessPetCoatTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetCoatTypeServiceClient interface {
	// List pet coat type template
	ListPetCoatTypeTemplate(ctx context.Context, in *ListPetCoatTypeTemplateParams, opts ...grpc.CallOption) (*ListPetCoatTypeTemplateResult, error)
	// List pet coat type of current company
	ListPetCoatType(ctx context.Context, in *ListPetCoatTypeParams, opts ...grpc.CallOption) (*ListPetCoatTypeResult, error)
	// Create a coat type
	CreatePetCoatType(ctx context.Context, in *CreatePetCoatTypeParams, opts ...grpc.CallOption) (*CreatePetCoatTypeResult, error)
	// Update a coat type
	UpdatePetCoatType(ctx context.Context, in *UpdatePetCoatTypeParams, opts ...grpc.CallOption) (*UpdatePetCoatTypeResult, error)
	// Sort coat types
	SortPetCoatType(ctx context.Context, in *SortPetCoatTypeParams, opts ...grpc.CallOption) (*SortPetCoatTypeResult, error)
	// Delete a coat type
	DeletePetCoatType(ctx context.Context, in *DeletePetCoatTypeParams, opts ...grpc.CallOption) (*DeletePetCoatTypeResult, error)
}

type businessPetCoatTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetCoatTypeServiceClient(cc grpc.ClientConnInterface) BusinessPetCoatTypeServiceClient {
	return &businessPetCoatTypeServiceClient{cc}
}

func (c *businessPetCoatTypeServiceClient) ListPetCoatTypeTemplate(ctx context.Context, in *ListPetCoatTypeTemplateParams, opts ...grpc.CallOption) (*ListPetCoatTypeTemplateResult, error) {
	out := new(ListPetCoatTypeTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatTypeTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) ListPetCoatType(ctx context.Context, in *ListPetCoatTypeParams, opts ...grpc.CallOption) (*ListPetCoatTypeResult, error) {
	out := new(ListPetCoatTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) CreatePetCoatType(ctx context.Context, in *CreatePetCoatTypeParams, opts ...grpc.CallOption) (*CreatePetCoatTypeResult, error) {
	out := new(CreatePetCoatTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/CreatePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) UpdatePetCoatType(ctx context.Context, in *UpdatePetCoatTypeParams, opts ...grpc.CallOption) (*UpdatePetCoatTypeResult, error) {
	out := new(UpdatePetCoatTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/UpdatePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) SortPetCoatType(ctx context.Context, in *SortPetCoatTypeParams, opts ...grpc.CallOption) (*SortPetCoatTypeResult, error) {
	out := new(SortPetCoatTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/SortPetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) DeletePetCoatType(ctx context.Context, in *DeletePetCoatTypeParams, opts ...grpc.CallOption) (*DeletePetCoatTypeResult, error) {
	out := new(DeletePetCoatTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetCoatTypeService/DeletePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetCoatTypeServiceServer is the server API for BusinessPetCoatTypeService service.
// All implementations must embed UnimplementedBusinessPetCoatTypeServiceServer
// for forward compatibility
type BusinessPetCoatTypeServiceServer interface {
	// List pet coat type template
	ListPetCoatTypeTemplate(context.Context, *ListPetCoatTypeTemplateParams) (*ListPetCoatTypeTemplateResult, error)
	// List pet coat type of current company
	ListPetCoatType(context.Context, *ListPetCoatTypeParams) (*ListPetCoatTypeResult, error)
	// Create a coat type
	CreatePetCoatType(context.Context, *CreatePetCoatTypeParams) (*CreatePetCoatTypeResult, error)
	// Update a coat type
	UpdatePetCoatType(context.Context, *UpdatePetCoatTypeParams) (*UpdatePetCoatTypeResult, error)
	// Sort coat types
	SortPetCoatType(context.Context, *SortPetCoatTypeParams) (*SortPetCoatTypeResult, error)
	// Delete a coat type
	DeletePetCoatType(context.Context, *DeletePetCoatTypeParams) (*DeletePetCoatTypeResult, error)
	mustEmbedUnimplementedBusinessPetCoatTypeServiceServer()
}

// UnimplementedBusinessPetCoatTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetCoatTypeServiceServer struct {
}

func (UnimplementedBusinessPetCoatTypeServiceServer) ListPetCoatTypeTemplate(context.Context, *ListPetCoatTypeTemplateParams) (*ListPetCoatTypeTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCoatTypeTemplate not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) ListPetCoatType(context.Context, *ListPetCoatTypeParams) (*ListPetCoatTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) CreatePetCoatType(context.Context, *CreatePetCoatTypeParams) (*CreatePetCoatTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) UpdatePetCoatType(context.Context, *UpdatePetCoatTypeParams) (*UpdatePetCoatTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) SortPetCoatType(context.Context, *SortPetCoatTypeParams) (*SortPetCoatTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) DeletePetCoatType(context.Context, *DeletePetCoatTypeParams) (*DeletePetCoatTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) mustEmbedUnimplementedBusinessPetCoatTypeServiceServer() {
}

// UnsafeBusinessPetCoatTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetCoatTypeServiceServer will
// result in compilation errors.
type UnsafeBusinessPetCoatTypeServiceServer interface {
	mustEmbedUnimplementedBusinessPetCoatTypeServiceServer()
}

func RegisterBusinessPetCoatTypeServiceServer(s grpc.ServiceRegistrar, srv BusinessPetCoatTypeServiceServer) {
	s.RegisterService(&BusinessPetCoatTypeService_ServiceDesc, srv)
}

func _BusinessPetCoatTypeService_ListPetCoatTypeTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCoatTypeTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatTypeTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatTypeTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatTypeTemplate(ctx, req.(*ListPetCoatTypeTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_ListPetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCoatTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatType(ctx, req.(*ListPetCoatTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_CreatePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetCoatTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).CreatePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/CreatePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).CreatePetCoatType(ctx, req.(*CreatePetCoatTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_UpdatePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetCoatTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).UpdatePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/UpdatePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).UpdatePetCoatType(ctx, req.(*UpdatePetCoatTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_SortPetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetCoatTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).SortPetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/SortPetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).SortPetCoatType(ctx, req.(*SortPetCoatTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_DeletePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetCoatTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).DeletePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetCoatTypeService/DeletePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).DeletePetCoatType(ctx, req.(*DeletePetCoatTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetCoatTypeService_ServiceDesc is the grpc.ServiceDesc for BusinessPetCoatTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetCoatTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetCoatTypeService",
	HandlerType: (*BusinessPetCoatTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPetCoatTypeTemplate",
			Handler:    _BusinessPetCoatTypeService_ListPetCoatTypeTemplate_Handler,
		},
		{
			MethodName: "ListPetCoatType",
			Handler:    _BusinessPetCoatTypeService_ListPetCoatType_Handler,
		},
		{
			MethodName: "CreatePetCoatType",
			Handler:    _BusinessPetCoatTypeService_CreatePetCoatType_Handler,
		},
		{
			MethodName: "UpdatePetCoatType",
			Handler:    _BusinessPetCoatTypeService_UpdatePetCoatType_Handler,
		},
		{
			MethodName: "SortPetCoatType",
			Handler:    _BusinessPetCoatTypeService_SortPetCoatType_Handler,
		},
		{
			MethodName: "DeletePetCoatType",
			Handler:    _BusinessPetCoatTypeService_DeletePetCoatType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_coat_type_api.proto",
}
