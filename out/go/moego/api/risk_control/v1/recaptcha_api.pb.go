// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/risk_control/v1/recaptcha_api.proto

package riskcontrolapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// challenge request
type RecaptchaChallengeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the recaptcha challenge def
	Recaptcha *v1.RecaptchaDef `protobuf:"bytes,1,opt,name=recaptcha,proto3" json:"recaptcha,omitempty"`
}

func (x *RecaptchaChallengeRequest) Reset() {
	*x = RecaptchaChallengeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaChallengeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaChallengeRequest) ProtoMessage() {}

func (x *RecaptchaChallengeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaChallengeRequest.ProtoReflect.Descriptor instead.
func (*RecaptchaChallengeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescGZIP(), []int{0}
}

func (x *RecaptchaChallengeRequest) GetRecaptcha() *v1.RecaptchaDef {
	if x != nil {
		return x.Recaptcha
	}
	return nil
}

// challenge response
type RecaptchaChallengeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RecaptchaChallengeResponse) Reset() {
	*x = RecaptchaChallengeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaChallengeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaChallengeResponse) ProtoMessage() {}

func (x *RecaptchaChallengeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaChallengeResponse.ProtoReflect.Descriptor instead.
func (*RecaptchaChallengeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescGZIP(), []int{1}
}

var File_moego_api_risk_control_v1_recaptcha_api_proto protoreflect.FileDescriptor

var file_moego_api_risk_control_v1_recaptcha_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6f, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x52, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x72, 0x65,
	0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x22, 0x1c, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x8c, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x78, 0x0a, 0x09, 0x43, 0x68,
	0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x43, 0x68, 0x61,
	0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x86, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x69, 0x73,
	0x6b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescOnce sync.Once
	file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescData = file_moego_api_risk_control_v1_recaptcha_api_proto_rawDesc
)

func file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescGZIP() []byte {
	file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescOnce.Do(func() {
		file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescData)
	})
	return file_moego_api_risk_control_v1_recaptcha_api_proto_rawDescData
}

var file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_risk_control_v1_recaptcha_api_proto_goTypes = []interface{}{
	(*RecaptchaChallengeRequest)(nil),  // 0: moego.api.risk_control.v1.RecaptchaChallengeRequest
	(*RecaptchaChallengeResponse)(nil), // 1: moego.api.risk_control.v1.RecaptchaChallengeResponse
	(*v1.RecaptchaDef)(nil),            // 2: moego.models.risk_control.v1.RecaptchaDef
}
var file_moego_api_risk_control_v1_recaptcha_api_proto_depIdxs = []int32{
	2, // 0: moego.api.risk_control.v1.RecaptchaChallengeRequest.recaptcha:type_name -> moego.models.risk_control.v1.RecaptchaDef
	0, // 1: moego.api.risk_control.v1.RecaptchaService.Challenge:input_type -> moego.api.risk_control.v1.RecaptchaChallengeRequest
	1, // 2: moego.api.risk_control.v1.RecaptchaService.Challenge:output_type -> moego.api.risk_control.v1.RecaptchaChallengeResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_risk_control_v1_recaptcha_api_proto_init() }
func file_moego_api_risk_control_v1_recaptcha_api_proto_init() {
	if File_moego_api_risk_control_v1_recaptcha_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaChallengeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaChallengeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_risk_control_v1_recaptcha_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_risk_control_v1_recaptcha_api_proto_goTypes,
		DependencyIndexes: file_moego_api_risk_control_v1_recaptcha_api_proto_depIdxs,
		MessageInfos:      file_moego_api_risk_control_v1_recaptcha_api_proto_msgTypes,
	}.Build()
	File_moego_api_risk_control_v1_recaptcha_api_proto = out.File
	file_moego_api_risk_control_v1_recaptcha_api_proto_rawDesc = nil
	file_moego_api_risk_control_v1_recaptcha_api_proto_goTypes = nil
	file_moego_api_risk_control_v1_recaptcha_api_proto_depIdxs = nil
}
