// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/branded_app/v1/branded_app_api.proto

package brandedAppApiV1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BrandedAppServiceClient is the client API for BrandedAppService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BrandedAppServiceClient interface {
	// get branded_app
	GetBrandedAppConfig(ctx context.Context, in *GetBrandedAppConfigParams, opts ...grpc.CallOption) (*GetBrandedAppConfigResult, error)
}

type brandedAppServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBrandedAppServiceClient(cc grpc.ClientConnInterface) BrandedAppServiceClient {
	return &brandedAppServiceClient{cc}
}

func (c *brandedAppServiceClient) GetBrandedAppConfig(ctx context.Context, in *GetBrandedAppConfigParams, opts ...grpc.CallOption) (*GetBrandedAppConfigResult, error) {
	out := new(GetBrandedAppConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.branded_app.v1.BrandedAppService/GetBrandedAppConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BrandedAppServiceServer is the server API for BrandedAppService service.
// All implementations must embed UnimplementedBrandedAppServiceServer
// for forward compatibility
type BrandedAppServiceServer interface {
	// get branded_app
	GetBrandedAppConfig(context.Context, *GetBrandedAppConfigParams) (*GetBrandedAppConfigResult, error)
	mustEmbedUnimplementedBrandedAppServiceServer()
}

// UnimplementedBrandedAppServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBrandedAppServiceServer struct {
}

func (UnimplementedBrandedAppServiceServer) GetBrandedAppConfig(context.Context, *GetBrandedAppConfigParams) (*GetBrandedAppConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBrandedAppConfig not implemented")
}
func (UnimplementedBrandedAppServiceServer) mustEmbedUnimplementedBrandedAppServiceServer() {}

// UnsafeBrandedAppServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BrandedAppServiceServer will
// result in compilation errors.
type UnsafeBrandedAppServiceServer interface {
	mustEmbedUnimplementedBrandedAppServiceServer()
}

func RegisterBrandedAppServiceServer(s grpc.ServiceRegistrar, srv BrandedAppServiceServer) {
	s.RegisterService(&BrandedAppService_ServiceDesc, srv)
}

func _BrandedAppService_GetBrandedAppConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBrandedAppConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BrandedAppServiceServer).GetBrandedAppConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.branded_app.v1.BrandedAppService/GetBrandedAppConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BrandedAppServiceServer).GetBrandedAppConfig(ctx, req.(*GetBrandedAppConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BrandedAppService_ServiceDesc is the grpc.ServiceDesc for BrandedAppService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BrandedAppService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.branded_app.v1.BrandedAppService",
	HandlerType: (*BrandedAppServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBrandedAppConfig",
			Handler:    _BrandedAppService_GetBrandedAppConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/branded_app/v1/branded_app_api.proto",
}
