// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/agreement/v1/agreement_api.proto

package agreementapipb

import (
	context "context"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementServiceClient is the client API for AgreementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementServiceClient interface {
	// get one agreement
	GetAgreement(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*v11.AgreementModel, error)
	// get agreement list
	GetAgreementList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAgreementListResponse, error)
	// get agreement list for whether need to sign
	GetAgreementSignStatusList(ctx context.Context, in *GetAgreementSignStatusListRequest, opts ...grpc.CallOption) (*GetAgreementSignStatusListResponse, error)
	// create an agreement
	AddAgreement(ctx context.Context, in *AddAgreementRequest, opts ...grpc.CallOption) (*v11.AgreementModel, error)
	// update an agreement
	UpdateAgreement(ctx context.Context, in *UpdateAgreementRequest, opts ...grpc.CallOption) (*v11.AgreementModel, error)
	// update agreement service type
	UpdateAgreementServiceType(ctx context.Context, in *UpdateServiceTypeRequest, opts ...grpc.CallOption) (*v11.AgreementModelSimpleView, error)
	// delete an agreement
	DeleteAgreement(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*DeleteAgreementResponse, error)
	// get agreement content list by company
	GetAgreementContentListByCompany(ctx context.Context, in *GetAgreementContentListByCompanyParams, opts ...grpc.CallOption) (*GetAgreementContentListByCompanyResult, error)
	// query company agreement info list
	QueryCompanyAgreementInfoList(ctx context.Context, in *QueryCompanyAgreementInfoListParams, opts ...grpc.CallOption) (*QueryCompanyAgreementInfoListResult, error)
}

type agreementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementServiceClient(cc grpc.ClientConnInterface) AgreementServiceClient {
	return &agreementServiceClient{cc}
}

func (c *agreementServiceClient) GetAgreement(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*v11.AgreementModel, error) {
	out := new(v11.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/GetAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) GetAgreementList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAgreementListResponse, error) {
	out := new(GetAgreementListResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/GetAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) GetAgreementSignStatusList(ctx context.Context, in *GetAgreementSignStatusListRequest, opts ...grpc.CallOption) (*GetAgreementSignStatusListResponse, error) {
	out := new(GetAgreementSignStatusListResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/GetAgreementSignStatusList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) AddAgreement(ctx context.Context, in *AddAgreementRequest, opts ...grpc.CallOption) (*v11.AgreementModel, error) {
	out := new(v11.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/AddAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) UpdateAgreement(ctx context.Context, in *UpdateAgreementRequest, opts ...grpc.CallOption) (*v11.AgreementModel, error) {
	out := new(v11.AgreementModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/UpdateAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) UpdateAgreementServiceType(ctx context.Context, in *UpdateServiceTypeRequest, opts ...grpc.CallOption) (*v11.AgreementModelSimpleView, error) {
	out := new(v11.AgreementModelSimpleView)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/UpdateAgreementServiceType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) DeleteAgreement(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*DeleteAgreementResponse, error) {
	out := new(DeleteAgreementResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/DeleteAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) GetAgreementContentListByCompany(ctx context.Context, in *GetAgreementContentListByCompanyParams, opts ...grpc.CallOption) (*GetAgreementContentListByCompanyResult, error) {
	out := new(GetAgreementContentListByCompanyResult)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/GetAgreementContentListByCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementServiceClient) QueryCompanyAgreementInfoList(ctx context.Context, in *QueryCompanyAgreementInfoListParams, opts ...grpc.CallOption) (*QueryCompanyAgreementInfoListResult, error) {
	out := new(QueryCompanyAgreementInfoListResult)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementService/QueryCompanyAgreementInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementServiceServer is the server API for AgreementService service.
// All implementations must embed UnimplementedAgreementServiceServer
// for forward compatibility
type AgreementServiceServer interface {
	// get one agreement
	GetAgreement(context.Context, *v1.Id) (*v11.AgreementModel, error)
	// get agreement list
	GetAgreementList(context.Context, *emptypb.Empty) (*GetAgreementListResponse, error)
	// get agreement list for whether need to sign
	GetAgreementSignStatusList(context.Context, *GetAgreementSignStatusListRequest) (*GetAgreementSignStatusListResponse, error)
	// create an agreement
	AddAgreement(context.Context, *AddAgreementRequest) (*v11.AgreementModel, error)
	// update an agreement
	UpdateAgreement(context.Context, *UpdateAgreementRequest) (*v11.AgreementModel, error)
	// update agreement service type
	UpdateAgreementServiceType(context.Context, *UpdateServiceTypeRequest) (*v11.AgreementModelSimpleView, error)
	// delete an agreement
	DeleteAgreement(context.Context, *v1.Id) (*DeleteAgreementResponse, error)
	// get agreement content list by company
	GetAgreementContentListByCompany(context.Context, *GetAgreementContentListByCompanyParams) (*GetAgreementContentListByCompanyResult, error)
	// query company agreement info list
	QueryCompanyAgreementInfoList(context.Context, *QueryCompanyAgreementInfoListParams) (*QueryCompanyAgreementInfoListResult, error)
	mustEmbedUnimplementedAgreementServiceServer()
}

// UnimplementedAgreementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementServiceServer struct {
}

func (UnimplementedAgreementServiceServer) GetAgreement(context.Context, *v1.Id) (*v11.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) GetAgreementList(context.Context, *emptypb.Empty) (*GetAgreementListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreementList not implemented")
}
func (UnimplementedAgreementServiceServer) GetAgreementSignStatusList(context.Context, *GetAgreementSignStatusListRequest) (*GetAgreementSignStatusListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreementSignStatusList not implemented")
}
func (UnimplementedAgreementServiceServer) AddAgreement(context.Context, *AddAgreementRequest) (*v11.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) UpdateAgreement(context.Context, *UpdateAgreementRequest) (*v11.AgreementModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) UpdateAgreementServiceType(context.Context, *UpdateServiceTypeRequest) (*v11.AgreementModelSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAgreementServiceType not implemented")
}
func (UnimplementedAgreementServiceServer) DeleteAgreement(context.Context, *v1.Id) (*DeleteAgreementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAgreement not implemented")
}
func (UnimplementedAgreementServiceServer) GetAgreementContentListByCompany(context.Context, *GetAgreementContentListByCompanyParams) (*GetAgreementContentListByCompanyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgreementContentListByCompany not implemented")
}
func (UnimplementedAgreementServiceServer) QueryCompanyAgreementInfoList(context.Context, *QueryCompanyAgreementInfoListParams) (*QueryCompanyAgreementInfoListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCompanyAgreementInfoList not implemented")
}
func (UnimplementedAgreementServiceServer) mustEmbedUnimplementedAgreementServiceServer() {}

// UnsafeAgreementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementServiceServer will
// result in compilation errors.
type UnsafeAgreementServiceServer interface {
	mustEmbedUnimplementedAgreementServiceServer()
}

func RegisterAgreementServiceServer(s grpc.ServiceRegistrar, srv AgreementServiceServer) {
	s.RegisterService(&AgreementService_ServiceDesc, srv)
}

func _AgreementService_GetAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/GetAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreement(ctx, req.(*v1.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_GetAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/GetAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreementList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_GetAgreementSignStatusList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgreementSignStatusListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreementSignStatusList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/GetAgreementSignStatusList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreementSignStatusList(ctx, req.(*GetAgreementSignStatusListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_AddAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).AddAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/AddAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).AddAgreement(ctx, req.(*AddAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_UpdateAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).UpdateAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/UpdateAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).UpdateAgreement(ctx, req.(*UpdateAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_UpdateAgreementServiceType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).UpdateAgreementServiceType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/UpdateAgreementServiceType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).UpdateAgreementServiceType(ctx, req.(*UpdateServiceTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_DeleteAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).DeleteAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/DeleteAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).DeleteAgreement(ctx, req.(*v1.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_GetAgreementContentListByCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgreementContentListByCompanyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetAgreementContentListByCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/GetAgreementContentListByCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetAgreementContentListByCompany(ctx, req.(*GetAgreementContentListByCompanyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementService_QueryCompanyAgreementInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCompanyAgreementInfoListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).QueryCompanyAgreementInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementService/QueryCompanyAgreementInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).QueryCompanyAgreementInfoList(ctx, req.(*QueryCompanyAgreementInfoListParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementService_ServiceDesc is the grpc.ServiceDesc for AgreementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.agreement.v1.AgreementService",
	HandlerType: (*AgreementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAgreement",
			Handler:    _AgreementService_GetAgreement_Handler,
		},
		{
			MethodName: "GetAgreementList",
			Handler:    _AgreementService_GetAgreementList_Handler,
		},
		{
			MethodName: "GetAgreementSignStatusList",
			Handler:    _AgreementService_GetAgreementSignStatusList_Handler,
		},
		{
			MethodName: "AddAgreement",
			Handler:    _AgreementService_AddAgreement_Handler,
		},
		{
			MethodName: "UpdateAgreement",
			Handler:    _AgreementService_UpdateAgreement_Handler,
		},
		{
			MethodName: "UpdateAgreementServiceType",
			Handler:    _AgreementService_UpdateAgreementServiceType_Handler,
		},
		{
			MethodName: "DeleteAgreement",
			Handler:    _AgreementService_DeleteAgreement_Handler,
		},
		{
			MethodName: "GetAgreementContentListByCompany",
			Handler:    _AgreementService_GetAgreementContentListByCompany_Handler,
		},
		{
			MethodName: "QueryCompanyAgreementInfoList",
			Handler:    _AgreementService_QueryCompanyAgreementInfoList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/agreement/v1/agreement_api.proto",
}
