// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/lodging_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// LodgingServiceClient is the client API for LodgingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LodgingServiceClient interface {
	// get lodging
	GetLodgingCalendarView(ctx context.Context, in *GetLodgingCalendarViewParams, opts ...grpc.CallOption) (*GetLodgingCalendarViewResult, error)
	// get lodging calendar view v2
	GetLodgingCalendarViewV2(ctx context.Context, in *GetLodgingCalendarViewV2Params, opts ...grpc.CallOption) (*GetLodgingCalendarViewV2Result, error)
	// get lodging list
	GetLodgingList(ctx context.Context, in *GetLodgingListParams, opts ...grpc.CallOption) (*GetLodgingListResult, error)
	// lodging unit transfer
	LodgingTransfer(ctx context.Context, in *LodgingTransferParams, opts ...grpc.CallOption) (*LodgingTransferResult, error)
	// check lodging if in use
	LodgingInUseCheck(ctx context.Context, in *LodgingInUseCheckParams, opts ...grpc.CallOption) (*LodgingInUseCheckResult, error)
	// get last lodging
	GetLastLodging(ctx context.Context, in *GetLastLodgingParams, opts ...grpc.CallOption) (*GetLastLodgingResult, error)
}

type lodgingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLodgingServiceClient(cc grpc.ClientConnInterface) LodgingServiceClient {
	return &lodgingServiceClient{cc}
}

func (c *lodgingServiceClient) GetLodgingCalendarView(ctx context.Context, in *GetLodgingCalendarViewParams, opts ...grpc.CallOption) (*GetLodgingCalendarViewResult, error) {
	out := new(GetLodgingCalendarViewResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/GetLodgingCalendarView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) GetLodgingCalendarViewV2(ctx context.Context, in *GetLodgingCalendarViewV2Params, opts ...grpc.CallOption) (*GetLodgingCalendarViewV2Result, error) {
	out := new(GetLodgingCalendarViewV2Result)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/GetLodgingCalendarViewV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) GetLodgingList(ctx context.Context, in *GetLodgingListParams, opts ...grpc.CallOption) (*GetLodgingListResult, error) {
	out := new(GetLodgingListResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/GetLodgingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) LodgingTransfer(ctx context.Context, in *LodgingTransferParams, opts ...grpc.CallOption) (*LodgingTransferResult, error) {
	out := new(LodgingTransferResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/LodgingTransfer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) LodgingInUseCheck(ctx context.Context, in *LodgingInUseCheckParams, opts ...grpc.CallOption) (*LodgingInUseCheckResult, error) {
	out := new(LodgingInUseCheckResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/LodgingInUseCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) GetLastLodging(ctx context.Context, in *GetLastLodgingParams, opts ...grpc.CallOption) (*GetLastLodgingResult, error) {
	out := new(GetLastLodgingResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.LodgingService/GetLastLodging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LodgingServiceServer is the server API for LodgingService service.
// All implementations must embed UnimplementedLodgingServiceServer
// for forward compatibility
type LodgingServiceServer interface {
	// get lodging
	GetLodgingCalendarView(context.Context, *GetLodgingCalendarViewParams) (*GetLodgingCalendarViewResult, error)
	// get lodging calendar view v2
	GetLodgingCalendarViewV2(context.Context, *GetLodgingCalendarViewV2Params) (*GetLodgingCalendarViewV2Result, error)
	// get lodging list
	GetLodgingList(context.Context, *GetLodgingListParams) (*GetLodgingListResult, error)
	// lodging unit transfer
	LodgingTransfer(context.Context, *LodgingTransferParams) (*LodgingTransferResult, error)
	// check lodging if in use
	LodgingInUseCheck(context.Context, *LodgingInUseCheckParams) (*LodgingInUseCheckResult, error)
	// get last lodging
	GetLastLodging(context.Context, *GetLastLodgingParams) (*GetLastLodgingResult, error)
	mustEmbedUnimplementedLodgingServiceServer()
}

// UnimplementedLodgingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLodgingServiceServer struct {
}

func (UnimplementedLodgingServiceServer) GetLodgingCalendarView(context.Context, *GetLodgingCalendarViewParams) (*GetLodgingCalendarViewResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingCalendarView not implemented")
}
func (UnimplementedLodgingServiceServer) GetLodgingCalendarViewV2(context.Context, *GetLodgingCalendarViewV2Params) (*GetLodgingCalendarViewV2Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingCalendarViewV2 not implemented")
}
func (UnimplementedLodgingServiceServer) GetLodgingList(context.Context, *GetLodgingListParams) (*GetLodgingListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingList not implemented")
}
func (UnimplementedLodgingServiceServer) LodgingTransfer(context.Context, *LodgingTransferParams) (*LodgingTransferResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LodgingTransfer not implemented")
}
func (UnimplementedLodgingServiceServer) LodgingInUseCheck(context.Context, *LodgingInUseCheckParams) (*LodgingInUseCheckResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LodgingInUseCheck not implemented")
}
func (UnimplementedLodgingServiceServer) GetLastLodging(context.Context, *GetLastLodgingParams) (*GetLastLodgingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastLodging not implemented")
}
func (UnimplementedLodgingServiceServer) mustEmbedUnimplementedLodgingServiceServer() {}

// UnsafeLodgingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LodgingServiceServer will
// result in compilation errors.
type UnsafeLodgingServiceServer interface {
	mustEmbedUnimplementedLodgingServiceServer()
}

func RegisterLodgingServiceServer(s grpc.ServiceRegistrar, srv LodgingServiceServer) {
	s.RegisterService(&LodgingService_ServiceDesc, srv)
}

func _LodgingService_GetLodgingCalendarView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingCalendarViewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).GetLodgingCalendarView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/GetLodgingCalendarView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).GetLodgingCalendarView(ctx, req.(*GetLodgingCalendarViewParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_GetLodgingCalendarViewV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingCalendarViewV2Params)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).GetLodgingCalendarViewV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/GetLodgingCalendarViewV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).GetLodgingCalendarViewV2(ctx, req.(*GetLodgingCalendarViewV2Params))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_GetLodgingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).GetLodgingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/GetLodgingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).GetLodgingList(ctx, req.(*GetLodgingListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_LodgingTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LodgingTransferParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).LodgingTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/LodgingTransfer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).LodgingTransfer(ctx, req.(*LodgingTransferParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_LodgingInUseCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LodgingInUseCheckParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).LodgingInUseCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/LodgingInUseCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).LodgingInUseCheck(ctx, req.(*LodgingInUseCheckParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_GetLastLodging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastLodgingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).GetLastLodging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.LodgingService/GetLastLodging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).GetLastLodging(ctx, req.(*GetLastLodgingParams))
	}
	return interceptor(ctx, in, info, handler)
}

// LodgingService_ServiceDesc is the grpc.ServiceDesc for LodgingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LodgingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.LodgingService",
	HandlerType: (*LodgingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLodgingCalendarView",
			Handler:    _LodgingService_GetLodgingCalendarView_Handler,
		},
		{
			MethodName: "GetLodgingCalendarViewV2",
			Handler:    _LodgingService_GetLodgingCalendarViewV2_Handler,
		},
		{
			MethodName: "GetLodgingList",
			Handler:    _LodgingService_GetLodgingList_Handler,
		},
		{
			MethodName: "LodgingTransfer",
			Handler:    _LodgingService_LodgingTransfer_Handler,
		},
		{
			MethodName: "LodgingInUseCheck",
			Handler:    _LodgingService_LodgingInUseCheck_Handler,
		},
		{
			MethodName: "GetLastLodging",
			Handler:    _LodgingService_GetLastLodging_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/lodging_api.proto",
}
