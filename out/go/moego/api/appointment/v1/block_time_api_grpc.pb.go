// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/block_time_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BlockTimeServiceClient is the client API for BlockTimeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BlockTimeServiceClient interface {
	// Create a block time
	CreateBlockTime(ctx context.Context, in *CreateBlockTimeParams, opts ...grpc.CallOption) (*CreateBlockTimeResult, error)
	// Update a block time
	UpdateBlockTime(ctx context.Context, in *UpdateBlockTimeParams, opts ...grpc.CallOption) (*UpdateBlockTimeResult, error)
}

type blockTimeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBlockTimeServiceClient(cc grpc.ClientConnInterface) BlockTimeServiceClient {
	return &blockTimeServiceClient{cc}
}

func (c *blockTimeServiceClient) CreateBlockTime(ctx context.Context, in *CreateBlockTimeParams, opts ...grpc.CallOption) (*CreateBlockTimeResult, error) {
	out := new(CreateBlockTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.BlockTimeService/CreateBlockTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blockTimeServiceClient) UpdateBlockTime(ctx context.Context, in *UpdateBlockTimeParams, opts ...grpc.CallOption) (*UpdateBlockTimeResult, error) {
	out := new(UpdateBlockTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.BlockTimeService/UpdateBlockTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BlockTimeServiceServer is the server API for BlockTimeService service.
// All implementations must embed UnimplementedBlockTimeServiceServer
// for forward compatibility
type BlockTimeServiceServer interface {
	// Create a block time
	CreateBlockTime(context.Context, *CreateBlockTimeParams) (*CreateBlockTimeResult, error)
	// Update a block time
	UpdateBlockTime(context.Context, *UpdateBlockTimeParams) (*UpdateBlockTimeResult, error)
	mustEmbedUnimplementedBlockTimeServiceServer()
}

// UnimplementedBlockTimeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBlockTimeServiceServer struct {
}

func (UnimplementedBlockTimeServiceServer) CreateBlockTime(context.Context, *CreateBlockTimeParams) (*CreateBlockTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlockTime not implemented")
}
func (UnimplementedBlockTimeServiceServer) UpdateBlockTime(context.Context, *UpdateBlockTimeParams) (*UpdateBlockTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlockTime not implemented")
}
func (UnimplementedBlockTimeServiceServer) mustEmbedUnimplementedBlockTimeServiceServer() {}

// UnsafeBlockTimeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlockTimeServiceServer will
// result in compilation errors.
type UnsafeBlockTimeServiceServer interface {
	mustEmbedUnimplementedBlockTimeServiceServer()
}

func RegisterBlockTimeServiceServer(s grpc.ServiceRegistrar, srv BlockTimeServiceServer) {
	s.RegisterService(&BlockTimeService_ServiceDesc, srv)
}

func _BlockTimeService_CreateBlockTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBlockTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlockTimeServiceServer).CreateBlockTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.BlockTimeService/CreateBlockTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlockTimeServiceServer).CreateBlockTime(ctx, req.(*CreateBlockTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BlockTimeService_UpdateBlockTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBlockTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlockTimeServiceServer).UpdateBlockTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.BlockTimeService/UpdateBlockTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlockTimeServiceServer).UpdateBlockTime(ctx, req.(*UpdateBlockTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BlockTimeService_ServiceDesc is the grpc.ServiceDesc for BlockTimeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BlockTimeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.BlockTimeService",
	HandlerType: (*BlockTimeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBlockTime",
			Handler:    _BlockTimeService_CreateBlockTime_Handler,
		},
		{
			MethodName: "UpdateBlockTime",
			Handler:    _BlockTimeService_UpdateBlockTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/block_time_api.proto",
}
