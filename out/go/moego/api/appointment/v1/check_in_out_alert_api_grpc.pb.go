// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/check_in_out_alert_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CheckInOutAlertServiceClient is the client API for CheckInOutAlertService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckInOutAlertServiceClient interface {
	// get check-in-out alter settings
	GetAlertSettings(ctx context.Context, in *GetAlertSettingsParams, opts ...grpc.CallOption) (*GetAlertSettingsResult, error)
	// save check-in-out alter settings
	SaveAlertSettings(ctx context.Context, in *SaveAlertSettingsParams, opts ...grpc.CallOption) (*SaveAlertSettingsResult, error)
	// batch get alter detail for check in
	BatchGetAlertsForCheckIn(ctx context.Context, in *BatchGetAlertsForCheckInParams, opts ...grpc.CallOption) (*BatchGetAlertsForCheckInResult, error)
	// get alter detail for check in
	GetAlertsForCheckIn(ctx context.Context, in *GetAlertsForCheckInParams, opts ...grpc.CallOption) (*GetAlertsForCheckInResult, error)
	// get alter detail for check out
	GetAlertsForCheckOut(ctx context.Context, in *GetAlertsForCheckOutParams, opts ...grpc.CallOption) (*GetAlertsForCheckOutResult, error)
}

type checkInOutAlertServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckInOutAlertServiceClient(cc grpc.ClientConnInterface) CheckInOutAlertServiceClient {
	return &checkInOutAlertServiceClient{cc}
}

func (c *checkInOutAlertServiceClient) GetAlertSettings(ctx context.Context, in *GetAlertSettingsParams, opts ...grpc.CallOption) (*GetAlertSettingsResult, error) {
	out := new(GetAlertSettingsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) SaveAlertSettings(ctx context.Context, in *SaveAlertSettingsParams, opts ...grpc.CallOption) (*SaveAlertSettingsResult, error) {
	out := new(SaveAlertSettingsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CheckInOutAlertService/SaveAlertSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) BatchGetAlertsForCheckIn(ctx context.Context, in *BatchGetAlertsForCheckInParams, opts ...grpc.CallOption) (*BatchGetAlertsForCheckInResult, error) {
	out := new(BatchGetAlertsForCheckInResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) GetAlertsForCheckIn(ctx context.Context, in *GetAlertsForCheckInParams, opts ...grpc.CallOption) (*GetAlertsForCheckInResult, error) {
	out := new(GetAlertsForCheckInResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) GetAlertsForCheckOut(ctx context.Context, in *GetAlertsForCheckOutParams, opts ...grpc.CallOption) (*GetAlertsForCheckOutResult, error) {
	out := new(GetAlertsForCheckOutResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckInOutAlertServiceServer is the server API for CheckInOutAlertService service.
// All implementations must embed UnimplementedCheckInOutAlertServiceServer
// for forward compatibility
type CheckInOutAlertServiceServer interface {
	// get check-in-out alter settings
	GetAlertSettings(context.Context, *GetAlertSettingsParams) (*GetAlertSettingsResult, error)
	// save check-in-out alter settings
	SaveAlertSettings(context.Context, *SaveAlertSettingsParams) (*SaveAlertSettingsResult, error)
	// batch get alter detail for check in
	BatchGetAlertsForCheckIn(context.Context, *BatchGetAlertsForCheckInParams) (*BatchGetAlertsForCheckInResult, error)
	// get alter detail for check in
	GetAlertsForCheckIn(context.Context, *GetAlertsForCheckInParams) (*GetAlertsForCheckInResult, error)
	// get alter detail for check out
	GetAlertsForCheckOut(context.Context, *GetAlertsForCheckOutParams) (*GetAlertsForCheckOutResult, error)
	mustEmbedUnimplementedCheckInOutAlertServiceServer()
}

// UnimplementedCheckInOutAlertServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCheckInOutAlertServiceServer struct {
}

func (UnimplementedCheckInOutAlertServiceServer) GetAlertSettings(context.Context, *GetAlertSettingsParams) (*GetAlertSettingsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertSettings not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) SaveAlertSettings(context.Context, *SaveAlertSettingsParams) (*SaveAlertSettingsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAlertSettings not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) BatchGetAlertsForCheckIn(context.Context, *BatchGetAlertsForCheckInParams) (*BatchGetAlertsForCheckInResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetAlertsForCheckIn not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) GetAlertsForCheckIn(context.Context, *GetAlertsForCheckInParams) (*GetAlertsForCheckInResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertsForCheckIn not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) GetAlertsForCheckOut(context.Context, *GetAlertsForCheckOutParams) (*GetAlertsForCheckOutResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertsForCheckOut not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) mustEmbedUnimplementedCheckInOutAlertServiceServer() {
}

// UnsafeCheckInOutAlertServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckInOutAlertServiceServer will
// result in compilation errors.
type UnsafeCheckInOutAlertServiceServer interface {
	mustEmbedUnimplementedCheckInOutAlertServiceServer()
}

func RegisterCheckInOutAlertServiceServer(s grpc.ServiceRegistrar, srv CheckInOutAlertServiceServer) {
	s.RegisterService(&CheckInOutAlertService_ServiceDesc, srv)
}

func _CheckInOutAlertService_GetAlertSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertSettingsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertSettings(ctx, req.(*GetAlertSettingsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_SaveAlertSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAlertSettingsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).SaveAlertSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CheckInOutAlertService/SaveAlertSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).SaveAlertSettings(ctx, req.(*SaveAlertSettingsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_BatchGetAlertsForCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAlertsForCheckInParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).BatchGetAlertsForCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).BatchGetAlertsForCheckIn(ctx, req.(*BatchGetAlertsForCheckInParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_GetAlertsForCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertsForCheckInParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckIn(ctx, req.(*GetAlertsForCheckInParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_GetAlertsForCheckOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertsForCheckOutParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckOut(ctx, req.(*GetAlertsForCheckOutParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CheckInOutAlertService_ServiceDesc is the grpc.ServiceDesc for CheckInOutAlertService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CheckInOutAlertService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.CheckInOutAlertService",
	HandlerType: (*CheckInOutAlertServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAlertSettings",
			Handler:    _CheckInOutAlertService_GetAlertSettings_Handler,
		},
		{
			MethodName: "SaveAlertSettings",
			Handler:    _CheckInOutAlertService_SaveAlertSettings_Handler,
		},
		{
			MethodName: "BatchGetAlertsForCheckIn",
			Handler:    _CheckInOutAlertService_BatchGetAlertsForCheckIn_Handler,
		},
		{
			MethodName: "GetAlertsForCheckIn",
			Handler:    _CheckInOutAlertService_GetAlertsForCheckIn_Handler,
		},
		{
			MethodName: "GetAlertsForCheckOut",
			Handler:    _CheckInOutAlertService_GetAlertsForCheckOut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/check_in_out_alert_api.proto",
}
