// @since 2024-06-24 10:14:35
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/daily_report_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get daily report config params
type GetDailyReportConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,3,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
}

func (x *GetDailyReportConfigParams) Reset() {
	*x = GetDailyReportConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportConfigParams) ProtoMessage() {}

func (x *GetDailyReportConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportConfigParams.ProtoReflect.Descriptor instead.
func (*GetDailyReportConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetDailyReportConfigParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetDailyReportConfigParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetDailyReportConfigParams) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

// get daily report config params
type GetDailyReportConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// report
	Report *v1.ReportDef `protobuf:"bytes,2,opt,name=report,proto3" json:"report,omitempty"`
	// report card status
	Status v1.ReportCardStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.appointment.v1.ReportCardStatus" json:"status,omitempty"`
}

func (x *GetDailyReportConfigResult) Reset() {
	*x = GetDailyReportConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportConfigResult) ProtoMessage() {}

func (x *GetDailyReportConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportConfigResult.ProtoReflect.Descriptor instead.
func (*GetDailyReportConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetDailyReportConfigResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetDailyReportConfigResult) GetReport() *v1.ReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *GetDailyReportConfigResult) GetStatus() v1.ReportCardStatus {
	if x != nil {
		return x.Status
	}
	return v1.ReportCardStatus(0)
}

// get daily report config params
type GetDailyReportSentResultParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,2,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
}

func (x *GetDailyReportSentResultParams) Reset() {
	*x = GetDailyReportSentResultParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportSentResultParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportSentResultParams) ProtoMessage() {}

func (x *GetDailyReportSentResultParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportSentResultParams.ProtoReflect.Descriptor instead.
func (*GetDailyReportSentResultParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetDailyReportSentResultParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetDailyReportSentResultParams) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

// get daily report config params
type GetDailyReportSentResultResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sent results
	SentResults []*v1.SentResultDef `protobuf:"bytes,1,rep,name=sent_results,json=sentResults,proto3" json:"sent_results,omitempty"`
}

func (x *GetDailyReportSentResultResult) Reset() {
	*x = GetDailyReportSentResultResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportSentResultResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportSentResultResult) ProtoMessage() {}

func (x *GetDailyReportSentResultResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportSentResultResult.ProtoReflect.Descriptor instead.
func (*GetDailyReportSentResultResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetDailyReportSentResultResult) GetSentResults() []*v1.SentResultDef {
	if x != nil {
		return x.SentResults
	}
	return nil
}

// create daily report config params
type UpsertDailyReportConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,4,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// report
	Report *v1.ReportDef `protobuf:"bytes,5,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *UpsertDailyReportConfigParams) Reset() {
	*x = UpsertDailyReportConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertDailyReportConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertDailyReportConfigParams) ProtoMessage() {}

func (x *UpsertDailyReportConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertDailyReportConfigParams.ProtoReflect.Descriptor instead.
func (*UpsertDailyReportConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpsertDailyReportConfigParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpsertDailyReportConfigParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *UpsertDailyReportConfigParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *UpsertDailyReportConfigParams) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

func (x *UpsertDailyReportConfigParams) GetReport() *v1.ReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

// get daily report config params
type UpsertDailyReportConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *UpsertDailyReportConfigResult) Reset() {
	*x = UpsertDailyReportConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertDailyReportConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertDailyReportConfigResult) ProtoMessage() {}

func (x *UpsertDailyReportConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertDailyReportConfigResult.ProtoReflect.Descriptor instead.
func (*UpsertDailyReportConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpsertDailyReportConfigResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpsertDailyReportConfigResult) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// get daily report sent history params
type GetDailyReportSentHistoryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *GetDailyReportSentHistoryParams) Reset() {
	*x = GetDailyReportSentHistoryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportSentHistoryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportSentHistoryParams) ProtoMessage() {}

func (x *GetDailyReportSentHistoryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportSentHistoryParams.ProtoReflect.Descriptor instead.
func (*GetDailyReportSentHistoryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetDailyReportSentHistoryParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetDailyReportSentHistoryParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// get daily report sent history result
type GetDailyReportSentHistoryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sent history list
	SentHistoryRecords []*v1.SentHistoryRecordDef `protobuf:"bytes,1,rep,name=sent_history_records,json=sentHistoryRecords,proto3" json:"sent_history_records,omitempty"`
}

func (x *GetDailyReportSentHistoryResult) Reset() {
	*x = GetDailyReportSentHistoryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportSentHistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportSentHistoryResult) ProtoMessage() {}

func (x *GetDailyReportSentHistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportSentHistoryResult.ProtoReflect.Descriptor instead.
func (*GetDailyReportSentHistoryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetDailyReportSentHistoryResult) GetSentHistoryRecords() []*v1.SentHistoryRecordDef {
	if x != nil {
		return x.SentHistoryRecords
	}
	return nil
}

// get daily report for customer params
type GetDailyReportForCustomerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uuid
	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GetDailyReportForCustomerParams) Reset() {
	*x = GetDailyReportForCustomerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportForCustomerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportForCustomerParams) ProtoMessage() {}

func (x *GetDailyReportForCustomerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportForCustomerParams.ProtoReflect.Descriptor instead.
func (*GetDailyReportForCustomerParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetDailyReportForCustomerParams) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// get daily report for customer result
type GetDailyReportForCustomerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report
	Report *v1.ReportDef `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,2,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// pet
	Pet *GetDailyReportForCustomerResult_Pet `protobuf:"bytes,3,opt,name=pet,proto3" json:"pet,omitempty"`
	// business
	Business *GetDailyReportForCustomerResult_Business `protobuf:"bytes,4,opt,name=business,proto3" json:"business,omitempty"`
}

func (x *GetDailyReportForCustomerResult) Reset() {
	*x = GetDailyReportForCustomerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportForCustomerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportForCustomerResult) ProtoMessage() {}

func (x *GetDailyReportForCustomerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportForCustomerResult.ProtoReflect.Descriptor instead.
func (*GetDailyReportForCustomerResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetDailyReportForCustomerResult) GetReport() *v1.ReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *GetDailyReportForCustomerResult) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

func (x *GetDailyReportForCustomerResult) GetPet() *GetDailyReportForCustomerResult_Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *GetDailyReportForCustomerResult) GetBusiness() *GetDailyReportForCustomerResult_Business {
	if x != nil {
		return x.Business
	}
	return nil
}

// generate message params
type GenerateMessageContentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,3,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
}

func (x *GenerateMessageContentParams) Reset() {
	*x = GenerateMessageContentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateMessageContentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMessageContentParams) ProtoMessage() {}

func (x *GenerateMessageContentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMessageContentParams.ProtoReflect.Descriptor instead.
func (*GenerateMessageContentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{10}
}

func (x *GenerateMessageContentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GenerateMessageContentParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GenerateMessageContentParams) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

// generate message result
type GenerateMessageContentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message for sending
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GenerateMessageContentResult) Reset() {
	*x = GenerateMessageContentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateMessageContentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateMessageContentResult) ProtoMessage() {}

func (x *GenerateMessageContentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateMessageContentResult.ProtoReflect.Descriptor instead.
func (*GenerateMessageContentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{11}
}

func (x *GenerateMessageContentResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// send message params
type SendMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// send method (default is SMS)
	SendMethod *v1.SendMethod `protobuf:"varint,2,opt,name=send_method,json=sendMethod,proto3,enum=moego.models.appointment.v1.SendMethod,oneof" json:"send_method,omitempty"`
	// recipient emails
	RecipientEmails []string `protobuf:"bytes,3,rep,name=recipient_emails,json=recipientEmails,proto3" json:"recipient_emails,omitempty"`
}

func (x *SendMessageParams) Reset() {
	*x = SendMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageParams) ProtoMessage() {}

func (x *SendMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageParams.ProtoReflect.Descriptor instead.
func (*SendMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{12}
}

func (x *SendMessageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SendMessageParams) GetSendMethod() v1.SendMethod {
	if x != nil && x.SendMethod != nil {
		return *x.SendMethod
	}
	return v1.SendMethod(0)
}

func (x *SendMessageParams) GetRecipientEmails() []string {
	if x != nil {
		return x.RecipientEmails
	}
	return nil
}

// send message result
type SendMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SendMessageResult) Reset() {
	*x = SendMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResult) ProtoMessage() {}

func (x *SendMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResult.ProtoReflect.Descriptor instead.
func (*SendMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{13}
}

func (x *SendMessageResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// list daily report config params
type ListDailyReportConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// list daily report config def
	Filter *v1.ListDailyReportConfigFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListDailyReportConfigParams) Reset() {
	*x = ListDailyReportConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyReportConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyReportConfigParams) ProtoMessage() {}

func (x *ListDailyReportConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyReportConfigParams.ProtoReflect.Descriptor instead.
func (*ListDailyReportConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{14}
}

func (x *ListDailyReportConfigParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListDailyReportConfigParams) GetFilter() *v1.ListDailyReportConfigFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListDailyReportConfigParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list daily report config result
type ListDailyReportConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report list
	ReportConfigs []*v1.DailyReportConfigDef `protobuf:"bytes,1,rep,name=report_configs,json=reportConfigs,proto3" json:"report_configs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// pet
	Pets []*ListDailyReportConfigResult_Pet `protobuf:"bytes,3,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListDailyReportConfigResult) Reset() {
	*x = ListDailyReportConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyReportConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyReportConfigResult) ProtoMessage() {}

func (x *ListDailyReportConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyReportConfigResult.ProtoReflect.Descriptor instead.
func (*ListDailyReportConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListDailyReportConfigResult) GetReportConfigs() []*v1.DailyReportConfigDef {
	if x != nil {
		return x.ReportConfigs
	}
	return nil
}

func (x *ListDailyReportConfigResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListDailyReportConfigResult) GetPets() []*ListDailyReportConfigResult_Pet {
	if x != nil {
		return x.Pets
	}
	return nil
}

// batch send daily draft report params
type BatchSendDailyDraftReportParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// daily report ids
	DailyReportIds []int64 `protobuf:"varint,2,rep,packed,name=daily_report_ids,json=dailyReportIds,proto3" json:"daily_report_ids,omitempty"`
	// send method
	SendMethod v1.SendMethod `protobuf:"varint,3,opt,name=send_method,json=sendMethod,proto3,enum=moego.models.appointment.v1.SendMethod" json:"send_method,omitempty"`
}

func (x *BatchSendDailyDraftReportParams) Reset() {
	*x = BatchSendDailyDraftReportParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSendDailyDraftReportParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendDailyDraftReportParams) ProtoMessage() {}

func (x *BatchSendDailyDraftReportParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendDailyDraftReportParams.ProtoReflect.Descriptor instead.
func (*BatchSendDailyDraftReportParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{16}
}

func (x *BatchSendDailyDraftReportParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchSendDailyDraftReportParams) GetDailyReportIds() []int64 {
	if x != nil {
		return x.DailyReportIds
	}
	return nil
}

func (x *BatchSendDailyDraftReportParams) GetSendMethod() v1.SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return v1.SendMethod(0)
}

// batch send daily draft report result
type BatchSendDailyDraftReportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchSendDailyDraftReportResult) Reset() {
	*x = BatchSendDailyDraftReportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSendDailyDraftReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSendDailyDraftReportResult) ProtoMessage() {}

func (x *BatchSendDailyDraftReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSendDailyDraftReportResult.ProtoReflect.Descriptor instead.
func (*BatchSendDailyDraftReportResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{17}
}

// batch delete daily report config params
type BatchDeleteDailyReportConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// daily report ids
	DailyReportIds []int64 `protobuf:"varint,2,rep,packed,name=daily_report_ids,json=dailyReportIds,proto3" json:"daily_report_ids,omitempty"`
}

func (x *BatchDeleteDailyReportConfigParams) Reset() {
	*x = BatchDeleteDailyReportConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteDailyReportConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteDailyReportConfigParams) ProtoMessage() {}

func (x *BatchDeleteDailyReportConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteDailyReportConfigParams.ProtoReflect.Descriptor instead.
func (*BatchDeleteDailyReportConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{18}
}

func (x *BatchDeleteDailyReportConfigParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchDeleteDailyReportConfigParams) GetDailyReportIds() []int64 {
	if x != nil {
		return x.DailyReportIds
	}
	return nil
}

// batch delete daily report config result
type BatchDeleteDailyReportConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteDailyReportConfigResult) Reset() {
	*x = BatchDeleteDailyReportConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteDailyReportConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteDailyReportConfigResult) ProtoMessage() {}

func (x *BatchDeleteDailyReportConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteDailyReportConfigResult.ProtoReflect.Descriptor instead.
func (*BatchDeleteDailyReportConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{19}
}

// pet
type GetDailyReportForCustomerResult_Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type
	PetType v11.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
}

func (x *GetDailyReportForCustomerResult_Pet) Reset() {
	*x = GetDailyReportForCustomerResult_Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportForCustomerResult_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportForCustomerResult_Pet) ProtoMessage() {}

func (x *GetDailyReportForCustomerResult_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportForCustomerResult_Pet.ProtoReflect.Descriptor instead.
func (*GetDailyReportForCustomerResult_Pet) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetDailyReportForCustomerResult_Pet) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetDailyReportForCustomerResult_Pet) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *GetDailyReportForCustomerResult_Pet) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *GetDailyReportForCustomerResult_Pet) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

// business
type GetDailyReportForCustomerResult_Business struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// business avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *GetDailyReportForCustomerResult_Business) Reset() {
	*x = GetDailyReportForCustomerResult_Business{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyReportForCustomerResult_Business) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyReportForCustomerResult_Business) ProtoMessage() {}

func (x *GetDailyReportForCustomerResult_Business) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyReportForCustomerResult_Business.ProtoReflect.Descriptor instead.
func (*GetDailyReportForCustomerResult_Business) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{9, 1}
}

func (x *GetDailyReportForCustomerResult_Business) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetDailyReportForCustomerResult_Business) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *GetDailyReportForCustomerResult_Business) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

// pet
type ListDailyReportConfigResult_Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet avatar
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type
	PetType v11.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
}

func (x *ListDailyReportConfigResult_Pet) Reset() {
	*x = ListDailyReportConfigResult_Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyReportConfigResult_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyReportConfigResult_Pet) ProtoMessage() {}

func (x *ListDailyReportConfigResult_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyReportConfigResult_Pet.ProtoReflect.Descriptor instead.
func (*ListDailyReportConfigResult_Pet) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ListDailyReportConfigResult_Pet) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListDailyReportConfigResult_Pet) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListDailyReportConfigResult_Pet) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListDailyReportConfigResult_Pet) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

var File_moego_api_appointment_v1_daily_report_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_daily_report_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x6f, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d,
	0x0a, 0x0c, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x66,
	0x52, 0x0b, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xa3, 0x02,
	0x0a, 0x1d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x22, 0x43, 0x0a, 0x1d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x71, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x63, 0x0a, 0x14, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x74,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66,
	0x52, 0x12, 0x73, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x22, 0x3e, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x22, 0xd4, 0x04, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66,
	0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4f,
	0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12,
	0x5e, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x1a,
	0x96, 0x01, 0x0a, 0x03, 0x50, 0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x71, 0x0a, 0x08, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x22, 0xae, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x38, 0x0a, 0x1c,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd4, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x59, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x3b, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92,
	0x01, 0x0a, 0x18, 0x01, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x60, 0x01, 0x52, 0x0f, 0x72, 0x65,
	0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x2b, 0x0a,
	0x11, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xf0, 0x01, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x03,
	0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x70,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x50, 0x65, 0x74, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x1a, 0x96, 0x01, 0x0a, 0x03, 0x50,
	0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x38, 0x0a, 0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x0b, 0x73,
	0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x22, 0x21, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x88, 0x01, 0x0a, 0x22, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0e, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x22,
	0x24, 0x0a, 0x22, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x8d, 0x0b, 0x0a, 0x12, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x82, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x8e, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x91, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a,
	0x15, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x44, 0x72, 0x61, 0x66,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x44, 0x72, 0x61, 0x66, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x1c, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_daily_report_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_daily_report_api_proto_rawDescData = file_moego_api_appointment_v1_daily_report_api_proto_rawDesc
)

func file_moego_api_appointment_v1_daily_report_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_daily_report_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_daily_report_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_daily_report_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_daily_report_api_proto_rawDescData
}

var file_moego_api_appointment_v1_daily_report_api_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_moego_api_appointment_v1_daily_report_api_proto_goTypes = []interface{}{
	(*GetDailyReportConfigParams)(nil),               // 0: moego.api.appointment.v1.GetDailyReportConfigParams
	(*GetDailyReportConfigResult)(nil),               // 1: moego.api.appointment.v1.GetDailyReportConfigResult
	(*GetDailyReportSentResultParams)(nil),           // 2: moego.api.appointment.v1.GetDailyReportSentResultParams
	(*GetDailyReportSentResultResult)(nil),           // 3: moego.api.appointment.v1.GetDailyReportSentResultResult
	(*UpsertDailyReportConfigParams)(nil),            // 4: moego.api.appointment.v1.UpsertDailyReportConfigParams
	(*UpsertDailyReportConfigResult)(nil),            // 5: moego.api.appointment.v1.UpsertDailyReportConfigResult
	(*GetDailyReportSentHistoryParams)(nil),          // 6: moego.api.appointment.v1.GetDailyReportSentHistoryParams
	(*GetDailyReportSentHistoryResult)(nil),          // 7: moego.api.appointment.v1.GetDailyReportSentHistoryResult
	(*GetDailyReportForCustomerParams)(nil),          // 8: moego.api.appointment.v1.GetDailyReportForCustomerParams
	(*GetDailyReportForCustomerResult)(nil),          // 9: moego.api.appointment.v1.GetDailyReportForCustomerResult
	(*GenerateMessageContentParams)(nil),             // 10: moego.api.appointment.v1.GenerateMessageContentParams
	(*GenerateMessageContentResult)(nil),             // 11: moego.api.appointment.v1.GenerateMessageContentResult
	(*SendMessageParams)(nil),                        // 12: moego.api.appointment.v1.SendMessageParams
	(*SendMessageResult)(nil),                        // 13: moego.api.appointment.v1.SendMessageResult
	(*ListDailyReportConfigParams)(nil),              // 14: moego.api.appointment.v1.ListDailyReportConfigParams
	(*ListDailyReportConfigResult)(nil),              // 15: moego.api.appointment.v1.ListDailyReportConfigResult
	(*BatchSendDailyDraftReportParams)(nil),          // 16: moego.api.appointment.v1.BatchSendDailyDraftReportParams
	(*BatchSendDailyDraftReportResult)(nil),          // 17: moego.api.appointment.v1.BatchSendDailyDraftReportResult
	(*BatchDeleteDailyReportConfigParams)(nil),       // 18: moego.api.appointment.v1.BatchDeleteDailyReportConfigParams
	(*BatchDeleteDailyReportConfigResult)(nil),       // 19: moego.api.appointment.v1.BatchDeleteDailyReportConfigResult
	(*GetDailyReportForCustomerResult_Pet)(nil),      // 20: moego.api.appointment.v1.GetDailyReportForCustomerResult.Pet
	(*GetDailyReportForCustomerResult_Business)(nil), // 21: moego.api.appointment.v1.GetDailyReportForCustomerResult.Business
	(*ListDailyReportConfigResult_Pet)(nil),          // 22: moego.api.appointment.v1.ListDailyReportConfigResult.Pet
	(*date.Date)(nil),                                // 23: google.type.Date
	(*v1.ReportDef)(nil),                             // 24: moego.models.appointment.v1.ReportDef
	(v1.ReportCardStatus)(0),                         // 25: moego.models.appointment.v1.ReportCardStatus
	(*v1.SentResultDef)(nil),                         // 26: moego.models.appointment.v1.SentResultDef
	(*v1.SentHistoryRecordDef)(nil),                  // 27: moego.models.appointment.v1.SentHistoryRecordDef
	(v1.SendMethod)(0),                               // 28: moego.models.appointment.v1.SendMethod
	(*v1.ListDailyReportConfigFilter)(nil),           // 29: moego.models.appointment.v1.ListDailyReportConfigFilter
	(*v2.PaginationRequest)(nil),                     // 30: moego.utils.v2.PaginationRequest
	(*v1.DailyReportConfigDef)(nil),                  // 31: moego.models.appointment.v1.DailyReportConfigDef
	(*v2.PaginationResponse)(nil),                    // 32: moego.utils.v2.PaginationResponse
	(v11.PetType)(0),                                 // 33: moego.models.customer.v1.PetType
}
var file_moego_api_appointment_v1_daily_report_api_proto_depIdxs = []int32{
	23, // 0: moego.api.appointment.v1.GetDailyReportConfigParams.service_date:type_name -> google.type.Date
	24, // 1: moego.api.appointment.v1.GetDailyReportConfigResult.report:type_name -> moego.models.appointment.v1.ReportDef
	25, // 2: moego.api.appointment.v1.GetDailyReportConfigResult.status:type_name -> moego.models.appointment.v1.ReportCardStatus
	23, // 3: moego.api.appointment.v1.GetDailyReportSentResultParams.service_date:type_name -> google.type.Date
	26, // 4: moego.api.appointment.v1.GetDailyReportSentResultResult.sent_results:type_name -> moego.models.appointment.v1.SentResultDef
	23, // 5: moego.api.appointment.v1.UpsertDailyReportConfigParams.service_date:type_name -> google.type.Date
	24, // 6: moego.api.appointment.v1.UpsertDailyReportConfigParams.report:type_name -> moego.models.appointment.v1.ReportDef
	27, // 7: moego.api.appointment.v1.GetDailyReportSentHistoryResult.sent_history_records:type_name -> moego.models.appointment.v1.SentHistoryRecordDef
	24, // 8: moego.api.appointment.v1.GetDailyReportForCustomerResult.report:type_name -> moego.models.appointment.v1.ReportDef
	23, // 9: moego.api.appointment.v1.GetDailyReportForCustomerResult.service_date:type_name -> google.type.Date
	20, // 10: moego.api.appointment.v1.GetDailyReportForCustomerResult.pet:type_name -> moego.api.appointment.v1.GetDailyReportForCustomerResult.Pet
	21, // 11: moego.api.appointment.v1.GetDailyReportForCustomerResult.business:type_name -> moego.api.appointment.v1.GetDailyReportForCustomerResult.Business
	23, // 12: moego.api.appointment.v1.GenerateMessageContentParams.service_date:type_name -> google.type.Date
	28, // 13: moego.api.appointment.v1.SendMessageParams.send_method:type_name -> moego.models.appointment.v1.SendMethod
	29, // 14: moego.api.appointment.v1.ListDailyReportConfigParams.filter:type_name -> moego.models.appointment.v1.ListDailyReportConfigFilter
	30, // 15: moego.api.appointment.v1.ListDailyReportConfigParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	31, // 16: moego.api.appointment.v1.ListDailyReportConfigResult.report_configs:type_name -> moego.models.appointment.v1.DailyReportConfigDef
	32, // 17: moego.api.appointment.v1.ListDailyReportConfigResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	22, // 18: moego.api.appointment.v1.ListDailyReportConfigResult.pets:type_name -> moego.api.appointment.v1.ListDailyReportConfigResult.Pet
	28, // 19: moego.api.appointment.v1.BatchSendDailyDraftReportParams.send_method:type_name -> moego.models.appointment.v1.SendMethod
	33, // 20: moego.api.appointment.v1.GetDailyReportForCustomerResult.Pet.pet_type:type_name -> moego.models.customer.v1.PetType
	33, // 21: moego.api.appointment.v1.ListDailyReportConfigResult.Pet.pet_type:type_name -> moego.models.customer.v1.PetType
	0,  // 22: moego.api.appointment.v1.DailyReportService.GetDailyReportConfig:input_type -> moego.api.appointment.v1.GetDailyReportConfigParams
	2,  // 23: moego.api.appointment.v1.DailyReportService.GetDailyReportSentResult:input_type -> moego.api.appointment.v1.GetDailyReportSentResultParams
	4,  // 24: moego.api.appointment.v1.DailyReportService.UpsertDailyReportConfig:input_type -> moego.api.appointment.v1.UpsertDailyReportConfigParams
	6,  // 25: moego.api.appointment.v1.DailyReportService.GetDailyReportSentHistory:input_type -> moego.api.appointment.v1.GetDailyReportSentHistoryParams
	8,  // 26: moego.api.appointment.v1.DailyReportService.GetDailyReportForCustomer:input_type -> moego.api.appointment.v1.GetDailyReportForCustomerParams
	10, // 27: moego.api.appointment.v1.DailyReportService.GenerateMessageContent:input_type -> moego.api.appointment.v1.GenerateMessageContentParams
	12, // 28: moego.api.appointment.v1.DailyReportService.SendMessage:input_type -> moego.api.appointment.v1.SendMessageParams
	14, // 29: moego.api.appointment.v1.DailyReportService.ListDailyReportConfig:input_type -> moego.api.appointment.v1.ListDailyReportConfigParams
	16, // 30: moego.api.appointment.v1.DailyReportService.BatchSendDailyDraftReport:input_type -> moego.api.appointment.v1.BatchSendDailyDraftReportParams
	18, // 31: moego.api.appointment.v1.DailyReportService.BatchDeleteDailyReportConfig:input_type -> moego.api.appointment.v1.BatchDeleteDailyReportConfigParams
	1,  // 32: moego.api.appointment.v1.DailyReportService.GetDailyReportConfig:output_type -> moego.api.appointment.v1.GetDailyReportConfigResult
	3,  // 33: moego.api.appointment.v1.DailyReportService.GetDailyReportSentResult:output_type -> moego.api.appointment.v1.GetDailyReportSentResultResult
	5,  // 34: moego.api.appointment.v1.DailyReportService.UpsertDailyReportConfig:output_type -> moego.api.appointment.v1.UpsertDailyReportConfigResult
	7,  // 35: moego.api.appointment.v1.DailyReportService.GetDailyReportSentHistory:output_type -> moego.api.appointment.v1.GetDailyReportSentHistoryResult
	9,  // 36: moego.api.appointment.v1.DailyReportService.GetDailyReportForCustomer:output_type -> moego.api.appointment.v1.GetDailyReportForCustomerResult
	11, // 37: moego.api.appointment.v1.DailyReportService.GenerateMessageContent:output_type -> moego.api.appointment.v1.GenerateMessageContentResult
	13, // 38: moego.api.appointment.v1.DailyReportService.SendMessage:output_type -> moego.api.appointment.v1.SendMessageResult
	15, // 39: moego.api.appointment.v1.DailyReportService.ListDailyReportConfig:output_type -> moego.api.appointment.v1.ListDailyReportConfigResult
	17, // 40: moego.api.appointment.v1.DailyReportService.BatchSendDailyDraftReport:output_type -> moego.api.appointment.v1.BatchSendDailyDraftReportResult
	19, // 41: moego.api.appointment.v1.DailyReportService.BatchDeleteDailyReportConfig:output_type -> moego.api.appointment.v1.BatchDeleteDailyReportConfigResult
	32, // [32:42] is the sub-list for method output_type
	22, // [22:32] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_daily_report_api_proto_init() }
func file_moego_api_appointment_v1_daily_report_api_proto_init() {
	if File_moego_api_appointment_v1_daily_report_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportSentResultParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportSentResultResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertDailyReportConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertDailyReportConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportSentHistoryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportSentHistoryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportForCustomerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportForCustomerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateMessageContentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateMessageContentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyReportConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyReportConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSendDailyDraftReportParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSendDailyDraftReportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteDailyReportConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteDailyReportConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportForCustomerResult_Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyReportForCustomerResult_Business); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyReportConfigResult_Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_daily_report_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_daily_report_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_daily_report_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_daily_report_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_daily_report_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_daily_report_api_proto = out.File
	file_moego_api_appointment_v1_daily_report_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_daily_report_api_proto_goTypes = nil
	file_moego_api_appointment_v1_daily_report_api_proto_depIdxs = nil
}
