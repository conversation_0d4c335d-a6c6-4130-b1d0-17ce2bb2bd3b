// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/pet_detail_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Save or update appointment pet detail params
type SaveOrUpdatePetDetailsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// DO NOT use this field, use `pet_details` instead.
	//
	// Deprecated: Do not use.
	PetDetail *v1.PetDetailDef `protobuf:"bytes,2,opt,name=pet_detail,json=petDetail,proto3,oneof" json:"pet_detail,omitempty"`
	// Multi pets update details
	// It contains all the services and add-on information selected for a single pet.
	// If it already exists, it will be overwritten with the latest data.
	// If it does not exist, the latest data will be directly inserted.
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,4,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
}

func (x *SaveOrUpdatePetDetailsParams) Reset() {
	*x = SaveOrUpdatePetDetailsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveOrUpdatePetDetailsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveOrUpdatePetDetailsParams) ProtoMessage() {}

func (x *SaveOrUpdatePetDetailsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveOrUpdatePetDetailsParams.ProtoReflect.Descriptor instead.
func (*SaveOrUpdatePetDetailsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{0}
}

func (x *SaveOrUpdatePetDetailsParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Deprecated: Do not use.
func (x *SaveOrUpdatePetDetailsParams) GetPetDetail() *v1.PetDetailDef {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

func (x *SaveOrUpdatePetDetailsParams) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *SaveOrUpdatePetDetailsParams) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Save or update appointment pet detail result
type SaveOrUpdatePetDetailsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveOrUpdatePetDetailsResult) Reset() {
	*x = SaveOrUpdatePetDetailsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveOrUpdatePetDetailsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveOrUpdatePetDetailsResult) ProtoMessage() {}

func (x *SaveOrUpdatePetDetailsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveOrUpdatePetDetailsResult.ProtoReflect.Descriptor instead.
func (*SaveOrUpdatePetDetailsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{1}
}

// Delete appointment selected pet params
type DeletePetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// selected pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
}

func (x *DeletePetParams) Reset() {
	*x = DeletePetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetParams) ProtoMessage() {}

func (x *DeletePetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetParams.ProtoReflect.Descriptor instead.
func (*DeletePetParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{2}
}

func (x *DeletePetParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *DeletePetParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DeletePetParams) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Delete appointment selected pet result
type DeletePetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetResult) Reset() {
	*x = DeletePetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetResult) ProtoMessage() {}

func (x *DeletePetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetResult.ProtoReflect.Descriptor instead.
func (*DeletePetResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{3}
}

// Delete appointment selected pet evaluation param
type DeletePetEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// selected evaluation service detail id
	EvaluationServiceDetailId int64 `protobuf:"varint,2,opt,name=evaluation_service_detail_id,json=evaluationServiceDetailId,proto3" json:"evaluation_service_detail_id,omitempty"`
}

func (x *DeletePetEvaluationParams) Reset() {
	*x = DeletePetEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetEvaluationParams) ProtoMessage() {}

func (x *DeletePetEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetEvaluationParams.ProtoReflect.Descriptor instead.
func (*DeletePetEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeletePetEvaluationParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *DeletePetEvaluationParams) GetEvaluationServiceDetailId() int64 {
	if x != nil {
		return x.EvaluationServiceDetailId
	}
	return 0
}

// Delete appointment selected pet result
type DeletePetEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetEvaluationResult) Reset() {
	*x = DeletePetEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetEvaluationResult) ProtoMessage() {}

func (x *DeletePetEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetEvaluationResult.ProtoReflect.Descriptor instead.
func (*DeletePetEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{5}
}

// pre check params for create evaluation service
type PreCreateEvaluationServiceCheckParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment date
	StartDate *string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *PreCreateEvaluationServiceCheckParams) Reset() {
	*x = PreCreateEvaluationServiceCheckParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCreateEvaluationServiceCheckParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCreateEvaluationServiceCheckParams) ProtoMessage() {}

func (x *PreCreateEvaluationServiceCheckParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCreateEvaluationServiceCheckParams.ProtoReflect.Descriptor instead.
func (*PreCreateEvaluationServiceCheckParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{6}
}

func (x *PreCreateEvaluationServiceCheckParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *PreCreateEvaluationServiceCheckParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

// pre check result for create evaluation service
type PreCreateEvaluationServiceCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet num for evaluation on the same day
	PetNumForEvaluation *int32 `protobuf:"varint,1,opt,name=pet_num_for_evaluation,json=petNumForEvaluation,proto3,oneof" json:"pet_num_for_evaluation,omitempty"`
}

func (x *PreCreateEvaluationServiceCheckResult) Reset() {
	*x = PreCreateEvaluationServiceCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCreateEvaluationServiceCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCreateEvaluationServiceCheckResult) ProtoMessage() {}

func (x *PreCreateEvaluationServiceCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCreateEvaluationServiceCheckResult.ProtoReflect.Descriptor instead.
func (*PreCreateEvaluationServiceCheckResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{7}
}

func (x *PreCreateEvaluationServiceCheckResult) GetPetNumForEvaluation() int32 {
	if x != nil && x.PetNumForEvaluation != nil {
		return *x.PetNumForEvaluation
	}
	return 0
}

// Count pet detail params
type CountPetDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date, format: yyyy-MM-dd
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date, format: yyyy-MM-dd
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, SERVICE_ITEM_TYPE_UNSPECIFIED (0) means all service item types
	ServiceItemType *v11.ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
}

func (x *CountPetDetailParams) Reset() {
	*x = CountPetDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountPetDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountPetDetailParams) ProtoMessage() {}

func (x *CountPetDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountPetDetailParams.ProtoReflect.Descriptor instead.
func (*CountPetDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{8}
}

func (x *CountPetDetailParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CountPetDetailParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *CountPetDetailParams) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *CountPetDetailParams) GetServiceItemType() v11.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// Count pet detail result
type CountPetDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet detail count
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CountPetDetailResult) Reset() {
	*x = CountPetDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountPetDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountPetDetailResult) ProtoMessage() {}

func (x *CountPetDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountPetDetailResult.ProtoReflect.Descriptor instead.
func (*CountPetDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP(), []int{9}
}

func (x *CountPetDetailResult) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_moego_api_appointment_v1_pet_detail_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_pet_detail_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe, 0x03, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65, 0x4f,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x09, 0x70, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0b, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x10, 0x64, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x1c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x42, 0x22, 0x0a, 0x20, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65, 0x4f,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x99, 0x02, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x91, 0x01, 0x0a, 0x1f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x1c,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x22, 0x0a, 0x20, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x95, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x1c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x19, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x1b,
	0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x25,
	0x50, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x22, 0x7c, 0x0a, 0x25, 0x50, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38, 0x0a, 0x16,
	0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x13,
	0x70, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xae, 0x02, 0x0a, 0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x64, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x2c, 0x0a, 0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x32, 0x99, 0x05, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x61, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0xa3, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_pet_detail_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_pet_detail_api_proto_rawDescData = file_moego_api_appointment_v1_pet_detail_api_proto_rawDesc
)

func file_moego_api_appointment_v1_pet_detail_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_pet_detail_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_pet_detail_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_pet_detail_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_pet_detail_api_proto_rawDescData
}

var file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_api_appointment_v1_pet_detail_api_proto_goTypes = []interface{}{
	(*SaveOrUpdatePetDetailsParams)(nil),          // 0: moego.api.appointment.v1.SaveOrUpdatePetDetailsParams
	(*SaveOrUpdatePetDetailsResult)(nil),          // 1: moego.api.appointment.v1.SaveOrUpdatePetDetailsResult
	(*DeletePetParams)(nil),                       // 2: moego.api.appointment.v1.DeletePetParams
	(*DeletePetResult)(nil),                       // 3: moego.api.appointment.v1.DeletePetResult
	(*DeletePetEvaluationParams)(nil),             // 4: moego.api.appointment.v1.DeletePetEvaluationParams
	(*DeletePetEvaluationResult)(nil),             // 5: moego.api.appointment.v1.DeletePetEvaluationResult
	(*PreCreateEvaluationServiceCheckParams)(nil), // 6: moego.api.appointment.v1.PreCreateEvaluationServiceCheckParams
	(*PreCreateEvaluationServiceCheckResult)(nil), // 7: moego.api.appointment.v1.PreCreateEvaluationServiceCheckResult
	(*CountPetDetailParams)(nil),                  // 8: moego.api.appointment.v1.CountPetDetailParams
	(*CountPetDetailResult)(nil),                  // 9: moego.api.appointment.v1.CountPetDetailResult
	(*v1.PetDetailDef)(nil),                       // 10: moego.models.appointment.v1.PetDetailDef
	(v1.RepeatAppointmentModifyScope)(0),          // 11: moego.models.appointment.v1.RepeatAppointmentModifyScope
	(v11.ServiceItemType)(0),                      // 12: moego.models.offering.v1.ServiceItemType
}
var file_moego_api_appointment_v1_pet_detail_api_proto_depIdxs = []int32{
	10, // 0: moego.api.appointment.v1.SaveOrUpdatePetDetailsParams.pet_detail:type_name -> moego.models.appointment.v1.PetDetailDef
	10, // 1: moego.api.appointment.v1.SaveOrUpdatePetDetailsParams.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	11, // 2: moego.api.appointment.v1.SaveOrUpdatePetDetailsParams.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	11, // 3: moego.api.appointment.v1.DeletePetParams.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	12, // 4: moego.api.appointment.v1.CountPetDetailParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	0,  // 5: moego.api.appointment.v1.PetDetailService.SaveOrUpdatePetDetails:input_type -> moego.api.appointment.v1.SaveOrUpdatePetDetailsParams
	2,  // 6: moego.api.appointment.v1.PetDetailService.DeletePet:input_type -> moego.api.appointment.v1.DeletePetParams
	4,  // 7: moego.api.appointment.v1.PetDetailService.DeletePetEvaluation:input_type -> moego.api.appointment.v1.DeletePetEvaluationParams
	6,  // 8: moego.api.appointment.v1.PetDetailService.PreCreateEvaluationServiceCheck:input_type -> moego.api.appointment.v1.PreCreateEvaluationServiceCheckParams
	8,  // 9: moego.api.appointment.v1.PetDetailService.CountPetDetail:input_type -> moego.api.appointment.v1.CountPetDetailParams
	1,  // 10: moego.api.appointment.v1.PetDetailService.SaveOrUpdatePetDetails:output_type -> moego.api.appointment.v1.SaveOrUpdatePetDetailsResult
	3,  // 11: moego.api.appointment.v1.PetDetailService.DeletePet:output_type -> moego.api.appointment.v1.DeletePetResult
	5,  // 12: moego.api.appointment.v1.PetDetailService.DeletePetEvaluation:output_type -> moego.api.appointment.v1.DeletePetEvaluationResult
	7,  // 13: moego.api.appointment.v1.PetDetailService.PreCreateEvaluationServiceCheck:output_type -> moego.api.appointment.v1.PreCreateEvaluationServiceCheckResult
	9,  // 14: moego.api.appointment.v1.PetDetailService.CountPetDetail:output_type -> moego.api.appointment.v1.CountPetDetailResult
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_pet_detail_api_proto_init() }
func file_moego_api_appointment_v1_pet_detail_api_proto_init() {
	if File_moego_api_appointment_v1_pet_detail_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveOrUpdatePetDetailsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveOrUpdatePetDetailsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCreateEvaluationServiceCheckParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCreateEvaluationServiceCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountPetDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountPetDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_pet_detail_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_pet_detail_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_pet_detail_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_pet_detail_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_pet_detail_api_proto = out.File
	file_moego_api_appointment_v1_pet_detail_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_pet_detail_api_proto_goTypes = nil
	file_moego_api_appointment_v1_pet_detail_api_proto_depIdxs = nil
}
