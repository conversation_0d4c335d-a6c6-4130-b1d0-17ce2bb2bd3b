// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/appointment_checker_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentCheckerServiceClient is the client API for AppointmentCheckerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentCheckerServiceClient interface {
	// check save appointment
	CheckSaveAppointment(ctx context.Context, in *CheckSaveAppointmentParams, opts ...grpc.CallOption) (*CheckSaveAppointmentResult, error)
	// get available dates
	GetAvailableDates(ctx context.Context, in *GetAvailableDatesParams, opts ...grpc.CallOption) (*GetAvailableDatesResult, error)
	// get available time ranges
	GetEvaluationAvailableTime(ctx context.Context, in *GetEvaluationAvailableTimeParams, opts ...grpc.CallOption) (*GetEvaluationAvailableTimeResult, error)
	// check lodging over capacity
	CheckLodgingOverCapacity(ctx context.Context, in *CheckLodgingOverCapacityParams, opts ...grpc.CallOption) (*CheckLodgingOverCapacityResult, error)
}

type appointmentCheckerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentCheckerServiceClient(cc grpc.ClientConnInterface) AppointmentCheckerServiceClient {
	return &appointmentCheckerServiceClient{cc}
}

func (c *appointmentCheckerServiceClient) CheckSaveAppointment(ctx context.Context, in *CheckSaveAppointmentParams, opts ...grpc.CallOption) (*CheckSaveAppointmentResult, error) {
	out := new(CheckSaveAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentCheckerService/CheckSaveAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentCheckerServiceClient) GetAvailableDates(ctx context.Context, in *GetAvailableDatesParams, opts ...grpc.CallOption) (*GetAvailableDatesResult, error) {
	out := new(GetAvailableDatesResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentCheckerService/GetAvailableDates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentCheckerServiceClient) GetEvaluationAvailableTime(ctx context.Context, in *GetEvaluationAvailableTimeParams, opts ...grpc.CallOption) (*GetEvaluationAvailableTimeResult, error) {
	out := new(GetEvaluationAvailableTimeResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentCheckerService/GetEvaluationAvailableTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentCheckerServiceClient) CheckLodgingOverCapacity(ctx context.Context, in *CheckLodgingOverCapacityParams, opts ...grpc.CallOption) (*CheckLodgingOverCapacityResult, error) {
	out := new(CheckLodgingOverCapacityResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentCheckerService/CheckLodgingOverCapacity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentCheckerServiceServer is the server API for AppointmentCheckerService service.
// All implementations must embed UnimplementedAppointmentCheckerServiceServer
// for forward compatibility
type AppointmentCheckerServiceServer interface {
	// check save appointment
	CheckSaveAppointment(context.Context, *CheckSaveAppointmentParams) (*CheckSaveAppointmentResult, error)
	// get available dates
	GetAvailableDates(context.Context, *GetAvailableDatesParams) (*GetAvailableDatesResult, error)
	// get available time ranges
	GetEvaluationAvailableTime(context.Context, *GetEvaluationAvailableTimeParams) (*GetEvaluationAvailableTimeResult, error)
	// check lodging over capacity
	CheckLodgingOverCapacity(context.Context, *CheckLodgingOverCapacityParams) (*CheckLodgingOverCapacityResult, error)
	mustEmbedUnimplementedAppointmentCheckerServiceServer()
}

// UnimplementedAppointmentCheckerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentCheckerServiceServer struct {
}

func (UnimplementedAppointmentCheckerServiceServer) CheckSaveAppointment(context.Context, *CheckSaveAppointmentParams) (*CheckSaveAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSaveAppointment not implemented")
}
func (UnimplementedAppointmentCheckerServiceServer) GetAvailableDates(context.Context, *GetAvailableDatesParams) (*GetAvailableDatesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableDates not implemented")
}
func (UnimplementedAppointmentCheckerServiceServer) GetEvaluationAvailableTime(context.Context, *GetEvaluationAvailableTimeParams) (*GetEvaluationAvailableTimeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluationAvailableTime not implemented")
}
func (UnimplementedAppointmentCheckerServiceServer) CheckLodgingOverCapacity(context.Context, *CheckLodgingOverCapacityParams) (*CheckLodgingOverCapacityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLodgingOverCapacity not implemented")
}
func (UnimplementedAppointmentCheckerServiceServer) mustEmbedUnimplementedAppointmentCheckerServiceServer() {
}

// UnsafeAppointmentCheckerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentCheckerServiceServer will
// result in compilation errors.
type UnsafeAppointmentCheckerServiceServer interface {
	mustEmbedUnimplementedAppointmentCheckerServiceServer()
}

func RegisterAppointmentCheckerServiceServer(s grpc.ServiceRegistrar, srv AppointmentCheckerServiceServer) {
	s.RegisterService(&AppointmentCheckerService_ServiceDesc, srv)
}

func _AppointmentCheckerService_CheckSaveAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSaveAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentCheckerServiceServer).CheckSaveAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentCheckerService/CheckSaveAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentCheckerServiceServer).CheckSaveAppointment(ctx, req.(*CheckSaveAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentCheckerService_GetAvailableDates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableDatesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentCheckerServiceServer).GetAvailableDates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentCheckerService/GetAvailableDates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentCheckerServiceServer).GetAvailableDates(ctx, req.(*GetAvailableDatesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentCheckerService_GetEvaluationAvailableTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationAvailableTimeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentCheckerServiceServer).GetEvaluationAvailableTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentCheckerService/GetEvaluationAvailableTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentCheckerServiceServer).GetEvaluationAvailableTime(ctx, req.(*GetEvaluationAvailableTimeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentCheckerService_CheckLodgingOverCapacity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLodgingOverCapacityParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentCheckerServiceServer).CheckLodgingOverCapacity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentCheckerService/CheckLodgingOverCapacity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentCheckerServiceServer).CheckLodgingOverCapacity(ctx, req.(*CheckLodgingOverCapacityParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentCheckerService_ServiceDesc is the grpc.ServiceDesc for AppointmentCheckerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentCheckerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.AppointmentCheckerService",
	HandlerType: (*AppointmentCheckerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckSaveAppointment",
			Handler:    _AppointmentCheckerService_CheckSaveAppointment_Handler,
		},
		{
			MethodName: "GetAvailableDates",
			Handler:    _AppointmentCheckerService_GetAvailableDates_Handler,
		},
		{
			MethodName: "GetEvaluationAvailableTime",
			Handler:    _AppointmentCheckerService_GetEvaluationAvailableTime_Handler,
		},
		{
			MethodName: "CheckLodgingOverCapacity",
			Handler:    _AppointmentCheckerService_CheckLodgingOverCapacity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/appointment_checker_api.proto",
}
