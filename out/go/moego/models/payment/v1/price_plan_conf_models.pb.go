// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/price_plan_conf_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// price plan conf
type PricePlanConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// stripe plan id
	StripePlanId string `protobuf:"bytes,2,opt,name=stripe_plan_id,json=stripePlanId,proto3" json:"stripe_plan_id,omitempty"`
	// level
	Level int64 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	// plan type
	PlanType int64 `protobuf:"varint,4,opt,name=plan_type,json=planType,proto3" json:"plan_type,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// title
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// plan name
	PlanName string `protobuf:"bytes,10,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	// is by id
	IsById int64 `protobuf:"varint,11,opt,name=is_by_id,json=isById,proto3" json:"is_by_id,omitempty"`
	// is new pricing
	IsNewPricing int64 `protobuf:"varint,12,opt,name=is_new_pricing,json=isNewPricing,proto3" json:"is_new_pricing,omitempty"`
	// business type
	BusinessType int64 `protobuf:"varint,13,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	// business num
	BusinessNum int64 `protobuf:"varint,14,opt,name=business_num,json=businessNum,proto3" json:"business_num,omitempty"`
}

func (x *PricePlanConf) Reset() {
	*x = PricePlanConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_price_plan_conf_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PricePlanConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricePlanConf) ProtoMessage() {}

func (x *PricePlanConf) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_price_plan_conf_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricePlanConf.ProtoReflect.Descriptor instead.
func (*PricePlanConf) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescGZIP(), []int{0}
}

func (x *PricePlanConf) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PricePlanConf) GetStripePlanId() string {
	if x != nil {
		return x.StripePlanId
	}
	return ""
}

func (x *PricePlanConf) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PricePlanConf) GetPlanType() int64 {
	if x != nil {
		return x.PlanType
	}
	return 0
}

func (x *PricePlanConf) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *PricePlanConf) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PricePlanConf) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PricePlanConf) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *PricePlanConf) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PricePlanConf) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *PricePlanConf) GetIsById() int64 {
	if x != nil {
		return x.IsById
	}
	return 0
}

func (x *PricePlanConf) GetIsNewPricing() int64 {
	if x != nil {
		return x.IsNewPricing
	}
	return 0
}

func (x *PricePlanConf) GetBusinessType() int64 {
	if x != nil {
		return x.BusinessType
	}
	return 0
}

func (x *PricePlanConf) GetBusinessNum() int64 {
	if x != nil {
		return x.BusinessNum
	}
	return 0
}

var File_moego_models_payment_v1_price_plan_conf_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_price_plan_conf_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22,
	0xad, 0x03, 0x0a, 0x0d, 0x50, 0x72, 0x69, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x70,
	0x65, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x62, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x73, 0x42, 0x79, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x73, 0x4e, 0x65, 0x77,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x42,
	0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescData = file_moego_models_payment_v1_price_plan_conf_models_proto_rawDesc
)

func file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_price_plan_conf_models_proto_rawDescData
}

var file_moego_models_payment_v1_price_plan_conf_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_price_plan_conf_models_proto_goTypes = []interface{}{
	(*PricePlanConf)(nil), // 0: moego.models.payment.v1.PricePlanConf
}
var file_moego_models_payment_v1_price_plan_conf_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_price_plan_conf_models_proto_init() }
func file_moego_models_payment_v1_price_plan_conf_models_proto_init() {
	if File_moego_models_payment_v1_price_plan_conf_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_price_plan_conf_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PricePlanConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_price_plan_conf_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_price_plan_conf_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_price_plan_conf_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_price_plan_conf_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_price_plan_conf_models_proto = out.File
	file_moego_models_payment_v1_price_plan_conf_models_proto_rawDesc = nil
	file_moego_models_payment_v1_price_plan_conf_models_proto_goTypes = nil
	file_moego_models_payment_v1_price_plan_conf_models_proto_depIdxs = nil
}
