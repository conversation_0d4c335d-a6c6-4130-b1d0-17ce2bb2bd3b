// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/tip_config_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// smart tip config model
type TipConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tip id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// is enable
	IsEnable bool `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// customized amount for splitting different tips config
	Threshold float64 `protobuf:"fixed64,4,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// lower tip type
	LowerTipType TipPriceType `protobuf:"varint,5,opt,name=lower_tip_type,json=lowerTipType,proto3,enum=moego.models.payment.v1.TipPriceType" json:"lower_tip_type,omitempty"`
	// lower preferred tip type
	LowerPreferredTip PreferredTipType `protobuf:"varint,6,opt,name=lower_preferred_tip,json=lowerPreferredTip,proto3,enum=moego.models.payment.v1.PreferredTipType" json:"lower_preferred_tip,omitempty"`
	// lower tip price config
	LowerTip *TipPriceModel `protobuf:"bytes,7,opt,name=lower_tip,json=lowerTip,proto3" json:"lower_tip,omitempty"`
	// upper tip type
	UpperTipType TipPriceType `protobuf:"varint,8,opt,name=upper_tip_type,json=upperTipType,proto3,enum=moego.models.payment.v1.TipPriceType" json:"upper_tip_type,omitempty"`
	// upper preferred tip type
	UpperPreferredTip PreferredTipType `protobuf:"varint,9,opt,name=upper_preferred_tip,json=upperPreferredTip,proto3,enum=moego.models.payment.v1.PreferredTipType" json:"upper_preferred_tip,omitempty"`
	// upper tip price config
	UpperTip *TipPriceModel `protobuf:"bytes,10,opt,name=upper_tip,json=upperTip,proto3" json:"upper_tip,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *TipConfigModel) Reset() {
	*x = TipConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TipConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TipConfigModel) ProtoMessage() {}

func (x *TipConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TipConfigModel.ProtoReflect.Descriptor instead.
func (*TipConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_models_proto_rawDescGZIP(), []int{0}
}

func (x *TipConfigModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TipConfigModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *TipConfigModel) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *TipConfigModel) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *TipConfigModel) GetLowerTipType() TipPriceType {
	if x != nil {
		return x.LowerTipType
	}
	return TipPriceType_TIP_PRICE_TYPE_AMOUNT
}

func (x *TipConfigModel) GetLowerPreferredTip() PreferredTipType {
	if x != nil {
		return x.LowerPreferredTip
	}
	return PreferredTipType_PREFERRED_TIP_TYPE_UNSPECIFIED
}

func (x *TipConfigModel) GetLowerTip() *TipPriceModel {
	if x != nil {
		return x.LowerTip
	}
	return nil
}

func (x *TipConfigModel) GetUpperTipType() TipPriceType {
	if x != nil {
		return x.UpperTipType
	}
	return TipPriceType_TIP_PRICE_TYPE_AMOUNT
}

func (x *TipConfigModel) GetUpperPreferredTip() PreferredTipType {
	if x != nil {
		return x.UpperPreferredTip
	}
	return PreferredTipType_PREFERRED_TIP_TYPE_UNSPECIFIED
}

func (x *TipConfigModel) GetUpperTip() *TipPriceModel {
	if x != nil {
		return x.UpperTip
	}
	return nil
}

func (x *TipConfigModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *TipConfigModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// tip price model
type TipPriceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// percentage config
	PercentageConfig *TipPriceLevelModel `protobuf:"bytes,1,opt,name=percentage_config,json=percentageConfig,proto3" json:"percentage_config,omitempty"`
	// amount config
	AmountConfig *TipPriceLevelModel `protobuf:"bytes,2,opt,name=amount_config,json=amountConfig,proto3" json:"amount_config,omitempty"`
}

func (x *TipPriceModel) Reset() {
	*x = TipPriceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TipPriceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TipPriceModel) ProtoMessage() {}

func (x *TipPriceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TipPriceModel.ProtoReflect.Descriptor instead.
func (*TipPriceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_models_proto_rawDescGZIP(), []int{1}
}

func (x *TipPriceModel) GetPercentageConfig() *TipPriceLevelModel {
	if x != nil {
		return x.PercentageConfig
	}
	return nil
}

func (x *TipPriceModel) GetAmountConfig() *TipPriceLevelModel {
	if x != nil {
		return x.AmountConfig
	}
	return nil
}

// tip price model
type TipPriceLevelModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// low price
	Low float64 `protobuf:"fixed64,1,opt,name=low,proto3" json:"low,omitempty"`
	// medium price
	Medium float64 `protobuf:"fixed64,2,opt,name=medium,proto3" json:"medium,omitempty"`
	// high price
	High float64 `protobuf:"fixed64,3,opt,name=high,proto3" json:"high,omitempty"`
}

func (x *TipPriceLevelModel) Reset() {
	*x = TipPriceLevelModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TipPriceLevelModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TipPriceLevelModel) ProtoMessage() {}

func (x *TipPriceLevelModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TipPriceLevelModel.ProtoReflect.Descriptor instead.
func (*TipPriceLevelModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_models_proto_rawDescGZIP(), []int{2}
}

func (x *TipPriceLevelModel) GetLow() float64 {
	if x != nil {
		return x.Low
	}
	return 0
}

func (x *TipPriceLevelModel) GetMedium() float64 {
	if x != nil {
		return x.Medium
	}
	return 0
}

func (x *TipPriceLevelModel) GetHigh() float64 {
	if x != nil {
		return x.High
	}
	return 0
}

// smart tip config model in c app client view
type TipConfigModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is enable
	IsEnable bool `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// customized amount for splitting different tips config
	Threshold float64 `protobuf:"fixed64,4,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// lower tip type
	LowerTipType TipPriceType `protobuf:"varint,5,opt,name=lower_tip_type,json=lowerTipType,proto3,enum=moego.models.payment.v1.TipPriceType" json:"lower_tip_type,omitempty"`
	// lower preferred tip type
	LowerPreferredTip PreferredTipType `protobuf:"varint,6,opt,name=lower_preferred_tip,json=lowerPreferredTip,proto3,enum=moego.models.payment.v1.PreferredTipType" json:"lower_preferred_tip,omitempty"`
	// lower tip price config
	LowerTip *TipPriceModel `protobuf:"bytes,7,opt,name=lower_tip,json=lowerTip,proto3" json:"lower_tip,omitempty"`
	// upper tip type
	UpperTipType TipPriceType `protobuf:"varint,8,opt,name=upper_tip_type,json=upperTipType,proto3,enum=moego.models.payment.v1.TipPriceType" json:"upper_tip_type,omitempty"`
	// upper preferred tip type
	UpperPreferredTip PreferredTipType `protobuf:"varint,9,opt,name=upper_preferred_tip,json=upperPreferredTip,proto3,enum=moego.models.payment.v1.PreferredTipType" json:"upper_preferred_tip,omitempty"`
	// upper tip price config
	UpperTip *TipPriceModel `protobuf:"bytes,10,opt,name=upper_tip,json=upperTip,proto3" json:"upper_tip,omitempty"`
}

func (x *TipConfigModelClientView) Reset() {
	*x = TipConfigModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TipConfigModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TipConfigModelClientView) ProtoMessage() {}

func (x *TipConfigModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_tip_config_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TipConfigModelClientView.ProtoReflect.Descriptor instead.
func (*TipConfigModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_models_proto_rawDescGZIP(), []int{3}
}

func (x *TipConfigModelClientView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *TipConfigModelClientView) GetThreshold() float64 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *TipConfigModelClientView) GetLowerTipType() TipPriceType {
	if x != nil {
		return x.LowerTipType
	}
	return TipPriceType_TIP_PRICE_TYPE_AMOUNT
}

func (x *TipConfigModelClientView) GetLowerPreferredTip() PreferredTipType {
	if x != nil {
		return x.LowerPreferredTip
	}
	return PreferredTipType_PREFERRED_TIP_TYPE_UNSPECIFIED
}

func (x *TipConfigModelClientView) GetLowerTip() *TipPriceModel {
	if x != nil {
		return x.LowerTip
	}
	return nil
}

func (x *TipConfigModelClientView) GetUpperTipType() TipPriceType {
	if x != nil {
		return x.UpperTipType
	}
	return TipPriceType_TIP_PRICE_TYPE_AMOUNT
}

func (x *TipConfigModelClientView) GetUpperPreferredTip() PreferredTipType {
	if x != nil {
		return x.UpperPreferredTip
	}
	return PreferredTipType_PREFERRED_TIP_TYPE_UNSPECIFIED
}

func (x *TipConfigModelClientView) GetUpperTip() *TipPriceModel {
	if x != nil {
		return x.UpperTip
	}
	return nil
}

var File_moego_models_payment_v1_tip_config_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_tip_config_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x70, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd0, 0x05, 0x0a, 0x0e,
	0x54, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x6c, 0x6f,
	0x77, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x59, 0x0a, 0x13, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x11, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54,
	0x69, 0x70, 0x12, 0x43, 0x0a, 0x09, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x54, 0x69, 0x70, 0x12, 0x4b, 0x0a, 0x0e, 0x75, 0x70, 0x70, 0x65, 0x72,
	0x5f, 0x74, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x75, 0x70, 0x70, 0x65, 0x72, 0x54, 0x69, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x59, 0x0a, 0x13, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x75, 0x70,
	0x70, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x12,
	0x43, 0x0a, 0x09, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x75, 0x70, 0x70, 0x65,
	0x72, 0x54, 0x69, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xbb,
	0x01, 0x0a, 0x0d, 0x54, 0x69, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x58, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x50, 0x0a, 0x0d, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x52, 0x0a, 0x12,
	0x54, 0x69, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6c, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x69, 0x67, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x68, 0x69, 0x67, 0x68,
	0x22, 0xaf, 0x04, 0x0a, 0x18, 0x54, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x6c, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x54, 0x69,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x59, 0x0a, 0x13, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70,
	0x12, 0x43, 0x0a, 0x09, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x6c, 0x6f, 0x77,
	0x65, 0x72, 0x54, 0x69, 0x70, 0x12, 0x4b, 0x0a, 0x0e, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x75, 0x70, 0x70, 0x65, 0x72, 0x54, 0x69, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x59, 0x0a, 0x13, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x75, 0x70, 0x70, 0x65,
	0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x70, 0x12, 0x43, 0x0a,
	0x09, 0x75, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x75, 0x70, 0x70, 0x65, 0x72, 0x54,
	0x69, 0x70, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_tip_config_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_tip_config_models_proto_rawDescData = file_moego_models_payment_v1_tip_config_models_proto_rawDesc
)

func file_moego_models_payment_v1_tip_config_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_tip_config_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_tip_config_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_tip_config_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_tip_config_models_proto_rawDescData
}

var file_moego_models_payment_v1_tip_config_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_payment_v1_tip_config_models_proto_goTypes = []interface{}{
	(*TipConfigModel)(nil),           // 0: moego.models.payment.v1.TipConfigModel
	(*TipPriceModel)(nil),            // 1: moego.models.payment.v1.TipPriceModel
	(*TipPriceLevelModel)(nil),       // 2: moego.models.payment.v1.TipPriceLevelModel
	(*TipConfigModelClientView)(nil), // 3: moego.models.payment.v1.TipConfigModelClientView
	(TipPriceType)(0),                // 4: moego.models.payment.v1.TipPriceType
	(PreferredTipType)(0),            // 5: moego.models.payment.v1.PreferredTipType
	(*timestamppb.Timestamp)(nil),    // 6: google.protobuf.Timestamp
}
var file_moego_models_payment_v1_tip_config_models_proto_depIdxs = []int32{
	4,  // 0: moego.models.payment.v1.TipConfigModel.lower_tip_type:type_name -> moego.models.payment.v1.TipPriceType
	5,  // 1: moego.models.payment.v1.TipConfigModel.lower_preferred_tip:type_name -> moego.models.payment.v1.PreferredTipType
	1,  // 2: moego.models.payment.v1.TipConfigModel.lower_tip:type_name -> moego.models.payment.v1.TipPriceModel
	4,  // 3: moego.models.payment.v1.TipConfigModel.upper_tip_type:type_name -> moego.models.payment.v1.TipPriceType
	5,  // 4: moego.models.payment.v1.TipConfigModel.upper_preferred_tip:type_name -> moego.models.payment.v1.PreferredTipType
	1,  // 5: moego.models.payment.v1.TipConfigModel.upper_tip:type_name -> moego.models.payment.v1.TipPriceModel
	6,  // 6: moego.models.payment.v1.TipConfigModel.create_time:type_name -> google.protobuf.Timestamp
	6,  // 7: moego.models.payment.v1.TipConfigModel.update_time:type_name -> google.protobuf.Timestamp
	2,  // 8: moego.models.payment.v1.TipPriceModel.percentage_config:type_name -> moego.models.payment.v1.TipPriceLevelModel
	2,  // 9: moego.models.payment.v1.TipPriceModel.amount_config:type_name -> moego.models.payment.v1.TipPriceLevelModel
	4,  // 10: moego.models.payment.v1.TipConfigModelClientView.lower_tip_type:type_name -> moego.models.payment.v1.TipPriceType
	5,  // 11: moego.models.payment.v1.TipConfigModelClientView.lower_preferred_tip:type_name -> moego.models.payment.v1.PreferredTipType
	1,  // 12: moego.models.payment.v1.TipConfigModelClientView.lower_tip:type_name -> moego.models.payment.v1.TipPriceModel
	4,  // 13: moego.models.payment.v1.TipConfigModelClientView.upper_tip_type:type_name -> moego.models.payment.v1.TipPriceType
	5,  // 14: moego.models.payment.v1.TipConfigModelClientView.upper_preferred_tip:type_name -> moego.models.payment.v1.PreferredTipType
	1,  // 15: moego.models.payment.v1.TipConfigModelClientView.upper_tip:type_name -> moego.models.payment.v1.TipPriceModel
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_tip_config_models_proto_init() }
func file_moego_models_payment_v1_tip_config_models_proto_init() {
	if File_moego_models_payment_v1_tip_config_models_proto != nil {
		return
	}
	file_moego_models_payment_v1_tip_config_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_tip_config_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TipConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_tip_config_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TipPriceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_tip_config_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TipPriceLevelModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_tip_config_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TipConfigModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_tip_config_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_tip_config_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_tip_config_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_tip_config_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_tip_config_models_proto = out.File
	file_moego_models_payment_v1_tip_config_models_proto_rawDesc = nil
	file_moego_models_payment_v1_tip_config_models_proto_goTypes = nil
	file_moego_models_payment_v1_tip_config_models_proto_depIdxs = nil
}
