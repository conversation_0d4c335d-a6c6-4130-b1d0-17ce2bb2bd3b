// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/tip_config_enums.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// tip price type
type TipPriceType int32

const (
	// tip price type
	TipPriceType_TIP_PRICE_TYPE_AMOUNT TipPriceType = 0
	// tip amount type
	TipPriceType_TIP_PRICE_TYPE_PERCENT TipPriceType = 1
)

// Enum value maps for TipPriceType.
var (
	TipPriceType_name = map[int32]string{
		0: "TIP_PRICE_TYPE_AMOUNT",
		1: "TIP_PRICE_TYPE_PERCENT",
	}
	TipPriceType_value = map[string]int32{
		"TIP_PRICE_TYPE_AMOUNT":  0,
		"TIP_PRICE_TYPE_PERCENT": 1,
	}
)

func (x TipPriceType) Enum() *TipPriceType {
	p := new(TipPriceType)
	*p = x
	return p
}

func (x TipPriceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TipPriceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_tip_config_enums_proto_enumTypes[0].Descriptor()
}

func (TipPriceType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_tip_config_enums_proto_enumTypes[0]
}

func (x TipPriceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TipPriceType.Descriptor instead.
func (TipPriceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_enums_proto_rawDescGZIP(), []int{0}
}

// preferred tip type
type PreferredTipType int32

const (
	// unspecified
	PreferredTipType_PREFERRED_TIP_TYPE_UNSPECIFIED PreferredTipType = 0
	// low
	PreferredTipType_PREFERRED_TIP_TYPE_LOW PreferredTipType = 1
	// medium
	PreferredTipType_PREFERRED_TIP_TYPE_MEDIUM PreferredTipType = 2
	// high
	PreferredTipType_PREFERRED_TIP_TYPE_HIGH PreferredTipType = 3
)

// Enum value maps for PreferredTipType.
var (
	PreferredTipType_name = map[int32]string{
		0: "PREFERRED_TIP_TYPE_UNSPECIFIED",
		1: "PREFERRED_TIP_TYPE_LOW",
		2: "PREFERRED_TIP_TYPE_MEDIUM",
		3: "PREFERRED_TIP_TYPE_HIGH",
	}
	PreferredTipType_value = map[string]int32{
		"PREFERRED_TIP_TYPE_UNSPECIFIED": 0,
		"PREFERRED_TIP_TYPE_LOW":         1,
		"PREFERRED_TIP_TYPE_MEDIUM":      2,
		"PREFERRED_TIP_TYPE_HIGH":        3,
	}
)

func (x PreferredTipType) Enum() *PreferredTipType {
	p := new(PreferredTipType)
	*p = x
	return p
}

func (x PreferredTipType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PreferredTipType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_tip_config_enums_proto_enumTypes[1].Descriptor()
}

func (PreferredTipType) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_tip_config_enums_proto_enumTypes[1]
}

func (x PreferredTipType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PreferredTipType.Descriptor instead.
func (PreferredTipType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_tip_config_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_payment_v1_tip_config_enums_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_tip_config_enums_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x70, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0x45, 0x0a, 0x0c, 0x54, 0x69, 0x70,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x49, 0x50,
	0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x49, 0x50, 0x5f, 0x50, 0x52, 0x49, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x01,
	0x2a, 0x8e, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x45, 0x44, 0x5f, 0x54, 0x49, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x45,
	0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x45, 0x44, 0x5f, 0x54, 0x49, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49,
	0x55, 0x4d, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x45,
	0x44, 0x5f, 0x54, 0x49, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10,
	0x03, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_tip_config_enums_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_tip_config_enums_proto_rawDescData = file_moego_models_payment_v1_tip_config_enums_proto_rawDesc
)

func file_moego_models_payment_v1_tip_config_enums_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_tip_config_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_tip_config_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_tip_config_enums_proto_rawDescData)
	})
	return file_moego_models_payment_v1_tip_config_enums_proto_rawDescData
}

var file_moego_models_payment_v1_tip_config_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_payment_v1_tip_config_enums_proto_goTypes = []interface{}{
	(TipPriceType)(0),     // 0: moego.models.payment.v1.TipPriceType
	(PreferredTipType)(0), // 1: moego.models.payment.v1.PreferredTipType
}
var file_moego_models_payment_v1_tip_config_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_tip_config_enums_proto_init() }
func file_moego_models_payment_v1_tip_config_enums_proto_init() {
	if File_moego_models_payment_v1_tip_config_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_tip_config_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_tip_config_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_tip_config_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v1_tip_config_enums_proto_enumTypes,
	}.Build()
	File_moego_models_payment_v1_tip_config_enums_proto = out.File
	file_moego_models_payment_v1_tip_config_enums_proto_rawDesc = nil
	file_moego_models_payment_v1_tip_config_enums_proto_goTypes = nil
	file_moego_models_payment_v1_tip_config_enums_proto_depIdxs = nil
}
