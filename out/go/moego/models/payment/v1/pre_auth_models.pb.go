// @since 2024-01-24 15:56:45
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/pre_auth_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The PreAuth model for appointment
type PreAuthCalendarView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pre auth id
	PreAuthId int64 `protobuf:"varint,1,opt,name=pre_auth_id,json=preAuthId,proto3" json:"pre_auth_id,omitempty"`
	// payment id
	PaymentId int64 `protobuf:"varint,2,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// ticket id
	TicketId int64 `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pre auth amount
	PreAuthAmount float64 `protobuf:"fixed64,5,opt,name=pre_auth_amount,json=preAuthAmount,proto3" json:"pre_auth_amount,omitempty"`
	// pre auth time
	PreAuthTime int64 `protobuf:"varint,6,opt,name=pre_auth_time,json=preAuthTime,proto3" json:"pre_auth_time,omitempty"`
	// pre auth status
	PreAuthStatus int32 `protobuf:"varint,7,opt,name=pre_auth_status,json=preAuthStatus,proto3" json:"pre_auth_status,omitempty"`
	// pre auth payment method
	PreAuthPaymentMethod string `protobuf:"bytes,8,opt,name=pre_auth_payment_method,json=preAuthPaymentMethod,proto3" json:"pre_auth_payment_method,omitempty"`
	// pre auth failed message
	PreAuthFailedMessage string `protobuf:"bytes,9,opt,name=pre_auth_failed_message,json=preAuthFailedMessage,proto3" json:"pre_auth_failed_message,omitempty"`
	// pre auth card number
	PreAuthCardNumber string `protobuf:"bytes,10,opt,name=pre_auth_card_number,json=preAuthCardNumber,proto3" json:"pre_auth_card_number,omitempty"`
	// in bspd
	InBspd bool `protobuf:"varint,11,opt,name=in_bspd,json=inBspd,proto3" json:"in_bspd,omitempty"`
	// pre auth finish time
	PreAuthFinishTime int64 `protobuf:"varint,12,opt,name=pre_auth_finish_time,json=preAuthFinishTime,proto3" json:"pre_auth_finish_time,omitempty"`
	// is capture
	IsCapture bool `protobuf:"varint,13,opt,name=is_capture,json=isCapture,proto3" json:"is_capture,omitempty"`
	// convenience fee
	ConvenienceFee float64 `protobuf:"fixed64,14,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// tips amount
	TipsAmount float64 `protobuf:"fixed64,15,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// booking fee
	BookingFee float64 `protobuf:"fixed64,16,opt,name=booking_fee,json=bookingFee,proto3" json:"booking_fee,omitempty"`
	// is open
	IsOpen bool `protobuf:"varint,17,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
}

func (x *PreAuthCalendarView) Reset() {
	*x = PreAuthCalendarView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_pre_auth_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAuthCalendarView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAuthCalendarView) ProtoMessage() {}

func (x *PreAuthCalendarView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_pre_auth_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAuthCalendarView.ProtoReflect.Descriptor instead.
func (*PreAuthCalendarView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_pre_auth_models_proto_rawDescGZIP(), []int{0}
}

func (x *PreAuthCalendarView) GetPreAuthId() int64 {
	if x != nil {
		return x.PreAuthId
	}
	return 0
}

func (x *PreAuthCalendarView) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *PreAuthCalendarView) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *PreAuthCalendarView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PreAuthCalendarView) GetPreAuthAmount() float64 {
	if x != nil {
		return x.PreAuthAmount
	}
	return 0
}

func (x *PreAuthCalendarView) GetPreAuthTime() int64 {
	if x != nil {
		return x.PreAuthTime
	}
	return 0
}

func (x *PreAuthCalendarView) GetPreAuthStatus() int32 {
	if x != nil {
		return x.PreAuthStatus
	}
	return 0
}

func (x *PreAuthCalendarView) GetPreAuthPaymentMethod() string {
	if x != nil {
		return x.PreAuthPaymentMethod
	}
	return ""
}

func (x *PreAuthCalendarView) GetPreAuthFailedMessage() string {
	if x != nil {
		return x.PreAuthFailedMessage
	}
	return ""
}

func (x *PreAuthCalendarView) GetPreAuthCardNumber() string {
	if x != nil {
		return x.PreAuthCardNumber
	}
	return ""
}

func (x *PreAuthCalendarView) GetInBspd() bool {
	if x != nil {
		return x.InBspd
	}
	return false
}

func (x *PreAuthCalendarView) GetPreAuthFinishTime() int64 {
	if x != nil {
		return x.PreAuthFinishTime
	}
	return 0
}

func (x *PreAuthCalendarView) GetIsCapture() bool {
	if x != nil {
		return x.IsCapture
	}
	return false
}

func (x *PreAuthCalendarView) GetConvenienceFee() float64 {
	if x != nil {
		return x.ConvenienceFee
	}
	return 0
}

func (x *PreAuthCalendarView) GetTipsAmount() float64 {
	if x != nil {
		return x.TipsAmount
	}
	return 0
}

func (x *PreAuthCalendarView) GetBookingFee() float64 {
	if x != nil {
		return x.BookingFee
	}
	return 0
}

func (x *PreAuthCalendarView) GetIsOpen() bool {
	if x != nil {
		return x.IsOpen
	}
	return false
}

var File_moego_models_payment_v1_pre_auth_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_pre_auth_models_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22, 0x92, 0x05, 0x0a, 0x13, 0x50, 0x72, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x65,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x35, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70, 0x72, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x2f, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x5f, 0x62, 0x73, 0x70, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x6e, 0x42, 0x73, 0x70, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x72, 0x65,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x65, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x65, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x46, 0x65, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x42, 0x7b, 0x0a,
	0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_pre_auth_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_pre_auth_models_proto_rawDescData = file_moego_models_payment_v1_pre_auth_models_proto_rawDesc
)

func file_moego_models_payment_v1_pre_auth_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_pre_auth_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_pre_auth_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_pre_auth_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_pre_auth_models_proto_rawDescData
}

var file_moego_models_payment_v1_pre_auth_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_pre_auth_models_proto_goTypes = []interface{}{
	(*PreAuthCalendarView)(nil), // 0: moego.models.payment.v1.PreAuthCalendarView
}
var file_moego_models_payment_v1_pre_auth_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_pre_auth_models_proto_init() }
func file_moego_models_payment_v1_pre_auth_models_proto_init() {
	if File_moego_models_payment_v1_pre_auth_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_pre_auth_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreAuthCalendarView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_pre_auth_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_pre_auth_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_pre_auth_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_pre_auth_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_pre_auth_models_proto = out.File
	file_moego_models_payment_v1_pre_auth_models_proto_rawDesc = nil
	file_moego_models_payment_v1_pre_auth_models_proto_goTypes = nil
	file_moego_models_payment_v1_pre_auth_models_proto_depIdxs = nil
}
