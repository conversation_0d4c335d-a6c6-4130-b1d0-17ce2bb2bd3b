// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/us_bank_account_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// us bank account model
type UsBankAccountModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the payment method
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// account holder type
	AccountHolderType string `protobuf:"bytes,2,opt,name=account_holder_type,json=accountHolderType,proto3" json:"account_holder_type,omitempty"`
	// account type
	AccountType string `protobuf:"bytes,3,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	// bank name
	BankName string `protobuf:"bytes,4,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// financial connections account
	FinancialConnectionsAccount string `protobuf:"bytes,5,opt,name=financial_connections_account,json=financialConnectionsAccount,proto3" json:"financial_connections_account,omitempty"`
	// fingerprint
	Fingerprint string `protobuf:"bytes,6,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	// last4
	Last4 string `protobuf:"bytes,7,opt,name=last4,proto3" json:"last4,omitempty"`
	// routing number
	RoutingNumber string `protobuf:"bytes,8,opt,name=routing_number,json=routingNumber,proto3" json:"routing_number,omitempty"`
	// status details
	StatusDetails string `protobuf:"bytes,9,opt,name=status_details,json=statusDetails,proto3" json:"status_details,omitempty"`
}

func (x *UsBankAccountModel) Reset() {
	*x = UsBankAccountModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_us_bank_account_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsBankAccountModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsBankAccountModel) ProtoMessage() {}

func (x *UsBankAccountModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_us_bank_account_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsBankAccountModel.ProtoReflect.Descriptor instead.
func (*UsBankAccountModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_us_bank_account_models_proto_rawDescGZIP(), []int{0}
}

func (x *UsBankAccountModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UsBankAccountModel) GetAccountHolderType() string {
	if x != nil {
		return x.AccountHolderType
	}
	return ""
}

func (x *UsBankAccountModel) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *UsBankAccountModel) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *UsBankAccountModel) GetFinancialConnectionsAccount() string {
	if x != nil {
		return x.FinancialConnectionsAccount
	}
	return ""
}

func (x *UsBankAccountModel) GetFingerprint() string {
	if x != nil {
		return x.Fingerprint
	}
	return ""
}

func (x *UsBankAccountModel) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

func (x *UsBankAccountModel) GetRoutingNumber() string {
	if x != nil {
		return x.RoutingNumber
	}
	return ""
}

func (x *UsBankAccountModel) GetStatusDetails() string {
	if x != nil {
		return x.StatusDetails
	}
	return ""
}

var File_moego_models_payment_v1_us_bank_account_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_us_bank_account_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22,
	0xde, 0x02, 0x0a, 0x12, 0x55, 0x73, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61,
	0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x1d, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69,
	0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x61, 0x73, 0x74, 0x34, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x73,
	0x74, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x6f, 0x75, 0x74,
	0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_us_bank_account_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_us_bank_account_models_proto_rawDescData = file_moego_models_payment_v1_us_bank_account_models_proto_rawDesc
)

func file_moego_models_payment_v1_us_bank_account_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_us_bank_account_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_us_bank_account_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_us_bank_account_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_us_bank_account_models_proto_rawDescData
}

var file_moego_models_payment_v1_us_bank_account_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_us_bank_account_models_proto_goTypes = []interface{}{
	(*UsBankAccountModel)(nil), // 0: moego.models.payment.v1.UsBankAccountModel
}
var file_moego_models_payment_v1_us_bank_account_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_us_bank_account_models_proto_init() }
func file_moego_models_payment_v1_us_bank_account_models_proto_init() {
	if File_moego_models_payment_v1_us_bank_account_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_us_bank_account_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsBankAccountModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_us_bank_account_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_us_bank_account_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_us_bank_account_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_us_bank_account_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_us_bank_account_models_proto = out.File
	file_moego_models_payment_v1_us_bank_account_models_proto_rawDesc = nil
	file_moego_models_payment_v1_us_bank_account_models_proto_goTypes = nil
	file_moego_models_payment_v1_us_bank_account_models_proto_depIdxs = nil
}
