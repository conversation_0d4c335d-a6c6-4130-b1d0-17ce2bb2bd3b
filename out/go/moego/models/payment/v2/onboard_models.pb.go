// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v2/onboard_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Onboard 的一个步骤
type OnboardStep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 步骤标识，前端用来区分步骤、展示对应的文案
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 待验证的信息
	Verifications []*OnboardVerification `protobuf:"bytes,2,rep,name=verifications,proto3" json:"verifications,omitempty"`
}

func (x *OnboardStep) Reset() {
	*x = OnboardStep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardStep) ProtoMessage() {}

func (x *OnboardStep) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardStep.ProtoReflect.Descriptor instead.
func (*OnboardStep) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{0}
}

func (x *OnboardStep) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *OnboardStep) GetVerifications() []*OnboardVerification {
	if x != nil {
		return x.Verifications
	}
	return nil
}

// 某个具体的验证记录
type OnboardVerification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态
	Status OnboardVerificationStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.payment.v2.OnboardVerificationStatus" json:"status,omitempty"`
	// 依赖该验证的 capability，不同 channel 可能取值有所不同。
	Capability string `protobuf:"bytes,2,opt,name=capability,proto3" json:"capability,omitempty"`
	// 信息，status 不为 Valid 的时候有效。
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *OnboardVerification) Reset() {
	*x = OnboardVerification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnboardVerification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardVerification) ProtoMessage() {}

func (x *OnboardVerification) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardVerification.ProtoReflect.Descriptor instead.
func (*OnboardVerification) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{1}
}

func (x *OnboardVerification) GetStatus() OnboardVerificationStatus {
	if x != nil {
		return x.Status
	}
	return OnboardVerificationStatus_ONBOARD_VERIFICATION_STATUS_UNSPECIFIED
}

func (x *OnboardVerification) GetCapability() string {
	if x != nil {
		return x.Capability
	}
	return ""
}

func (x *OnboardVerification) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// 一个渠道账号
type ChannelAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Entity type
	EntityType EntityType `protobuf:"varint,2,opt,name=entity_type,json=entityType,proto3,enum=moego.models.payment.v2.EntityType" json:"entity_type,omitempty"`
	// Entity ID.
	EntityId int64 `protobuf:"varint,3,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// channel type
	ChannelType ChannelType `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// Channel account ID
	ChannelAccountId string `protobuf:"bytes,5,opt,name=channel_account_id,json=channelAccountId,proto3" json:"channel_account_id,omitempty"`
	// Account (verification) status
	Status ChannelAccountStatus `protobuf:"varint,6,opt,name=status,proto3,enum=moego.models.payment.v2.ChannelAccountStatus" json:"status,omitempty"`
	// Charges enabled
	ChargesEnabled bool `protobuf:"varint,7,opt,name=charges_enabled,json=chargesEnabled,proto3" json:"charges_enabled,omitempty"`
	// Payouts enabled
	PayoutsEnabled bool `protobuf:"varint,8,opt,name=payouts_enabled,json=payoutsEnabled,proto3" json:"payouts_enabled,omitempty"`
}

func (x *ChannelAccount) Reset() {
	*x = ChannelAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccount) ProtoMessage() {}

func (x *ChannelAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccount.ProtoReflect.Descriptor instead.
func (*ChannelAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{2}
}

func (x *ChannelAccount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChannelAccount) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (x *ChannelAccount) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *ChannelAccount) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *ChannelAccount) GetChannelAccountId() string {
	if x != nil {
		return x.ChannelAccountId
	}
	return ""
}

func (x *ChannelAccount) GetStatus() ChannelAccountStatus {
	if x != nil {
		return x.Status
	}
	return ChannelAccountStatus_CHANNEL_ACCOUNT_STATUS_UNSPECIFIED
}

func (x *ChannelAccount) GetChargesEnabled() bool {
	if x != nil {
		return x.ChargesEnabled
	}
	return false
}

func (x *ChannelAccount) GetPayoutsEnabled() bool {
	if x != nil {
		return x.PayoutsEnabled
	}
	return false
}

// The extra for an channel account.
type ChannelAccountExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The extra
	//
	// Types that are assignable to Extra:
	//
	//	*ChannelAccountExtra_AdyenCompanyExtra_
	//	*ChannelAccountExtra_AdyenBusinessExtra_
	Extra isChannelAccountExtra_Extra `protobuf_oneof:"extra"`
}

func (x *ChannelAccountExtra) Reset() {
	*x = ChannelAccountExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccountExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccountExtra) ProtoMessage() {}

func (x *ChannelAccountExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccountExtra.ProtoReflect.Descriptor instead.
func (*ChannelAccountExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{3}
}

func (m *ChannelAccountExtra) GetExtra() isChannelAccountExtra_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *ChannelAccountExtra) GetAdyenCompanyExtra() *ChannelAccountExtra_AdyenCompanyExtra {
	if x, ok := x.GetExtra().(*ChannelAccountExtra_AdyenCompanyExtra_); ok {
		return x.AdyenCompanyExtra
	}
	return nil
}

func (x *ChannelAccountExtra) GetAdyenBusinessExtra() *ChannelAccountExtra_AdyenBusinessExtra {
	if x, ok := x.GetExtra().(*ChannelAccountExtra_AdyenBusinessExtra_); ok {
		return x.AdyenBusinessExtra
	}
	return nil
}

type isChannelAccountExtra_Extra interface {
	isChannelAccountExtra_Extra()
}

type ChannelAccountExtra_AdyenCompanyExtra_ struct {
	// Adyen Company account extra
	AdyenCompanyExtra *ChannelAccountExtra_AdyenCompanyExtra `protobuf:"bytes,1,opt,name=adyen_company_extra,json=adyenCompanyExtra,proto3,oneof"`
}

type ChannelAccountExtra_AdyenBusinessExtra_ struct {
	// Adyen Business account extra
	AdyenBusinessExtra *ChannelAccountExtra_AdyenBusinessExtra `protobuf:"bytes,2,opt,name=adyen_business_extra,json=adyenBusinessExtra,proto3,oneof"`
}

func (*ChannelAccountExtra_AdyenCompanyExtra_) isChannelAccountExtra_Extra() {}

func (*ChannelAccountExtra_AdyenBusinessExtra_) isChannelAccountExtra_Extra() {}

// The bank account
type BankAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 渠道侧 ID，这边先不存 Bank Account，所以没有 ID，只有渠道 ID
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 卡号信息
	//
	// Types that are assignable to Account:
	//
	//	*BankAccount_Us
	//	*BankAccount_Uk
	//	*BankAccount_Au
	//	*BankAccount_Ca
	Account isBankAccount_Account `protobuf_oneof:"account"`
}

func (x *BankAccount) Reset() {
	*x = BankAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccount) ProtoMessage() {}

func (x *BankAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccount.ProtoReflect.Descriptor instead.
func (*BankAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{4}
}

func (x *BankAccount) GetChannelType() ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChannelType_CHANNEL_TYPE_UNSPECIFIED
}

func (x *BankAccount) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (m *BankAccount) GetAccount() isBankAccount_Account {
	if m != nil {
		return m.Account
	}
	return nil
}

func (x *BankAccount) GetUs() *USAccount {
	if x, ok := x.GetAccount().(*BankAccount_Us); ok {
		return x.Us
	}
	return nil
}

func (x *BankAccount) GetUk() *UKAccount {
	if x, ok := x.GetAccount().(*BankAccount_Uk); ok {
		return x.Uk
	}
	return nil
}

func (x *BankAccount) GetAu() *AUAccount {
	if x, ok := x.GetAccount().(*BankAccount_Au); ok {
		return x.Au
	}
	return nil
}

func (x *BankAccount) GetCa() *CAAccount {
	if x, ok := x.GetAccount().(*BankAccount_Ca); ok {
		return x.Ca
	}
	return nil
}

type isBankAccount_Account interface {
	isBankAccount_Account()
}

type BankAccount_Us struct {
	// US
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: 误报。 --)
	Us *USAccount `protobuf:"bytes,3,opt,name=us,proto3,oneof"`
}

type BankAccount_Uk struct {
	// UK
	Uk *UKAccount `protobuf:"bytes,4,opt,name=uk,proto3,oneof"`
}

type BankAccount_Au struct {
	// AU
	Au *AUAccount `protobuf:"bytes,5,opt,name=au,proto3,oneof"`
}

type BankAccount_Ca struct {
	// CA
	Ca *CAAccount `protobuf:"bytes,6,opt,name=ca,proto3,oneof"`
}

func (*BankAccount_Us) isBankAccount_Account() {}

func (*BankAccount_Uk) isBankAccount_Account() {}

func (*BankAccount_Au) isBankAccount_Account() {}

func (*BankAccount_Ca) isBankAccount_Account() {}

// US account number
type USAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Routing number
	RoutingNumber string `protobuf:"bytes,1,opt,name=routing_number,json=routingNumber,proto3" json:"routing_number,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *USAccount) Reset() {
	*x = USAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USAccount) ProtoMessage() {}

func (x *USAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USAccount.ProtoReflect.Descriptor instead.
func (*USAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{5}
}

func (x *USAccount) GetRoutingNumber() string {
	if x != nil {
		return x.RoutingNumber
	}
	return ""
}

func (x *USAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// UK account number
type UKAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Sort code
	SortCode string `protobuf:"bytes,1,opt,name=sort_code,json=sortCode,proto3" json:"sort_code,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *UKAccount) Reset() {
	*x = UKAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UKAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UKAccount) ProtoMessage() {}

func (x *UKAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UKAccount.ProtoReflect.Descriptor instead.
func (*UKAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{6}
}

func (x *UKAccount) GetSortCode() string {
	if x != nil {
		return x.SortCode
	}
	return ""
}

func (x *UKAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// Australia account number
type AUAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// BSB code
	BsbCode string `protobuf:"bytes,1,opt,name=bsb_code,json=bsbCode,proto3" json:"bsb_code,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *AUAccount) Reset() {
	*x = AUAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AUAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AUAccount) ProtoMessage() {}

func (x *AUAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AUAccount.ProtoReflect.Descriptor instead.
func (*AUAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{7}
}

func (x *AUAccount) GetBsbCode() string {
	if x != nil {
		return x.BsbCode
	}
	return ""
}

func (x *AUAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// Canada account number
type CAAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Transit number
	TransitNumber string `protobuf:"bytes,1,opt,name=transit_number,json=transitNumber,proto3" json:"transit_number,omitempty"`
	// Institution number
	InstitutionNumber string `protobuf:"bytes,2,opt,name=institution_number,json=institutionNumber,proto3" json:"institution_number,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *CAAccount) Reset() {
	*x = CAAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CAAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CAAccount) ProtoMessage() {}

func (x *CAAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CAAccount.ProtoReflect.Descriptor instead.
func (*CAAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{8}
}

func (x *CAAccount) GetTransitNumber() string {
	if x != nil {
		return x.TransitNumber
	}
	return ""
}

func (x *CAAccount) GetInstitutionNumber() string {
	if x != nil {
		return x.InstitutionNumber
	}
	return ""
}

func (x *CAAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// The extra for an Adyen company-level account.
type ChannelAccountExtra_AdyenCompanyExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID for Adyen business line
	BusinessLineId string `protobuf:"bytes,1,opt,name=business_line_id,json=businessLineId,proto3" json:"business_line_id,omitempty"`
	// ID for Adyen account holder
	AccountHolderId string `protobuf:"bytes,2,opt,name=account_holder_id,json=accountHolderId,proto3" json:"account_holder_id,omitempty"`
}

func (x *ChannelAccountExtra_AdyenCompanyExtra) Reset() {
	*x = ChannelAccountExtra_AdyenCompanyExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccountExtra_AdyenCompanyExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccountExtra_AdyenCompanyExtra) ProtoMessage() {}

func (x *ChannelAccountExtra_AdyenCompanyExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccountExtra_AdyenCompanyExtra.ProtoReflect.Descriptor instead.
func (*ChannelAccountExtra_AdyenCompanyExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ChannelAccountExtra_AdyenCompanyExtra) GetBusinessLineId() string {
	if x != nil {
		return x.BusinessLineId
	}
	return ""
}

func (x *ChannelAccountExtra_AdyenCompanyExtra) GetAccountHolderId() string {
	if x != nil {
		return x.AccountHolderId
	}
	return ""
}

// The extra for an Adyen business-level account.
type ChannelAccountExtra_AdyenBusinessExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID for Adyen store
	StoreId string `protobuf:"bytes,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// The reference of the Adyen store
	StoreReference string `protobuf:"bytes,2,opt,name=store_reference,json=storeReference,proto3" json:"store_reference,omitempty"`
}

func (x *ChannelAccountExtra_AdyenBusinessExtra) Reset() {
	*x = ChannelAccountExtra_AdyenBusinessExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccountExtra_AdyenBusinessExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccountExtra_AdyenBusinessExtra) ProtoMessage() {}

func (x *ChannelAccountExtra_AdyenBusinessExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v2_onboard_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccountExtra_AdyenBusinessExtra.ProtoReflect.Descriptor instead.
func (*ChannelAccountExtra_AdyenBusinessExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP(), []int{3, 1}
}

func (x *ChannelAccountExtra_AdyenBusinessExtra) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *ChannelAccountExtra_AdyenBusinessExtra) GetStoreReference() string {
	if x != nil {
		return x.StoreReference
	}
	return ""
}

var File_moego_models_payment_v2_onboard_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v2_onboard_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x73, 0x0a, 0x0b, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x74, 0x65, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x52, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x13, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x70,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x93,
	0x03, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x44, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x22, 0xca, 0x03, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x70, 0x0a, 0x13,
	0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x00, 0x52, 0x11, 0x61, 0x64, 0x79,
	0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x73,
	0x0a, 0x14, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x00, 0x52,
	0x12, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x1a, 0x69, 0x0a, 0x11, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x58,
	0x0a, 0x12, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x22, 0xd8, 0x02, 0x0a, 0x0b, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x02, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x53, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x02, 0x75, 0x73, 0x12,
	0x34, 0x0a, 0x02, 0x75, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x4b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x02, 0x75, 0x6b, 0x12, 0x34, 0x0a, 0x02, 0x61, 0x75, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x55, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x02, 0x61, 0x75, 0x12, 0x34, 0x0a, 0x02, 0x63,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x41, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x02, 0x63,
	0x61, 0x42, 0x09, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x59, 0x0a, 0x09,
	0x55, 0x53, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x09, 0x55, 0x4b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x4d, 0x0a, 0x09, 0x41, 0x55, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x73, 0x62, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x73, 0x62, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x88, 0x01, 0x0a, 0x09, 0x43, 0x41, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12,
	0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v2_onboard_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v2_onboard_models_proto_rawDescData = file_moego_models_payment_v2_onboard_models_proto_rawDesc
)

func file_moego_models_payment_v2_onboard_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v2_onboard_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v2_onboard_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v2_onboard_models_proto_rawDescData)
	})
	return file_moego_models_payment_v2_onboard_models_proto_rawDescData
}

var file_moego_models_payment_v2_onboard_models_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_models_payment_v2_onboard_models_proto_goTypes = []interface{}{
	(*OnboardStep)(nil),                            // 0: moego.models.payment.v2.OnboardStep
	(*OnboardVerification)(nil),                    // 1: moego.models.payment.v2.OnboardVerification
	(*ChannelAccount)(nil),                         // 2: moego.models.payment.v2.ChannelAccount
	(*ChannelAccountExtra)(nil),                    // 3: moego.models.payment.v2.ChannelAccountExtra
	(*BankAccount)(nil),                            // 4: moego.models.payment.v2.BankAccount
	(*USAccount)(nil),                              // 5: moego.models.payment.v2.USAccount
	(*UKAccount)(nil),                              // 6: moego.models.payment.v2.UKAccount
	(*AUAccount)(nil),                              // 7: moego.models.payment.v2.AUAccount
	(*CAAccount)(nil),                              // 8: moego.models.payment.v2.CAAccount
	(*ChannelAccountExtra_AdyenCompanyExtra)(nil),  // 9: moego.models.payment.v2.ChannelAccountExtra.AdyenCompanyExtra
	(*ChannelAccountExtra_AdyenBusinessExtra)(nil), // 10: moego.models.payment.v2.ChannelAccountExtra.AdyenBusinessExtra
	(OnboardVerificationStatus)(0),                 // 11: moego.models.payment.v2.OnboardVerificationStatus
	(EntityType)(0),                                // 12: moego.models.payment.v2.EntityType
	(ChannelType)(0),                               // 13: moego.models.payment.v2.ChannelType
	(ChannelAccountStatus)(0),                      // 14: moego.models.payment.v2.ChannelAccountStatus
}
var file_moego_models_payment_v2_onboard_models_proto_depIdxs = []int32{
	1,  // 0: moego.models.payment.v2.OnboardStep.verifications:type_name -> moego.models.payment.v2.OnboardVerification
	11, // 1: moego.models.payment.v2.OnboardVerification.status:type_name -> moego.models.payment.v2.OnboardVerificationStatus
	12, // 2: moego.models.payment.v2.ChannelAccount.entity_type:type_name -> moego.models.payment.v2.EntityType
	13, // 3: moego.models.payment.v2.ChannelAccount.channel_type:type_name -> moego.models.payment.v2.ChannelType
	14, // 4: moego.models.payment.v2.ChannelAccount.status:type_name -> moego.models.payment.v2.ChannelAccountStatus
	9,  // 5: moego.models.payment.v2.ChannelAccountExtra.adyen_company_extra:type_name -> moego.models.payment.v2.ChannelAccountExtra.AdyenCompanyExtra
	10, // 6: moego.models.payment.v2.ChannelAccountExtra.adyen_business_extra:type_name -> moego.models.payment.v2.ChannelAccountExtra.AdyenBusinessExtra
	13, // 7: moego.models.payment.v2.BankAccount.channel_type:type_name -> moego.models.payment.v2.ChannelType
	5,  // 8: moego.models.payment.v2.BankAccount.us:type_name -> moego.models.payment.v2.USAccount
	6,  // 9: moego.models.payment.v2.BankAccount.uk:type_name -> moego.models.payment.v2.UKAccount
	7,  // 10: moego.models.payment.v2.BankAccount.au:type_name -> moego.models.payment.v2.AUAccount
	8,  // 11: moego.models.payment.v2.BankAccount.ca:type_name -> moego.models.payment.v2.CAAccount
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v2_onboard_models_proto_init() }
func file_moego_models_payment_v2_onboard_models_proto_init() {
	if File_moego_models_payment_v2_onboard_models_proto != nil {
		return
	}
	file_moego_models_payment_v2_common_enums_proto_init()
	file_moego_models_payment_v2_onboard_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardStep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnboardVerification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccountExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UKAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AUAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CAAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccountExtra_AdyenCompanyExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v2_onboard_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccountExtra_AdyenBusinessExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_payment_v2_onboard_models_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*ChannelAccountExtra_AdyenCompanyExtra_)(nil),
		(*ChannelAccountExtra_AdyenBusinessExtra_)(nil),
	}
	file_moego_models_payment_v2_onboard_models_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*BankAccount_Us)(nil),
		(*BankAccount_Uk)(nil),
		(*BankAccount_Au)(nil),
		(*BankAccount_Ca)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v2_onboard_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v2_onboard_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v2_onboard_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v2_onboard_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v2_onboard_models_proto = out.File
	file_moego_models_payment_v2_onboard_models_proto_rawDesc = nil
	file_moego_models_payment_v2_onboard_models_proto_goTypes = nil
	file_moego_models_payment_v2_onboard_models_proto_depIdxs = nil
}
