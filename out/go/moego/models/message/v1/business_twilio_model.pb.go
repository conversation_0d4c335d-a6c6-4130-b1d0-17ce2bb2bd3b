// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/business_twilio_model.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// business twilio model
type BusinessTwilioModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// twilio sid
	TwilioSid string `protobuf:"bytes,2,opt,name=twilio_sid,json=twilioSid,proto3" json:"twilio_sid,omitempty"`
	// twilio token
	TwilioToken string `protobuf:"bytes,3,opt,name=twilio_token,json=twilioToken,proto3" json:"twilio_token,omitempty"`
	// twilio phone number
	TwilioNumber string `protobuf:"bytes,4,opt,name=twilio_number,json=twilioNumber,proto3" json:"twilio_number,omitempty"`
	// twilio number alias
	FriendlyName string `protobuf:"bytes,5,opt,name=friendly_name,json=friendlyName,proto3" json:"friendly_name,omitempty"`
	// can handle type, call forwarding only for US/CA
	CallHandleType bool `protobuf:"varint,6,opt,name=call_handle_type,json=callHandleType,proto3" json:"call_handle_type,omitempty"`
	// call forwarding
	CanPhoneNumber bool `protobuf:"varint,7,opt,name=can_phone_number,json=canPhoneNumber,proto3" json:"can_phone_number,omitempty"`
	// reply message
	ReplyMessage string `protobuf:"bytes,8,opt,name=reply_message,json=replyMessage,proto3" json:"reply_message,omitempty"`
	// twilio number assign status
	AssignStatue TwilioNumberAssignStatus `protobuf:"varint,9,opt,name=assign_statue,json=assignStatue,proto3,enum=moego.models.message.v1.TwilioNumberAssignStatus" json:"assign_statue,omitempty"`
	// twilio number use status
	UseStatus TwilioNumberUseStatus `protobuf:"varint,10,opt,name=use_status,json=useStatus,proto3,enum=moego.models.message.v1.TwilioNumberUseStatus" json:"use_status,omitempty"`
	// share status
	Share bool `protobuf:"varint,11,opt,name=share,proto3" json:"share,omitempty"`
	// bind time
	CreateTime int64 `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// note, bind failure reason
	Remark string `protobuf:"bytes,13,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *BusinessTwilioModel) Reset() {
	*x = BusinessTwilioModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_business_twilio_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessTwilioModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessTwilioModel) ProtoMessage() {}

func (x *BusinessTwilioModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_business_twilio_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessTwilioModel.ProtoReflect.Descriptor instead.
func (*BusinessTwilioModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_business_twilio_model_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessTwilioModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessTwilioModel) GetTwilioSid() string {
	if x != nil {
		return x.TwilioSid
	}
	return ""
}

func (x *BusinessTwilioModel) GetTwilioToken() string {
	if x != nil {
		return x.TwilioToken
	}
	return ""
}

func (x *BusinessTwilioModel) GetTwilioNumber() string {
	if x != nil {
		return x.TwilioNumber
	}
	return ""
}

func (x *BusinessTwilioModel) GetFriendlyName() string {
	if x != nil {
		return x.FriendlyName
	}
	return ""
}

func (x *BusinessTwilioModel) GetCallHandleType() bool {
	if x != nil {
		return x.CallHandleType
	}
	return false
}

func (x *BusinessTwilioModel) GetCanPhoneNumber() bool {
	if x != nil {
		return x.CanPhoneNumber
	}
	return false
}

func (x *BusinessTwilioModel) GetReplyMessage() string {
	if x != nil {
		return x.ReplyMessage
	}
	return ""
}

func (x *BusinessTwilioModel) GetAssignStatue() TwilioNumberAssignStatus {
	if x != nil {
		return x.AssignStatue
	}
	return TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_UNSPECIFIED
}

func (x *BusinessTwilioModel) GetUseStatus() TwilioNumberUseStatus {
	if x != nil {
		return x.UseStatus
	}
	return TwilioNumberUseStatus_TWILIO_NUMBER_USE_STATUS_UNSPECIFIED
}

func (x *BusinessTwilioModel) GetShare() bool {
	if x != nil {
		return x.Share
	}
	return false
}

func (x *BusinessTwilioModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *BusinessTwilioModel) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// business twilio number view
type BusinessTwilioNumberView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// twilio number
	TwilioNumber string `protobuf:"bytes,2,opt,name=twilio_number,json=twilioNumber,proto3" json:"twilio_number,omitempty"`
}

func (x *BusinessTwilioNumberView) Reset() {
	*x = BusinessTwilioNumberView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_business_twilio_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessTwilioNumberView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessTwilioNumberView) ProtoMessage() {}

func (x *BusinessTwilioNumberView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_business_twilio_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessTwilioNumberView.ProtoReflect.Descriptor instead.
func (*BusinessTwilioNumberView) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_business_twilio_model_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessTwilioNumberView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessTwilioNumberView) GetTwilioNumber() string {
	if x != nil {
		return x.TwilioNumber
	}
	return ""
}

var File_moego_models_message_v1_business_twilio_model_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_business_twilio_model_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x2a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x04, 0x0a, 0x13, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x53,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x61, 0x6e,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6c,
	0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x65,
	0x12, 0x4d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x55, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x75, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x60,
	0x0a, 0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_business_twilio_model_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_business_twilio_model_proto_rawDescData = file_moego_models_message_v1_business_twilio_model_proto_rawDesc
)

func file_moego_models_message_v1_business_twilio_model_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_business_twilio_model_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_business_twilio_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_business_twilio_model_proto_rawDescData)
	})
	return file_moego_models_message_v1_business_twilio_model_proto_rawDescData
}

var file_moego_models_message_v1_business_twilio_model_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_message_v1_business_twilio_model_proto_goTypes = []interface{}{
	(*BusinessTwilioModel)(nil),      // 0: moego.models.message.v1.BusinessTwilioModel
	(*BusinessTwilioNumberView)(nil), // 1: moego.models.message.v1.BusinessTwilioNumberView
	(TwilioNumberAssignStatus)(0),    // 2: moego.models.message.v1.TwilioNumberAssignStatus
	(TwilioNumberUseStatus)(0),       // 3: moego.models.message.v1.TwilioNumberUseStatus
}
var file_moego_models_message_v1_business_twilio_model_proto_depIdxs = []int32{
	2, // 0: moego.models.message.v1.BusinessTwilioModel.assign_statue:type_name -> moego.models.message.v1.TwilioNumberAssignStatus
	3, // 1: moego.models.message.v1.BusinessTwilioModel.use_status:type_name -> moego.models.message.v1.TwilioNumberUseStatus
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_business_twilio_model_proto_init() }
func file_moego_models_message_v1_business_twilio_model_proto_init() {
	if File_moego_models_message_v1_business_twilio_model_proto != nil {
		return
	}
	file_moego_models_message_v1_twilio_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v1_business_twilio_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessTwilioModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_business_twilio_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessTwilioNumberView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_business_twilio_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_business_twilio_model_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_business_twilio_model_proto_depIdxs,
		MessageInfos:      file_moego_models_message_v1_business_twilio_model_proto_msgTypes,
	}.Build()
	File_moego_models_message_v1_business_twilio_model_proto = out.File
	file_moego_models_message_v1_business_twilio_model_proto_rawDesc = nil
	file_moego_models_message_v1_business_twilio_model_proto_goTypes = nil
	file_moego_models_message_v1_business_twilio_model_proto_depIdxs = nil
}
