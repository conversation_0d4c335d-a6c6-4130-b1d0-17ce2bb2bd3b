// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/message_enums.proto

package messagepb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the system supported message type
// see customer detail -> Message/Email preference
type MessageType int32

const (
	// unspecified
	MessageType_MESSAGE_TYPE_UNSPECIFIED MessageType = 0
	// sms
	MessageType_MESSAGE_TYPE_SMS MessageType = 1
	// email
	MessageType_MESSAGE_TYPE_EMAIL MessageType = 2
	// call
	MessageType_MESSAGE_TYPE_CALL MessageType = 3
	// app
	MessageType_MESSAGE_TYPE_APP MessageType = 4
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_SMS",
		2: "MESSAGE_TYPE_EMAIL",
		3: "MESSAGE_TYPE_CALL",
		4: "MESSAGE_TYPE_APP",
	}
	MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED": 0,
		"MESSAGE_TYPE_SMS":         1,
		"MESSAGE_TYPE_EMAIL":       2,
		"MESSAGE_TYPE_CALL":        3,
		"MESSAGE_TYPE_APP":         4,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{0}
}

// target type
// see moego-server-api/src/main/java/com/moego/server/message/enums/MessageTargetTypeEnums.java
type TargetType int32

const (
	// unspecified
	TargetType_TARGET_TYPE_UNSPECIFIED TargetType = 0
	// thread id
	TargetType_TARGET_TYPE_THREAD TargetType = 1
	// batch id
	TargetType_TARGET_TYPE_BATCH TargetType = 2
	// pick up
	TargetType_TARGET_TYPE_AUTO_PICKUP TargetType = 3
	// auto agreement
	TargetType_TARGET_TYPE_AUTO_AGREEMENT TargetType = 4
	// agreement
	TargetType_TARGET_TYPE_AGREEMENT TargetType = 5
	// review
	TargetType_TARGET_TYPE_REVIEW TargetType = 6
	// review booster reply
	TargetType_TARGET_TYPE_REVIEW_REPLY TargetType = 7
	// review booster appointment
	TargetType_TARGET_TYPE_REMINDER_APPOINTMENT_FIRST TargetType = 8
	// review booster appointment
	TargetType_TARGET_TYPE_REMINDER_APPOINTMENT_SECOND TargetType = 9
	// review booster appointment
	TargetType_TARGET_TYPE_REMINDER_APPOINTMENT_REMIND TargetType = 10
	// review booster birthday
	TargetType_TARGET_TYPE_REMINDER_PET_BIRTHDAY TargetType = 11
	// review booster rebook
	TargetType_TARGET_TYPE_REMINDER_REBOOK TargetType = 12
	// auto appointment book
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_BOOK TargetType = 13
	// auto appointment rebook
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_RESCHEDULED TargetType = 14
	// auto appointment rebook
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED TargetType = 15
	// forget password
	TargetType_TARGET_TYPE_FORGET_PASSWORD TargetType = 16
	// business daily
	TargetType_TARGET_TYPE_BUSINESS_DAILY TargetType = 17
	// verification code
	TargetType_TARGET_TYPE_VERIFICATION_CODE TargetType = 18
	// ob send to business email
	TargetType_TARGET_TYPE_OB_BUSINESS_EMAIL TargetType = 19
	// ob send to client email
	TargetType_TARGET_TYPE_OB_CLIENT_EMAIL TargetType = 20
	// ob send to client message
	TargetType_TARGET_TYPE_OB_CLIENT_MESSAGE TargetType = 21
	// mobile calendar push reminder
	TargetType_TARGET_TYPE_CALENDAR_REMINDER TargetType = 22
	// auto appointment confirmed by client
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT TargetType = 23
	// auto appointment cancelled by client
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED_BY_CLIENT TargetType = 24
	// auto receipt
	TargetType_TARGET_TYPE_AUTO_RECEIPT TargetType = 25
	// pay online
	TargetType_TARGET_TYPE_PAY_ONLINE TargetType = 26
	// cof link
	TargetType_TARGET_TYPE_COF_LINK TargetType = 27
	// auto appointment moved to wait list
	TargetType_TARGET_TYPE_AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST TargetType = 28
	// grooming report
	TargetType_TARGET_TYPE_GROOMING_REPORT TargetType = 29
	// abandoned schedule message
	TargetType_TARGET_TYPE_ABANDONED_SCHEDULE_MESSAGE TargetType = 30
	// daily report
	TargetType_TARGET_TYPE_DAILY_REPORT TargetType = 31
)

// Enum value maps for TargetType.
var (
	TargetType_name = map[int32]string{
		0:  "TARGET_TYPE_UNSPECIFIED",
		1:  "TARGET_TYPE_THREAD",
		2:  "TARGET_TYPE_BATCH",
		3:  "TARGET_TYPE_AUTO_PICKUP",
		4:  "TARGET_TYPE_AUTO_AGREEMENT",
		5:  "TARGET_TYPE_AGREEMENT",
		6:  "TARGET_TYPE_REVIEW",
		7:  "TARGET_TYPE_REVIEW_REPLY",
		8:  "TARGET_TYPE_REMINDER_APPOINTMENT_FIRST",
		9:  "TARGET_TYPE_REMINDER_APPOINTMENT_SECOND",
		10: "TARGET_TYPE_REMINDER_APPOINTMENT_REMIND",
		11: "TARGET_TYPE_REMINDER_PET_BIRTHDAY",
		12: "TARGET_TYPE_REMINDER_REBOOK",
		13: "TARGET_TYPE_AUTO_APPOINTMENT_BOOK",
		14: "TARGET_TYPE_AUTO_APPOINTMENT_RESCHEDULED",
		15: "TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED",
		16: "TARGET_TYPE_FORGET_PASSWORD",
		17: "TARGET_TYPE_BUSINESS_DAILY",
		18: "TARGET_TYPE_VERIFICATION_CODE",
		19: "TARGET_TYPE_OB_BUSINESS_EMAIL",
		20: "TARGET_TYPE_OB_CLIENT_EMAIL",
		21: "TARGET_TYPE_OB_CLIENT_MESSAGE",
		22: "TARGET_TYPE_CALENDAR_REMINDER",
		23: "TARGET_TYPE_AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT",
		24: "TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED_BY_CLIENT",
		25: "TARGET_TYPE_AUTO_RECEIPT",
		26: "TARGET_TYPE_PAY_ONLINE",
		27: "TARGET_TYPE_COF_LINK",
		28: "TARGET_TYPE_AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST",
		29: "TARGET_TYPE_GROOMING_REPORT",
		30: "TARGET_TYPE_ABANDONED_SCHEDULE_MESSAGE",
		31: "TARGET_TYPE_DAILY_REPORT",
	}
	TargetType_value = map[string]int32{
		"TARGET_TYPE_UNSPECIFIED":                          0,
		"TARGET_TYPE_THREAD":                               1,
		"TARGET_TYPE_BATCH":                                2,
		"TARGET_TYPE_AUTO_PICKUP":                          3,
		"TARGET_TYPE_AUTO_AGREEMENT":                       4,
		"TARGET_TYPE_AGREEMENT":                            5,
		"TARGET_TYPE_REVIEW":                               6,
		"TARGET_TYPE_REVIEW_REPLY":                         7,
		"TARGET_TYPE_REMINDER_APPOINTMENT_FIRST":           8,
		"TARGET_TYPE_REMINDER_APPOINTMENT_SECOND":          9,
		"TARGET_TYPE_REMINDER_APPOINTMENT_REMIND":          10,
		"TARGET_TYPE_REMINDER_PET_BIRTHDAY":                11,
		"TARGET_TYPE_REMINDER_REBOOK":                      12,
		"TARGET_TYPE_AUTO_APPOINTMENT_BOOK":                13,
		"TARGET_TYPE_AUTO_APPOINTMENT_RESCHEDULED":         14,
		"TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED":           15,
		"TARGET_TYPE_FORGET_PASSWORD":                      16,
		"TARGET_TYPE_BUSINESS_DAILY":                       17,
		"TARGET_TYPE_VERIFICATION_CODE":                    18,
		"TARGET_TYPE_OB_BUSINESS_EMAIL":                    19,
		"TARGET_TYPE_OB_CLIENT_EMAIL":                      20,
		"TARGET_TYPE_OB_CLIENT_MESSAGE":                    21,
		"TARGET_TYPE_CALENDAR_REMINDER":                    22,
		"TARGET_TYPE_AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT": 23,
		"TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED_BY_CLIENT": 24,
		"TARGET_TYPE_AUTO_RECEIPT":                         25,
		"TARGET_TYPE_PAY_ONLINE":                           26,
		"TARGET_TYPE_COF_LINK":                             27,
		"TARGET_TYPE_AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST":  28,
		"TARGET_TYPE_GROOMING_REPORT":                      29,
		"TARGET_TYPE_ABANDONED_SCHEDULE_MESSAGE":           30,
		"TARGET_TYPE_DAILY_REPORT":                         31,
	}
)

func (x TargetType) Enum() *TargetType {
	p := new(TargetType)
	*p = x
	return p
}

func (x TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[1].Descriptor()
}

func (TargetType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[1]
}

func (x TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetType.Descriptor instead.
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{1}
}

// sender type
type SenderType int32

const (
	// unspecified
	SenderType_SENDER_TYPE_UNSPECIFIED SenderType = 0
	// by business
	SenderType_SENDER_TYPE_BUSINESS SenderType = 1
	// bt customer
	SenderType_SENDER_TYPE_CUSTOMER SenderType = 2
)

// Enum value maps for SenderType.
var (
	SenderType_name = map[int32]string{
		0: "SENDER_TYPE_UNSPECIFIED",
		1: "SENDER_TYPE_BUSINESS",
		2: "SENDER_TYPE_CUSTOMER",
	}
	SenderType_value = map[string]int32{
		"SENDER_TYPE_UNSPECIFIED": 0,
		"SENDER_TYPE_BUSINESS":    1,
		"SENDER_TYPE_CUSTOMER":    2,
	}
)

func (x SenderType) Enum() *SenderType {
	p := new(SenderType)
	*p = x
	return p
}

func (x SenderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SenderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[2].Descriptor()
}

func (SenderType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[2]
}

func (x SenderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SenderType.Descriptor instead.
func (SenderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{2}
}

// business settings auto message type
type AutoMessageType int32

const (
	// unspecified
	AutoMessageType_AUTO_MESSAGE_TYPE_UNSPECIFIED AutoMessageType = 0
	// When new appt is booked
	AutoMessageType_APPT_BOOKED AutoMessageType = 1
	// When appt is rescheduled
	AutoMessageType_APPT_RESCHEDULED AutoMessageType = 2
	// When appt is cancelled
	AutoMessageType_APPT_CANCELLED AutoMessageType = 3
	// Ready for pick up
	AutoMessageType_READY_FOR_PICK_UP AutoMessageType = 4
	// Send ETA
	AutoMessageType_SEND_ETA AutoMessageType = 5
	// When appt is confirmed by client
	AutoMessageType_APPT_CONFIRMED_BY_CLIENT AutoMessageType = 6
	// When appt is cancelled by client
	AutoMessageType_APPT_CANCELLED_BY_CLIENT AutoMessageType = 7
	// Send receipt when a ticket is fully paid
	AutoMessageType_SEND_FULLY_PAID_RECEIPT AutoMessageType = 8
	// Send invoice to pay online
	AutoMessageType_PAY_ONLINE AutoMessageType = 9
	// When appt moved to waiting list
	AutoMessageType_APPT_MOVED_TO_WAITLIST AutoMessageType = 10
)

// Enum value maps for AutoMessageType.
var (
	AutoMessageType_name = map[int32]string{
		0:  "AUTO_MESSAGE_TYPE_UNSPECIFIED",
		1:  "APPT_BOOKED",
		2:  "APPT_RESCHEDULED",
		3:  "APPT_CANCELLED",
		4:  "READY_FOR_PICK_UP",
		5:  "SEND_ETA",
		6:  "APPT_CONFIRMED_BY_CLIENT",
		7:  "APPT_CANCELLED_BY_CLIENT",
		8:  "SEND_FULLY_PAID_RECEIPT",
		9:  "PAY_ONLINE",
		10: "APPT_MOVED_TO_WAITLIST",
	}
	AutoMessageType_value = map[string]int32{
		"AUTO_MESSAGE_TYPE_UNSPECIFIED": 0,
		"APPT_BOOKED":                   1,
		"APPT_RESCHEDULED":              2,
		"APPT_CANCELLED":                3,
		"READY_FOR_PICK_UP":             4,
		"SEND_ETA":                      5,
		"APPT_CONFIRMED_BY_CLIENT":      6,
		"APPT_CANCELLED_BY_CLIENT":      7,
		"SEND_FULLY_PAID_RECEIPT":       8,
		"PAY_ONLINE":                    9,
		"APPT_MOVED_TO_WAITLIST":        10,
	}
)

func (x AutoMessageType) Enum() *AutoMessageType {
	p := new(AutoMessageType)
	*p = x
	return p
}

func (x AutoMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[3].Descriptor()
}

func (AutoMessageType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[3]
}

func (x AutoMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoMessageType.Descriptor instead.
func (AutoMessageType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{3}
}

// schedule message status
type ScheduleMessageStatus int32

const (
	// unspecified
	ScheduleMessageStatus_SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED ScheduleMessageStatus = 0
	// scheduled
	ScheduleMessageStatus_SCHEDULED ScheduleMessageStatus = 1
	// sent successfully
	ScheduleMessageStatus_SENT_SUCCESSFULLY ScheduleMessageStatus = 2
	// sent failed
	ScheduleMessageStatus_SENT_FAILED ScheduleMessageStatus = 3
	// deleted
	ScheduleMessageStatus_DELETED ScheduleMessageStatus = 4
	// prepared
	ScheduleMessageStatus_PREPARED ScheduleMessageStatus = 5
)

// Enum value maps for ScheduleMessageStatus.
var (
	ScheduleMessageStatus_name = map[int32]string{
		0: "SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED",
		1: "SCHEDULED",
		2: "SENT_SUCCESSFULLY",
		3: "SENT_FAILED",
		4: "DELETED",
		5: "PREPARED",
	}
	ScheduleMessageStatus_value = map[string]int32{
		"SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED": 0,
		"SCHEDULED":         1,
		"SENT_SUCCESSFULLY": 2,
		"SENT_FAILED":       3,
		"DELETED":           4,
		"PREPARED":          5,
	}
)

func (x ScheduleMessageStatus) Enum() *ScheduleMessageStatus {
	p := new(ScheduleMessageStatus)
	*p = x
	return p
}

func (x ScheduleMessageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScheduleMessageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[4].Descriptor()
}

func (ScheduleMessageStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[4]
}

func (x ScheduleMessageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScheduleMessageStatus.Descriptor instead.
func (ScheduleMessageStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{4}
}

// the message send out method
// column of mysql.moe_message.moe_business_message_detail
type Method int32

const (
	// unspecified
	Method_METHOD_UNSPECIFIED Method = 0
	// sms
	Method_METHOD_SMS Method = 1
	// email
	Method_METHOD_EMAIL Method = 2
	// call
	Method_METHOD_CALL Method = 4
	// app
	Method_METHOD_APP Method = 5
)

// Enum value maps for Method.
var (
	Method_name = map[int32]string{
		0: "METHOD_UNSPECIFIED",
		1: "METHOD_SMS",
		2: "METHOD_EMAIL",
		4: "METHOD_CALL",
		5: "METHOD_APP",
	}
	Method_value = map[string]int32{
		"METHOD_UNSPECIFIED": 0,
		"METHOD_SMS":         1,
		"METHOD_EMAIL":       2,
		"METHOD_CALL":        4,
		"METHOD_APP":         5,
	}
)

func (x Method) Enum() *Method {
	p := new(Method)
	*p = x
	return p
}

func (x Method) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Method) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_message_enums_proto_enumTypes[5].Descriptor()
}

func (Method) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_message_enums_proto_enumTypes[5]
}

func (x Method) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Method.Descriptor instead.
func (Method) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{5}
}

// message type list
type MessageTypeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message type list
	MessageTypes []MessageType `protobuf:"varint,1,rep,packed,name=message_types,json=messageTypes,proto3,enum=moego.models.message.v1.MessageType" json:"message_types,omitempty"`
}

func (x *MessageTypeList) Reset() {
	*x = MessageTypeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_message_enums_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageTypeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageTypeList) ProtoMessage() {}

func (x *MessageTypeList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_message_enums_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageTypeList.ProtoReflect.Descriptor instead.
func (*MessageTypeList) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_message_enums_proto_rawDescGZIP(), []int{0}
}

func (x *MessageTypeList) GetMessageTypes() []MessageType {
	if x != nil {
		return x.MessageTypes
	}
	return nil
}

var File_moego_models_message_v1_message_enums_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_message_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x6f, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x5c, 0x0a, 0x0d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x2a, 0x86, 0x01, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x4d, 0x53, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x4c,
	0x4c, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x04, 0x2a, 0x83, 0x09, 0x0a, 0x0a, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x52, 0x47,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10,
	0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x04, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12,
	0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x59,
	0x10, 0x07, 0x12, 0x2a, 0x0a, 0x26, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x10, 0x08, 0x12, 0x2b,
	0x0a, 0x27, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x10, 0x09, 0x12, 0x2b, 0x0a, 0x27, 0x54,
	0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e,
	0x44, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x10, 0x0a, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x41, 0x52, 0x47,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x42, 0x49, 0x52, 0x54, 0x48, 0x44, 0x41, 0x59, 0x10, 0x0b, 0x12,
	0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x0c,
	0x12, 0x25, 0x0a, 0x21, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x0d, 0x12, 0x2c, 0x0a, 0x28, 0x54, 0x41, 0x52, 0x47, 0x45,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55,
	0x4c, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x2a, 0x0a, 0x26, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x0f, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x4f, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44,
	0x10, 0x10, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59,
	0x10, 0x11, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x12, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x52, 0x47,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x14, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x41, 0x52,
	0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x43, 0x4c, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x15, 0x12, 0x21, 0x0a, 0x1d,
	0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x4c, 0x45,
	0x4e, 0x44, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x16, 0x12,
	0x34, 0x0a, 0x30, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x10, 0x17, 0x12, 0x34, 0x0a, 0x30, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f,
	0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x18, 0x12, 0x1c, 0x0a, 0x18, 0x54,
	0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f,
	0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0x19, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x41, 0x52,
	0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4f, 0x4e, 0x4c,
	0x49, 0x4e, 0x45, 0x10, 0x1a, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x1b, 0x12,
	0x33, 0x0a, 0x2f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x4d, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x10, 0x1c, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x10, 0x1d, 0x12, 0x2a, 0x0a, 0x26, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x42, 0x41, 0x4e, 0x44, 0x4f, 0x4e, 0x45, 0x44, 0x5f, 0x53,
	0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10,
	0x1e, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x1f, 0x2a,
	0x5d, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45,
	0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45,
	0x53, 0x53, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x02, 0x2a, 0x99,
	0x02, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x42, 0x4f,
	0x4f, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e,
	0x41, 0x50, 0x50, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x49,
	0x43, 0x4b, 0x5f, 0x55, 0x50, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x45, 0x54, 0x41, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10,
	0x07, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f,
	0x50, 0x41, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0x08, 0x12, 0x0e,
	0x0a, 0x0a, 0x50, 0x41, 0x59, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x09, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f,
	0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x0a, 0x2a, 0x97, 0x01, 0x0a, 0x15, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x45, 0x50, 0x41, 0x52,
	0x45, 0x44, 0x10, 0x05, 0x2a, 0x69, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16,
	0x0a, 0x12, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x54,
	0x48, 0x4f, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x05, 0x22, 0x04, 0x08, 0x03, 0x10, 0x03, 0x42,
	0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f,
	0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_message_enums_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_message_enums_proto_rawDescData = file_moego_models_message_v1_message_enums_proto_rawDesc
)

func file_moego_models_message_v1_message_enums_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_message_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_message_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_message_enums_proto_rawDescData)
	})
	return file_moego_models_message_v1_message_enums_proto_rawDescData
}

var file_moego_models_message_v1_message_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_moego_models_message_v1_message_enums_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_message_v1_message_enums_proto_goTypes = []interface{}{
	(MessageType)(0),           // 0: moego.models.message.v1.MessageType
	(TargetType)(0),            // 1: moego.models.message.v1.TargetType
	(SenderType)(0),            // 2: moego.models.message.v1.SenderType
	(AutoMessageType)(0),       // 3: moego.models.message.v1.AutoMessageType
	(ScheduleMessageStatus)(0), // 4: moego.models.message.v1.ScheduleMessageStatus
	(Method)(0),                // 5: moego.models.message.v1.Method
	(*MessageTypeList)(nil),    // 6: moego.models.message.v1.MessageTypeList
}
var file_moego_models_message_v1_message_enums_proto_depIdxs = []int32{
	0, // 0: moego.models.message.v1.MessageTypeList.message_types:type_name -> moego.models.message.v1.MessageType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_message_enums_proto_init() }
func file_moego_models_message_v1_message_enums_proto_init() {
	if File_moego_models_message_v1_message_enums_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v1_message_enums_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageTypeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_message_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_message_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_message_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v1_message_enums_proto_enumTypes,
		MessageInfos:      file_moego_models_message_v1_message_enums_proto_msgTypes,
	}.Build()
	File_moego_models_message_v1_message_enums_proto = out.File
	file_moego_models_message_v1_message_enums_proto_rawDesc = nil
	file_moego_models_message_v1_message_enums_proto_goTypes = nil
	file_moego_models_message_v1_message_enums_proto_depIdxs = nil
}
