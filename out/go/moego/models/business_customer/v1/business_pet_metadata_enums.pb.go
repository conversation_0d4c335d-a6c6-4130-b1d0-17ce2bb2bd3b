// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_metadata_enums.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Business pet metadata names.
type BusinessPetMetadataName int32

const (
	// unspecified
	BusinessPetMetadataName_PET_METADATA_NAME_UNSPECIFIED BusinessPetMetadataName = 0
	// feeding schedule
	BusinessPetMetadataName_FEEDING_SCHEDULE BusinessPetMetadataName = 1
	// feeding unit
	BusinessPetMetadataName_FEEDING_UNIT BusinessPetMetadataName = 2
	// feeding type
	BusinessPetMetadataName_FEEDING_TYPE BusinessPetMetadataName = 3
	// feeding source
	BusinessPetMetadataName_FEEDING_SOURCE BusinessPetMetadataName = 4
	// feeding instruction
	BusinessPetMetadataName_FEEDING_INSTRUCTION BusinessPetMetadataName = 5
	// medication schedule
	BusinessPetMetadataName_MEDICATION_SCHEDULE BusinessPetMetadataName = 6
	// medication unit
	BusinessPetMetadataName_MEDICATION_UNIT BusinessPetMetadataName = 7
)

// Enum value maps for BusinessPetMetadataName.
var (
	BusinessPetMetadataName_name = map[int32]string{
		0: "PET_METADATA_NAME_UNSPECIFIED",
		1: "FEEDING_SCHEDULE",
		2: "FEEDING_UNIT",
		3: "FEEDING_TYPE",
		4: "FEEDING_SOURCE",
		5: "FEEDING_INSTRUCTION",
		6: "MEDICATION_SCHEDULE",
		7: "MEDICATION_UNIT",
	}
	BusinessPetMetadataName_value = map[string]int32{
		"PET_METADATA_NAME_UNSPECIFIED": 0,
		"FEEDING_SCHEDULE":              1,
		"FEEDING_UNIT":                  2,
		"FEEDING_TYPE":                  3,
		"FEEDING_SOURCE":                4,
		"FEEDING_INSTRUCTION":           5,
		"MEDICATION_SCHEDULE":           6,
		"MEDICATION_UNIT":               7,
	}
)

func (x BusinessPetMetadataName) Enum() *BusinessPetMetadataName {
	p := new(BusinessPetMetadataName)
	*p = x
	return p
}

func (x BusinessPetMetadataName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessPetMetadataName) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_enumTypes[0].Descriptor()
}

func (BusinessPetMetadataName) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_enumTypes[0]
}

func (x BusinessPetMetadataName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessPetMetadataName.Descriptor instead.
func (BusinessPetMetadataName) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_business_customer_v1_business_pet_metadata_enums_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2a, 0xd1, 0x01, 0x0a, 0x17, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x41,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x45, 0x45, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x03, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x17,
	0x0a, 0x13, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x45, 0x44, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x10, 0x07, 0x42, 0x98, 0x01, 0x0a,
	0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_goTypes = []interface{}{
	(BusinessPetMetadataName)(0), // 0: moego.models.business_customer.v1.BusinessPetMetadataName
}
var file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_init() }
func file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_metadata_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_enumTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_metadata_enums_proto = out.File
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_metadata_enums_proto_depIdxs = nil
}
