// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business/v1/business_enums.proto

package businesspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// business app type
type BusinessAppType int32

const (
	// mobile grooming
	BusinessAppType_BUSINESS_APP_TYPE_MOBILE BusinessAppType = 0
	// grooming salon
	BusinessAppType_BUSINESS_APP_TYPE_SALON BusinessAppType = 1
	// hybrid
	BusinessAppType_BUSINESS_APP_TYPE_HYBRID BusinessAppType = 2
)

// Enum value maps for BusinessAppType.
var (
	BusinessAppType_name = map[int32]string{
		0: "BUSINESS_APP_TYPE_MOBILE",
		1: "BUSINESS_APP_TYPE_SALON",
		2: "BUSINESS_APP_TYPE_HYBRID",
	}
	BusinessAppType_value = map[string]int32{
		"BUSINESS_APP_TYPE_MOBILE": 0,
		"BUSINESS_APP_TYPE_SALON":  1,
		"BUSINESS_APP_TYPE_HYBRID": 2,
	}
)

func (x BusinessAppType) Enum() *BusinessAppType {
	p := new(BusinessAppType)
	*p = x
	return p
}

func (x BusinessAppType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessAppType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_v1_business_enums_proto_enumTypes[0].Descriptor()
}

func (BusinessAppType) Type() protoreflect.EnumType {
	return &file_moego_models_business_v1_business_enums_proto_enumTypes[0]
}

func (x BusinessAppType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessAppType.Descriptor instead.
func (BusinessAppType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_enums_proto_rawDescGZIP(), []int{0}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// business pay type
type BusinessPayType int32

const (
	// none
	BusinessPayType_BUSINESS_PAY_TYPE_NONE BusinessPayType = 0
	// stripe
	BusinessPayType_BUSINESS_PAY_TYPE_STRIPE BusinessPayType = 1
	// square
	BusinessPayType_BUSINESS_PAY_TYPE_SQUARE BusinessPayType = 2
)

// Enum value maps for BusinessPayType.
var (
	BusinessPayType_name = map[int32]string{
		0: "BUSINESS_PAY_TYPE_NONE",
		1: "BUSINESS_PAY_TYPE_STRIPE",
		2: "BUSINESS_PAY_TYPE_SQUARE",
	}
	BusinessPayType_value = map[string]int32{
		"BUSINESS_PAY_TYPE_NONE":   0,
		"BUSINESS_PAY_TYPE_STRIPE": 1,
		"BUSINESS_PAY_TYPE_SQUARE": 2,
	}
)

func (x BusinessPayType) Enum() *BusinessPayType {
	p := new(BusinessPayType)
	*p = x
	return p
}

func (x BusinessPayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessPayType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_v1_business_enums_proto_enumTypes[1].Descriptor()
}

func (BusinessPayType) Type() protoreflect.EnumType {
	return &file_moego_models_business_v1_business_enums_proto_enumTypes[1]
}

func (x BusinessPayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessPayType.Descriptor instead.
func (BusinessPayType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_enums_proto_rawDescGZIP(), []int{1}
}

// BusinessSource
type BusinessSource int32

const (
	// unspecified
	BusinessSource_BUSINESS_SOURCE_UNSPECIFIED BusinessSource = 0
	// app android
	BusinessSource_APP_ANDROID BusinessSource = 1
	// app ios
	BusinessSource_APP_IOS BusinessSource = 2
	// web android
	BusinessSource_WEB_ANDROID BusinessSource = 3
	// web ios
	BusinessSource_WEB_IOS BusinessSource = 4
	// web desktop
	BusinessSource_WEB_DESKTOP BusinessSource = 5
)

// Enum value maps for BusinessSource.
var (
	BusinessSource_name = map[int32]string{
		0: "BUSINESS_SOURCE_UNSPECIFIED",
		1: "APP_ANDROID",
		2: "APP_IOS",
		3: "WEB_ANDROID",
		4: "WEB_IOS",
		5: "WEB_DESKTOP",
	}
	BusinessSource_value = map[string]int32{
		"BUSINESS_SOURCE_UNSPECIFIED": 0,
		"APP_ANDROID":                 1,
		"APP_IOS":                     2,
		"WEB_ANDROID":                 3,
		"WEB_IOS":                     4,
		"WEB_DESKTOP":                 5,
	}
)

func (x BusinessSource) Enum() *BusinessSource {
	p := new(BusinessSource)
	*p = x
	return p
}

func (x BusinessSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_v1_business_enums_proto_enumTypes[2].Descriptor()
}

func (BusinessSource) Type() protoreflect.EnumType {
	return &file_moego_models_business_v1_business_enums_proto_enumTypes[2]
}

func (x BusinessSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessSource.Descriptor instead.
func (BusinessSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_enums_proto_rawDescGZIP(), []int{2}
}

// BusinessMoveFrom
type BusinessMoveFrom int32

const (
	// unspecified
	BusinessMoveFrom_BUSINESS_MOVE_FROM_UNSPECIFIED BusinessMoveFrom = 0
	// just start
	BusinessMoveFrom_JUST_START BusinessMoveFrom = 1
	// move from paper
	BusinessMoveFrom_MOVE_FROM_PAPER BusinessMoveFrom = 2
	// from other software
	BusinessMoveFrom_FROM_OTHER_SOFTWARE BusinessMoveFrom = 3
)

// Enum value maps for BusinessMoveFrom.
var (
	BusinessMoveFrom_name = map[int32]string{
		0: "BUSINESS_MOVE_FROM_UNSPECIFIED",
		1: "JUST_START",
		2: "MOVE_FROM_PAPER",
		3: "FROM_OTHER_SOFTWARE",
	}
	BusinessMoveFrom_value = map[string]int32{
		"BUSINESS_MOVE_FROM_UNSPECIFIED": 0,
		"JUST_START":                     1,
		"MOVE_FROM_PAPER":                2,
		"FROM_OTHER_SOFTWARE":            3,
	}
)

func (x BusinessMoveFrom) Enum() *BusinessMoveFrom {
	p := new(BusinessMoveFrom)
	*p = x
	return p
}

func (x BusinessMoveFrom) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessMoveFrom) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_v1_business_enums_proto_enumTypes[3].Descriptor()
}

func (BusinessMoveFrom) Type() protoreflect.EnumType {
	return &file_moego_models_business_v1_business_enums_proto_enumTypes[3]
}

func (x BusinessMoveFrom) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessMoveFrom.Descriptor instead.
func (BusinessMoveFrom) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_v1_business_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_business_v1_business_enums_proto protoreflect.FileDescriptor

var file_moego_models_business_v1_business_enums_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0x6a, 0x0a, 0x0f, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x41, 0x4c, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x59, 0x42,
	0x52, 0x49, 0x44, 0x10, 0x02, 0x2a, 0x69, 0x0a, 0x0f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x42, 0x55, 0x53, 0x49,
	0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x50,
	0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x10, 0x02,
	0x2a, 0x7e, 0x0a, 0x0e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4f, 0x53, 0x10,
	0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x45, 0x42, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x45, 0x42, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x04, 0x12,
	0x0f, 0x0a, 0x0b, 0x57, 0x45, 0x42, 0x5f, 0x44, 0x45, 0x53, 0x4b, 0x54, 0x4f, 0x50, 0x10, 0x05,
	0x2a, 0x74, 0x0a, 0x10, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x76, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4a, 0x55, 0x53, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4f, 0x56, 0x45,
	0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x10, 0x02, 0x12, 0x17, 0x0a,
	0x13, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x46, 0x54,
	0x57, 0x41, 0x52, 0x45, 0x10, 0x03, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_v1_business_enums_proto_rawDescOnce sync.Once
	file_moego_models_business_v1_business_enums_proto_rawDescData = file_moego_models_business_v1_business_enums_proto_rawDesc
)

func file_moego_models_business_v1_business_enums_proto_rawDescGZIP() []byte {
	file_moego_models_business_v1_business_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_business_v1_business_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_v1_business_enums_proto_rawDescData)
	})
	return file_moego_models_business_v1_business_enums_proto_rawDescData
}

var file_moego_models_business_v1_business_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_business_v1_business_enums_proto_goTypes = []interface{}{
	(BusinessAppType)(0),  // 0: moego.models.business.v1.BusinessAppType
	(BusinessPayType)(0),  // 1: moego.models.business.v1.BusinessPayType
	(BusinessSource)(0),   // 2: moego.models.business.v1.BusinessSource
	(BusinessMoveFrom)(0), // 3: moego.models.business.v1.BusinessMoveFrom
}
var file_moego_models_business_v1_business_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_v1_business_enums_proto_init() }
func file_moego_models_business_v1_business_enums_proto_init() {
	if File_moego_models_business_v1_business_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_v1_business_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_v1_business_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_business_v1_business_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_business_v1_business_enums_proto_enumTypes,
	}.Build()
	File_moego_models_business_v1_business_enums_proto = out.File
	file_moego_models_business_v1_business_enums_proto_rawDesc = nil
	file_moego_models_business_v1_business_enums_proto_goTypes = nil
	file_moego_models_business_v1_business_enums_proto_depIdxs = nil
}
