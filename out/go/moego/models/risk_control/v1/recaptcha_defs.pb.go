// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/risk_control/v1/recaptcha_defs.proto

package riskcontrolpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// https://developers.google.com/recaptcha/docs/v3
// Google recaptcha definition
type RecaptchaDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recaptcha site key
	SiteKey string `protobuf:"bytes,1,opt,name=site_key,json=siteKey,proto3" json:"site_key,omitempty"`
	// recaptcha token
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// recaptcha version
	Version RecaptchaVersion `protobuf:"varint,3,opt,name=version,proto3,enum=moego.models.risk_control.v1.RecaptchaVersion" json:"version,omitempty"`
	// recaptcha action
	Action RecaptchaAction `protobuf:"varint,4,opt,name=action,proto3,enum=moego.models.risk_control.v1.RecaptchaAction" json:"action,omitempty"`
}

func (x *RecaptchaDef) Reset() {
	*x = RecaptchaDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_risk_control_v1_recaptcha_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaDef) ProtoMessage() {}

func (x *RecaptchaDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_risk_control_v1_recaptcha_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaDef.ProtoReflect.Descriptor instead.
func (*RecaptchaDef) Descriptor() ([]byte, []int) {
	return file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescGZIP(), []int{0}
}

func (x *RecaptchaDef) GetSiteKey() string {
	if x != nil {
		return x.SiteKey
	}
	return ""
}

func (x *RecaptchaDef) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RecaptchaDef) GetVersion() RecaptchaVersion {
	if x != nil {
		return x.Version
	}
	return RecaptchaVersion_RECAPTCHA_VERSION_UNSPECIFIED
}

func (x *RecaptchaDef) GetAction() RecaptchaAction {
	if x != nil {
		return x.Action
	}
	return RecaptchaAction_RECAPTCHA_ACTION_UNSPECIFIED
}

var File_moego_models_risk_control_v1_recaptcha_defs_proto protoreflect.FileDescriptor

var file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76,
	0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd0, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x44, 0x65, 0x66, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x48, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x45, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x69, 0x73, 0x6b, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescOnce sync.Once
	file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescData = file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDesc
)

func file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescGZIP() []byte {
	file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescData)
	})
	return file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDescData
}

var file_moego_models_risk_control_v1_recaptcha_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_risk_control_v1_recaptcha_defs_proto_goTypes = []interface{}{
	(*RecaptchaDef)(nil),  // 0: moego.models.risk_control.v1.RecaptchaDef
	(RecaptchaVersion)(0), // 1: moego.models.risk_control.v1.RecaptchaVersion
	(RecaptchaAction)(0),  // 2: moego.models.risk_control.v1.RecaptchaAction
}
var file_moego_models_risk_control_v1_recaptcha_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.risk_control.v1.RecaptchaDef.version:type_name -> moego.models.risk_control.v1.RecaptchaVersion
	2, // 1: moego.models.risk_control.v1.RecaptchaDef.action:type_name -> moego.models.risk_control.v1.RecaptchaAction
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_risk_control_v1_recaptcha_defs_proto_init() }
func file_moego_models_risk_control_v1_recaptcha_defs_proto_init() {
	if File_moego_models_risk_control_v1_recaptcha_defs_proto != nil {
		return
	}
	file_moego_models_risk_control_v1_recaptcha_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_risk_control_v1_recaptcha_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_risk_control_v1_recaptcha_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_risk_control_v1_recaptcha_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_risk_control_v1_recaptcha_defs_proto_msgTypes,
	}.Build()
	File_moego_models_risk_control_v1_recaptcha_defs_proto = out.File
	file_moego_models_risk_control_v1_recaptcha_defs_proto_rawDesc = nil
	file_moego_models_risk_control_v1_recaptcha_defs_proto_goTypes = nil
	file_moego_models_risk_control_v1_recaptcha_defs_proto_depIdxs = nil
}
