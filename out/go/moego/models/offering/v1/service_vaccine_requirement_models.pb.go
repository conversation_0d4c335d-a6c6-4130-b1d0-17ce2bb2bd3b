// @since 2024-09-19 21:27:56
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/service_vaccine_requirement_models.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The ServiceVaccineRequirement model
type ServiceVaccineRequirementModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id of this record
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
}

func (x *ServiceVaccineRequirementModel) Reset() {
	*x = ServiceVaccineRequirementModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_vaccine_requirement_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceVaccineRequirementModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceVaccineRequirementModel) ProtoMessage() {}

func (x *ServiceVaccineRequirementModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_vaccine_requirement_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceVaccineRequirementModel.ProtoReflect.Descriptor instead.
func (*ServiceVaccineRequirementModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceVaccineRequirementModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceVaccineRequirementModel) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceVaccineRequirementModel) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

var File_moego_models_offering_v1_service_vaccine_requirement_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa6, 0x01, 0x0a, 0x1e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x55, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescData = file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDesc
)

func file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDescData
}

var file_moego_models_offering_v1_service_vaccine_requirement_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_offering_v1_service_vaccine_requirement_models_proto_goTypes = []interface{}{
	(*ServiceVaccineRequirementModel)(nil), // 0: moego.models.offering.v1.ServiceVaccineRequirementModel
	(ServiceItemType)(0),                   // 1: moego.models.offering.v1.ServiceItemType
}
var file_moego_models_offering_v1_service_vaccine_requirement_models_proto_depIdxs = []int32{
	1, // 0: moego.models.offering.v1.ServiceVaccineRequirementModel.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_service_vaccine_requirement_models_proto_init() }
func file_moego_models_offering_v1_service_vaccine_requirement_models_proto_init() {
	if File_moego_models_offering_v1_service_vaccine_requirement_models_proto != nil {
		return
	}
	file_moego_models_offering_v1_service_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_service_vaccine_requirement_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceVaccineRequirementModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_service_vaccine_requirement_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_service_vaccine_requirement_models_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_service_vaccine_requirement_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_service_vaccine_requirement_models_proto = out.File
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_rawDesc = nil
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_goTypes = nil
	file_moego_models_offering_v1_service_vaccine_requirement_models_proto_depIdxs = nil
}
