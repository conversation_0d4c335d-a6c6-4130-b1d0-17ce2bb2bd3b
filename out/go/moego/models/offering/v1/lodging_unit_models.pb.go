// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/lodging_unit_models.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// lodging unit list model
type LodgingUnitView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// id of the lodging unit
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging unit
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// lodging type of this lodging unit
	LodgingTypeId int64 `protobuf:"varint,4,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// camera id
	CameraId int64 `protobuf:"varint,5,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// Sort for the lodging unit
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *LodgingUnitView) Reset() {
	*x = LodgingUnitView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUnitView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnitView) ProtoMessage() {}

func (x *LodgingUnitView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnitView.ProtoReflect.Descriptor instead.
func (*LodgingUnitView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_unit_models_proto_rawDescGZIP(), []int{0}
}

func (x *LodgingUnitView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LodgingUnitView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnitView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnitView) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *LodgingUnitView) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *LodgingUnitView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// lodging unit list model
type LodgingUnitModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// id of the lodging unit
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging unit
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// lodging type of this lodging unit
	LodgingTypeId int64 `protobuf:"varint,5,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// camera id
	CameraId int64 `protobuf:"varint,6,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// Sort for the lodging unit
	Sort int32 `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *LodgingUnitModel) Reset() {
	*x = LodgingUnitModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUnitModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnitModel) ProtoMessage() {}

func (x *LodgingUnitModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnitModel.ProtoReflect.Descriptor instead.
func (*LodgingUnitModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_unit_models_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingUnitModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LodgingUnitModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LodgingUnitModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnitModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnitModel) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *LodgingUnitModel) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *LodgingUnitModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_moego_models_offering_v1_lodging_unit_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_lodging_unit_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x22, 0xaf,
	0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x22, 0xcf, 0x01, 0x0a, 0x10, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_lodging_unit_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_lodging_unit_models_proto_rawDescData = file_moego_models_offering_v1_lodging_unit_models_proto_rawDesc
)

func file_moego_models_offering_v1_lodging_unit_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_lodging_unit_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_lodging_unit_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_lodging_unit_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_lodging_unit_models_proto_rawDescData
}

var file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_offering_v1_lodging_unit_models_proto_goTypes = []interface{}{
	(*LodgingUnitView)(nil),  // 0: moego.models.offering.v1.LodgingUnitView
	(*LodgingUnitModel)(nil), // 1: moego.models.offering.v1.LodgingUnitModel
}
var file_moego_models_offering_v1_lodging_unit_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_lodging_unit_models_proto_init() }
func file_moego_models_offering_v1_lodging_unit_models_proto_init() {
	if File_moego_models_offering_v1_lodging_unit_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUnitView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUnitModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_lodging_unit_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_lodging_unit_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_lodging_unit_models_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_lodging_unit_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_lodging_unit_models_proto = out.File
	file_moego_models_offering_v1_lodging_unit_models_proto_rawDesc = nil
	file_moego_models_offering_v1_lodging_unit_models_proto_goTypes = nil
	file_moego_models_offering_v1_lodging_unit_models_proto_depIdxs = nil
}
