// @since 2-24-01-12
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/pay_ops/v1/verification_model.proto

package payopspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the Stripe account information.
type StripeAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business information about the account.
	BusinessProfile *StripeAccount_BusinessProfile `protobuf:"bytes,1,opt,name=business_profile,json=businessProfile,proto3" json:"business_profile,omitempty"`
	// The business type (company, government_entity, individual, non_profit).
	BusinessType string `protobuf:"bytes,2,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	// A hash containing the set of capabilities requested for this account and their states.
	Capabilities *StripeAccount_Capabilities `protobuf:"bytes,3,opt,name=capabilities,proto3" json:"capabilities,omitempty"`
	// Whether the account can create live charges.
	ChargesEnabled bool `protobuf:"varint,4,opt,name=charges_enabled,json=chargesEnabled,proto3" json:"charges_enabled,omitempty"`
	// Company information associated with the account.
	Company *StripeAccount_Company `protobuf:"bytes,5,opt,name=company,proto3" json:"company,omitempty"`
	// Controller information associated with the account.
	Controller *StripeAccount_Controller `protobuf:"bytes,6,opt,name=controller,proto3" json:"controller,omitempty"`
	// The account's country.
	Country string `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty"`
	// Time at which the account was connected. Measured in seconds since the Unix epoch.
	Created int64 `protobuf:"varint,8,opt,name=created,proto3" json:"created,omitempty"`
	// Three-letter ISO currency code representing the default currency for the account.
	DefaultCurrency string `protobuf:"bytes,9,opt,name=default_currency,json=defaultCurrency,proto3" json:"default_currency,omitempty"`
	// Always true for a deleted object.
	Deleted bool `protobuf:"varint,10,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// Whether account details have been submitted.
	DetailsSubmitted bool `protobuf:"varint,11,opt,name=details_submitted,json=detailsSubmitted,proto3" json:"details_submitted,omitempty"`
	// An email address associated with the account.
	Email string `protobuf:"bytes,12,opt,name=email,proto3" json:"email,omitempty"`
	// External accounts (bank accounts and debit cards) currently attached to this account.
	ExternalAccounts []*StripeAccount_ExternalAccount `protobuf:"bytes,13,rep,name=external_accounts,json=externalAccounts,proto3" json:"external_accounts,omitempty"`
	// Future requirements for the account.
	FutureRequirements *StripeAccount_FutureRequirements `protobuf:"bytes,14,opt,name=future_requirements,json=futureRequirements,proto3" json:"future_requirements,omitempty"`
	// Unique identifier for the object.
	Id string `protobuf:"bytes,15,opt,name=id,proto3" json:"id,omitempty"`
	// Information about a person associated with a Stripe account.
	Individual []*StripePerson `protobuf:"bytes,16,rep,name=individual,proto3" json:"individual,omitempty"`
	// Metadata associated with the account.
	Metadata map[string]string `protobuf:"bytes,17,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Object's type. Equal to "account".
	Object string `protobuf:"bytes,18,opt,name=object,proto3" json:"object,omitempty"`
	// Whether Stripe can send payouts to this account.
	PayoutsEnabled bool `protobuf:"varint,19,opt,name=payouts_enabled,json=payoutsEnabled,proto3" json:"payouts_enabled,omitempty"`
	// Requirements for the account.
	Requirements *StripeAccount_Requirements `protobuf:"bytes,20,opt,name=requirements,proto3" json:"requirements,omitempty"`
	// Settings for customizing how the account functions within Stripe.
	Settings *StripeAccount_Settings `protobuf:"bytes,21,opt,name=settings,proto3" json:"settings,omitempty"` // Define Settings message as needed.
	// Terms of Service acceptance information.
	TosAcceptance *StripeAccount_TosAcceptance `protobuf:"bytes,22,opt,name=tos_acceptance,json=tosAcceptance,proto3" json:"tos_acceptance,omitempty"` // Define TosAcceptance message as needed.
	// The Stripe account type (standard, express, custom).
	Type string `protobuf:"bytes,23,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *StripeAccount) Reset() {
	*x = StripeAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount) ProtoMessage() {}

func (x *StripeAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount.ProtoReflect.Descriptor instead.
func (*StripeAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0}
}

func (x *StripeAccount) GetBusinessProfile() *StripeAccount_BusinessProfile {
	if x != nil {
		return x.BusinessProfile
	}
	return nil
}

func (x *StripeAccount) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *StripeAccount) GetCapabilities() *StripeAccount_Capabilities {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

func (x *StripeAccount) GetChargesEnabled() bool {
	if x != nil {
		return x.ChargesEnabled
	}
	return false
}

func (x *StripeAccount) GetCompany() *StripeAccount_Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *StripeAccount) GetController() *StripeAccount_Controller {
	if x != nil {
		return x.Controller
	}
	return nil
}

func (x *StripeAccount) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripeAccount) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeAccount) GetDefaultCurrency() string {
	if x != nil {
		return x.DefaultCurrency
	}
	return ""
}

func (x *StripeAccount) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *StripeAccount) GetDetailsSubmitted() bool {
	if x != nil {
		return x.DetailsSubmitted
	}
	return false
}

func (x *StripeAccount) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *StripeAccount) GetExternalAccounts() []*StripeAccount_ExternalAccount {
	if x != nil {
		return x.ExternalAccounts
	}
	return nil
}

func (x *StripeAccount) GetFutureRequirements() *StripeAccount_FutureRequirements {
	if x != nil {
		return x.FutureRequirements
	}
	return nil
}

func (x *StripeAccount) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripeAccount) GetIndividual() []*StripePerson {
	if x != nil {
		return x.Individual
	}
	return nil
}

func (x *StripeAccount) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *StripeAccount) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *StripeAccount) GetPayoutsEnabled() bool {
	if x != nil {
		return x.PayoutsEnabled
	}
	return false
}

func (x *StripeAccount) GetRequirements() *StripeAccount_Requirements {
	if x != nil {
		return x.Requirements
	}
	return nil
}

func (x *StripeAccount) GetSettings() *StripeAccount_Settings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *StripeAccount) GetTosAcceptance() *StripeAccount_TosAcceptance {
	if x != nil {
		return x.TosAcceptance
	}
	return nil
}

func (x *StripeAccount) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// Represents the detailed address information for Stripe.
type StripeAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// City, district, suburb, town, or village.
	City string `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	// Two-letter country code.
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// Address line 1 (e.g., street, PO Box, or company name).
	Line1 string `protobuf:"bytes,3,opt,name=line1,proto3" json:"line1,omitempty"`
	// Address line 2 (e.g., apartment, suite, unit, or building).
	Line2 string `protobuf:"bytes,4,opt,name=line2,proto3" json:"line2,omitempty"`
	// ZIP or postal code.
	PostalCode string `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// State, county, province, or region.
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *StripeAddress) Reset() {
	*x = StripeAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAddress) ProtoMessage() {}

func (x *StripeAddress) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAddress.ProtoReflect.Descriptor instead.
func (*StripeAddress) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{1}
}

func (x *StripeAddress) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *StripeAddress) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripeAddress) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *StripeAddress) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

func (x *StripeAddress) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *StripeAddress) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

// Represents the detailed information of a Stripe person.
type StripePerson struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The account the person is associated with.
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// address
	Address *StripeAddress `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// The Kana variation of the person's address (Japan only).
	AddressKana *StripePerson_AddressKana `protobuf:"bytes,3,opt,name=address_kana,json=addressKana,proto3" json:"address_kana,omitempty"`
	// The Kanji variation of the person's address (Japan only).
	AddressKanji *StripePerson_AddressKanji `protobuf:"bytes,4,opt,name=address_kanji,json=addressKanji,proto3" json:"address_kanji,omitempty"`
	// Time at which the object was created. Measured in seconds since the Unix epoch.
	Created int64 `protobuf:"varint,5,opt,name=created,proto3" json:"created,omitempty"`
	// Always true for a deleted object.
	Deleted bool `protobuf:"varint,6,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// dob
	Dob *StripePerson_Dob `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	// The person's email address.
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	// The person's first name.
	FirstName string `protobuf:"bytes,9,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// The Kana variation of the person's first name (Japan only).
	FirstNameKana string `protobuf:"bytes,10,opt,name=first_name_kana,json=firstNameKana,proto3" json:"first_name_kana,omitempty"`
	// The Kanji variation of the person's first name (Japan only).
	FirstNameKanji string `protobuf:"bytes,11,opt,name=first_name_kanji,json=firstNameKanji,proto3" json:"first_name_kanji,omitempty"`
	// A list of alternate names or aliases that the person is known by.
	FullNameAliases []string `protobuf:"bytes,12,rep,name=full_name_aliases,json=fullNameAliases,proto3" json:"full_name_aliases,omitempty"`
	// Information about the upcoming new requirements for this person.
	FutureRequirements *StripePerson_FutureRequirements `protobuf:"bytes,13,opt,name=future_requirements,json=futureRequirements,proto3" json:"future_requirements,omitempty"`
	// The person's gender (must be "male" or "female").
	Gender string `protobuf:"bytes,14,opt,name=gender,proto3" json:"gender,omitempty"`
	// Unique identifier for the object.
	Id string `protobuf:"bytes,15,opt,name=id,proto3" json:"id,omitempty"`
	// Whether the person's ID number was provided.
	IdNumberProvided bool `protobuf:"varint,16,opt,name=id_number_provided,json=idNumberProvided,proto3" json:"id_number_provided,omitempty"`
	// Whether the person's secondary ID number was provided.
	IdNumberSecondaryProvided bool `protobuf:"varint,17,opt,name=id_number_secondary_provided,json=idNumberSecondaryProvided,proto3" json:"id_number_secondary_provided,omitempty"`
	// The person's last name.
	LastName string `protobuf:"bytes,18,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// The Kana variation of the person's last name (Japan only).
	LastNameKana string `protobuf:"bytes,19,opt,name=last_name_kana,json=lastNameKana,proto3" json:"last_name_kana,omitempty"`
	// The Kanji variation of the person's last name (Japan only).
	LastNameKanji string `protobuf:"bytes,20,opt,name=last_name_kanji,json=lastNameKanji,proto3" json:"last_name_kanji,omitempty"`
	// The person's maiden name.
	MaidenName string `protobuf:"bytes,21,opt,name=maiden_name,json=maidenName,proto3" json:"maiden_name,omitempty"`
	// Key-value pairs that you can attach to an object.
	Metadata map[string]string `protobuf:"bytes,22,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The country where the person is a national.
	Nationality string `protobuf:"bytes,23,opt,name=nationality,proto3" json:"nationality,omitempty"`
	// Object's type. Equal to "person".
	Object string `protobuf:"bytes,24,opt,name=object,proto3" json:"object,omitempty"`
	// The person's phone number.
	Phone string `protobuf:"bytes,25,opt,name=phone,proto3" json:"phone,omitempty"`
	// Indicates if the person holds an important public job or function.
	PoliticalExposure string `protobuf:"bytes,26,opt,name=political_exposure,json=politicalExposure,proto3" json:"political_exposure,omitempty"`
	// address of the person's registered office (U.K. only).
	RegisteredAddress *StripeAddress `protobuf:"bytes,27,opt,name=registered_address,json=registeredAddress,proto3" json:"registered_address,omitempty"`
	// relationship
	Relationship *StripePerson_Relationship `protobuf:"bytes,28,opt,name=relationship,proto3" json:"relationship,omitempty"`
	// Information about the requirements for this person.
	Requirements *StripePerson_Requirements `protobuf:"bytes,29,opt,name=requirements,proto3" json:"requirements,omitempty"`
	// Whether the last four digits of the person's SSN have been provided (U.S. only).
	SsnLast_4Provided bool `protobuf:"varint,30,opt,name=ssn_last_4_provided,json=ssnLast4Provided,proto3" json:"ssn_last_4_provided,omitempty"`
	// verification
	Verification *StripePerson_Verification `protobuf:"bytes,31,opt,name=verification,proto3" json:"verification,omitempty"`
}

func (x *StripePerson) Reset() {
	*x = StripePerson{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson) ProtoMessage() {}

func (x *StripePerson) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson.ProtoReflect.Descriptor instead.
func (*StripePerson) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2}
}

func (x *StripePerson) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *StripePerson) GetAddress() *StripeAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *StripePerson) GetAddressKana() *StripePerson_AddressKana {
	if x != nil {
		return x.AddressKana
	}
	return nil
}

func (x *StripePerson) GetAddressKanji() *StripePerson_AddressKanji {
	if x != nil {
		return x.AddressKanji
	}
	return nil
}

func (x *StripePerson) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripePerson) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *StripePerson) GetDob() *StripePerson_Dob {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *StripePerson) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *StripePerson) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *StripePerson) GetFirstNameKana() string {
	if x != nil {
		return x.FirstNameKana
	}
	return ""
}

func (x *StripePerson) GetFirstNameKanji() string {
	if x != nil {
		return x.FirstNameKanji
	}
	return ""
}

func (x *StripePerson) GetFullNameAliases() []string {
	if x != nil {
		return x.FullNameAliases
	}
	return nil
}

func (x *StripePerson) GetFutureRequirements() *StripePerson_FutureRequirements {
	if x != nil {
		return x.FutureRequirements
	}
	return nil
}

func (x *StripePerson) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *StripePerson) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripePerson) GetIdNumberProvided() bool {
	if x != nil {
		return x.IdNumberProvided
	}
	return false
}

func (x *StripePerson) GetIdNumberSecondaryProvided() bool {
	if x != nil {
		return x.IdNumberSecondaryProvided
	}
	return false
}

func (x *StripePerson) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *StripePerson) GetLastNameKana() string {
	if x != nil {
		return x.LastNameKana
	}
	return ""
}

func (x *StripePerson) GetLastNameKanji() string {
	if x != nil {
		return x.LastNameKanji
	}
	return ""
}

func (x *StripePerson) GetMaidenName() string {
	if x != nil {
		return x.MaidenName
	}
	return ""
}

func (x *StripePerson) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *StripePerson) GetNationality() string {
	if x != nil {
		return x.Nationality
	}
	return ""
}

func (x *StripePerson) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *StripePerson) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *StripePerson) GetPoliticalExposure() string {
	if x != nil {
		return x.PoliticalExposure
	}
	return ""
}

func (x *StripePerson) GetRegisteredAddress() *StripeAddress {
	if x != nil {
		return x.RegisteredAddress
	}
	return nil
}

func (x *StripePerson) GetRelationship() *StripePerson_Relationship {
	if x != nil {
		return x.Relationship
	}
	return nil
}

func (x *StripePerson) GetRequirements() *StripePerson_Requirements {
	if x != nil {
		return x.Requirements
	}
	return nil
}

func (x *StripePerson) GetSsnLast_4Provided() bool {
	if x != nil {
		return x.SsnLast_4Provided
	}
	return false
}

func (x *StripePerson) GetVerification() *StripePerson_Verification {
	if x != nil {
		return x.Verification
	}
	return nil
}

// external account
type StripeAccount_ExternalAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// map entry list
	Data map[string]string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// size
	Size int32 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *StripeAccount_ExternalAccount) Reset() {
	*x = StripeAccount_ExternalAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_ExternalAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_ExternalAccount) ProtoMessage() {}

func (x *StripeAccount_ExternalAccount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_ExternalAccount.ProtoReflect.Descriptor instead.
func (*StripeAccount_ExternalAccount) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 1}
}

func (x *StripeAccount_ExternalAccount) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *StripeAccount_ExternalAccount) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

// Represents the business profile information.
type StripeAccount_BusinessProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The merchant category code for the account.
	Mcc string `protobuf:"bytes,1,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// The customer-facing business name.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Internal-only description of the product sold or service provided by the business.
	ProductDescription string `protobuf:"bytes,3,opt,name=product_description,json=productDescription,proto3" json:"product_description,omitempty"`
	// A publicly available mailing address for sending support issues to.
	SupportAddress *StripeAddress `protobuf:"bytes,4,opt,name=support_address,json=supportAddress,proto3" json:"support_address,omitempty"`
	// A publicly available email address for sending support issues to.
	SupportEmail string `protobuf:"bytes,5,opt,name=support_email,json=supportEmail,proto3" json:"support_email,omitempty"`
	// A publicly available phone number to call with support issues.
	SupportPhone string `protobuf:"bytes,6,opt,name=support_phone,json=supportPhone,proto3" json:"support_phone,omitempty"`
	// A publicly available website for handling support issues.
	SupportUrl string `protobuf:"bytes,7,opt,name=support_url,json=supportUrl,proto3" json:"support_url,omitempty"`
	// The business's publicly available website.
	Url string `protobuf:"bytes,8,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *StripeAccount_BusinessProfile) Reset() {
	*x = StripeAccount_BusinessProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_BusinessProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_BusinessProfile) ProtoMessage() {}

func (x *StripeAccount_BusinessProfile) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_BusinessProfile.ProtoReflect.Descriptor instead.
func (*StripeAccount_BusinessProfile) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 2}
}

func (x *StripeAccount_BusinessProfile) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetSupportAddress() *StripeAddress {
	if x != nil {
		return x.SupportAddress
	}
	return nil
}

func (x *StripeAccount_BusinessProfile) GetSupportEmail() string {
	if x != nil {
		return x.SupportEmail
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetSupportPhone() string {
	if x != nil {
		return x.SupportPhone
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetSupportUrl() string {
	if x != nil {
		return x.SupportUrl
	}
	return ""
}

func (x *StripeAccount_BusinessProfile) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// Represents various payment capabilities of an account.
type StripeAccount_Capabilities struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of Canadian pre-authorized debits payments.
	AcssDebitPayments string `protobuf:"bytes,1,opt,name=acss_debit_payments,json=acssDebitPayments,proto3" json:"acss_debit_payments,omitempty"`
	// Status of Affirm payments.
	AffirmPayments string `protobuf:"bytes,2,opt,name=affirm_payments,json=affirmPayments,proto3" json:"affirm_payments,omitempty"`
	// Status of Afterpay Clearpay payments.
	AfterpayClearpayPayments string `protobuf:"bytes,3,opt,name=afterpay_clearpay_payments,json=afterpayClearpayPayments,proto3" json:"afterpay_clearpay_payments,omitempty"`
	// Status of BECS Direct Debit (AU) payments.
	AuBecsDebitPayments string `protobuf:"bytes,4,opt,name=au_becs_debit_payments,json=auBecsDebitPayments,proto3" json:"au_becs_debit_payments,omitempty"`
	// Status of Bacs Direct Debits payments.
	BacsDebitPayments string `protobuf:"bytes,5,opt,name=bacs_debit_payments,json=bacsDebitPayments,proto3" json:"bacs_debit_payments,omitempty"`
	// Status of Bancontact payments.
	BancontactPayments string `protobuf:"bytes,6,opt,name=bancontact_payments,json=bancontactPayments,proto3" json:"bancontact_payments,omitempty"`
	// Status of customer_balance payments.
	BankTransferPayments string `protobuf:"bytes,7,opt,name=bank_transfer_payments,json=bankTransferPayments,proto3" json:"bank_transfer_payments,omitempty"`
	// Status of blik payments.
	BlikPayments string `protobuf:"bytes,8,opt,name=blik_payments,json=blikPayments,proto3" json:"blik_payments,omitempty"`
	// Status of boleto payments.
	BoletoPayments string `protobuf:"bytes,9,opt,name=boleto_payments,json=boletoPayments,proto3" json:"boleto_payments,omitempty"`
	// Status of card issuing capability.
	CardIssuing string `protobuf:"bytes,10,opt,name=card_issuing,json=cardIssuing,proto3" json:"card_issuing,omitempty"`
	// Status of card payments.
	CardPayments string `protobuf:"bytes,11,opt,name=card_payments,json=cardPayments,proto3" json:"card_payments,omitempty"`
	// Status of Cartes Bancaires payments.
	CartesBancairesPayments string `protobuf:"bytes,12,opt,name=cartes_bancaires_payments,json=cartesBancairesPayments,proto3" json:"cartes_bancaires_payments,omitempty"`
	// Status of EPS payments.
	EpsPayments string `protobuf:"bytes,13,opt,name=eps_payments,json=epsPayments,proto3" json:"eps_payments,omitempty"`
	// Status of FPX payments.
	FpxPayments string `protobuf:"bytes,14,opt,name=fpx_payments,json=fpxPayments,proto3" json:"fpx_payments,omitempty"`
	// Status of giropay payments.
	GiropayPayments string `protobuf:"bytes,15,opt,name=giropay_payments,json=giropayPayments,proto3" json:"giropay_payments,omitempty"`
	// Status of GrabPay payments.
	GrabpayPayments string `protobuf:"bytes,16,opt,name=grabpay_payments,json=grabpayPayments,proto3" json:"grabpay_payments,omitempty"`
	// Status of iDEAL payments.
	IdealPayments string `protobuf:"bytes,17,opt,name=ideal_payments,json=idealPayments,proto3" json:"ideal_payments,omitempty"`
	// Status of JCB payments (Japan only).
	JcbPayments string `protobuf:"bytes,18,opt,name=jcb_payments,json=jcbPayments,proto3" json:"jcb_payments,omitempty"`
	// Status of Klarna payments.
	KlarnaPayments string `protobuf:"bytes,19,opt,name=klarna_payments,json=klarnaPayments,proto3" json:"klarna_payments,omitempty"`
	// Status of konbini payments.
	KonbiniPayments string `protobuf:"bytes,20,opt,name=konbini_payments,json=konbiniPayments,proto3" json:"konbini_payments,omitempty"`
	// Status of legacy payments.
	LegacyPayments string `protobuf:"bytes,21,opt,name=legacy_payments,json=legacyPayments,proto3" json:"legacy_payments,omitempty"`
	// Status of link_payments capability.
	LinkPayments string `protobuf:"bytes,22,opt,name=link_payments,json=linkPayments,proto3" json:"link_payments,omitempty"`
	// Status of OXXO payments.
	OxxoPayments string `protobuf:"bytes,23,opt,name=oxxo_payments,json=oxxoPayments,proto3" json:"oxxo_payments,omitempty"`
	// Status of P24 payments.
	P24Payments string `protobuf:"bytes,24,opt,name=p24_payments,json=p24Payments,proto3" json:"p24_payments,omitempty"`
	// Status of paynow payments.
	PaynowPayments string `protobuf:"bytes,25,opt,name=paynow_payments,json=paynowPayments,proto3" json:"paynow_payments,omitempty"`
	// Status of promptpay payments.
	PromptpayPayments string `protobuf:"bytes,26,opt,name=promptpay_payments,json=promptpayPayments,proto3" json:"promptpay_payments,omitempty"`
	// Status of SEPA Direct Debits payments.
	SepaDebitPayments string `protobuf:"bytes,27,opt,name=sepa_debit_payments,json=sepaDebitPayments,proto3" json:"sepa_debit_payments,omitempty"`
	// Status of Sofort payments.
	SofortPayments string `protobuf:"bytes,28,opt,name=sofort_payments,json=sofortPayments,proto3" json:"sofort_payments,omitempty"`
	// Status of tax reporting 1099-K (US).
	TaxReportingUs_1099K string `protobuf:"bytes,29,opt,name=tax_reporting_us_1099_k,json=taxReportingUs1099K,proto3" json:"tax_reporting_us_1099_k,omitempty"`
	// Status of tax reporting 1099-MISC (US).
	TaxReportingUs_1099Misc string `protobuf:"bytes,30,opt,name=tax_reporting_us_1099_misc,json=taxReportingUs1099Misc,proto3" json:"tax_reporting_us_1099_misc,omitempty"`
	// Status of transfers capability.
	Transfers string `protobuf:"bytes,31,opt,name=transfers,proto3" json:"transfers,omitempty"`
	// Status of banking capability.
	Treasury string `protobuf:"bytes,32,opt,name=treasury,proto3" json:"treasury,omitempty"`
	// Status of US bank account ACH payments.
	UsBankAccountAchPayments string `protobuf:"bytes,33,opt,name=us_bank_account_ach_payments,json=usBankAccountAchPayments,proto3" json:"us_bank_account_ach_payments,omitempty"`
}

func (x *StripeAccount_Capabilities) Reset() {
	*x = StripeAccount_Capabilities{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Capabilities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Capabilities) ProtoMessage() {}

func (x *StripeAccount_Capabilities) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Capabilities.ProtoReflect.Descriptor instead.
func (*StripeAccount_Capabilities) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 3}
}

func (x *StripeAccount_Capabilities) GetAcssDebitPayments() string {
	if x != nil {
		return x.AcssDebitPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetAffirmPayments() string {
	if x != nil {
		return x.AffirmPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetAfterpayClearpayPayments() string {
	if x != nil {
		return x.AfterpayClearpayPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetAuBecsDebitPayments() string {
	if x != nil {
		return x.AuBecsDebitPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetBacsDebitPayments() string {
	if x != nil {
		return x.BacsDebitPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetBancontactPayments() string {
	if x != nil {
		return x.BancontactPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetBankTransferPayments() string {
	if x != nil {
		return x.BankTransferPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetBlikPayments() string {
	if x != nil {
		return x.BlikPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetBoletoPayments() string {
	if x != nil {
		return x.BoletoPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetCardIssuing() string {
	if x != nil {
		return x.CardIssuing
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetCardPayments() string {
	if x != nil {
		return x.CardPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetCartesBancairesPayments() string {
	if x != nil {
		return x.CartesBancairesPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetEpsPayments() string {
	if x != nil {
		return x.EpsPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetFpxPayments() string {
	if x != nil {
		return x.FpxPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetGiropayPayments() string {
	if x != nil {
		return x.GiropayPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetGrabpayPayments() string {
	if x != nil {
		return x.GrabpayPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetIdealPayments() string {
	if x != nil {
		return x.IdealPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetJcbPayments() string {
	if x != nil {
		return x.JcbPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetKlarnaPayments() string {
	if x != nil {
		return x.KlarnaPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetKonbiniPayments() string {
	if x != nil {
		return x.KonbiniPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetLegacyPayments() string {
	if x != nil {
		return x.LegacyPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetLinkPayments() string {
	if x != nil {
		return x.LinkPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetOxxoPayments() string {
	if x != nil {
		return x.OxxoPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetP24Payments() string {
	if x != nil {
		return x.P24Payments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetPaynowPayments() string {
	if x != nil {
		return x.PaynowPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetPromptpayPayments() string {
	if x != nil {
		return x.PromptpayPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetSepaDebitPayments() string {
	if x != nil {
		return x.SepaDebitPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetSofortPayments() string {
	if x != nil {
		return x.SofortPayments
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetTaxReportingUs_1099K() string {
	if x != nil {
		return x.TaxReportingUs_1099K
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetTaxReportingUs_1099Misc() string {
	if x != nil {
		return x.TaxReportingUs_1099Misc
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetTransfers() string {
	if x != nil {
		return x.Transfers
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetTreasury() string {
	if x != nil {
		return x.Treasury
	}
	return ""
}

func (x *StripeAccount_Capabilities) GetUsBankAccountAchPayments() string {
	if x != nil {
		return x.UsBankAccountAchPayments
	}
	return ""
}

// The main company information.
type StripeAccount_Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Address of the company.
	Address *StripeAddress `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// The Kana variation of the company's primary address (Japan only).
	AddressKana *StripeAccount_AddressKana `protobuf:"bytes,2,opt,name=address_kana,json=addressKana,proto3" json:"address_kana,omitempty"`
	// The Kanji variation of the company's primary address (Japan only).
	AddressKanji *StripeAccount_AddressKanji `protobuf:"bytes,3,opt,name=address_kanji,json=addressKanji,proto3" json:"address_kanji,omitempty"`
	// Indicates if the company's directors have been provided.
	DirectorsProvided bool `protobuf:"varint,4,opt,name=directors_provided,json=directorsProvided,proto3" json:"directors_provided,omitempty"`
	// Indicates if the company's executives have been provided.
	ExecutivesProvided bool `protobuf:"varint,5,opt,name=executives_provided,json=executivesProvided,proto3" json:"executives_provided,omitempty"`
	// Legal name of the company.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// The Kana variation of the company's legal name (Japan only).
	NameKana string `protobuf:"bytes,7,opt,name=name_kana,json=nameKana,proto3" json:"name_kana,omitempty"`
	// The Kanji variation of the company's legal name (Japan only).
	NameKanji string `protobuf:"bytes,8,opt,name=name_kanji,json=nameKanji,proto3" json:"name_kanji,omitempty"`
	// Indicates if the company's owners have been provided.
	OwnersProvided bool `protobuf:"varint,9,opt,name=owners_provided,json=ownersProvided,proto3" json:"owners_provided,omitempty"`
	// Attestation regarding the beneficial owner information.
	OwnershipDeclaration *StripeAccount_OwnershipDeclaration `protobuf:"bytes,10,opt,name=ownership_declaration,json=ownershipDeclaration,proto3" json:"ownership_declaration,omitempty"`
	// Phone number of the company.
	Phone string `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`
	// Legal structure of the company.
	Structure string `protobuf:"bytes,12,opt,name=structure,proto3" json:"structure,omitempty"`
	// Indicates if the company's business ID number was provided.
	TaxIdProvided bool `protobuf:"varint,13,opt,name=tax_id_provided,json=taxIdProvided,proto3" json:"tax_id_provided,omitempty"`
	// Jurisdiction for the tax ID (Germany-based companies only).
	TaxIdRegistrar string `protobuf:"bytes,14,opt,name=tax_id_registrar,json=taxIdRegistrar,proto3" json:"tax_id_registrar,omitempty"`
	// Indicates if the company's VAT number was provided.
	VatIdProvided bool `protobuf:"varint,15,opt,name=vat_id_provided,json=vatIdProvided,proto3" json:"vat_id_provided,omitempty"`
	// Verification state of the company.
	Verification *StripeAccount_Verification `protobuf:"bytes,16,opt,name=verification,proto3" json:"verification,omitempty"`
}

func (x *StripeAccount_Company) Reset() {
	*x = StripeAccount_Company{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Company) ProtoMessage() {}

func (x *StripeAccount_Company) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Company.ProtoReflect.Descriptor instead.
func (*StripeAccount_Company) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 4}
}

func (x *StripeAccount_Company) GetAddress() *StripeAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *StripeAccount_Company) GetAddressKana() *StripeAccount_AddressKana {
	if x != nil {
		return x.AddressKana
	}
	return nil
}

func (x *StripeAccount_Company) GetAddressKanji() *StripeAccount_AddressKanji {
	if x != nil {
		return x.AddressKanji
	}
	return nil
}

func (x *StripeAccount_Company) GetDirectorsProvided() bool {
	if x != nil {
		return x.DirectorsProvided
	}
	return false
}

func (x *StripeAccount_Company) GetExecutivesProvided() bool {
	if x != nil {
		return x.ExecutivesProvided
	}
	return false
}

func (x *StripeAccount_Company) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StripeAccount_Company) GetNameKana() string {
	if x != nil {
		return x.NameKana
	}
	return ""
}

func (x *StripeAccount_Company) GetNameKanji() string {
	if x != nil {
		return x.NameKanji
	}
	return ""
}

func (x *StripeAccount_Company) GetOwnersProvided() bool {
	if x != nil {
		return x.OwnersProvided
	}
	return false
}

func (x *StripeAccount_Company) GetOwnershipDeclaration() *StripeAccount_OwnershipDeclaration {
	if x != nil {
		return x.OwnershipDeclaration
	}
	return nil
}

func (x *StripeAccount_Company) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *StripeAccount_Company) GetStructure() string {
	if x != nil {
		return x.Structure
	}
	return ""
}

func (x *StripeAccount_Company) GetTaxIdProvided() bool {
	if x != nil {
		return x.TaxIdProvided
	}
	return false
}

func (x *StripeAccount_Company) GetTaxIdRegistrar() string {
	if x != nil {
		return x.TaxIdRegistrar
	}
	return ""
}

func (x *StripeAccount_Company) GetVatIdProvided() bool {
	if x != nil {
		return x.VatIdProvided
	}
	return false
}

func (x *StripeAccount_Company) GetVerification() *StripeAccount_Verification {
	if x != nil {
		return x.Verification
	}
	return nil
}

// Address in Kana format.
type StripeAccount_AddressKana struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// City/Ward.
	City string `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	// Two-letter country code.
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// Block/Building number.
	Line1 string `protobuf:"bytes,3,opt,name=line1,proto3" json:"line1,omitempty"`
	// Building details.
	Line2 string `protobuf:"bytes,4,opt,name=line2,proto3" json:"line2,omitempty"`
	// ZIP or postal code.
	PostalCode string `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Prefecture.
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// Town/cho-me.
	Town string `protobuf:"bytes,7,opt,name=town,proto3" json:"town,omitempty"`
}

func (x *StripeAccount_AddressKana) Reset() {
	*x = StripeAccount_AddressKana{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_AddressKana) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_AddressKana) ProtoMessage() {}

func (x *StripeAccount_AddressKana) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_AddressKana.ProtoReflect.Descriptor instead.
func (*StripeAccount_AddressKana) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 5}
}

func (x *StripeAccount_AddressKana) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *StripeAccount_AddressKana) GetTown() string {
	if x != nil {
		return x.Town
	}
	return ""
}

// Address in Kanji format.
type StripeAccount_AddressKanji struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// City/Ward.
	City string `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	// Two-letter country code.
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// Block/Building number.
	Line1 string `protobuf:"bytes,3,opt,name=line1,proto3" json:"line1,omitempty"`
	// Building details.
	Line2 string `protobuf:"bytes,4,opt,name=line2,proto3" json:"line2,omitempty"`
	// ZIP or postal code.
	PostalCode string `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Prefecture.
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// Town/cho-me.
	Town string `protobuf:"bytes,7,opt,name=town,proto3" json:"town,omitempty"`
}

func (x *StripeAccount_AddressKanji) Reset() {
	*x = StripeAccount_AddressKanji{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_AddressKanji) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_AddressKanji) ProtoMessage() {}

func (x *StripeAccount_AddressKanji) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_AddressKanji.ProtoReflect.Descriptor instead.
func (*StripeAccount_AddressKanji) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 6}
}

func (x *StripeAccount_AddressKanji) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *StripeAccount_AddressKanji) GetTown() string {
	if x != nil {
		return x.Town
	}
	return ""
}

// Declaration of ownership.
type StripeAccount_OwnershipDeclaration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Timestamp of the beneficial owner attestation.
	Date int64 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	// IP address from which the attestation was made.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// User-agent of the browser used for attestation.
	UserAgent string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *StripeAccount_OwnershipDeclaration) Reset() {
	*x = StripeAccount_OwnershipDeclaration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_OwnershipDeclaration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_OwnershipDeclaration) ProtoMessage() {}

func (x *StripeAccount_OwnershipDeclaration) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_OwnershipDeclaration.ProtoReflect.Descriptor instead.
func (*StripeAccount_OwnershipDeclaration) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 7}
}

func (x *StripeAccount_OwnershipDeclaration) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *StripeAccount_OwnershipDeclaration) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *StripeAccount_OwnershipDeclaration) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// Verification information of the company.
type StripeAccount_Verification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Document for verification.
	Document *StripeAccount_Verification_Document `protobuf:"bytes,1,opt,name=document,proto3" json:"document,omitempty"`
}

func (x *StripeAccount_Verification) Reset() {
	*x = StripeAccount_Verification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Verification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Verification) ProtoMessage() {}

func (x *StripeAccount_Verification) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Verification.ProtoReflect.Descriptor instead.
func (*StripeAccount_Verification) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 8}
}

func (x *StripeAccount_Verification) GetDocument() *StripeAccount_Verification_Document {
	if x != nil {
		return x.Document
	}
	return nil
}

// Represents the controller information of an account.
type StripeAccount_Controller struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Indicates if the Connect application controls the account.
	IsController bool `protobuf:"varint,1,opt,name=is_controller,json=isController,proto3" json:"is_controller,omitempty"`
	// Type of the controller, either 'application' or 'account'.
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *StripeAccount_Controller) Reset() {
	*x = StripeAccount_Controller{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Controller) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Controller) ProtoMessage() {}

func (x *StripeAccount_Controller) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Controller.ProtoReflect.Descriptor instead.
func (*StripeAccount_Controller) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 9}
}

func (x *StripeAccount_Controller) GetIsController() bool {
	if x != nil {
		return x.IsController
	}
	return false
}

func (x *StripeAccount_Controller) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// future requirements
type StripeAccount_FutureRequirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fields that are due and can be satisfied by providing the corresponding alternative fields instead.
	Alternatives []*StripeAccount_FutureRequirements_Alternative `protobuf:"bytes,1,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	// Date on which future_requirements merges with the main requirements hash and future_requirements becomes empty.
	CurrentDeadline int64 `protobuf:"varint,2,opt,name=current_deadline,json=currentDeadline,proto3" json:"current_deadline,omitempty"`
	// Fields that need to be collected to keep the account enabled.
	CurrentlyDue []string `protobuf:"bytes,3,rep,name=currently_due,json=currentlyDue,proto3" json:"currently_due,omitempty"`
	// This is typed as a string for consistency with requirements.disabled_reason,
	// but it safe to assume future_requirements.disabled_reason is empty because fields in future_requirements will never disable the account.
	DisabledReason string `protobuf:"bytes,4,opt,name=disabled_reason,json=disabledReason,proto3" json:"disabled_reason,omitempty"`
	// Fields that are currently_due and need to be collected again because validation or verification failed.
	Errors []*StripeAccount_FutureRequirements_Errors `protobuf:"bytes,5,rep,name=errors,proto3" json:"errors,omitempty"`
	// Fields that need to be collected assuming all volume thresholds are reached.
	EventuallyDue []string `protobuf:"bytes,6,rep,name=eventually_due,json=eventuallyDue,proto3" json:"eventually_due,omitempty"`
	// Fields that weren't collected by requirements.current_deadline.
	PastDue []string `protobuf:"bytes,7,rep,name=past_due,json=pastDue,proto3" json:"past_due,omitempty"`
	// Fields that may become required depending on the results of verification or review.
	PendingVerification []string `protobuf:"bytes,8,rep,name=pending_verification,json=pendingVerification,proto3" json:"pending_verification,omitempty"`
}

func (x *StripeAccount_FutureRequirements) Reset() {
	*x = StripeAccount_FutureRequirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_FutureRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_FutureRequirements) ProtoMessage() {}

func (x *StripeAccount_FutureRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_FutureRequirements.ProtoReflect.Descriptor instead.
func (*StripeAccount_FutureRequirements) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 10}
}

func (x *StripeAccount_FutureRequirements) GetAlternatives() []*StripeAccount_FutureRequirements_Alternative {
	if x != nil {
		return x.Alternatives
	}
	return nil
}

func (x *StripeAccount_FutureRequirements) GetCurrentDeadline() int64 {
	if x != nil {
		return x.CurrentDeadline
	}
	return 0
}

func (x *StripeAccount_FutureRequirements) GetCurrentlyDue() []string {
	if x != nil {
		return x.CurrentlyDue
	}
	return nil
}

func (x *StripeAccount_FutureRequirements) GetDisabledReason() string {
	if x != nil {
		return x.DisabledReason
	}
	return ""
}

func (x *StripeAccount_FutureRequirements) GetErrors() []*StripeAccount_FutureRequirements_Errors {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *StripeAccount_FutureRequirements) GetEventuallyDue() []string {
	if x != nil {
		return x.EventuallyDue
	}
	return nil
}

func (x *StripeAccount_FutureRequirements) GetPastDue() []string {
	if x != nil {
		return x.PastDue
	}
	return nil
}

func (x *StripeAccount_FutureRequirements) GetPendingVerification() []string {
	if x != nil {
		return x.PendingVerification
	}
	return nil
}

// requirements
type StripeAccount_Requirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fields that are due and can be satisfied by providing the corresponding alternative fields instead.
	Alternatives []*StripeAccount_Requirements_Alternative `protobuf:"bytes,1,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	// Date by which the fields in currently_due must be collected to keep the account enabled.
	// These fields may disable the account sooner if the next threshold is reached before they are collected.
	CurrentDeadline int64 `protobuf:"varint,2,opt,name=current_deadline,json=currentDeadline,proto3" json:"current_deadline,omitempty"`
	// Fields that need to be collected to keep the account enabled. If not collected by current_deadline,
	// these fields appear in past_due as well, and the account is disabled.
	CurrentlyDue []string `protobuf:"bytes,3,rep,name=currently_due,json=currentlyDue,proto3" json:"currently_due,omitempty"`
	// If the account is disabled, this string describes why.
	// Can be requirements.past_due, requirements.pending_verification, listed, platform_paused,
	// rejected.fraud, rejected.listed, rejected.terms_of_service, rejected.other, under_review, or other.
	DisabledReason string `protobuf:"bytes,4,opt,name=disabled_reason,json=disabledReason,proto3" json:"disabled_reason,omitempty"`
	// Fields that are currently_due and need to be collected again because validation or verification failed.
	Errors []*StripeAccount_Requirements_Errors `protobuf:"bytes,5,rep,name=errors,proto3" json:"errors,omitempty"`
	// Fields that need to be collected assuming all volume thresholds are reached.
	// As they become required, they appear in currently_due as well, and current_deadline becomes set.
	EventuallyDue []string `protobuf:"bytes,6,rep,name=eventually_due,json=eventuallyDue,proto3" json:"eventually_due,omitempty"`
	// Fields that weren't collected by current_deadline.
	// These fields need to be collected to enable the account.
	PastDue []string `protobuf:"bytes,7,rep,name=past_due,json=pastDue,proto3" json:"past_due,omitempty"`
	// Fields that may become required depending on the results of verification or review.
	// Will be an empty array unless an asynchronous verification is pending.
	// If verification fails, these fields move to eventually_due, currently_due, or past_due.
	PendingVerification []string `protobuf:"bytes,8,rep,name=pending_verification,json=pendingVerification,proto3" json:"pending_verification,omitempty"`
}

func (x *StripeAccount_Requirements) Reset() {
	*x = StripeAccount_Requirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Requirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Requirements) ProtoMessage() {}

func (x *StripeAccount_Requirements) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Requirements.ProtoReflect.Descriptor instead.
func (*StripeAccount_Requirements) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 11}
}

func (x *StripeAccount_Requirements) GetAlternatives() []*StripeAccount_Requirements_Alternative {
	if x != nil {
		return x.Alternatives
	}
	return nil
}

func (x *StripeAccount_Requirements) GetCurrentDeadline() int64 {
	if x != nil {
		return x.CurrentDeadline
	}
	return 0
}

func (x *StripeAccount_Requirements) GetCurrentlyDue() []string {
	if x != nil {
		return x.CurrentlyDue
	}
	return nil
}

func (x *StripeAccount_Requirements) GetDisabledReason() string {
	if x != nil {
		return x.DisabledReason
	}
	return ""
}

func (x *StripeAccount_Requirements) GetErrors() []*StripeAccount_Requirements_Errors {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *StripeAccount_Requirements) GetEventuallyDue() []string {
	if x != nil {
		return x.EventuallyDue
	}
	return nil
}

func (x *StripeAccount_Requirements) GetPastDue() []string {
	if x != nil {
		return x.PastDue
	}
	return nil
}

func (x *StripeAccount_Requirements) GetPendingVerification() []string {
	if x != nil {
		return x.PendingVerification
	}
	return nil
}

// settings
type StripeAccount_Settings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bacs_debit_payments
	BacsDebitPayments *StripeAccount_Settings_BacsDebitPayments `protobuf:"bytes,1,opt,name=bacs_debit_payments,json=bacsDebitPayments,proto3" json:"bacs_debit_payments,omitempty"`
	// branding
	Branding *StripeAccount_Settings_Branding `protobuf:"bytes,2,opt,name=branding,proto3" json:"branding,omitempty"`
	// card_issuing
	CardIssuing *StripeAccount_Settings_CardIssuing `protobuf:"bytes,3,opt,name=card_issuing,json=cardIssuing,proto3" json:"card_issuing,omitempty"`
	// card_payments
	CardPayments *StripeAccount_Settings_CardPayments `protobuf:"bytes,4,opt,name=card_payments,json=cardPayments,proto3" json:"card_payments,omitempty"`
	// dashboard
	Dashboard *StripeAccount_Settings_Dashboard `protobuf:"bytes,5,opt,name=dashboard,proto3" json:"dashboard,omitempty"`
	// payments
	Payments *StripeAccount_Settings_Payments `protobuf:"bytes,6,opt,name=payments,proto3" json:"payments,omitempty"`
	// payouts
	Payouts *StripeAccount_Settings_Payouts `protobuf:"bytes,7,opt,name=payouts,proto3" json:"payouts,omitempty"`
	// sepa_debit_payments
	SepaDebitPayments *StripeAccount_Settings_SepaDebitPayments `protobuf:"bytes,8,opt,name=sepa_debit_payments,json=sepaDebitPayments,proto3" json:"sepa_debit_payments,omitempty"`
	// treasury
	Treasury *StripeAccount_Settings_Treasury `protobuf:"bytes,9,opt,name=treasury,proto3" json:"treasury,omitempty"`
}

func (x *StripeAccount_Settings) Reset() {
	*x = StripeAccount_Settings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings) ProtoMessage() {}

func (x *StripeAccount_Settings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12}
}

func (x *StripeAccount_Settings) GetBacsDebitPayments() *StripeAccount_Settings_BacsDebitPayments {
	if x != nil {
		return x.BacsDebitPayments
	}
	return nil
}

func (x *StripeAccount_Settings) GetBranding() *StripeAccount_Settings_Branding {
	if x != nil {
		return x.Branding
	}
	return nil
}

func (x *StripeAccount_Settings) GetCardIssuing() *StripeAccount_Settings_CardIssuing {
	if x != nil {
		return x.CardIssuing
	}
	return nil
}

func (x *StripeAccount_Settings) GetCardPayments() *StripeAccount_Settings_CardPayments {
	if x != nil {
		return x.CardPayments
	}
	return nil
}

func (x *StripeAccount_Settings) GetDashboard() *StripeAccount_Settings_Dashboard {
	if x != nil {
		return x.Dashboard
	}
	return nil
}

func (x *StripeAccount_Settings) GetPayments() *StripeAccount_Settings_Payments {
	if x != nil {
		return x.Payments
	}
	return nil
}

func (x *StripeAccount_Settings) GetPayouts() *StripeAccount_Settings_Payouts {
	if x != nil {
		return x.Payouts
	}
	return nil
}

func (x *StripeAccount_Settings) GetSepaDebitPayments() *StripeAccount_Settings_SepaDebitPayments {
	if x != nil {
		return x.SepaDebitPayments
	}
	return nil
}

func (x *StripeAccount_Settings) GetTreasury() *StripeAccount_Settings_Treasury {
	if x != nil {
		return x.Treasury
	}
	return nil
}

// tos acceptance
type StripeAccount_TosAcceptance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Unix timestamp marking when the account representative accepted their service agreement.
	Date int64 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	// The IP address from which the account representative accepted their service agreement.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// The user's service agreement type.
	ServiceAgreement string `protobuf:"bytes,3,opt,name=service_agreement,json=serviceAgreement,proto3" json:"service_agreement,omitempty"`
	// The user agent of the browser from which the account representative accepted their service agreement.
	UserAgent string `protobuf:"bytes,4,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *StripeAccount_TosAcceptance) Reset() {
	*x = StripeAccount_TosAcceptance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_TosAcceptance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_TosAcceptance) ProtoMessage() {}

func (x *StripeAccount_TosAcceptance) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_TosAcceptance.ProtoReflect.Descriptor instead.
func (*StripeAccount_TosAcceptance) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 13}
}

func (x *StripeAccount_TosAcceptance) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *StripeAccount_TosAcceptance) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *StripeAccount_TosAcceptance) GetServiceAgreement() string {
	if x != nil {
		return x.ServiceAgreement
	}
	return ""
}

func (x *StripeAccount_TosAcceptance) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// Document details for verification.
type StripeAccount_Verification_Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Back of the document.
	Back string `protobuf:"bytes,1,opt,name=back,proto3" json:"back,omitempty"`
	// Description of the verification state.
	Details string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	// Code specifying the verification state.
	DetailsCode string `protobuf:"bytes,3,opt,name=details_code,json=detailsCode,proto3" json:"details_code,omitempty"`
	// Front of the document.
	Front string `protobuf:"bytes,4,opt,name=front,proto3" json:"front,omitempty"`
}

func (x *StripeAccount_Verification_Document) Reset() {
	*x = StripeAccount_Verification_Document{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Verification_Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Verification_Document) ProtoMessage() {}

func (x *StripeAccount_Verification_Document) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Verification_Document.ProtoReflect.Descriptor instead.
func (*StripeAccount_Verification_Document) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 8, 0}
}

func (x *StripeAccount_Verification_Document) GetBack() string {
	if x != nil {
		return x.Back
	}
	return ""
}

func (x *StripeAccount_Verification_Document) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *StripeAccount_Verification_Document) GetDetailsCode() string {
	if x != nil {
		return x.DetailsCode
	}
	return ""
}

func (x *StripeAccount_Verification_Document) GetFront() string {
	if x != nil {
		return x.Front
	}
	return ""
}

// Alternative fields structure.
type StripeAccount_FutureRequirements_Alternative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fields that can be provided to satisfy all fields in original_fields_due.
	AlternativeFieldsDue []string `protobuf:"bytes,1,rep,name=alternative_fields_due,json=alternativeFieldsDue,proto3" json:"alternative_fields_due,omitempty"`
	// Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
	OriginalFieldsDue []string `protobuf:"bytes,2,rep,name=original_fields_due,json=originalFieldsDue,proto3" json:"original_fields_due,omitempty"`
}

func (x *StripeAccount_FutureRequirements_Alternative) Reset() {
	*x = StripeAccount_FutureRequirements_Alternative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_FutureRequirements_Alternative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_FutureRequirements_Alternative) ProtoMessage() {}

func (x *StripeAccount_FutureRequirements_Alternative) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_FutureRequirements_Alternative.ProtoReflect.Descriptor instead.
func (*StripeAccount_FutureRequirements_Alternative) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 10, 0}
}

func (x *StripeAccount_FutureRequirements_Alternative) GetAlternativeFieldsDue() []string {
	if x != nil {
		return x.AlternativeFieldsDue
	}
	return nil
}

func (x *StripeAccount_FutureRequirements_Alternative) GetOriginalFieldsDue() []string {
	if x != nil {
		return x.OriginalFieldsDue
	}
	return nil
}

// errors structure.
type StripeAccount_FutureRequirements_Errors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The code for the type of error.
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// An informative message that indicates the error type and provides additional details about the error.
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// The specific requirement related to this error.
	Requirement string `protobuf:"bytes,3,opt,name=requirement,proto3" json:"requirement,omitempty"`
}

func (x *StripeAccount_FutureRequirements_Errors) Reset() {
	*x = StripeAccount_FutureRequirements_Errors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_FutureRequirements_Errors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_FutureRequirements_Errors) ProtoMessage() {}

func (x *StripeAccount_FutureRequirements_Errors) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_FutureRequirements_Errors.ProtoReflect.Descriptor instead.
func (*StripeAccount_FutureRequirements_Errors) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 10, 1}
}

func (x *StripeAccount_FutureRequirements_Errors) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *StripeAccount_FutureRequirements_Errors) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripeAccount_FutureRequirements_Errors) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

// Alternative fields structure.
type StripeAccount_Requirements_Alternative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fields that can be provided to satisfy all fields in original_fields_due.
	AlternativeFieldsDue []string `protobuf:"bytes,1,rep,name=alternative_fields_due,json=alternativeFieldsDue,proto3" json:"alternative_fields_due,omitempty"`
	// Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
	OriginalFieldsDue []string `protobuf:"bytes,2,rep,name=original_fields_due,json=originalFieldsDue,proto3" json:"original_fields_due,omitempty"`
}

func (x *StripeAccount_Requirements_Alternative) Reset() {
	*x = StripeAccount_Requirements_Alternative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Requirements_Alternative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Requirements_Alternative) ProtoMessage() {}

func (x *StripeAccount_Requirements_Alternative) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Requirements_Alternative.ProtoReflect.Descriptor instead.
func (*StripeAccount_Requirements_Alternative) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 11, 0}
}

func (x *StripeAccount_Requirements_Alternative) GetAlternativeFieldsDue() []string {
	if x != nil {
		return x.AlternativeFieldsDue
	}
	return nil
}

func (x *StripeAccount_Requirements_Alternative) GetOriginalFieldsDue() []string {
	if x != nil {
		return x.OriginalFieldsDue
	}
	return nil
}

// errors structure
type StripeAccount_Requirements_Errors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The code for the type of error.
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// An informative message that indicates the error type and provides additional details about the error.
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// The specific requirement related to this error.
	Requirement string `protobuf:"bytes,3,opt,name=requirement,proto3" json:"requirement,omitempty"`
}

func (x *StripeAccount_Requirements_Errors) Reset() {
	*x = StripeAccount_Requirements_Errors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Requirements_Errors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Requirements_Errors) ProtoMessage() {}

func (x *StripeAccount_Requirements_Errors) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Requirements_Errors.ProtoReflect.Descriptor instead.
func (*StripeAccount_Requirements_Errors) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 11, 1}
}

func (x *StripeAccount_Requirements_Errors) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *StripeAccount_Requirements_Errors) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripeAccount_Requirements_Errors) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

// bacs debit payments
type StripeAccount_Settings_BacsDebitPayments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Bacs Direct Debit Display Name for this account. For payments made with Bacs Direct Debit, this will appear on the mandate, and as the statement descriptor.
	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *StripeAccount_Settings_BacsDebitPayments) Reset() {
	*x = StripeAccount_Settings_BacsDebitPayments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_BacsDebitPayments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_BacsDebitPayments) ProtoMessage() {}

func (x *StripeAccount_Settings_BacsDebitPayments) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_BacsDebitPayments.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_BacsDebitPayments) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 0}
}

func (x *StripeAccount_Settings_BacsDebitPayments) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// branding
type StripeAccount_Settings_Branding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An icon for the account. Must be square and at least 128px x 128px.
	Icon string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// A logo for the account that will be used in Checkout instead of the icon and without the account's name next to it if provided. Must be at least 128px x 128px.
	Logo string `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo,omitempty"`
	// A CSS hex color value representing the primary branding color for this account.
	PrimaryColor string `protobuf:"bytes,3,opt,name=primary_color,json=primaryColor,proto3" json:"primary_color,omitempty"`
	// A CSS hex color value representing the secondary branding color for this account.
	SecondaryColor string `protobuf:"bytes,4,opt,name=secondary_color,json=secondaryColor,proto3" json:"secondary_color,omitempty"`
}

func (x *StripeAccount_Settings_Branding) Reset() {
	*x = StripeAccount_Settings_Branding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Branding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Branding) ProtoMessage() {}

func (x *StripeAccount_Settings_Branding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Branding.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Branding) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 1}
}

func (x *StripeAccount_Settings_Branding) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *StripeAccount_Settings_Branding) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *StripeAccount_Settings_Branding) GetPrimaryColor() string {
	if x != nil {
		return x.PrimaryColor
	}
	return ""
}

func (x *StripeAccount_Settings_Branding) GetSecondaryColor() string {
	if x != nil {
		return x.SecondaryColor
	}
	return ""
}

// card issuing
type StripeAccount_Settings_CardIssuing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tos acceptance
	TosAcceptance *StripeAccount_Settings_CardIssuing_TosAcceptance `protobuf:"bytes,1,opt,name=tos_acceptance,json=tosAcceptance,proto3" json:"tos_acceptance,omitempty"`
}

func (x *StripeAccount_Settings_CardIssuing) Reset() {
	*x = StripeAccount_Settings_CardIssuing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_CardIssuing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_CardIssuing) ProtoMessage() {}

func (x *StripeAccount_Settings_CardIssuing) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_CardIssuing.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_CardIssuing) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 2}
}

func (x *StripeAccount_Settings_CardIssuing) GetTosAcceptance() *StripeAccount_Settings_CardIssuing_TosAcceptance {
	if x != nil {
		return x.TosAcceptance
	}
	return nil
}

// card payments
type StripeAccount_Settings_CardPayments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// decline on message
	DeclineOn *StripeAccount_Settings_CardPayments_DeclineOn `protobuf:"bytes,1,opt,name=decline_on,json=declineOn,proto3" json:"decline_on,omitempty"`
	// The default text that appears on credit card statements when a charge is made.
	// This field prefixes any dynamic statement_descriptor specified on the charge.
	StatementDescriptorPrefix string `protobuf:"bytes,2,opt,name=statement_descriptor_prefix,json=statementDescriptorPrefix,proto3" json:"statement_descriptor_prefix,omitempty"`
	// The Kana variation of the default text that appears on credit card statements when a charge is made (Japan only).
	StatementDescriptorPrefixKana string `protobuf:"bytes,3,opt,name=statement_descriptor_prefix_kana,json=statementDescriptorPrefixKana,proto3" json:"statement_descriptor_prefix_kana,omitempty"`
	// The Kanji variation of the default text that appears on credit card statements when a charge is made (Japan only).
	StatementDescriptorPrefixKanji string `protobuf:"bytes,4,opt,name=statement_descriptor_prefix_kanji,json=statementDescriptorPrefixKanji,proto3" json:"statement_descriptor_prefix_kanji,omitempty"`
}

func (x *StripeAccount_Settings_CardPayments) Reset() {
	*x = StripeAccount_Settings_CardPayments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_CardPayments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_CardPayments) ProtoMessage() {}

func (x *StripeAccount_Settings_CardPayments) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_CardPayments.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_CardPayments) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 3}
}

func (x *StripeAccount_Settings_CardPayments) GetDeclineOn() *StripeAccount_Settings_CardPayments_DeclineOn {
	if x != nil {
		return x.DeclineOn
	}
	return nil
}

func (x *StripeAccount_Settings_CardPayments) GetStatementDescriptorPrefix() string {
	if x != nil {
		return x.StatementDescriptorPrefix
	}
	return ""
}

func (x *StripeAccount_Settings_CardPayments) GetStatementDescriptorPrefixKana() string {
	if x != nil {
		return x.StatementDescriptorPrefixKana
	}
	return ""
}

func (x *StripeAccount_Settings_CardPayments) GetStatementDescriptorPrefixKanji() string {
	if x != nil {
		return x.StatementDescriptorPrefixKanji
	}
	return ""
}

// dashboard
type StripeAccount_Settings_Dashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The display name for this account. This is used on the Stripe Dashboard to differentiate between accounts.
	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// The timezone used in the Stripe Dashboard for this account. A list of possible time zone values is maintained at the IANA Time Zone Database.
	Timezone string `protobuf:"bytes,2,opt,name=timezone,proto3" json:"timezone,omitempty"`
}

func (x *StripeAccount_Settings_Dashboard) Reset() {
	*x = StripeAccount_Settings_Dashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Dashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Dashboard) ProtoMessage() {}

func (x *StripeAccount_Settings_Dashboard) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Dashboard.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Dashboard) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 4}
}

func (x *StripeAccount_Settings_Dashboard) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *StripeAccount_Settings_Dashboard) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

// payments
type StripeAccount_Settings_Payments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The default text that appears on credit card statements when a charge is made. This field
	// prefixes any dynamic statement_descriptor specified on the charge.
	StatementDescriptor string `protobuf:"bytes,1,opt,name=statement_descriptor,json=statementDescriptor,proto3" json:"statement_descriptor,omitempty"`
	// The Kana variation of the default text that appears on credit card statements when a charge
	// is made (Japan only).
	StatementDescriptorKana string `protobuf:"bytes,2,opt,name=statement_descriptor_kana,json=statementDescriptorKana,proto3" json:"statement_descriptor_kana,omitempty"`
	// The Kanji variation of the default text that appears on credit card statements when a charge
	// is made (Japan only).
	StatementDescriptorKanji string `protobuf:"bytes,3,opt,name=statement_descriptor_kanji,json=statementDescriptorKanji,proto3" json:"statement_descriptor_kanji,omitempty"`
	// The Kana variation of the default text that appears on credit card statements when a charge
	// is made (Japan only). This field prefixes any dynamic statement_descriptor_suffix_kana
	// specified on the charge. statement_descriptor_prefix_kana is useful for maximizing descriptor
	// space for the dynamic portion.
	StatementDescriptorPrefixKana string `protobuf:"bytes,4,opt,name=statement_descriptor_prefix_kana,json=statementDescriptorPrefixKana,proto3" json:"statement_descriptor_prefix_kana,omitempty"`
	// The Kanji variation of the default text that appears on credit card statements when a charge
	// is made (Japan only). This field prefixes any dynamic statement_descriptor_suffix_kanji
	// specified on the charge. statement_descriptor_prefix_kanji is useful for maximizing descriptor
	// space for the dynamic portion.
	StatementDescriptorPrefixKanji string `protobuf:"bytes,5,opt,name=statement_descriptor_prefix_kanji,json=statementDescriptorPrefixKanji,proto3" json:"statement_descriptor_prefix_kanji,omitempty"`
}

func (x *StripeAccount_Settings_Payments) Reset() {
	*x = StripeAccount_Settings_Payments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Payments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Payments) ProtoMessage() {}

func (x *StripeAccount_Settings_Payments) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Payments.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Payments) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 5}
}

func (x *StripeAccount_Settings_Payments) GetStatementDescriptor() string {
	if x != nil {
		return x.StatementDescriptor
	}
	return ""
}

func (x *StripeAccount_Settings_Payments) GetStatementDescriptorKana() string {
	if x != nil {
		return x.StatementDescriptorKana
	}
	return ""
}

func (x *StripeAccount_Settings_Payments) GetStatementDescriptorKanji() string {
	if x != nil {
		return x.StatementDescriptorKanji
	}
	return ""
}

func (x *StripeAccount_Settings_Payments) GetStatementDescriptorPrefixKana() string {
	if x != nil {
		return x.StatementDescriptorPrefixKana
	}
	return ""
}

func (x *StripeAccount_Settings_Payments) GetStatementDescriptorPrefixKanji() string {
	if x != nil {
		return x.StatementDescriptorPrefixKanji
	}
	return ""
}

// payouts
type StripeAccount_Settings_Payouts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A Boolean indicating if Stripe should try to reclaim negative balances from an attached bank account.
	// Default value is false for Custom accounts, otherwise true.
	DebitNegativeBalances bool `protobuf:"varint,1,opt,name=debit_negative_balances,json=debitNegativeBalances,proto3" json:"debit_negative_balances,omitempty"`
	// The schedule for payouts.
	Schedule *StripeAccount_Settings_Payouts_Schedule `protobuf:"bytes,2,opt,name=schedule,proto3" json:"schedule,omitempty"`
	// The text that appears on the bank account statement for payouts.
	// If not set, this defaults to the platform's bank descriptor as set in the Dashboard.
	StatementDescriptor string `protobuf:"bytes,3,opt,name=statement_descriptor,json=statementDescriptor,proto3" json:"statement_descriptor,omitempty"`
}

func (x *StripeAccount_Settings_Payouts) Reset() {
	*x = StripeAccount_Settings_Payouts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Payouts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Payouts) ProtoMessage() {}

func (x *StripeAccount_Settings_Payouts) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Payouts.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Payouts) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 6}
}

func (x *StripeAccount_Settings_Payouts) GetDebitNegativeBalances() bool {
	if x != nil {
		return x.DebitNegativeBalances
	}
	return false
}

func (x *StripeAccount_Settings_Payouts) GetSchedule() *StripeAccount_Settings_Payouts_Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *StripeAccount_Settings_Payouts) GetStatementDescriptor() string {
	if x != nil {
		return x.StatementDescriptor
	}
	return ""
}

// sepa debit payments
type StripeAccount_Settings_SepaDebitPayments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SEPA creditor identifier that identifies the company making the payment.
	CreditorId string `protobuf:"bytes,1,opt,name=creditor_id,json=creditorId,proto3" json:"creditor_id,omitempty"`
}

func (x *StripeAccount_Settings_SepaDebitPayments) Reset() {
	*x = StripeAccount_Settings_SepaDebitPayments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_SepaDebitPayments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_SepaDebitPayments) ProtoMessage() {}

func (x *StripeAccount_Settings_SepaDebitPayments) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_SepaDebitPayments.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_SepaDebitPayments) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 7}
}

func (x *StripeAccount_Settings_SepaDebitPayments) GetCreditorId() string {
	if x != nil {
		return x.CreditorId
	}
	return ""
}

// treasury
type StripeAccount_Settings_Treasury struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Acceptance terms of service.
	TosAcceptance *StripeAccount_Settings_Treasury_TosAcceptance `protobuf:"bytes,1,opt,name=tos_acceptance,json=tosAcceptance,proto3" json:"tos_acceptance,omitempty"`
}

func (x *StripeAccount_Settings_Treasury) Reset() {
	*x = StripeAccount_Settings_Treasury{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Treasury) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Treasury) ProtoMessage() {}

func (x *StripeAccount_Settings_Treasury) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Treasury.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Treasury) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 8}
}

func (x *StripeAccount_Settings_Treasury) GetTosAcceptance() *StripeAccount_Settings_Treasury_TosAcceptance {
	if x != nil {
		return x.TosAcceptance
	}
	return nil
}

// tos acceptance
type StripeAccount_Settings_CardIssuing_TosAcceptance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Unix timestamp marking when the account representative accepted the service agreement.
	Date int64 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	// The IP address from which the account representative accepted the service agreement.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// The user agent of the browser from which the account representative accepted the service agreement.
	UserAgent string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) Reset() {
	*x = StripeAccount_Settings_CardIssuing_TosAcceptance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_CardIssuing_TosAcceptance) ProtoMessage() {}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_CardIssuing_TosAcceptance.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_CardIssuing_TosAcceptance) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 2, 0}
}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *StripeAccount_Settings_CardIssuing_TosAcceptance) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// decline on message
type StripeAccount_Settings_CardPayments_DeclineOn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether Stripe automatically declines charges with an incorrect ZIP or postal code.
	AvsFailure bool `protobuf:"varint,1,opt,name=avs_failure,json=avsFailure,proto3" json:"avs_failure,omitempty"`
	// Whether Stripe automatically declines charges with an incorrect CVC.
	CvcFailure bool `protobuf:"varint,2,opt,name=cvc_failure,json=cvcFailure,proto3" json:"cvc_failure,omitempty"`
}

func (x *StripeAccount_Settings_CardPayments_DeclineOn) Reset() {
	*x = StripeAccount_Settings_CardPayments_DeclineOn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_CardPayments_DeclineOn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_CardPayments_DeclineOn) ProtoMessage() {}

func (x *StripeAccount_Settings_CardPayments_DeclineOn) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_CardPayments_DeclineOn.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_CardPayments_DeclineOn) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 3, 0}
}

func (x *StripeAccount_Settings_CardPayments_DeclineOn) GetAvsFailure() bool {
	if x != nil {
		return x.AvsFailure
	}
	return false
}

func (x *StripeAccount_Settings_CardPayments_DeclineOn) GetCvcFailure() bool {
	if x != nil {
		return x.CvcFailure
	}
	return false
}

// The schedule for payouts.
type StripeAccount_Settings_Payouts_Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of days charges for the account will be held before being paid out.
	DelayDays int64 `protobuf:"varint,1,opt,name=delay_days,json=delayDays,proto3" json:"delay_days,omitempty"`
	// How frequently funds will be paid out. One of manual (payouts only created via API call), daily, weekly, or monthly.
	Interval string `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// The day of the month funds will be paid out. Only shown if interval is monthly.
	// Payouts scheduled between the 29th and 31st of the month are sent on the last day of shorter months.
	MonthlyAnchor int64 `protobuf:"varint,3,opt,name=monthly_anchor,json=monthlyAnchor,proto3" json:"monthly_anchor,omitempty"`
	// The day of the week funds will be paid out, of the style 'monday', 'tuesday', etc.
	// Only shown if interval is weekly.
	WeeklyAnchor string `protobuf:"bytes,4,opt,name=weekly_anchor,json=weeklyAnchor,proto3" json:"weekly_anchor,omitempty"`
}

func (x *StripeAccount_Settings_Payouts_Schedule) Reset() {
	*x = StripeAccount_Settings_Payouts_Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Payouts_Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Payouts_Schedule) ProtoMessage() {}

func (x *StripeAccount_Settings_Payouts_Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Payouts_Schedule.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Payouts_Schedule) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 6, 0}
}

func (x *StripeAccount_Settings_Payouts_Schedule) GetDelayDays() int64 {
	if x != nil {
		return x.DelayDays
	}
	return 0
}

func (x *StripeAccount_Settings_Payouts_Schedule) GetInterval() string {
	if x != nil {
		return x.Interval
	}
	return ""
}

func (x *StripeAccount_Settings_Payouts_Schedule) GetMonthlyAnchor() int64 {
	if x != nil {
		return x.MonthlyAnchor
	}
	return 0
}

func (x *StripeAccount_Settings_Payouts_Schedule) GetWeeklyAnchor() string {
	if x != nil {
		return x.WeeklyAnchor
	}
	return ""
}

// Acceptance terms of service.
type StripeAccount_Settings_Treasury_TosAcceptance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Unix timestamp marking when the account representative accepted the service agreement.
	Date int64 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	// The IP address from which the account representative accepted the service agreement.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// The user agent of the browser from which the account representative accepted the service agreement.
	UserAgent string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) Reset() {
	*x = StripeAccount_Settings_Treasury_TosAcceptance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeAccount_Settings_Treasury_TosAcceptance) ProtoMessage() {}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeAccount_Settings_Treasury_TosAcceptance.ProtoReflect.Descriptor instead.
func (*StripeAccount_Settings_Treasury_TosAcceptance) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{0, 12, 8, 0}
}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *StripeAccount_Settings_Treasury_TosAcceptance) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// Address in Kana format.
type StripePerson_AddressKana struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// City/Ward.
	City string `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	// Two-letter country code (ISO 3166-1 alpha-2).
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// Block/Building number.
	Line1 string `protobuf:"bytes,3,opt,name=line1,proto3" json:"line1,omitempty"`
	// Building details.
	Line2 string `protobuf:"bytes,4,opt,name=line2,proto3" json:"line2,omitempty"`
	// ZIP or postal code.
	PostalCode string `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Prefecture.
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// Town/cho-me.
	Town string `protobuf:"bytes,7,opt,name=town,proto3" json:"town,omitempty"`
}

func (x *StripePerson_AddressKana) Reset() {
	*x = StripePerson_AddressKana{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_AddressKana) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_AddressKana) ProtoMessage() {}

func (x *StripePerson_AddressKana) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_AddressKana.ProtoReflect.Descriptor instead.
func (*StripePerson_AddressKana) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 1}
}

func (x *StripePerson_AddressKana) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *StripePerson_AddressKana) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripePerson_AddressKana) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *StripePerson_AddressKana) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

func (x *StripePerson_AddressKana) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *StripePerson_AddressKana) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *StripePerson_AddressKana) GetTown() string {
	if x != nil {
		return x.Town
	}
	return ""
}

// Address in Kanji format.
type StripePerson_AddressKanji struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// City/Ward.
	City string `protobuf:"bytes,1,opt,name=city,proto3" json:"city,omitempty"`
	// Two-letter country code (ISO 3166-1 alpha-2).
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// Block/Building number.
	Line1 string `protobuf:"bytes,3,opt,name=line1,proto3" json:"line1,omitempty"`
	// Building details.
	Line2 string `protobuf:"bytes,4,opt,name=line2,proto3" json:"line2,omitempty"`
	// ZIP or postal code.
	PostalCode string `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// Prefecture.
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// Town/cho-me.
	Town string `protobuf:"bytes,7,opt,name=town,proto3" json:"town,omitempty"`
}

func (x *StripePerson_AddressKanji) Reset() {
	*x = StripePerson_AddressKanji{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_AddressKanji) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_AddressKanji) ProtoMessage() {}

func (x *StripePerson_AddressKanji) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_AddressKanji.ProtoReflect.Descriptor instead.
func (*StripePerson_AddressKanji) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 2}
}

func (x *StripePerson_AddressKanji) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *StripePerson_AddressKanji) GetTown() string {
	if x != nil {
		return x.Town
	}
	return ""
}

// Date of birth.
type StripePerson_Dob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The day of birth, between 1 and 31.
	Day int32 `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	// The month of birth, between 1 and 12.
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	// The four-digit year of birth.
	Year int32 `protobuf:"varint,3,opt,name=year,proto3" json:"year,omitempty"`
}

func (x *StripePerson_Dob) Reset() {
	*x = StripePerson_Dob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Dob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Dob) ProtoMessage() {}

func (x *StripePerson_Dob) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Dob.ProtoReflect.Descriptor instead.
func (*StripePerson_Dob) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 3}
}

func (x *StripePerson_Dob) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *StripePerson_Dob) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *StripePerson_Dob) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

// Future requirements for a person's account.
type StripePerson_FutureRequirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Alternatives to satisfy requirements.
	Alternatives []*StripePerson_FutureRequirements_Alternative `protobuf:"bytes,1,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	// Fields currently due.
	CurrentlyDue []string `protobuf:"bytes,2,rep,name=currently_due,json=currentlyDue,proto3" json:"currently_due,omitempty"`
	// Errors in validation or verification.
	Errors []*StripePerson_FutureRequirements_Errors `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty"`
	// Fields that will eventually be due.
	EventuallyDue []string `protobuf:"bytes,4,rep,name=eventually_due,json=eventuallyDue,proto3" json:"eventually_due,omitempty"`
	// Fields that are past due.
	PastDue []string `protobuf:"bytes,5,rep,name=past_due,json=pastDue,proto3" json:"past_due,omitempty"`
	// Fields pending verification.
	PendingVerification []string `protobuf:"bytes,6,rep,name=pending_verification,json=pendingVerification,proto3" json:"pending_verification,omitempty"`
}

func (x *StripePerson_FutureRequirements) Reset() {
	*x = StripePerson_FutureRequirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_FutureRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_FutureRequirements) ProtoMessage() {}

func (x *StripePerson_FutureRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_FutureRequirements.ProtoReflect.Descriptor instead.
func (*StripePerson_FutureRequirements) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 4}
}

func (x *StripePerson_FutureRequirements) GetAlternatives() []*StripePerson_FutureRequirements_Alternative {
	if x != nil {
		return x.Alternatives
	}
	return nil
}

func (x *StripePerson_FutureRequirements) GetCurrentlyDue() []string {
	if x != nil {
		return x.CurrentlyDue
	}
	return nil
}

func (x *StripePerson_FutureRequirements) GetErrors() []*StripePerson_FutureRequirements_Errors {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *StripePerson_FutureRequirements) GetEventuallyDue() []string {
	if x != nil {
		return x.EventuallyDue
	}
	return nil
}

func (x *StripePerson_FutureRequirements) GetPastDue() []string {
	if x != nil {
		return x.PastDue
	}
	return nil
}

func (x *StripePerson_FutureRequirements) GetPendingVerification() []string {
	if x != nil {
		return x.PendingVerification
	}
	return nil
}

// Relationship information for a person.
type StripePerson_Relationship struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the person is a director.
	Director bool `protobuf:"varint,1,opt,name=director,proto3" json:"director,omitempty"`
	// Whether the person is an executive.
	Executive bool `protobuf:"varint,2,opt,name=executive,proto3" json:"executive,omitempty"`
	// Whether the person is an owner.
	Owner bool `protobuf:"varint,3,opt,name=owner,proto3" json:"owner,omitempty"`
	// Percent ownership.
	PercentOwnership float64 `protobuf:"fixed64,4,opt,name=percent_ownership,json=percentOwnership,proto3" json:"percent_ownership,omitempty"`
	// Whether the person is the primary representative.
	Representative bool `protobuf:"varint,5,opt,name=representative,proto3" json:"representative,omitempty"`
	// The person's title.
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *StripePerson_Relationship) Reset() {
	*x = StripePerson_Relationship{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Relationship) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Relationship) ProtoMessage() {}

func (x *StripePerson_Relationship) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Relationship.ProtoReflect.Descriptor instead.
func (*StripePerson_Relationship) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 5}
}

func (x *StripePerson_Relationship) GetDirector() bool {
	if x != nil {
		return x.Director
	}
	return false
}

func (x *StripePerson_Relationship) GetExecutive() bool {
	if x != nil {
		return x.Executive
	}
	return false
}

func (x *StripePerson_Relationship) GetOwner() bool {
	if x != nil {
		return x.Owner
	}
	return false
}

func (x *StripePerson_Relationship) GetPercentOwnership() float64 {
	if x != nil {
		return x.PercentOwnership
	}
	return 0
}

func (x *StripePerson_Relationship) GetRepresentative() bool {
	if x != nil {
		return x.Representative
	}
	return false
}

func (x *StripePerson_Relationship) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// Requirements for a person's account.
type StripePerson_Requirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Alternatives to satisfy requirements.
	Alternatives []*StripePerson_Requirements_Alternative `protobuf:"bytes,1,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	// Fields currently due.
	CurrentlyDue []string `protobuf:"bytes,2,rep,name=currently_due,json=currentlyDue,proto3" json:"currently_due,omitempty"`
	// Errors in validation or verification.
	Errors []*StripePerson_Requirements_Errors `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty"`
	// Fields that will eventually be due.
	EventuallyDue []string `protobuf:"bytes,4,rep,name=eventually_due,json=eventuallyDue,proto3" json:"eventually_due,omitempty"`
	// Fields that are past due.
	PastDue []string `protobuf:"bytes,5,rep,name=past_due,json=pastDue,proto3" json:"past_due,omitempty"`
	// Fields pending verification.
	PendingVerification []string `protobuf:"bytes,6,rep,name=pending_verification,json=pendingVerification,proto3" json:"pending_verification,omitempty"`
}

func (x *StripePerson_Requirements) Reset() {
	*x = StripePerson_Requirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Requirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Requirements) ProtoMessage() {}

func (x *StripePerson_Requirements) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Requirements.ProtoReflect.Descriptor instead.
func (*StripePerson_Requirements) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 6}
}

func (x *StripePerson_Requirements) GetAlternatives() []*StripePerson_Requirements_Alternative {
	if x != nil {
		return x.Alternatives
	}
	return nil
}

func (x *StripePerson_Requirements) GetCurrentlyDue() []string {
	if x != nil {
		return x.CurrentlyDue
	}
	return nil
}

func (x *StripePerson_Requirements) GetErrors() []*StripePerson_Requirements_Errors {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *StripePerson_Requirements) GetEventuallyDue() []string {
	if x != nil {
		return x.EventuallyDue
	}
	return nil
}

func (x *StripePerson_Requirements) GetPastDue() []string {
	if x != nil {
		return x.PastDue
	}
	return nil
}

func (x *StripePerson_Requirements) GetPendingVerification() []string {
	if x != nil {
		return x.PendingVerification
	}
	return nil
}

// Verification information for a person.
type StripePerson_Verification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Additional document for address verification.
	AdditionalDocument *StripePerson_Verification_AdditionalDocument `protobuf:"bytes,1,opt,name=additional_document,json=additionalDocument,proto3" json:"additional_document,omitempty"`
	// Description of the verification state.
	Details string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	// Machine-readable code specifying the verification state.
	DetailsCode string `protobuf:"bytes,3,opt,name=details_code,json=detailsCode,proto3" json:"details_code,omitempty"`
	// Primary document for identity verification.
	Document *StripePerson_Verification_Document `protobuf:"bytes,4,opt,name=document,proto3" json:"document,omitempty"`
	// Verification status.
	Status string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StripePerson_Verification) Reset() {
	*x = StripePerson_Verification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Verification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Verification) ProtoMessage() {}

func (x *StripePerson_Verification) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Verification.ProtoReflect.Descriptor instead.
func (*StripePerson_Verification) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 7}
}

func (x *StripePerson_Verification) GetAdditionalDocument() *StripePerson_Verification_AdditionalDocument {
	if x != nil {
		return x.AdditionalDocument
	}
	return nil
}

func (x *StripePerson_Verification) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *StripePerson_Verification) GetDetailsCode() string {
	if x != nil {
		return x.DetailsCode
	}
	return ""
}

func (x *StripePerson_Verification) GetDocument() *StripePerson_Verification_Document {
	if x != nil {
		return x.Document
	}
	return nil
}

func (x *StripePerson_Verification) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// Alternative fields structure.
type StripePerson_FutureRequirements_Alternative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Alternative fields due.
	AlternativeFieldsDue []string `protobuf:"bytes,1,rep,name=alternative_fields_due,json=alternativeFieldsDue,proto3" json:"alternative_fields_due,omitempty"`
	// Original fields due.
	OriginalFieldsDue []string `protobuf:"bytes,2,rep,name=original_fields_due,json=originalFieldsDue,proto3" json:"original_fields_due,omitempty"`
}

func (x *StripePerson_FutureRequirements_Alternative) Reset() {
	*x = StripePerson_FutureRequirements_Alternative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_FutureRequirements_Alternative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_FutureRequirements_Alternative) ProtoMessage() {}

func (x *StripePerson_FutureRequirements_Alternative) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_FutureRequirements_Alternative.ProtoReflect.Descriptor instead.
func (*StripePerson_FutureRequirements_Alternative) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 4, 0}
}

func (x *StripePerson_FutureRequirements_Alternative) GetAlternativeFieldsDue() []string {
	if x != nil {
		return x.AlternativeFieldsDue
	}
	return nil
}

func (x *StripePerson_FutureRequirements_Alternative) GetOriginalFieldsDue() []string {
	if x != nil {
		return x.OriginalFieldsDue
	}
	return nil
}

// Errors structure.
type StripePerson_FutureRequirements_Errors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Error code.
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// Error reason.
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// Requirement related to the error.
	Requirement string `protobuf:"bytes,3,opt,name=requirement,proto3" json:"requirement,omitempty"`
}

func (x *StripePerson_FutureRequirements_Errors) Reset() {
	*x = StripePerson_FutureRequirements_Errors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_FutureRequirements_Errors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_FutureRequirements_Errors) ProtoMessage() {}

func (x *StripePerson_FutureRequirements_Errors) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_FutureRequirements_Errors.ProtoReflect.Descriptor instead.
func (*StripePerson_FutureRequirements_Errors) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 4, 1}
}

func (x *StripePerson_FutureRequirements_Errors) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *StripePerson_FutureRequirements_Errors) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripePerson_FutureRequirements_Errors) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

// Alternative structure for satisfying fields.
type StripePerson_Requirements_Alternative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Fields that can be provided to satisfy all fields in original_fields_due.
	AlternativeFieldsDue []string `protobuf:"bytes,1,rep,name=alternative_fields_due,json=alternativeFieldsDue,proto3" json:"alternative_fields_due,omitempty"`
	// Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
	OriginalFieldsDue []string `protobuf:"bytes,2,rep,name=original_fields_due,json=originalFieldsDue,proto3" json:"original_fields_due,omitempty"`
}

func (x *StripePerson_Requirements_Alternative) Reset() {
	*x = StripePerson_Requirements_Alternative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Requirements_Alternative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Requirements_Alternative) ProtoMessage() {}

func (x *StripePerson_Requirements_Alternative) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Requirements_Alternative.ProtoReflect.Descriptor instead.
func (*StripePerson_Requirements_Alternative) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 6, 0}
}

func (x *StripePerson_Requirements_Alternative) GetAlternativeFieldsDue() []string {
	if x != nil {
		return x.AlternativeFieldsDue
	}
	return nil
}

func (x *StripePerson_Requirements_Alternative) GetOriginalFieldsDue() []string {
	if x != nil {
		return x.OriginalFieldsDue
	}
	return nil
}

// Errors structure for requirement validation.
type StripePerson_Requirements_Errors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The code for the type of error.
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// An informative message that indicates the error type and provides additional details.
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// Requirement related to the error.
	Requirement string `protobuf:"bytes,3,opt,name=requirement,proto3" json:"requirement,omitempty"`
}

func (x *StripePerson_Requirements_Errors) Reset() {
	*x = StripePerson_Requirements_Errors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Requirements_Errors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Requirements_Errors) ProtoMessage() {}

func (x *StripePerson_Requirements_Errors) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Requirements_Errors.ProtoReflect.Descriptor instead.
func (*StripePerson_Requirements_Errors) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 6, 1}
}

func (x *StripePerson_Requirements_Errors) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *StripePerson_Requirements_Errors) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StripePerson_Requirements_Errors) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

// Additional document structure.
type StripePerson_Verification_AdditionalDocument struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Back of the ID.
	Back string `protobuf:"bytes,1,opt,name=back,proto3" json:"back,omitempty"`
	// Details about the document's verification state.
	Details string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	// Code specifying the verification state for the document.
	DetailsCode string `protobuf:"bytes,3,opt,name=details_code,json=detailsCode,proto3" json:"details_code,omitempty"`
	// Front of the ID.
	Front string `protobuf:"bytes,4,opt,name=front,proto3" json:"front,omitempty"`
}

func (x *StripePerson_Verification_AdditionalDocument) Reset() {
	*x = StripePerson_Verification_AdditionalDocument{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Verification_AdditionalDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Verification_AdditionalDocument) ProtoMessage() {}

func (x *StripePerson_Verification_AdditionalDocument) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Verification_AdditionalDocument.ProtoReflect.Descriptor instead.
func (*StripePerson_Verification_AdditionalDocument) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 7, 0}
}

func (x *StripePerson_Verification_AdditionalDocument) GetBack() string {
	if x != nil {
		return x.Back
	}
	return ""
}

func (x *StripePerson_Verification_AdditionalDocument) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *StripePerson_Verification_AdditionalDocument) GetDetailsCode() string {
	if x != nil {
		return x.DetailsCode
	}
	return ""
}

func (x *StripePerson_Verification_AdditionalDocument) GetFront() string {
	if x != nil {
		return x.Front
	}
	return ""
}

// Document structure.
type StripePerson_Verification_Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Back of the ID.
	Back string `protobuf:"bytes,1,opt,name=back,proto3" json:"back,omitempty"`
	// Details about the document's verification state.
	Details string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	// Code specifying the verification state for the document.
	DetailsCode string `protobuf:"bytes,3,opt,name=details_code,json=detailsCode,proto3" json:"details_code,omitempty"`
	// Front of the ID.
	Front string `protobuf:"bytes,4,opt,name=front,proto3" json:"front,omitempty"`
}

func (x *StripePerson_Verification_Document) Reset() {
	*x = StripePerson_Verification_Document{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripePerson_Verification_Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripePerson_Verification_Document) ProtoMessage() {}

func (x *StripePerson_Verification_Document) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripePerson_Verification_Document.ProtoReflect.Descriptor instead.
func (*StripePerson_Verification_Document) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP(), []int{2, 7, 1}
}

func (x *StripePerson_Verification_Document) GetBack() string {
	if x != nil {
		return x.Back
	}
	return ""
}

func (x *StripePerson_Verification_Document) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

func (x *StripePerson_Verification_Document) GetDetailsCode() string {
	if x != nil {
		return x.DetailsCode
	}
	return ""
}

func (x *StripePerson_Verification_Document) GetFront() string {
	if x != nil {
		return x.Front
	}
	return ""
}

var File_moego_models_pay_ops_v1_verification_model_proto protoreflect.FileDescriptor

var file_moego_models_pay_ops_v1_verification_model_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x22, 0x96, 0x47, 0x0a, 0x0d,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x61, 0x0a,
	0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x48, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x12, 0x51, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x6c, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x2b, 0x0a,
	0x11, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x63, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x6a, 0x0a, 0x13, 0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x75, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x12, 0x66,
	0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x45, 0x0a, 0x0a, 0x69, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x18,
	0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x6e,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x12, 0x50, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x57, 0x0a, 0x0c, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x4b, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x5b, 0x0a, 0x0e, 0x74, 0x6f, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x54, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x0d, 0x74, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0xb4, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x1a, 0x37, 0x0a,
	0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xb6, 0x02, 0x0a, 0x0f, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2f, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4f, 0x0a, 0x0f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x0e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a,
	0xb0, 0x0b, 0x0a, 0x0c, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61,
	0x63, 0x73, 0x73, 0x44, 0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x61, 0x66, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x66, 0x66, 0x69, 0x72,
	0x6d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x66, 0x74,
	0x65, 0x72, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x70, 0x61, 0x79, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x70, 0x61, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x70, 0x61, 0x79, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x61, 0x75, 0x5f, 0x62, 0x65,
	0x63, 0x73, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x42, 0x65, 0x63, 0x73, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x62, 0x61, 0x63, 0x73, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x63, 0x73, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x13,
	0x62, 0x61, 0x6e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x0a,
	0x16, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x62,
	0x61, 0x6e, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x6c, 0x69, 0x6b, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x6c, 0x69, 0x6b,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x6f, 0x6c, 0x65,
	0x74, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6c, 0x65, 0x74, 0x6f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x69, 0x6e,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x49, 0x73, 0x73,
	0x75, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x72,
	0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x19, 0x63, 0x61, 0x72,
	0x74, 0x65, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x63, 0x61, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x63, 0x61,
	0x72, 0x74, 0x65, 0x73, 0x42, 0x61, 0x6e, 0x63, 0x61, 0x69, 0x72, 0x65, 0x73, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x70, 0x73, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x70, 0x73,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x70, 0x78, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x70, 0x78, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x67,
	0x69, 0x72, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x69, 0x72, 0x6f, 0x70, 0x61, 0x79, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x67, 0x72, 0x61, 0x62, 0x70, 0x61,
	0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x67, 0x72, 0x61, 0x62, 0x70, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6a, 0x63, 0x62, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6a, 0x63, 0x62, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6b,
	0x6c, 0x61, 0x72, 0x6e, 0x61, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6b, 0x6c, 0x61, 0x72, 0x6e, 0x61, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x6b, 0x6f, 0x6e, 0x62, 0x69, 0x6e, 0x69, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x6b, 0x6f, 0x6e, 0x62, 0x69, 0x6e, 0x69, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x6f, 0x78, 0x78, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x78, 0x78, 0x6f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x32, 0x34, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x32, 0x34, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6e, 0x6f, 0x77, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x70, 0x61, 0x79, 0x6e, 0x6f, 0x77, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d,
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x70, 0x61, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x70, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a,
	0x13, 0x73, 0x65, 0x70, 0x61, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x70, 0x61,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x6f, 0x66, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x6f, 0x66, 0x6f, 0x72, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x17, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x73, 0x5f, 0x31, 0x30, 0x39, 0x39, 0x5f,
	0x6b, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x61, 0x78, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x31, 0x30, 0x39, 0x39, 0x4b, 0x12, 0x3a, 0x0a, 0x1a,
	0x74, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x73,
	0x5f, 0x31, 0x30, 0x39, 0x39, 0x5f, 0x6d, 0x69, 0x73, 0x63, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x16, 0x74, 0x61, 0x78, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x73,
	0x31, 0x30, 0x39, 0x39, 0x4d, 0x69, 0x73, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x79, 0x12, 0x3e, 0x0a, 0x1c, 0x75, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x75, 0x73, 0x42, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x68, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x1a, 0xce, 0x06, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x40,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x55, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x61, 0x6e, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x61, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x61, 0x12, 0x58, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61,
	0x6e, 0x6a, 0x69, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x6a,
	0x69, 0x12, 0x2d, 0x0a, 0x12, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64,
	0x12, 0x2f, 0x0a, 0x13, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x61,
	0x6e, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x61, 0x6d, 0x65, 0x4b, 0x61,
	0x6e, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x4b, 0x61, 0x6e, 0x6a,
	0x69, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x70, 0x0a, 0x15, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x74, 0x61, 0x78, 0x49, 0x64,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x74, 0x61, 0x78, 0x49, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x76, 0x61, 0x74,
	0x49, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x57, 0x0a, 0x0c, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0xb2, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b,
	0x61, 0x6e, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x1a, 0xb3, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x6a, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69,
	0x6e, 0x65, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f,
	0x77, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x1a, 0x59,
	0x0a, 0x14, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0xdb, 0x01, 0x0a, 0x0c, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x08, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x71, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x1a, 0x45, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x94,
	0x05, 0x0a, 0x12, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x69, 0x0a, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x61, 0x64,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x44, 0x75, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x06, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x52, 0x06, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c,
	0x79, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x44, 0x75, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61,
	0x73, 0x74, 0x44, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x13, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x73, 0x0a, 0x0b, 0x41, 0x6c, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x64, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44, 0x75, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x5f, 0x64, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44, 0x75, 0x65, 0x1a, 0x56, 0x0a,
	0x06, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x82, 0x05, 0x0a, 0x0c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x0c, 0x61,
	0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x44, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x44, 0x75, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x70, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x73, 0x0a,
	0x0b, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x34, 0x0a, 0x16,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x61, 0x6c,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44,
	0x75, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44,
	0x75, 0x65, 0x1a, 0x56, 0x0a, 0x06, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x96, 0x15, 0x0a, 0x08, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x71, 0x0a, 0x13, 0x62, 0x61, 0x63, 0x73, 0x5f,
	0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x63, 0x73, 0x44, 0x65, 0x62, 0x69, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x11, 0x62, 0x61, 0x63, 0x73, 0x44, 0x65, 0x62,
	0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x54, 0x0a, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x5e, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x73, 0x73, 0x75,
	0x69, 0x6e, 0x67, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x69, 0x6e, 0x67,
	0x12, 0x61, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x54, 0x0a, 0x08,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x51, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x73, 0x12, 0x71, 0x0a, 0x13, 0x73, 0x65, 0x70, 0x61, 0x5f, 0x64, 0x65,
	0x62, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x44, 0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x11, 0x73, 0x65, 0x70, 0x61, 0x44, 0x65, 0x62, 0x69, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x54, 0x0a, 0x08, 0x74, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x54, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x79, 0x52, 0x08, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x79, 0x1a, 0x36,
	0x0a, 0x11, 0x42, 0x61, 0x63, 0x73, 0x44, 0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x80, 0x01, 0x0a, 0x08, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0xd3, 0x01, 0x0a, 0x0b, 0x43, 0x61,
	0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x69, 0x6e, 0x67, 0x12, 0x70, 0x0a, 0x0e, 0x74, 0x6f, 0x73,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0d, 0x74, 0x6f,
	0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x52, 0x0a, 0x0d, 0x54,
	0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x1a,
	0x98, 0x03, 0x0a, 0x0c, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x65, 0x0a, 0x0a, 0x64, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x6e, 0x52, 0x09, 0x64, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x6e, 0x12, 0x3e, 0x0a, 0x1b, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x47, 0x0a, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x5f, 0x6b, 0x61, 0x6e, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4b, 0x61, 0x6e, 0x61,
	0x12, 0x49, 0x0a, 0x21, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x5f,
	0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4b, 0x61, 0x6e, 0x6a, 0x69, 0x1a, 0x4d, 0x0a, 0x09, 0x44,
	0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x73, 0x5f,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61,
	0x76, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x76, 0x63,
	0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x63, 0x76, 0x63, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x1a, 0x4a, 0x0a, 0x09, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x1a, 0xcb, 0x02, 0x0a, 0x08, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x19, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f, 0x6b,
	0x61, 0x6e, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x4b, 0x61,
	0x6e, 0x61, 0x12, 0x3c, 0x0a, 0x1a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x4b, 0x61, 0x6e, 0x6a, 0x69,
	0x12, 0x47, 0x0a, 0x20, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x5f,
	0x6b, 0x61, 0x6e, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x50,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x4b, 0x61, 0x6e, 0x61, 0x12, 0x49, 0x0a, 0x21, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4b,
	0x61, 0x6e, 0x6a, 0x69, 0x1a, 0xe6, 0x02, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73,
	0x12, 0x36, 0x0a, 0x17, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x15, 0x64, 0x65, 0x62, 0x69, 0x74, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x1a, 0x91, 0x01, 0x0a, 0x08, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f,
	0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x61,
	0x79, 0x44, 0x61, 0x79, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x61, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x65, 0x65, 0x6b,
	0x6c, 0x79, 0x5f, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x1a, 0x34, 0x0a,
	0x11, 0x53, 0x65, 0x70, 0x61, 0x44, 0x65, 0x62, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x1a, 0xcd, 0x01, 0x0a, 0x08, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x79,
	0x12, 0x6d, 0x0a, 0x0e, 0x74, 0x6f, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x79, 0x2e, 0x54, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x0d, 0x74, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x1a,
	0x52, 0x0a, 0x0d, 0x54, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x1a, 0x7f, 0x0a, 0x0d, 0x54, 0x6f, 0x73, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x22, 0xa0, 0x01, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6e, 0x65, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xf7, 0x1e, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x54, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x6b, 0x61, 0x6e, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x61, 0x52, 0x0b, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x61, 0x12, 0x57, 0x0a, 0x0d, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4b, 0x61, 0x6e, 0x6a, 0x69, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61,
	0x6e, 0x6a, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x62, 0x52,
	0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x61, 0x6e, 0x61, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4b, 0x61, 0x6e,
	0x61, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4b, 0x61, 0x6e, 0x6a, 0x69, 0x12, 0x2a, 0x0a, 0x11, 0x66,
	0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x13, 0x66, 0x75, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x46, 0x75, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x12,
	0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x64,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x1c, 0x69, 0x64, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19,
	0x69, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x61, 0x6e, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4b, 0x61, 0x6e, 0x61, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x61, 0x6e, 0x6a, 0x69, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4b,
	0x61, 0x6e, 0x6a, 0x69, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x69, 0x64, 0x65,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x55, 0x0a, 0x12, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x11, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x56, 0x0a, 0x0c,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x12, 0x56, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x13,
	0x73, 0x73, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x34, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x73, 0x6e, 0x4c, 0x61,
	0x73, 0x74, 0x34, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x64, 0x12, 0x56, 0x0a, 0x0c, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0xb2, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4b, 0x61, 0x6e, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c,
	0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x6f, 0x77, 0x6e, 0x1a, 0xb3, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x4b, 0x61, 0x6e, 0x6a, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6e, 0x65, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x6f, 0x77, 0x6e, 0x1a, 0x41, 0x0a, 0x03, 0x44,
	0x6f, 0x62, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x64, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x1a, 0xbe,
	0x04, 0x0a, 0x12, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x68, 0x0a, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x2e, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c,
	0x79, 0x44, 0x75, 0x65, 0x12, 0x57, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x46, 0x75, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c,
	0x79, 0x44, 0x75, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x75, 0x65,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x12,
	0x31, 0x0a, 0x14, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x70,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x73, 0x0a, 0x0b, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x14, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x44, 0x75, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x44, 0x75, 0x65, 0x1a, 0x56, 0x0a, 0x06, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a,
	0xc9, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x12, 0x2b, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x26, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x1a, 0xac, 0x04, 0x0a, 0x0c,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x62, 0x0a, 0x0c,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x5f, 0x64, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x6c, 0x79, 0x44, 0x75, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x44, 0x75, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x70, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x73, 0x0a,
	0x0b, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x34, 0x0a, 0x16,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x61, 0x6c,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44,
	0x75, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x44,
	0x75, 0x65, 0x1a, 0x56, 0x0a, 0x06, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xa4, 0x04, 0x0a, 0x0c, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x76, 0x0a, 0x13, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x57, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x7b, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x63, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x1a, 0x71,
	0x0a, 0x08, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_pay_ops_v1_verification_model_proto_rawDescOnce sync.Once
	file_moego_models_pay_ops_v1_verification_model_proto_rawDescData = file_moego_models_pay_ops_v1_verification_model_proto_rawDesc
)

func file_moego_models_pay_ops_v1_verification_model_proto_rawDescGZIP() []byte {
	file_moego_models_pay_ops_v1_verification_model_proto_rawDescOnce.Do(func() {
		file_moego_models_pay_ops_v1_verification_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_pay_ops_v1_verification_model_proto_rawDescData)
	})
	return file_moego_models_pay_ops_v1_verification_model_proto_rawDescData
}

var file_moego_models_pay_ops_v1_verification_model_proto_msgTypes = make([]protoimpl.MessageInfo, 50)
var file_moego_models_pay_ops_v1_verification_model_proto_goTypes = []interface{}{
	(*StripeAccount)(nil),                                    // 0: moego.models.pay_ops.v1.StripeAccount
	(*StripeAddress)(nil),                                    // 1: moego.models.pay_ops.v1.StripeAddress
	(*StripePerson)(nil),                                     // 2: moego.models.pay_ops.v1.StripePerson
	nil,                                                      // 3: moego.models.pay_ops.v1.StripeAccount.MetadataEntry
	(*StripeAccount_ExternalAccount)(nil),                    // 4: moego.models.pay_ops.v1.StripeAccount.ExternalAccount
	(*StripeAccount_BusinessProfile)(nil),                    // 5: moego.models.pay_ops.v1.StripeAccount.BusinessProfile
	(*StripeAccount_Capabilities)(nil),                       // 6: moego.models.pay_ops.v1.StripeAccount.Capabilities
	(*StripeAccount_Company)(nil),                            // 7: moego.models.pay_ops.v1.StripeAccount.Company
	(*StripeAccount_AddressKana)(nil),                        // 8: moego.models.pay_ops.v1.StripeAccount.AddressKana
	(*StripeAccount_AddressKanji)(nil),                       // 9: moego.models.pay_ops.v1.StripeAccount.AddressKanji
	(*StripeAccount_OwnershipDeclaration)(nil),               // 10: moego.models.pay_ops.v1.StripeAccount.OwnershipDeclaration
	(*StripeAccount_Verification)(nil),                       // 11: moego.models.pay_ops.v1.StripeAccount.Verification
	(*StripeAccount_Controller)(nil),                         // 12: moego.models.pay_ops.v1.StripeAccount.Controller
	(*StripeAccount_FutureRequirements)(nil),                 // 13: moego.models.pay_ops.v1.StripeAccount.FutureRequirements
	(*StripeAccount_Requirements)(nil),                       // 14: moego.models.pay_ops.v1.StripeAccount.Requirements
	(*StripeAccount_Settings)(nil),                           // 15: moego.models.pay_ops.v1.StripeAccount.Settings
	(*StripeAccount_TosAcceptance)(nil),                      // 16: moego.models.pay_ops.v1.StripeAccount.TosAcceptance
	nil,                                                      // 17: moego.models.pay_ops.v1.StripeAccount.ExternalAccount.DataEntry
	(*StripeAccount_Verification_Document)(nil),              // 18: moego.models.pay_ops.v1.StripeAccount.Verification.Document
	(*StripeAccount_FutureRequirements_Alternative)(nil),     // 19: moego.models.pay_ops.v1.StripeAccount.FutureRequirements.Alternative
	(*StripeAccount_FutureRequirements_Errors)(nil),          // 20: moego.models.pay_ops.v1.StripeAccount.FutureRequirements.Errors
	(*StripeAccount_Requirements_Alternative)(nil),           // 21: moego.models.pay_ops.v1.StripeAccount.Requirements.Alternative
	(*StripeAccount_Requirements_Errors)(nil),                // 22: moego.models.pay_ops.v1.StripeAccount.Requirements.Errors
	(*StripeAccount_Settings_BacsDebitPayments)(nil),         // 23: moego.models.pay_ops.v1.StripeAccount.Settings.BacsDebitPayments
	(*StripeAccount_Settings_Branding)(nil),                  // 24: moego.models.pay_ops.v1.StripeAccount.Settings.Branding
	(*StripeAccount_Settings_CardIssuing)(nil),               // 25: moego.models.pay_ops.v1.StripeAccount.Settings.CardIssuing
	(*StripeAccount_Settings_CardPayments)(nil),              // 26: moego.models.pay_ops.v1.StripeAccount.Settings.CardPayments
	(*StripeAccount_Settings_Dashboard)(nil),                 // 27: moego.models.pay_ops.v1.StripeAccount.Settings.Dashboard
	(*StripeAccount_Settings_Payments)(nil),                  // 28: moego.models.pay_ops.v1.StripeAccount.Settings.Payments
	(*StripeAccount_Settings_Payouts)(nil),                   // 29: moego.models.pay_ops.v1.StripeAccount.Settings.Payouts
	(*StripeAccount_Settings_SepaDebitPayments)(nil),         // 30: moego.models.pay_ops.v1.StripeAccount.Settings.SepaDebitPayments
	(*StripeAccount_Settings_Treasury)(nil),                  // 31: moego.models.pay_ops.v1.StripeAccount.Settings.Treasury
	(*StripeAccount_Settings_CardIssuing_TosAcceptance)(nil), // 32: moego.models.pay_ops.v1.StripeAccount.Settings.CardIssuing.TosAcceptance
	(*StripeAccount_Settings_CardPayments_DeclineOn)(nil),    // 33: moego.models.pay_ops.v1.StripeAccount.Settings.CardPayments.DeclineOn
	(*StripeAccount_Settings_Payouts_Schedule)(nil),          // 34: moego.models.pay_ops.v1.StripeAccount.Settings.Payouts.Schedule
	(*StripeAccount_Settings_Treasury_TosAcceptance)(nil),    // 35: moego.models.pay_ops.v1.StripeAccount.Settings.Treasury.TosAcceptance
	nil,                                                  // 36: moego.models.pay_ops.v1.StripePerson.MetadataEntry
	(*StripePerson_AddressKana)(nil),                     // 37: moego.models.pay_ops.v1.StripePerson.AddressKana
	(*StripePerson_AddressKanji)(nil),                    // 38: moego.models.pay_ops.v1.StripePerson.AddressKanji
	(*StripePerson_Dob)(nil),                             // 39: moego.models.pay_ops.v1.StripePerson.Dob
	(*StripePerson_FutureRequirements)(nil),              // 40: moego.models.pay_ops.v1.StripePerson.FutureRequirements
	(*StripePerson_Relationship)(nil),                    // 41: moego.models.pay_ops.v1.StripePerson.Relationship
	(*StripePerson_Requirements)(nil),                    // 42: moego.models.pay_ops.v1.StripePerson.Requirements
	(*StripePerson_Verification)(nil),                    // 43: moego.models.pay_ops.v1.StripePerson.Verification
	(*StripePerson_FutureRequirements_Alternative)(nil),  // 44: moego.models.pay_ops.v1.StripePerson.FutureRequirements.Alternative
	(*StripePerson_FutureRequirements_Errors)(nil),       // 45: moego.models.pay_ops.v1.StripePerson.FutureRequirements.Errors
	(*StripePerson_Requirements_Alternative)(nil),        // 46: moego.models.pay_ops.v1.StripePerson.Requirements.Alternative
	(*StripePerson_Requirements_Errors)(nil),             // 47: moego.models.pay_ops.v1.StripePerson.Requirements.Errors
	(*StripePerson_Verification_AdditionalDocument)(nil), // 48: moego.models.pay_ops.v1.StripePerson.Verification.AdditionalDocument
	(*StripePerson_Verification_Document)(nil),           // 49: moego.models.pay_ops.v1.StripePerson.Verification.Document
}
var file_moego_models_pay_ops_v1_verification_model_proto_depIdxs = []int32{
	5,  // 0: moego.models.pay_ops.v1.StripeAccount.business_profile:type_name -> moego.models.pay_ops.v1.StripeAccount.BusinessProfile
	6,  // 1: moego.models.pay_ops.v1.StripeAccount.capabilities:type_name -> moego.models.pay_ops.v1.StripeAccount.Capabilities
	7,  // 2: moego.models.pay_ops.v1.StripeAccount.company:type_name -> moego.models.pay_ops.v1.StripeAccount.Company
	12, // 3: moego.models.pay_ops.v1.StripeAccount.controller:type_name -> moego.models.pay_ops.v1.StripeAccount.Controller
	4,  // 4: moego.models.pay_ops.v1.StripeAccount.external_accounts:type_name -> moego.models.pay_ops.v1.StripeAccount.ExternalAccount
	13, // 5: moego.models.pay_ops.v1.StripeAccount.future_requirements:type_name -> moego.models.pay_ops.v1.StripeAccount.FutureRequirements
	2,  // 6: moego.models.pay_ops.v1.StripeAccount.individual:type_name -> moego.models.pay_ops.v1.StripePerson
	3,  // 7: moego.models.pay_ops.v1.StripeAccount.metadata:type_name -> moego.models.pay_ops.v1.StripeAccount.MetadataEntry
	14, // 8: moego.models.pay_ops.v1.StripeAccount.requirements:type_name -> moego.models.pay_ops.v1.StripeAccount.Requirements
	15, // 9: moego.models.pay_ops.v1.StripeAccount.settings:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings
	16, // 10: moego.models.pay_ops.v1.StripeAccount.tos_acceptance:type_name -> moego.models.pay_ops.v1.StripeAccount.TosAcceptance
	1,  // 11: moego.models.pay_ops.v1.StripePerson.address:type_name -> moego.models.pay_ops.v1.StripeAddress
	37, // 12: moego.models.pay_ops.v1.StripePerson.address_kana:type_name -> moego.models.pay_ops.v1.StripePerson.AddressKana
	38, // 13: moego.models.pay_ops.v1.StripePerson.address_kanji:type_name -> moego.models.pay_ops.v1.StripePerson.AddressKanji
	39, // 14: moego.models.pay_ops.v1.StripePerson.dob:type_name -> moego.models.pay_ops.v1.StripePerson.Dob
	40, // 15: moego.models.pay_ops.v1.StripePerson.future_requirements:type_name -> moego.models.pay_ops.v1.StripePerson.FutureRequirements
	36, // 16: moego.models.pay_ops.v1.StripePerson.metadata:type_name -> moego.models.pay_ops.v1.StripePerson.MetadataEntry
	1,  // 17: moego.models.pay_ops.v1.StripePerson.registered_address:type_name -> moego.models.pay_ops.v1.StripeAddress
	41, // 18: moego.models.pay_ops.v1.StripePerson.relationship:type_name -> moego.models.pay_ops.v1.StripePerson.Relationship
	42, // 19: moego.models.pay_ops.v1.StripePerson.requirements:type_name -> moego.models.pay_ops.v1.StripePerson.Requirements
	43, // 20: moego.models.pay_ops.v1.StripePerson.verification:type_name -> moego.models.pay_ops.v1.StripePerson.Verification
	17, // 21: moego.models.pay_ops.v1.StripeAccount.ExternalAccount.data:type_name -> moego.models.pay_ops.v1.StripeAccount.ExternalAccount.DataEntry
	1,  // 22: moego.models.pay_ops.v1.StripeAccount.BusinessProfile.support_address:type_name -> moego.models.pay_ops.v1.StripeAddress
	1,  // 23: moego.models.pay_ops.v1.StripeAccount.Company.address:type_name -> moego.models.pay_ops.v1.StripeAddress
	8,  // 24: moego.models.pay_ops.v1.StripeAccount.Company.address_kana:type_name -> moego.models.pay_ops.v1.StripeAccount.AddressKana
	9,  // 25: moego.models.pay_ops.v1.StripeAccount.Company.address_kanji:type_name -> moego.models.pay_ops.v1.StripeAccount.AddressKanji
	10, // 26: moego.models.pay_ops.v1.StripeAccount.Company.ownership_declaration:type_name -> moego.models.pay_ops.v1.StripeAccount.OwnershipDeclaration
	11, // 27: moego.models.pay_ops.v1.StripeAccount.Company.verification:type_name -> moego.models.pay_ops.v1.StripeAccount.Verification
	18, // 28: moego.models.pay_ops.v1.StripeAccount.Verification.document:type_name -> moego.models.pay_ops.v1.StripeAccount.Verification.Document
	19, // 29: moego.models.pay_ops.v1.StripeAccount.FutureRequirements.alternatives:type_name -> moego.models.pay_ops.v1.StripeAccount.FutureRequirements.Alternative
	20, // 30: moego.models.pay_ops.v1.StripeAccount.FutureRequirements.errors:type_name -> moego.models.pay_ops.v1.StripeAccount.FutureRequirements.Errors
	21, // 31: moego.models.pay_ops.v1.StripeAccount.Requirements.alternatives:type_name -> moego.models.pay_ops.v1.StripeAccount.Requirements.Alternative
	22, // 32: moego.models.pay_ops.v1.StripeAccount.Requirements.errors:type_name -> moego.models.pay_ops.v1.StripeAccount.Requirements.Errors
	23, // 33: moego.models.pay_ops.v1.StripeAccount.Settings.bacs_debit_payments:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.BacsDebitPayments
	24, // 34: moego.models.pay_ops.v1.StripeAccount.Settings.branding:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Branding
	25, // 35: moego.models.pay_ops.v1.StripeAccount.Settings.card_issuing:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.CardIssuing
	26, // 36: moego.models.pay_ops.v1.StripeAccount.Settings.card_payments:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.CardPayments
	27, // 37: moego.models.pay_ops.v1.StripeAccount.Settings.dashboard:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Dashboard
	28, // 38: moego.models.pay_ops.v1.StripeAccount.Settings.payments:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Payments
	29, // 39: moego.models.pay_ops.v1.StripeAccount.Settings.payouts:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Payouts
	30, // 40: moego.models.pay_ops.v1.StripeAccount.Settings.sepa_debit_payments:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.SepaDebitPayments
	31, // 41: moego.models.pay_ops.v1.StripeAccount.Settings.treasury:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Treasury
	32, // 42: moego.models.pay_ops.v1.StripeAccount.Settings.CardIssuing.tos_acceptance:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.CardIssuing.TosAcceptance
	33, // 43: moego.models.pay_ops.v1.StripeAccount.Settings.CardPayments.decline_on:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.CardPayments.DeclineOn
	34, // 44: moego.models.pay_ops.v1.StripeAccount.Settings.Payouts.schedule:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Payouts.Schedule
	35, // 45: moego.models.pay_ops.v1.StripeAccount.Settings.Treasury.tos_acceptance:type_name -> moego.models.pay_ops.v1.StripeAccount.Settings.Treasury.TosAcceptance
	44, // 46: moego.models.pay_ops.v1.StripePerson.FutureRequirements.alternatives:type_name -> moego.models.pay_ops.v1.StripePerson.FutureRequirements.Alternative
	45, // 47: moego.models.pay_ops.v1.StripePerson.FutureRequirements.errors:type_name -> moego.models.pay_ops.v1.StripePerson.FutureRequirements.Errors
	46, // 48: moego.models.pay_ops.v1.StripePerson.Requirements.alternatives:type_name -> moego.models.pay_ops.v1.StripePerson.Requirements.Alternative
	47, // 49: moego.models.pay_ops.v1.StripePerson.Requirements.errors:type_name -> moego.models.pay_ops.v1.StripePerson.Requirements.Errors
	48, // 50: moego.models.pay_ops.v1.StripePerson.Verification.additional_document:type_name -> moego.models.pay_ops.v1.StripePerson.Verification.AdditionalDocument
	49, // 51: moego.models.pay_ops.v1.StripePerson.Verification.document:type_name -> moego.models.pay_ops.v1.StripePerson.Verification.Document
	52, // [52:52] is the sub-list for method output_type
	52, // [52:52] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_moego_models_pay_ops_v1_verification_model_proto_init() }
func file_moego_models_pay_ops_v1_verification_model_proto_init() {
	if File_moego_models_pay_ops_v1_verification_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_ExternalAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_BusinessProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Capabilities); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Company); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_AddressKana); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_AddressKanji); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_OwnershipDeclaration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Verification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Controller); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_FutureRequirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Requirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_TosAcceptance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Verification_Document); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_FutureRequirements_Alternative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_FutureRequirements_Errors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Requirements_Alternative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Requirements_Errors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_BacsDebitPayments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Branding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_CardIssuing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_CardPayments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Dashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Payments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Payouts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_SepaDebitPayments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Treasury); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_CardIssuing_TosAcceptance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_CardPayments_DeclineOn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Payouts_Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeAccount_Settings_Treasury_TosAcceptance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_AddressKana); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_AddressKanji); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Dob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_FutureRequirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Relationship); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Requirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Verification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_FutureRequirements_Alternative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_FutureRequirements_Errors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Requirements_Alternative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Requirements_Errors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Verification_AdditionalDocument); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_verification_model_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripePerson_Verification_Document); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_pay_ops_v1_verification_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   50,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_pay_ops_v1_verification_model_proto_goTypes,
		DependencyIndexes: file_moego_models_pay_ops_v1_verification_model_proto_depIdxs,
		MessageInfos:      file_moego_models_pay_ops_v1_verification_model_proto_msgTypes,
	}.Build()
	File_moego_models_pay_ops_v1_verification_model_proto = out.File
	file_moego_models_pay_ops_v1_verification_model_proto_rawDesc = nil
	file_moego_models_pay_ops_v1_verification_model_proto_goTypes = nil
	file_moego_models_pay_ops_v1_verification_model_proto_depIdxs = nil
}
