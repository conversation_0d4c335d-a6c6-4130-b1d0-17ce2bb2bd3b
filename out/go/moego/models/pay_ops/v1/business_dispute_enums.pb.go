// @since 2-23-12-14
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/pay_ops/v1/business_dispute_enums.proto

package payopspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// dispute upload documents type
type DisputeUploadDocumentType int32

const (
	// unspecified
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_UNSPECIFIED DisputeUploadDocumentType = 0
	// choose category
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_CHOOSE_CATEGORY DisputeUploadDocumentType = 1
	// customer communication
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_COMMUNICATION DisputeUploadDocumentType = 2
	// customer signature
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_SIGNATURE DisputeUploadDocumentType = 3
	// duplicate charge documentation
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_DUPLICATE_CHARGE_DOCUMENTATION DisputeUploadDocumentType = 4
	// receipt
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_RECEIPT DisputeUploadDocumentType = 5
	// refund and cancellation policy
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_REFUND_AND_CANCELLATION_POLICY DisputeUploadDocumentType = 6
	// service documentation
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_SERVICE_DOCUMENTATION DisputeUploadDocumentType = 7
	// shipping documentation
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_SHIPPING_DOCUMENTATION DisputeUploadDocumentType = 8
	// other
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_OTHER DisputeUploadDocumentType = 9
	// credit voucher
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_CREDIT_VOUCHER DisputeUploadDocumentType = 10
	// goverment order
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_GOVERMENT_ORDER DisputeUploadDocumentType = 11
	// Terms disclosure
	DisputeUploadDocumentType_DISPUTE_UPLOAD_DOCUMENT_TYPE_TERMS_DISCLOSURE DisputeUploadDocumentType = 12
)

// Enum value maps for DisputeUploadDocumentType.
var (
	DisputeUploadDocumentType_name = map[int32]string{
		0:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_UNSPECIFIED",
		1:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_CHOOSE_CATEGORY",
		2:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_COMMUNICATION",
		3:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_SIGNATURE",
		4:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_DUPLICATE_CHARGE_DOCUMENTATION",
		5:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_RECEIPT",
		6:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_REFUND_AND_CANCELLATION_POLICY",
		7:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_SERVICE_DOCUMENTATION",
		8:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_SHIPPING_DOCUMENTATION",
		9:  "DISPUTE_UPLOAD_DOCUMENT_TYPE_OTHER",
		10: "DISPUTE_UPLOAD_DOCUMENT_TYPE_CREDIT_VOUCHER",
		11: "DISPUTE_UPLOAD_DOCUMENT_TYPE_GOVERMENT_ORDER",
		12: "DISPUTE_UPLOAD_DOCUMENT_TYPE_TERMS_DISCLOSURE",
	}
	DisputeUploadDocumentType_value = map[string]int32{
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_UNSPECIFIED":                    0,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_CHOOSE_CATEGORY":                1,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_COMMUNICATION":         2,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_CUSTOMER_SIGNATURE":             3,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_DUPLICATE_CHARGE_DOCUMENTATION": 4,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_RECEIPT":                        5,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_REFUND_AND_CANCELLATION_POLICY": 6,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_SERVICE_DOCUMENTATION":          7,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_SHIPPING_DOCUMENTATION":         8,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_OTHER":                          9,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_CREDIT_VOUCHER":                 10,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_GOVERMENT_ORDER":                11,
		"DISPUTE_UPLOAD_DOCUMENT_TYPE_TERMS_DISCLOSURE":               12,
	}
)

func (x DisputeUploadDocumentType) Enum() *DisputeUploadDocumentType {
	p := new(DisputeUploadDocumentType)
	*p = x
	return p
}

func (x DisputeUploadDocumentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeUploadDocumentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[0].Descriptor()
}

func (DisputeUploadDocumentType) Type() protoreflect.EnumType {
	return &file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[0]
}

func (x DisputeUploadDocumentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeUploadDocumentType.Descriptor instead.
func (DisputeUploadDocumentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescGZIP(), []int{0}
}

// dispute reason type
type DisputeReasonType int32

const (
	// unspecified
	DisputeReasonType_DISPUTE_REASON_TYPE_UNSPECIFIED DisputeReasonType = 0
	// bank cannot process
	DisputeReasonType_DISPUTE_REASON_TYPE_BANK_CANNOT_PROCESS DisputeReasonType = 1
	// check returned
	DisputeReasonType_DISPUTE_REASON_TYPE_CHECK_RETURNED DisputeReasonType = 2
	// credit not processed
	DisputeReasonType_DISPUTE_REASON_TYPE_CREDIT_NOT_PROCESSED DisputeReasonType = 3
	// customer initiated
	DisputeReasonType_DISPUTE_REASON_TYPE_CUSTOMER_INITIATED DisputeReasonType = 4
	// debit not authorized
	DisputeReasonType_DISPUTE_REASON_TYPE_DEBIT_NOT_AUTHORIZED DisputeReasonType = 5
	// duplicate
	DisputeReasonType_DISPUTE_REASON_TYPE_DUPLICATE DisputeReasonType = 6
	// fraudulent
	DisputeReasonType_DISPUTE_REASON_TYPE_FRAUDULENT DisputeReasonType = 7
	// general
	DisputeReasonType_DISPUTE_REASON_TYPE_GENERAL DisputeReasonType = 8
	// incorrect account details
	DisputeReasonType_DISPUTE_REASON_TYPE_INCORRECT_ACCOUNT_DETAILS DisputeReasonType = 9
	// insufficient funds
	DisputeReasonType_DISPUTE_REASON_TYPE_INSUFFICIENT_FUNDS DisputeReasonType = 10
	// product not received
	DisputeReasonType_DISPUTE_REASON_TYPE_PRODUCT_NOT_RECEIVED DisputeReasonType = 11
	// product unacceptable
	DisputeReasonType_DISPUTE_REASON_TYPE_PRODUCT_UNACCEPTABLE DisputeReasonType = 12
	// subscription canceled
	DisputeReasonType_DISPUTE_REASON_TYPE_SUBSCRIPTION_CANCELED DisputeReasonType = 13
	// unrecognized
	DisputeReasonType_DISPUTE_REASON_TYPE_UNRECOGNIZED DisputeReasonType = 14
)

// Enum value maps for DisputeReasonType.
var (
	DisputeReasonType_name = map[int32]string{
		0:  "DISPUTE_REASON_TYPE_UNSPECIFIED",
		1:  "DISPUTE_REASON_TYPE_BANK_CANNOT_PROCESS",
		2:  "DISPUTE_REASON_TYPE_CHECK_RETURNED",
		3:  "DISPUTE_REASON_TYPE_CREDIT_NOT_PROCESSED",
		4:  "DISPUTE_REASON_TYPE_CUSTOMER_INITIATED",
		5:  "DISPUTE_REASON_TYPE_DEBIT_NOT_AUTHORIZED",
		6:  "DISPUTE_REASON_TYPE_DUPLICATE",
		7:  "DISPUTE_REASON_TYPE_FRAUDULENT",
		8:  "DISPUTE_REASON_TYPE_GENERAL",
		9:  "DISPUTE_REASON_TYPE_INCORRECT_ACCOUNT_DETAILS",
		10: "DISPUTE_REASON_TYPE_INSUFFICIENT_FUNDS",
		11: "DISPUTE_REASON_TYPE_PRODUCT_NOT_RECEIVED",
		12: "DISPUTE_REASON_TYPE_PRODUCT_UNACCEPTABLE",
		13: "DISPUTE_REASON_TYPE_SUBSCRIPTION_CANCELED",
		14: "DISPUTE_REASON_TYPE_UNRECOGNIZED",
	}
	DisputeReasonType_value = map[string]int32{
		"DISPUTE_REASON_TYPE_UNSPECIFIED":               0,
		"DISPUTE_REASON_TYPE_BANK_CANNOT_PROCESS":       1,
		"DISPUTE_REASON_TYPE_CHECK_RETURNED":            2,
		"DISPUTE_REASON_TYPE_CREDIT_NOT_PROCESSED":      3,
		"DISPUTE_REASON_TYPE_CUSTOMER_INITIATED":        4,
		"DISPUTE_REASON_TYPE_DEBIT_NOT_AUTHORIZED":      5,
		"DISPUTE_REASON_TYPE_DUPLICATE":                 6,
		"DISPUTE_REASON_TYPE_FRAUDULENT":                7,
		"DISPUTE_REASON_TYPE_GENERAL":                   8,
		"DISPUTE_REASON_TYPE_INCORRECT_ACCOUNT_DETAILS": 9,
		"DISPUTE_REASON_TYPE_INSUFFICIENT_FUNDS":        10,
		"DISPUTE_REASON_TYPE_PRODUCT_NOT_RECEIVED":      11,
		"DISPUTE_REASON_TYPE_PRODUCT_UNACCEPTABLE":      12,
		"DISPUTE_REASON_TYPE_SUBSCRIPTION_CANCELED":     13,
		"DISPUTE_REASON_TYPE_UNRECOGNIZED":              14,
	}
)

func (x DisputeReasonType) Enum() *DisputeReasonType {
	p := new(DisputeReasonType)
	*p = x
	return p
}

func (x DisputeReasonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeReasonType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[1].Descriptor()
}

func (DisputeReasonType) Type() protoreflect.EnumType {
	return &file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[1]
}

func (x DisputeReasonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeReasonType.Descriptor instead.
func (DisputeReasonType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescGZIP(), []int{1}
}

// dispute status type
type DisputeStatusType int32

const (
	// unspecified
	DisputeStatusType_DISPUTE_STATUS_TYPE_UNSPECIFIED DisputeStatusType = 0
	// warning needs response
	DisputeStatusType_DISPUTE_STATUS_TYPE_WARNING_NEEDS_RESPONSE DisputeStatusType = 1
	// warning under review
	DisputeStatusType_DISPUTE_STATUS_TYPE_WARNING_UNDER_REVIEW DisputeStatusType = 2
	// warning closed
	DisputeStatusType_DISPUTE_STATUS_TYPE_WARNING_CLOSED DisputeStatusType = 3
	// needs response
	DisputeStatusType_DISPUTE_STATUS_TYPE_NEEDS_RESPONSE DisputeStatusType = 4
	// under review
	DisputeStatusType_DISPUTE_STATUS_TYPE_UNDER_REVIEW DisputeStatusType = 5
	// won
	DisputeStatusType_DISPUTE_STATUS_TYPE_WON DisputeStatusType = 6
	// lost
	DisputeStatusType_DISPUTE_STATUS_TYPE_LOST DisputeStatusType = 7
)

// Enum value maps for DisputeStatusType.
var (
	DisputeStatusType_name = map[int32]string{
		0: "DISPUTE_STATUS_TYPE_UNSPECIFIED",
		1: "DISPUTE_STATUS_TYPE_WARNING_NEEDS_RESPONSE",
		2: "DISPUTE_STATUS_TYPE_WARNING_UNDER_REVIEW",
		3: "DISPUTE_STATUS_TYPE_WARNING_CLOSED",
		4: "DISPUTE_STATUS_TYPE_NEEDS_RESPONSE",
		5: "DISPUTE_STATUS_TYPE_UNDER_REVIEW",
		6: "DISPUTE_STATUS_TYPE_WON",
		7: "DISPUTE_STATUS_TYPE_LOST",
	}
	DisputeStatusType_value = map[string]int32{
		"DISPUTE_STATUS_TYPE_UNSPECIFIED":            0,
		"DISPUTE_STATUS_TYPE_WARNING_NEEDS_RESPONSE": 1,
		"DISPUTE_STATUS_TYPE_WARNING_UNDER_REVIEW":   2,
		"DISPUTE_STATUS_TYPE_WARNING_CLOSED":         3,
		"DISPUTE_STATUS_TYPE_NEEDS_RESPONSE":         4,
		"DISPUTE_STATUS_TYPE_UNDER_REVIEW":           5,
		"DISPUTE_STATUS_TYPE_WON":                    6,
		"DISPUTE_STATUS_TYPE_LOST":                   7,
	}
)

func (x DisputeStatusType) Enum() *DisputeStatusType {
	p := new(DisputeStatusType)
	*p = x
	return p
}

func (x DisputeStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[2].Descriptor()
}

func (DisputeStatusType) Type() protoreflect.EnumType {
	return &file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes[2]
}

func (x DisputeStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeStatusType.Descriptor instead.
func (DisputeStatusType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_pay_ops_v1_business_dispute_enums_proto protoreflect.FileDescriptor

var file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2a,
	0xc4, 0x05, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a,
	0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f,
	0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x4f, 0x4f,
	0x53, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x01, 0x12, 0x37, 0x0a,
	0x33, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f,
	0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x33, 0x0a, 0x2f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f,
	0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x3f, 0x0a, 0x3b, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x55, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x44, 0x4f, 0x43,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44,
	0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x43,
	0x45, 0x49, 0x50, 0x54, 0x10, 0x05, 0x12, 0x3f, 0x0a, 0x3b, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x06, 0x12, 0x36, 0x0a, 0x32, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12,
	0x37, 0x0a, 0x33, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x49, 0x53, 0x50,
	0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x09,
	0x12, 0x2f, 0x0a, 0x2b, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x56, 0x4f, 0x55, 0x43, 0x48, 0x45, 0x52, 0x10,
	0x0a, 0x12, 0x30, 0x0a, 0x2c, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x47, 0x4f, 0x56, 0x45, 0x52, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x10, 0x0b, 0x12, 0x31, 0x0a, 0x2d, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x55,
	0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4c, 0x4f,
	0x53, 0x55, 0x52, 0x45, 0x10, 0x0c, 0x2a, 0x8d, 0x05, 0x0a, 0x11, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x2b, 0x0a, 0x27, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43, 0x41,
	0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x26,
	0x0a, 0x22, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x52, 0x45, 0x54, 0x55,
	0x52, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x05, 0x12, 0x21,
	0x0a, 0x1d, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10,
	0x06, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x55, 0x4c,
	0x45, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x4c, 0x10, 0x08, 0x12, 0x31, 0x0a, 0x2d, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e,
	0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x2a, 0x0a, 0x26, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x10, 0x0a, 0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45,
	0x44, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x55, 0x4e, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x0c, 0x12, 0x2d, 0x0a, 0x29, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x0d,
	0x12, 0x24, 0x0a, 0x20, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x52, 0x45, 0x43, 0x4f, 0x47, 0x4e,
	0x49, 0x5a, 0x45, 0x44, 0x10, 0x0e, 0x2a, 0xc7, 0x02, 0x0a, 0x11, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47,
	0x5f, 0x4e, 0x45, 0x45, 0x44, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10,
	0x01, 0x12, 0x2c, 0x0a, 0x28, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47,
	0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12,
	0x26, 0x0a, 0x22, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e,
	0x45, 0x45, 0x44, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x04, 0x12,
	0x24, 0x0a, 0x20, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x4f, 0x4e,
	0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x53, 0x54, 0x10, 0x07,
	0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73,
	0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescOnce sync.Once
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescData = file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDesc
)

func file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescGZIP() []byte {
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescData)
	})
	return file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDescData
}

var file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_pay_ops_v1_business_dispute_enums_proto_goTypes = []interface{}{
	(DisputeUploadDocumentType)(0), // 0: moego.models.pay_ops.v1.DisputeUploadDocumentType
	(DisputeReasonType)(0),         // 1: moego.models.pay_ops.v1.DisputeReasonType
	(DisputeStatusType)(0),         // 2: moego.models.pay_ops.v1.DisputeStatusType
}
var file_moego_models_pay_ops_v1_business_dispute_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_pay_ops_v1_business_dispute_enums_proto_init() }
func file_moego_models_pay_ops_v1_business_dispute_enums_proto_init() {
	if File_moego_models_pay_ops_v1_business_dispute_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_pay_ops_v1_business_dispute_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_pay_ops_v1_business_dispute_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_pay_ops_v1_business_dispute_enums_proto_enumTypes,
	}.Build()
	File_moego_models_pay_ops_v1_business_dispute_enums_proto = out.File
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_rawDesc = nil
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_goTypes = nil
	file_moego_models_pay_ops_v1_business_dispute_enums_proto_depIdxs = nil
}
