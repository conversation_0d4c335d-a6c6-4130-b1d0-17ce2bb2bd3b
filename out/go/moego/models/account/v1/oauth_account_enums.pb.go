// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/account/v1/oauth_account_enums.proto

package accountpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// oauth account provider
type OauthAccountProvider int32

const (
	// unspecified
	OauthAccountProvider_OAUTH_ACCOUNT_PROVIDER_UNSPECIFIED OauthAccountProvider = 0
	// google
	OauthAccountProvider_OAUTH_ACCOUNT_PROVIDER_GOOGLE OauthAccountProvider = 1
	// apple
	OauthAccountProvider_OAUTH_ACCOUNT_PROVIDER_APPLE OauthAccountProvider = 2
	// facebook
	OauthAccountProvider_OAUTH_ACCOUNT_PROVIDER_FACEBOOK OauthAccountProvider = 3
)

// Enum value maps for OauthAccountProvider.
var (
	OauthAccountProvider_name = map[int32]string{
		0: "OAUTH_ACCOUNT_PROVIDER_UNSPECIFIED",
		1: "OAUTH_ACCOUNT_PROVIDER_GOOGLE",
		2: "OAUTH_ACCOUNT_PROVIDER_APPLE",
		3: "OAUTH_ACCOUNT_PROVIDER_FACEBOOK",
	}
	OauthAccountProvider_value = map[string]int32{
		"OAUTH_ACCOUNT_PROVIDER_UNSPECIFIED": 0,
		"OAUTH_ACCOUNT_PROVIDER_GOOGLE":      1,
		"OAUTH_ACCOUNT_PROVIDER_APPLE":       2,
		"OAUTH_ACCOUNT_PROVIDER_FACEBOOK":    3,
	}
)

func (x OauthAccountProvider) Enum() *OauthAccountProvider {
	p := new(OauthAccountProvider)
	*p = x
	return p
}

func (x OauthAccountProvider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OauthAccountProvider) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_account_v1_oauth_account_enums_proto_enumTypes[0].Descriptor()
}

func (OauthAccountProvider) Type() protoreflect.EnumType {
	return &file_moego_models_account_v1_oauth_account_enums_proto_enumTypes[0]
}

func (x OauthAccountProvider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OauthAccountProvider.Descriptor instead.
func (OauthAccountProvider) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_account_v1_oauth_account_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_account_v1_oauth_account_enums_proto protoreflect.FileDescriptor

var file_moego_models_account_v1_oauth_account_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0xa8, 0x01, 0x0a,
	0x14, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a,
	0x1d, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50,
	0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x45,
	0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x03, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_account_v1_oauth_account_enums_proto_rawDescOnce sync.Once
	file_moego_models_account_v1_oauth_account_enums_proto_rawDescData = file_moego_models_account_v1_oauth_account_enums_proto_rawDesc
)

func file_moego_models_account_v1_oauth_account_enums_proto_rawDescGZIP() []byte {
	file_moego_models_account_v1_oauth_account_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_account_v1_oauth_account_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_account_v1_oauth_account_enums_proto_rawDescData)
	})
	return file_moego_models_account_v1_oauth_account_enums_proto_rawDescData
}

var file_moego_models_account_v1_oauth_account_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_account_v1_oauth_account_enums_proto_goTypes = []interface{}{
	(OauthAccountProvider)(0), // 0: moego.models.account.v1.OauthAccountProvider
}
var file_moego_models_account_v1_oauth_account_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_account_v1_oauth_account_enums_proto_init() }
func file_moego_models_account_v1_oauth_account_enums_proto_init() {
	if File_moego_models_account_v1_oauth_account_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_account_v1_oauth_account_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_account_v1_oauth_account_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_account_v1_oauth_account_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_account_v1_oauth_account_enums_proto_enumTypes,
	}.Build()
	File_moego_models_account_v1_oauth_account_enums_proto = out.File
	file_moego_models_account_v1_oauth_account_enums_proto_rawDesc = nil
	file_moego_models_account_v1_oauth_account_enums_proto_goTypes = nil
	file_moego_models_account_v1_oauth_account_enums_proto_depIdxs = nil
}
