// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/customer/v1/customer_pet_vaccine_defs.proto

package customerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// online booking pet vaccine definition
type VaccineDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine binding id
	VaccineBindingId *int32 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3,oneof" json:"vaccine_binding_id,omitempty"`
	// vaccine id
	VaccineId int32 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date
	ExpirationDate string `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	// document url
	DocumentUrls []string `protobuf:"bytes,4,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *VaccineDef) Reset() {
	*x = VaccineDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VaccineDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaccineDef) ProtoMessage() {}

func (x *VaccineDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaccineDef.ProtoReflect.Descriptor instead.
func (*VaccineDef) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescGZIP(), []int{0}
}

func (x *VaccineDef) GetVaccineBindingId() int32 {
	if x != nil && x.VaccineBindingId != nil {
		return *x.VaccineBindingId
	}
	return 0
}

func (x *VaccineDef) GetVaccineId() int32 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *VaccineDef) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

func (x *VaccineDef) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

var File_moego_models_customer_v1_customer_pet_vaccine_defs_proto protoreflect.FileDescriptor

var file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf9, 0x01,
	0x0a, 0x0a, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3a, 0x0a, 0x12,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x4b, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0xfa, 0x42, 0x1f, 0x72, 0x1d,
	0x18, 0x14, 0x32, 0x16, 0x5e, 0x28, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x29, 0x3f, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0e, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescOnce sync.Once
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescData = file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDesc
)

func file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescGZIP() []byte {
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescData)
	})
	return file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDescData
}

var file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_goTypes = []interface{}{
	(*VaccineDef)(nil), // 0: moego.models.customer.v1.VaccineDef
}
var file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_init() }
func file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_init() {
	if File_moego_models_customer_v1_customer_pet_vaccine_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VaccineDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_msgTypes,
	}.Build()
	File_moego_models_customer_v1_customer_pet_vaccine_defs_proto = out.File
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_rawDesc = nil
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_goTypes = nil
	file_moego_models_customer_v1_customer_pet_vaccine_defs_proto_depIdxs = nil
}
