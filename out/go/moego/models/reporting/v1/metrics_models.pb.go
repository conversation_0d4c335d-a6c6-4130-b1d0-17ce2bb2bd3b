// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v1/metrics_models.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Business Type
type BusinessType int32

const (
	// unspecified
	BusinessType_BUSINESS_TYPE_UNSPECIFIED BusinessType = 0
	// mobile
	BusinessType_MOBILE BusinessType = 1
	// salon
	BusinessType_SALON BusinessType = 2
	// hybrid
	BusinessType_HYBRID BusinessType = 3
)

// Enum value maps for BusinessType.
var (
	BusinessType_name = map[int32]string{
		0: "BUSINESS_TYPE_UNSPECIFIED",
		1: "MOBILE",
		2: "SALON",
		3: "HYBRID",
	}
	BusinessType_value = map[string]int32{
		"BUSINESS_TYPE_UNSPECIFIED": 0,
		"MOBILE":                    1,
		"SALON":                     2,
		"HYBRID":                    3,
	}
)

func (x BusinessType) Enum() *BusinessType {
	p := new(BusinessType)
	*p = x
	return p
}

func (x BusinessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_metrics_models_proto_enumTypes[0].Descriptor()
}

func (BusinessType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_metrics_models_proto_enumTypes[0]
}

func (x BusinessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessType.Descriptor instead.
func (BusinessType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{0}
}

// Online Booking Status
type OBStatus int32

const (
	// unspecified
	OBStatus_OB_STATUS_UNSPECIFIED OBStatus = 0
	// not enabled
	OBStatus_NOT_ENABLED OBStatus = 1
	// ob 2.0
	OBStatus_OB_2 OBStatus = 2
)

// Enum value maps for OBStatus.
var (
	OBStatus_name = map[int32]string{
		0: "OB_STATUS_UNSPECIFIED",
		1: "NOT_ENABLED",
		2: "OB_2",
	}
	OBStatus_value = map[string]int32{
		"OB_STATUS_UNSPECIFIED": 0,
		"NOT_ENABLED":           1,
		"OB_2":                  2,
	}
)

func (x OBStatus) Enum() *OBStatus {
	p := new(OBStatus)
	*p = x
	return p
}

func (x OBStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OBStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_metrics_models_proto_enumTypes[1].Descriptor()
}

func (OBStatus) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_metrics_models_proto_enumTypes[1]
}

func (x OBStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OBStatus.Descriptor instead.
func (OBStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{1}
}

// MoeGo Pay Status
type MoeGoPayStatus int32

const (
	// unspecified
	MoeGoPayStatus_MOEGO_PAY_STATUS_UNSPECIFIED MoeGoPayStatus = 0
	// uninitialized
	MoeGoPayStatus_UNINITIALIZED MoeGoPayStatus = 1
	// operated
	MoeGoPayStatus_OPERATED MoeGoPayStatus = 2
)

// Enum value maps for MoeGoPayStatus.
var (
	MoeGoPayStatus_name = map[int32]string{
		0: "MOEGO_PAY_STATUS_UNSPECIFIED",
		1: "UNINITIALIZED",
		2: "OPERATED",
	}
	MoeGoPayStatus_value = map[string]int32{
		"MOEGO_PAY_STATUS_UNSPECIFIED": 0,
		"UNINITIALIZED":                1,
		"OPERATED":                     2,
	}
)

func (x MoeGoPayStatus) Enum() *MoeGoPayStatus {
	p := new(MoeGoPayStatus)
	*p = x
	return p
}

func (x MoeGoPayStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MoeGoPayStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_metrics_models_proto_enumTypes[2].Descriptor()
}

func (MoeGoPayStatus) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_metrics_models_proto_enumTypes[2]
}

func (x MoeGoPayStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MoeGoPayStatus.Descriptor instead.
func (MoeGoPayStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{2}
}

// Permission Level
type PermissionLevel int32

const (
	// unspecified
	PermissionLevel_PERMISSION_LEVEL_UNSPECIFIED PermissionLevel = 0
)

// Enum value maps for PermissionLevel.
var (
	PermissionLevel_name = map[int32]string{
		0: "PERMISSION_LEVEL_UNSPECIFIED",
	}
	PermissionLevel_value = map[string]int32{
		"PERMISSION_LEVEL_UNSPECIFIED": 0,
	}
)

func (x PermissionLevel) Enum() *PermissionLevel {
	p := new(PermissionLevel)
	*p = x
	return p
}

func (x PermissionLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_metrics_models_proto_enumTypes[3].Descriptor()
}

func (PermissionLevel) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_metrics_models_proto_enumTypes[3]
}

func (x PermissionLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionLevel.Descriptor instead.
func (PermissionLevel) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{3}
}

// business metrics
type BusinessMetricsModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business type
	BusinessType BusinessType `protobuf:"varint,2,opt,name=business_type,json=businessType,proto3,enum=moego.models.reporting.v1.BusinessType" json:"business_type,omitempty"`
	// business country
	BusinessCountry string `protobuf:"bytes,3,opt,name=business_country,json=businessCountry,proto3" json:"business_country,omitempty"`
	// Monthly Rev: amount of finished invoice during last 30 days
	MonthlyRev float64 `protobuf:"fixed64,4,opt,name=monthly_rev,json=monthlyRev,proto3" json:"monthly_rev,omitempty"`
	// Total Units: # of locations and vans purchased in the latest plan
	TotalUnits int64 `protobuf:"varint,5,opt,name=total_units,json=totalUnits,proto3" json:"total_units,omitempty"`
	// Total Staff: # of staff on current staff list
	TotalStaff int64 `protobuf:"varint,6,opt,name=total_staff,json=totalStaff,proto3" json:"total_staff,omitempty"`
	// Total Appt: # of appointments created on calendar of last 30 days
	TotalAppt int64 `protobuf:"varint,7,opt,name=total_appt,json=totalAppt,proto3" json:"total_appt,omitempty"`
	// Total Finished Appt: # of appointments marked as finished in the last 30 days
	TotalFinishedAppt int64 `protobuf:"varint,8,opt,name=total_finished_appt,json=totalFinishedAppt,proto3" json:"total_finished_appt,omitempty"`
	// Total Msg Usage: # of msg estimated to be sent in the current billing period
	TotalMsgUsage int64 `protobuf:"varint,9,opt,name=total_msg_usage,json=totalMsgUsage,proto3" json:"total_msg_usage,omitempty"`
	// MoeGo Pay Status: Uninitialized/Operated
	MoegoPayStatus MoeGoPayStatus `protobuf:"varint,10,opt,name=moego_pay_status,json=moegoPayStatus,proto3,enum=moego.models.reporting.v1.MoeGoPayStatus" json:"moego_pay_status,omitempty"`
	// MoeGo Pay Count: # of transactions through MoeGo Pay last 30 days
	MoegoPayCount int64 `protobuf:"varint,11,opt,name=moego_pay_count,json=moegoPayCount,proto3" json:"moego_pay_count,omitempty"`
	// MoeGo Pay Transactions: amount of transactions through MoeGo Pay last 30 days
	MoegoPayTransactions float64 `protobuf:"fixed64,12,opt,name=moego_pay_transactions,json=moegoPayTransactions,proto3" json:"moego_pay_transactions,omitempty"`
	// OB Status: Not Enabled/ OB 2.0
	ObStatus OBStatus `protobuf:"varint,13,opt,name=ob_status,json=obStatus,proto3,enum=moego.models.reporting.v1.OBStatus" json:"ob_status,omitempty"`
	// OB Request: # of requests submitted through OB in the last 30 days
	ObRequests int64 `protobuf:"varint,14,opt,name=ob_requests,json=obRequests,proto3" json:"ob_requests,omitempty"`
	// Van Credits: # of vans purchased
	VanCredits int64 `protobuf:"varint,15,opt,name=van_credits,json=vanCredits,proto3" json:"van_credits,omitempty"`
	// Van Created: # of vans set up
	VanCreated int64 `protobuf:"varint,16,opt,name=van_created,json=vanCreated,proto3" json:"van_created,omitempty"`
	// Location Credits: # of locations purchased
	LocationCredits int64 `protobuf:"varint,17,opt,name=location_credits,json=locationCredits,proto3" json:"location_credits,omitempty"`
	// Location Created: # of locations set up
	LocationCreated int64 `protobuf:"varint,18,opt,name=location_created,json=locationCreated,proto3" json:"location_created,omitempty"`
	// Permission Level: 101, 102, 1001, 1002, 1003
	PermissionLevel string `protobuf:"bytes,19,opt,name=permission_level,json=permissionLevel,proto3" json:"permission_level,omitempty"`
	// Metricized At: UTC data Time of yesterday
	MetricizedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=metricized_at,json=metricizedAt,proto3" json:"metricized_at,omitempty"`
}

func (x *BusinessMetricsModel) Reset() {
	*x = BusinessMetricsModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessMetricsModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessMetricsModel) ProtoMessage() {}

func (x *BusinessMetricsModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessMetricsModel.ProtoReflect.Descriptor instead.
func (*BusinessMetricsModel) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessMetricsModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessMetricsModel) GetBusinessType() BusinessType {
	if x != nil {
		return x.BusinessType
	}
	return BusinessType_BUSINESS_TYPE_UNSPECIFIED
}

func (x *BusinessMetricsModel) GetBusinessCountry() string {
	if x != nil {
		return x.BusinessCountry
	}
	return ""
}

func (x *BusinessMetricsModel) GetMonthlyRev() float64 {
	if x != nil {
		return x.MonthlyRev
	}
	return 0
}

func (x *BusinessMetricsModel) GetTotalUnits() int64 {
	if x != nil {
		return x.TotalUnits
	}
	return 0
}

func (x *BusinessMetricsModel) GetTotalStaff() int64 {
	if x != nil {
		return x.TotalStaff
	}
	return 0
}

func (x *BusinessMetricsModel) GetTotalAppt() int64 {
	if x != nil {
		return x.TotalAppt
	}
	return 0
}

func (x *BusinessMetricsModel) GetTotalFinishedAppt() int64 {
	if x != nil {
		return x.TotalFinishedAppt
	}
	return 0
}

func (x *BusinessMetricsModel) GetTotalMsgUsage() int64 {
	if x != nil {
		return x.TotalMsgUsage
	}
	return 0
}

func (x *BusinessMetricsModel) GetMoegoPayStatus() MoeGoPayStatus {
	if x != nil {
		return x.MoegoPayStatus
	}
	return MoeGoPayStatus_MOEGO_PAY_STATUS_UNSPECIFIED
}

func (x *BusinessMetricsModel) GetMoegoPayCount() int64 {
	if x != nil {
		return x.MoegoPayCount
	}
	return 0
}

func (x *BusinessMetricsModel) GetMoegoPayTransactions() float64 {
	if x != nil {
		return x.MoegoPayTransactions
	}
	return 0
}

func (x *BusinessMetricsModel) GetObStatus() OBStatus {
	if x != nil {
		return x.ObStatus
	}
	return OBStatus_OB_STATUS_UNSPECIFIED
}

func (x *BusinessMetricsModel) GetObRequests() int64 {
	if x != nil {
		return x.ObRequests
	}
	return 0
}

func (x *BusinessMetricsModel) GetVanCredits() int64 {
	if x != nil {
		return x.VanCredits
	}
	return 0
}

func (x *BusinessMetricsModel) GetVanCreated() int64 {
	if x != nil {
		return x.VanCreated
	}
	return 0
}

func (x *BusinessMetricsModel) GetLocationCredits() int64 {
	if x != nil {
		return x.LocationCredits
	}
	return 0
}

func (x *BusinessMetricsModel) GetLocationCreated() int64 {
	if x != nil {
		return x.LocationCreated
	}
	return 0
}

func (x *BusinessMetricsModel) GetPermissionLevel() string {
	if x != nil {
		return x.PermissionLevel
	}
	return ""
}

func (x *BusinessMetricsModel) GetMetricizedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.MetricizedAt
	}
	return nil
}

// campaign metrics
type CampaignMetricsModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign id
	CampaignId int64 `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	// starter
	Starter string `protobuf:"bytes,2,opt,name=starter,proto3" json:"starter,omitempty"`
	// created at
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// notification title
	NotificationTitle string `protobuf:"bytes,4,opt,name=notification_title,json=notificationTitle,proto3" json:"notification_title,omitempty"`
	// notification body
	NotificationBody string `protobuf:"bytes,5,opt,name=notification_body,json=notificationBody,proto3" json:"notification_body,omitempty"`
	// notification read count
	NotificationReadCount int64 `protobuf:"varint,6,opt,name=notification_read_count,json=notificationReadCount,proto3" json:"notification_read_count,omitempty"`
	// notification app clicked count
	NotificationAppClickedCount int64 `protobuf:"varint,7,opt,name=notification_app_clicked_count,json=notificationAppClickedCount,proto3" json:"notification_app_clicked_count,omitempty"`
	// company count
	CompanyCount int64 `protobuf:"varint,8,opt,name=company_count,json=companyCount,proto3" json:"company_count,omitempty"`
	// business count
	BusinessCount int64 `protobuf:"varint,9,opt,name=business_count,json=businessCount,proto3" json:"business_count,omitempty"`
	// staff count
	StaffCount int64 `protobuf:"varint,10,opt,name=staff_count,json=staffCount,proto3" json:"staff_count,omitempty"`
}

func (x *CampaignMetricsModel) Reset() {
	*x = CampaignMetricsModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignMetricsModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignMetricsModel) ProtoMessage() {}

func (x *CampaignMetricsModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignMetricsModel.ProtoReflect.Descriptor instead.
func (*CampaignMetricsModel) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{1}
}

func (x *CampaignMetricsModel) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *CampaignMetricsModel) GetStarter() string {
	if x != nil {
		return x.Starter
	}
	return ""
}

func (x *CampaignMetricsModel) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *CampaignMetricsModel) GetNotificationTitle() string {
	if x != nil {
		return x.NotificationTitle
	}
	return ""
}

func (x *CampaignMetricsModel) GetNotificationBody() string {
	if x != nil {
		return x.NotificationBody
	}
	return ""
}

func (x *CampaignMetricsModel) GetNotificationReadCount() int64 {
	if x != nil {
		return x.NotificationReadCount
	}
	return 0
}

func (x *CampaignMetricsModel) GetNotificationAppClickedCount() int64 {
	if x != nil {
		return x.NotificationAppClickedCount
	}
	return 0
}

func (x *CampaignMetricsModel) GetCompanyCount() int64 {
	if x != nil {
		return x.CompanyCount
	}
	return 0
}

func (x *CampaignMetricsModel) GetBusinessCount() int64 {
	if x != nil {
		return x.BusinessCount
	}
	return 0
}

func (x *CampaignMetricsModel) GetStaffCount() int64 {
	if x != nil {
		return x.StaffCount
	}
	return 0
}

// staff campaign metrics
type StaffCampaignMetricsModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// receiver staff id
	ReceiverStaffId int64 `protobuf:"varint,1,opt,name=receiver_staff_id,json=receiverStaffId,proto3" json:"receiver_staff_id,omitempty"`
	// receiver name
	ReceiverName string `protobuf:"bytes,2,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name,omitempty"`
	// notification read count
	NotificationReadCount int64 `protobuf:"varint,3,opt,name=notification_read_count,json=notificationReadCount,proto3" json:"notification_read_count,omitempty"`
	// notification app clicked count
	NotificationAppClickedCount int64 `protobuf:"varint,4,opt,name=notification_app_clicked_count,json=notificationAppClickedCount,proto3" json:"notification_app_clicked_count,omitempty"`
	// notification count
	NotificationCount int64 `protobuf:"varint,5,opt,name=notification_count,json=notificationCount,proto3" json:"notification_count,omitempty"`
	// campaign count
	CampaignCount int64 `protobuf:"varint,6,opt,name=campaign_count,json=campaignCount,proto3" json:"campaign_count,omitempty"`
}

func (x *StaffCampaignMetricsModel) Reset() {
	*x = StaffCampaignMetricsModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffCampaignMetricsModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffCampaignMetricsModel) ProtoMessage() {}

func (x *StaffCampaignMetricsModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_metrics_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffCampaignMetricsModel.ProtoReflect.Descriptor instead.
func (*StaffCampaignMetricsModel) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP(), []int{2}
}

func (x *StaffCampaignMetricsModel) GetReceiverStaffId() int64 {
	if x != nil {
		return x.ReceiverStaffId
	}
	return 0
}

func (x *StaffCampaignMetricsModel) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *StaffCampaignMetricsModel) GetNotificationReadCount() int64 {
	if x != nil {
		return x.NotificationReadCount
	}
	return 0
}

func (x *StaffCampaignMetricsModel) GetNotificationAppClickedCount() int64 {
	if x != nil {
		return x.NotificationAppClickedCount
	}
	return 0
}

func (x *StaffCampaignMetricsModel) GetNotificationCount() int64 {
	if x != nil {
		return x.NotificationCount
	}
	return 0
}

func (x *StaffCampaignMetricsModel) GetCampaignCount() int64 {
	if x != nil {
		return x.CampaignCount
	}
	return 0
}

var File_moego_models_reporting_v1_metrics_models_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v1_metrics_models_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x07, 0x0a,
	0x14, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x76, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x76,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x70, 0x70,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x70, 0x70,
	0x74, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x10, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x50, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f,
	0x70, 0x61, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x09,
	0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x76, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x76, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x3f, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x69, 0x7a, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x69, 0x7a, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xd2, 0x03, 0x0a, 0x14, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x36,
	0x0a, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x1e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xbf, 0x02, 0x0a, 0x19, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x43, 0x0a, 0x1e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x50, 0x0a, 0x0c, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x41, 0x4c, 0x4f, 0x4e, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x48, 0x59, 0x42, 0x52, 0x49, 0x44, 0x10, 0x03, 0x2a, 0x40, 0x0a, 0x08,
	0x4f, 0x42, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x42, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x42, 0x5f, 0x32, 0x10, 0x02, 0x2a, 0x53,
	0x0a, 0x0e, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x50, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49,
	0x5a, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x02, 0x2a, 0x33, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v1_metrics_models_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v1_metrics_models_proto_rawDescData = file_moego_models_reporting_v1_metrics_models_proto_rawDesc
)

func file_moego_models_reporting_v1_metrics_models_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v1_metrics_models_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v1_metrics_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v1_metrics_models_proto_rawDescData)
	})
	return file_moego_models_reporting_v1_metrics_models_proto_rawDescData
}

var file_moego_models_reporting_v1_metrics_models_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_reporting_v1_metrics_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_reporting_v1_metrics_models_proto_goTypes = []interface{}{
	(BusinessType)(0),                 // 0: moego.models.reporting.v1.BusinessType
	(OBStatus)(0),                     // 1: moego.models.reporting.v1.OBStatus
	(MoeGoPayStatus)(0),               // 2: moego.models.reporting.v1.MoeGoPayStatus
	(PermissionLevel)(0),              // 3: moego.models.reporting.v1.PermissionLevel
	(*BusinessMetricsModel)(nil),      // 4: moego.models.reporting.v1.BusinessMetricsModel
	(*CampaignMetricsModel)(nil),      // 5: moego.models.reporting.v1.CampaignMetricsModel
	(*StaffCampaignMetricsModel)(nil), // 6: moego.models.reporting.v1.StaffCampaignMetricsModel
	(*timestamppb.Timestamp)(nil),     // 7: google.protobuf.Timestamp
}
var file_moego_models_reporting_v1_metrics_models_proto_depIdxs = []int32{
	0, // 0: moego.models.reporting.v1.BusinessMetricsModel.business_type:type_name -> moego.models.reporting.v1.BusinessType
	2, // 1: moego.models.reporting.v1.BusinessMetricsModel.moego_pay_status:type_name -> moego.models.reporting.v1.MoeGoPayStatus
	1, // 2: moego.models.reporting.v1.BusinessMetricsModel.ob_status:type_name -> moego.models.reporting.v1.OBStatus
	7, // 3: moego.models.reporting.v1.BusinessMetricsModel.metricized_at:type_name -> google.protobuf.Timestamp
	7, // 4: moego.models.reporting.v1.CampaignMetricsModel.started_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v1_metrics_models_proto_init() }
func file_moego_models_reporting_v1_metrics_models_proto_init() {
	if File_moego_models_reporting_v1_metrics_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v1_metrics_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessMetricsModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v1_metrics_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignMetricsModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v1_metrics_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffCampaignMetricsModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v1_metrics_models_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v1_metrics_models_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v1_metrics_models_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v1_metrics_models_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v1_metrics_models_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v1_metrics_models_proto = out.File
	file_moego_models_reporting_v1_metrics_models_proto_rawDesc = nil
	file_moego_models_reporting_v1_metrics_models_proto_goTypes = nil
	file_moego_models_reporting_v1_metrics_models_proto_depIdxs = nil
}
