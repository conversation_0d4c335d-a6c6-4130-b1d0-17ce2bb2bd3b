// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/diagram_model.proto

package reportingpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An enumeration of diagram type
type DiagramType int32

const (
	// Unspecified diagram type
	DiagramType_DIAGRAM_TYPE_UNSPECIFIED DiagramType = 0
	// BAR diagram
	DiagramType_BAR DiagramType = 1
	// Funnel diagram
	DiagramType_FUNNEL DiagramType = 2
	// Table diagram
	DiagramType_TABLE DiagramType = 3
	// Pie diagram
	DiagramType_PIE DiagramType = 4
	// Big diagram
	DiagramType_BIG_NUMBER DiagramType = 5
	// Status bar diagram
	DiagramType_STATUS_BAR DiagramType = 6
	// Group
	DiagramType_GROUP DiagramType = 7
	// Rank
	DiagramType_RANK DiagramType = 8
	// Procedure number
	DiagramType_PROCEDURE_NUMBER DiagramType = 9
	// A array of number with label
	DiagramType_NUMBER_ARRAY DiagramType = 10
	// A line chart type
	DiagramType_LINE DiagramType = 11
	// Odometer
	DiagramType_ODOMETER DiagramType = 12
	// Rating star
	DiagramType_RATING_STAR DiagramType = 13
	// Number list, display as rows
	DiagramType_NUMBER_LIST DiagramType = 14
	// Bar chart version 2
	DiagramType_BAR_V2 DiagramType = 15
	// Legend
	DiagramType_LEGEND DiagramType = 16
	// Dynamic table
	DiagramType_DYNAMIC_TABLE DiagramType = 17
	// Multi dimension table
	DiagramType_MULTI_DIMENSION_TABLE DiagramType = 18
)

// Enum value maps for DiagramType.
var (
	DiagramType_name = map[int32]string{
		0:  "DIAGRAM_TYPE_UNSPECIFIED",
		1:  "BAR",
		2:  "FUNNEL",
		3:  "TABLE",
		4:  "PIE",
		5:  "BIG_NUMBER",
		6:  "STATUS_BAR",
		7:  "GROUP",
		8:  "RANK",
		9:  "PROCEDURE_NUMBER",
		10: "NUMBER_ARRAY",
		11: "LINE",
		12: "ODOMETER",
		13: "RATING_STAR",
		14: "NUMBER_LIST",
		15: "BAR_V2",
		16: "LEGEND",
		17: "DYNAMIC_TABLE",
		18: "MULTI_DIMENSION_TABLE",
	}
	DiagramType_value = map[string]int32{
		"DIAGRAM_TYPE_UNSPECIFIED": 0,
		"BAR":                      1,
		"FUNNEL":                   2,
		"TABLE":                    3,
		"PIE":                      4,
		"BIG_NUMBER":               5,
		"STATUS_BAR":               6,
		"GROUP":                    7,
		"RANK":                     8,
		"PROCEDURE_NUMBER":         9,
		"NUMBER_ARRAY":             10,
		"LINE":                     11,
		"ODOMETER":                 12,
		"RATING_STAR":              13,
		"NUMBER_LIST":              14,
		"BAR_V2":                   15,
		"LEGEND":                   16,
		"DYNAMIC_TABLE":            17,
		"MULTI_DIMENSION_TABLE":    18,
	}
)

func (x DiagramType) Enum() *DiagramType {
	p := new(DiagramType)
	*p = x
	return p
}

func (x DiagramType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiagramType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_diagram_model_proto_enumTypes[0].Descriptor()
}

func (DiagramType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_diagram_model_proto_enumTypes[0]
}

func (x DiagramType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiagramType.Descriptor instead.
func (DiagramType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{0}
}

// Customer KPI configuration
type CustomizeKPI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The label of the KPI
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// The diagram id of the KPI
	DiagramId string `protobuf:"bytes,2,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The checkbox status of the KPI
	Selected bool `protobuf:"varint,3,opt,name=selected,proto3" json:"selected,omitempty"`
}

func (x *CustomizeKPI) Reset() {
	*x = CustomizeKPI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizeKPI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeKPI) ProtoMessage() {}

func (x *CustomizeKPI) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeKPI.ProtoReflect.Descriptor instead.
func (*CustomizeKPI) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{0}
}

func (x *CustomizeKPI) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CustomizeKPI) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *CustomizeKPI) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

// A table customized configuration
type TableCustomizedConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The sorted field
	SortedFieldKeys []string `protobuf:"bytes,1,rep,name=sorted_field_keys,json=sortedFieldKeys,proto3" json:"sorted_field_keys,omitempty"`
	// The hidden field keys
	HiddenFieldKeys []string `protobuf:"bytes,2,rep,name=hidden_field_keys,json=hiddenFieldKeys,proto3" json:"hidden_field_keys,omitempty"`
	// The group by field keys
	Kpis []*CustomizeKPI `protobuf:"bytes,3,rep,name=kpis,proto3" json:"kpis,omitempty"`
	// Saved filters
	SavedFilters []*FilterRequest `protobuf:"bytes,4,rep,name=saved_filters,json=savedFilters,proto3" json:"saved_filters,omitempty"`
}

func (x *TableCustomizedConfig) Reset() {
	*x = TableCustomizedConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableCustomizedConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableCustomizedConfig) ProtoMessage() {}

func (x *TableCustomizedConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableCustomizedConfig.ProtoReflect.Descriptor instead.
func (*TableCustomizedConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{1}
}

func (x *TableCustomizedConfig) GetSortedFieldKeys() []string {
	if x != nil {
		return x.SortedFieldKeys
	}
	return nil
}

func (x *TableCustomizedConfig) GetHiddenFieldKeys() []string {
	if x != nil {
		return x.HiddenFieldKeys
	}
	return nil
}

func (x *TableCustomizedConfig) GetKpis() []*CustomizeKPI {
	if x != nil {
		return x.Kpis
	}
	return nil
}

func (x *TableCustomizedConfig) GetSavedFilters() []*FilterRequest {
	if x != nil {
		return x.SavedFilters
	}
	return nil
}

// A table meta
type TableMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current report table id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The fields of the table
	Fields []*Field `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	// The filters of the table, deprecated by chris, use filter_groups instead
	Filters []*Filter `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// The customized configurations of the table
	CustomizedConfig *TableCustomizedConfig `protobuf:"bytes,4,opt,name=customized_config,json=customizedConfig,proto3" json:"customized_config,omitempty"`
	// The title of the table
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// The description of the table
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// The download enable of the table
	DownloadEnable bool `protobuf:"varint,7,opt,name=download_enable,json=downloadEnable,proto3" json:"download_enable,omitempty"`
	// The drill configuration
	DrillConfig *DrillConfig `protobuf:"bytes,8,opt,name=drill_config,json=drillConfig,proto3" json:"drill_config,omitempty"`
	// default group by field key
	DefaultGroupByFieldKeys []string `protobuf:"bytes,9,rep,name=default_group_by_field_keys,json=defaultGroupByFieldKeys,proto3" json:"default_group_by_field_keys,omitempty"`
	// Current table's required permission code
	PermissionCode string `protobuf:"bytes,10,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	// The filter groups of the table
	FilterGroups []*FilterGroup `protobuf:"bytes,11,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
	// Whether to display location/franchisee selector
	ShowScopeSelector bool `protobuf:"varint,12,opt,name=show_scope_selector,json=showScopeSelector,proto3" json:"show_scope_selector,omitempty"`
	// Whether to display compare period selector
	ShowComparePeriodSelector bool `protobuf:"varint,13,opt,name=show_compare_period_selector,json=showComparePeriodSelector,proto3" json:"show_compare_period_selector,omitempty"`
	// Max grouping by dimensions count
	MaxQueryDimensions int32 `protobuf:"varint,14,opt,name=max_query_dimensions,json=maxQueryDimensions,proto3" json:"max_query_dimensions,omitempty"`
}

func (x *TableMeta) Reset() {
	*x = TableMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableMeta) ProtoMessage() {}

func (x *TableMeta) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableMeta.ProtoReflect.Descriptor instead.
func (*TableMeta) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{2}
}

func (x *TableMeta) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *TableMeta) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TableMeta) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *TableMeta) GetCustomizedConfig() *TableCustomizedConfig {
	if x != nil {
		return x.CustomizedConfig
	}
	return nil
}

func (x *TableMeta) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *TableMeta) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TableMeta) GetDownloadEnable() bool {
	if x != nil {
		return x.DownloadEnable
	}
	return false
}

func (x *TableMeta) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *TableMeta) GetDefaultGroupByFieldKeys() []string {
	if x != nil {
		return x.DefaultGroupByFieldKeys
	}
	return nil
}

func (x *TableMeta) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *TableMeta) GetFilterGroups() []*FilterGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

func (x *TableMeta) GetShowScopeSelector() bool {
	if x != nil {
		return x.ShowScopeSelector
	}
	return false
}

func (x *TableMeta) GetShowComparePeriodSelector() bool {
	if x != nil {
		return x.ShowComparePeriodSelector
	}
	return false
}

func (x *TableMeta) GetMaxQueryDimensions() int32 {
	if x != nil {
		return x.MaxQueryDimensions
	}
	return 0
}

// A dashboard diagram data
type DiagramData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The diagram content
	//
	// Types that are assignable to Data:
	//
	//	*DiagramData_NumberData
	//	*DiagramData_BarData
	//	*DiagramData_PieData
	//	*DiagramData_RainbowTableData
	//	*DiagramData_RankData
	//	*DiagramData_ProcedureNumberData
	//	*DiagramData_FunnelData
	//	*DiagramData_NumberArrayData
	//	*DiagramData_LineData
	//	*DiagramData_OdometerData
	//	*DiagramData_RatingStarData
	//	*DiagramData_NumberListData
	//	*DiagramData_LegendData
	Data isDiagramData_Data `protobuf_oneof:"data"`
}

func (x *DiagramData) Reset() {
	*x = DiagramData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiagramData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiagramData) ProtoMessage() {}

func (x *DiagramData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiagramData.ProtoReflect.Descriptor instead.
func (*DiagramData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{3}
}

func (x *DiagramData) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (m *DiagramData) GetData() isDiagramData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *DiagramData) GetNumberData() *NumberData {
	if x, ok := x.GetData().(*DiagramData_NumberData); ok {
		return x.NumberData
	}
	return nil
}

func (x *DiagramData) GetBarData() *BarData {
	if x, ok := x.GetData().(*DiagramData_BarData); ok {
		return x.BarData
	}
	return nil
}

func (x *DiagramData) GetPieData() *PieData {
	if x, ok := x.GetData().(*DiagramData_PieData); ok {
		return x.PieData
	}
	return nil
}

func (x *DiagramData) GetRainbowTableData() *TableData {
	if x, ok := x.GetData().(*DiagramData_RainbowTableData); ok {
		return x.RainbowTableData
	}
	return nil
}

func (x *DiagramData) GetRankData() *RankData {
	if x, ok := x.GetData().(*DiagramData_RankData); ok {
		return x.RankData
	}
	return nil
}

func (x *DiagramData) GetProcedureNumberData() *ProcedureNumberData {
	if x, ok := x.GetData().(*DiagramData_ProcedureNumberData); ok {
		return x.ProcedureNumberData
	}
	return nil
}

func (x *DiagramData) GetFunnelData() *FunnelData {
	if x, ok := x.GetData().(*DiagramData_FunnelData); ok {
		return x.FunnelData
	}
	return nil
}

func (x *DiagramData) GetNumberArrayData() *NumberArrayData {
	if x, ok := x.GetData().(*DiagramData_NumberArrayData); ok {
		return x.NumberArrayData
	}
	return nil
}

func (x *DiagramData) GetLineData() *LineData {
	if x, ok := x.GetData().(*DiagramData_LineData); ok {
		return x.LineData
	}
	return nil
}

func (x *DiagramData) GetOdometerData() *OdometerData {
	if x, ok := x.GetData().(*DiagramData_OdometerData); ok {
		return x.OdometerData
	}
	return nil
}

func (x *DiagramData) GetRatingStarData() *RatingStarData {
	if x, ok := x.GetData().(*DiagramData_RatingStarData); ok {
		return x.RatingStarData
	}
	return nil
}

func (x *DiagramData) GetNumberListData() *NumberListData {
	if x, ok := x.GetData().(*DiagramData_NumberListData); ok {
		return x.NumberListData
	}
	return nil
}

func (x *DiagramData) GetLegendData() *LegendData {
	if x, ok := x.GetData().(*DiagramData_LegendData); ok {
		return x.LegendData
	}
	return nil
}

type isDiagramData_Data interface {
	isDiagramData_Data()
}

type DiagramData_NumberData struct {
	// The number data
	NumberData *NumberData `protobuf:"bytes,2,opt,name=number_data,json=numberData,proto3,oneof"`
}

type DiagramData_BarData struct {
	// The bar data
	BarData *BarData `protobuf:"bytes,3,opt,name=bar_data,json=barData,proto3,oneof"`
}

type DiagramData_PieData struct {
	// The pie data
	PieData *PieData `protobuf:"bytes,4,opt,name=pie_data,json=pieData,proto3,oneof"`
}

type DiagramData_RainbowTableData struct {
	// The rainbow table data
	RainbowTableData *TableData `protobuf:"bytes,5,opt,name=rainbow_table_data,json=rainbowTableData,proto3,oneof"`
}

type DiagramData_RankData struct {
	// The rank data
	RankData *RankData `protobuf:"bytes,6,opt,name=rank_data,json=rankData,proto3,oneof"`
}

type DiagramData_ProcedureNumberData struct {
	// The procedure number data
	ProcedureNumberData *ProcedureNumberData `protobuf:"bytes,7,opt,name=procedure_number_data,json=procedureNumberData,proto3,oneof"`
}

type DiagramData_FunnelData struct {
	// The funnel data
	FunnelData *FunnelData `protobuf:"bytes,8,opt,name=funnel_data,json=funnelData,proto3,oneof"`
}

type DiagramData_NumberArrayData struct {
	// The number array data
	NumberArrayData *NumberArrayData `protobuf:"bytes,9,opt,name=number_array_data,json=numberArrayData,proto3,oneof"`
}

type DiagramData_LineData struct {
	// Line data
	LineData *LineData `protobuf:"bytes,10,opt,name=line_data,json=lineData,proto3,oneof"`
}

type DiagramData_OdometerData struct {
	// Odometer data
	OdometerData *OdometerData `protobuf:"bytes,11,opt,name=odometer_data,json=odometerData,proto3,oneof"`
}

type DiagramData_RatingStarData struct {
	// Rating star data
	RatingStarData *RatingStarData `protobuf:"bytes,12,opt,name=rating_star_data,json=ratingStarData,proto3,oneof"`
}

type DiagramData_NumberListData struct {
	// Number list data
	NumberListData *NumberListData `protobuf:"bytes,13,opt,name=number_list_data,json=numberListData,proto3,oneof"`
}

type DiagramData_LegendData struct {
	// Legend data
	LegendData *LegendData `protobuf:"bytes,14,opt,name=legend_data,json=legendData,proto3,oneof"`
}

func (*DiagramData_NumberData) isDiagramData_Data() {}

func (*DiagramData_BarData) isDiagramData_Data() {}

func (*DiagramData_PieData) isDiagramData_Data() {}

func (*DiagramData_RainbowTableData) isDiagramData_Data() {}

func (*DiagramData_RankData) isDiagramData_Data() {}

func (*DiagramData_ProcedureNumberData) isDiagramData_Data() {}

func (*DiagramData_FunnelData) isDiagramData_Data() {}

func (*DiagramData_NumberArrayData) isDiagramData_Data() {}

func (*DiagramData_LineData) isDiagramData_Data() {}

func (*DiagramData_OdometerData) isDiagramData_Data() {}

func (*DiagramData_RatingStarData) isDiagramData_Data() {}

func (*DiagramData_NumberListData) isDiagramData_Data() {}

func (*DiagramData_LegendData) isDiagramData_Data() {}

// Number data
type NumberData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The field_key of the big number
	FieldKey string `protobuf:"bytes,1,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// The type of the field
	FieldType Field_Type `protobuf:"varint,2,opt,name=field_type,json=fieldType,proto3,enum=moego.models.reporting.v2.Field_Type" json:"field_type,omitempty"`
	// The value of the big number
	Value *Value `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	// The previous value of the big number
	PreviousValue *Value `protobuf:"bytes,4,opt,name=previous_value,json=previousValue,proto3,oneof" json:"previous_value,omitempty"`
	// The label of the big number
	Label *string `protobuf:"bytes,5,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// The description of the big number
	Description *string `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// The trend of the field
	Trend Trend `protobuf:"varint,7,opt,name=trend,proto3,enum=moego.models.reporting.v2.Trend" json:"trend,omitempty"`
	// The insight information
	Insight *MoeGoInsight `protobuf:"bytes,8,opt,name=insight,proto3,oneof" json:"insight,omitempty"`
	// Style config of the line
	Style *StyleConfig `protobuf:"bytes,9,opt,name=style,proto3,oneof" json:"style,omitempty"`
	// Drill config for the field
	DrillConfig *DrillConfig `protobuf:"bytes,10,opt,name=drill_config,json=drillConfig,proto3,oneof" json:"drill_config,omitempty"`
	// Linkage config for the field
	LinkageConfig *LinkageConfig `protobuf:"bytes,11,opt,name=linkage_config,json=linkageConfig,proto3,oneof" json:"linkage_config,omitempty"`
}

func (x *NumberData) Reset() {
	*x = NumberData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberData) ProtoMessage() {}

func (x *NumberData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberData.ProtoReflect.Descriptor instead.
func (*NumberData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{4}
}

func (x *NumberData) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *NumberData) GetFieldType() Field_Type {
	if x != nil {
		return x.FieldType
	}
	return Field_TYPE_UNSPECIFIED
}

func (x *NumberData) GetValue() *Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *NumberData) GetPreviousValue() *Value {
	if x != nil {
		return x.PreviousValue
	}
	return nil
}

func (x *NumberData) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *NumberData) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *NumberData) GetTrend() Trend {
	if x != nil {
		return x.Trend
	}
	return Trend_TREND_UNSPECIFIED
}

func (x *NumberData) GetInsight() *MoeGoInsight {
	if x != nil {
		return x.Insight
	}
	return nil
}

func (x *NumberData) GetStyle() *StyleConfig {
	if x != nil {
		return x.Style
	}
	return nil
}

func (x *NumberData) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *NumberData) GetLinkageConfig() *LinkageConfig {
	if x != nil {
		return x.LinkageConfig
	}
	return nil
}

// rank unit
type RankUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// avatar url
	AvatarUrl *string `protobuf:"bytes,1,opt,name=avatar_url,json=avatarUrl,proto3,oneof" json:"avatar_url,omitempty"`
	// label
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// main_metric
	MainMetric *Value `protobuf:"bytes,3,opt,name=main_metric,json=mainMetric,proto3" json:"main_metric,omitempty"`
	// secondary_metric
	SecondaryMetric *Value `protobuf:"bytes,4,opt,name=secondary_metric,json=secondaryMetric,proto3,oneof" json:"secondary_metric,omitempty"`
	// Style config of the line
	Style *StyleConfig `protobuf:"bytes,5,opt,name=style,proto3,oneof" json:"style,omitempty"`
	// main metric data
	MainMetricData *NumberData `protobuf:"bytes,6,opt,name=main_metric_data,json=mainMetricData,proto3" json:"main_metric_data,omitempty"`
	// secondary metric data
	SecondaryMetricData *NumberData `protobuf:"bytes,7,opt,name=secondary_metric_data,json=secondaryMetricData,proto3,oneof" json:"secondary_metric_data,omitempty"`
}

func (x *RankUnit) Reset() {
	*x = RankUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankUnit) ProtoMessage() {}

func (x *RankUnit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankUnit.ProtoReflect.Descriptor instead.
func (*RankUnit) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{5}
}

func (x *RankUnit) GetAvatarUrl() string {
	if x != nil && x.AvatarUrl != nil {
		return *x.AvatarUrl
	}
	return ""
}

func (x *RankUnit) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *RankUnit) GetMainMetric() *Value {
	if x != nil {
		return x.MainMetric
	}
	return nil
}

func (x *RankUnit) GetSecondaryMetric() *Value {
	if x != nil {
		return x.SecondaryMetric
	}
	return nil
}

func (x *RankUnit) GetStyle() *StyleConfig {
	if x != nil {
		return x.Style
	}
	return nil
}

func (x *RankUnit) GetMainMetricData() *NumberData {
	if x != nil {
		return x.MainMetricData
	}
	return nil
}

func (x *RankUnit) GetSecondaryMetricData() *NumberData {
	if x != nil {
		return x.SecondaryMetricData
	}
	return nil
}

// A MoeGo insight
type MoeGoInsight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The title of the insight
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// The description of the insight:
	DescriptionPattern string `protobuf:"bytes,2,opt,name=description_pattern,json=descriptionPattern,proto3" json:"description_pattern,omitempty"`
	// The values of the insight description
	Values []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *MoeGoInsight) Reset() {
	*x = MoeGoInsight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoeGoInsight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoeGoInsight) ProtoMessage() {}

func (x *MoeGoInsight) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoeGoInsight.ProtoReflect.Descriptor instead.
func (*MoeGoInsight) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{6}
}

func (x *MoeGoInsight) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MoeGoInsight) GetDescriptionPattern() string {
	if x != nil {
		return x.DescriptionPattern
	}
	return ""
}

func (x *MoeGoInsight) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// Bar data
type BarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The groups of the bar data
	Groups []*BarGroupData `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *BarData) Reset() {
	*x = BarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarData) ProtoMessage() {}

func (x *BarData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarData.ProtoReflect.Descriptor instead.
func (*BarData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{7}
}

func (x *BarData) GetGroups() []*BarGroupData {
	if x != nil {
		return x.Groups
	}
	return nil
}

// Bar group data
type BarGroupData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the group
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	// numbers
	Numbers []*NumberData `protobuf:"bytes,2,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// Style config of the line
	Style *StyleConfig `protobuf:"bytes,3,opt,name=style,proto3,oneof" json:"style,omitempty"`
}

func (x *BarGroupData) Reset() {
	*x = BarGroupData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarGroupData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarGroupData) ProtoMessage() {}

func (x *BarGroupData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarGroupData.ProtoReflect.Descriptor instead.
func (*BarGroupData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{8}
}

func (x *BarGroupData) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *BarGroupData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *BarGroupData) GetStyle() *StyleConfig {
	if x != nil {
		return x.Style
	}
	return nil
}

// Pie data
type PieData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The title of the pie data
	Numbers []*NumberData `protobuf:"bytes,1,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// The total of the pie data
	Total *NumberData `protobuf:"bytes,2,opt,name=total,proto3,oneof" json:"total,omitempty"`
}

func (x *PieData) Reset() {
	*x = PieData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PieData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PieData) ProtoMessage() {}

func (x *PieData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PieData.ProtoReflect.Descriptor instead.
func (*PieData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{9}
}

func (x *PieData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *PieData) GetTotal() *NumberData {
	if x != nil {
		return x.Total
	}
	return nil
}

// Funnel data
type FunnelData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The title of the funnel data
	Numbers []*NumberData `protobuf:"bytes,1,rep,name=numbers,proto3" json:"numbers,omitempty"`
}

func (x *FunnelData) Reset() {
	*x = FunnelData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelData) ProtoMessage() {}

func (x *FunnelData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelData.ProtoReflect.Descriptor instead.
func (*FunnelData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{10}
}

func (x *FunnelData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

// Table data
type TableData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The title of the table
	Rows []*TableRowData `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	// Fields of the table, fields from TableMeta or Dynamic generated fields
	Fields []*Field `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
}

func (x *TableData) Reset() {
	*x = TableData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableData) ProtoMessage() {}

func (x *TableData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableData.ProtoReflect.Descriptor instead.
func (*TableData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{11}
}

func (x *TableData) GetRows() []*TableRowData {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *TableData) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

// Table row data
type TableRowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The data of one row
	Data map[string]*NumberData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// drill config of one row
	DrillConfig *DrillConfig `protobuf:"bytes,2,opt,name=drill_config,json=drillConfig,proto3,oneof" json:"drill_config,omitempty"`
	// sub data for multi dimension table
	SubData *TableData `protobuf:"bytes,3,opt,name=sub_data,json=subData,proto3,oneof" json:"sub_data,omitempty"`
}

func (x *TableRowData) Reset() {
	*x = TableRowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TableRowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableRowData) ProtoMessage() {}

func (x *TableRowData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableRowData.ProtoReflect.Descriptor instead.
func (*TableRowData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{12}
}

func (x *TableRowData) GetData() map[string]*NumberData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TableRowData) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *TableRowData) GetSubData() *TableData {
	if x != nil {
		return x.SubData
	}
	return nil
}

// A rank data
type RankData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of the rank data
	Ranks []*RankUnit `protobuf:"bytes,1,rep,name=ranks,proto3" json:"ranks,omitempty"`
}

func (x *RankData) Reset() {
	*x = RankData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankData) ProtoMessage() {}

func (x *RankData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankData.ProtoReflect.Descriptor instead.
func (*RankData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{13}
}

func (x *RankData) GetRanks() []*RankUnit {
	if x != nil {
		return x.Ranks
	}
	return nil
}

// Step unit of Procedure number data
type StepUnitData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data lead to step, if it's the first one, lead data might be empty
	LeadData *NumberData `protobuf:"bytes,1,opt,name=lead_data,json=leadData,proto3,oneof" json:"lead_data,omitempty"`
	// step data
	StepData *NumberData `protobuf:"bytes,2,opt,name=step_data,json=stepData,proto3" json:"step_data,omitempty"`
}

func (x *StepUnitData) Reset() {
	*x = StepUnitData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StepUnitData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StepUnitData) ProtoMessage() {}

func (x *StepUnitData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StepUnitData.ProtoReflect.Descriptor instead.
func (*StepUnitData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{14}
}

func (x *StepUnitData) GetLeadData() *NumberData {
	if x != nil {
		return x.LeadData
	}
	return nil
}

func (x *StepUnitData) GetStepData() *NumberData {
	if x != nil {
		return x.StepData
	}
	return nil
}

// Procedure number data
type ProcedureNumberData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// step data list
	Steps []*StepUnitData `protobuf:"bytes,1,rep,name=steps,proto3" json:"steps,omitempty"`
}

func (x *ProcedureNumberData) Reset() {
	*x = ProcedureNumberData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcedureNumberData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcedureNumberData) ProtoMessage() {}

func (x *ProcedureNumberData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcedureNumberData.ProtoReflect.Descriptor instead.
func (*ProcedureNumberData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{15}
}

func (x *ProcedureNumberData) GetSteps() []*StepUnitData {
	if x != nil {
		return x.Steps
	}
	return nil
}

// Number array data
type NumberArrayData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The numbers of the number array data
	Numbers []*NumberData `protobuf:"bytes,1,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// Title to display
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Description or tips
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Style config
	Style *StyleConfig `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
}

func (x *NumberArrayData) Reset() {
	*x = NumberArrayData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberArrayData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberArrayData) ProtoMessage() {}

func (x *NumberArrayData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberArrayData.ProtoReflect.Descriptor instead.
func (*NumberArrayData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{16}
}

func (x *NumberArrayData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *NumberArrayData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *NumberArrayData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NumberArrayData) GetStyle() *StyleConfig {
	if x != nil {
		return x.Style
	}
	return nil
}

// Line group data, contributed to the line chart
type LineGroupData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the group
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	// The line data
	Numbers []*NumberData `protobuf:"bytes,2,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// Style config of the line
	Style *StyleConfig `protobuf:"bytes,3,opt,name=style,proto3,oneof" json:"style,omitempty"`
}

func (x *LineGroupData) Reset() {
	*x = LineGroupData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineGroupData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineGroupData) ProtoMessage() {}

func (x *LineGroupData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineGroupData.ProtoReflect.Descriptor instead.
func (*LineGroupData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{17}
}

func (x *LineGroupData) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *LineGroupData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *LineGroupData) GetStyle() *StyleConfig {
	if x != nil {
		return x.Style
	}
	return nil
}

// Line chart data
type LineData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group list: One group represents a line in the line chart
	Groups []*LineGroupData `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *LineData) Reset() {
	*x = LineData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineData) ProtoMessage() {}

func (x *LineData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineData.ProtoReflect.Descriptor instead.
func (*LineData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{18}
}

func (x *LineData) GetGroups() []*LineGroupData {
	if x != nil {
		return x.Groups
	}
	return nil
}

// Odometer data
type OdometerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// odometer's right value
	MeasureMetric *NumberData `protobuf:"bytes,1,opt,name=measure_metric,json=measureMetric,proto3" json:"measure_metric,omitempty"`
	// odometer's left value
	IndicatorMetric *NumberData `protobuf:"bytes,2,opt,name=indicator_metric,json=indicatorMetric,proto3" json:"indicator_metric,omitempty"`
	// odometer's icon
	MainIcon *NumberData `protobuf:"bytes,3,opt,name=main_icon,json=mainIcon,proto3" json:"main_icon,omitempty"`
	// odometer's center value
	MainMetric *NumberData `protobuf:"bytes,4,opt,name=main_metric,json=mainMetric,proto3" json:"main_metric,omitempty"`
	// character's first name
	FirstName *NumberData `protobuf:"bytes,5,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// character's last name
	LastName *NumberData `protobuf:"bytes,6,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *OdometerData) Reset() {
	*x = OdometerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OdometerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OdometerData) ProtoMessage() {}

func (x *OdometerData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OdometerData.ProtoReflect.Descriptor instead.
func (*OdometerData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{19}
}

func (x *OdometerData) GetMeasureMetric() *NumberData {
	if x != nil {
		return x.MeasureMetric
	}
	return nil
}

func (x *OdometerData) GetIndicatorMetric() *NumberData {
	if x != nil {
		return x.IndicatorMetric
	}
	return nil
}

func (x *OdometerData) GetMainIcon() *NumberData {
	if x != nil {
		return x.MainIcon
	}
	return nil
}

func (x *OdometerData) GetMainMetric() *NumberData {
	if x != nil {
		return x.MainMetric
	}
	return nil
}

func (x *OdometerData) GetFirstName() *NumberData {
	if x != nil {
		return x.FirstName
	}
	return nil
}

func (x *OdometerData) GetLastName() *NumberData {
	if x != nil {
		return x.LastName
	}
	return nil
}

// Rating star data
type RatingStarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rating star value
	RatingStar *NumberData `protobuf:"bytes,1,opt,name=rating_star,json=ratingStar,proto3" json:"rating_star,omitempty"`
}

func (x *RatingStarData) Reset() {
	*x = RatingStarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingStarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingStarData) ProtoMessage() {}

func (x *RatingStarData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingStarData.ProtoReflect.Descriptor instead.
func (*RatingStarData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{20}
}

func (x *RatingStarData) GetRatingStar() *NumberData {
	if x != nil {
		return x.RatingStar
	}
	return nil
}

// Number list data, same as number array, display as rows
type NumberListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Data of the number list
	Numbers []*NumberData `protobuf:"bytes,1,rep,name=numbers,proto3" json:"numbers,omitempty"`
}

func (x *NumberListData) Reset() {
	*x = NumberListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberListData) ProtoMessage() {}

func (x *NumberListData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberListData.ProtoReflect.Descriptor instead.
func (*NumberListData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{21}
}

func (x *NumberListData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

// Legend data
type LegendData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number list
	Numbers []*NumberData `protobuf:"bytes,1,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// Total value of legend data
	Total *NumberData `protobuf:"bytes,2,opt,name=total,proto3,oneof" json:"total,omitempty"`
}

func (x *LegendData) Reset() {
	*x = LegendData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LegendData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegendData) ProtoMessage() {}

func (x *LegendData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_diagram_model_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegendData.ProtoReflect.Descriptor instead.
func (*LegendData) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP(), []int{22}
}

func (x *LegendData) GetNumbers() []*NumberData {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *LegendData) GetTotal() *NumberData {
	if x != nil {
		return x.Total
	}
	return nil
}

var File_moego_models_reporting_v2_diagram_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_diagram_model_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0c,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x4b, 0x50, 0x49, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x9b, 0x02,
	0x0a, 0x15, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3a, 0x0a, 0x11, 0x73, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x0f, 0x73, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b,
	0x65, 0x79, 0x73, 0x12, 0x3a, 0x0a, 0x11, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x0f,
	0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12,
	0x3b, 0x0a, 0x04, 0x6b, 0x70, 0x69, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x4b, 0x50, 0x49, 0x52, 0x04, 0x6b, 0x70, 0x69, 0x73, 0x12, 0x4d, 0x0a, 0x0d,
	0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0c, 0x73,
	0x61, 0x76, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x83, 0x06, 0x0a, 0x09,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64,
	0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x5d, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x49, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x64,
	0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x0a, 0x1b, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f,
	0x77, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x3f,
	0x0a, 0x1c, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x5f, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x73, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72,
	0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x30, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x69, 0x6d,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6d,
	0x61, 0x78, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0xb0, 0x08, 0x0a, 0x0b, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x48, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x08, 0x62, 0x61,
	0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x07, 0x62, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x08, 0x70,
	0x69, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x07, 0x70, 0x69, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x12,
	0x72, 0x61, 0x69, 0x6e, 0x62, 0x6f, 0x77, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x10, 0x72, 0x61, 0x69, 0x6e, 0x62, 0x6f, 0x77, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x42, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x72, 0x61,
	0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x64,
	0x75, 0x72, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75,
	0x72, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x0b,
	0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x75,
	0x6e, 0x6e, 0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x75, 0x6e, 0x6e,
	0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x58, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x61, 0x72, 0x72, 0x61, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x41, 0x72, 0x72, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x72, 0x72, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x42, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x0d, 0x6f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x10, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x10, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0e, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x48, 0x0a, 0x0b, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x0a, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x87, 0x06, 0x0a, 0x0a, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79,
	0x12, 0x44, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4c,
	0x0a, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x36,
	0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52,
	0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x46, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x48, 0x03, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x41,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x04, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x4e, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48,
	0x05, 0x52, 0x0b, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01,
	0x01, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x06, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64,
	0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x95,
	0x04, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x41, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6d, 0x61,
	0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x50, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x01, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x02, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a,
	0x10, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0e,
	0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5e,
	0x0a, 0x15, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x03, 0x52, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6d, 0x0a, 0x0c, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x49,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x13,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x4a, 0x0a, 0x07, 0x42, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3f, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x22, 0xbb, 0x01, 0x0a, 0x0c, 0x42, 0x61, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3f, 0x0a, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74,
	0x79, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22,
	0x96, 0x01, 0x0a, 0x07, 0x50, 0x69, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x07, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4d, 0x0a, 0x0a, 0x46, 0x75, 0x6e, 0x6e,
	0x65, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x72, 0x6f,
	0x77, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0xe9, 0x02, 0x0a,
	0x0c, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x01, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x1a, 0x5e, 0x0a, 0x09, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64,
	0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x73, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x45, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x61, 0x6e, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x22,
	0xa9, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x65, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x47, 0x0a, 0x09, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x6c, 0x65,
	0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x09, 0x73, 0x74, 0x65,
	0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x44, 0x61, 0x74, 0x61, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x13, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x3d, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74,
	0x65, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70,
	0x73, 0x22, 0xc8, 0x01, 0x0a, 0x0f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x72, 0x72, 0x61,
	0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22, 0xbc, 0x01, 0x0a,
	0x0d, 0x4c, 0x69, 0x6e, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a,
	0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x41,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22, 0x4c, 0x0a, 0x08, 0x4c,
	0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0xc4, 0x03, 0x0a, 0x0c, 0x4f, 0x64,
	0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x0e, 0x6d, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x6d, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x50, 0x0a, 0x10, 0x69, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x69, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x42, 0x0a, 0x09, 0x6d, 0x61,
	0x69, 0x6e, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x46,
	0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x6d, 0x61, 0x69, 0x6e,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x44, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x09,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x58, 0x0a, 0x0e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x46, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x72, 0x22, 0x51, 0x0a, 0x0e, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x07,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22, 0x99, 0x01,
	0x0a, 0x0a, 0x4c, 0x65, 0x67, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x07,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x08, 0x0a, 0x06, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x2a, 0xb1, 0x02, 0x0a, 0x0b, 0x44, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x41,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x52, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x49, 0x45, 0x10, 0x04,
	0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x49, 0x47, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x05,
	0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x06,
	0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x52,
	0x41, 0x4e, 0x4b, 0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x44, 0x55,
	0x52, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x4e,
	0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x52, 0x52, 0x41, 0x59, 0x10, 0x0a, 0x12, 0x08, 0x0a,
	0x04, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x0b, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x44, 0x4f, 0x4d, 0x45,
	0x54, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x54, 0x41, 0x52, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x0e, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41, 0x52, 0x5f, 0x56,
	0x32, 0x10, 0x0f, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x10, 0x10, 0x12,
	0x11, 0x0a, 0x0d, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x11, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x44, 0x49, 0x4d, 0x45,
	0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x12, 0x42, 0x81, 0x01,
	0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_diagram_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_diagram_model_proto_rawDescData = file_moego_models_reporting_v2_diagram_model_proto_rawDesc
)

func file_moego_models_reporting_v2_diagram_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_diagram_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_diagram_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_diagram_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_diagram_model_proto_rawDescData
}

var file_moego_models_reporting_v2_diagram_model_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_reporting_v2_diagram_model_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_models_reporting_v2_diagram_model_proto_goTypes = []interface{}{
	(DiagramType)(0),              // 0: moego.models.reporting.v2.DiagramType
	(*CustomizeKPI)(nil),          // 1: moego.models.reporting.v2.CustomizeKPI
	(*TableCustomizedConfig)(nil), // 2: moego.models.reporting.v2.TableCustomizedConfig
	(*TableMeta)(nil),             // 3: moego.models.reporting.v2.TableMeta
	(*DiagramData)(nil),           // 4: moego.models.reporting.v2.DiagramData
	(*NumberData)(nil),            // 5: moego.models.reporting.v2.NumberData
	(*RankUnit)(nil),              // 6: moego.models.reporting.v2.RankUnit
	(*MoeGoInsight)(nil),          // 7: moego.models.reporting.v2.MoeGoInsight
	(*BarData)(nil),               // 8: moego.models.reporting.v2.BarData
	(*BarGroupData)(nil),          // 9: moego.models.reporting.v2.BarGroupData
	(*PieData)(nil),               // 10: moego.models.reporting.v2.PieData
	(*FunnelData)(nil),            // 11: moego.models.reporting.v2.FunnelData
	(*TableData)(nil),             // 12: moego.models.reporting.v2.TableData
	(*TableRowData)(nil),          // 13: moego.models.reporting.v2.TableRowData
	(*RankData)(nil),              // 14: moego.models.reporting.v2.RankData
	(*StepUnitData)(nil),          // 15: moego.models.reporting.v2.StepUnitData
	(*ProcedureNumberData)(nil),   // 16: moego.models.reporting.v2.ProcedureNumberData
	(*NumberArrayData)(nil),       // 17: moego.models.reporting.v2.NumberArrayData
	(*LineGroupData)(nil),         // 18: moego.models.reporting.v2.LineGroupData
	(*LineData)(nil),              // 19: moego.models.reporting.v2.LineData
	(*OdometerData)(nil),          // 20: moego.models.reporting.v2.OdometerData
	(*RatingStarData)(nil),        // 21: moego.models.reporting.v2.RatingStarData
	(*NumberListData)(nil),        // 22: moego.models.reporting.v2.NumberListData
	(*LegendData)(nil),            // 23: moego.models.reporting.v2.LegendData
	nil,                           // 24: moego.models.reporting.v2.TableRowData.DataEntry
	(*FilterRequest)(nil),         // 25: moego.models.reporting.v2.FilterRequest
	(*Field)(nil),                 // 26: moego.models.reporting.v2.Field
	(*Filter)(nil),                // 27: moego.models.reporting.v2.Filter
	(*DrillConfig)(nil),           // 28: moego.models.reporting.v2.DrillConfig
	(*FilterGroup)(nil),           // 29: moego.models.reporting.v2.FilterGroup
	(Field_Type)(0),               // 30: moego.models.reporting.v2.Field.Type
	(*Value)(nil),                 // 31: moego.models.reporting.v2.Value
	(Trend)(0),                    // 32: moego.models.reporting.v2.Trend
	(*StyleConfig)(nil),           // 33: moego.models.reporting.v2.StyleConfig
	(*LinkageConfig)(nil),         // 34: moego.models.reporting.v2.LinkageConfig
}
var file_moego_models_reporting_v2_diagram_model_proto_depIdxs = []int32{
	1,  // 0: moego.models.reporting.v2.TableCustomizedConfig.kpis:type_name -> moego.models.reporting.v2.CustomizeKPI
	25, // 1: moego.models.reporting.v2.TableCustomizedConfig.saved_filters:type_name -> moego.models.reporting.v2.FilterRequest
	26, // 2: moego.models.reporting.v2.TableMeta.fields:type_name -> moego.models.reporting.v2.Field
	27, // 3: moego.models.reporting.v2.TableMeta.filters:type_name -> moego.models.reporting.v2.Filter
	2,  // 4: moego.models.reporting.v2.TableMeta.customized_config:type_name -> moego.models.reporting.v2.TableCustomizedConfig
	28, // 5: moego.models.reporting.v2.TableMeta.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	29, // 6: moego.models.reporting.v2.TableMeta.filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	5,  // 7: moego.models.reporting.v2.DiagramData.number_data:type_name -> moego.models.reporting.v2.NumberData
	8,  // 8: moego.models.reporting.v2.DiagramData.bar_data:type_name -> moego.models.reporting.v2.BarData
	10, // 9: moego.models.reporting.v2.DiagramData.pie_data:type_name -> moego.models.reporting.v2.PieData
	12, // 10: moego.models.reporting.v2.DiagramData.rainbow_table_data:type_name -> moego.models.reporting.v2.TableData
	14, // 11: moego.models.reporting.v2.DiagramData.rank_data:type_name -> moego.models.reporting.v2.RankData
	16, // 12: moego.models.reporting.v2.DiagramData.procedure_number_data:type_name -> moego.models.reporting.v2.ProcedureNumberData
	11, // 13: moego.models.reporting.v2.DiagramData.funnel_data:type_name -> moego.models.reporting.v2.FunnelData
	17, // 14: moego.models.reporting.v2.DiagramData.number_array_data:type_name -> moego.models.reporting.v2.NumberArrayData
	19, // 15: moego.models.reporting.v2.DiagramData.line_data:type_name -> moego.models.reporting.v2.LineData
	20, // 16: moego.models.reporting.v2.DiagramData.odometer_data:type_name -> moego.models.reporting.v2.OdometerData
	21, // 17: moego.models.reporting.v2.DiagramData.rating_star_data:type_name -> moego.models.reporting.v2.RatingStarData
	22, // 18: moego.models.reporting.v2.DiagramData.number_list_data:type_name -> moego.models.reporting.v2.NumberListData
	23, // 19: moego.models.reporting.v2.DiagramData.legend_data:type_name -> moego.models.reporting.v2.LegendData
	30, // 20: moego.models.reporting.v2.NumberData.field_type:type_name -> moego.models.reporting.v2.Field.Type
	31, // 21: moego.models.reporting.v2.NumberData.value:type_name -> moego.models.reporting.v2.Value
	31, // 22: moego.models.reporting.v2.NumberData.previous_value:type_name -> moego.models.reporting.v2.Value
	32, // 23: moego.models.reporting.v2.NumberData.trend:type_name -> moego.models.reporting.v2.Trend
	7,  // 24: moego.models.reporting.v2.NumberData.insight:type_name -> moego.models.reporting.v2.MoeGoInsight
	33, // 25: moego.models.reporting.v2.NumberData.style:type_name -> moego.models.reporting.v2.StyleConfig
	28, // 26: moego.models.reporting.v2.NumberData.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	34, // 27: moego.models.reporting.v2.NumberData.linkage_config:type_name -> moego.models.reporting.v2.LinkageConfig
	31, // 28: moego.models.reporting.v2.RankUnit.main_metric:type_name -> moego.models.reporting.v2.Value
	31, // 29: moego.models.reporting.v2.RankUnit.secondary_metric:type_name -> moego.models.reporting.v2.Value
	33, // 30: moego.models.reporting.v2.RankUnit.style:type_name -> moego.models.reporting.v2.StyleConfig
	5,  // 31: moego.models.reporting.v2.RankUnit.main_metric_data:type_name -> moego.models.reporting.v2.NumberData
	5,  // 32: moego.models.reporting.v2.RankUnit.secondary_metric_data:type_name -> moego.models.reporting.v2.NumberData
	9,  // 33: moego.models.reporting.v2.BarData.groups:type_name -> moego.models.reporting.v2.BarGroupData
	5,  // 34: moego.models.reporting.v2.BarGroupData.numbers:type_name -> moego.models.reporting.v2.NumberData
	33, // 35: moego.models.reporting.v2.BarGroupData.style:type_name -> moego.models.reporting.v2.StyleConfig
	5,  // 36: moego.models.reporting.v2.PieData.numbers:type_name -> moego.models.reporting.v2.NumberData
	5,  // 37: moego.models.reporting.v2.PieData.total:type_name -> moego.models.reporting.v2.NumberData
	5,  // 38: moego.models.reporting.v2.FunnelData.numbers:type_name -> moego.models.reporting.v2.NumberData
	13, // 39: moego.models.reporting.v2.TableData.rows:type_name -> moego.models.reporting.v2.TableRowData
	26, // 40: moego.models.reporting.v2.TableData.fields:type_name -> moego.models.reporting.v2.Field
	24, // 41: moego.models.reporting.v2.TableRowData.data:type_name -> moego.models.reporting.v2.TableRowData.DataEntry
	28, // 42: moego.models.reporting.v2.TableRowData.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	12, // 43: moego.models.reporting.v2.TableRowData.sub_data:type_name -> moego.models.reporting.v2.TableData
	6,  // 44: moego.models.reporting.v2.RankData.ranks:type_name -> moego.models.reporting.v2.RankUnit
	5,  // 45: moego.models.reporting.v2.StepUnitData.lead_data:type_name -> moego.models.reporting.v2.NumberData
	5,  // 46: moego.models.reporting.v2.StepUnitData.step_data:type_name -> moego.models.reporting.v2.NumberData
	15, // 47: moego.models.reporting.v2.ProcedureNumberData.steps:type_name -> moego.models.reporting.v2.StepUnitData
	5,  // 48: moego.models.reporting.v2.NumberArrayData.numbers:type_name -> moego.models.reporting.v2.NumberData
	33, // 49: moego.models.reporting.v2.NumberArrayData.style:type_name -> moego.models.reporting.v2.StyleConfig
	5,  // 50: moego.models.reporting.v2.LineGroupData.numbers:type_name -> moego.models.reporting.v2.NumberData
	33, // 51: moego.models.reporting.v2.LineGroupData.style:type_name -> moego.models.reporting.v2.StyleConfig
	18, // 52: moego.models.reporting.v2.LineData.groups:type_name -> moego.models.reporting.v2.LineGroupData
	5,  // 53: moego.models.reporting.v2.OdometerData.measure_metric:type_name -> moego.models.reporting.v2.NumberData
	5,  // 54: moego.models.reporting.v2.OdometerData.indicator_metric:type_name -> moego.models.reporting.v2.NumberData
	5,  // 55: moego.models.reporting.v2.OdometerData.main_icon:type_name -> moego.models.reporting.v2.NumberData
	5,  // 56: moego.models.reporting.v2.OdometerData.main_metric:type_name -> moego.models.reporting.v2.NumberData
	5,  // 57: moego.models.reporting.v2.OdometerData.first_name:type_name -> moego.models.reporting.v2.NumberData
	5,  // 58: moego.models.reporting.v2.OdometerData.last_name:type_name -> moego.models.reporting.v2.NumberData
	5,  // 59: moego.models.reporting.v2.RatingStarData.rating_star:type_name -> moego.models.reporting.v2.NumberData
	5,  // 60: moego.models.reporting.v2.NumberListData.numbers:type_name -> moego.models.reporting.v2.NumberData
	5,  // 61: moego.models.reporting.v2.LegendData.numbers:type_name -> moego.models.reporting.v2.NumberData
	5,  // 62: moego.models.reporting.v2.LegendData.total:type_name -> moego.models.reporting.v2.NumberData
	5,  // 63: moego.models.reporting.v2.TableRowData.DataEntry.value:type_name -> moego.models.reporting.v2.NumberData
	64, // [64:64] is the sub-list for method output_type
	64, // [64:64] is the sub-list for method input_type
	64, // [64:64] is the sub-list for extension type_name
	64, // [64:64] is the sub-list for extension extendee
	0,  // [0:64] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_diagram_model_proto_init() }
func file_moego_models_reporting_v2_diagram_model_proto_init() {
	if File_moego_models_reporting_v2_diagram_model_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_field_model_proto_init()
	file_moego_models_reporting_v2_filter_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizeKPI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableCustomizedConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiagramData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoeGoInsight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarGroupData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PieData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TableRowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StepUnitData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcedureNumberData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberArrayData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineGroupData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OdometerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingStarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_diagram_model_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LegendData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*DiagramData_NumberData)(nil),
		(*DiagramData_BarData)(nil),
		(*DiagramData_PieData)(nil),
		(*DiagramData_RainbowTableData)(nil),
		(*DiagramData_RankData)(nil),
		(*DiagramData_ProcedureNumberData)(nil),
		(*DiagramData_FunnelData)(nil),
		(*DiagramData_NumberArrayData)(nil),
		(*DiagramData_LineData)(nil),
		(*DiagramData_OdometerData)(nil),
		(*DiagramData_RatingStarData)(nil),
		(*DiagramData_NumberListData)(nil),
		(*DiagramData_LegendData)(nil),
	}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_diagram_model_proto_msgTypes[22].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_diagram_model_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_diagram_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_diagram_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_diagram_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_diagram_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_diagram_model_proto = out.File
	file_moego_models_reporting_v2_diagram_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_diagram_model_proto_goTypes = nil
	file_moego_models_reporting_v2_diagram_model_proto_depIdxs = nil
}
