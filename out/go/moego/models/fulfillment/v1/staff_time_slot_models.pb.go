// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/fulfillment/v1/staff_time_slot_models.proto

package fulfillmentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The Staff time slot model
type StaffTimeSlotModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the staff time slot
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The company business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Fulfillment id
	FulfillmentId int64 `protobuf:"varint,4,opt,name=fulfillment_id,json=fulfillmentId,proto3" json:"fulfillment_id,omitempty"`
	// Care type
	CareType v1.ServiceItemType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// The care type service detail ID
	DetailId int64 `protobuf:"varint,6,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`
	// The order line item ID
	OrderLineItemId int64 `protobuf:"varint,7,opt,name=order_line_item_id,json=orderLineItemId,proto3" json:"order_line_item_id,omitempty"`
	// The staff ID
	StaffId int64 `protobuf:"varint,8,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The pet ID
	PetId int64 `protobuf:"varint,9,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The customer ID
	CustomerId int64 `protobuf:"varint,10,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// The start datetime of the staff time slot
	StartDatetime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=start_datetime,json=startDatetime,proto3" json:"start_datetime,omitempty"`
	// The end datetime of the staff time slot
	EndDatetime int64 `protobuf:"varint,12,opt,name=end_datetime,json=endDatetime,proto3" json:"end_datetime,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
}

func (x *StaffTimeSlotModel) Reset() {
	*x = StaffTimeSlotModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffTimeSlotModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffTimeSlotModel) ProtoMessage() {}

func (x *StaffTimeSlotModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffTimeSlotModel.ProtoReflect.Descriptor instead.
func (*StaffTimeSlotModel) Descriptor() ([]byte, []int) {
	return file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescGZIP(), []int{0}
}

func (x *StaffTimeSlotModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffTimeSlotModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetFulfillmentId() int64 {
	if x != nil {
		return x.FulfillmentId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *StaffTimeSlotModel) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetOrderLineItemId() int64 {
	if x != nil {
		return x.OrderLineItemId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *StaffTimeSlotModel) GetStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDatetime
	}
	return nil
}

func (x *StaffTimeSlotModel) GetEndDatetime() int64 {
	if x != nil {
		return x.EndDatetime
	}
	return 0
}

func (x *StaffTimeSlotModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *StaffTimeSlotModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *StaffTimeSlotModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_moego_models_fulfillment_v1_staff_time_slot_models_proto protoreflect.FileDescriptor

var file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x05, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescOnce sync.Once
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescData = file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDesc
)

func file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescGZIP() []byte {
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescOnce.Do(func() {
		file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescData)
	})
	return file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDescData
}

var file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_fulfillment_v1_staff_time_slot_models_proto_goTypes = []interface{}{
	(*StaffTimeSlotModel)(nil),    // 0: moego.models.fulfillment.v1.StaffTimeSlotModel
	(v1.ServiceItemType)(0),       // 1: moego.models.offering.v1.ServiceItemType
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_moego_models_fulfillment_v1_staff_time_slot_models_proto_depIdxs = []int32{
	1, // 0: moego.models.fulfillment.v1.StaffTimeSlotModel.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	2, // 1: moego.models.fulfillment.v1.StaffTimeSlotModel.start_datetime:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.fulfillment.v1.StaffTimeSlotModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 3: moego.models.fulfillment.v1.StaffTimeSlotModel.updated_at:type_name -> google.protobuf.Timestamp
	2, // 4: moego.models.fulfillment.v1.StaffTimeSlotModel.deleted_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_fulfillment_v1_staff_time_slot_models_proto_init() }
func file_moego_models_fulfillment_v1_staff_time_slot_models_proto_init() {
	if File_moego_models_fulfillment_v1_staff_time_slot_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffTimeSlotModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_fulfillment_v1_staff_time_slot_models_proto_goTypes,
		DependencyIndexes: file_moego_models_fulfillment_v1_staff_time_slot_models_proto_depIdxs,
		MessageInfos:      file_moego_models_fulfillment_v1_staff_time_slot_models_proto_msgTypes,
	}.Build()
	File_moego_models_fulfillment_v1_staff_time_slot_models_proto = out.File
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_rawDesc = nil
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_goTypes = nil
	file_moego_models_fulfillment_v1_staff_time_slot_models_proto_depIdxs = nil
}
