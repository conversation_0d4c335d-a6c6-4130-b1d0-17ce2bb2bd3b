// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/staff_models.proto

package organizationpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status
type StaffModel_Status int32

const (
	// unspecified
	StaffModel_STAFF_STATUS_UNSPECIFIED StaffModel_Status = 0
	// normal
	StaffModel_NORMAL StaffModel_Status = 1
	// deleted
	StaffModel_DELETED StaffModel_Status = 2
	// migrated
	StaffModel_MIGRATED StaffModel_Status = 3
	// temporary
	StaffModel_TEMPORARY StaffModel_Status = 4 //
)

// Enum value maps for StaffModel_Status.
var (
	StaffModel_Status_name = map[int32]string{
		0: "STAFF_STATUS_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETED",
		3: "MIGRATED",
		4: "TEMPORARY",
	}
	StaffModel_Status_value = map[string]int32{
		"STAFF_STATUS_UNSPECIFIED": 0,
		"NORMAL":                   1,
		"DELETED":                  2,
		"MIGRATED":                 3,
		"TEMPORARY":                4,
	}
)

func (x StaffModel_Status) Enum() *StaffModel_Status {
	p := new(StaffModel_Status)
	*p = x
	return p
}

func (x StaffModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_models_proto_enumTypes[0].Descriptor()
}

func (StaffModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_models_proto_enumTypes[0]
}

func (x StaffModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffModel_Status.Descriptor instead.
func (StaffModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_models_proto_rawDescGZIP(), []int{0, 0}
}

// staff model
type StaffModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// is available for book online
	IsBookOnlineAvailable bool `protobuf:"varint,6,opt,name=is_book_online_available,json=isBookOnlineAvailable,proto3" json:"is_book_online_available,omitempty"`
	// is show on calendar
	IsShowOnCalendar bool `protobuf:"varint,7,opt,name=is_show_on_calendar,json=isShowOnCalendar,proto3" json:"is_show_on_calendar,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,8,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,9,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// employee category
	EmployeeCategory StaffEmployeeCategory `protobuf:"varint,11,opt,name=employee_category,json=employeeCategory,proto3,enum=moego.models.organization.v1.StaffEmployeeCategory" json:"employee_category,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,12,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// role id
	RoleId int64 `protobuf:"varint,13,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// hire date
	HireDate int64 `protobuf:"varint,14,opt,name=hire_date,json=hireDate,proto3" json:"hire_date,omitempty"`
	// note
	Note string `protobuf:"bytes,15,opt,name=note,proto3" json:"note,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,16,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// last visit business id
	LastVisitBusinessId int64 `protobuf:"varint,17,opt,name=last_visit_business_id,json=lastVisitBusinessId,proto3" json:"last_visit_business_id,omitempty"`
	// invite code
	InviteCode string `protobuf:"bytes,18,opt,name=invite_code,json=inviteCode,proto3" json:"invite_code,omitempty"`
	// access all working locations staffs
	AccessAllWorkingLocationsStaffs bool `protobuf:"varint,19,opt,name=access_all_working_locations_staffs,json=accessAllWorkingLocationsStaffs,proto3" json:"access_all_working_locations_staffs,omitempty"`
	// profile email
	ProfileEmail string `protobuf:"bytes,20,opt,name=profile_email,json=profileEmail,proto3" json:"profile_email,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,21,opt,name=sort,proto3" json:"sort,omitempty"`
	// access code
	AccessCode string `protobuf:"bytes,22,opt,name=access_code,json=accessCode,proto3" json:"access_code,omitempty"`
	// require access code
	RequireAccessCode bool `protobuf:"varint,23,opt,name=require_access_code,json=requireAccessCode,proto3" json:"require_access_code,omitempty"`
	// status
	Status StaffModel_Status `protobuf:"varint,24,opt,name=status,proto3,enum=moego.models.organization.v1.StaffModel_Status" json:"status,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// staff source
	Source StaffSource `protobuf:"varint,26,opt,name=source,proto3,enum=moego.models.organization.v1.StaffSource" json:"source,omitempty"`
	// preserved num before 50 for future use
	// working locations
	WorkingLocationList []*LocationBriefView `protobuf:"bytes,50,rep,name=working_location_list,json=workingLocationList,proto3" json:"working_location_list,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,51,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *StaffModel) Reset() {
	*x = StaffModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffModel) ProtoMessage() {}

func (x *StaffModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffModel.ProtoReflect.Descriptor instead.
func (*StaffModel) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_models_proto_rawDescGZIP(), []int{0}
}

func (x *StaffModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *StaffModel) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StaffModel) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *StaffModel) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *StaffModel) GetIsBookOnlineAvailable() bool {
	if x != nil {
		return x.IsBookOnlineAvailable
	}
	return false
}

func (x *StaffModel) GetIsShowOnCalendar() bool {
	if x != nil {
		return x.IsShowOnCalendar
	}
	return false
}

func (x *StaffModel) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *StaffModel) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *StaffModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StaffModel) GetEmployeeCategory() StaffEmployeeCategory {
	if x != nil {
		return x.EmployeeCategory
	}
	return StaffEmployeeCategory_COMPANY_STAFF
}

func (x *StaffModel) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *StaffModel) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *StaffModel) GetHireDate() int64 {
	if x != nil {
		return x.HireDate
	}
	return 0
}

func (x *StaffModel) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *StaffModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *StaffModel) GetLastVisitBusinessId() int64 {
	if x != nil {
		return x.LastVisitBusinessId
	}
	return 0
}

func (x *StaffModel) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *StaffModel) GetAccessAllWorkingLocationsStaffs() bool {
	if x != nil {
		return x.AccessAllWorkingLocationsStaffs
	}
	return false
}

func (x *StaffModel) GetProfileEmail() string {
	if x != nil {
		return x.ProfileEmail
	}
	return ""
}

func (x *StaffModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *StaffModel) GetAccessCode() string {
	if x != nil {
		return x.AccessCode
	}
	return ""
}

func (x *StaffModel) GetRequireAccessCode() bool {
	if x != nil {
		return x.RequireAccessCode
	}
	return false
}

func (x *StaffModel) GetStatus() StaffModel_Status {
	if x != nil {
		return x.Status
	}
	return StaffModel_STAFF_STATUS_UNSPECIFIED
}

func (x *StaffModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *StaffModel) GetSource() StaffSource {
	if x != nil {
		return x.Source
	}
	return StaffSource_STAFF_SOURCE_UNSPECIFIED
}

func (x *StaffModel) GetWorkingLocationList() []*LocationBriefView {
	if x != nil {
		return x.WorkingLocationList
	}
	return nil
}

func (x *StaffModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// staff model brief view
type StaffBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// employee category
	EmployeeCategory StaffEmployeeCategory `protobuf:"varint,4,opt,name=employee_category,json=employeeCategory,proto3,enum=moego.models.organization.v1.StaffEmployeeCategory" json:"employee_category,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,5,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *StaffBriefView) Reset() {
	*x = StaffBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffBriefView) ProtoMessage() {}

func (x *StaffBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffBriefView.ProtoReflect.Descriptor instead.
func (*StaffBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_models_proto_rawDescGZIP(), []int{1}
}

func (x *StaffBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffBriefView) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StaffBriefView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StaffBriefView) GetEmployeeCategory() StaffEmployeeCategory {
	if x != nil {
		return x.EmployeeCategory
	}
	return StaffEmployeeCategory_COMPANY_STAFF
}

func (x *StaffBriefView) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// view for staff list, with basic info for staff
type StaffBasicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// is available for book online
	IsBookOnlineAvailable bool `protobuf:"varint,6,opt,name=is_book_online_available,json=isBookOnlineAvailable,proto3" json:"is_book_online_available,omitempty"`
	// is show on calendar
	IsShowOnCalendar bool `protobuf:"varint,7,opt,name=is_show_on_calendar,json=isShowOnCalendar,proto3" json:"is_show_on_calendar,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,8,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,9,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// employee category
	EmployeeCategory StaffEmployeeCategory `protobuf:"varint,11,opt,name=employee_category,json=employeeCategory,proto3,enum=moego.models.organization.v1.StaffEmployeeCategory" json:"employee_category,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,12,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// role id
	RoleId int64 `protobuf:"varint,13,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// hire date
	HireDate int64 `protobuf:"varint,14,opt,name=hire_date,json=hireDate,proto3" json:"hire_date,omitempty"`
	// note
	Note string `protobuf:"bytes,15,opt,name=note,proto3" json:"note,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,16,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// last visit business id
	LastVisitBusinessId int64 `protobuf:"varint,17,opt,name=last_visit_business_id,json=lastVisitBusinessId,proto3" json:"last_visit_business_id,omitempty"`
	// invite code
	InviteCode string `protobuf:"bytes,18,opt,name=invite_code,json=inviteCode,proto3" json:"invite_code,omitempty"`
	// access all working locations staffs
	AccessAllWorkingLocationsStaffs bool `protobuf:"varint,19,opt,name=access_all_working_locations_staffs,json=accessAllWorkingLocationsStaffs,proto3" json:"access_all_working_locations_staffs,omitempty"`
	// profile email
	ProfileEmail string `protobuf:"bytes,20,opt,name=profile_email,json=profileEmail,proto3" json:"profile_email,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,21,opt,name=sort,proto3" json:"sort,omitempty"`
	// access code
	AccessCode string `protobuf:"bytes,22,opt,name=access_code,json=accessCode,proto3" json:"access_code,omitempty"`
	// require access code
	RequireAccessCode bool `protobuf:"varint,23,opt,name=require_access_code,json=requireAccessCode,proto3" json:"require_access_code,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,24,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *StaffBasicView) Reset() {
	*x = StaffBasicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffBasicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffBasicView) ProtoMessage() {}

func (x *StaffBasicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffBasicView.ProtoReflect.Descriptor instead.
func (*StaffBasicView) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_models_proto_rawDescGZIP(), []int{2}
}

func (x *StaffBasicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffBasicView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *StaffBasicView) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StaffBasicView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *StaffBasicView) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *StaffBasicView) GetIsBookOnlineAvailable() bool {
	if x != nil {
		return x.IsBookOnlineAvailable
	}
	return false
}

func (x *StaffBasicView) GetIsShowOnCalendar() bool {
	if x != nil {
		return x.IsShowOnCalendar
	}
	return false
}

func (x *StaffBasicView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *StaffBasicView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *StaffBasicView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StaffBasicView) GetEmployeeCategory() StaffEmployeeCategory {
	if x != nil {
		return x.EmployeeCategory
	}
	return StaffEmployeeCategory_COMPANY_STAFF
}

func (x *StaffBasicView) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *StaffBasicView) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *StaffBasicView) GetHireDate() int64 {
	if x != nil {
		return x.HireDate
	}
	return 0
}

func (x *StaffBasicView) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *StaffBasicView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *StaffBasicView) GetLastVisitBusinessId() int64 {
	if x != nil {
		return x.LastVisitBusinessId
	}
	return 0
}

func (x *StaffBasicView) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *StaffBasicView) GetAccessAllWorkingLocationsStaffs() bool {
	if x != nil {
		return x.AccessAllWorkingLocationsStaffs
	}
	return false
}

func (x *StaffBasicView) GetProfileEmail() string {
	if x != nil {
		return x.ProfileEmail
	}
	return ""
}

func (x *StaffBasicView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *StaffBasicView) GetAccessCode() string {
	if x != nil {
		return x.AccessCode
	}
	return ""
}

func (x *StaffBasicView) GetRequireAccessCode() bool {
	if x != nil {
		return x.RequireAccessCode
	}
	return false
}

func (x *StaffBasicView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// staff login time model
type StaffLoginTimeModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// login limit type
	LoginLimitType StaffLoginLimitType `protobuf:"varint,1,opt,name=login_limit_type,json=loginLimitType,proto3,enum=moego.models.organization.v1.StaffLoginLimitType" json:"login_limit_type,omitempty"`
	// accurate to the minute now
	TimeRange *v1.TimeOfDayInterval `protobuf:"bytes,2,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *StaffLoginTimeModel) Reset() {
	*x = StaffLoginTimeModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffLoginTimeModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffLoginTimeModel) ProtoMessage() {}

func (x *StaffLoginTimeModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffLoginTimeModel.ProtoReflect.Descriptor instead.
func (*StaffLoginTimeModel) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_models_proto_rawDescGZIP(), []int{3}
}

func (x *StaffLoginTimeModel) GetLoginLimitType() StaffLoginLimitType {
	if x != nil {
		return x.LoginLimitType
	}
	return StaffLoginLimitType_LOGIN_LIMIT_TYPE_UNSPECIFIED
}

func (x *StaffLoginTimeModel) GetTimeRange() *v1.TimeOfDayInterval {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

var File_moego_models_organization_v1_staff_models_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_staff_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xac, 0x0a, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x37,
	0x0a, 0x18, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x60, 0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x10, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x68, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x74, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4c, 0x0a, 0x23, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6c, 0x6c, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x47, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x32, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x33, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x5c, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x54,
	0x41, 0x46, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d,
	0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x0d, 0x0a, 0x09, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41, 0x52, 0x59, 0x10, 0x04, 0x22, 0xe5,
	0x01, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x60, 0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x10, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0xa4, 0x07, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x42,
	0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6f, 0x6e,
	0x5f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x11,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x68, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x33, 0x0a, 0x16,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x6c, 0x61,
	0x73, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x4c, 0x0a, 0x23, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x6c, 0x6c,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x1f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xb4, 0x01,
	0x0a, 0x13, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x5b, 0x0a, 0x10, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61,
	0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_staff_models_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_staff_models_proto_rawDescData = file_moego_models_organization_v1_staff_models_proto_rawDesc
)

func file_moego_models_organization_v1_staff_models_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_staff_models_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_staff_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_staff_models_proto_rawDescData)
	})
	return file_moego_models_organization_v1_staff_models_proto_rawDescData
}

var file_moego_models_organization_v1_staff_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_organization_v1_staff_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_organization_v1_staff_models_proto_goTypes = []interface{}{
	(StaffModel_Status)(0),        // 0: moego.models.organization.v1.StaffModel.Status
	(*StaffModel)(nil),            // 1: moego.models.organization.v1.StaffModel
	(*StaffBriefView)(nil),        // 2: moego.models.organization.v1.StaffBriefView
	(*StaffBasicView)(nil),        // 3: moego.models.organization.v1.StaffBasicView
	(*StaffLoginTimeModel)(nil),   // 4: moego.models.organization.v1.StaffLoginTimeModel
	(StaffEmployeeCategory)(0),    // 5: moego.models.organization.v1.StaffEmployeeCategory
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(StaffSource)(0),              // 7: moego.models.organization.v1.StaffSource
	(*LocationBriefView)(nil),     // 8: moego.models.organization.v1.LocationBriefView
	(StaffLoginLimitType)(0),      // 9: moego.models.organization.v1.StaffLoginLimitType
	(*v1.TimeOfDayInterval)(nil),  // 10: moego.utils.v1.TimeOfDayInterval
}
var file_moego_models_organization_v1_staff_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.organization.v1.StaffModel.employee_category:type_name -> moego.models.organization.v1.StaffEmployeeCategory
	0,  // 1: moego.models.organization.v1.StaffModel.status:type_name -> moego.models.organization.v1.StaffModel.Status
	6,  // 2: moego.models.organization.v1.StaffModel.update_time:type_name -> google.protobuf.Timestamp
	7,  // 3: moego.models.organization.v1.StaffModel.source:type_name -> moego.models.organization.v1.StaffSource
	8,  // 4: moego.models.organization.v1.StaffModel.working_location_list:type_name -> moego.models.organization.v1.LocationBriefView
	5,  // 5: moego.models.organization.v1.StaffBriefView.employee_category:type_name -> moego.models.organization.v1.StaffEmployeeCategory
	5,  // 6: moego.models.organization.v1.StaffBasicView.employee_category:type_name -> moego.models.organization.v1.StaffEmployeeCategory
	9,  // 7: moego.models.organization.v1.StaffLoginTimeModel.login_limit_type:type_name -> moego.models.organization.v1.StaffLoginLimitType
	10, // 8: moego.models.organization.v1.StaffLoginTimeModel.time_range:type_name -> moego.utils.v1.TimeOfDayInterval
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_staff_models_proto_init() }
func file_moego_models_organization_v1_staff_models_proto_init() {
	if File_moego_models_organization_v1_staff_models_proto != nil {
		return
	}
	file_moego_models_organization_v1_location_models_proto_init()
	file_moego_models_organization_v1_staff_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_staff_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffBasicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffLoginTimeModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_staff_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_staff_models_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_staff_models_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_staff_models_proto_enumTypes,
		MessageInfos:      file_moego_models_organization_v1_staff_models_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_staff_models_proto = out.File
	file_moego_models_organization_v1_staff_models_proto_rawDesc = nil
	file_moego_models_organization_v1_staff_models_proto_goTypes = nil
	file_moego_models_organization_v1_staff_models_proto_depIdxs = nil
}
