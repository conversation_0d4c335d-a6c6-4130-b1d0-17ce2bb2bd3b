// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/migrate_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// migrate status enums
type MigrateStatus int32

const (
	// UNSPECIFIED
	MigrateStatus_MIGRATE_STATUS_UNSPECIFIED MigrateStatus = 0
	// UN_MIGRATED
	MigrateStatus_UN_MIGRATED MigrateStatus = 1
	// MIGRATING
	MigrateStatus_MIGRATING MigrateStatus = 2
	// MIGRATED
	MigrateStatus_MIGRATED MigrateStatus = 3
)

// Enum value maps for MigrateStatus.
var (
	MigrateStatus_name = map[int32]string{
		0: "MIGRATE_STATUS_UNSPECIFIED",
		1: "UN_MIGRATED",
		2: "MIGRATING",
		3: "MIGRATED",
	}
	MigrateStatus_value = map[string]int32{
		"MIGRATE_STATUS_UNSPECIFIED": 0,
		"UN_MIGRATED":                1,
		"MIGRATING":                  2,
		"MIGRATED":                   3,
	}
)

func (x MigrateStatus) Enum() *MigrateStatus {
	p := new(MigrateStatus)
	*p = x
	return p
}

func (x MigrateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MigrateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_migrate_enums_proto_enumTypes[0].Descriptor()
}

func (MigrateStatus) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_migrate_enums_proto_enumTypes[0]
}

func (x MigrateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MigrateStatus.Descriptor instead.
func (MigrateStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_migrate_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_organization_v1_migrate_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_migrate_enums_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2a, 0x5d, 0x0a, 0x0d, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x42,
	0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_migrate_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_migrate_enums_proto_rawDescData = file_moego_models_organization_v1_migrate_enums_proto_rawDesc
)

func file_moego_models_organization_v1_migrate_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_migrate_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_migrate_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_migrate_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_migrate_enums_proto_rawDescData
}

var file_moego_models_organization_v1_migrate_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_organization_v1_migrate_enums_proto_goTypes = []interface{}{
	(MigrateStatus)(0), // 0: moego.models.organization.v1.MigrateStatus
}
var file_moego_models_organization_v1_migrate_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_migrate_enums_proto_init() }
func file_moego_models_organization_v1_migrate_enums_proto_init() {
	if File_moego_models_organization_v1_migrate_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_migrate_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_migrate_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_migrate_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_migrate_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_migrate_enums_proto = out.File
	file_moego_models_organization_v1_migrate_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_migrate_enums_proto_goTypes = nil
	file_moego_models_organization_v1_migrate_enums_proto_depIdxs = nil
}
