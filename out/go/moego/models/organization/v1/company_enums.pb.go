// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/company_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// unit of weight
type WeightUnit int32

const (
	// UNSPECIFIED
	WeightUnit_WEIGHT_UNIT_UNSPECIFIED WeightUnit = 0
	// pound
	WeightUnit_POUND WeightUnit = 1
	// kilogram
	WeightUnit_KILOGRAM WeightUnit = 2
)

// Enum value maps for WeightUnit.
var (
	WeightUnit_name = map[int32]string{
		0: "WEIGHT_UNIT_UNSPECIFIED",
		1: "POUND",
		2: "KILOGRAM",
	}
	WeightUnit_value = map[string]int32{
		"WEIGHT_UNIT_UNSPECIFIED": 0,
		"POUND":                   1,
		"KILOGRAM":                2,
	}
)

func (x WeightUnit) Enum() *WeightUnit {
	p := new(WeightUnit)
	*p = x
	return p
}

func (x WeightUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WeightUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_company_enums_proto_enumTypes[0].Descriptor()
}

func (WeightUnit) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_company_enums_proto_enumTypes[0]
}

func (x WeightUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WeightUnit.Descriptor instead.
func (WeightUnit) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_enums_proto_rawDescGZIP(), []int{0}
}

// unit of distance
type DistanceUnit int32

const (
	// unspecified
	DistanceUnit_DISTANCE_UNIT_UNSPECIFIED DistanceUnit = 0
	// mile
	DistanceUnit_MILE DistanceUnit = 1
	// kilometer
	DistanceUnit_KILOMETER DistanceUnit = 2
)

// Enum value maps for DistanceUnit.
var (
	DistanceUnit_name = map[int32]string{
		0: "DISTANCE_UNIT_UNSPECIFIED",
		1: "MILE",
		2: "KILOMETER",
	}
	DistanceUnit_value = map[string]int32{
		"DISTANCE_UNIT_UNSPECIFIED": 0,
		"MILE":                      1,
		"KILOMETER":                 2,
	}
)

func (x DistanceUnit) Enum() *DistanceUnit {
	p := new(DistanceUnit)
	*p = x
	return p
}

func (x DistanceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DistanceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_company_enums_proto_enumTypes[1].Descriptor()
}

func (DistanceUnit) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_company_enums_proto_enumTypes[1]
}

func (x DistanceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DistanceUnit.Descriptor instead.
func (DistanceUnit) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_enums_proto_rawDescGZIP(), []int{1}
}

// date format
type DateFormat int32

const (
	// unspecified
	DateFormat_DATE_FORMAT_UNSPECIFIED DateFormat = 0
	// MM/DD/YYYY (e.g. 01/31/2024)
	DateFormat_MM_DD_YYYY_LINE DateFormat = 1
	// DD/MM/YYYY (e.g. 31/01/2024)
	DateFormat_DD_MM_YYYY_LINE DateFormat = 2
	// DD.MM.YYYY (e.g. 31.01.2024)
	DateFormat_DD_MM_YYYY_DOT DateFormat = 3
	// YYYY.MM.DD (e.g. 2024.01.31)
	DateFormat_YYYY_MM_DD_DOT DateFormat = 4
	// YYYY/MM/DD (e.g. 2024/01/31)
	DateFormat_YYYY_MM_DD_LINE DateFormat = 5
	// MMM DD/YYYY (e.g. Jan 31/2024)
	DateFormat_MMM_DD_YYYY_LINE DateFormat = 6
	// MM/DD/YY (e.g. 01/31/24)
	DateFormat_MM_DD_YY_LINE DateFormat = 7
)

// Enum value maps for DateFormat.
var (
	DateFormat_name = map[int32]string{
		0: "DATE_FORMAT_UNSPECIFIED",
		1: "MM_DD_YYYY_LINE",
		2: "DD_MM_YYYY_LINE",
		3: "DD_MM_YYYY_DOT",
		4: "YYYY_MM_DD_DOT",
		5: "YYYY_MM_DD_LINE",
		6: "MMM_DD_YYYY_LINE",
		7: "MM_DD_YY_LINE",
	}
	DateFormat_value = map[string]int32{
		"DATE_FORMAT_UNSPECIFIED": 0,
		"MM_DD_YYYY_LINE":         1,
		"DD_MM_YYYY_LINE":         2,
		"DD_MM_YYYY_DOT":          3,
		"YYYY_MM_DD_DOT":          4,
		"YYYY_MM_DD_LINE":         5,
		"MMM_DD_YYYY_LINE":        6,
		"MM_DD_YY_LINE":           7,
	}
)

func (x DateFormat) Enum() *DateFormat {
	p := new(DateFormat)
	*p = x
	return p
}

func (x DateFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_company_enums_proto_enumTypes[2].Descriptor()
}

func (DateFormat) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_company_enums_proto_enumTypes[2]
}

func (x DateFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateFormat.Descriptor instead.
func (DateFormat) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_enums_proto_rawDescGZIP(), []int{2}
}

// time format
type TimeFormat int32

const (
	// unspecified
	TimeFormat_TIME_FORMAT_UNSPECIFIED TimeFormat = 0
	// 24 hour
	TimeFormat_HOUR_24 TimeFormat = 1
	// 12 hour
	TimeFormat_HOUR_12 TimeFormat = 2
)

// Enum value maps for TimeFormat.
var (
	TimeFormat_name = map[int32]string{
		0: "TIME_FORMAT_UNSPECIFIED",
		1: "HOUR_24",
		2: "HOUR_12",
	}
	TimeFormat_value = map[string]int32{
		"TIME_FORMAT_UNSPECIFIED": 0,
		"HOUR_24":                 1,
		"HOUR_12":                 2,
	}
)

func (x TimeFormat) Enum() *TimeFormat {
	p := new(TimeFormat)
	*p = x
	return p
}

func (x TimeFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_company_enums_proto_enumTypes[3].Descriptor()
}

func (TimeFormat) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_company_enums_proto_enumTypes[3]
}

func (x TimeFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeFormat.Descriptor instead.
func (TimeFormat) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_organization_v1_company_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_company_enums_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2a, 0x42, 0x0a, 0x0a, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1b,
	0x0a, 0x17, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x49, 0x4c, 0x4f, 0x47, 0x52,
	0x41, 0x4d, 0x10, 0x02, 0x2a, 0x46, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x55, 0x6e, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x4b, 0x49, 0x4c, 0x4f, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x10, 0x02, 0x2a, 0xb9, 0x01, 0x0a,
	0x0a, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4d, 0x5f, 0x44,
	0x44, 0x5f, 0x59, 0x59, 0x59, 0x59, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x44, 0x5f, 0x4d, 0x4d, 0x5f, 0x59, 0x59, 0x59, 0x59, 0x5f, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x44, 0x5f, 0x4d, 0x4d, 0x5f, 0x59, 0x59, 0x59, 0x59,
	0x5f, 0x44, 0x4f, 0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x59, 0x59, 0x59, 0x59, 0x5f, 0x4d,
	0x4d, 0x5f, 0x44, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x59, 0x59,
	0x59, 0x59, 0x5f, 0x4d, 0x4d, 0x5f, 0x44, 0x44, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x05, 0x12,
	0x14, 0x0a, 0x10, 0x4d, 0x4d, 0x4d, 0x5f, 0x44, 0x44, 0x5f, 0x59, 0x59, 0x59, 0x59, 0x5f, 0x4c,
	0x49, 0x4e, 0x45, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4d, 0x5f, 0x44, 0x44, 0x5f, 0x59,
	0x59, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x07, 0x2a, 0x43, 0x0a, 0x0a, 0x54, 0x69, 0x6d, 0x65,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x4f, 0x55, 0x52, 0x5f, 0x32, 0x34, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x48, 0x4f, 0x55, 0x52, 0x5f, 0x31, 0x32, 0x10, 0x02, 0x42, 0x8a, 0x01,
	0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_company_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_company_enums_proto_rawDescData = file_moego_models_organization_v1_company_enums_proto_rawDesc
)

func file_moego_models_organization_v1_company_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_company_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_company_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_company_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_company_enums_proto_rawDescData
}

var file_moego_models_organization_v1_company_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_organization_v1_company_enums_proto_goTypes = []interface{}{
	(WeightUnit)(0),   // 0: moego.models.organization.v1.WeightUnit
	(DistanceUnit)(0), // 1: moego.models.organization.v1.DistanceUnit
	(DateFormat)(0),   // 2: moego.models.organization.v1.DateFormat
	(TimeFormat)(0),   // 3: moego.models.organization.v1.TimeFormat
}
var file_moego_models_organization_v1_company_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_company_enums_proto_init() }
func file_moego_models_organization_v1_company_enums_proto_init() {
	if File_moego_models_organization_v1_company_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_company_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_company_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_company_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_company_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_company_enums_proto = out.File
	file_moego_models_organization_v1_company_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_company_enums_proto_goTypes = nil
	file_moego_models_organization_v1_company_enums_proto_depIdxs = nil
}
