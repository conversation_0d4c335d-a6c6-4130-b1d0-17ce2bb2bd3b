// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/hubspot_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// company tier class
type TierEnums int32

const (
	// UNSPECIFIED
	TierEnums_TIER_UNSPECIFIED TierEnums = 0
	// tier 1
	TierEnums_TIER_1 TierEnums = 1
	// tier 2
	TierEnums_TIER_2 TierEnums = 2
	// tier 3
	TierEnums_TIER_3 TierEnums = 3
	// tier 4
	TierEnums_TIER_4 TierEnums = 4
)

// Enum value maps for TierEnums.
var (
	TierEnums_name = map[int32]string{
		0: "TIER_UNSPECIFIED",
		1: "TIER_1",
		2: "TIER_2",
		3: "TIER_3",
		4: "TIER_4",
	}
	TierEnums_value = map[string]int32{
		"TIER_UNSPECIFIED": 0,
		"TIER_1":           1,
		"TIER_2":           2,
		"TIER_3":           3,
		"TIER_4":           4,
	}
)

func (x TierEnums) Enum() *TierEnums {
	p := new(TierEnums)
	*p = x
	return p
}

func (x TierEnums) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TierEnums) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_hubspot_enums_proto_enumTypes[0].Descriptor()
}

func (TierEnums) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_hubspot_enums_proto_enumTypes[0]
}

func (x TierEnums) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TierEnums.Descriptor instead.
func (TierEnums) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_hubspot_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_organization_v1_hubspot_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_hubspot_enums_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x68,
	0x75, 0x62, 0x73, 0x70, 0x6f, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2a, 0x51, 0x0a, 0x09, 0x54, 0x69, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x31, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x54,
	0x49, 0x45, 0x52, 0x5f, 0x33, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x49, 0x45, 0x52, 0x5f,
	0x34, 0x10, 0x04, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_hubspot_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_hubspot_enums_proto_rawDescData = file_moego_models_organization_v1_hubspot_enums_proto_rawDesc
)

func file_moego_models_organization_v1_hubspot_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_hubspot_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_hubspot_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_hubspot_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_hubspot_enums_proto_rawDescData
}

var file_moego_models_organization_v1_hubspot_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_organization_v1_hubspot_enums_proto_goTypes = []interface{}{
	(TierEnums)(0), // 0: moego.models.organization.v1.TierEnums
}
var file_moego_models_organization_v1_hubspot_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_hubspot_enums_proto_init() }
func file_moego_models_organization_v1_hubspot_enums_proto_init() {
	if File_moego_models_organization_v1_hubspot_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_hubspot_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_hubspot_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_hubspot_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_hubspot_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_hubspot_enums_proto = out.File
	file_moego_models_organization_v1_hubspot_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_hubspot_enums_proto_goTypes = nil
	file_moego_models_organization_v1_hubspot_enums_proto_depIdxs = nil
}
