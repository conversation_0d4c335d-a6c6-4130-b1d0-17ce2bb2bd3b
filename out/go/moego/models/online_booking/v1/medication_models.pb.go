// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/medication_models.proto

package onlinebookingpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Stores information about medication events.
type MedicationModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The primary key identifier for each medication event.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The booking request identifier.
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The service detail identifier.
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// service detail type, 2: boarding, 3: daycare
	ServiceDetailType v1.ServiceItemType `protobuf:"varint,4,opt,name=service_detail_type,json=serviceDetailType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_detail_type,omitempty"`
	// Medication time.
	Time []*MedicationModel_MedicationSchedule `protobuf:"bytes,5,rep,name=time,proto3" json:"time,omitempty"`
	// Medication amount, must be greater than 0.
	Amount float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// Medication unit.
	Unit string `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	// Medication name.
	MedicationName string `protobuf:"bytes,8,opt,name=medication_name,json=medicationName,proto3" json:"medication_name,omitempty"`
	// Additional notes about the medication.
	Notes string `protobuf:"bytes,9,opt,name=notes,proto3" json:"notes,omitempty"`
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Medication amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,12,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
	// Medication select date
	SelectedDate *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,13,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
}

func (x *MedicationModel) Reset() {
	*x = MedicationModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_medication_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MedicationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicationModel) ProtoMessage() {}

func (x *MedicationModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_medication_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicationModel.ProtoReflect.Descriptor instead.
func (*MedicationModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_medication_models_proto_rawDescGZIP(), []int{0}
}

func (x *MedicationModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MedicationModel) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *MedicationModel) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *MedicationModel) GetServiceDetailType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceDetailType
	}
	return v1.ServiceItemType(0)
}

func (x *MedicationModel) GetTime() []*MedicationModel_MedicationSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *MedicationModel) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MedicationModel) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *MedicationModel) GetMedicationName() string {
	if x != nil {
		return x.MedicationName
	}
	return ""
}

func (x *MedicationModel) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *MedicationModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MedicationModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MedicationModel) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

func (x *MedicationModel) GetSelectedDate() *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// Medication schedule.
type MedicationModel_MedicationSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Label for the schedule.
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// Time for the schedule, in minutes.
	Time int32 `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *MedicationModel_MedicationSchedule) Reset() {
	*x = MedicationModel_MedicationSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_medication_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MedicationModel_MedicationSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicationModel_MedicationSchedule) ProtoMessage() {}

func (x *MedicationModel_MedicationSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_medication_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicationModel_MedicationSchedule.ProtoReflect.Descriptor instead.
func (*MedicationModel_MedicationSchedule) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_medication_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *MedicationModel_MedicationSchedule) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *MedicationModel_MedicationSchedule) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_moego_models_online_booking_v1_medication_models_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_medication_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x90, 0x06, 0x0a, 0x0f, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64,
	0x12, 0x59, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x1a, 0x3e, 0x0a, 0x12, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x72, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_medication_models_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_medication_models_proto_rawDescData = file_moego_models_online_booking_v1_medication_models_proto_rawDesc
)

func file_moego_models_online_booking_v1_medication_models_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_medication_models_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_medication_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_medication_models_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_medication_models_proto_rawDescData
}

var file_moego_models_online_booking_v1_medication_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_online_booking_v1_medication_models_proto_goTypes = []interface{}{
	(*MedicationModel)(nil),                                         // 0: moego.models.online_booking.v1.MedicationModel
	(*MedicationModel_MedicationSchedule)(nil),                      // 1: moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	(v1.ServiceItemType)(0),                                         // 2: moego.models.offering.v1.ServiceItemType
	(*timestamppb.Timestamp)(nil),                                   // 3: google.protobuf.Timestamp
	(*v11.AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil), // 4: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
}
var file_moego_models_online_booking_v1_medication_models_proto_depIdxs = []int32{
	2, // 0: moego.models.online_booking.v1.MedicationModel.service_detail_type:type_name -> moego.models.offering.v1.ServiceItemType
	1, // 1: moego.models.online_booking.v1.MedicationModel.time:type_name -> moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	3, // 2: moego.models.online_booking.v1.MedicationModel.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: moego.models.online_booking.v1.MedicationModel.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: moego.models.online_booking.v1.MedicationModel.selected_date:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_medication_models_proto_init() }
func file_moego_models_online_booking_v1_medication_models_proto_init() {
	if File_moego_models_online_booking_v1_medication_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_medication_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MedicationModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_medication_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MedicationModel_MedicationSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_medication_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_medication_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_medication_models_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_medication_models_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_medication_models_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_medication_models_proto = out.File
	file_moego_models_online_booking_v1_medication_models_proto_rawDesc = nil
	file_moego_models_online_booking_v1_medication_models_proto_goTypes = nil
	file_moego_models_online_booking_v1_medication_models_proto_depIdxs = nil
}
