// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/booking_availability_enums.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Pet unavailable reason
type PetUnavailableReason int32

const (
	// unspecified
	PetUnavailableReason_PET_UNAVAILABLE_REASON_UNSPECIFIED PetUnavailableReason = 0
	// Over weight limit
	PetUnavailableReason_OVER_WEIGHT_LIMIT PetUnavailableReason = 1
	// Please update pet info
	PetUnavailableReason_PET_INFO_NEEDS_UPDATE PetUnavailableReason = 2
	// Pet type not accepted boarding
	PetUnavailableReason_PET_TYPE_NOT_ACCEPTED_BOARDING PetUnavailableReason = 3
	// Pet type not accepted daycare
	PetUnavailableReason_PET_TYPE_NOT_ACCEPTED_DAYCARE PetUnavailableReason = 4
	// Pet type not accepted grooming
	PetUnavailableReason_PET_TYPE_NOT_ACCEPTED_GROOMING PetUnavailableReason = 5
)

// Enum value maps for PetUnavailableReason.
var (
	PetUnavailableReason_name = map[int32]string{
		0: "PET_UNAVAILABLE_REASON_UNSPECIFIED",
		1: "OVER_WEIGHT_LIMIT",
		2: "PET_INFO_NEEDS_UPDATE",
		3: "PET_TYPE_NOT_ACCEPTED_BOARDING",
		4: "PET_TYPE_NOT_ACCEPTED_DAYCARE",
		5: "PET_TYPE_NOT_ACCEPTED_GROOMING",
	}
	PetUnavailableReason_value = map[string]int32{
		"PET_UNAVAILABLE_REASON_UNSPECIFIED": 0,
		"OVER_WEIGHT_LIMIT":                  1,
		"PET_INFO_NEEDS_UPDATE":              2,
		"PET_TYPE_NOT_ACCEPTED_BOARDING":     3,
		"PET_TYPE_NOT_ACCEPTED_DAYCARE":      4,
		"PET_TYPE_NOT_ACCEPTED_GROOMING":     5,
	}
)

func (x PetUnavailableReason) Enum() *PetUnavailableReason {
	p := new(PetUnavailableReason)
	*p = x
	return p
}

func (x PetUnavailableReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetUnavailableReason) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_booking_availability_enums_proto_enumTypes[0].Descriptor()
}

func (PetUnavailableReason) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_booking_availability_enums_proto_enumTypes[0]
}

func (x PetUnavailableReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetUnavailableReason.Descriptor instead.
func (PetUnavailableReason) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_online_booking_v1_booking_availability_enums_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2a, 0xdb, 0x01, 0x0a, 0x14, 0x50, 0x65, 0x74, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x45,
	0x54, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48,
	0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x45, 0x54,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45,
	0x44, 0x5f, 0x44, 0x41, 0x59, 0x43, 0x41, 0x52, 0x45, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x50,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45,
	0x50, 0x54, 0x45, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x42,
	0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescData = file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDesc
)

func file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDescData
}

var file_moego_models_online_booking_v1_booking_availability_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_online_booking_v1_booking_availability_enums_proto_goTypes = []interface{}{
	(PetUnavailableReason)(0), // 0: moego.models.online_booking.v1.PetUnavailableReason
}
var file_moego_models_online_booking_v1_booking_availability_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_booking_availability_enums_proto_init() }
func file_moego_models_online_booking_v1_booking_availability_enums_proto_init() {
	if File_moego_models_online_booking_v1_booking_availability_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_booking_availability_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_booking_availability_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_online_booking_v1_booking_availability_enums_proto_enumTypes,
	}.Build()
	File_moego_models_online_booking_v1_booking_availability_enums_proto = out.File
	file_moego_models_online_booking_v1_booking_availability_enums_proto_rawDesc = nil
	file_moego_models_online_booking_v1_booking_availability_enums_proto_goTypes = nil
	file_moego_models_online_booking_v1_booking_availability_enums_proto_depIdxs = nil
}
