// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/booking_availability_defs.proto

package onlinebookingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Booking pet definition<br>
// If pet id is not specified, it is new pet<br>
// If pet id is specified, it is existing pet
type BookingPetDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, pet's unique identifier<br>
	// new pet may be index number or uuid or timestamp
	// existing pet may be auto-increment id or uuid
	VirtualId string `protobuf:"bytes,1,opt,name=virtual_id,json=virtualId,proto3" json:"virtual_id,omitempty"`
	// existing pet id
	PetId *int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// pet name
	PetName *string `protobuf:"bytes,3,opt,name=pet_name,json=petName,proto3,oneof" json:"pet_name,omitempty"`
	// pet type id
	PetType *v1.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType,oneof" json:"pet_type,omitempty"`
	// pet breed
	Breed *string `protobuf:"bytes,5,opt,name=breed,proto3,oneof" json:"breed,omitempty"`
	// pet weight
	Weight *float64 `protobuf:"fixed64,6,opt,name=weight,proto3,oneof" json:"weight,omitempty"`
	// coat type
	CoatType *string `protobuf:"bytes,7,opt,name=coat_type,json=coatType,proto3,oneof" json:"coat_type,omitempty"`
}

func (x *BookingPetDef) Reset() {
	*x = BookingPetDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingPetDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingPetDef) ProtoMessage() {}

func (x *BookingPetDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingPetDef.ProtoReflect.Descriptor instead.
func (*BookingPetDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BookingPetDef) GetVirtualId() string {
	if x != nil {
		return x.VirtualId
	}
	return ""
}

func (x *BookingPetDef) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *BookingPetDef) GetPetName() string {
	if x != nil && x.PetName != nil {
		return *x.PetName
	}
	return ""
}

func (x *BookingPetDef) GetPetType() v1.PetType {
	if x != nil && x.PetType != nil {
		return *x.PetType
	}
	return v1.PetType(0)
}

func (x *BookingPetDef) GetBreed() string {
	if x != nil && x.Breed != nil {
		return *x.Breed
	}
	return ""
}

func (x *BookingPetDef) GetWeight() float64 {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return 0
}

func (x *BookingPetDef) GetCoatType() string {
	if x != nil && x.CoatType != nil {
		return *x.CoatType
	}
	return ""
}

// Booking pet's available service definition
type BookingPetServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Booking pet id
	Pet *BookingPetDef `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// Available service id list
	AvailableServiceIds []int64 `protobuf:"varint,2,rep,packed,name=available_service_ids,json=availableServiceIds,proto3" json:"available_service_ids,omitempty"`
	// Pet's customized service
	CustomizedServices []*CustomizedServiceDef `protobuf:"bytes,3,rep,name=customized_services,json=customizedServices,proto3" json:"customized_services,omitempty"`
	// Missing evaluations for this pet
	MissingEvaluations []*BookingPetServiceDef_MissingEvaluations `protobuf:"bytes,4,rep,name=missing_evaluations,json=missingEvaluations,proto3" json:"missing_evaluations,omitempty"`
}

func (x *BookingPetServiceDef) Reset() {
	*x = BookingPetServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingPetServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingPetServiceDef) ProtoMessage() {}

func (x *BookingPetServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingPetServiceDef.ProtoReflect.Descriptor instead.
func (*BookingPetServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BookingPetServiceDef) GetPet() *BookingPetDef {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *BookingPetServiceDef) GetAvailableServiceIds() []int64 {
	if x != nil {
		return x.AvailableServiceIds
	}
	return nil
}

func (x *BookingPetServiceDef) GetCustomizedServices() []*CustomizedServiceDef {
	if x != nil {
		return x.CustomizedServices
	}
	return nil
}

func (x *BookingPetServiceDef) GetMissingEvaluations() []*BookingPetServiceDef_MissingEvaluations {
	if x != nil {
		return x.MissingEvaluations
	}
	return nil
}

// Pet's customized service information, contains service price and duration
type CustomizedServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// duration(null for not override)
	Duration *int32 `protobuf:"varint,3,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *CustomizedServiceDef) Reset() {
	*x = CustomizedServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceDef) ProtoMessage() {}

func (x *CustomizedServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceDef.ProtoReflect.Descriptor instead.
func (*CustomizedServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescGZIP(), []int{2}
}

func (x *CustomizedServiceDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *CustomizedServiceDef) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *CustomizedServiceDef) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// Service unpassed evaluation
type BookingPetServiceDef_MissingEvaluations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// unpassed evaluation
	MissingEvaluation *v11.EvaluationBriefView `protobuf:"bytes,2,opt,name=missing_evaluation,json=missingEvaluation,proto3" json:"missing_evaluation,omitempty"`
}

func (x *BookingPetServiceDef_MissingEvaluations) Reset() {
	*x = BookingPetServiceDef_MissingEvaluations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingPetServiceDef_MissingEvaluations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingPetServiceDef_MissingEvaluations) ProtoMessage() {}

func (x *BookingPetServiceDef_MissingEvaluations) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingPetServiceDef_MissingEvaluations.ProtoReflect.Descriptor instead.
func (*BookingPetServiceDef_MissingEvaluations) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BookingPetServiceDef_MissingEvaluations) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BookingPetServiceDef_MissingEvaluations) GetMissingEvaluation() *v11.EvaluationBriefView {
	if x != nil {
		return x.MissingEvaluation
	}
	return nil
}

var File_moego_models_online_booking_v1_booking_availability_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99,
	0x03, 0x0a, 0x0d, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66,
	0x12, 0x27, 0x0a, 0x0a, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x09,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x48, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27,
	0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4d, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x03,
	0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12,
	0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x04, 0x52, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x48, 0x05, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x80, 0x04, 0x0a, 0x14, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x3f, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x66, 0x52,
	0x03, 0x70, 0x65, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x65, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x12, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x78, 0x0a, 0x13, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x66, 0x2e, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x12, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x91, 0x01, 0x0a, 0x12, 0x4d, 0x69,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x5c, 0x0a, 0x12, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x11, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x01,
	0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescData = file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_online_booking_v1_booking_availability_defs_proto_goTypes = []interface{}{
	(*BookingPetDef)(nil),                           // 0: moego.models.online_booking.v1.BookingPetDef
	(*BookingPetServiceDef)(nil),                    // 1: moego.models.online_booking.v1.BookingPetServiceDef
	(*CustomizedServiceDef)(nil),                    // 2: moego.models.online_booking.v1.CustomizedServiceDef
	(*BookingPetServiceDef_MissingEvaluations)(nil), // 3: moego.models.online_booking.v1.BookingPetServiceDef.MissingEvaluations
	(v1.PetType)(0),                                 // 4: moego.models.customer.v1.PetType
	(*v11.EvaluationBriefView)(nil),                 // 5: moego.models.offering.v1.EvaluationBriefView
}
var file_moego_models_online_booking_v1_booking_availability_defs_proto_depIdxs = []int32{
	4, // 0: moego.models.online_booking.v1.BookingPetDef.pet_type:type_name -> moego.models.customer.v1.PetType
	0, // 1: moego.models.online_booking.v1.BookingPetServiceDef.pet:type_name -> moego.models.online_booking.v1.BookingPetDef
	2, // 2: moego.models.online_booking.v1.BookingPetServiceDef.customized_services:type_name -> moego.models.online_booking.v1.CustomizedServiceDef
	3, // 3: moego.models.online_booking.v1.BookingPetServiceDef.missing_evaluations:type_name -> moego.models.online_booking.v1.BookingPetServiceDef.MissingEvaluations
	5, // 4: moego.models.online_booking.v1.BookingPetServiceDef.MissingEvaluations.missing_evaluation:type_name -> moego.models.offering.v1.EvaluationBriefView
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_booking_availability_defs_proto_init() }
func file_moego_models_online_booking_v1_booking_availability_defs_proto_init() {
	if File_moego_models_online_booking_v1_booking_availability_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingPetDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingPetServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingPetServiceDef_MissingEvaluations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_booking_availability_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_booking_availability_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_booking_availability_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_booking_availability_defs_proto = out.File
	file_moego_models_online_booking_v1_booking_availability_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_booking_availability_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_booking_availability_defs_proto_depIdxs = nil
}
