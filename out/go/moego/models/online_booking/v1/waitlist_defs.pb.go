// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/waitlist_defs.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The booking request service
type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the service type
	//
	// Types that are assignable to Service:
	//
	//	*Service_Boarding
	//	*Service_Daycare
	Service isService_Service `protobuf_oneof:"service"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescGZIP(), []int{0}
}

func (m *Service) GetService() isService_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *Service) GetBoarding() *BoardingService {
	if x, ok := x.GetService().(*Service_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *Service) GetDaycare() *DaycareService {
	if x, ok := x.GetService().(*Service_Daycare); ok {
		return x.Daycare
	}
	return nil
}

type isService_Service interface {
	isService_Service()
}

type Service_Boarding struct {
	// boarding service
	Boarding *BoardingService `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type Service_Daycare struct {
	// daycare service
	Daycare *DaycareService `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"`
}

func (*Service_Boarding) isService_Service() {}

func (*Service_Daycare) isService_Service() {}

// Boarding service
type BoardingService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service id
	Service *PetServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *BoardingService) Reset() {
	*x = BoardingService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingService) ProtoMessage() {}

func (x *BoardingService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingService.ProtoReflect.Descriptor instead.
func (*BoardingService) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BoardingService) GetService() *PetServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

// Daycare service
type DaycareService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service id
	Service *PetServiceDetail `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *DaycareService) Reset() {
	*x = DaycareService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareService) ProtoMessage() {}

func (x *DaycareService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareService.ProtoReflect.Descriptor instead.
func (*DaycareService) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescGZIP(), []int{2}
}

func (x *DaycareService) GetService() *PetServiceDetail {
	if x != nil {
		return x.Service
	}
	return nil
}

// service base info
type PetServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current service
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The time of current service, unit minute
	ServiceTime int32 `protobuf:"varint,3,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// The price of current service
	ServicePrice float64 `protobuf:"fixed64,4,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *PetServiceDetail) Reset() {
	*x = PetServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetServiceDetail) ProtoMessage() {}

func (x *PetServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetServiceDetail.ProtoReflect.Descriptor instead.
func (*PetServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescGZIP(), []int{3}
}

func (x *PetServiceDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetServiceDetail) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *PetServiceDetail) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

var File_moego_models_online_booking_v1_waitlist_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_waitlist_defs_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x22, 0xaf, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x4d, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x4a, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x5d, 0x0a, 0x0f, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x5c, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescData = file_moego_models_online_booking_v1_waitlist_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_waitlist_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_online_booking_v1_waitlist_defs_proto_goTypes = []interface{}{
	(*Service)(nil),          // 0: moego.models.online_booking.v1.Service
	(*BoardingService)(nil),  // 1: moego.models.online_booking.v1.BoardingService
	(*DaycareService)(nil),   // 2: moego.models.online_booking.v1.DaycareService
	(*PetServiceDetail)(nil), // 3: moego.models.online_booking.v1.PetServiceDetail
}
var file_moego_models_online_booking_v1_waitlist_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.online_booking.v1.Service.boarding:type_name -> moego.models.online_booking.v1.BoardingService
	2, // 1: moego.models.online_booking.v1.Service.daycare:type_name -> moego.models.online_booking.v1.DaycareService
	3, // 2: moego.models.online_booking.v1.BoardingService.service:type_name -> moego.models.online_booking.v1.PetServiceDetail
	3, // 3: moego.models.online_booking.v1.DaycareService.service:type_name -> moego.models.online_booking.v1.PetServiceDetail
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_waitlist_defs_proto_init() }
func file_moego_models_online_booking_v1_waitlist_defs_proto_init() {
	if File_moego_models_online_booking_v1_waitlist_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Service_Boarding)(nil),
		(*Service_Daycare)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_waitlist_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_waitlist_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_waitlist_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_waitlist_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_waitlist_defs_proto = out.File
	file_moego_models_online_booking_v1_waitlist_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_waitlist_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_waitlist_defs_proto_depIdxs = nil
}
