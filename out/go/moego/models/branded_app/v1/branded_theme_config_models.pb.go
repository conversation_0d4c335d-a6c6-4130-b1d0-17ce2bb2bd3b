// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/branded_app/v1/branded_theme_config_models.proto

package brandedapppb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Logo type
type BrandedThemeConfigModel_LogoType int32

const (
	// Unspecified
	BrandedThemeConfigModel_LOGO_TYPE_UNSPECIFIED BrandedThemeConfigModel_LogoType = 0
	// logo only
	BrandedThemeConfigModel_LOGO_ONLY BrandedThemeConfigModel_LogoType = 1
	// logo with text
	BrandedThemeConfigModel_LOGO_WITH_TEXT BrandedThemeConfigModel_LogoType = 2
)

// Enum value maps for BrandedThemeConfigModel_LogoType.
var (
	BrandedThemeConfigModel_LogoType_name = map[int32]string{
		0: "LOGO_TYPE_UNSPECIFIED",
		1: "LOGO_ONLY",
		2: "LOGO_WITH_TEXT",
	}
	BrandedThemeConfigModel_LogoType_value = map[string]int32{
		"LOGO_TYPE_UNSPECIFIED": 0,
		"LOGO_ONLY":             1,
		"LOGO_WITH_TEXT":        2,
	}
)

func (x BrandedThemeConfigModel_LogoType) Enum() *BrandedThemeConfigModel_LogoType {
	p := new(BrandedThemeConfigModel_LogoType)
	*p = x
	return p
}

func (x BrandedThemeConfigModel_LogoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BrandedThemeConfigModel_LogoType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_branded_app_v1_branded_theme_config_models_proto_enumTypes[0].Descriptor()
}

func (BrandedThemeConfigModel_LogoType) Type() protoreflect.EnumType {
	return &file_moego_models_branded_app_v1_branded_theme_config_models_proto_enumTypes[0]
}

func (x BrandedThemeConfigModel_LogoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BrandedThemeConfigModel_LogoType.Descriptor instead.
func (BrandedThemeConfigModel_LogoType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescGZIP(), []int{0, 0}
}

// The Branded App theme config model
type BrandedThemeConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the branded app id
	BrandedAppId string `protobuf:"bytes,2,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
	// the theme name
	ThemeName string `protobuf:"bytes,3,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	// the logo type
	LogoType BrandedThemeConfigModel_LogoType `protobuf:"varint,4,opt,name=logo_type,json=logoType,proto3,enum=moego.models.branded_app.v1.BrandedThemeConfigModel_LogoType" json:"logo_type,omitempty"`
	// the home icon url
	HomeIconUrl string `protobuf:"bytes,5,opt,name=home_icon_url,json=homeIconUrl,proto3" json:"home_icon_url,omitempty"`
	// branded logo url
	BrandedLogoUrl string `protobuf:"bytes,6,opt,name=branded_logo_url,json=brandedLogoUrl,proto3" json:"branded_logo_url,omitempty"`
	// branded font url
	BrandedFontUrl string `protobuf:"bytes,7,opt,name=branded_font_url,json=brandedFontUrl,proto3" json:"branded_font_url,omitempty"`
	// introduction image url
	IntroductionImageUrl string `protobuf:"bytes,8,opt,name=introduction_image_url,json=introductionImageUrl,proto3" json:"introduction_image_url,omitempty"`
	// introduction text color
	IntroductionTextColor string `protobuf:"bytes,9,opt,name=introduction_text_color,json=introductionTextColor,proto3" json:"introduction_text_color,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,10,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// sub theme color
	SubThemeColor string `protobuf:"bytes,11,opt,name=sub_theme_color,json=subThemeColor,proto3" json:"sub_theme_color,omitempty"`
	// custom background image url
	CustomBackgroundImageUrl string `protobuf:"bytes,12,opt,name=custom_background_image_url,json=customBackgroundImageUrl,proto3" json:"custom_background_image_url,omitempty"`
	// custom theme
	CustomTheme string `protobuf:"bytes,13,opt,name=custom_theme,json=customTheme,proto3" json:"custom_theme,omitempty"`
}

func (x *BrandedThemeConfigModel) Reset() {
	*x = BrandedThemeConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_branded_app_v1_branded_theme_config_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandedThemeConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandedThemeConfigModel) ProtoMessage() {}

func (x *BrandedThemeConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_branded_app_v1_branded_theme_config_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandedThemeConfigModel.ProtoReflect.Descriptor instead.
func (*BrandedThemeConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescGZIP(), []int{0}
}

func (x *BrandedThemeConfigModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BrandedThemeConfigModel) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetThemeName() string {
	if x != nil {
		return x.ThemeName
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetLogoType() BrandedThemeConfigModel_LogoType {
	if x != nil {
		return x.LogoType
	}
	return BrandedThemeConfigModel_LOGO_TYPE_UNSPECIFIED
}

func (x *BrandedThemeConfigModel) GetHomeIconUrl() string {
	if x != nil {
		return x.HomeIconUrl
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetBrandedLogoUrl() string {
	if x != nil {
		return x.BrandedLogoUrl
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetBrandedFontUrl() string {
	if x != nil {
		return x.BrandedFontUrl
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetIntroductionImageUrl() string {
	if x != nil {
		return x.IntroductionImageUrl
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetIntroductionTextColor() string {
	if x != nil {
		return x.IntroductionTextColor
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetSubThemeColor() string {
	if x != nil {
		return x.SubThemeColor
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetCustomBackgroundImageUrl() string {
	if x != nil {
		return x.CustomBackgroundImageUrl
	}
	return ""
}

func (x *BrandedThemeConfigModel) GetCustomTheme() string {
	if x != nil {
		return x.CustomTheme
	}
	return ""
}

var File_moego_models_branded_app_v1_branded_theme_config_models_proto protoreflect.FileDescriptor

var file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x22, 0xa5, 0x05, 0x0a,
	0x17, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a,
	0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x68, 0x6f, 0x6d,
	0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x68, 0x6f, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x5f, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x46, 0x6f, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e, 0x74, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x54, 0x68,
	0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x08, 0x4c, 0x6f,
	0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x4f, 0x47, 0x4f, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x4f, 0x47, 0x4f, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x01,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x4f, 0x47, 0x4f, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x54, 0x45,
	0x58, 0x54, 0x10, 0x02, 0x42, 0x86, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31,
	0x3b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x61, 0x70, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescOnce sync.Once
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescData = file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDesc
)

func file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescGZIP() []byte {
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescOnce.Do(func() {
		file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescData)
	})
	return file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDescData
}

var file_moego_models_branded_app_v1_branded_theme_config_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_branded_app_v1_branded_theme_config_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_branded_app_v1_branded_theme_config_models_proto_goTypes = []interface{}{
	(BrandedThemeConfigModel_LogoType)(0), // 0: moego.models.branded_app.v1.BrandedThemeConfigModel.LogoType
	(*BrandedThemeConfigModel)(nil),       // 1: moego.models.branded_app.v1.BrandedThemeConfigModel
}
var file_moego_models_branded_app_v1_branded_theme_config_models_proto_depIdxs = []int32{
	0, // 0: moego.models.branded_app.v1.BrandedThemeConfigModel.logo_type:type_name -> moego.models.branded_app.v1.BrandedThemeConfigModel.LogoType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_branded_app_v1_branded_theme_config_models_proto_init() }
func file_moego_models_branded_app_v1_branded_theme_config_models_proto_init() {
	if File_moego_models_branded_app_v1_branded_theme_config_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_branded_app_v1_branded_theme_config_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandedThemeConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_branded_app_v1_branded_theme_config_models_proto_goTypes,
		DependencyIndexes: file_moego_models_branded_app_v1_branded_theme_config_models_proto_depIdxs,
		EnumInfos:         file_moego_models_branded_app_v1_branded_theme_config_models_proto_enumTypes,
		MessageInfos:      file_moego_models_branded_app_v1_branded_theme_config_models_proto_msgTypes,
	}.Build()
	File_moego_models_branded_app_v1_branded_theme_config_models_proto = out.File
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_rawDesc = nil
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_goTypes = nil
	file_moego_models_branded_app_v1_branded_theme_config_models_proto_depIdxs = nil
}
