// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/finance_tools/v1/cash_drawer_models.proto

package financetoolspb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Cash drawer report.
type CashDrawerReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ID of the staff that make this reconciliation report.
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The date range
	Range *interval.Interval `protobuf:"bytes,3,opt,name=range,proto3" json:"range,omitempty"`
	// The starting balance
	StartBalance *money.Money `protobuf:"bytes,4,opt,name=start_balance,json=startBalance,proto3" json:"start_balance,omitempty"`
	// The total of cash payments
	PaymentsTotal *money.Money `protobuf:"bytes,5,opt,name=payments_total,json=paymentsTotal,proto3" json:"payments_total,omitempty"`
	// The total of cash in/out adjustments
	AdjustmentsTotal *money.Money `protobuf:"bytes,6,opt,name=adjustments_total,json=adjustmentsTotal,proto3" json:"adjustments_total,omitempty"`
	// The expected ending balance
	ExpectedBalance *money.Money `protobuf:"bytes,7,opt,name=expected_balance,json=expectedBalance,proto3" json:"expected_balance,omitempty"`
	// The actual ending balance
	CountedBalance *money.Money `protobuf:"bytes,8,opt,name=counted_balance,json=countedBalance,proto3" json:"counted_balance,omitempty"`
	// The difference between the expected and the actual ending balance. Will be positive if the actual balance is over
	// the expected, and negative if the actual balance is short.
	Difference *money.Money `protobuf:"bytes,9,opt,name=difference,proto3" json:"difference,omitempty"`
	// Comment
	Comment *string `protobuf:"bytes,10,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *CashDrawerReport) Reset() {
	*x = CashDrawerReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashDrawerReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashDrawerReport) ProtoMessage() {}

func (x *CashDrawerReport) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashDrawerReport.ProtoReflect.Descriptor instead.
func (*CashDrawerReport) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescGZIP(), []int{0}
}

func (x *CashDrawerReport) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CashDrawerReport) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CashDrawerReport) GetRange() *interval.Interval {
	if x != nil {
		return x.Range
	}
	return nil
}

func (x *CashDrawerReport) GetStartBalance() *money.Money {
	if x != nil {
		return x.StartBalance
	}
	return nil
}

func (x *CashDrawerReport) GetPaymentsTotal() *money.Money {
	if x != nil {
		return x.PaymentsTotal
	}
	return nil
}

func (x *CashDrawerReport) GetAdjustmentsTotal() *money.Money {
	if x != nil {
		return x.AdjustmentsTotal
	}
	return nil
}

func (x *CashDrawerReport) GetExpectedBalance() *money.Money {
	if x != nil {
		return x.ExpectedBalance
	}
	return nil
}

func (x *CashDrawerReport) GetCountedBalance() *money.Money {
	if x != nil {
		return x.CountedBalance
	}
	return nil
}

func (x *CashDrawerReport) GetDifference() *money.Money {
	if x != nil {
		return x.Difference
	}
	return nil
}

func (x *CashDrawerReport) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

// A cash adjustment.
type CashDrawerAdjustment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The adjustment ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the report this transaction is attached. Optional because an adjustment may not be attached to any report
	// yet.
	ReportId *int64 `protobuf:"varint,2,opt,name=report_id,json=reportId,proto3,oneof" json:"report_id,omitempty"`
	// Adjustment type (direction)
	Type CashDrawerAdjustmentType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.finance_tools.v1.CashDrawerAdjustmentType" json:"type,omitempty"`
	// Time
	Time *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=time,proto3" json:"time,omitempty"`
	// ID of the staff that make this cash in/out.
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The amount of cash in/out adjustments. Always positive.
	Amount *money.Money `protobuf:"bytes,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// Comment
	Comment *string `protobuf:"bytes,7,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *CashDrawerAdjustment) Reset() {
	*x = CashDrawerAdjustment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashDrawerAdjustment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashDrawerAdjustment) ProtoMessage() {}

func (x *CashDrawerAdjustment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashDrawerAdjustment.ProtoReflect.Descriptor instead.
func (*CashDrawerAdjustment) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescGZIP(), []int{1}
}

func (x *CashDrawerAdjustment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CashDrawerAdjustment) GetReportId() int64 {
	if x != nil && x.ReportId != nil {
		return *x.ReportId
	}
	return 0
}

func (x *CashDrawerAdjustment) GetType() CashDrawerAdjustmentType {
	if x != nil {
		return x.Type
	}
	return CashDrawerAdjustmentType_CASH_DRAWER_ADJUSTMENT_TYPE_UNSPECIFIED
}

func (x *CashDrawerAdjustment) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *CashDrawerAdjustment) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CashDrawerAdjustment) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CashDrawerAdjustment) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

var File_moego_models_finance_tools_v1_cash_drawer_models_proto protoreflect.FileDescriptor

var file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74,
	0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x73,
	0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x96, 0x04,
	0x0a, 0x10, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x0d,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x3f, 0x0a, 0x11, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x10, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x3d, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x32, 0x0a,
	0x0a, 0x64, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xc8, 0x01, 0x48, 0x00, 0x52, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x80, 0x03, 0x0a, 0x14, 0x43, 0x61, 0x73, 0x68, 0x44,
	0x72, 0x61, 0x77, 0x65, 0x72, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x41, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x2a, 0x00,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xc8,
	0x01, 0x48, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x8c, 0x01, 0x0a, 0x25, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescOnce sync.Once
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescData = file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDesc
)

func file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescGZIP() []byte {
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescOnce.Do(func() {
		file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescData)
	})
	return file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDescData
}

var file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_finance_tools_v1_cash_drawer_models_proto_goTypes = []interface{}{
	(*CashDrawerReport)(nil),      // 0: moego.models.finance_tools.v1.CashDrawerReport
	(*CashDrawerAdjustment)(nil),  // 1: moego.models.finance_tools.v1.CashDrawerAdjustment
	(*interval.Interval)(nil),     // 2: google.type.Interval
	(*money.Money)(nil),           // 3: google.type.Money
	(CashDrawerAdjustmentType)(0), // 4: moego.models.finance_tools.v1.CashDrawerAdjustmentType
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_moego_models_finance_tools_v1_cash_drawer_models_proto_depIdxs = []int32{
	2,  // 0: moego.models.finance_tools.v1.CashDrawerReport.range:type_name -> google.type.Interval
	3,  // 1: moego.models.finance_tools.v1.CashDrawerReport.start_balance:type_name -> google.type.Money
	3,  // 2: moego.models.finance_tools.v1.CashDrawerReport.payments_total:type_name -> google.type.Money
	3,  // 3: moego.models.finance_tools.v1.CashDrawerReport.adjustments_total:type_name -> google.type.Money
	3,  // 4: moego.models.finance_tools.v1.CashDrawerReport.expected_balance:type_name -> google.type.Money
	3,  // 5: moego.models.finance_tools.v1.CashDrawerReport.counted_balance:type_name -> google.type.Money
	3,  // 6: moego.models.finance_tools.v1.CashDrawerReport.difference:type_name -> google.type.Money
	4,  // 7: moego.models.finance_tools.v1.CashDrawerAdjustment.type:type_name -> moego.models.finance_tools.v1.CashDrawerAdjustmentType
	5,  // 8: moego.models.finance_tools.v1.CashDrawerAdjustment.time:type_name -> google.protobuf.Timestamp
	3,  // 9: moego.models.finance_tools.v1.CashDrawerAdjustment.amount:type_name -> google.type.Money
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_models_finance_tools_v1_cash_drawer_models_proto_init() }
func file_moego_models_finance_tools_v1_cash_drawer_models_proto_init() {
	if File_moego_models_finance_tools_v1_cash_drawer_models_proto != nil {
		return
	}
	file_moego_models_finance_tools_v1_cash_drawer_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashDrawerReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashDrawerAdjustment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_finance_tools_v1_cash_drawer_models_proto_goTypes,
		DependencyIndexes: file_moego_models_finance_tools_v1_cash_drawer_models_proto_depIdxs,
		MessageInfos:      file_moego_models_finance_tools_v1_cash_drawer_models_proto_msgTypes,
	}.Build()
	File_moego_models_finance_tools_v1_cash_drawer_models_proto = out.File
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_rawDesc = nil
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_goTypes = nil
	file_moego_models_finance_tools_v1_cash_drawer_models_proto_depIdxs = nil
}
