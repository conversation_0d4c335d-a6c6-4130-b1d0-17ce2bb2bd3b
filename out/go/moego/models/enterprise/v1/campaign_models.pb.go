// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/campaign_models.proto

package enterprisepb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Campaign type
type Campaign_Type int32

const (
	// Unspecified campaign type
	Campaign_TYPE_UNSPECIFIED Campaign_Type = 0
	// sms
	Campaign_SMS Campaign_Type = 1
	// email
	Campaign_EMAIL Campaign_Type = 2
)

// Enum value maps for Campaign_Type.
var (
	Campaign_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SMS",
		2: "EMAIL",
	}
	Campaign_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SMS":              1,
		"EMAIL":            2,
	}
)

func (x Campaign_Type) Enum() *Campaign_Type {
	p := new(Campaign_Type)
	*p = x
	return p
}

func (x Campaign_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Campaign_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[0].Descriptor()
}

func (Campaign_Type) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[0]
}

func (x Campaign_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Campaign_Type.Descriptor instead.
func (Campaign_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{0, 0}
}

// Campaign status
type Campaign_Status int32

const (
	// Unspecified campaign status
	Campaign_STATUS_UNSPECIFIED Campaign_Status = 0
	// draft
	Campaign_DRAFT Campaign_Status = 1
	// scheduled
	Campaign_SCHEDULED Campaign_Status = 2
	// sent
	Campaign_SENT Campaign_Status = 3
	// cancelled
	Campaign_CANCELLED Campaign_Status = 4
)

// Enum value maps for Campaign_Status.
var (
	Campaign_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "DRAFT",
		2: "SCHEDULED",
		3: "SENT",
		4: "CANCELLED",
	}
	Campaign_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"DRAFT":              1,
		"SCHEDULED":          2,
		"SENT":               3,
		"CANCELLED":          4,
	}
)

func (x Campaign_Status) Enum() *Campaign_Status {
	p := new(Campaign_Status)
	*p = x
	return p
}

func (x Campaign_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Campaign_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[1].Descriptor()
}

func (Campaign_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[1]
}

func (x Campaign_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Campaign_Status.Descriptor instead.
func (Campaign_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{0, 1}
}

// Template status
type CampaignTemplate_Status int32

const (
	// Unspecified campaign status
	CampaignTemplate_STATUS_UNSPECIFIED CampaignTemplate_Status = 0
	// draft
	CampaignTemplate_DRAFT CampaignTemplate_Status = 1
	// published
	CampaignTemplate_PUBLISHED CampaignTemplate_Status = 2
)

// Enum value maps for CampaignTemplate_Status.
var (
	CampaignTemplate_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "DRAFT",
		2: "PUBLISHED",
	}
	CampaignTemplate_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"DRAFT":              1,
		"PUBLISHED":          2,
	}
)

func (x CampaignTemplate_Status) Enum() *CampaignTemplate_Status {
	p := new(CampaignTemplate_Status)
	*p = x
	return p
}

func (x CampaignTemplate_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CampaignTemplate_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[2].Descriptor()
}

func (CampaignTemplate_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_campaign_models_proto_enumTypes[2]
}

func (x CampaignTemplate_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CampaignTemplate_Status.Descriptor instead.
func (CampaignTemplate_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{1, 0}
}

// Campaign
type Campaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// staff_id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// type
	Type Campaign_Type `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.enterprise.v1.Campaign_Type" json:"type,omitempty"`
	// status
	Status Campaign_Status `protobuf:"varint,6,opt,name=status,proto3,enum=moego.models.enterprise.v1.Campaign_Status" json:"status,omitempty"`
	// scheduled_at
	ScheduledAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	// stat
	Stat *Campaign_Stat `protobuf:"bytes,8,opt,name=stat,proto3" json:"stat,omitempty"`
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{0}
}

func (x *Campaign) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Campaign) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Campaign) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetType() Campaign_Type {
	if x != nil {
		return x.Type
	}
	return Campaign_TYPE_UNSPECIFIED
}

func (x *Campaign) GetStatus() Campaign_Status {
	if x != nil {
		return x.Status
	}
	return Campaign_STATUS_UNSPECIFIED
}

func (x *Campaign) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Campaign) GetStat() *Campaign_Stat {
	if x != nil {
		return x.Stat
	}
	return nil
}

// Campaign template
type CampaignTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise_id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// staff_id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// type
	Type Campaign_Type `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.enterprise.v1.Campaign_Type" json:"type,omitempty"`
	// status
	Status CampaignTemplate_Status `protobuf:"varint,6,opt,name=status,proto3,enum=moego.models.enterprise.v1.CampaignTemplate_Status" json:"status,omitempty"`
	// internal template id
	InternalTemplateId int64 `protobuf:"varint,7,opt,name=internal_template_id,json=internalTemplateId,proto3" json:"internal_template_id,omitempty"`
	// stat
	Stat *Campaign_Stat `protobuf:"bytes,8,opt,name=stat,proto3" json:"stat,omitempty"`
	// 下面这几个都是外部模型的字段， enterprise 这边不存
	// description
	Description string `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	// cover
	Cover string `protobuf:"bytes,10,opt,name=cover,proto3" json:"cover,omitempty"`
	// subject
	Subject string `protobuf:"bytes,11,opt,name=subject,proto3" json:"subject,omitempty"`
	// content
	Content string `protobuf:"bytes,12,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *CampaignTemplate) Reset() {
	*x = CampaignTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignTemplate) ProtoMessage() {}

func (x *CampaignTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignTemplate.ProtoReflect.Descriptor instead.
func (*CampaignTemplate) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{1}
}

func (x *CampaignTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CampaignTemplate) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CampaignTemplate) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CampaignTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CampaignTemplate) GetType() Campaign_Type {
	if x != nil {
		return x.Type
	}
	return Campaign_TYPE_UNSPECIFIED
}

func (x *CampaignTemplate) GetStatus() CampaignTemplate_Status {
	if x != nil {
		return x.Status
	}
	return CampaignTemplate_STATUS_UNSPECIFIED
}

func (x *CampaignTemplate) GetInternalTemplateId() int64 {
	if x != nil {
		return x.InternalTemplateId
	}
	return 0
}

func (x *CampaignTemplate) GetStat() *Campaign_Stat {
	if x != nil {
		return x.Stat
	}
	return nil
}

func (x *CampaignTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CampaignTemplate) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *CampaignTemplate) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *CampaignTemplate) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// CampaignTemplatePushRecord
type CampaignTemplatePushRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// TODO(ark: 改造成通用的 enterprise hub 分发数据方案)
	// template id
	TemplateId int64 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// type
	Type Campaign_Type `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.enterprise.v1.Campaign_Type" json:"type,omitempty"`
	// internal template id
	InternalTemplateId int64 `protobuf:"varint,5,opt,name=internal_template_id,json=internalTemplateId,proto3" json:"internal_template_id,omitempty"`
	// target
	Target *TenantObject `protobuf:"bytes,6,opt,name=target,proto3" json:"target,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *CampaignTemplatePushRecord) Reset() {
	*x = CampaignTemplatePushRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignTemplatePushRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignTemplatePushRecord) ProtoMessage() {}

func (x *CampaignTemplatePushRecord) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignTemplatePushRecord.ProtoReflect.Descriptor instead.
func (*CampaignTemplatePushRecord) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{2}
}

func (x *CampaignTemplatePushRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CampaignTemplatePushRecord) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CampaignTemplatePushRecord) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *CampaignTemplatePushRecord) GetType() Campaign_Type {
	if x != nil {
		return x.Type
	}
	return Campaign_TYPE_UNSPECIFIED
}

func (x *CampaignTemplatePushRecord) GetInternalTemplateId() int64 {
	if x != nil {
		return x.InternalTemplateId
	}
	return 0
}

func (x *CampaignTemplatePushRecord) GetTarget() *TenantObject {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *CampaignTemplatePushRecord) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// stat
type Campaign_Stat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// franchisee
	Franchisee int64 `protobuf:"varint,1,opt,name=franchisee,proto3" json:"franchisee,omitempty"`
	// sent
	Sent int64 `protobuf:"varint,2,opt,name=sent,proto3" json:"sent,omitempty"`
	// open rate %
	OpenRate int64 `protobuf:"varint,3,opt,name=open_rate,json=openRate,proto3" json:"open_rate,omitempty"`
	// click rate %
	ClickRate int64 `protobuf:"varint,4,opt,name=click_rate,json=clickRate,proto3" json:"click_rate,omitempty"`
	// bookings
	Bookings int64 `protobuf:"varint,5,opt,name=bookings,proto3" json:"bookings,omitempty"`
	// revenue
	Revenue *money.Money `protobuf:"bytes,6,opt,name=revenue,proto3" json:"revenue,omitempty"`
}

func (x *Campaign_Stat) Reset() {
	*x = Campaign_Stat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign_Stat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign_Stat) ProtoMessage() {}

func (x *Campaign_Stat) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign_Stat.ProtoReflect.Descriptor instead.
func (*Campaign_Stat) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Campaign_Stat) GetFranchisee() int64 {
	if x != nil {
		return x.Franchisee
	}
	return 0
}

func (x *Campaign_Stat) GetSent() int64 {
	if x != nil {
		return x.Sent
	}
	return 0
}

func (x *Campaign_Stat) GetOpenRate() int64 {
	if x != nil {
		return x.OpenRate
	}
	return 0
}

func (x *Campaign_Stat) GetClickRate() int64 {
	if x != nil {
		return x.ClickRate
	}
	return 0
}

func (x *Campaign_Stat) GetBookings() int64 {
	if x != nil {
		return x.Bookings
	}
	return 0
}

func (x *Campaign_Stat) GetRevenue() *money.Money {
	if x != nil {
		return x.Revenue
	}
	return nil
}

var File_moego_models_enterprise_v1_campaign_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_campaign_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x05, 0x0a, 0x08, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a,
	0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x04,
	0x73, 0x74, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x04, 0x73, 0x74, 0x61, 0x74, 0x1a, 0xc0, 0x01, 0x0a, 0x04,
	0x53, 0x74, 0x61, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69, 0x73,
	0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x69, 0x73, 0x65, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x2c, 0x0a, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x22, 0x30,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02,
	0x22, 0x53, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x4c, 0x45, 0x44, 0x10, 0x04, 0x22, 0x9b, 0x04, 0x0a, 0x10, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x04,
	0x73, 0x74, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x04, 0x73, 0x74, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x3a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x52, 0x41,
	0x46, 0x54, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45,
	0x44, 0x10, 0x02, 0x22, 0xc0, 0x02, 0x0a, 0x1a, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x06, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_campaign_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_campaign_models_proto_rawDescData = file_moego_models_enterprise_v1_campaign_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_campaign_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_campaign_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_campaign_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_campaign_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_campaign_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_campaign_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_enterprise_v1_campaign_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_enterprise_v1_campaign_models_proto_goTypes = []interface{}{
	(Campaign_Type)(0),                 // 0: moego.models.enterprise.v1.Campaign.Type
	(Campaign_Status)(0),               // 1: moego.models.enterprise.v1.Campaign.Status
	(CampaignTemplate_Status)(0),       // 2: moego.models.enterprise.v1.CampaignTemplate.Status
	(*Campaign)(nil),                   // 3: moego.models.enterprise.v1.Campaign
	(*CampaignTemplate)(nil),           // 4: moego.models.enterprise.v1.CampaignTemplate
	(*CampaignTemplatePushRecord)(nil), // 5: moego.models.enterprise.v1.CampaignTemplatePushRecord
	(*Campaign_Stat)(nil),              // 6: moego.models.enterprise.v1.Campaign.Stat
	(*timestamppb.Timestamp)(nil),      // 7: google.protobuf.Timestamp
	(*TenantObject)(nil),               // 8: moego.models.enterprise.v1.TenantObject
	(*money.Money)(nil),                // 9: google.type.Money
}
var file_moego_models_enterprise_v1_campaign_models_proto_depIdxs = []int32{
	0,  // 0: moego.models.enterprise.v1.Campaign.type:type_name -> moego.models.enterprise.v1.Campaign.Type
	1,  // 1: moego.models.enterprise.v1.Campaign.status:type_name -> moego.models.enterprise.v1.Campaign.Status
	7,  // 2: moego.models.enterprise.v1.Campaign.scheduled_at:type_name -> google.protobuf.Timestamp
	6,  // 3: moego.models.enterprise.v1.Campaign.stat:type_name -> moego.models.enterprise.v1.Campaign.Stat
	0,  // 4: moego.models.enterprise.v1.CampaignTemplate.type:type_name -> moego.models.enterprise.v1.Campaign.Type
	2,  // 5: moego.models.enterprise.v1.CampaignTemplate.status:type_name -> moego.models.enterprise.v1.CampaignTemplate.Status
	6,  // 6: moego.models.enterprise.v1.CampaignTemplate.stat:type_name -> moego.models.enterprise.v1.Campaign.Stat
	0,  // 7: moego.models.enterprise.v1.CampaignTemplatePushRecord.type:type_name -> moego.models.enterprise.v1.Campaign.Type
	8,  // 8: moego.models.enterprise.v1.CampaignTemplatePushRecord.target:type_name -> moego.models.enterprise.v1.TenantObject
	9,  // 9: moego.models.enterprise.v1.Campaign.Stat.revenue:type_name -> google.type.Money
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_campaign_models_proto_init() }
func file_moego_models_enterprise_v1_campaign_models_proto_init() {
	if File_moego_models_enterprise_v1_campaign_models_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_tenant_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignTemplatePushRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_campaign_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign_Stat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_campaign_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_campaign_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_campaign_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_campaign_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_campaign_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_campaign_models_proto = out.File
	file_moego_models_enterprise_v1_campaign_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_campaign_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_campaign_models_proto_depIdxs = nil
}
