// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/tenant_group_mapping_models.proto

package enterprisepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// tenant status
// enum for tenant status
type TenantGroupMappingModel_Status int32

const (
	// UNSPECIFIED is the default value
	TenantGroupMappingModel_TENANT_GROUP_MAPPING_STATUS_UNSPECIFIED TenantGroupMappingModel_Status = 0
	// normal
	TenantGroupMappingModel_NORMAL TenantGroupMappingModel_Status = 1
	// delete
	TenantGroupMappingModel_DELETE TenantGroupMappingModel_Status = 2
)

// Enum value maps for TenantGroupMappingModel_Status.
var (
	TenantGroupMappingModel_Status_name = map[int32]string{
		0: "TENANT_GROUP_MAPPING_STATUS_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETE",
	}
	TenantGroupMappingModel_Status_value = map[string]int32{
		"TENANT_GROUP_MAPPING_STATUS_UNSPECIFIED": 0,
		"NORMAL": 1,
		"DELETE": 2,
	}
)

func (x TenantGroupMappingModel_Status) Enum() *TenantGroupMappingModel_Status {
	p := new(TenantGroupMappingModel_Status)
	*p = x
	return p
}

func (x TenantGroupMappingModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantGroupMappingModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_enumTypes[0].Descriptor()
}

func (TenantGroupMappingModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_enumTypes[0]
}

func (x TenantGroupMappingModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantGroupMappingModel_Status.Descriptor instead.
func (TenantGroupMappingModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescGZIP(), []int{0, 0}
}

// tenant group mapping model
type TenantGroupMappingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tenant id
	TenantId int64 `protobuf:"varint,2,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// group id
	GroupId int64 `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// status
	Status TenantGroupMappingModel_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.enterprise.v1.TenantGroupMappingModel_Status" json:"status,omitempty"`
}

func (x *TenantGroupMappingModel) Reset() {
	*x = TenantGroupMappingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantGroupMappingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantGroupMappingModel) ProtoMessage() {}

func (x *TenantGroupMappingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantGroupMappingModel.ProtoReflect.Descriptor instead.
func (*TenantGroupMappingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescGZIP(), []int{0}
}

func (x *TenantGroupMappingModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantGroupMappingModel) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *TenantGroupMappingModel) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *TenantGroupMappingModel) GetStatus() TenantGroupMappingModel_Status {
	if x != nil {
		return x.Status
	}
	return TenantGroupMappingModel_TENANT_GROUP_MAPPING_STATUS_UNSPECIFIED
}

var File_moego_models_enterprise_v1_tenant_group_mapping_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x22, 0x84, 0x02, 0x0a, 0x17, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x52,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x4d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27,
	0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4d, 0x41, 0x50,
	0x50, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52,
	0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10,
	0x02, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescData = file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_goTypes = []interface{}{
	(TenantGroupMappingModel_Status)(0), // 0: moego.models.enterprise.v1.TenantGroupMappingModel.Status
	(*TenantGroupMappingModel)(nil),     // 1: moego.models.enterprise.v1.TenantGroupMappingModel
}
var file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_depIdxs = []int32{
	0, // 0: moego.models.enterprise.v1.TenantGroupMappingModel.status:type_name -> moego.models.enterprise.v1.TenantGroupMappingModel.Status
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_init() }
func file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_init() {
	if File_moego_models_enterprise_v1_tenant_group_mapping_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantGroupMappingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_tenant_group_mapping_models_proto = out.File
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_tenant_group_mapping_models_proto_depIdxs = nil
}
