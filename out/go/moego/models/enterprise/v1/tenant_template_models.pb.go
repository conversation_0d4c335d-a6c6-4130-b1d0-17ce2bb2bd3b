// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/tenant_template_models.proto

package enterprisepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum for tenant template status
type TenantTemplateModel_Status int32

const (
	// UNSPECIFIED is the default value
	TenantTemplateModel_TENANT_TEMPLATE_STATUS_UNSPECIFIED TenantTemplateModel_Status = 0
	// normal
	TenantTemplateModel_NORMAL TenantTemplateModel_Status = 1
	// delete
	TenantTemplateModel_DELETE TenantTemplateModel_Status = 2
)

// Enum value maps for TenantTemplateModel_Status.
var (
	TenantTemplateModel_Status_name = map[int32]string{
		0: "TENANT_TEMPLATE_STATUS_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETE",
	}
	TenantTemplateModel_Status_value = map[string]int32{
		"TENANT_TEMPLATE_STATUS_UNSPECIFIED": 0,
		"NORMAL":                             1,
		"DELETE":                             2,
	}
)

func (x TenantTemplateModel_Status) Enum() *TenantTemplateModel_Status {
	p := new(TenantTemplateModel_Status)
	*p = x
	return p
}

func (x TenantTemplateModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantTemplateModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes[0].Descriptor()
}

func (TenantTemplateModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes[0]
}

func (x TenantTemplateModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantTemplateModel_Status.Descriptor instead.
func (TenantTemplateModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescGZIP(), []int{0, 0}
}

// enum for tenant template type
type TenantTemplateModel_Type int32

const (
	// UNSPECIFIED is the default value
	TenantTemplateModel_TENANT_TEMPLATE_TYPE_UNSPECIFIED TenantTemplateModel_Type = 0
	// demo company type
	TenantTemplateModel_COMPANY TenantTemplateModel_Type = 1
	// config
	TenantTemplateModel_CONFIG TenantTemplateModel_Type = 2
)

// Enum value maps for TenantTemplateModel_Type.
var (
	TenantTemplateModel_Type_name = map[int32]string{
		0: "TENANT_TEMPLATE_TYPE_UNSPECIFIED",
		1: "COMPANY",
		2: "CONFIG",
	}
	TenantTemplateModel_Type_value = map[string]int32{
		"TENANT_TEMPLATE_TYPE_UNSPECIFIED": 0,
		"COMPANY":                          1,
		"CONFIG":                           2,
	}
)

func (x TenantTemplateModel_Type) Enum() *TenantTemplateModel_Type {
	p := new(TenantTemplateModel_Type)
	*p = x
	return p
}

func (x TenantTemplateModel_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantTemplateModel_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes[1].Descriptor()
}

func (TenantTemplateModel_Type) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes[1]
}

func (x TenantTemplateModel_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantTemplateModel_Type.Descriptor instead.
func (TenantTemplateModel_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescGZIP(), []int{0, 1}
}

// tenant model
type TenantTemplateModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// tenant template name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// tenant template status
	Status TenantTemplateModel_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.enterprise.v1.TenantTemplateModel_Status" json:"status,omitempty"`
	// tenant template type
	Type TenantTemplateModel_Type `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.enterprise.v1.TenantTemplateModel_Type" json:"type,omitempty"`
	// tenant template config
	Config *string `protobuf:"bytes,6,opt,name=config,proto3,oneof" json:"config,omitempty"`
	// min van num
	MinVanNum int64 `protobuf:"varint,7,opt,name=min_van_num,json=minVanNum,proto3" json:"min_van_num,omitempty"`
	// min location num
	MinLocationNum int64 `protobuf:"varint,8,opt,name=min_location_num,json=minLocationNum,proto3" json:"min_location_num,omitempty"`
	// company type info
	CompanyTypeInfo *TenantTemplateModel_CompanyTypeInfoModel `protobuf:"bytes,9,opt,name=company_type_info,json=companyTypeInfo,proto3,oneof" json:"company_type_info,omitempty"`
}

func (x *TenantTemplateModel) Reset() {
	*x = TenantTemplateModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantTemplateModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantTemplateModel) ProtoMessage() {}

func (x *TenantTemplateModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantTemplateModel.ProtoReflect.Descriptor instead.
func (*TenantTemplateModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescGZIP(), []int{0}
}

func (x *TenantTemplateModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantTemplateModel) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *TenantTemplateModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TenantTemplateModel) GetStatus() TenantTemplateModel_Status {
	if x != nil {
		return x.Status
	}
	return TenantTemplateModel_TENANT_TEMPLATE_STATUS_UNSPECIFIED
}

func (x *TenantTemplateModel) GetType() TenantTemplateModel_Type {
	if x != nil {
		return x.Type
	}
	return TenantTemplateModel_TENANT_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *TenantTemplateModel) GetConfig() string {
	if x != nil && x.Config != nil {
		return *x.Config
	}
	return ""
}

func (x *TenantTemplateModel) GetMinVanNum() int64 {
	if x != nil {
		return x.MinVanNum
	}
	return 0
}

func (x *TenantTemplateModel) GetMinLocationNum() int64 {
	if x != nil {
		return x.MinLocationNum
	}
	return 0
}

func (x *TenantTemplateModel) GetCompanyTypeInfo() *TenantTemplateModel_CompanyTypeInfoModel {
	if x != nil {
		return x.CompanyTypeInfo
	}
	return nil
}

// type for company struct
type TenantTemplateModel_CompanyTypeInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template company id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company type
	Type v1.CompanyModel_CompanyType `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.organization.v1.CompanyModel_CompanyType" json:"type,omitempty"`
}

func (x *TenantTemplateModel_CompanyTypeInfoModel) Reset() {
	*x = TenantTemplateModel_CompanyTypeInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantTemplateModel_CompanyTypeInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantTemplateModel_CompanyTypeInfoModel) ProtoMessage() {}

func (x *TenantTemplateModel_CompanyTypeInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantTemplateModel_CompanyTypeInfoModel.ProtoReflect.Descriptor instead.
func (*TenantTemplateModel_CompanyTypeInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TenantTemplateModel_CompanyTypeInfoModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantTemplateModel_CompanyTypeInfoModel) GetType() v1.CompanyModel_CompanyType {
	if x != nil {
		return x.Type
	}
	return v1.CompanyModel_CompanyType(0)
}

var File_moego_models_enterprise_v1_tenant_template_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_tenant_template_models_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfc, 0x05, 0x0a, 0x13, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6e, 0x4e, 0x75, 0x6d,
	0x12, 0x28, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x75, 0x0a, 0x11, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x01, 0x52, 0x0f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01,
	0x01, 0x1a, 0x72, 0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x26, 0x0a, 0x22, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x22,
	0x45, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x45, 0x4e, 0x41, 0x4e,
	0x54, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x47, 0x10, 0x02, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescData = file_moego_models_enterprise_v1_tenant_template_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_tenant_template_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_enterprise_v1_tenant_template_models_proto_goTypes = []interface{}{
	(TenantTemplateModel_Status)(0),                  // 0: moego.models.enterprise.v1.TenantTemplateModel.Status
	(TenantTemplateModel_Type)(0),                    // 1: moego.models.enterprise.v1.TenantTemplateModel.Type
	(*TenantTemplateModel)(nil),                      // 2: moego.models.enterprise.v1.TenantTemplateModel
	(*TenantTemplateModel_CompanyTypeInfoModel)(nil), // 3: moego.models.enterprise.v1.TenantTemplateModel.CompanyTypeInfoModel
	(v1.CompanyModel_CompanyType)(0),                 // 4: moego.models.organization.v1.CompanyModel.CompanyType
}
var file_moego_models_enterprise_v1_tenant_template_models_proto_depIdxs = []int32{
	0, // 0: moego.models.enterprise.v1.TenantTemplateModel.status:type_name -> moego.models.enterprise.v1.TenantTemplateModel.Status
	1, // 1: moego.models.enterprise.v1.TenantTemplateModel.type:type_name -> moego.models.enterprise.v1.TenantTemplateModel.Type
	3, // 2: moego.models.enterprise.v1.TenantTemplateModel.company_type_info:type_name -> moego.models.enterprise.v1.TenantTemplateModel.CompanyTypeInfoModel
	4, // 3: moego.models.enterprise.v1.TenantTemplateModel.CompanyTypeInfoModel.type:type_name -> moego.models.organization.v1.CompanyModel.CompanyType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_tenant_template_models_proto_init() }
func file_moego_models_enterprise_v1_tenant_template_models_proto_init() {
	if File_moego_models_enterprise_v1_tenant_template_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantTemplateModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantTemplateModel_CompanyTypeInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_tenant_template_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_tenant_template_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_tenant_template_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_tenant_template_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_tenant_template_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_tenant_template_models_proto = out.File
	file_moego_models_enterprise_v1_tenant_template_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_tenant_template_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_tenant_template_models_proto_depIdxs = nil
}
