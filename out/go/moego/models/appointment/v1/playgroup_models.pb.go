// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/playgroup_models.proto

package appointmentpb

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet playgroup model
type PetPlaygroupModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// playgroup id
	PlaygroupId int64 `protobuf:"varint,3,opt,name=playgroup_id,json=playgroupId,proto3" json:"playgroup_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,4,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,5,opt,name=date,proto3" json:"date,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PetPlaygroupModel) Reset() {
	*x = PetPlaygroupModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_playgroup_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetPlaygroupModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetPlaygroupModel) ProtoMessage() {}

func (x *PetPlaygroupModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_playgroup_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetPlaygroupModel.ProtoReflect.Descriptor instead.
func (*PetPlaygroupModel) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_playgroup_models_proto_rawDescGZIP(), []int{0}
}

func (x *PetPlaygroupModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetPlaygroupModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetPlaygroupModel) GetPlaygroupId() int64 {
	if x != nil {
		return x.PlaygroupId
	}
	return 0
}

func (x *PetPlaygroupModel) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *PetPlaygroupModel) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *PetPlaygroupModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_moego_models_appointment_v1_playgroup_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_playgroup_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x01, 0x0a, 0x11, 0x50, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_playgroup_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_playgroup_models_proto_rawDescData = file_moego_models_appointment_v1_playgroup_models_proto_rawDesc
)

func file_moego_models_appointment_v1_playgroup_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_playgroup_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_playgroup_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_playgroup_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_playgroup_models_proto_rawDescData
}

var file_moego_models_appointment_v1_playgroup_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_appointment_v1_playgroup_models_proto_goTypes = []interface{}{
	(*PetPlaygroupModel)(nil), // 0: moego.models.appointment.v1.PetPlaygroupModel
	(*date.Date)(nil),         // 1: google.type.Date
}
var file_moego_models_appointment_v1_playgroup_models_proto_depIdxs = []int32{
	1, // 0: moego.models.appointment.v1.PetPlaygroupModel.date:type_name -> google.type.Date
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_playgroup_models_proto_init() }
func file_moego_models_appointment_v1_playgroup_models_proto_init() {
	if File_moego_models_appointment_v1_playgroup_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_playgroup_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetPlaygroupModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_playgroup_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_playgroup_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_playgroup_models_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_playgroup_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_playgroup_models_proto = out.File
	file_moego_models_appointment_v1_playgroup_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_playgroup_models_proto_goTypes = nil
	file_moego_models_appointment_v1_playgroup_models_proto_depIdxs = nil
}
