// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/call_models.proto

package eventbuspb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// model for call update status
type CallUpdatedStatusEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// call log id
	CallLogId int64 `protobuf:"varint,1,opt,name=call_log_id,json=callLogId,proto3" json:"call_log_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// twilio call sid
	TwilioCallSid string `protobuf:"bytes,4,opt,name=twilio_call_sid,json=twilioCallSid,proto3" json:"twilio_call_sid,omitempty"`
	// twilio conference sid
	TwilioConferenceSid string `protobuf:"bytes,5,opt,name=twilio_conference_sid,json=twilioConferenceSid,proto3" json:"twilio_conference_sid,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// direction
	Direction v1.CallingDirection `protobuf:"varint,50,opt,name=direction,proto3,enum=moego.models.engagement.v1.CallingDirection" json:"direction,omitempty"`
	// status
	Status v1.Status `protobuf:"varint,51,opt,name=status,proto3,enum=moego.models.engagement.v1.Status" json:"status,omitempty"`
	// start time
	InitTime *timestamppb.Timestamp `protobuf:"bytes,52,opt,name=init_time,json=initTime,proto3" json:"init_time,omitempty"`
}

func (x *CallUpdatedStatusEvent) Reset() {
	*x = CallUpdatedStatusEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_call_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallUpdatedStatusEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallUpdatedStatusEvent) ProtoMessage() {}

func (x *CallUpdatedStatusEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_call_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallUpdatedStatusEvent.ProtoReflect.Descriptor instead.
func (*CallUpdatedStatusEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_call_models_proto_rawDescGZIP(), []int{0}
}

func (x *CallUpdatedStatusEvent) GetCallLogId() int64 {
	if x != nil {
		return x.CallLogId
	}
	return 0
}

func (x *CallUpdatedStatusEvent) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CallUpdatedStatusEvent) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CallUpdatedStatusEvent) GetTwilioCallSid() string {
	if x != nil {
		return x.TwilioCallSid
	}
	return ""
}

func (x *CallUpdatedStatusEvent) GetTwilioConferenceSid() string {
	if x != nil {
		return x.TwilioConferenceSid
	}
	return ""
}

func (x *CallUpdatedStatusEvent) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CallUpdatedStatusEvent) GetDirection() v1.CallingDirection {
	if x != nil {
		return x.Direction
	}
	return v1.CallingDirection(0)
}

func (x *CallUpdatedStatusEvent) GetStatus() v1.Status {
	if x != nil {
		return x.Status
	}
	return v1.Status(0)
}

func (x *CallUpdatedStatusEvent) GetInitTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InitTime
	}
	return nil
}

var File_moego_models_event_bus_v1_call_models_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_call_models_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x03,
	0x0a, 0x16, 0x43, 0x61, 0x6c, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x15,
	0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x77, 0x69,
	0x6c, 0x69, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x4a, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x32, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x33, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x69,
	0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x69, 0x6e, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x62, 0x75, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_call_models_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_call_models_proto_rawDescData = file_moego_models_event_bus_v1_call_models_proto_rawDesc
)

func file_moego_models_event_bus_v1_call_models_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_call_models_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_call_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_call_models_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_call_models_proto_rawDescData
}

var file_moego_models_event_bus_v1_call_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_event_bus_v1_call_models_proto_goTypes = []interface{}{
	(*CallUpdatedStatusEvent)(nil), // 0: moego.models.event_bus.v1.CallUpdatedStatusEvent
	(v1.CallingDirection)(0),       // 1: moego.models.engagement.v1.CallingDirection
	(v1.Status)(0),                 // 2: moego.models.engagement.v1.Status
	(*timestamppb.Timestamp)(nil),  // 3: google.protobuf.Timestamp
}
var file_moego_models_event_bus_v1_call_models_proto_depIdxs = []int32{
	1, // 0: moego.models.event_bus.v1.CallUpdatedStatusEvent.direction:type_name -> moego.models.engagement.v1.CallingDirection
	2, // 1: moego.models.event_bus.v1.CallUpdatedStatusEvent.status:type_name -> moego.models.engagement.v1.Status
	3, // 2: moego.models.event_bus.v1.CallUpdatedStatusEvent.init_time:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_call_models_proto_init() }
func file_moego_models_event_bus_v1_call_models_proto_init() {
	if File_moego_models_event_bus_v1_call_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_event_bus_v1_call_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallUpdatedStatusEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_call_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_call_models_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_call_models_proto_depIdxs,
		MessageInfos:      file_moego_models_event_bus_v1_call_models_proto_msgTypes,
	}.Build()
	File_moego_models_event_bus_v1_call_models_proto = out.File
	file_moego_models_event_bus_v1_call_models_proto_rawDesc = nil
	file_moego_models_event_bus_v1_call_models_proto_goTypes = nil
	file_moego_models_event_bus_v1_call_models_proto_depIdxs = nil
}
