// @since 2024-06-12 11:04:31
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/membership_defs.proto

package membershippb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 周期类型
type PeriodType int32

const (
	// 未指定
	PeriodType_PERIOD_UNSPECIFIED PeriodType = 0
	// 跟随周期
	PeriodType_FOLLOW_MEMBERSHIP PeriodType = 1
	// 指定周期范围
	PeriodType_SPECIFIED PeriodType = 2
)

// Enum value maps for PeriodType.
var (
	PeriodType_name = map[int32]string{
		0: "PERIOD_UNSPECIFIED",
		1: "FOLLOW_MEMBERSHIP",
		2: "SPECIFIED",
	}
	PeriodType_value = map[string]int32{
		"PERIOD_UNSPECIFIED": 0,
		"FOLLOW_MEMBERSHIP":  1,
		"SPECIFIED":          2,
	}
)

func (x PeriodType) Enum() *PeriodType {
	p := new(PeriodType)
	*p = x
	return p
}

func (x PeriodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PeriodType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_membership_defs_proto_enumTypes[0].Descriptor()
}

func (PeriodType) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_membership_defs_proto_enumTypes[0]
}

func (x PeriodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PeriodType.Descriptor instead.
func (PeriodType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{0}
}

// Redeem Source Type
type SourceType int32

const (
	// unspecified
	SourceType_SOURCE_TYPE_UNSPECIFIED SourceType = 0
	// appointment
	SourceType_APPOINTMENT SourceType = 1
	// Booking Request
	SourceType_BOOKING_REQUEST SourceType = 2
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "APPOINTMENT",
		2: "BOOKING_REQUEST",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED": 0,
		"APPOINTMENT":             1,
		"BOOKING_REQUEST":         2,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_membership_defs_proto_enumTypes[1].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_membership_defs_proto_enumTypes[1]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{1}
}

// The Membership Full Definition
type MembershipCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name, will check unique ignore case
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// the description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// the status
	Status MembershipModel_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status" json:"status,omitempty"`
	// the price
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// the tax id
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle *MembershipModel_BillingCycle `protobuf:"varint,8,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle,oneof" json:"billing_cycle,omitempty"`
	// policy
	Policy string `protobuf:"bytes,9,opt,name=policy,proto3" json:"policy,omitempty"`
	// enable purchase for online booking
	EnableOnlineBooking bool `protobuf:"varint,10,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,11,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// enable discount benefits
	EnableDiscountBenefits bool `protobuf:"varint,12,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3" json:"enable_discount_benefits,omitempty"`
	// enable quantity benefits
	EnableQuantityBenefits bool `protobuf:"varint,13,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3" json:"enable_quantity_benefits,omitempty"`
	// billing cycyle day of week
	BillingCycleDayOfWeek *dayofweek.DayOfWeek `protobuf:"varint,14,opt,name=billing_cycle_day_of_week,json=billingCycleDayOfWeek,proto3,enum=google.type.DayOfWeek,oneof" json:"billing_cycle_day_of_week,omitempty"`
	// breed filter
	BreedFilter bool `protobuf:"varint,15,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// customized breed
	CustomizedBreed []*v11.CustomizedBreed `protobuf:"bytes,16,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,17,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,18,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,19,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,20,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// source
	Source MembershipModel_Source `protobuf:"varint,21,opt,name=source,proto3,enum=moego.models.membership.v1.MembershipModel_Source" json:"source,omitempty"`
}

func (x *MembershipCreateDef) Reset() {
	*x = MembershipCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipCreateDef) ProtoMessage() {}

func (x *MembershipCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipCreateDef.ProtoReflect.Descriptor instead.
func (*MembershipCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{0}
}

func (x *MembershipCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MembershipCreateDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MembershipCreateDef) GetStatus() MembershipModel_Status {
	if x != nil {
		return x.Status
	}
	return MembershipModel_STATUS_UNSPECIFIED
}

func (x *MembershipCreateDef) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *MembershipCreateDef) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *MembershipCreateDef) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil && x.BillingCycle != nil {
		return *x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *MembershipCreateDef) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *MembershipCreateDef) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *MembershipCreateDef) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *MembershipCreateDef) GetEnableDiscountBenefits() bool {
	if x != nil {
		return x.EnableDiscountBenefits
	}
	return false
}

func (x *MembershipCreateDef) GetEnableQuantityBenefits() bool {
	if x != nil {
		return x.EnableQuantityBenefits
	}
	return false
}

func (x *MembershipCreateDef) GetBillingCycleDayOfWeek() dayofweek.DayOfWeek {
	if x != nil && x.BillingCycleDayOfWeek != nil {
		return *x.BillingCycleDayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *MembershipCreateDef) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *MembershipCreateDef) GetCustomizedBreed() []*v11.CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *MembershipCreateDef) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *MembershipCreateDef) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

func (x *MembershipCreateDef) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *MembershipCreateDef) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *MembershipCreateDef) GetSource() MembershipModel_Source {
	if x != nil {
		return x.Source
	}
	return MembershipModel_SOURCE_UNSPECIFIED
}

// The Membership Partial Definition
type MembershipUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name, will check unique ignore case
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// the description
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// the status
	Status *MembershipModel_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status,oneof" json:"status,omitempty"`
	// the price
	Price *float64 `protobuf:"fixed64,6,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// the tax id
	TaxId *int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle *MembershipModel_BillingCycle `protobuf:"varint,8,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle,oneof" json:"billing_cycle,omitempty"`
	// policy
	Policy *string `protobuf:"bytes,9,opt,name=policy,proto3,oneof" json:"policy,omitempty"`
	// enable purchase for online booking
	EnableOnlineBooking *bool `protobuf:"varint,10,opt,name=enable_online_booking,json=enableOnlineBooking,proto3,oneof" json:"enable_online_booking,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,11,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// enable discount benefits
	EnableDiscountBenefits *bool `protobuf:"varint,12,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3,oneof" json:"enable_discount_benefits,omitempty"`
	// enable quantity benefits
	EnableQuantityBenefits *bool `protobuf:"varint,13,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3,oneof" json:"enable_quantity_benefits,omitempty"`
	// billing cycyle day of week
	BillingCycleDayOfWeek *dayofweek.DayOfWeek `protobuf:"varint,14,opt,name=billing_cycle_day_of_week,json=billingCycleDayOfWeek,proto3,enum=google.type.DayOfWeek,oneof" json:"billing_cycle_day_of_week,omitempty"`
	// breed filter
	BreedFilter bool `protobuf:"varint,15,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// customized breed
	CustomizedBreed []*v11.CustomizedBreed `protobuf:"bytes,16,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,17,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,18,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,19,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,20,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// source
	Source *MembershipModel_Source `protobuf:"varint,21,opt,name=source,proto3,enum=moego.models.membership.v1.MembershipModel_Source,oneof" json:"source,omitempty"`
}

func (x *MembershipUpdateDef) Reset() {
	*x = MembershipUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipUpdateDef) ProtoMessage() {}

func (x *MembershipUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipUpdateDef.ProtoReflect.Descriptor instead.
func (*MembershipUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{1}
}

func (x *MembershipUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *MembershipUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *MembershipUpdateDef) GetStatus() MembershipModel_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MembershipModel_STATUS_UNSPECIFIED
}

func (x *MembershipUpdateDef) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *MembershipUpdateDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *MembershipUpdateDef) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil && x.BillingCycle != nil {
		return *x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *MembershipUpdateDef) GetPolicy() string {
	if x != nil && x.Policy != nil {
		return *x.Policy
	}
	return ""
}

func (x *MembershipUpdateDef) GetEnableOnlineBooking() bool {
	if x != nil && x.EnableOnlineBooking != nil {
		return *x.EnableOnlineBooking
	}
	return false
}

func (x *MembershipUpdateDef) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *MembershipUpdateDef) GetEnableDiscountBenefits() bool {
	if x != nil && x.EnableDiscountBenefits != nil {
		return *x.EnableDiscountBenefits
	}
	return false
}

func (x *MembershipUpdateDef) GetEnableQuantityBenefits() bool {
	if x != nil && x.EnableQuantityBenefits != nil {
		return *x.EnableQuantityBenefits
	}
	return false
}

func (x *MembershipUpdateDef) GetBillingCycleDayOfWeek() dayofweek.DayOfWeek {
	if x != nil && x.BillingCycleDayOfWeek != nil {
		return *x.BillingCycleDayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *MembershipUpdateDef) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *MembershipUpdateDef) GetCustomizedBreed() []*v11.CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *MembershipUpdateDef) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *MembershipUpdateDef) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

func (x *MembershipUpdateDef) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *MembershipUpdateDef) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *MembershipUpdateDef) GetSource() MembershipModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return MembershipModel_SOURCE_UNSPECIFIED
}

// message for membership benefits
type MembershipQuantityBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership id
	MembershipId int64 `protobuf:"varint,1,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// quality, a typo, should be quantity
	QuantityDefs []*QualityBenefitModel `protobuf:"bytes,2,rep,name=quantity_defs,json=quantityDefs,proto3" json:"quantity_defs,omitempty"`
	// 周期类型
	// period type
	PeriodType PeriodType `protobuf:"varint,3,opt,name=period_type,json=periodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"period_type,omitempty"`
	// specified period
	SpecifiedPeriod *v1.TimePeriod `protobuf:"bytes,4,opt,name=specified_period,json=specifiedPeriod,proto3" json:"specified_period,omitempty"`
}

func (x *MembershipQuantityBenefitsDef) Reset() {
	*x = MembershipQuantityBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipQuantityBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipQuantityBenefitsDef) ProtoMessage() {}

func (x *MembershipQuantityBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipQuantityBenefitsDef.ProtoReflect.Descriptor instead.
func (*MembershipQuantityBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{2}
}

func (x *MembershipQuantityBenefitsDef) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *MembershipQuantityBenefitsDef) GetQuantityDefs() []*QualityBenefitModel {
	if x != nil {
		return x.QuantityDefs
	}
	return nil
}

func (x *MembershipQuantityBenefitsDef) GetPeriodType() PeriodType {
	if x != nil {
		return x.PeriodType
	}
	return PeriodType_PERIOD_UNSPECIFIED
}

func (x *MembershipQuantityBenefitsDef) GetSpecifiedPeriod() *v1.TimePeriod {
	if x != nil {
		return x.SpecifiedPeriod
	}
	return nil
}

// membership discount benefit
type MembershipDiscountBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership id
	MembershipId int64 `protobuf:"varint,1,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// discounts
	Discounts []*DiscountBenefitModel `protobuf:"bytes,2,rep,name=discounts,proto3" json:"discounts,omitempty"`
}

func (x *MembershipDiscountBenefitsDef) Reset() {
	*x = MembershipDiscountBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipDiscountBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipDiscountBenefitsDef) ProtoMessage() {}

func (x *MembershipDiscountBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipDiscountBenefitsDef.ProtoReflect.Descriptor instead.
func (*MembershipDiscountBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{3}
}

func (x *MembershipDiscountBenefitsDef) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *MembershipDiscountBenefitsDef) GetDiscounts() []*DiscountBenefitModel {
	if x != nil {
		return x.Discounts
	}
	return nil
}

// membership discount benefit
type CreateMembershipDiscountBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discounts
	Discounts []*DiscountDef `protobuf:"bytes,1,rep,name=discounts,proto3" json:"discounts,omitempty"`
}

func (x *CreateMembershipDiscountBenefitsDef) Reset() {
	*x = CreateMembershipDiscountBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipDiscountBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipDiscountBenefitsDef) ProtoMessage() {}

func (x *CreateMembershipDiscountBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipDiscountBenefitsDef.ProtoReflect.Descriptor instead.
func (*CreateMembershipDiscountBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{4}
}

func (x *CreateMembershipDiscountBenefitsDef) GetDiscounts() []*DiscountDef {
	if x != nil {
		return x.Discounts
	}
	return nil
}

// CreateMembershipQuantityBenefitsDef
type CreateMembershipQuantityBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity
	Quantities []*QuantityDef `protobuf:"bytes,1,rep,name=quantities,proto3" json:"quantities,omitempty"`
	// period type
	PeriodType PeriodType `protobuf:"varint,2,opt,name=period_type,json=periodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"period_type,omitempty"`
	// specified period
	SpecifiedPeriod *v1.TimePeriod `protobuf:"bytes,3,opt,name=specified_period,json=specifiedPeriod,proto3" json:"specified_period,omitempty"`
}

func (x *CreateMembershipQuantityBenefitsDef) Reset() {
	*x = CreateMembershipQuantityBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMembershipQuantityBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMembershipQuantityBenefitsDef) ProtoMessage() {}

func (x *CreateMembershipQuantityBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMembershipQuantityBenefitsDef.ProtoReflect.Descriptor instead.
func (*CreateMembershipQuantityBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{5}
}

func (x *CreateMembershipQuantityBenefitsDef) GetQuantities() []*QuantityDef {
	if x != nil {
		return x.Quantities
	}
	return nil
}

func (x *CreateMembershipQuantityBenefitsDef) GetPeriodType() PeriodType {
	if x != nil {
		return x.PeriodType
	}
	return PeriodType_PERIOD_UNSPECIFIED
}

func (x *CreateMembershipQuantityBenefitsDef) GetSpecifiedPeriod() *v1.TimePeriod {
	if x != nil {
		return x.SpecifiedPeriod
	}
	return nil
}

// membership discount benefit
type UpdateMembershipDiscountBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discounts
	Discounts []*DiscountDef `protobuf:"bytes,1,rep,name=discounts,proto3" json:"discounts,omitempty"`
}

func (x *UpdateMembershipDiscountBenefitsDef) Reset() {
	*x = UpdateMembershipDiscountBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipDiscountBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipDiscountBenefitsDef) ProtoMessage() {}

func (x *UpdateMembershipDiscountBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipDiscountBenefitsDef.ProtoReflect.Descriptor instead.
func (*UpdateMembershipDiscountBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateMembershipDiscountBenefitsDef) GetDiscounts() []*DiscountDef {
	if x != nil {
		return x.Discounts
	}
	return nil
}

// UpdateMembershipQuantityBenefitsDef
type UpdateMembershipQuantityBenefitsDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity
	Quantities []*QuantityDef `protobuf:"bytes,1,rep,name=quantities,proto3" json:"quantities,omitempty"`
	// period type
	PeriodType PeriodType `protobuf:"varint,2,opt,name=period_type,json=periodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"period_type,omitempty"`
	// specified period
	SpecifiedPeriod *v1.TimePeriod `protobuf:"bytes,3,opt,name=specified_period,json=specifiedPeriod,proto3" json:"specified_period,omitempty"`
}

func (x *UpdateMembershipQuantityBenefitsDef) Reset() {
	*x = UpdateMembershipQuantityBenefitsDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMembershipQuantityBenefitsDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMembershipQuantityBenefitsDef) ProtoMessage() {}

func (x *UpdateMembershipQuantityBenefitsDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMembershipQuantityBenefitsDef.ProtoReflect.Descriptor instead.
func (*UpdateMembershipQuantityBenefitsDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateMembershipQuantityBenefitsDef) GetQuantities() []*QuantityDef {
	if x != nil {
		return x.Quantities
	}
	return nil
}

func (x *UpdateMembershipQuantityBenefitsDef) GetPeriodType() PeriodType {
	if x != nil {
		return x.PeriodType
	}
	return PeriodType_PERIOD_UNSPECIFIED
}

func (x *UpdateMembershipQuantityBenefitsDef) GetSpecifiedPeriod() *v1.TimePeriod {
	if x != nil {
		return x.SpecifiedPeriod
	}
	return nil
}

// discount def
type DiscountDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is all
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// ids
	TargetIds []int64 `protobuf:"varint,2,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
	// unit
	Unit DiscountUnit `protobuf:"varint,4,opt,name=unit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"unit,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
}

func (x *DiscountDef) Reset() {
	*x = DiscountDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountDef) ProtoMessage() {}

func (x *DiscountDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountDef.ProtoReflect.Descriptor instead.
func (*DiscountDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{8}
}

func (x *DiscountDef) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *DiscountDef) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *DiscountDef) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *DiscountDef) GetUnit() DiscountUnit {
	if x != nil {
		return x.Unit
	}
	return DiscountUnit_UNIT_UNSPECIFIED
}

func (x *DiscountDef) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

// quantity
type QuantityDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is limit
	IsLimited bool `protobuf:"varint,1,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// count
	LimitedValue int64 `protobuf:"varint,2,opt,name=limited_value,json=limitedValue,proto3" json:"limited_value,omitempty"`
	// id
	TargetId int64 `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target type
	TargetSubType TargetSubType `protobuf:"varint,5,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
}

func (x *QuantityDef) Reset() {
	*x = QuantityDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantityDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantityDef) ProtoMessage() {}

func (x *QuantityDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantityDef.ProtoReflect.Descriptor instead.
func (*QuantityDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{9}
}

func (x *QuantityDef) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *QuantityDef) GetLimitedValue() int64 {
	if x != nil {
		return x.LimitedValue
	}
	return 0
}

func (x *QuantityDef) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *QuantityDef) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *QuantityDef) GetTargetSubType() TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return TargetSubType_TARGET_SUB_TYPE_UNSPECIFIED
}

// perk cycle item
type PerkCycleItemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// validity start time
	ValidityStartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=validity_start_time,json=validityStartTime,proto3" json:"validity_start_time,omitempty"`
	// total remain perk amount
	TotalRemainPerkAmount int64 `protobuf:"varint,2,opt,name=total_remain_perk_amount,json=totalRemainPerkAmount,proto3" json:"total_remain_perk_amount,omitempty"`
	// perks
	Perks []*IncludeBenefitView `protobuf:"bytes,3,rep,name=perks,proto3" json:"perks,omitempty"`
}

func (x *PerkCycleItemDef) Reset() {
	*x = PerkCycleItemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerkCycleItemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerkCycleItemDef) ProtoMessage() {}

func (x *PerkCycleItemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerkCycleItemDef.ProtoReflect.Descriptor instead.
func (*PerkCycleItemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{10}
}

func (x *PerkCycleItemDef) GetValidityStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidityStartTime
	}
	return nil
}

func (x *PerkCycleItemDef) GetTotalRemainPerkAmount() int64 {
	if x != nil {
		return x.TotalRemainPerkAmount
	}
	return 0
}

func (x *PerkCycleItemDef) GetPerks() []*IncludeBenefitView {
	if x != nil {
		return x.Perks
	}
	return nil
}

// pet filter
type PetFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type
	PetType v12.PetType `protobuf:"varint,1,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet breed
	PetBreed *string `protobuf:"bytes,2,opt,name=pet_breed,json=petBreed,proto3,oneof" json:"pet_breed,omitempty"`
	// pet size
	PetSizeId *int64 `protobuf:"varint,3,opt,name=pet_size_id,json=petSizeId,proto3,oneof" json:"pet_size_id,omitempty"`
	// pet code
	PetCoatId *int64 `protobuf:"varint,4,opt,name=pet_coat_id,json=petCoatId,proto3,oneof" json:"pet_coat_id,omitempty"`
}

func (x *PetFilter) Reset() {
	*x = PetFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetFilter) ProtoMessage() {}

func (x *PetFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetFilter.ProtoReflect.Descriptor instead.
func (*PetFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{11}
}

func (x *PetFilter) GetPetType() v12.PetType {
	if x != nil {
		return x.PetType
	}
	return v12.PetType(0)
}

func (x *PetFilter) GetPetBreed() string {
	if x != nil && x.PetBreed != nil {
		return *x.PetBreed
	}
	return ""
}

func (x *PetFilter) GetPetSizeId() int64 {
	if x != nil && x.PetSizeId != nil {
		return *x.PetSizeId
	}
	return 0
}

func (x *PetFilter) GetPetCoatId() int64 {
	if x != nil && x.PetCoatId != nil {
		return *x.PetCoatId
	}
	return 0
}

// filter
type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet filter
	PetFilters []*PetFilter `protobuf:"bytes,1,rep,name=pet_filters,json=petFilters,proto3" json:"pet_filters,omitempty"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{12}
}

func (x *Filter) GetPetFilters() []*PetFilter {
	if x != nil {
		return x.PetFilters
	}
	return nil
}

// perk detail
type PerkDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target type
	TargetType TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target sub type
	TargetSubType TargetSubType `protobuf:"varint,2,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
	// remain amount
	RemainPerkAmount int64 `protobuf:"varint,3,opt,name=remain_perk_amount,json=remainPerkAmount,proto3" json:"remain_perk_amount,omitempty"`
	// is limited
	IsLimited bool `protobuf:"varint,4,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// validity end time
	ValidityEndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=validity_end_time,json=validityEndTime,proto3" json:"validity_end_time,omitempty"`
}

func (x *PerkDetail) Reset() {
	*x = PerkDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerkDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerkDetail) ProtoMessage() {}

func (x *PerkDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_defs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerkDetail.ProtoReflect.Descriptor instead.
func (*PerkDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP(), []int{13}
}

func (x *PerkDetail) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *PerkDetail) GetTargetSubType() TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return TargetSubType_TARGET_SUB_TYPE_UNSPECIFIED
}

func (x *PerkDetail) GetRemainPerkAmount() int64 {
	if x != nil {
		return x.RemainPerkAmount
	}
	return 0
}

func (x *PerkDetail) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *PerkDetail) GetValidityEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidityEndTime
	}
	return nil
}

var File_moego_models_membership_v1_membership_defs_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_membership_defs_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79,
	0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf7, 0x08,
	0x0a, 0x13, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xdc, 0x0b, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x56, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x0c,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x20, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x90, 0x4e, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52,
	0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x38, 0x0a,
	0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x19, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f,
	0x77, 0x65, 0x65, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65,
	0x65, 0x6b, 0x48, 0x01, 0x52, 0x15, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63,
	0x6c, 0x65, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x21,
	0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x30, 0x0a, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61, 0x74, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74, 0x12, 0x4a, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f,
	0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x22, 0xcc, 0x0a, 0x0a, 0x13, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x22, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xdc, 0x0b, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x32, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40,
	0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x04,
	0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x66, 0x0a, 0x0d, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x48,
	0x05, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x25, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x90, 0x4e, 0x48, 0x06, 0x52, 0x06,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x12, 0x3d, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x3d, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x09, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x55,
	0x0a, 0x19, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x48, 0x0a, 0x52, 0x15, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65,
	0x65, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x12,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63,
	0x6f, 0x61, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f,
	0x61, 0x74, 0x12, 0x4f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x0b, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x42, 0x1c, 0x0a,
	0x1a, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xaa, 0x02, 0x0a, 0x1d, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65, 0x66, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x54, 0x0a,
	0x0d, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x66, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x10,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x1d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x44, 0x65, 0x66, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x09, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x6c, 0x0a, 0x23, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65,
	0x66, 0x12, 0x45, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x52, 0x09, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xfe, 0x01, 0x0a, 0x23, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65, 0x66,
	0x12, 0x47, 0x0a, 0x0a, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x6c, 0x0a, 0x23, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65, 0x66,
	0x12, 0x45, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x52, 0x09, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xfe, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x51, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65, 0x66, 0x12,
	0x47, 0x0a, 0x0a, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x0b, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x53,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xa0, 0x02, 0x0a, 0x0b, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x0f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xdd, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x6b, 0x43,
	0x79, 0x63, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12, 0x4a, 0x0a, 0x13, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6b, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x44, 0x0a, 0x05, 0x70, 0x65, 0x72, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x05, 0x70, 0x65, 0x72, 0x6b, 0x73, 0x22, 0xe3, 0x01, 0x0a, 0x09, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x70, 0x65, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0b, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xbd,
	0x02, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x47, 0x0a,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x50, 0x65, 0x72,
	0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0x4a,
	0x0a, 0x0a, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x4d,
	0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x4f, 0x0a, 0x0a, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x02, 0x42, 0x84, 0x01, 0x0a, 0x22,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_membership_defs_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_membership_defs_proto_rawDescData = file_moego_models_membership_v1_membership_defs_proto_rawDesc
)

func file_moego_models_membership_v1_membership_defs_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_membership_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_membership_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_membership_defs_proto_rawDescData)
	})
	return file_moego_models_membership_v1_membership_defs_proto_rawDescData
}

var file_moego_models_membership_v1_membership_defs_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_membership_v1_membership_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_models_membership_v1_membership_defs_proto_goTypes = []interface{}{
	(PeriodType)(0),                             // 0: moego.models.membership.v1.PeriodType
	(SourceType)(0),                             // 1: moego.models.membership.v1.SourceType
	(*MembershipCreateDef)(nil),                 // 2: moego.models.membership.v1.MembershipCreateDef
	(*MembershipUpdateDef)(nil),                 // 3: moego.models.membership.v1.MembershipUpdateDef
	(*MembershipQuantityBenefitsDef)(nil),       // 4: moego.models.membership.v1.MembershipQuantityBenefitsDef
	(*MembershipDiscountBenefitsDef)(nil),       // 5: moego.models.membership.v1.MembershipDiscountBenefitsDef
	(*CreateMembershipDiscountBenefitsDef)(nil), // 6: moego.models.membership.v1.CreateMembershipDiscountBenefitsDef
	(*CreateMembershipQuantityBenefitsDef)(nil), // 7: moego.models.membership.v1.CreateMembershipQuantityBenefitsDef
	(*UpdateMembershipDiscountBenefitsDef)(nil), // 8: moego.models.membership.v1.UpdateMembershipDiscountBenefitsDef
	(*UpdateMembershipQuantityBenefitsDef)(nil), // 9: moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef
	(*DiscountDef)(nil),                         // 10: moego.models.membership.v1.DiscountDef
	(*QuantityDef)(nil),                         // 11: moego.models.membership.v1.QuantityDef
	(*PerkCycleItemDef)(nil),                    // 12: moego.models.membership.v1.PerkCycleItemDef
	(*PetFilter)(nil),                           // 13: moego.models.membership.v1.PetFilter
	(*Filter)(nil),                              // 14: moego.models.membership.v1.Filter
	(*PerkDetail)(nil),                          // 15: moego.models.membership.v1.PerkDetail
	(MembershipModel_Status)(0),                 // 16: moego.models.membership.v1.MembershipModel.Status
	(MembershipModel_BillingCycle)(0),           // 17: moego.models.membership.v1.MembershipModel.BillingCycle
	(*v1.TimePeriod)(nil),                       // 18: moego.utils.v1.TimePeriod
	(dayofweek.DayOfWeek)(0),                    // 19: google.type.DayOfWeek
	(*v11.CustomizedBreed)(nil),                 // 20: moego.models.offering.v1.CustomizedBreed
	(MembershipModel_Source)(0),                 // 21: moego.models.membership.v1.MembershipModel.Source
	(*QualityBenefitModel)(nil),                 // 22: moego.models.membership.v1.QualityBenefitModel
	(*DiscountBenefitModel)(nil),                // 23: moego.models.membership.v1.DiscountBenefitModel
	(DiscountUnit)(0),                           // 24: moego.models.membership.v1.DiscountUnit
	(TargetType)(0),                             // 25: moego.models.membership.v1.TargetType
	(TargetSubType)(0),                          // 26: moego.models.membership.v1.TargetSubType
	(*timestamppb.Timestamp)(nil),               // 27: google.protobuf.Timestamp
	(*IncludeBenefitView)(nil),                  // 28: moego.models.membership.v1.IncludeBenefitView
	(v12.PetType)(0),                            // 29: moego.models.customer.v1.PetType
}
var file_moego_models_membership_v1_membership_defs_proto_depIdxs = []int32{
	16, // 0: moego.models.membership.v1.MembershipCreateDef.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	17, // 1: moego.models.membership.v1.MembershipCreateDef.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	18, // 2: moego.models.membership.v1.MembershipCreateDef.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	19, // 3: moego.models.membership.v1.MembershipCreateDef.billing_cycle_day_of_week:type_name -> google.type.DayOfWeek
	20, // 4: moego.models.membership.v1.MembershipCreateDef.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	21, // 5: moego.models.membership.v1.MembershipCreateDef.source:type_name -> moego.models.membership.v1.MembershipModel.Source
	16, // 6: moego.models.membership.v1.MembershipUpdateDef.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	17, // 7: moego.models.membership.v1.MembershipUpdateDef.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	18, // 8: moego.models.membership.v1.MembershipUpdateDef.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	19, // 9: moego.models.membership.v1.MembershipUpdateDef.billing_cycle_day_of_week:type_name -> google.type.DayOfWeek
	20, // 10: moego.models.membership.v1.MembershipUpdateDef.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	21, // 11: moego.models.membership.v1.MembershipUpdateDef.source:type_name -> moego.models.membership.v1.MembershipModel.Source
	22, // 12: moego.models.membership.v1.MembershipQuantityBenefitsDef.quantity_defs:type_name -> moego.models.membership.v1.QualityBenefitModel
	0,  // 13: moego.models.membership.v1.MembershipQuantityBenefitsDef.period_type:type_name -> moego.models.membership.v1.PeriodType
	18, // 14: moego.models.membership.v1.MembershipQuantityBenefitsDef.specified_period:type_name -> moego.utils.v1.TimePeriod
	23, // 15: moego.models.membership.v1.MembershipDiscountBenefitsDef.discounts:type_name -> moego.models.membership.v1.DiscountBenefitModel
	10, // 16: moego.models.membership.v1.CreateMembershipDiscountBenefitsDef.discounts:type_name -> moego.models.membership.v1.DiscountDef
	11, // 17: moego.models.membership.v1.CreateMembershipQuantityBenefitsDef.quantities:type_name -> moego.models.membership.v1.QuantityDef
	0,  // 18: moego.models.membership.v1.CreateMembershipQuantityBenefitsDef.period_type:type_name -> moego.models.membership.v1.PeriodType
	18, // 19: moego.models.membership.v1.CreateMembershipQuantityBenefitsDef.specified_period:type_name -> moego.utils.v1.TimePeriod
	10, // 20: moego.models.membership.v1.UpdateMembershipDiscountBenefitsDef.discounts:type_name -> moego.models.membership.v1.DiscountDef
	11, // 21: moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef.quantities:type_name -> moego.models.membership.v1.QuantityDef
	0,  // 22: moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef.period_type:type_name -> moego.models.membership.v1.PeriodType
	18, // 23: moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef.specified_period:type_name -> moego.utils.v1.TimePeriod
	24, // 24: moego.models.membership.v1.DiscountDef.unit:type_name -> moego.models.membership.v1.DiscountUnit
	25, // 25: moego.models.membership.v1.DiscountDef.target_type:type_name -> moego.models.membership.v1.TargetType
	25, // 26: moego.models.membership.v1.QuantityDef.target_type:type_name -> moego.models.membership.v1.TargetType
	26, // 27: moego.models.membership.v1.QuantityDef.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	27, // 28: moego.models.membership.v1.PerkCycleItemDef.validity_start_time:type_name -> google.protobuf.Timestamp
	28, // 29: moego.models.membership.v1.PerkCycleItemDef.perks:type_name -> moego.models.membership.v1.IncludeBenefitView
	29, // 30: moego.models.membership.v1.PetFilter.pet_type:type_name -> moego.models.customer.v1.PetType
	13, // 31: moego.models.membership.v1.Filter.pet_filters:type_name -> moego.models.membership.v1.PetFilter
	25, // 32: moego.models.membership.v1.PerkDetail.target_type:type_name -> moego.models.membership.v1.TargetType
	26, // 33: moego.models.membership.v1.PerkDetail.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	27, // 34: moego.models.membership.v1.PerkDetail.validity_end_time:type_name -> google.protobuf.Timestamp
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_membership_defs_proto_init() }
func file_moego_models_membership_v1_membership_defs_proto_init() {
	if File_moego_models_membership_v1_membership_defs_proto != nil {
		return
	}
	file_moego_models_membership_v1_membership_models_proto_init()
	file_moego_models_membership_v1_redeem_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipQuantityBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipDiscountBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipDiscountBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMembershipQuantityBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipDiscountBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMembershipQuantityBenefitsDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantityDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerkCycleItemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_defs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerkDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_membership_v1_membership_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_defs_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_membership_defs_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_membership_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_membership_defs_proto_depIdxs,
		EnumInfos:         file_moego_models_membership_v1_membership_defs_proto_enumTypes,
		MessageInfos:      file_moego_models_membership_v1_membership_defs_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_membership_defs_proto = out.File
	file_moego_models_membership_v1_membership_defs_proto_rawDesc = nil
	file_moego_models_membership_v1_membership_defs_proto_goTypes = nil
	file_moego_models_membership_v1_membership_defs_proto_depIdxs = nil
}
