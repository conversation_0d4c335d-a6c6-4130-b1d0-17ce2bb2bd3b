// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/data_migration_models.proto

package membershippb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// membership data
type MembershipData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid 虚拟 ID，用于做关联关系绑定
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// name，有唯一索引
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// description，可为空
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// the price
	Price *money.Money `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	// price id 仅作为返回值
	PriceId int64 `protobuf:"varint,7,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// product id 仅作为返回值
	ProductId int64 `protobuf:"varint,8,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,9,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// billing cycle period
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,10,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// billing cycle day of week, 收款日，仅对订阅周期为 week 的 membership 生效
	BillingCycleDayOfWeek dayofweek.DayOfWeek `protobuf:"varint,11,opt,name=billing_cycle_day_of_week,json=billingCycleDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"billing_cycle_day_of_week,omitempty"`
}

func (x *MembershipData) Reset() {
	*x = MembershipData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipData) ProtoMessage() {}

func (x *MembershipData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipData.ProtoReflect.Descriptor instead.
func (*MembershipData) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP(), []int{0}
}

func (x *MembershipData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *MembershipData) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MembershipData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MembershipData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MembershipData) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *MembershipData) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *MembershipData) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *MembershipData) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *MembershipData) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *MembershipData) GetBillingCycleDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.BillingCycleDayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

// discount benefit data
type DiscountBenefitData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid 虚拟 ID，用于做关联关系绑定
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,3,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target ids
	TargetIds []int64 `protobuf:"varint,5,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// is all
	IsAll bool `protobuf:"varint,6,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// discount unit
	DiscountUnit DiscountUnit `protobuf:"varint,7,opt,name=discount_unit,json=discountUnit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"discount_unit,omitempty"`
	// discount value
	DiscountValue float64 `protobuf:"fixed64,8,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	// feature id, 仅作为返回值
	FeatureId int64 `protobuf:"varint,9,opt,name=feature_id,json=featureId,proto3" json:"feature_id,omitempty"`
}

func (x *DiscountBenefitData) Reset() {
	*x = DiscountBenefitData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountBenefitData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountBenefitData) ProtoMessage() {}

func (x *DiscountBenefitData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountBenefitData.ProtoReflect.Descriptor instead.
func (*DiscountBenefitData) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP(), []int{1}
}

func (x *DiscountBenefitData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DiscountBenefitData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *DiscountBenefitData) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *DiscountBenefitData) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *DiscountBenefitData) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *DiscountBenefitData) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *DiscountBenefitData) GetDiscountUnit() DiscountUnit {
	if x != nil {
		return x.DiscountUnit
	}
	return DiscountUnit_UNIT_UNSPECIFIED
}

func (x *DiscountBenefitData) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *DiscountBenefitData) GetFeatureId() int64 {
	if x != nil {
		return x.FeatureId
	}
	return 0
}

// quantity benefit data
type QuantityBenefitData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid 虚拟 ID，用于做关联关系绑定
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,3,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,5,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// is limited
	IsLimited bool `protobuf:"varint,6,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// limited value
	LimitedValue int64 `protobuf:"varint,7,opt,name=limited_value,json=limitedValue,proto3" json:"limited_value,omitempty"`
	// period type
	PeriodType PeriodType `protobuf:"varint,8,opt,name=period_type,json=periodType,proto3,enum=moego.models.membership.v1.PeriodType" json:"period_type,omitempty"`
	// specified period
	SpecifiedPeriod *v1.TimePeriod `protobuf:"bytes,9,opt,name=specified_period,json=specifiedPeriod,proto3" json:"specified_period,omitempty"`
	// feature id, 仅作为返回值
	FeatureId int64 `protobuf:"varint,10,opt,name=feature_id,json=featureId,proto3" json:"feature_id,omitempty"`
}

func (x *QuantityBenefitData) Reset() {
	*x = QuantityBenefitData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantityBenefitData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantityBenefitData) ProtoMessage() {}

func (x *QuantityBenefitData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantityBenefitData.ProtoReflect.Descriptor instead.
func (*QuantityBenefitData) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP(), []int{2}
}

func (x *QuantityBenefitData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QuantityBenefitData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *QuantityBenefitData) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *QuantityBenefitData) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *QuantityBenefitData) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *QuantityBenefitData) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *QuantityBenefitData) GetLimitedValue() int64 {
	if x != nil {
		return x.LimitedValue
	}
	return 0
}

func (x *QuantityBenefitData) GetPeriodType() PeriodType {
	if x != nil {
		return x.PeriodType
	}
	return PeriodType_PERIOD_UNSPECIFIED
}

func (x *QuantityBenefitData) GetSpecifiedPeriod() *v1.TimePeriod {
	if x != nil {
		return x.SpecifiedPeriod
	}
	return nil
}

func (x *QuantityBenefitData) GetFeatureId() int64 {
	if x != nil {
		return x.FeatureId
	}
	return 0
}

// subscription data
// 只需要导入当期的 subscription，已过期的 subscription 不需要导入
type SubscriptionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid 虚拟 ID，用于做关联关系绑定
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,3,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// the start time of the subscription period
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time of the subscription period
	EndTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// auto resume at，什么时候开始收钱
	AutoResumeAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=auto_resume_at,json=autoResumeAt,proto3" json:"auto_resume_at,omitempty"`
}

func (x *SubscriptionData) Reset() {
	*x = SubscriptionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionData) ProtoMessage() {}

func (x *SubscriptionData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionData.ProtoReflect.Descriptor instead.
func (*SubscriptionData) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP(), []int{3}
}

func (x *SubscriptionData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *SubscriptionData) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *SubscriptionData) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SubscriptionData) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SubscriptionData) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SubscriptionData) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *SubscriptionData) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *SubscriptionData) GetAutoResumeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoResumeAt
	}
	return nil
}

// quantity entitlement data
// 已过期的 subscription 但未消费完的 entitlement 可以通过这个数据导入
type QuantityEntitlementData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 导入时不需要填写仅作为返回值，更新时需要填写，删除时填负数
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid 虚拟 ID，用于做关联关系绑定
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// subscription id
	SubscriptionId int64 `protobuf:"varint,3,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// benefit id
	BenefitId int64 `protobuf:"varint,4,opt,name=benefit_id,json=benefitId,proto3" json:"benefit_id,omitempty"`
	// the remaining quantity of this entitlement
	Remaining int64 `protobuf:"varint,5,opt,name=remaining,proto3" json:"remaining,omitempty"`
	// whether this entitlement has usage limits
	IsLimited bool `protobuf:"varint,6,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// the start time of the entitlement period，不填时默认根据 subscription 的 start_time 计算
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// the end time of the entitlement period，不填时默认根据 subscription 的 start_time 和 quantity benefit 配置计算
	EndTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *QuantityEntitlementData) Reset() {
	*x = QuantityEntitlementData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuantityEntitlementData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuantityEntitlementData) ProtoMessage() {}

func (x *QuantityEntitlementData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_data_migration_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuantityEntitlementData.ProtoReflect.Descriptor instead.
func (*QuantityEntitlementData) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP(), []int{4}
}

func (x *QuantityEntitlementData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QuantityEntitlementData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *QuantityEntitlementData) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *QuantityEntitlementData) GetBenefitId() int64 {
	if x != nil {
		return x.BenefitId
	}
	return 0
}

func (x *QuantityEntitlementData) GetRemaining() int64 {
	if x != nil {
		return x.Remaining
	}
	return 0
}

func (x *QuantityEntitlementData) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *QuantityEntitlementData) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *QuantityEntitlementData) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

var File_moego_models_membership_v1_data_migration_models_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_data_migration_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xa2, 0x03, 0x0a, 0x0e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x61, 0x78, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x12, 0x50, 0x0a, 0x19, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x15, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x4f, 0x66,
	0x57, 0x65, 0x65, 0x6b, 0x22, 0xf0, 0x02, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41,
	0x6c, 0x6c, 0x12, 0x4d, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x22, 0xb5, 0x03, 0x0a, 0x13, 0x51, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x47, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52,
	0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x22,
	0xee, 0x02, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40,
	0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x74,
	0x22, 0xb2, 0x02, 0x0a, 0x17, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_data_migration_models_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_data_migration_models_proto_rawDescData = file_moego_models_membership_v1_data_migration_models_proto_rawDesc
)

func file_moego_models_membership_v1_data_migration_models_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_data_migration_models_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_data_migration_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_data_migration_models_proto_rawDescData)
	})
	return file_moego_models_membership_v1_data_migration_models_proto_rawDescData
}

var file_moego_models_membership_v1_data_migration_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_membership_v1_data_migration_models_proto_goTypes = []interface{}{
	(*MembershipData)(nil),          // 0: moego.models.membership.v1.MembershipData
	(*DiscountBenefitData)(nil),     // 1: moego.models.membership.v1.DiscountBenefitData
	(*QuantityBenefitData)(nil),     // 2: moego.models.membership.v1.QuantityBenefitData
	(*SubscriptionData)(nil),        // 3: moego.models.membership.v1.SubscriptionData
	(*QuantityEntitlementData)(nil), // 4: moego.models.membership.v1.QuantityEntitlementData
	(*money.Money)(nil),             // 5: google.type.Money
	(*v1.TimePeriod)(nil),           // 6: moego.utils.v1.TimePeriod
	(dayofweek.DayOfWeek)(0),        // 7: google.type.DayOfWeek
	(TargetType)(0),                 // 8: moego.models.membership.v1.TargetType
	(DiscountUnit)(0),               // 9: moego.models.membership.v1.DiscountUnit
	(PeriodType)(0),                 // 10: moego.models.membership.v1.PeriodType
	(*timestamppb.Timestamp)(nil),   // 11: google.protobuf.Timestamp
}
var file_moego_models_membership_v1_data_migration_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.membership.v1.MembershipData.price:type_name -> google.type.Money
	6,  // 1: moego.models.membership.v1.MembershipData.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	7,  // 2: moego.models.membership.v1.MembershipData.billing_cycle_day_of_week:type_name -> google.type.DayOfWeek
	8,  // 3: moego.models.membership.v1.DiscountBenefitData.target_type:type_name -> moego.models.membership.v1.TargetType
	9,  // 4: moego.models.membership.v1.DiscountBenefitData.discount_unit:type_name -> moego.models.membership.v1.DiscountUnit
	8,  // 5: moego.models.membership.v1.QuantityBenefitData.target_type:type_name -> moego.models.membership.v1.TargetType
	10, // 6: moego.models.membership.v1.QuantityBenefitData.period_type:type_name -> moego.models.membership.v1.PeriodType
	6,  // 7: moego.models.membership.v1.QuantityBenefitData.specified_period:type_name -> moego.utils.v1.TimePeriod
	11, // 8: moego.models.membership.v1.SubscriptionData.start_time:type_name -> google.protobuf.Timestamp
	11, // 9: moego.models.membership.v1.SubscriptionData.end_time:type_name -> google.protobuf.Timestamp
	11, // 10: moego.models.membership.v1.SubscriptionData.auto_resume_at:type_name -> google.protobuf.Timestamp
	11, // 11: moego.models.membership.v1.QuantityEntitlementData.start_time:type_name -> google.protobuf.Timestamp
	11, // 12: moego.models.membership.v1.QuantityEntitlementData.end_time:type_name -> google.protobuf.Timestamp
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_data_migration_models_proto_init() }
func file_moego_models_membership_v1_data_migration_models_proto_init() {
	if File_moego_models_membership_v1_data_migration_models_proto != nil {
		return
	}
	file_moego_models_membership_v1_membership_defs_proto_init()
	file_moego_models_membership_v1_redeem_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_data_migration_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_data_migration_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountBenefitData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_data_migration_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantityBenefitData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_data_migration_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_data_migration_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuantityEntitlementData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_data_migration_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_data_migration_models_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_data_migration_models_proto_depIdxs,
		MessageInfos:      file_moego_models_membership_v1_data_migration_models_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_data_migration_models_proto = out.File
	file_moego_models_membership_v1_data_migration_models_proto_rawDesc = nil
	file_moego_models_membership_v1_data_migration_models_proto_goTypes = nil
	file_moego_models_membership_v1_data_migration_models_proto_depIdxs = nil
}
