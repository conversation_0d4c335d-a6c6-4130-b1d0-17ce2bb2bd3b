// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/ai_assistant/v1/conversation_template_models.proto

package aiassistantpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The ConversationTemplate model
type ConversationTemplateModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the scenario
	Scenario string `protobuf:"bytes,2,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// the template
	Template string `protobuf:"bytes,3,opt,name=template,proto3" json:"template,omitempty"`
	// the trailer_template
	TrailerTemplate string `protobuf:"bytes,4,opt,name=trailer_template,json=trailerTemplate,proto3" json:"trailer_template,omitempty"`
	// the temperature, default is 1
	Temperature float64 `protobuf:"fixed64,5,opt,name=temperature,proto3" json:"temperature,omitempty"`
	// the operator id
	OperatorId string `protobuf:"bytes,12,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
}

func (x *ConversationTemplateModel) Reset() {
	*x = ConversationTemplateModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversationTemplateModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationTemplateModel) ProtoMessage() {}

func (x *ConversationTemplateModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationTemplateModel.ProtoReflect.Descriptor instead.
func (*ConversationTemplateModel) Descriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescGZIP(), []int{0}
}

func (x *ConversationTemplateModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConversationTemplateModel) GetScenario() string {
	if x != nil {
		return x.Scenario
	}
	return ""
}

func (x *ConversationTemplateModel) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *ConversationTemplateModel) GetTrailerTemplate() string {
	if x != nil {
		return x.TrailerTemplate
	}
	return ""
}

func (x *ConversationTemplateModel) GetTemperature() float64 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *ConversationTemplateModel) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *ConversationTemplateModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ConversationTemplateModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ConversationTemplateModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_moego_models_ai_assistant_v1_conversation_template_models_proto protoreflect.FileDescriptor

var file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x96, 0x03, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65,
	0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescOnce sync.Once
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescData = file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDesc
)

func file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescGZIP() []byte {
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescOnce.Do(func() {
		file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescData)
	})
	return file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDescData
}

var file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_ai_assistant_v1_conversation_template_models_proto_goTypes = []interface{}{
	(*ConversationTemplateModel)(nil), // 0: moego.models.ai_assistant.v1.ConversationTemplateModel
	(*timestamppb.Timestamp)(nil),     // 1: google.protobuf.Timestamp
}
var file_moego_models_ai_assistant_v1_conversation_template_models_proto_depIdxs = []int32{
	1, // 0: moego.models.ai_assistant.v1.ConversationTemplateModel.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: moego.models.ai_assistant.v1.ConversationTemplateModel.updated_at:type_name -> google.protobuf.Timestamp
	1, // 2: moego.models.ai_assistant.v1.ConversationTemplateModel.deleted_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_ai_assistant_v1_conversation_template_models_proto_init() }
func file_moego_models_ai_assistant_v1_conversation_template_models_proto_init() {
	if File_moego_models_ai_assistant_v1_conversation_template_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversationTemplateModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_ai_assistant_v1_conversation_template_models_proto_goTypes,
		DependencyIndexes: file_moego_models_ai_assistant_v1_conversation_template_models_proto_depIdxs,
		MessageInfos:      file_moego_models_ai_assistant_v1_conversation_template_models_proto_msgTypes,
	}.Build()
	File_moego_models_ai_assistant_v1_conversation_template_models_proto = out.File
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_rawDesc = nil
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_goTypes = nil
	file_moego_models_ai_assistant_v1_conversation_template_models_proto_depIdxs = nil
}
