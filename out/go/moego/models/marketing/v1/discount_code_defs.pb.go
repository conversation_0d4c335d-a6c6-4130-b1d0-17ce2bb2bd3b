// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/marketing/v1/discount_code_defs.proto

package marketingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// discount code summary def
type DiscountCodeSummaryDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client name
	ClientNames []string `protobuf:"bytes,1,rep,name=client_names,json=clientNames,proto3" json:"client_names,omitempty"`
	// service name
	ServiceNames []string `protobuf:"bytes,2,rep,name=service_names,json=serviceNames,proto3" json:"service_names,omitempty"`
	// product name
	ProductNames []string `protobuf:"bytes,3,rep,name=product_names,json=productNames,proto3" json:"product_names,omitempty"`
	// location name
	LocationNames []string `protobuf:"bytes,4,rep,name=location_names,json=locationNames,proto3" json:"location_names,omitempty"`
}

func (x *DiscountCodeSummaryDef) Reset() {
	*x = DiscountCodeSummaryDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountCodeSummaryDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountCodeSummaryDef) ProtoMessage() {}

func (x *DiscountCodeSummaryDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountCodeSummaryDef.ProtoReflect.Descriptor instead.
func (*DiscountCodeSummaryDef) Descriptor() ([]byte, []int) {
	return file_moego_models_marketing_v1_discount_code_defs_proto_rawDescGZIP(), []int{0}
}

func (x *DiscountCodeSummaryDef) GetClientNames() []string {
	if x != nil {
		return x.ClientNames
	}
	return nil
}

func (x *DiscountCodeSummaryDef) GetServiceNames() []string {
	if x != nil {
		return x.ServiceNames
	}
	return nil
}

func (x *DiscountCodeSummaryDef) GetProductNames() []string {
	if x != nil {
		return x.ProductNames
	}
	return nil
}

func (x *DiscountCodeSummaryDef) GetLocationNames() []string {
	if x != nil {
		return x.LocationNames
	}
	return nil
}

// item
type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// object id
	ObjectId int64 `protobuf:"varint,1,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// subTotal amount
	SubTotalAmount float64 `protobuf:"fixed64,3,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_moego_models_marketing_v1_discount_code_defs_proto_rawDescGZIP(), []int{1}
}

func (x *Item) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *Item) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Item) GetSubTotalAmount() float64 {
	if x != nil {
		return x.SubTotalAmount
	}
	return 0
}

// expiry def
type ExpiryDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// expiry type
	Type ExpiryType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.marketing.v1.ExpiryType" json:"type,omitempty"`
	// expiry time
	Time *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=time,proto3,oneof" json:"time,omitempty"`
}

func (x *ExpiryDef) Reset() {
	*x = ExpiryDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpiryDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpiryDef) ProtoMessage() {}

func (x *ExpiryDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpiryDef.ProtoReflect.Descriptor instead.
func (*ExpiryDef) Descriptor() ([]byte, []int) {
	return file_moego_models_marketing_v1_discount_code_defs_proto_rawDescGZIP(), []int{2}
}

func (x *ExpiryDef) GetType() ExpiryType {
	if x != nil {
		return x.Type
	}
	return ExpiryType_EXPIRY_TYPE_UNSPECIFIED
}

func (x *ExpiryDef) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

var File_moego_models_marketing_v1_discount_code_defs_proto protoreflect.FileDescriptor

var file_moego_models_marketing_v1_discount_code_defs_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x16, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x44, 0x65, 0x66,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x22, 0x61, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x44, 0x65, 0x66, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x33, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x81,
	0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_marketing_v1_discount_code_defs_proto_rawDescOnce sync.Once
	file_moego_models_marketing_v1_discount_code_defs_proto_rawDescData = file_moego_models_marketing_v1_discount_code_defs_proto_rawDesc
)

func file_moego_models_marketing_v1_discount_code_defs_proto_rawDescGZIP() []byte {
	file_moego_models_marketing_v1_discount_code_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_marketing_v1_discount_code_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_marketing_v1_discount_code_defs_proto_rawDescData)
	})
	return file_moego_models_marketing_v1_discount_code_defs_proto_rawDescData
}

var file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_marketing_v1_discount_code_defs_proto_goTypes = []interface{}{
	(*DiscountCodeSummaryDef)(nil), // 0: moego.models.marketing.v1.DiscountCodeSummaryDef
	(*Item)(nil),                   // 1: moego.models.marketing.v1.Item
	(*ExpiryDef)(nil),              // 2: moego.models.marketing.v1.ExpiryDef
	(ExpiryType)(0),                // 3: moego.models.marketing.v1.ExpiryType
	(*timestamppb.Timestamp)(nil),  // 4: google.protobuf.Timestamp
}
var file_moego_models_marketing_v1_discount_code_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.marketing.v1.ExpiryDef.type:type_name -> moego.models.marketing.v1.ExpiryType
	4, // 1: moego.models.marketing.v1.ExpiryDef.time:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_marketing_v1_discount_code_defs_proto_init() }
func file_moego_models_marketing_v1_discount_code_defs_proto_init() {
	if File_moego_models_marketing_v1_discount_code_defs_proto != nil {
		return
	}
	file_moego_models_marketing_v1_discount_code_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountCodeSummaryDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpiryDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_marketing_v1_discount_code_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_marketing_v1_discount_code_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_marketing_v1_discount_code_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_marketing_v1_discount_code_defs_proto_msgTypes,
	}.Build()
	File_moego_models_marketing_v1_discount_code_defs_proto = out.File
	file_moego_models_marketing_v1_discount_code_defs_proto_rawDesc = nil
	file_moego_models_marketing_v1_discount_code_defs_proto_goTypes = nil
	file_moego_models_marketing_v1_discount_code_defs_proto_depIdxs = nil
}
