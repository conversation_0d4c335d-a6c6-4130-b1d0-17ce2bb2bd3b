// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/billing/v1/subscription_models.proto

package billingpb

import (
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// moego defined subscription status
type SubscriptionModel_Status int32

const (
	// unknown status
	SubscriptionModel_STATUS_UNSPECIFIED SubscriptionModel_Status = 0
	// active
	SubscriptionModel_ACTIVE SubscriptionModel_Status = 1
	// pending，waiting for payment
	SubscriptionModel_PENDING SubscriptionModel_Status = 2
	// incomplete, payment tried but failed
	SubscriptionModel_INCOMPLETE SubscriptionModel_Status = 3
	// expired, renewal failed
	SubscriptionModel_EXPIRED SubscriptionModel_Status = 4
)

// Enum value maps for SubscriptionModel_Status.
var (
	SubscriptionModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "PENDING",
		3: "INCOMPLETE",
		4: "EXPIRED",
	}
	SubscriptionModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"PENDING":            2,
		"INCOMPLETE":         3,
		"EXPIRED":            4,
	}
)

func (x SubscriptionModel_Status) Enum() *SubscriptionModel_Status {
	p := new(SubscriptionModel_Status)
	*p = x
	return p
}

func (x SubscriptionModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_billing_v1_subscription_models_proto_enumTypes[0].Descriptor()
}

func (SubscriptionModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_billing_v1_subscription_models_proto_enumTypes[0]
}

func (x SubscriptionModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionModel_Status.Descriptor instead.
func (SubscriptionModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{0, 0}
}

// From Stripe
// https://docs.stripe.com/api/subscriptions/create#create_subscription-payment_behavior
type SubscriptionModel_PaymentBehavior int32

const (
	// unknown behavior
	SubscriptionModel_BEHAVIOR_UNSPECIFIED SubscriptionModel_PaymentBehavior = 0
	// allow incomplete
	SubscriptionModel_ALLOW_INCOMPLETE SubscriptionModel_PaymentBehavior = 1
	// default incomplete
	SubscriptionModel_DEFAULT_INCOMPLETE SubscriptionModel_PaymentBehavior = 2
	// error if incomplete
	SubscriptionModel_ERROR_IF_INCOMPLETE SubscriptionModel_PaymentBehavior = 3
	// pending if incomplete
	SubscriptionModel_PENDING_IF_INCOMPLETE SubscriptionModel_PaymentBehavior = 4
)

// Enum value maps for SubscriptionModel_PaymentBehavior.
var (
	SubscriptionModel_PaymentBehavior_name = map[int32]string{
		0: "BEHAVIOR_UNSPECIFIED",
		1: "ALLOW_INCOMPLETE",
		2: "DEFAULT_INCOMPLETE",
		3: "ERROR_IF_INCOMPLETE",
		4: "PENDING_IF_INCOMPLETE",
	}
	SubscriptionModel_PaymentBehavior_value = map[string]int32{
		"BEHAVIOR_UNSPECIFIED":  0,
		"ALLOW_INCOMPLETE":      1,
		"DEFAULT_INCOMPLETE":    2,
		"ERROR_IF_INCOMPLETE":   3,
		"PENDING_IF_INCOMPLETE": 4,
	}
)

func (x SubscriptionModel_PaymentBehavior) Enum() *SubscriptionModel_PaymentBehavior {
	p := new(SubscriptionModel_PaymentBehavior)
	*p = x
	return p
}

func (x SubscriptionModel_PaymentBehavior) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionModel_PaymentBehavior) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_billing_v1_subscription_models_proto_enumTypes[1].Descriptor()
}

func (SubscriptionModel_PaymentBehavior) Type() protoreflect.EnumType {
	return &file_moego_models_billing_v1_subscription_models_proto_enumTypes[1]
}

func (x SubscriptionModel_PaymentBehavior) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionModel_PaymentBehavior.Descriptor instead.
func (SubscriptionModel_PaymentBehavior) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{0, 1}
}

// payment collection behavior
type PauseCollection_Behavior int32

const (
	// unknown behavior
	PauseCollection_BEHAVIOR_UNSPECIFIED PauseCollection_Behavior = 0
	// keep as draft
	PauseCollection_KEEP_AS_DRAFT PauseCollection_Behavior = 1
	// mark as uncollectible
	PauseCollection_MARK_UNCOLLECTIBLE PauseCollection_Behavior = 2
	// void
	PauseCollection_VOID PauseCollection_Behavior = 3
)

// Enum value maps for PauseCollection_Behavior.
var (
	PauseCollection_Behavior_name = map[int32]string{
		0: "BEHAVIOR_UNSPECIFIED",
		1: "KEEP_AS_DRAFT",
		2: "MARK_UNCOLLECTIBLE",
		3: "VOID",
	}
	PauseCollection_Behavior_value = map[string]int32{
		"BEHAVIOR_UNSPECIFIED": 0,
		"KEEP_AS_DRAFT":        1,
		"MARK_UNCOLLECTIBLE":   2,
		"VOID":                 3,
	}
)

func (x PauseCollection_Behavior) Enum() *PauseCollection_Behavior {
	p := new(PauseCollection_Behavior)
	*p = x
	return p
}

func (x PauseCollection_Behavior) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PauseCollection_Behavior) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_billing_v1_subscription_models_proto_enumTypes[2].Descriptor()
}

func (PauseCollection_Behavior) Type() protoreflect.EnumType {
	return &file_moego_models_billing_v1_subscription_models_proto_enumTypes[2]
}

func (x PauseCollection_Behavior) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PauseCollection_Behavior.Descriptor instead.
func (PauseCollection_Behavior) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{1, 0}
}

// subscription Object
type SubscriptionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subscription object id
	SubscriptionId string `protobuf:"bytes,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// vendor subscription id
	VendorSubscriptionId string `protobuf:"bytes,3,opt,name=vendor_subscription_id,json=vendorSubscriptionId,proto3" json:"vendor_subscription_id,omitempty"`
	// subscription items
	PlanUnits []*PlanUnit `protobuf:"bytes,4,rep,name=plan_units,json=planUnits,proto3" json:"plan_units,omitempty"`
	// payment method
	PaymentMethod string `protobuf:"bytes,5,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// coupon id
	CouponId *int64 `protobuf:"varint,6,opt,name=coupon_id,json=couponId,proto3,oneof" json:"coupon_id,omitempty"`
	// metadata
	Metadata map[string]string `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// vendor_status
	VendorStatus string `protobuf:"bytes,8,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	// status
	Status SubscriptionModel_Status `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.billing.v1.SubscriptionModel_Status" json:"status,omitempty"`
	// vendor customer id
	VendorCustomerId string `protobuf:"bytes,10,opt,name=vendor_customer_id,json=vendorCustomerId,proto3" json:"vendor_customer_id,omitempty"`
}

func (x *SubscriptionModel) Reset() {
	*x = SubscriptionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionModel) ProtoMessage() {}

func (x *SubscriptionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionModel.ProtoReflect.Descriptor instead.
func (*SubscriptionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{0}
}

func (x *SubscriptionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionModel) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *SubscriptionModel) GetVendorSubscriptionId() string {
	if x != nil {
		return x.VendorSubscriptionId
	}
	return ""
}

func (x *SubscriptionModel) GetPlanUnits() []*PlanUnit {
	if x != nil {
		return x.PlanUnits
	}
	return nil
}

func (x *SubscriptionModel) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *SubscriptionModel) GetCouponId() int64 {
	if x != nil && x.CouponId != nil {
		return *x.CouponId
	}
	return 0
}

func (x *SubscriptionModel) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SubscriptionModel) GetVendorStatus() string {
	if x != nil {
		return x.VendorStatus
	}
	return ""
}

func (x *SubscriptionModel) GetStatus() SubscriptionModel_Status {
	if x != nil {
		return x.Status
	}
	return SubscriptionModel_STATUS_UNSPECIFIED
}

func (x *SubscriptionModel) GetVendorCustomerId() string {
	if x != nil {
		return x.VendorCustomerId
	}
	return ""
}

// subscription payment pause collection
type PauseCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment collection behavior
	Behavior PauseCollection_Behavior `protobuf:"varint,1,opt,name=behavior,proto3,enum=moego.models.billing.v1.PauseCollection_Behavior" json:"behavior,omitempty"`
	// resumes at
	ResumesAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=resumes_at,json=resumesAt,proto3" json:"resumes_at,omitempty"`
}

func (x *PauseCollection) Reset() {
	*x = PauseCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseCollection) ProtoMessage() {}

func (x *PauseCollection) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseCollection.ProtoReflect.Descriptor instead.
func (*PauseCollection) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{1}
}

func (x *PauseCollection) GetBehavior() PauseCollection_Behavior {
	if x != nil {
		return x.Behavior
	}
	return PauseCollection_BEHAVIOR_UNSPECIFIED
}

func (x *PauseCollection) GetResumesAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ResumesAt
	}
	return nil
}

// subscription schedule model
type SubscriptionScheduleModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vendor subscription schedule id
	VendorSubscriptionScheduleId string `protobuf:"bytes,1,opt,name=vendor_subscription_schedule_id,json=vendorSubscriptionScheduleId,proto3" json:"vendor_subscription_schedule_id,omitempty"`
	// subscription id
	SubscriptionId int64 `protobuf:"varint,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// phases
	Phases []*SubscriptionScheduleModel_Phase `protobuf:"bytes,3,rep,name=phases,proto3" json:"phases,omitempty"`
}

func (x *SubscriptionScheduleModel) Reset() {
	*x = SubscriptionScheduleModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionScheduleModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionScheduleModel) ProtoMessage() {}

func (x *SubscriptionScheduleModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionScheduleModel.ProtoReflect.Descriptor instead.
func (*SubscriptionScheduleModel) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{2}
}

func (x *SubscriptionScheduleModel) GetVendorSubscriptionScheduleId() string {
	if x != nil {
		return x.VendorSubscriptionScheduleId
	}
	return ""
}

func (x *SubscriptionScheduleModel) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *SubscriptionScheduleModel) GetPhases() []*SubscriptionScheduleModel_Phase {
	if x != nil {
		return x.Phases
	}
	return nil
}

// phase model
type SubscriptionScheduleModel_Phase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// items
	PlanUnits []*PlanUnit `protobuf:"bytes,1,rep,name=plan_units,json=planUnits,proto3" json:"plan_units,omitempty"`
	// interval
	Interval *interval.Interval `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *SubscriptionScheduleModel_Phase) Reset() {
	*x = SubscriptionScheduleModel_Phase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionScheduleModel_Phase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionScheduleModel_Phase) ProtoMessage() {}

func (x *SubscriptionScheduleModel_Phase) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionScheduleModel_Phase.ProtoReflect.Descriptor instead.
func (*SubscriptionScheduleModel_Phase) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SubscriptionScheduleModel_Phase) GetPlanUnits() []*PlanUnit {
	if x != nil {
		return x.PlanUnits
	}
	return nil
}

func (x *SubscriptionScheduleModel_Phase) GetInterval() *interval.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

var File_moego_models_billing_v1_subscription_models_proto protoreflect.FileDescriptor

var file_moego_models_billing_v1_subscription_models_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x06, 0x0a, 0x11, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x40, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c,
	0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2c, 0x0a, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x56, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45,
	0x44, 0x10, 0x04, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x45, 0x48, 0x41, 0x56,
	0x49, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x46, 0x41, 0x55,
	0x4c, 0x54, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x49, 0x46, 0x5f, 0x49, 0x4e, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x49, 0x46, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54,
	0x45, 0x10, 0x04, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x22, 0xf6, 0x01, 0x0a, 0x0f, 0x50, 0x61, 0x75, 0x73, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x08, 0x62, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x5f,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x41, 0x74, 0x22,
	0x59, 0x0a, 0x08, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x42,
	0x45, 0x48, 0x41, 0x56, 0x49, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4b, 0x45, 0x45, 0x50, 0x5f, 0x41, 0x53,
	0x5f, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x41, 0x52, 0x4b,
	0x5f, 0x55, 0x4e, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x02,
	0x12, 0x08, 0x0a, 0x04, 0x56, 0x4f, 0x49, 0x44, 0x10, 0x03, 0x22, 0xdb, 0x02, 0x0a, 0x19, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x45, 0x0a, 0x1f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x06, 0x70, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x52, 0x06, 0x70, 0x68, 0x61, 0x73, 0x65, 0x73, 0x1a, 0x7c, 0x0a, 0x05, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_billing_v1_subscription_models_proto_rawDescOnce sync.Once
	file_moego_models_billing_v1_subscription_models_proto_rawDescData = file_moego_models_billing_v1_subscription_models_proto_rawDesc
)

func file_moego_models_billing_v1_subscription_models_proto_rawDescGZIP() []byte {
	file_moego_models_billing_v1_subscription_models_proto_rawDescOnce.Do(func() {
		file_moego_models_billing_v1_subscription_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_billing_v1_subscription_models_proto_rawDescData)
	})
	return file_moego_models_billing_v1_subscription_models_proto_rawDescData
}

var file_moego_models_billing_v1_subscription_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_billing_v1_subscription_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_billing_v1_subscription_models_proto_goTypes = []interface{}{
	(SubscriptionModel_Status)(0),           // 0: moego.models.billing.v1.SubscriptionModel.Status
	(SubscriptionModel_PaymentBehavior)(0),  // 1: moego.models.billing.v1.SubscriptionModel.PaymentBehavior
	(PauseCollection_Behavior)(0),           // 2: moego.models.billing.v1.PauseCollection.Behavior
	(*SubscriptionModel)(nil),               // 3: moego.models.billing.v1.SubscriptionModel
	(*PauseCollection)(nil),                 // 4: moego.models.billing.v1.PauseCollection
	(*SubscriptionScheduleModel)(nil),       // 5: moego.models.billing.v1.SubscriptionScheduleModel
	nil,                                     // 6: moego.models.billing.v1.SubscriptionModel.MetadataEntry
	(*SubscriptionScheduleModel_Phase)(nil), // 7: moego.models.billing.v1.SubscriptionScheduleModel.Phase
	(*PlanUnit)(nil),                        // 8: moego.models.billing.v1.PlanUnit
	(*timestamppb.Timestamp)(nil),           // 9: google.protobuf.Timestamp
	(*interval.Interval)(nil),               // 10: google.type.Interval
}
var file_moego_models_billing_v1_subscription_models_proto_depIdxs = []int32{
	8,  // 0: moego.models.billing.v1.SubscriptionModel.plan_units:type_name -> moego.models.billing.v1.PlanUnit
	6,  // 1: moego.models.billing.v1.SubscriptionModel.metadata:type_name -> moego.models.billing.v1.SubscriptionModel.MetadataEntry
	0,  // 2: moego.models.billing.v1.SubscriptionModel.status:type_name -> moego.models.billing.v1.SubscriptionModel.Status
	2,  // 3: moego.models.billing.v1.PauseCollection.behavior:type_name -> moego.models.billing.v1.PauseCollection.Behavior
	9,  // 4: moego.models.billing.v1.PauseCollection.resumes_at:type_name -> google.protobuf.Timestamp
	7,  // 5: moego.models.billing.v1.SubscriptionScheduleModel.phases:type_name -> moego.models.billing.v1.SubscriptionScheduleModel.Phase
	8,  // 6: moego.models.billing.v1.SubscriptionScheduleModel.Phase.plan_units:type_name -> moego.models.billing.v1.PlanUnit
	10, // 7: moego.models.billing.v1.SubscriptionScheduleModel.Phase.interval:type_name -> google.type.Interval
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_models_billing_v1_subscription_models_proto_init() }
func file_moego_models_billing_v1_subscription_models_proto_init() {
	if File_moego_models_billing_v1_subscription_models_proto != nil {
		return
	}
	file_moego_models_billing_v1_subscription_defs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_billing_v1_subscription_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionScheduleModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionScheduleModel_Phase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_billing_v1_subscription_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_billing_v1_subscription_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_billing_v1_subscription_models_proto_goTypes,
		DependencyIndexes: file_moego_models_billing_v1_subscription_models_proto_depIdxs,
		EnumInfos:         file_moego_models_billing_v1_subscription_models_proto_enumTypes,
		MessageInfos:      file_moego_models_billing_v1_subscription_models_proto_msgTypes,
	}.Build()
	File_moego_models_billing_v1_subscription_models_proto = out.File
	file_moego_models_billing_v1_subscription_models_proto_rawDesc = nil
	file_moego_models_billing_v1_subscription_models_proto_goTypes = nil
	file_moego_models_billing_v1_subscription_models_proto_depIdxs = nil
}
