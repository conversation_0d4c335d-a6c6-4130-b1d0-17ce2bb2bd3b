// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/billing/v1/subscription_defs.proto

package billingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// subscription event type
// ref: https://stripe.com/docs/api/events/types
type BillingEventType int32

const (
	// unknown event type
	BillingEventType_BILLING_EVENT_TYPE_UNSPECIFIED BillingEventType = 0
	// subscription created
	BillingEventType_SUBSCRIPTION_CREATED BillingEventType = 1
	// subscription updated  -> customer.subscription.updated
	BillingEventType_SUBSCRIPTION_UPDATED BillingEventType = 2
	// subscription canceled
	BillingEventType_SUBSCRIPTION_CANCELED BillingEventType = 3
	// invoice paid -> invoice.paid
	BillingEventType_INVOICE_PAID BillingEventType = 4
	// invoice payment failed -> invoice.payment_failed
	BillingEventType_INVOICE_PAYMENT_FAILED BillingEventType = 5
	// invoice upcoming -> invoice.upcoming
	BillingEventType_INVOICE_UPCOMING BillingEventType = 6
	// invoice created -> invoice.created
	BillingEventType_INVOICE_CREATED BillingEventType = 7
)

// Enum value maps for BillingEventType.
var (
	BillingEventType_name = map[int32]string{
		0: "BILLING_EVENT_TYPE_UNSPECIFIED",
		1: "SUBSCRIPTION_CREATED",
		2: "SUBSCRIPTION_UPDATED",
		3: "SUBSCRIPTION_CANCELED",
		4: "INVOICE_PAID",
		5: "INVOICE_PAYMENT_FAILED",
		6: "INVOICE_UPCOMING",
		7: "INVOICE_CREATED",
	}
	BillingEventType_value = map[string]int32{
		"BILLING_EVENT_TYPE_UNSPECIFIED": 0,
		"SUBSCRIPTION_CREATED":           1,
		"SUBSCRIPTION_UPDATED":           2,
		"SUBSCRIPTION_CANCELED":          3,
		"INVOICE_PAID":                   4,
		"INVOICE_PAYMENT_FAILED":         5,
		"INVOICE_UPCOMING":               6,
		"INVOICE_CREATED":                7,
	}
)

func (x BillingEventType) Enum() *BillingEventType {
	p := new(BillingEventType)
	*p = x
	return p
}

func (x BillingEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_billing_v1_subscription_defs_proto_enumTypes[0].Descriptor()
}

func (BillingEventType) Type() protoreflect.EnumType {
	return &file_moego_models_billing_v1_subscription_defs_proto_enumTypes[0]
}

func (x BillingEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingEventType.Descriptor instead.
func (BillingEventType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{0}
}

// price unit object
type PlanUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PlanUnit count
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// PlanUnit price
	UnitPriceId int64 `protobuf:"varint,2,opt,name=unit_price_id,json=unitPriceId,proto3" json:"unit_price_id,omitempty"`
}

func (x *PlanUnit) Reset() {
	*x = PlanUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanUnit) ProtoMessage() {}

func (x *PlanUnit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanUnit.ProtoReflect.Descriptor instead.
func (*PlanUnit) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PlanUnit) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *PlanUnit) GetUnitPriceId() int64 {
	if x != nil {
		return x.UnitPriceId
	}
	return 0
}

// https://docs.stripe.com/api/subscriptions/create#create_subscription-transfer_data
type TransferData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// destination, stripe account id
	Destination string `protobuf:"bytes,1,opt,name=destination,proto3" json:"destination,omitempty"`
	// amount percent
	// A non-negative decimal between 0 and 100, with at most two decimal places.
	// This represents the percentage of the subscription invoice total that will be transferred to the destination account.
	// By default, the entire amount is transferred to the destination.
	AmountPercent *float64 `protobuf:"fixed64,2,opt,name=amount_percent,json=amountPercent,proto3,oneof" json:"amount_percent,omitempty"`
}

func (x *TransferData) Reset() {
	*x = TransferData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferData) ProtoMessage() {}

func (x *TransferData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferData.ProtoReflect.Descriptor instead.
func (*TransferData) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{1}
}

func (x *TransferData) GetDestination() string {
	if x != nil {
		return x.Destination
	}
	return ""
}

func (x *TransferData) GetAmountPercent() float64 {
	if x != nil && x.AmountPercent != nil {
		return *x.AmountPercent
	}
	return 0
}

// event model for event bus
type EventModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// event type
	EventType BillingEventType `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=moego.models.billing.v1.BillingEventType" json:"event_type,omitempty"`
	// event data
	//
	// Types that are assignable to EventDetail:
	//
	//	*EventModel_Subscription
	//	*EventModel_Invoice
	EventDetail isEventModel_EventDetail `protobuf_oneof:"event_detail"`
}

func (x *EventModel) Reset() {
	*x = EventModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventModel) ProtoMessage() {}

func (x *EventModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventModel.ProtoReflect.Descriptor instead.
func (*EventModel) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{2}
}

func (x *EventModel) GetEventType() BillingEventType {
	if x != nil {
		return x.EventType
	}
	return BillingEventType_BILLING_EVENT_TYPE_UNSPECIFIED
}

func (m *EventModel) GetEventDetail() isEventModel_EventDetail {
	if m != nil {
		return m.EventDetail
	}
	return nil
}

func (x *EventModel) GetSubscription() *Subscription {
	if x, ok := x.GetEventDetail().(*EventModel_Subscription); ok {
		return x.Subscription
	}
	return nil
}

func (x *EventModel) GetInvoice() *Invoice {
	if x, ok := x.GetEventDetail().(*EventModel_Invoice); ok {
		return x.Invoice
	}
	return nil
}

type isEventModel_EventDetail interface {
	isEventModel_EventDetail()
}

type EventModel_Subscription struct {
	// subscription
	Subscription *Subscription `protobuf:"bytes,2,opt,name=subscription,proto3,oneof"`
}

type EventModel_Invoice struct {
	// Invoice
	Invoice *Invoice `protobuf:"bytes,3,opt,name=invoice,proto3,oneof"`
}

func (*EventModel_Subscription) isEventModel_EventDetail() {}

func (*EventModel_Invoice) isEventModel_EventDetail() {}

// billing subscription
type Subscription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription id, 注意此处 id 是 vendor 的 subscription id，非 billing 库中的主键id
	SubscriptionId string `protobuf:"bytes,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// customer id
	CustomerId string `protobuf:"bytes,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// subscription status
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// subscription start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// subscription end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// subscription cancel time： A date in the future at which the subscription will automatically get canceled
	CancelAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=cancel_at,json=cancelAt,proto3" json:"cancel_at,omitempty"`
	// If the subscription has been canceled, the date of that cancellation.
	// If the subscription was canceled with cancel_at_period_end, canceled_at will reflect the time of the most recent update request,
	// not the end of the subscription period when the subscription is automatically moved to a canceled state.
	CanceledAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=canceled_at,json=canceledAt,proto3" json:"canceled_at,omitempty"`
	// created time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// subscription cancel at period end
	CancelAtPeriodEnd bool `protobuf:"varint,9,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// subscription transfer data
	LatestInvoice string `protobuf:"bytes,10,opt,name=latest_invoice,json=latestInvoice,proto3" json:"latest_invoice,omitempty"`
	// description
	Description string `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	// metadata
	Metadata map[string]string `protobuf:"bytes,12,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// id，注意此处 id 是billing库中的主键id，才是真正意义上的 subscription_id
	Id int64 `protobuf:"varint,13,opt,name=id,proto3" json:"id,omitempty"`
	// payment method
	PaymentMethod string `protobuf:"bytes,14,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
}

func (x *Subscription) Reset() {
	*x = Subscription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subscription) ProtoMessage() {}

func (x *Subscription) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subscription.ProtoReflect.Descriptor instead.
func (*Subscription) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{3}
}

func (x *Subscription) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *Subscription) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *Subscription) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Subscription) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Subscription) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Subscription) GetCancelAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CancelAt
	}
	return nil
}

func (x *Subscription) GetCanceledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CanceledAt
	}
	return nil
}

func (x *Subscription) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Subscription) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *Subscription) GetLatestInvoice() string {
	if x != nil {
		return x.LatestInvoice
	}
	return ""
}

func (x *Subscription) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Subscription) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Subscription) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Subscription) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

// event bus: invoice event
type Invoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id
	InvoiceId string `protobuf:"bytes,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// customer id
	CustomerId string `protobuf:"bytes,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// subscription id，注意此处是 vendor 的 subscription id
	SubscriptionId string `protobuf:"bytes,3,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// invoice status
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// invoice amount
	Amount int64 `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// invoice currency
	Currency string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	// invoice start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// invoice end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// application fee amount
	ApplicationFeeAmount int64 `protobuf:"varint,9,opt,name=application_fee_amount,json=applicationFeeAmount,proto3" json:"application_fee_amount,omitempty"`
	// created time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// amount remaining
	AmountRemaining int64 `protobuf:"varint,11,opt,name=amount_remaining,json=amountRemaining,proto3" json:"amount_remaining,omitempty"`
	// billing reason
	BillingReason string `protobuf:"bytes,12,opt,name=billing_reason,json=billingReason,proto3" json:"billing_reason,omitempty"`
	// description
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// live mode
	Livemode bool `protobuf:"varint,14,opt,name=livemode,proto3" json:"livemode,omitempty"`
	// payment intent
	PaymentIntent string `protobuf:"bytes,15,opt,name=payment_intent,json=paymentIntent,proto3" json:"payment_intent,omitempty"`
	// billing subscription id，注意此处 id 是billing库中的主键id
	BillingSubscriptionId int64 `protobuf:"varint,16,opt,name=billing_subscription_id,json=billingSubscriptionId,proto3" json:"billing_subscription_id,omitempty"`
}

func (x *Invoice) Reset() {
	*x = Invoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Invoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Invoice) ProtoMessage() {}

func (x *Invoice) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_subscription_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Invoice.ProtoReflect.Descriptor instead.
func (*Invoice) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP(), []int{4}
}

func (x *Invoice) GetInvoiceId() string {
	if x != nil {
		return x.InvoiceId
	}
	return ""
}

func (x *Invoice) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *Invoice) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *Invoice) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Invoice) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Invoice) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Invoice) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Invoice) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Invoice) GetApplicationFeeAmount() int64 {
	if x != nil {
		return x.ApplicationFeeAmount
	}
	return 0
}

func (x *Invoice) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Invoice) GetAmountRemaining() int64 {
	if x != nil {
		return x.AmountRemaining
	}
	return 0
}

func (x *Invoice) GetBillingReason() string {
	if x != nil {
		return x.BillingReason
	}
	return ""
}

func (x *Invoice) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Invoice) GetLivemode() bool {
	if x != nil {
		return x.Livemode
	}
	return false
}

func (x *Invoice) GetPaymentIntent() string {
	if x != nil {
		return x.PaymentIntent
	}
	return ""
}

func (x *Invoice) GetBillingSubscriptionId() int64 {
	if x != nil {
		return x.BillingSubscriptionId
	}
	return 0
}

var File_moego_models_billing_v1_subscription_defs_proto protoreflect.FileDescriptor

var file_moego_models_billing_v1_subscription_defs_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x44, 0x0a, 0x08, 0x50,
	0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0d, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x6f, 0x0a, 0x0c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0d, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x22, 0xf1, 0x01, 0x0a, 0x0a, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x48, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x07, 0x69,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xd2, 0x05, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x14,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x90, 0x05, 0x0a, 0x07,
	0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x39, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x29, 0x0a, 0x10, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x2a, 0xde,
	0x01, 0x0a, 0x10, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56, 0x4f,
	0x49, 0x43, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f,
	0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e,
	0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x07, 0x42,
	0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_billing_v1_subscription_defs_proto_rawDescOnce sync.Once
	file_moego_models_billing_v1_subscription_defs_proto_rawDescData = file_moego_models_billing_v1_subscription_defs_proto_rawDesc
)

func file_moego_models_billing_v1_subscription_defs_proto_rawDescGZIP() []byte {
	file_moego_models_billing_v1_subscription_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_billing_v1_subscription_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_billing_v1_subscription_defs_proto_rawDescData)
	})
	return file_moego_models_billing_v1_subscription_defs_proto_rawDescData
}

var file_moego_models_billing_v1_subscription_defs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_billing_v1_subscription_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_billing_v1_subscription_defs_proto_goTypes = []interface{}{
	(BillingEventType)(0),         // 0: moego.models.billing.v1.BillingEventType
	(*PlanUnit)(nil),              // 1: moego.models.billing.v1.PlanUnit
	(*TransferData)(nil),          // 2: moego.models.billing.v1.TransferData
	(*EventModel)(nil),            // 3: moego.models.billing.v1.EventModel
	(*Subscription)(nil),          // 4: moego.models.billing.v1.Subscription
	(*Invoice)(nil),               // 5: moego.models.billing.v1.Invoice
	nil,                           // 6: moego.models.billing.v1.Subscription.MetadataEntry
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
}
var file_moego_models_billing_v1_subscription_defs_proto_depIdxs = []int32{
	0,  // 0: moego.models.billing.v1.EventModel.event_type:type_name -> moego.models.billing.v1.BillingEventType
	4,  // 1: moego.models.billing.v1.EventModel.subscription:type_name -> moego.models.billing.v1.Subscription
	5,  // 2: moego.models.billing.v1.EventModel.invoice:type_name -> moego.models.billing.v1.Invoice
	7,  // 3: moego.models.billing.v1.Subscription.start_time:type_name -> google.protobuf.Timestamp
	7,  // 4: moego.models.billing.v1.Subscription.end_time:type_name -> google.protobuf.Timestamp
	7,  // 5: moego.models.billing.v1.Subscription.cancel_at:type_name -> google.protobuf.Timestamp
	7,  // 6: moego.models.billing.v1.Subscription.canceled_at:type_name -> google.protobuf.Timestamp
	7,  // 7: moego.models.billing.v1.Subscription.created_at:type_name -> google.protobuf.Timestamp
	6,  // 8: moego.models.billing.v1.Subscription.metadata:type_name -> moego.models.billing.v1.Subscription.MetadataEntry
	7,  // 9: moego.models.billing.v1.Invoice.start_time:type_name -> google.protobuf.Timestamp
	7,  // 10: moego.models.billing.v1.Invoice.end_time:type_name -> google.protobuf.Timestamp
	7,  // 11: moego.models.billing.v1.Invoice.created_at:type_name -> google.protobuf.Timestamp
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_models_billing_v1_subscription_defs_proto_init() }
func file_moego_models_billing_v1_subscription_defs_proto_init() {
	if File_moego_models_billing_v1_subscription_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_billing_v1_subscription_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subscription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_subscription_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Invoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_billing_v1_subscription_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_billing_v1_subscription_defs_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*EventModel_Subscription)(nil),
		(*EventModel_Invoice)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_billing_v1_subscription_defs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_billing_v1_subscription_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_billing_v1_subscription_defs_proto_depIdxs,
		EnumInfos:         file_moego_models_billing_v1_subscription_defs_proto_enumTypes,
		MessageInfos:      file_moego_models_billing_v1_subscription_defs_proto_msgTypes,
	}.Build()
	File_moego_models_billing_v1_subscription_defs_proto = out.File
	file_moego_models_billing_v1_subscription_defs_proto_rawDesc = nil
	file_moego_models_billing_v1_subscription_defs_proto_goTypes = nil
	file_moego_models_billing_v1_subscription_defs_proto_depIdxs = nil
}
