// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/order_line_item_models.proto

package orderpb

import (
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 计算方式.
type PriceDetailModel_PriceItem_Operator int32

const (
	// 无.
	PriceDetailModel_PriceItem_OPERATOR_UNSPECIFIED PriceDetailModel_PriceItem_Operator = 0
	// 加.
	PriceDetailModel_PriceItem_ADD PriceDetailModel_PriceItem_Operator = 1
	// 降.
	PriceDetailModel_PriceItem_SUBTRACT PriceDetailModel_PriceItem_Operator = 2
)

// Enum value maps for PriceDetailModel_PriceItem_Operator.
var (
	PriceDetailModel_PriceItem_Operator_name = map[int32]string{
		0: "OPERATOR_UNSPECIFIED",
		1: "ADD",
		2: "SUBTRACT",
	}
	PriceDetailModel_PriceItem_Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED": 0,
		"ADD":                  1,
		"SUBTRACT":             2,
	}
)

func (x PriceDetailModel_PriceItem_Operator) Enum() *PriceDetailModel_PriceItem_Operator {
	p := new(PriceDetailModel_PriceItem_Operator)
	*p = x
	return p
}

func (x PriceDetailModel_PriceItem_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceDetailModel_PriceItem_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_line_item_models_proto_enumTypes[0].Descriptor()
}

func (PriceDetailModel_PriceItem_Operator) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_line_item_models_proto_enumTypes[0]
}

func (x PriceDetailModel_PriceItem_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceDetailModel_PriceItem_Operator.Descriptor instead.
func (PriceDetailModel_PriceItem_Operator) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP(), []int{2, 0, 0}
}

// *
// line item info
type OrderLineItemModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// object id
	ObjectId int64 `protobuf:"varint,3,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// item name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// item unit price
	UnitPrice float64 `protobuf:"fixed64,6,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// total quantity
	Quantity int32 `protobuf:"varint,7,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,8,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// order id
	OrderId *int64 `protobuf:"varint,9,opt,name=order_id,json=orderId,proto3,oneof" json:"order_id,omitempty"`
	// is deleted
	IsDeleted *bool `protobuf:"varint,10,opt,name=is_deleted,json=isDeleted,proto3,oneof" json:"is_deleted,omitempty"`
	// item description
	Description *string `protobuf:"bytes,11,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// quantity of using package
	PurchasedQuantity *int32 `protobuf:"varint,12,opt,name=purchased_quantity,json=purchasedQuantity,proto3,oneof" json:"purchased_quantity,omitempty"`
	// tips distribute on this item
	TipsAmount *float64 `protobuf:"fixed64,13,opt,name=tips_amount,json=tipsAmount,proto3,oneof" json:"tips_amount,omitempty"`
	// tax of this item
	TaxAmount *float64 `protobuf:"fixed64,14,opt,name=tax_amount,json=taxAmount,proto3,oneof" json:"tax_amount,omitempty"`
	// discount distribute on this item
	DiscountAmount *float64 `protobuf:"fixed64,15,opt,name=discount_amount,json=discountAmount,proto3,oneof" json:"discount_amount,omitempty"`
	// extra fee distribute on this item
	ExtraFeeAmount *float64 `protobuf:"fixed64,16,opt,name=extra_fee_amount,json=extraFeeAmount,proto3,oneof" json:"extra_fee_amount,omitempty"`
	// subTotal amount
	SubTotalAmount *float64 `protobuf:"fixed64,17,opt,name=sub_total_amount,json=subTotalAmount,proto3,oneof" json:"sub_total_amount,omitempty"`
	// total amount
	TotalAmount *float64 `protobuf:"fixed64,18,opt,name=total_amount,json=totalAmount,proto3,oneof" json:"total_amount,omitempty"`
	// create time
	CreateTime *int64 `protobuf:"varint,19,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`
	// update time
	UpdateTime *int64 `protobuf:"varint,20,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`
	// tax applied on this item
	LineTaxes []*OrderLineTaxModel `protobuf:"bytes,21,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// discount applied on this item
	LineDiscounts []*OrderLineDiscountModel `protobuf:"bytes,22,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// extra fee applied on this item
	LineExtraFees []*OrderLineExtraFeeModel `protobuf:"bytes,23,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// pet ID.
	PetId int64 `protobuf:"varint,24,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet detail ID.
	PetDetailId int64 `protobuf:"varint,25,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// Tax ID
	TaxId int64 `protobuf:"varint,26,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Tax Rate
	TaxRate *decimal.Decimal `protobuf:"bytes,27,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// Tax name
	TaxName string `protobuf:"bytes,28,opt,name=tax_name,json=taxName,proto3" json:"tax_name,omitempty"`
	// currency code.
	CurrencyCode string `protobuf:"bytes,30,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 已退数量.
	RefundedQuantity int32 `protobuf:"varint,31,opt,name=refunded_quantity,json=refundedQuantity,proto3" json:"refunded_quantity,omitempty"`
	// 已退总金额.
	RefundedAmount *money.Money `protobuf:"bytes,32,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// 已退的税.
	RefundedTaxAmount *money.Money `protobuf:"bytes,33,opt,name=refunded_tax_amount,json=refundedTaxAmount,proto3" json:"refunded_tax_amount,omitempty"`
	// 已退的 discount.
	RefundedDiscountAmount *money.Money `protobuf:"bytes,34,opt,name=refunded_discount_amount,json=refundedDiscountAmount,proto3" json:"refunded_discount_amount,omitempty"`
	// External UUID 外部的 UUID，常见场景是 petDetailID，或者 fulfillment ID 等.
	ExternalUuid string `protobuf:"bytes,35,opt,name=external_uuid,json=externalUuid,proto3" json:"external_uuid,omitempty"`
	// Deposit 抵扣的金额，包含在 total_amount + tax_amount 中.
	DepositAmount *money.Money `protobuf:"bytes,36,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// 已退的 deposit，包含在 deposit_amount 中.
	RefundedDepositAmount *money.Money `protobuf:"bytes,37,opt,name=refunded_deposit_amount,json=refundedDepositAmount,proto3" json:"refunded_deposit_amount,omitempty"`
}

func (x *OrderLineItemModel) Reset() {
	*x = OrderLineItemModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderLineItemModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderLineItemModel) ProtoMessage() {}

func (x *OrderLineItemModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderLineItemModel.ProtoReflect.Descriptor instead.
func (*OrderLineItemModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderLineItemModel) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *OrderLineItemModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderLineItemModel) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *OrderLineItemModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OrderLineItemModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderLineItemModel) GetUnitPrice() float64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

func (x *OrderLineItemModel) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderLineItemModel) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *OrderLineItemModel) GetOrderId() int64 {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return 0
}

func (x *OrderLineItemModel) GetIsDeleted() bool {
	if x != nil && x.IsDeleted != nil {
		return *x.IsDeleted
	}
	return false
}

func (x *OrderLineItemModel) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *OrderLineItemModel) GetPurchasedQuantity() int32 {
	if x != nil && x.PurchasedQuantity != nil {
		return *x.PurchasedQuantity
	}
	return 0
}

func (x *OrderLineItemModel) GetTipsAmount() float64 {
	if x != nil && x.TipsAmount != nil {
		return *x.TipsAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetTaxAmount() float64 {
	if x != nil && x.TaxAmount != nil {
		return *x.TaxAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetDiscountAmount() float64 {
	if x != nil && x.DiscountAmount != nil {
		return *x.DiscountAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetExtraFeeAmount() float64 {
	if x != nil && x.ExtraFeeAmount != nil {
		return *x.ExtraFeeAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetSubTotalAmount() float64 {
	if x != nil && x.SubTotalAmount != nil {
		return *x.SubTotalAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetTotalAmount() float64 {
	if x != nil && x.TotalAmount != nil {
		return *x.TotalAmount
	}
	return 0
}

func (x *OrderLineItemModel) GetCreateTime() int64 {
	if x != nil && x.CreateTime != nil {
		return *x.CreateTime
	}
	return 0
}

func (x *OrderLineItemModel) GetUpdateTime() int64 {
	if x != nil && x.UpdateTime != nil {
		return *x.UpdateTime
	}
	return 0
}

func (x *OrderLineItemModel) GetLineTaxes() []*OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *OrderLineItemModel) GetLineDiscounts() []*OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *OrderLineItemModel) GetLineExtraFees() []*OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *OrderLineItemModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *OrderLineItemModel) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *OrderLineItemModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *OrderLineItemModel) GetTaxRate() *decimal.Decimal {
	if x != nil {
		return x.TaxRate
	}
	return nil
}

func (x *OrderLineItemModel) GetTaxName() string {
	if x != nil {
		return x.TaxName
	}
	return ""
}

func (x *OrderLineItemModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *OrderLineItemModel) GetRefundedQuantity() int32 {
	if x != nil {
		return x.RefundedQuantity
	}
	return 0
}

func (x *OrderLineItemModel) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *OrderLineItemModel) GetRefundedTaxAmount() *money.Money {
	if x != nil {
		return x.RefundedTaxAmount
	}
	return nil
}

func (x *OrderLineItemModel) GetRefundedDiscountAmount() *money.Money {
	if x != nil {
		return x.RefundedDiscountAmount
	}
	return nil
}

func (x *OrderLineItemModel) GetExternalUuid() string {
	if x != nil {
		return x.ExternalUuid
	}
	return ""
}

func (x *OrderLineItemModel) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *OrderLineItemModel) GetRefundedDepositAmount() *money.Money {
	if x != nil {
		return x.RefundedDepositAmount
	}
	return nil
}

// Order item. 与原有的 OrderLineItemModel 兼容.
// 与 RefundOrderItem 对应.
type OrderItemModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// object id
	ObjectId int64 `protobuf:"varint,3,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// item name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// item unit price
	// 存在 SubtotalDetail 时，会保证 UnitPrice 是由 Detail / quantity 计算得出.
	UnitPrice *money.Money `protobuf:"bytes,6,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// total quantity
	Quantity int32 `protobuf:"varint,7,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// staff id
	// 当 staff id != 0 时，也会包含在 staff_ids 字段中
	StaffId int64 `protobuf:"varint,8,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,9,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,10,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// item description
	Description string `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	// quantity of using package
	PurchasedQuantity int32 `protobuf:"varint,12,opt,name=purchased_quantity,json=purchasedQuantity,proto3" json:"purchased_quantity,omitempty"`
	// tips distribute on this item
	TipsAmount *money.Money `protobuf:"bytes,13,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// tax of this item
	TaxAmount *money.Money `protobuf:"bytes,14,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// discount distribute on this item
	DiscountAmount *money.Money `protobuf:"bytes,15,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// 当前没有分摊 convenience fee 到 item 上, 先移除.
	//
	//	// convenience fee distribute on this item
	//	google.type.Money convenience_fee = 16;
	//
	// subTotal amount
	SubTotalAmount *money.Money `protobuf:"bytes,17,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
	// total amount
	TotalAmount *money.Money `protobuf:"bytes,18,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,19,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,20,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// discount applied on this item
	LineDiscounts []*OrderLineDiscountModelV1 `protobuf:"bytes,22,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// pet ID.
	PetId int64 `protobuf:"varint,24,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet detail ID.
	PetDetailId int64 `protobuf:"varint,25,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// Tax ID
	TaxId int64 `protobuf:"varint,26,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Tax Rate
	TaxRate *decimal.Decimal `protobuf:"bytes,27,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// Tax name
	TaxName string `protobuf:"bytes,28,opt,name=tax_name,json=taxName,proto3" json:"tax_name,omitempty"`
	// currency code.
	CurrencyCode string `protobuf:"bytes,30,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 已退数量.
	RefundedQuantity int32 `protobuf:"varint,31,opt,name=refunded_quantity,json=refundedQuantity,proto3" json:"refunded_quantity,omitempty"`
	// 已退总金额.
	RefundedAmount *money.Money `protobuf:"bytes,32,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// 已退的税.
	RefundedTaxAmount *money.Money `protobuf:"bytes,33,opt,name=refunded_tax_amount,json=refundedTaxAmount,proto3" json:"refunded_tax_amount,omitempty"`
	// 已退的 discount.
	RefundedDiscountAmount *money.Money `protobuf:"bytes,34,opt,name=refunded_discount_amount,json=refundedDiscountAmount,proto3" json:"refunded_discount_amount,omitempty"`
	// Deposit 抵扣的金额，包含在 total_amount + tax_amount 中.
	DepositAmount *money.Money `protobuf:"bytes,38,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// 已退的 deposit，包含在 deposit_amount 中.
	RefundedDepositAmount *money.Money `protobuf:"bytes,39,opt,name=refunded_deposit_amount,json=refundedDepositAmount,proto3" json:"refunded_deposit_amount,omitempty"`
	// 关联的 Staff 的 ID.
	StaffIds []int64 `protobuf:"varint,35,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// SubTotal 详情. 存在详情时，会保证 UnitPrice 是由 PriceDetail 除以 Quantity 计算得出.
	SubtotalDetail *PriceDetailModel `protobuf:"bytes,36,opt,name=subtotal_detail,json=subtotalDetail,proto3" json:"subtotal_detail,omitempty"`
	// External UUID 外部的 UUID，常见场景是 petDetailID，或者 fulfillment ID 等.
	ExternalUuid string `protobuf:"bytes,37,opt,name=external_uuid,json=externalUuid,proto3" json:"external_uuid,omitempty"`
}

func (x *OrderItemModel) Reset() {
	*x = OrderItemModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderItemModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderItemModel) ProtoMessage() {}

func (x *OrderItemModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderItemModel.ProtoReflect.Descriptor instead.
func (*OrderItemModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP(), []int{1}
}

func (x *OrderItemModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderItemModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderItemModel) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *OrderItemModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OrderItemModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderItemModel) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *OrderItemModel) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderItemModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *OrderItemModel) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderItemModel) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *OrderItemModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OrderItemModel) GetPurchasedQuantity() int32 {
	if x != nil {
		return x.PurchasedQuantity
	}
	return 0
}

func (x *OrderItemModel) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *OrderItemModel) GetTaxAmount() *money.Money {
	if x != nil {
		return x.TaxAmount
	}
	return nil
}

func (x *OrderItemModel) GetDiscountAmount() *money.Money {
	if x != nil {
		return x.DiscountAmount
	}
	return nil
}

func (x *OrderItemModel) GetSubTotalAmount() *money.Money {
	if x != nil {
		return x.SubTotalAmount
	}
	return nil
}

func (x *OrderItemModel) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *OrderItemModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *OrderItemModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *OrderItemModel) GetLineDiscounts() []*OrderLineDiscountModelV1 {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *OrderItemModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *OrderItemModel) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *OrderItemModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *OrderItemModel) GetTaxRate() *decimal.Decimal {
	if x != nil {
		return x.TaxRate
	}
	return nil
}

func (x *OrderItemModel) GetTaxName() string {
	if x != nil {
		return x.TaxName
	}
	return ""
}

func (x *OrderItemModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *OrderItemModel) GetRefundedQuantity() int32 {
	if x != nil {
		return x.RefundedQuantity
	}
	return 0
}

func (x *OrderItemModel) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *OrderItemModel) GetRefundedTaxAmount() *money.Money {
	if x != nil {
		return x.RefundedTaxAmount
	}
	return nil
}

func (x *OrderItemModel) GetRefundedDiscountAmount() *money.Money {
	if x != nil {
		return x.RefundedDiscountAmount
	}
	return nil
}

func (x *OrderItemModel) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *OrderItemModel) GetRefundedDepositAmount() *money.Money {
	if x != nil {
		return x.RefundedDepositAmount
	}
	return nil
}

func (x *OrderItemModel) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *OrderItemModel) GetSubtotalDetail() *PriceDetailModel {
	if x != nil {
		return x.SubtotalDetail
	}
	return nil
}

func (x *OrderItemModel) GetExternalUuid() string {
	if x != nil {
		return x.ExternalUuid
	}
	return ""
}

// 支持计算得到的 Price.
type PriceDetailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 每一个条用于计算 UnitPrice 的项目.
	// 按顺序依次计算.
	PriceItems []*PriceDetailModel_PriceItem `protobuf:"bytes,1,rep,name=price_items,json=priceItems,proto3" json:"price_items,omitempty"`
}

func (x *PriceDetailModel) Reset() {
	*x = PriceDetailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceDetailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceDetailModel) ProtoMessage() {}

func (x *PriceDetailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceDetailModel.ProtoReflect.Descriptor instead.
func (*PriceDetailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP(), []int{2}
}

func (x *PriceDetailModel) GetPriceItems() []*PriceDetailModel_PriceItem {
	if x != nil {
		return x.PriceItems
	}
	return nil
}

// 单条影响单价的条目.
type PriceDetailModel_PriceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 名字.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 计算方式.
	Operator PriceDetailModel_PriceItem_Operator `protobuf:"varint,2,opt,name=operator,proto3,enum=moego.models.order.v1.PriceDetailModel_PriceItem_Operator" json:"operator,omitempty"`
	// 影响次数.
	Quantity int32 `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 影响的金额.
	UnitPrice *money.Money `protobuf:"bytes,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// 影响的总金额.
	SubTotal *money.Money `protobuf:"bytes,5,opt,name=sub_total,json=subTotal,proto3" json:"sub_total,omitempty"`
	// Object type that contributes to this price item.
	ObjectType ItemType `protobuf:"varint,6,opt,name=object_type,json=objectType,proto3,enum=moego.models.order.v1.ItemType" json:"object_type,omitempty"`
	// Object id that contributes to this price item.
	ObjectId int64 `protobuf:"varint,7,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
}

func (x *PriceDetailModel_PriceItem) Reset() {
	*x = PriceDetailModel_PriceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceDetailModel_PriceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceDetailModel_PriceItem) ProtoMessage() {}

func (x *PriceDetailModel_PriceItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_line_item_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceDetailModel_PriceItem.ProtoReflect.Descriptor instead.
func (*PriceDetailModel_PriceItem) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP(), []int{2, 0}
}

func (x *PriceDetailModel_PriceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PriceDetailModel_PriceItem) GetOperator() PriceDetailModel_PriceItem_Operator {
	if x != nil {
		return x.Operator
	}
	return PriceDetailModel_PriceItem_OPERATOR_UNSPECIFIED
}

func (x *PriceDetailModel_PriceItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PriceDetailModel_PriceItem) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *PriceDetailModel_PriceItem) GetSubTotal() *money.Money {
	if x != nil {
		return x.SubTotal
	}
	return nil
}

func (x *PriceDetailModel_PriceItem) GetObjectType() ItemType {
	if x != nil {
		return x.ObjectType
	}
	return ItemType_ITEM_TYPE_UNSPECIFIED
}

func (x *PriceDetailModel_PriceItem) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

var File_moego_models_order_v1_order_line_item_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_order_line_item_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x0e, 0x0a,
	0x12, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x04, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x12, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x05, 0x52, 0x11, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x48, 0x06, 0x52,
	0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x07, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x48, 0x08, 0x52, 0x0e, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x2d, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x48, 0x09, 0x52, 0x0e, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x2d, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0a, 0x52, 0x0e, 0x73, 0x75, 0x62,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x0b, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0c, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x0d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x47, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x16, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x55, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x66, 0x65, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x46, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x74, 0x61,
	0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d,
	0x61, 0x6c, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x61, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64,
	0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64,
	0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x18, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x16, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x75, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x0e,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x24,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74,
	0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x89, 0x0c, 0x0a, 0x0e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x31, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x73, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x51, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x74,
	0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b,
	0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x0d, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x08,
	0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63,
	0x69, 0x6d, 0x61, 0x6c, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x61, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x61, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x65, 0x64, 0x54, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x18, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x16, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x23, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x50, 0x0a,
	0x0f, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0e, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x55, 0x75, 0x69, 0x64, 0x22, 0xfc, 0x03, 0x0a, 0x10, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x52, 0x0a, 0x0b, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x93, 0x03,
	0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x56, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x31, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x75, 0x6e, 0x69,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x55, 0x42, 0x54, 0x52, 0x41, 0x43,
	0x54, 0x10, 0x02, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_order_v1_order_line_item_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_order_line_item_models_proto_rawDescData = file_moego_models_order_v1_order_line_item_models_proto_rawDesc
)

func file_moego_models_order_v1_order_line_item_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_order_line_item_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_order_line_item_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_order_line_item_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_order_line_item_models_proto_rawDescData
}

var file_moego_models_order_v1_order_line_item_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_order_v1_order_line_item_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_order_v1_order_line_item_models_proto_goTypes = []interface{}{
	(PriceDetailModel_PriceItem_Operator)(0), // 0: moego.models.order.v1.PriceDetailModel.PriceItem.Operator
	(*OrderLineItemModel)(nil),               // 1: moego.models.order.v1.OrderLineItemModel
	(*OrderItemModel)(nil),                   // 2: moego.models.order.v1.OrderItemModel
	(*PriceDetailModel)(nil),                 // 3: moego.models.order.v1.PriceDetailModel
	(*PriceDetailModel_PriceItem)(nil),       // 4: moego.models.order.v1.PriceDetailModel.PriceItem
	(*OrderLineTaxModel)(nil),                // 5: moego.models.order.v1.OrderLineTaxModel
	(*OrderLineDiscountModel)(nil),           // 6: moego.models.order.v1.OrderLineDiscountModel
	(*OrderLineExtraFeeModel)(nil),           // 7: moego.models.order.v1.OrderLineExtraFeeModel
	(*decimal.Decimal)(nil),                  // 8: google.type.Decimal
	(*money.Money)(nil),                      // 9: google.type.Money
	(*OrderLineDiscountModelV1)(nil),         // 10: moego.models.order.v1.OrderLineDiscountModelV1
	(ItemType)(0),                            // 11: moego.models.order.v1.ItemType
}
var file_moego_models_order_v1_order_line_item_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.order.v1.OrderLineItemModel.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	6,  // 1: moego.models.order.v1.OrderLineItemModel.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	7,  // 2: moego.models.order.v1.OrderLineItemModel.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	8,  // 3: moego.models.order.v1.OrderLineItemModel.tax_rate:type_name -> google.type.Decimal
	9,  // 4: moego.models.order.v1.OrderLineItemModel.refunded_amount:type_name -> google.type.Money
	9,  // 5: moego.models.order.v1.OrderLineItemModel.refunded_tax_amount:type_name -> google.type.Money
	9,  // 6: moego.models.order.v1.OrderLineItemModel.refunded_discount_amount:type_name -> google.type.Money
	9,  // 7: moego.models.order.v1.OrderLineItemModel.deposit_amount:type_name -> google.type.Money
	9,  // 8: moego.models.order.v1.OrderLineItemModel.refunded_deposit_amount:type_name -> google.type.Money
	9,  // 9: moego.models.order.v1.OrderItemModel.unit_price:type_name -> google.type.Money
	9,  // 10: moego.models.order.v1.OrderItemModel.tips_amount:type_name -> google.type.Money
	9,  // 11: moego.models.order.v1.OrderItemModel.tax_amount:type_name -> google.type.Money
	9,  // 12: moego.models.order.v1.OrderItemModel.discount_amount:type_name -> google.type.Money
	9,  // 13: moego.models.order.v1.OrderItemModel.sub_total_amount:type_name -> google.type.Money
	9,  // 14: moego.models.order.v1.OrderItemModel.total_amount:type_name -> google.type.Money
	10, // 15: moego.models.order.v1.OrderItemModel.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModelV1
	8,  // 16: moego.models.order.v1.OrderItemModel.tax_rate:type_name -> google.type.Decimal
	9,  // 17: moego.models.order.v1.OrderItemModel.refunded_amount:type_name -> google.type.Money
	9,  // 18: moego.models.order.v1.OrderItemModel.refunded_tax_amount:type_name -> google.type.Money
	9,  // 19: moego.models.order.v1.OrderItemModel.refunded_discount_amount:type_name -> google.type.Money
	9,  // 20: moego.models.order.v1.OrderItemModel.deposit_amount:type_name -> google.type.Money
	9,  // 21: moego.models.order.v1.OrderItemModel.refunded_deposit_amount:type_name -> google.type.Money
	3,  // 22: moego.models.order.v1.OrderItemModel.subtotal_detail:type_name -> moego.models.order.v1.PriceDetailModel
	4,  // 23: moego.models.order.v1.PriceDetailModel.price_items:type_name -> moego.models.order.v1.PriceDetailModel.PriceItem
	0,  // 24: moego.models.order.v1.PriceDetailModel.PriceItem.operator:type_name -> moego.models.order.v1.PriceDetailModel.PriceItem.Operator
	9,  // 25: moego.models.order.v1.PriceDetailModel.PriceItem.unit_price:type_name -> google.type.Money
	9,  // 26: moego.models.order.v1.PriceDetailModel.PriceItem.sub_total:type_name -> google.type.Money
	11, // 27: moego.models.order.v1.PriceDetailModel.PriceItem.object_type:type_name -> moego.models.order.v1.ItemType
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_order_line_item_models_proto_init() }
func file_moego_models_order_v1_order_line_item_models_proto_init() {
	if File_moego_models_order_v1_order_line_item_models_proto != nil {
		return
	}
	file_moego_models_order_v1_order_enums_proto_init()
	file_moego_models_order_v1_order_line_discount_models_proto_init()
	file_moego_models_order_v1_order_line_extra_fee_models_proto_init()
	file_moego_models_order_v1_order_line_tax_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_order_line_item_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderLineItemModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_line_item_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderItemModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_line_item_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceDetailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_line_item_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceDetailModel_PriceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_order_line_item_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_order_line_item_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_order_line_item_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_order_line_item_models_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_order_line_item_models_proto_enumTypes,
		MessageInfos:      file_moego_models_order_v1_order_line_item_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_order_line_item_models_proto = out.File
	file_moego_models_order_v1_order_line_item_models_proto_rawDesc = nil
	file_moego_models_order_v1_order_line_item_models_proto_goTypes = nil
	file_moego_models_order_v1_order_line_item_models_proto_depIdxs = nil
}
