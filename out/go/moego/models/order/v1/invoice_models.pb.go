// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/invoice_models.proto

package orderpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// order info
type InvoiceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// grooming id
	GroomingId int64 `protobuf:"varint,3,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// type: appointment, noshow
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// subTotal amount
	SubTotalAmount float64 `protobuf:"fixed64,6,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
	// discount amount
	DiscountAmount float64 `protobuf:"fixed64,7,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// discount rate
	DiscountRate float64 `protobuf:"fixed64,8,opt,name=discount_rate,json=discountRate,proto3" json:"discount_rate,omitempty"`
	// discount type
	DiscountType string `protobuf:"bytes,9,opt,name=discount_type,json=discountType,proto3" json:"discount_type,omitempty"`
	// tips amount
	TipsAmount float64 `protobuf:"fixed64,10,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// tips rate
	TipsRate float64 `protobuf:"fixed64,11,opt,name=tips_rate,json=tipsRate,proto3" json:"tips_rate,omitempty"`
	// tips type
	TipsType string `protobuf:"bytes,12,opt,name=tips_type,json=tipsType,proto3" json:"tips_type,omitempty"`
	// tax amount
	TaxAmount float64 `protobuf:"fixed64,13,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// convenience_fee
	ConvenienceFee float64 `protobuf:"fixed64,14,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// payment amount
	PaymentAmount float64 `protobuf:"fixed64,15,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount,omitempty"`
	// paid amount
	PaidAmount float64 `protobuf:"fixed64,16,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// remain amount to pay
	RemainAmount float64 `protobuf:"fixed64,17,opt,name=remain_amount,json=remainAmount,proto3" json:"remain_amount,omitempty"`
	// refunded amount
	RefundedAmount float64 `protobuf:"fixed64,18,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// status
	Status int32 `protobuf:"varint,19,opt,name=status,proto3" json:"status,omitempty"`
	// order guid
	Guid string `protobuf:"bytes,20,opt,name=guid,proto3" json:"guid,omitempty"`
	// staff id of creating this order
	CreateBy int64 `protobuf:"varint,21,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// staff id of last updating this order
	UpdateBy int64 `protobuf:"varint,22,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,23,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,24,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// amount of tips based on
	TipsBasedAmount float64 `protobuf:"fixed64,25,opt,name=tips_based_amount,json=tipsBasedAmount,proto3" json:"tips_based_amount,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,26,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// retail title
	Title string `protobuf:"bytes,27,opt,name=title,proto3" json:"title,omitempty"`
	// retail item quantity
	Quantity int32 `protobuf:"varint,28,opt,name=quantity,proto3" json:"quantity,omitempty"`
}

func (x *InvoiceModel) Reset() {
	*x = InvoiceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvoiceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvoiceModel) ProtoMessage() {}

func (x *InvoiceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvoiceModel.ProtoReflect.Descriptor instead.
func (*InvoiceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_invoice_models_proto_rawDescGZIP(), []int{0}
}

func (x *InvoiceModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InvoiceModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *InvoiceModel) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *InvoiceModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InvoiceModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *InvoiceModel) GetSubTotalAmount() float64 {
	if x != nil {
		return x.SubTotalAmount
	}
	return 0
}

func (x *InvoiceModel) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *InvoiceModel) GetDiscountRate() float64 {
	if x != nil {
		return x.DiscountRate
	}
	return 0
}

func (x *InvoiceModel) GetDiscountType() string {
	if x != nil {
		return x.DiscountType
	}
	return ""
}

func (x *InvoiceModel) GetTipsAmount() float64 {
	if x != nil {
		return x.TipsAmount
	}
	return 0
}

func (x *InvoiceModel) GetTipsRate() float64 {
	if x != nil {
		return x.TipsRate
	}
	return 0
}

func (x *InvoiceModel) GetTipsType() string {
	if x != nil {
		return x.TipsType
	}
	return ""
}

func (x *InvoiceModel) GetTaxAmount() float64 {
	if x != nil {
		return x.TaxAmount
	}
	return 0
}

func (x *InvoiceModel) GetConvenienceFee() float64 {
	if x != nil {
		return x.ConvenienceFee
	}
	return 0
}

func (x *InvoiceModel) GetPaymentAmount() float64 {
	if x != nil {
		return x.PaymentAmount
	}
	return 0
}

func (x *InvoiceModel) GetPaidAmount() float64 {
	if x != nil {
		return x.PaidAmount
	}
	return 0
}

func (x *InvoiceModel) GetRemainAmount() float64 {
	if x != nil {
		return x.RemainAmount
	}
	return 0
}

func (x *InvoiceModel) GetRefundedAmount() float64 {
	if x != nil {
		return x.RefundedAmount
	}
	return 0
}

func (x *InvoiceModel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InvoiceModel) GetGuid() string {
	if x != nil {
		return x.Guid
	}
	return ""
}

func (x *InvoiceModel) GetCreateBy() int64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *InvoiceModel) GetUpdateBy() int64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *InvoiceModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *InvoiceModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *InvoiceModel) GetTipsBasedAmount() float64 {
	if x != nil {
		return x.TipsBasedAmount
	}
	return 0
}

func (x *InvoiceModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *InvoiceModel) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *InvoiceModel) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

// invoice for appointment
type InvoiceCalendarView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// status
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	// paid amount
	PaidAmount float64 `protobuf:"fixed64,3,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// refunded amount
	RefundedAmount float64 `protobuf:"fixed64,4,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// total amount
	TotalAmount float64 `protobuf:"fixed64,5,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// sub total amount
	SubTotalAmount float64 `protobuf:"fixed64,6,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
	// payment status
	PaymentStatus OrderModel_PaymentStatus `protobuf:"varint,7,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// outstanding_balance: computed by formula (total_amount - paid_amount + refunded_amount)
	OutstandingBalance float64 `protobuf:"fixed64,8,opt,name=outstanding_balance,json=outstandingBalance,proto3" json:"outstanding_balance,omitempty"`
}

func (x *InvoiceCalendarView) Reset() {
	*x = InvoiceCalendarView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvoiceCalendarView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvoiceCalendarView) ProtoMessage() {}

func (x *InvoiceCalendarView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvoiceCalendarView.ProtoReflect.Descriptor instead.
func (*InvoiceCalendarView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_invoice_models_proto_rawDescGZIP(), []int{1}
}

func (x *InvoiceCalendarView) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *InvoiceCalendarView) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InvoiceCalendarView) GetPaidAmount() float64 {
	if x != nil {
		return x.PaidAmount
	}
	return 0
}

func (x *InvoiceCalendarView) GetRefundedAmount() float64 {
	if x != nil {
		return x.RefundedAmount
	}
	return 0
}

func (x *InvoiceCalendarView) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *InvoiceCalendarView) GetSubTotalAmount() float64 {
	if x != nil {
		return x.SubTotalAmount
	}
	return 0
}

func (x *InvoiceCalendarView) GetPaymentStatus() OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *InvoiceCalendarView) GetOutstandingBalance() float64 {
	if x != nil {
		return x.OutstandingBalance
	}
	return 0
}

// no show invoice for appointment
type NoShowInvoiceCalendarView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// status
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *NoShowInvoiceCalendarView) Reset() {
	*x = NoShowInvoiceCalendarView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoShowInvoiceCalendarView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoShowInvoiceCalendarView) ProtoMessage() {}

func (x *NoShowInvoiceCalendarView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_invoice_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoShowInvoiceCalendarView.ProtoReflect.Descriptor instead.
func (*NoShowInvoiceCalendarView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_invoice_models_proto_rawDescGZIP(), []int{2}
}

func (x *NoShowInvoiceCalendarView) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *NoShowInvoiceCalendarView) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_moego_models_order_v1_invoice_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_invoice_models_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x07,
	0x0a, 0x0c, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x74, 0x69, 0x70, 0x73, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x70, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x62, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62,
	0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f,
	0x74, 0x69, 0x70, 0x73, 0x42, 0x61, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xec, 0x02, 0x0a,
	0x13, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x6f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x52, 0x0a, 0x19, 0x4e,
	0x6f, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_invoice_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_invoice_models_proto_rawDescData = file_moego_models_order_v1_invoice_models_proto_rawDesc
)

func file_moego_models_order_v1_invoice_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_invoice_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_invoice_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_invoice_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_invoice_models_proto_rawDescData
}

var file_moego_models_order_v1_invoice_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_order_v1_invoice_models_proto_goTypes = []interface{}{
	(*InvoiceModel)(nil),              // 0: moego.models.order.v1.InvoiceModel
	(*InvoiceCalendarView)(nil),       // 1: moego.models.order.v1.InvoiceCalendarView
	(*NoShowInvoiceCalendarView)(nil), // 2: moego.models.order.v1.NoShowInvoiceCalendarView
	(OrderModel_PaymentStatus)(0),     // 3: moego.models.order.v1.OrderModel.PaymentStatus
}
var file_moego_models_order_v1_invoice_models_proto_depIdxs = []int32{
	3, // 0: moego.models.order.v1.InvoiceCalendarView.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_invoice_models_proto_init() }
func file_moego_models_order_v1_invoice_models_proto_init() {
	if File_moego_models_order_v1_invoice_models_proto != nil {
		return
	}
	file_moego_models_order_v1_order_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_invoice_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvoiceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_invoice_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvoiceCalendarView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_invoice_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoShowInvoiceCalendarView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_invoice_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_invoice_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_invoice_models_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_invoice_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_invoice_models_proto = out.File
	file_moego_models_order_v1_invoice_models_proto_rawDesc = nil
	file_moego_models_order_v1_invoice_models_proto_goTypes = nil
	file_moego_models_order_v1_invoice_models_proto_depIdxs = nil
}
