// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/deposit_rule_defs.proto

package orderpb

import (
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create a deposit rule
type CreateDepositRuleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// filters
	Filters []*DepositFilter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// Deposit config
	//
	// Types that are assignable to DepositConfig:
	//
	//	*CreateDepositRuleDef_DepositByAmount
	//	*CreateDepositRuleDef_DepositByPercentage
	DepositConfig isCreateDepositRuleDef_DepositConfig `protobuf_oneof:"deposit_config"`
}

func (x *CreateDepositRuleDef) Reset() {
	*x = CreateDepositRuleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositRuleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositRuleDef) ProtoMessage() {}

func (x *CreateDepositRuleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositRuleDef.ProtoReflect.Descriptor instead.
func (*CreateDepositRuleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDepositRuleDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDepositRuleDef) GetFilters() []*DepositFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (m *CreateDepositRuleDef) GetDepositConfig() isCreateDepositRuleDef_DepositConfig {
	if m != nil {
		return m.DepositConfig
	}
	return nil
}

func (x *CreateDepositRuleDef) GetDepositByAmount() *money.Money {
	if x, ok := x.GetDepositConfig().(*CreateDepositRuleDef_DepositByAmount); ok {
		return x.DepositByAmount
	}
	return nil
}

func (x *CreateDepositRuleDef) GetDepositByPercentage() *decimal.Decimal {
	if x, ok := x.GetDepositConfig().(*CreateDepositRuleDef_DepositByPercentage); ok {
		return x.DepositByPercentage
	}
	return nil
}

type isCreateDepositRuleDef_DepositConfig interface {
	isCreateDepositRuleDef_DepositConfig()
}

type CreateDepositRuleDef_DepositByAmount struct {
	// By amount
	DepositByAmount *money.Money `protobuf:"bytes,3,opt,name=deposit_by_amount,json=depositByAmount,proto3,oneof"`
}

type CreateDepositRuleDef_DepositByPercentage struct {
	// By percentage (1 for 1%)
	DepositByPercentage *decimal.Decimal `protobuf:"bytes,4,opt,name=deposit_by_percentage,json=depositByPercentage,proto3,oneof"`
}

func (*CreateDepositRuleDef_DepositByAmount) isCreateDepositRuleDef_DepositConfig() {}

func (*CreateDepositRuleDef_DepositByPercentage) isCreateDepositRuleDef_DepositConfig() {}

// Update a deposit rule
type UpdateDepositRuleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// filters
	Filters []*DepositFilter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// Deposit config
	//
	// Types that are assignable to DepositConfig:
	//
	//	*UpdateDepositRuleDef_DepositByAmount
	//	*UpdateDepositRuleDef_DepositByPercentage
	DepositConfig isUpdateDepositRuleDef_DepositConfig `protobuf_oneof:"deposit_config"`
}

func (x *UpdateDepositRuleDef) Reset() {
	*x = UpdateDepositRuleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDepositRuleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepositRuleDef) ProtoMessage() {}

func (x *UpdateDepositRuleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepositRuleDef.ProtoReflect.Descriptor instead.
func (*UpdateDepositRuleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateDepositRuleDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDepositRuleDef) GetFilters() []*DepositFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (m *UpdateDepositRuleDef) GetDepositConfig() isUpdateDepositRuleDef_DepositConfig {
	if m != nil {
		return m.DepositConfig
	}
	return nil
}

func (x *UpdateDepositRuleDef) GetDepositByAmount() *money.Money {
	if x, ok := x.GetDepositConfig().(*UpdateDepositRuleDef_DepositByAmount); ok {
		return x.DepositByAmount
	}
	return nil
}

func (x *UpdateDepositRuleDef) GetDepositByPercentage() *decimal.Decimal {
	if x, ok := x.GetDepositConfig().(*UpdateDepositRuleDef_DepositByPercentage); ok {
		return x.DepositByPercentage
	}
	return nil
}

type isUpdateDepositRuleDef_DepositConfig interface {
	isUpdateDepositRuleDef_DepositConfig()
}

type UpdateDepositRuleDef_DepositByAmount struct {
	// By amount
	DepositByAmount *money.Money `protobuf:"bytes,3,opt,name=deposit_by_amount,json=depositByAmount,proto3,oneof"`
}

type UpdateDepositRuleDef_DepositByPercentage struct {
	// By percentage (1 for 1%)
	DepositByPercentage *decimal.Decimal `protobuf:"bytes,4,opt,name=deposit_by_percentage,json=depositByPercentage,proto3,oneof"`
}

func (*UpdateDepositRuleDef_DepositByAmount) isUpdateDepositRuleDef_DepositConfig() {}

func (*UpdateDepositRuleDef_DepositByPercentage) isUpdateDepositRuleDef_DepositConfig() {}

var File_moego_models_order_v1_deposit_rule_defs_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_deposit_rule_defs_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x02, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x42, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x15, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x48,
	0x00, 0x52, 0x13, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x42, 0x79, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x8a, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x5f, 0x62, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x42,
	0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x15, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x13,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x42, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_deposit_rule_defs_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_deposit_rule_defs_proto_rawDescData = file_moego_models_order_v1_deposit_rule_defs_proto_rawDesc
)

func file_moego_models_order_v1_deposit_rule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_deposit_rule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_deposit_rule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_deposit_rule_defs_proto_rawDescData)
	})
	return file_moego_models_order_v1_deposit_rule_defs_proto_rawDescData
}

var file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_order_v1_deposit_rule_defs_proto_goTypes = []interface{}{
	(*CreateDepositRuleDef)(nil), // 0: moego.models.order.v1.CreateDepositRuleDef
	(*UpdateDepositRuleDef)(nil), // 1: moego.models.order.v1.UpdateDepositRuleDef
	(*DepositFilter)(nil),        // 2: moego.models.order.v1.DepositFilter
	(*money.Money)(nil),          // 3: google.type.Money
	(*decimal.Decimal)(nil),      // 4: google.type.Decimal
}
var file_moego_models_order_v1_deposit_rule_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.order.v1.CreateDepositRuleDef.filters:type_name -> moego.models.order.v1.DepositFilter
	3, // 1: moego.models.order.v1.CreateDepositRuleDef.deposit_by_amount:type_name -> google.type.Money
	4, // 2: moego.models.order.v1.CreateDepositRuleDef.deposit_by_percentage:type_name -> google.type.Decimal
	2, // 3: moego.models.order.v1.UpdateDepositRuleDef.filters:type_name -> moego.models.order.v1.DepositFilter
	3, // 4: moego.models.order.v1.UpdateDepositRuleDef.deposit_by_amount:type_name -> google.type.Money
	4, // 5: moego.models.order.v1.UpdateDepositRuleDef.deposit_by_percentage:type_name -> google.type.Decimal
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_deposit_rule_defs_proto_init() }
func file_moego_models_order_v1_deposit_rule_defs_proto_init() {
	if File_moego_models_order_v1_deposit_rule_defs_proto != nil {
		return
	}
	file_moego_models_order_v1_deposit_rule_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositRuleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDepositRuleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreateDepositRuleDef_DepositByAmount)(nil),
		(*CreateDepositRuleDef_DepositByPercentage)(nil),
	}
	file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*UpdateDepositRuleDef_DepositByAmount)(nil),
		(*UpdateDepositRuleDef_DepositByPercentage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_deposit_rule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_deposit_rule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_deposit_rule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_deposit_rule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_deposit_rule_defs_proto = out.File
	file_moego_models_order_v1_deposit_rule_defs_proto_rawDesc = nil
	file_moego_models_order_v1_deposit_rule_defs_proto_goTypes = nil
	file_moego_models_order_v1_deposit_rule_defs_proto_depIdxs = nil
}
