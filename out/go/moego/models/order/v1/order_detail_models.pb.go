// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/order_detail_models.proto

package orderpb

import (
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Order detail with line items, discounts, taxes, extra fees
type OrderDetailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order info
	Order *OrderModel `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// order line items
	LineItems []*OrderLineItemModel `protobuf:"bytes,2,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// order line discounts
	LineDiscounts []*OrderLineDiscountModel `protobuf:"bytes,3,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// order line taxes
	LineTaxes []*OrderLineTaxModel `protobuf:"bytes,4,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// order line extra fee
	LineExtraFees []*OrderLineExtraFeeModel `protobuf:"bytes,5,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
}

func (x *OrderDetailModel) Reset() {
	*x = OrderDetailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailModel) ProtoMessage() {}

func (x *OrderDetailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailModel.ProtoReflect.Descriptor instead.
func (*OrderDetailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderDetailModel) GetOrder() *OrderModel {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *OrderDetailModel) GetLineItems() []*OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *OrderDetailModel) GetLineDiscounts() []*OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *OrderDetailModel) GetLineTaxes() []*OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *OrderDetailModel) GetLineExtraFees() []*OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

// OrderDetailModeV1 适用于支持 RefundByItem 之后的订单。
// 结构与 RefundOrderDetail 基本对齐。
type OrderDetailModelV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *OrderModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// OrderItems
	OrderItems []*OrderItemModel `protobuf:"bytes,2,rep,name=order_items,json=orderItems,proto3" json:"order_items,omitempty"`
	// Tips 的分配情况.
	TipsDetail []*CustomizedTipConfig `protobuf:"bytes,3,rep,name=tips_detail,json=tipsDetail,proto3" json:"tips_detail,omitempty"`
	// Payments.
	OrderPayments []*OrderPaymentModel `protobuf:"bytes,4,rep,name=order_payments,json=orderPayments,proto3" json:"order_payments,omitempty"`
	// Refund Payments.
	RefundOrderPayments []*RefundOrderPaymentModel `protobuf:"bytes,5,rep,name=refund_order_payments,json=refundOrderPayments,proto3" json:"refund_order_payments,omitempty"`
	// Order discounts.
	// 仅包含订单纬度的 Discount (Item 的已经在 Item 中).
	OrderDiscounts []*OrderLineDiscountModelV1 `protobuf:"bytes,6,rep,name=order_discounts,json=orderDiscounts,proto3" json:"order_discounts,omitempty"`
	// order promotions
	// 订单纬度所有的优惠都在这里(包括 Discount,Package,Membership,Store Credit,ETC.)
	OrderPromotions []*OrderPromotionModel `protobuf:"bytes,7,rep,name=order_promotions,json=orderPromotions,proto3" json:"order_promotions,omitempty"`
}

func (x *OrderDetailModelV1) Reset() {
	*x = OrderDetailModelV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailModelV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailModelV1) ProtoMessage() {}

func (x *OrderDetailModelV1) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailModelV1.ProtoReflect.Descriptor instead.
func (*OrderDetailModelV1) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{1}
}

func (x *OrderDetailModelV1) GetOrder() *OrderModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *OrderDetailModelV1) GetOrderItems() []*OrderItemModel {
	if x != nil {
		return x.OrderItems
	}
	return nil
}

func (x *OrderDetailModelV1) GetTipsDetail() []*CustomizedTipConfig {
	if x != nil {
		return x.TipsDetail
	}
	return nil
}

func (x *OrderDetailModelV1) GetOrderPayments() []*OrderPaymentModel {
	if x != nil {
		return x.OrderPayments
	}
	return nil
}

func (x *OrderDetailModelV1) GetRefundOrderPayments() []*RefundOrderPaymentModel {
	if x != nil {
		return x.RefundOrderPayments
	}
	return nil
}

func (x *OrderDetailModelV1) GetOrderDiscounts() []*OrderLineDiscountModelV1 {
	if x != nil {
		return x.OrderDiscounts
	}
	return nil
}

func (x *OrderDetailModelV1) GetOrderPromotions() []*OrderPromotionModel {
	if x != nil {
		return x.OrderPromotions
	}
	return nil
}

// Order detail view.
type OrderDetailView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订单详情.
	OrderDetail *OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order_detail,json=orderDetail,proto3" json:"order_detail,omitempty"`
	// 关联的宠物的基本信息.
	PetBriefs []*OrderDetailView_PetBrief `protobuf:"bytes,2,rep,name=pet_briefs,json=petBriefs,proto3" json:"pet_briefs,omitempty"`
	// 关联的 Staff 的基本信息.
	StaffBriefs []*OrderDetailView_StaffBrief `protobuf:"bytes,3,rep,name=staff_briefs,json=staffBriefs,proto3" json:"staff_briefs,omitempty"`
	// 按 Order item 的 Type 聚合之后的，Item.TotalAmount 的和（即折后税前价）
	SubtotalByItemType map[string]*money.Money `protobuf:"bytes,4,rep,name=subtotal_by_item_type,json=subtotalByItemType,proto3" json:"subtotal_by_item_type,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 按 Tax ID 纬度统计聚合之后的 Tax Amount，不是 Item Subtotal.
	SubtotalByTax []*OrderDetailView_Tax `protobuf:"bytes,5,rep,name=subtotal_by_tax,json=subtotalByTax,proto3" json:"subtotal_by_tax,omitempty"`
	// 按照 Order Item 的 Type 聚合之后的，Item.Subtotal 的和（即折前税前价）
	PreDiscountSubtotalByItemType map[string]*money.Money `protobuf:"bytes,6,rep,name=pre_discount_subtotal_by_item_type,json=preDiscountSubtotalByItemType,proto3" json:"pre_discount_subtotal_by_item_type,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *OrderDetailView) Reset() {
	*x = OrderDetailView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailView) ProtoMessage() {}

func (x *OrderDetailView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailView.ProtoReflect.Descriptor instead.
func (*OrderDetailView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{2}
}

func (x *OrderDetailView) GetOrderDetail() *OrderDetailModelV1 {
	if x != nil {
		return x.OrderDetail
	}
	return nil
}

func (x *OrderDetailView) GetPetBriefs() []*OrderDetailView_PetBrief {
	if x != nil {
		return x.PetBriefs
	}
	return nil
}

func (x *OrderDetailView) GetStaffBriefs() []*OrderDetailView_StaffBrief {
	if x != nil {
		return x.StaffBriefs
	}
	return nil
}

func (x *OrderDetailView) GetSubtotalByItemType() map[string]*money.Money {
	if x != nil {
		return x.SubtotalByItemType
	}
	return nil
}

func (x *OrderDetailView) GetSubtotalByTax() []*OrderDetailView_Tax {
	if x != nil {
		return x.SubtotalByTax
	}
	return nil
}

func (x *OrderDetailView) GetPreDiscountSubtotalByItemType() map[string]*money.Money {
	if x != nil {
		return x.PreDiscountSubtotalByItemType
	}
	return nil
}

// Refund order detail view.
type RefundOrderDetailView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order detail.
	RefundOrderDetail *RefundOrderDetailModel `protobuf:"bytes,1,opt,name=refund_order_detail,json=refundOrderDetail,proto3" json:"refund_order_detail,omitempty"`
	// 关联的宠物的基本信息.
	PetBriefs []*OrderDetailView_PetBrief `protobuf:"bytes,2,rep,name=pet_briefs,json=petBriefs,proto3" json:"pet_briefs,omitempty"`
	// 关联的 Staff 的基本信息.
	StaffBriefs []*OrderDetailView_StaffBrief `protobuf:"bytes,3,rep,name=staff_briefs,json=staffBriefs,proto3" json:"staff_briefs,omitempty"`
	// 按 Refund order item 的 Type 聚合之后的 Subtotal.
	SubtotalByItemType map[string]*money.Money `protobuf:"bytes,4,rep,name=subtotal_by_item_type,json=subtotalByItemType,proto3" json:"subtotal_by_item_type,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 按 Tax ID 纬度统计聚合之后的结果.
	SubtotalByTax []*OrderDetailView_Tax `protobuf:"bytes,5,rep,name=subtotal_by_tax,json=subtotalByTax,proto3" json:"subtotal_by_tax,omitempty"`
}

func (x *RefundOrderDetailView) Reset() {
	*x = RefundOrderDetailView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderDetailView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderDetailView) ProtoMessage() {}

func (x *RefundOrderDetailView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderDetailView.ProtoReflect.Descriptor instead.
func (*RefundOrderDetailView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{3}
}

func (x *RefundOrderDetailView) GetRefundOrderDetail() *RefundOrderDetailModel {
	if x != nil {
		return x.RefundOrderDetail
	}
	return nil
}

func (x *RefundOrderDetailView) GetPetBriefs() []*OrderDetailView_PetBrief {
	if x != nil {
		return x.PetBriefs
	}
	return nil
}

func (x *RefundOrderDetailView) GetStaffBriefs() []*OrderDetailView_StaffBrief {
	if x != nil {
		return x.StaffBriefs
	}
	return nil
}

func (x *RefundOrderDetailView) GetSubtotalByItemType() map[string]*money.Money {
	if x != nil {
		return x.SubtotalByItemType
	}
	return nil
}

func (x *RefundOrderDetailView) GetSubtotalByTax() []*OrderDetailView_Tax {
	if x != nil {
		return x.SubtotalByTax
	}
	return nil
}

// 基础的 Staff 信息.
type OrderDetailView_StaffBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Staff ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Staff first name.
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// Staff last name.
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *OrderDetailView_StaffBrief) Reset() {
	*x = OrderDetailView_StaffBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailView_StaffBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailView_StaffBrief) ProtoMessage() {}

func (x *OrderDetailView_StaffBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailView_StaffBrief.ProtoReflect.Descriptor instead.
func (*OrderDetailView_StaffBrief) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{2, 2}
}

func (x *OrderDetailView_StaffBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderDetailView_StaffBrief) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *OrderDetailView_StaffBrief) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// 基础的宠物信息
type OrderDetailView_PetBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Pet name.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Pet breed.
	Breed string `protobuf:"bytes,3,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *OrderDetailView_PetBrief) Reset() {
	*x = OrderDetailView_PetBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailView_PetBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailView_PetBrief) ProtoMessage() {}

func (x *OrderDetailView_PetBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailView_PetBrief.ProtoReflect.Descriptor instead.
func (*OrderDetailView_PetBrief) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{2, 3}
}

func (x *OrderDetailView_PetBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderDetailView_PetBrief) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderDetailView_PetBrief) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

// Tax.
type OrderDetailView_Tax struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tax ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Tax name.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Tax rate. 仅用作记录，不参与计算.
	Rate *decimal.Decimal `protobuf:"bytes,3,opt,name=rate,proto3" json:"rate,omitempty"`
	// Tax amount.
	Amount *money.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *OrderDetailView_Tax) Reset() {
	*x = OrderDetailView_Tax{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailView_Tax) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailView_Tax) ProtoMessage() {}

func (x *OrderDetailView_Tax) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_detail_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailView_Tax.ProtoReflect.Descriptor instead.
func (*OrderDetailView_Tax) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP(), []int{2, 4}
}

func (x *OrderDetailView_Tax) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderDetailView_Tax) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderDetailView_Tax) GetRate() *decimal.Decimal {
	if x != nil {
		return x.Rate
	}
	return nil
}

func (x *OrderDetailView_Tax) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

var File_moego_models_order_v1_order_detail_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_order_detail_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74,
	0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x8b, 0x03, 0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x37, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48,
	0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x47,
	0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69,
	0x6e, 0x65, 0x54, 0x61, 0x78, 0x65, 0x73, 0x12, 0x55, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x73, 0x22, 0xca,
	0x04, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x56, 0x31, 0x12, 0x39, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x46, 0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x54, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4f, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x62, 0x0a, 0x15, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x0f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x56, 0x31, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc5, 0x08, 0x0a, 0x0f,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x4c, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31,
	0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a,
	0x0a, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x09, 0x70, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x73, 0x12, 0x54, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x73, 0x12, 0x71, 0x0a, 0x15, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x62, 0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x75, 0x62, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x12, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x61, 0x78, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x0d, 0x73, 0x75, 0x62,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x54, 0x61, 0x78, 0x12, 0x94, 0x01, 0x0a, 0x22, 0x70,
	0x72, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e,
	0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x1d, 0x70, 0x72, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x1a, 0x59, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x64, 0x0a, 0x22,
	0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x58, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69, 0x65, 0x66,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x44, 0x0a, 0x08,
	0x50, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x1a, 0x7f, 0x0a, 0x03, 0x54, 0x61, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61,
	0x6c, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xc4, 0x04, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x5d, 0x0a,
	0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x0a,
	0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x52, 0x09, 0x70, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x73, 0x12, 0x54, 0x0a, 0x0c,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x73, 0x12, 0x77, 0x0a, 0x15, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62,
	0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e,
	0x53, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x73,
	0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x61, 0x78, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x54, 0x61, 0x78,
	0x52, 0x0d, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x54, 0x61, 0x78, 0x1a,
	0x59, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_order_detail_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_order_detail_models_proto_rawDescData = file_moego_models_order_v1_order_detail_models_proto_rawDesc
)

func file_moego_models_order_v1_order_detail_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_order_detail_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_order_detail_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_order_detail_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_order_detail_models_proto_rawDescData
}

var file_moego_models_order_v1_order_detail_models_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_order_v1_order_detail_models_proto_goTypes = []interface{}{
	(*OrderDetailModel)(nil),           // 0: moego.models.order.v1.OrderDetailModel
	(*OrderDetailModelV1)(nil),         // 1: moego.models.order.v1.OrderDetailModelV1
	(*OrderDetailView)(nil),            // 2: moego.models.order.v1.OrderDetailView
	(*RefundOrderDetailView)(nil),      // 3: moego.models.order.v1.RefundOrderDetailView
	nil,                                // 4: moego.models.order.v1.OrderDetailView.SubtotalByItemTypeEntry
	nil,                                // 5: moego.models.order.v1.OrderDetailView.PreDiscountSubtotalByItemTypeEntry
	(*OrderDetailView_StaffBrief)(nil), // 6: moego.models.order.v1.OrderDetailView.StaffBrief
	(*OrderDetailView_PetBrief)(nil),   // 7: moego.models.order.v1.OrderDetailView.PetBrief
	(*OrderDetailView_Tax)(nil),        // 8: moego.models.order.v1.OrderDetailView.Tax
	nil,                                // 9: moego.models.order.v1.RefundOrderDetailView.SubtotalByItemTypeEntry
	(*OrderModel)(nil),                 // 10: moego.models.order.v1.OrderModel
	(*OrderLineItemModel)(nil),         // 11: moego.models.order.v1.OrderLineItemModel
	(*OrderLineDiscountModel)(nil),     // 12: moego.models.order.v1.OrderLineDiscountModel
	(*OrderLineTaxModel)(nil),          // 13: moego.models.order.v1.OrderLineTaxModel
	(*OrderLineExtraFeeModel)(nil),     // 14: moego.models.order.v1.OrderLineExtraFeeModel
	(*OrderModelV1)(nil),               // 15: moego.models.order.v1.OrderModelV1
	(*OrderItemModel)(nil),             // 16: moego.models.order.v1.OrderItemModel
	(*CustomizedTipConfig)(nil),        // 17: moego.models.order.v1.CustomizedTipConfig
	(*OrderPaymentModel)(nil),          // 18: moego.models.order.v1.OrderPaymentModel
	(*RefundOrderPaymentModel)(nil),    // 19: moego.models.order.v1.RefundOrderPaymentModel
	(*OrderLineDiscountModelV1)(nil),   // 20: moego.models.order.v1.OrderLineDiscountModelV1
	(*OrderPromotionModel)(nil),        // 21: moego.models.order.v1.OrderPromotionModel
	(*RefundOrderDetailModel)(nil),     // 22: moego.models.order.v1.RefundOrderDetailModel
	(*money.Money)(nil),                // 23: google.type.Money
	(*decimal.Decimal)(nil),            // 24: google.type.Decimal
}
var file_moego_models_order_v1_order_detail_models_proto_depIdxs = []int32{
	10, // 0: moego.models.order.v1.OrderDetailModel.order:type_name -> moego.models.order.v1.OrderModel
	11, // 1: moego.models.order.v1.OrderDetailModel.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	12, // 2: moego.models.order.v1.OrderDetailModel.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	13, // 3: moego.models.order.v1.OrderDetailModel.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	14, // 4: moego.models.order.v1.OrderDetailModel.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	15, // 5: moego.models.order.v1.OrderDetailModelV1.order:type_name -> moego.models.order.v1.OrderModelV1
	16, // 6: moego.models.order.v1.OrderDetailModelV1.order_items:type_name -> moego.models.order.v1.OrderItemModel
	17, // 7: moego.models.order.v1.OrderDetailModelV1.tips_detail:type_name -> moego.models.order.v1.CustomizedTipConfig
	18, // 8: moego.models.order.v1.OrderDetailModelV1.order_payments:type_name -> moego.models.order.v1.OrderPaymentModel
	19, // 9: moego.models.order.v1.OrderDetailModelV1.refund_order_payments:type_name -> moego.models.order.v1.RefundOrderPaymentModel
	20, // 10: moego.models.order.v1.OrderDetailModelV1.order_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModelV1
	21, // 11: moego.models.order.v1.OrderDetailModelV1.order_promotions:type_name -> moego.models.order.v1.OrderPromotionModel
	1,  // 12: moego.models.order.v1.OrderDetailView.order_detail:type_name -> moego.models.order.v1.OrderDetailModelV1
	7,  // 13: moego.models.order.v1.OrderDetailView.pet_briefs:type_name -> moego.models.order.v1.OrderDetailView.PetBrief
	6,  // 14: moego.models.order.v1.OrderDetailView.staff_briefs:type_name -> moego.models.order.v1.OrderDetailView.StaffBrief
	4,  // 15: moego.models.order.v1.OrderDetailView.subtotal_by_item_type:type_name -> moego.models.order.v1.OrderDetailView.SubtotalByItemTypeEntry
	8,  // 16: moego.models.order.v1.OrderDetailView.subtotal_by_tax:type_name -> moego.models.order.v1.OrderDetailView.Tax
	5,  // 17: moego.models.order.v1.OrderDetailView.pre_discount_subtotal_by_item_type:type_name -> moego.models.order.v1.OrderDetailView.PreDiscountSubtotalByItemTypeEntry
	22, // 18: moego.models.order.v1.RefundOrderDetailView.refund_order_detail:type_name -> moego.models.order.v1.RefundOrderDetailModel
	7,  // 19: moego.models.order.v1.RefundOrderDetailView.pet_briefs:type_name -> moego.models.order.v1.OrderDetailView.PetBrief
	6,  // 20: moego.models.order.v1.RefundOrderDetailView.staff_briefs:type_name -> moego.models.order.v1.OrderDetailView.StaffBrief
	9,  // 21: moego.models.order.v1.RefundOrderDetailView.subtotal_by_item_type:type_name -> moego.models.order.v1.RefundOrderDetailView.SubtotalByItemTypeEntry
	8,  // 22: moego.models.order.v1.RefundOrderDetailView.subtotal_by_tax:type_name -> moego.models.order.v1.OrderDetailView.Tax
	23, // 23: moego.models.order.v1.OrderDetailView.SubtotalByItemTypeEntry.value:type_name -> google.type.Money
	23, // 24: moego.models.order.v1.OrderDetailView.PreDiscountSubtotalByItemTypeEntry.value:type_name -> google.type.Money
	24, // 25: moego.models.order.v1.OrderDetailView.Tax.rate:type_name -> google.type.Decimal
	23, // 26: moego.models.order.v1.OrderDetailView.Tax.amount:type_name -> google.type.Money
	23, // 27: moego.models.order.v1.RefundOrderDetailView.SubtotalByItemTypeEntry.value:type_name -> google.type.Money
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_order_detail_models_proto_init() }
func file_moego_models_order_v1_order_detail_models_proto_init() {
	if File_moego_models_order_v1_order_detail_models_proto != nil {
		return
	}
	file_moego_models_order_v1_order_line_discount_models_proto_init()
	file_moego_models_order_v1_order_line_extra_fee_models_proto_init()
	file_moego_models_order_v1_order_line_item_models_proto_init()
	file_moego_models_order_v1_order_line_tax_models_proto_init()
	file_moego_models_order_v1_order_models_proto_init()
	file_moego_models_order_v1_order_promotion_models_proto_init()
	file_moego_models_order_v1_refund_order_models_proto_init()
	file_moego_models_order_v1_split_tips_defs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailModelV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderDetailView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailView_StaffBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailView_PetBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_detail_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailView_Tax); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_order_detail_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_order_detail_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_order_detail_models_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_order_detail_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_order_detail_models_proto = out.File
	file_moego_models_order_v1_order_detail_models_proto_rawDesc = nil
	file_moego_models_order_v1_order_detail_models_proto_goTypes = nil
	file_moego_models_order_v1_order_detail_models_proto_depIdxs = nil
}
