// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/map/v1/route_models.proto

package mappb

import (
	viewport "google.golang.org/genproto/googleapis/geo/type/viewport"
	status "google.golang.org/genproto/googleapis/rpc/status"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	localized_text "google.golang.org/genproto/googleapis/type/localized_text"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Waypoint
type Waypoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Different ways to represent a location.
	//
	// Types that are assignable to LocationType:
	//
	//	*Waypoint_Coordinate
	//	*Waypoint_AddressSource
	//	*Waypoint_Address
	LocationType isWaypoint_LocationType `protobuf_oneof:"location_type"`
	// Marks this waypoint as a milestone rather a stopping point.
	// For each non-via waypoint in the request,
	// the response appends an entry to the [Route.legs] array to provide the details for stopovers on that leg of the trip.
	// Set this value to true when you want the route to pass through this waypoint without stopping over.
	// Via waypoints don't cause an entry to be added to the `legs` array, but they do route the journey through the waypoint.
	// You can only set this value on waypoints that are intermediates.
	// The request fails if you set this field on terminal waypoints.
	// If [QueryRoutesRequest.optimize_waypoint_order] is set to true then this field cannot be set to true; otherwise, the request fails.
	Via *bool `protobuf:"varint,4,opt,name=via,proto3,oneof" json:"via,omitempty"`
	// Indicates that the waypoint is meant for vehicles to stop at, where the intention is to either pickup or drop-off.
	// When you set this value, the calculated route won't include non-`via` waypoints on roads that are unsuitable for pickup and drop-off.
	// This option works only for `DRIVING` and `TWO_WHEELER` travel modes, and when the `location_type` is [google.type.LatLng].
	VehicleStopover *bool `protobuf:"varint,5,opt,name=vehicle_stopover,json=vehicleStopover,proto3,oneof" json:"vehicle_stopover,omitempty"`
	// Indicates that the location of this waypoint is meant to have a preference for the vehicle to stop at a particular side of road.
	// When you set this value, the route will pass through the location so that
	// the vehicle can stop at the side of road that the location is biased towards from the center of the road.
	// This option works only for 'DRIVING' and 'TWO_WHEELER'.
	SideOfRoad *bool `protobuf:"varint,6,opt,name=side_of_road,json=sideOfRoad,proto3,oneof" json:"side_of_road,omitempty"`
}

func (x *Waypoint) Reset() {
	*x = Waypoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Waypoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Waypoint) ProtoMessage() {}

func (x *Waypoint) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Waypoint.ProtoReflect.Descriptor instead.
func (*Waypoint) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{0}
}

func (m *Waypoint) GetLocationType() isWaypoint_LocationType {
	if m != nil {
		return m.LocationType
	}
	return nil
}

func (x *Waypoint) GetCoordinate() *latlng.LatLng {
	if x, ok := x.GetLocationType().(*Waypoint_Coordinate); ok {
		return x.Coordinate
	}
	return nil
}

func (x *Waypoint) GetAddressSource() *AddressSource {
	if x, ok := x.GetLocationType().(*Waypoint_AddressSource); ok {
		return x.AddressSource
	}
	return nil
}

func (x *Waypoint) GetAddress() string {
	if x, ok := x.GetLocationType().(*Waypoint_Address); ok {
		return x.Address
	}
	return ""
}

func (x *Waypoint) GetVia() bool {
	if x != nil && x.Via != nil {
		return *x.Via
	}
	return false
}

func (x *Waypoint) GetVehicleStopover() bool {
	if x != nil && x.VehicleStopover != nil {
		return *x.VehicleStopover
	}
	return false
}

func (x *Waypoint) GetSideOfRoad() bool {
	if x != nil && x.SideOfRoad != nil {
		return *x.SideOfRoad
	}
	return false
}

type isWaypoint_LocationType interface {
	isWaypoint_LocationType()
}

type Waypoint_Coordinate struct {
	// A point specified using geographic coordinates
	Coordinate *latlng.LatLng `protobuf:"bytes,1,opt,name=coordinate,proto3,oneof"`
}

type Waypoint_AddressSource struct {
	// address source, e.g. the Google Map Place ID or a plus code or a custom address id.
	AddressSource *AddressSource `protobuf:"bytes,2,opt,name=address_source,json=addressSource,proto3,oneof"`
}

type Waypoint_Address struct {
	// Human readable address.
	Address string `protobuf:"bytes,3,opt,name=address,proto3,oneof"`
}

func (*Waypoint_Coordinate) isWaypoint_LocationType() {}

func (*Waypoint_AddressSource) isWaypoint_LocationType() {}

func (*Waypoint_Address) isWaypoint_LocationType() {}

// Route
type Route struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The travel distance of the route, in meters.
	Distance *int32 `protobuf:"varint,1,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	// The length of time needed to navigate the route.
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// The overall route polyline. This polyline is the combined polyline of all `legs`.
	Polyline *Polyline `protobuf:"bytes,3,opt,name=polyline,proto3,oneof" json:"polyline,omitempty"`
	// Labels for the `Route` that are useful to identify specific properties of the route to compare against others.
	Labels []RouteLabel `protobuf:"varint,4,rep,packed,name=labels,proto3,enum=moego.models.map.v1.RouteLabel" json:"labels,omitempty"`
	// if you set [QueryRoutesRequest.optimize_waypoint_order] to true,
	// this field contains the optimized ordering of intermediate waypoints. Otherwise, this field is empty.
	OptimizedPointIndex []int32 `protobuf:"varint,5,rep,packed,name=optimized_point_index,json=optimizedPointIndex,proto3" json:"optimized_point_index,omitempty"`
	// a collection of legs (path segments between waypoints) that make up the route.
	// each leg corresponds to the trip between two non-`via` [Waypoints].
	Legs []*RouteLeg `protobuf:"bytes,6,rep,name=legs,proto3" json:"legs,omitempty"`
	// An array of warnings to show when displaying the route.
	Warnings []string `protobuf:"bytes,7,rep,name=warnings,proto3" json:"warnings,omitempty"`
	// A description of the route.
	Description *string `protobuf:"bytes,8,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// The viewport bounding box of the polyline.
	Viewport *viewport.Viewport `protobuf:"bytes,9,opt,name=viewport,proto3,oneof" json:"viewport,omitempty"`
	// Additional information about the route.
	TravelAdvisory *RouteTravelAdvisory `protobuf:"bytes,10,opt,name=travel_advisory,json=travelAdvisory,proto3,oneof" json:"travel_advisory,omitempty"`
	// A web-safe, base64-encoded route token that can be passed to the Navigation SDK,
	// that allows the Navigation SDK to reconstruct the route during navigation,
	// and, in the event of rerouting, honor the original intention
	// when you created the route by calling ComputeRoutes. Customers should treat this token as an opaque blob.
	// It is not meant for reading or mutating.
	// NOTE: `Route.route_token` is only available for requests that have set `QueryRoutesRequest.route_preference` to `ROUTE_PREFERENCE_AWARE` or `ROUTE_PREFERENCE_OPTIMAL`.
	// `Route.route_token` is not supported for requests that have Via waypoints.
	RouteToken *string `protobuf:"bytes,11,opt,name=route_token,json=routeToken,proto3,oneof" json:"route_token,omitempty"`
	// Text representations of properties of the `RouteLegStep`.
	LocalizedValues *LocalizedValues `protobuf:"bytes,12,opt,name=localized_values,json=localizedValues,proto3,oneof" json:"localized_values,omitempty"`
}

func (x *Route) Reset() {
	*x = Route{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Route) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Route) ProtoMessage() {}

func (x *Route) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Route.ProtoReflect.Descriptor instead.
func (*Route) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{1}
}

func (x *Route) GetDistance() int32 {
	if x != nil && x.Distance != nil {
		return *x.Distance
	}
	return 0
}

func (x *Route) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *Route) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *Route) GetLabels() []RouteLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Route) GetOptimizedPointIndex() []int32 {
	if x != nil {
		return x.OptimizedPointIndex
	}
	return nil
}

func (x *Route) GetLegs() []*RouteLeg {
	if x != nil {
		return x.Legs
	}
	return nil
}

func (x *Route) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *Route) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Route) GetViewport() *viewport.Viewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

func (x *Route) GetTravelAdvisory() *RouteTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

func (x *Route) GetRouteToken() string {
	if x != nil && x.RouteToken != nil {
		return *x.RouteToken
	}
	return ""
}

func (x *Route) GetLocalizedValues() *LocalizedValues {
	if x != nil {
		return x.LocalizedValues
	}
	return nil
}

// RouteLeg
// Contains a segment between non-`via` waypoints.
type RouteLeg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The travel distance of the route leg, in meters.
	Distance *int32 `protobuf:"varint,1,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	// The length of time needed to navigate the leg.
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// The overall polyline for this leg that includes each `step`'s polyline.
	Polyline *Polyline `protobuf:"bytes,3,opt,name=polyline,proto3,oneof" json:"polyline,omitempty"`
	// The start location of this leg. This location might be different from the provided `origin`.
	// For example, when the provided `origin` is not near a road, this is a point on the road.
	StartLocation *latlng.LatLng `protobuf:"bytes,4,opt,name=start_location,json=startLocation,proto3,oneof" json:"start_location,omitempty"`
	// The end location of this leg. This location might be different from the provided `destination`.
	// For example, when the provided `destination` is not near a road, this is a point on the road.
	EndLocation *latlng.LatLng `protobuf:"bytes,5,opt,name=end_location,json=endLocation,proto3,oneof" json:"end_location,omitempty"`
	// An array of steps denoting segments within this leg. Each step represents one navigation instruction.
	Steps []*RouteLegStep `protobuf:"bytes,6,rep,name=steps,proto3" json:"steps,omitempty"`
	// Contains the additional information that the user should be informed about, such as possible traffic zone restrictions, on a route leg.
	TravelAdvisory *RouteTravelAdvisory `protobuf:"bytes,7,opt,name=travel_advisory,json=travelAdvisory,proto3,oneof" json:"travel_advisory,omitempty"`
	// Overview information about the steps in this `RouteLeg`. This field is only populated for TRANSIT routes.
	StepsOverview *StepsOverview `protobuf:"bytes,8,opt,name=steps_overview,json=stepsOverview,proto3,oneof" json:"steps_overview,omitempty"`
	// Text representations of properties of the `RouteLeg`.
	LocalizedValues *LocalizedValues `protobuf:"bytes,9,opt,name=localized_values,json=localizedValues,proto3,oneof" json:"localized_values,omitempty"`
}

func (x *RouteLeg) Reset() {
	*x = RouteLeg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLeg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLeg) ProtoMessage() {}

func (x *RouteLeg) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLeg.ProtoReflect.Descriptor instead.
func (*RouteLeg) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{2}
}

func (x *RouteLeg) GetDistance() int32 {
	if x != nil && x.Distance != nil {
		return *x.Distance
	}
	return 0
}

func (x *RouteLeg) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *RouteLeg) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *RouteLeg) GetStartLocation() *latlng.LatLng {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *RouteLeg) GetEndLocation() *latlng.LatLng {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *RouteLeg) GetSteps() []*RouteLegStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *RouteLeg) GetTravelAdvisory() *RouteTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

func (x *RouteLeg) GetStepsOverview() *StepsOverview {
	if x != nil {
		return x.StepsOverview
	}
	return nil
}

func (x *RouteLeg) GetLocalizedValues() *LocalizedValues {
	if x != nil {
		return x.LocalizedValues
	}
	return nil
}

// RouteLegStep
// Contains a segment of a [RouteLeg].
// A step corresponds to a single navigation instruction. Route legs are made up of steps.
type RouteLegStep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The travel distance of this step, in meters.
	// In some circumstances, this field might not have a value.
	Distance *int32 `protobuf:"varint,1,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	// The duration of travel through this step without taking traffic conditions into consideration.
	// In some circumstances, this field might not have a value.
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// The polyline associated with this step.
	Polyline *Polyline `protobuf:"bytes,3,opt,name=polyline,proto3,oneof" json:"polyline,omitempty"`
	// The start location of this step.
	StartLocation *latlng.LatLng `protobuf:"bytes,4,opt,name=start_location,json=startLocation,proto3,oneof" json:"start_location,omitempty"`
	// The end location of this step.
	EndLocation *latlng.LatLng `protobuf:"bytes,5,opt,name=end_location,json=endLocation,proto3,oneof" json:"end_location,omitempty"`
	// The travel mode used for this step.
	TravelMode *TravelMode `protobuf:"varint,6,opt,name=travel_mode,json=travelMode,proto3,enum=moego.models.map.v1.TravelMode,oneof" json:"travel_mode,omitempty"`
	// Navigation instructions.
	NavigationInstruction *NavigationInstruction `protobuf:"bytes,7,opt,name=navigation_instruction,json=navigationInstruction,proto3,oneof" json:"navigation_instruction,omitempty"`
	// Contains the additional information that the user should be informed
	// about, such as possible traffic zone restrictions, on a leg step.
	TravelAdvisory *RouteTravelAdvisory `protobuf:"bytes,8,opt,name=travel_advisory,json=travelAdvisory,proto3,oneof" json:"travel_advisory,omitempty"`
	// Details pertaining to this step if the travel mode is `TRANSIT`.
	TransitDetails *RouteLegStepTransitDetails `protobuf:"bytes,9,opt,name=transit_details,json=transitDetails,proto3,oneof" json:"transit_details,omitempty"`
	// Text representations of properties of the `RouteLegStep`.
	LocalizedValues *LocalizedValues `protobuf:"bytes,10,opt,name=localized_values,json=localizedValues,proto3,oneof" json:"localized_values,omitempty"`
}

func (x *RouteLegStep) Reset() {
	*x = RouteLegStep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegStep) ProtoMessage() {}

func (x *RouteLegStep) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegStep.ProtoReflect.Descriptor instead.
func (*RouteLegStep) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{3}
}

func (x *RouteLegStep) GetDistance() int32 {
	if x != nil && x.Distance != nil {
		return *x.Distance
	}
	return 0
}

func (x *RouteLegStep) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *RouteLegStep) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *RouteLegStep) GetStartLocation() *latlng.LatLng {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *RouteLegStep) GetEndLocation() *latlng.LatLng {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *RouteLegStep) GetTravelMode() TravelMode {
	if x != nil && x.TravelMode != nil {
		return *x.TravelMode
	}
	return TravelMode_TRAVEL_MODE_UNSPECIFIED
}

func (x *RouteLegStep) GetNavigationInstruction() *NavigationInstruction {
	if x != nil {
		return x.NavigationInstruction
	}
	return nil
}

func (x *RouteLegStep) GetTravelAdvisory() *RouteTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

func (x *RouteLegStep) GetTransitDetails() *RouteLegStepTransitDetails {
	if x != nil {
		return x.TransitDetails
	}
	return nil
}

func (x *RouteLegStep) GetLocalizedValues() *LocalizedValues {
	if x != nil {
		return x.LocalizedValues
	}
	return nil
}

// RouteModifiers
// Encapsulates a set of optional conditions to satisfy when calculating the routes.
type RouteModifiers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if true, avoids highways where reasonable, giving preference to routes not containing highways.
	// when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
	AvoidTolls *bool `protobuf:"varint,1,opt,name=avoid_tolls,json=avoidTolls,proto3,oneof" json:"avoid_tolls,omitempty"`
	// if true, avoids toll roads where reasonable, giving preference to routes not containing toll roads.
	// when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
	AvoidHighways *bool `protobuf:"varint,2,opt,name=avoid_highways,json=avoidHighways,proto3,oneof" json:"avoid_highways,omitempty"`
	// if true, avoids ferries where reasonable, giving preference to routes not containing ferries.
	// when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
	AvoidFerries *bool `protobuf:"varint,3,opt,name=avoid_ferries,json=avoidFerries,proto3,oneof" json:"avoid_ferries,omitempty"`
	// if true, avoids navigating indoors where reasonable, giving preference to routes not containing indoor navigation.
	// when specified, the travel_mode must be WALKING, otherwise the value will be ignored.
	AvoidIndoor *bool `protobuf:"varint,4,opt,name=avoid_indoor,json=avoidIndoor,proto3,oneof" json:"avoid_indoor,omitempty"`
	// Specifies the vehicle information.
	// specifies the vehicle's emission type.
	// when specified, the travel_mode must be DRIVING, otherwise the value will be ignored.
	EmissionType *VehicleEmissionType `protobuf:"varint,5,opt,name=emission_type,json=emissionType,proto3,enum=moego.models.map.v1.VehicleEmissionType,oneof" json:"emission_type,omitempty"`
	// specifies the information about toll passes.
	// If toll passes are provided, the API tries to return the pass price.
	// If toll passes are not provided, the API treats the toll pass as unknown and tries to return the cash price.
	// when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
	// list of supported toll passes see: https://developers.google.com/maps/documentation/routes/reference/rest/v2/RouteModifiers#tollpass
	TollPasses []string `protobuf:"bytes,6,rep,name=toll_passes,json=tollPasses,proto3" json:"toll_passes,omitempty"`
}

func (x *RouteModifiers) Reset() {
	*x = RouteModifiers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteModifiers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteModifiers) ProtoMessage() {}

func (x *RouteModifiers) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteModifiers.ProtoReflect.Descriptor instead.
func (*RouteModifiers) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{4}
}

func (x *RouteModifiers) GetAvoidTolls() bool {
	if x != nil && x.AvoidTolls != nil {
		return *x.AvoidTolls
	}
	return false
}

func (x *RouteModifiers) GetAvoidHighways() bool {
	if x != nil && x.AvoidHighways != nil {
		return *x.AvoidHighways
	}
	return false
}

func (x *RouteModifiers) GetAvoidFerries() bool {
	if x != nil && x.AvoidFerries != nil {
		return *x.AvoidFerries
	}
	return false
}

func (x *RouteModifiers) GetAvoidIndoor() bool {
	if x != nil && x.AvoidIndoor != nil {
		return *x.AvoidIndoor
	}
	return false
}

func (x *RouteModifiers) GetEmissionType() VehicleEmissionType {
	if x != nil && x.EmissionType != nil {
		return *x.EmissionType
	}
	return VehicleEmissionType_VEHICLE_EMISSION_TYPE_UNSPECIFIED
}

func (x *RouteModifiers) GetTollPasses() []string {
	if x != nil {
		return x.TollPasses
	}
	return nil
}

// Encapsulates an encoded polyline.
type Polyline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates the type of polyline. Defaults to encoded_polyline.
	//
	// Types that are assignable to PolylineType:
	//
	//	*Polyline_EncodedPolyline
	//	*Polyline_GeoJsonLinestring
	PolylineType isPolyline_PolylineType `protobuf_oneof:"polyline_type"`
}

func (x *Polyline) Reset() {
	*x = Polyline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Polyline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Polyline) ProtoMessage() {}

func (x *Polyline) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Polyline.ProtoReflect.Descriptor instead.
func (*Polyline) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{5}
}

func (m *Polyline) GetPolylineType() isPolyline_PolylineType {
	if m != nil {
		return m.PolylineType
	}
	return nil
}

func (x *Polyline) GetEncodedPolyline() string {
	if x, ok := x.GetPolylineType().(*Polyline_EncodedPolyline); ok {
		return x.EncodedPolyline
	}
	return ""
}

func (x *Polyline) GetGeoJsonLinestring() *structpb.Struct {
	if x, ok := x.GetPolylineType().(*Polyline_GeoJsonLinestring); ok {
		return x.GeoJsonLinestring
	}
	return nil
}

type isPolyline_PolylineType interface {
	isPolyline_PolylineType()
}

type Polyline_EncodedPolyline struct {
	// The string encoding of the polyline using the [polyline encoding algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm)
	EncodedPolyline string `protobuf:"bytes,1,opt,name=encoded_polyline,json=encodedPolyline,proto3,oneof"`
}

type Polyline_GeoJsonLinestring struct {
	// Specifies a polyline using the [GeoJSON LineString format](https://tools.ietf.org/html/rfc7946#section-3.1.4)
	GeoJsonLinestring *structpb.Struct `protobuf:"bytes,2,opt,name=geo_json_linestring,json=geoJsonLinestring,proto3,oneof"`
}

func (*Polyline_EncodedPolyline) isPolyline_PolylineType() {}

func (*Polyline_GeoJsonLinestring) isPolyline_PolylineType() {}

// TollInfo
// Encapsulates toll information on a `Route` or on a `RouteLeg`.
type TollInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The monetary amount of tolls for the corresponding `Route` or `RouteLeg`.
	// This list contains a money amount for each currency that is expected to be charged by the toll stations.
	// Typically this list will contain only one item for routes with tolls in one currency.
	// For international trips, this list may contain multiple items to reflect tolls in different currencies.
	EstimatedPrice []*money.Money `protobuf:"bytes,1,rep,name=estimated_price,json=estimatedPrice,proto3" json:"estimated_price,omitempty"`
}

func (x *TollInfo) Reset() {
	*x = TollInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TollInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TollInfo) ProtoMessage() {}

func (x *TollInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TollInfo.ProtoReflect.Descriptor instead.
func (*TollInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{6}
}

func (x *TollInfo) GetEstimatedPrice() []*money.Money {
	if x != nil {
		return x.EstimatedPrice
	}
	return nil
}

// TransitPreferences
type TransitPreferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// a set of travel modes to use when getting a TRANSIT route. Defaults to all supported modes of travel.
	TransitMode []TransitTravelMode `protobuf:"varint,1,rep,packed,name=transit_mode,json=transitMode,proto3,enum=moego.models.map.v1.TransitTravelMode" json:"transit_mode,omitempty"`
	// a routing preference that, when specified, influences the TRANSIT route returned.
	RoutingPreference *TransitRoutePreference `protobuf:"varint,2,opt,name=routing_preference,json=routingPreference,proto3,enum=moego.models.map.v1.TransitRoutePreference,oneof" json:"routing_preference,omitempty"`
}

func (x *TransitPreferences) Reset() {
	*x = TransitPreferences{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitPreferences) ProtoMessage() {}

func (x *TransitPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitPreferences.ProtoReflect.Descriptor instead.
func (*TransitPreferences) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{7}
}

func (x *TransitPreferences) GetTransitMode() []TransitTravelMode {
	if x != nil {
		return x.TransitMode
	}
	return nil
}

func (x *TransitPreferences) GetRoutingPreference() TransitRoutePreference {
	if x != nil && x.RoutingPreference != nil {
		return *x.RoutingPreference
	}
	return TransitRoutePreference_TRANSIT_ROUTE_PREFERENCE_UNSPECIFIED
}

// TrafficDensityIndicator
// Traffic density indicator on a contiguous segment of a polyline or path.
// Given a path with points P_0, P_1, ... , P_N (zero-based index),
// the TrafficDensityIndicator defines an interval and describes its traffic using the following categories.
type TrafficDensityIndicator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The starting index of this interval in the polyline.
	StartIndex *int32 `protobuf:"varint,1,opt,name=start_index,json=startIndex,proto3,oneof" json:"start_index,omitempty"`
	// The ending index of this interval in the polyline.
	EndIndex *int32 `protobuf:"varint,2,opt,name=end_index,json=endIndex,proto3,oneof" json:"end_index,omitempty"`
	// SpeedType
	//
	// Types that are assignable to SpeedType:
	//
	//	*TrafficDensityIndicator_Speed
	SpeedType isTrafficDensityIndicator_SpeedType `protobuf_oneof:"speed_type"`
}

func (x *TrafficDensityIndicator) Reset() {
	*x = TrafficDensityIndicator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficDensityIndicator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficDensityIndicator) ProtoMessage() {}

func (x *TrafficDensityIndicator) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficDensityIndicator.ProtoReflect.Descriptor instead.
func (*TrafficDensityIndicator) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{8}
}

func (x *TrafficDensityIndicator) GetStartIndex() int32 {
	if x != nil && x.StartIndex != nil {
		return *x.StartIndex
	}
	return 0
}

func (x *TrafficDensityIndicator) GetEndIndex() int32 {
	if x != nil && x.EndIndex != nil {
		return *x.EndIndex
	}
	return 0
}

func (m *TrafficDensityIndicator) GetSpeedType() isTrafficDensityIndicator_SpeedType {
	if m != nil {
		return m.SpeedType
	}
	return nil
}

func (x *TrafficDensityIndicator) GetSpeed() TrafficCondition {
	if x, ok := x.GetSpeedType().(*TrafficDensityIndicator_Speed); ok {
		return x.Speed
	}
	return TrafficCondition_TRAFFIC_CONDITION_UNSPECIFIED
}

type isTrafficDensityIndicator_SpeedType interface {
	isTrafficDensityIndicator_SpeedType()
}

type TrafficDensityIndicator_Speed struct {
	// Traffic speed in this interval.
	Speed TrafficCondition `protobuf:"varint,3,opt,name=speed,proto3,enum=moego.models.map.v1.TrafficCondition,oneof"`
}

func (*TrafficDensityIndicator_Speed) isTrafficDensityIndicator_SpeedType() {}

// RouteTravelAdvisory
// Contains the additional information that the user should be informed about, such as possible traffic zone restrictions.
type RouteTravelAdvisory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Speed reading intervals detailing traffic density. Applicable in case of
	// `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
	// The intervals cover the entire polyline of the route without overlap.
	// The start point of a specified interval is the same as the end point of the
	// preceding interval.
	//
	// Example:
	//
	//	polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
	//	speed_reading_intervals: [A,C), [C,D), [D,G).
	SpeedReadingIntervals []*TrafficDensityIndicator `protobuf:"bytes,1,rep,name=speed_reading_intervals,json=speedReadingIntervals,proto3" json:"speed_reading_intervals,omitempty"`
	// Contains information about tolls on the route. This field is only populated if tolls are expected on the route.
	// If this field is set, but the estimatedPrice subfield is not populated, then the route contains tolls, but the estimated price is unknown.
	// If this field is not set, then there are no tolls expected on the route.
	TollInfo *TollInfo `protobuf:"bytes,2,opt,name=toll_info,json=tollInfo,proto3,oneof" json:"toll_info,omitempty"`
	// The predicted fuel consumption in microliters.
	FuelConsumption *int64 `protobuf:"varint,3,opt,name=fuel_consumption,json=fuelConsumption,proto3,oneof" json:"fuel_consumption,omitempty"`
	// Returned route may have restrictions that are not suitable for requested travel mode or route modifiers.
	RouteRestrictionsPartiallyIgnored *bool `protobuf:"varint,4,opt,name=route_restrictions_partially_ignored,json=routeRestrictionsPartiallyIgnored,proto3,oneof" json:"route_restrictions_partially_ignored,omitempty"`
	// If present, contains the total fare or ticket costs on this route
	// This property is only returned for `TRANSIT` requests and only for routes where fare information is available for all transit steps.
	TransitFare *money.Money `protobuf:"bytes,5,opt,name=transit_fare,json=transitFare,proto3,oneof" json:"transit_fare,omitempty"`
}

func (x *RouteTravelAdvisory) Reset() {
	*x = RouteTravelAdvisory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteTravelAdvisory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteTravelAdvisory) ProtoMessage() {}

func (x *RouteTravelAdvisory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteTravelAdvisory.ProtoReflect.Descriptor instead.
func (*RouteTravelAdvisory) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{9}
}

func (x *RouteTravelAdvisory) GetSpeedReadingIntervals() []*TrafficDensityIndicator {
	if x != nil {
		return x.SpeedReadingIntervals
	}
	return nil
}

func (x *RouteTravelAdvisory) GetTollInfo() *TollInfo {
	if x != nil {
		return x.TollInfo
	}
	return nil
}

func (x *RouteTravelAdvisory) GetFuelConsumption() int64 {
	if x != nil && x.FuelConsumption != nil {
		return *x.FuelConsumption
	}
	return 0
}

func (x *RouteTravelAdvisory) GetRouteRestrictionsPartiallyIgnored() bool {
	if x != nil && x.RouteRestrictionsPartiallyIgnored != nil {
		return *x.RouteRestrictionsPartiallyIgnored
	}
	return false
}

func (x *RouteTravelAdvisory) GetTransitFare() *money.Money {
	if x != nil {
		return x.TransitFare
	}
	return nil
}

// NavigationInstruction
// Encapsulates navigation instructions for a [RouteLegStep]
type NavigationInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates the navigation instructions for the current step (e.g., turn left, merge, straight, etc.).
	// This field determines which icon to display.
	// see: https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRoutes#maneuver
	Maneuver *string `protobuf:"bytes,1,opt,name=maneuver,proto3,oneof" json:"maneuver,omitempty"`
	// Instructions for navigating this step.
	Instruction *string `protobuf:"bytes,2,opt,name=instruction,proto3,oneof" json:"instruction,omitempty"`
}

func (x *NavigationInstruction) Reset() {
	*x = NavigationInstruction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NavigationInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NavigationInstruction) ProtoMessage() {}

func (x *NavigationInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NavigationInstruction.ProtoReflect.Descriptor instead.
func (*NavigationInstruction) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{10}
}

func (x *NavigationInstruction) GetManeuver() string {
	if x != nil && x.Maneuver != nil {
		return *x.Maneuver
	}
	return ""
}

func (x *NavigationInstruction) GetInstruction() string {
	if x != nil && x.Instruction != nil {
		return *x.Instruction
	}
	return ""
}

// StepsOverview
// Provides overview information about a list of `RouteLegStep`s.
type StepsOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Summarized information about different multi-modal segments of the `RouteLeg.steps`.
	// This field is not populated if the `RouteLeg` does not contain any multi-modal segments in the steps.
	MultiModalSegments []*MultiModalSegment `protobuf:"bytes,1,rep,name=multi_modal_segments,json=multiModalSegments,proto3" json:"multi_modal_segments,omitempty"`
}

func (x *StepsOverview) Reset() {
	*x = StepsOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StepsOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StepsOverview) ProtoMessage() {}

func (x *StepsOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StepsOverview.ProtoReflect.Descriptor instead.
func (*StepsOverview) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{11}
}

func (x *StepsOverview) GetMultiModalSegments() []*MultiModalSegment {
	if x != nil {
		return x.MultiModalSegments
	}
	return nil
}

// MultiModalSegment
// Provides summarized information about different multi-modal segments of the `RouteLeg.steps`.
// A multi-modal segment is defined as one or more contiguous `RouteLegStep` that have the same `TravelMode`.
// This field is not populated if the `RouteLeg` does not contain any multi-modal segments in the steps.
type MultiModalSegment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The corresponding `RouteLegStep` index that is the start of a multi-modal segment.
	StartIndex *int32 `protobuf:"varint,1,opt,name=start_index,json=startIndex,proto3,oneof" json:"start_index,omitempty"`
	// The corresponding `RouteLegStep` index that is the end of a multi-modal segment.
	EndIndex *int32 `protobuf:"varint,2,opt,name=end_index,json=endIndex,proto3,oneof" json:"end_index,omitempty"`
	// NavigationInstruction for the multi-modal segment.
	NavigationInstruction *NavigationInstruction `protobuf:"bytes,3,opt,name=navigation_instruction,json=navigationInstruction,proto3,oneof" json:"navigation_instruction,omitempty"`
	// The travel mode of the multi-modal segment.
	TravelMode *TravelMode `protobuf:"varint,4,opt,name=travel_mode,json=travelMode,proto3,enum=moego.models.map.v1.TravelMode,oneof" json:"travel_mode,omitempty"`
}

func (x *MultiModalSegment) Reset() {
	*x = MultiModalSegment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiModalSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiModalSegment) ProtoMessage() {}

func (x *MultiModalSegment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiModalSegment.ProtoReflect.Descriptor instead.
func (*MultiModalSegment) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{12}
}

func (x *MultiModalSegment) GetStartIndex() int32 {
	if x != nil && x.StartIndex != nil {
		return *x.StartIndex
	}
	return 0
}

func (x *MultiModalSegment) GetEndIndex() int32 {
	if x != nil && x.EndIndex != nil {
		return *x.EndIndex
	}
	return 0
}

func (x *MultiModalSegment) GetNavigationInstruction() *NavigationInstruction {
	if x != nil {
		return x.NavigationInstruction
	}
	return nil
}

func (x *MultiModalSegment) GetTravelMode() TravelMode {
	if x != nil && x.TravelMode != nil {
		return *x.TravelMode
	}
	return TravelMode_TRAVEL_MODE_UNSPECIFIED
}

// RouteLegStepTransitDetails
// Additional information for the `RouteLegStep` related to `TRANSIT` routes.
type RouteLegStepTransitDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the direction in which to travel on this line as marked on the vehicle or at the departure stop.
	// The direction is often the terminus station.
	Headsign *string `protobuf:"bytes,1,opt,name=headsign,proto3,oneof" json:"headsign,omitempty"`
	// Specifies the expected time as a duration between departures from the same stop at this time.
	// For example, with a headway seconds value of 600, you would expect a ten minute wait if you should miss your bus.
	Headway *durationpb.Duration `protobuf:"bytes,2,opt,name=headway,proto3,oneof" json:"headway,omitempty"`
	// The number of stops from the departure to the arrival stop. This count
	// includes the arrival stop, but excludes the departure stop. For example, if
	// your route leaves from Stop A, passes through stops B and C, and arrives at
	// stop D, stop_count will return 3.
	StopCount *int32 `protobuf:"varint,3,opt,name=stop_count,json=stopCount,proto3,oneof" json:"stop_count,omitempty"`
	// The text that appears in schedules and sign boards to identify a transit
	// trip to passengers. The text should uniquely identify a trip within a
	// service day. For example, "538" is the `trip_short_text` of the Amtrak
	// train that leaves San Jose, CA at 15:10 on weekdays to Sacramento, CA.
	TripName *string `protobuf:"bytes,4,opt,name=trip_name,json=tripName,proto3,oneof" json:"trip_name,omitempty"`
	// Information about the arrival and departure stops for the step.
	TransitStop *TransitStopDetails `protobuf:"bytes,5,opt,name=transit_stop,json=transitStop,proto3,oneof" json:"transit_stop,omitempty"`
	// Information about the transit line used in this step.
	TransitLine *TransitLine `protobuf:"bytes,6,opt,name=transit_line,json=transitLine,proto3,oneof" json:"transit_line,omitempty"`
	// Text representations of properties of the `RouteLegStepTransitDetails`.
	LocalizedValues *RouteLegStepTransitDetails_LocalizedValuesType `protobuf:"bytes,7,opt,name=localized_values,json=localizedValues,proto3,oneof" json:"localized_values,omitempty"`
}

func (x *RouteLegStepTransitDetails) Reset() {
	*x = RouteLegStepTransitDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegStepTransitDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegStepTransitDetails) ProtoMessage() {}

func (x *RouteLegStepTransitDetails) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegStepTransitDetails.ProtoReflect.Descriptor instead.
func (*RouteLegStepTransitDetails) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{13}
}

func (x *RouteLegStepTransitDetails) GetHeadsign() string {
	if x != nil && x.Headsign != nil {
		return *x.Headsign
	}
	return ""
}

func (x *RouteLegStepTransitDetails) GetHeadway() *durationpb.Duration {
	if x != nil {
		return x.Headway
	}
	return nil
}

func (x *RouteLegStepTransitDetails) GetStopCount() int32 {
	if x != nil && x.StopCount != nil {
		return *x.StopCount
	}
	return 0
}

func (x *RouteLegStepTransitDetails) GetTripName() string {
	if x != nil && x.TripName != nil {
		return *x.TripName
	}
	return ""
}

func (x *RouteLegStepTransitDetails) GetTransitStop() *TransitStopDetails {
	if x != nil {
		return x.TransitStop
	}
	return nil
}

func (x *RouteLegStepTransitDetails) GetTransitLine() *TransitLine {
	if x != nil {
		return x.TransitLine
	}
	return nil
}

func (x *RouteLegStepTransitDetails) GetLocalizedValues() *RouteLegStepTransitDetails_LocalizedValuesType {
	if x != nil {
		return x.LocalizedValues
	}
	return nil
}

// TransitStopDetails
// Details about the transit stops for the `RouteLegStep`
type TransitStopDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The estimated time of departure for the step.
	DepartureTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=departure_time,json=departureTime,proto3,oneof" json:"departure_time,omitempty"`
	// Information about the departure stop for the step.
	DepartureStop *TransitStop `protobuf:"bytes,2,opt,name=departure_stop,json=departureStop,proto3,oneof" json:"departure_stop,omitempty"`
	// The estimated time of arrival for the step.
	ArrivalTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=arrival_time,json=arrivalTime,proto3,oneof" json:"arrival_time,omitempty"`
	// Information about the arrival stop for the step.
	ArrivalStop *TransitStop `protobuf:"bytes,4,opt,name=arrival_stop,json=arrivalStop,proto3,oneof" json:"arrival_stop,omitempty"`
}

func (x *TransitStopDetails) Reset() {
	*x = TransitStopDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitStopDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitStopDetails) ProtoMessage() {}

func (x *TransitStopDetails) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitStopDetails.ProtoReflect.Descriptor instead.
func (*TransitStopDetails) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{14}
}

func (x *TransitStopDetails) GetDepartureTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DepartureTime
	}
	return nil
}

func (x *TransitStopDetails) GetDepartureStop() *TransitStop {
	if x != nil {
		return x.DepartureStop
	}
	return nil
}

func (x *TransitStopDetails) GetArrivalTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ArrivalTime
	}
	return nil
}

func (x *TransitStopDetails) GetArrivalStop() *TransitStop {
	if x != nil {
		return x.ArrivalStop
	}
	return nil
}

// TransitStop
// Information about a transit stop.
type TransitStop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the transit stop.
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// The location of the stop expressed in latitude/longitude coordinates.
	Coordinate *latlng.LatLng `protobuf:"bytes,2,opt,name=coordinate,proto3,oneof" json:"coordinate,omitempty"`
}

func (x *TransitStop) Reset() {
	*x = TransitStop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitStop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitStop) ProtoMessage() {}

func (x *TransitStop) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitStop.ProtoReflect.Descriptor instead.
func (*TransitStop) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{15}
}

func (x *TransitStop) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransitStop) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

// TransitLine
// Contains information about the transit line used in this step.
type TransitLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The full name of this transit line, For example, "8 Avenue Local".
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// The short name of this transit line. This name will normally be a line
	// number, such as "M7" or "355".
	ShortName *string `protobuf:"bytes,2,opt,name=short_name,json=shortName,proto3,oneof" json:"short_name,omitempty"`
	// the URI for this transit line as provided by the transit agency.
	Uri *string `protobuf:"bytes,3,opt,name=uri,proto3,oneof" json:"uri,omitempty"`
	// The URI for the icon associated with this line.
	IconUri *string `protobuf:"bytes,4,opt,name=icon_uri,json=iconUri,proto3,oneof" json:"icon_uri,omitempty"`
	// The color commonly used in signage for this line. Represented in
	// hexadecimal.
	Color *string `protobuf:"bytes,5,opt,name=color,proto3,oneof" json:"color,omitempty"`
	// The color commonly used in text on signage for this line. Represented in
	// hexadecimal.
	TextColor *string `protobuf:"bytes,6,opt,name=text_color,json=textColor,proto3,oneof" json:"text_color,omitempty"`
	// The transit agency (or agencies) that operates this transit line.
	Agency []*TransitAgency `protobuf:"bytes,7,rep,name=agency,proto3" json:"agency,omitempty"`
	// The type of vehicle that operates on this transit line.
	Vehicle *TransitVehicle `protobuf:"bytes,8,opt,name=vehicle,proto3,oneof" json:"vehicle,omitempty"`
}

func (x *TransitLine) Reset() {
	*x = TransitLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitLine) ProtoMessage() {}

func (x *TransitLine) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitLine.ProtoReflect.Descriptor instead.
func (*TransitLine) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{16}
}

func (x *TransitLine) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransitLine) GetShortName() string {
	if x != nil && x.ShortName != nil {
		return *x.ShortName
	}
	return ""
}

func (x *TransitLine) GetUri() string {
	if x != nil && x.Uri != nil {
		return *x.Uri
	}
	return ""
}

func (x *TransitLine) GetIconUri() string {
	if x != nil && x.IconUri != nil {
		return *x.IconUri
	}
	return ""
}

func (x *TransitLine) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

func (x *TransitLine) GetTextColor() string {
	if x != nil && x.TextColor != nil {
		return *x.TextColor
	}
	return ""
}

func (x *TransitLine) GetAgency() []*TransitAgency {
	if x != nil {
		return x.Agency
	}
	return nil
}

func (x *TransitLine) GetVehicle() *TransitVehicle {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

// TransitAgency
// A transit agency that operates a transit line.
type TransitAgency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of this transit agency.
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// The transit agency's URI.
	Uri *string `protobuf:"bytes,2,opt,name=uri,proto3,oneof" json:"uri,omitempty"`
	// The transit agency's locale-specific formatted phone number.
	PhoneNumber *string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
}

func (x *TransitAgency) Reset() {
	*x = TransitAgency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitAgency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitAgency) ProtoMessage() {}

func (x *TransitAgency) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitAgency.ProtoReflect.Descriptor instead.
func (*TransitAgency) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{17}
}

func (x *TransitAgency) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransitAgency) GetUri() string {
	if x != nil && x.Uri != nil {
		return *x.Uri
	}
	return ""
}

func (x *TransitAgency) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

// TransitVehicle
// Information about a vehicle used in transit routes.
type TransitVehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of this vehicle, capitalized.
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// The type of vehicle used.
	// see https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRoutes#transitvehicletype
	Type *string `protobuf:"bytes,2,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// The URI for an icon associated with this vehicle type.
	IconUri *string `protobuf:"bytes,3,opt,name=icon_uri,json=iconUri,proto3,oneof" json:"icon_uri,omitempty"`
	// The URI for the icon associated with this vehicle type, based on the local transport signage.
	LocalIconUri *string `protobuf:"bytes,4,opt,name=local_icon_uri,json=localIconUri,proto3,oneof" json:"local_icon_uri,omitempty"`
}

func (x *TransitVehicle) Reset() {
	*x = TransitVehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransitVehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransitVehicle) ProtoMessage() {}

func (x *TransitVehicle) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransitVehicle.ProtoReflect.Descriptor instead.
func (*TransitVehicle) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{18}
}

func (x *TransitVehicle) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *TransitVehicle) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *TransitVehicle) GetIconUri() string {
	if x != nil && x.IconUri != nil {
		return *x.IconUri
	}
	return ""
}

func (x *TransitVehicle) GetLocalIconUri() string {
	if x != nil && x.LocalIconUri != nil {
		return *x.LocalIconUri
	}
	return ""
}

// RouteMatrixElement
type RouteMatrixElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Error status code for this element.
	Status *status.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Zero-based index of the origin in the request.
	OriginIndex *int32 `protobuf:"varint,2,opt,name=origin_index,json=originIndex,proto3,oneof" json:"origin_index,omitempty"`
	// Zero-based index of the destination in the request.
	DestinationIndex *int32 `protobuf:"varint,3,opt,name=destination_index,json=destinationIndex,proto3,oneof" json:"destination_index,omitempty"`
	// The travel distance of the route, in meters.
	Distance *int32 `protobuf:"varint,4,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	// The duration from origin to destination
	Duration *durationpb.Duration `protobuf:"bytes,5,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Additional information about the route. For example: restriction information and toll information
	TravelAdvisory *RouteTravelAdvisory `protobuf:"bytes,6,opt,name=travel_advisory,json=travelAdvisory,proto3,oneof" json:"travel_advisory,omitempty"`
	// In some cases when the server is not able to compute the route with the given preferences for this particular origin/destination pair,
	// it may fall back to using a different mode of computation. When fallback mode is used, this field contains detailed information about the fallback response.
	// Otherwise this field is unset.
	FallbackInfo *FallbackInfo `protobuf:"bytes,7,opt,name=fallback_info,json=fallbackInfo,proto3,oneof" json:"fallback_info,omitempty"`
	// Text representations of properties of the `RouteMatrixElement`.
	LocalizedValues *LocalizedValues `protobuf:"bytes,8,opt,name=localized_values,json=localizedValues,proto3,oneof" json:"localized_values,omitempty"`
}

func (x *RouteMatrixElement) Reset() {
	*x = RouteMatrixElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteMatrixElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteMatrixElement) ProtoMessage() {}

func (x *RouteMatrixElement) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteMatrixElement.ProtoReflect.Descriptor instead.
func (*RouteMatrixElement) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{19}
}

func (x *RouteMatrixElement) GetStatus() *status.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RouteMatrixElement) GetOriginIndex() int32 {
	if x != nil && x.OriginIndex != nil {
		return *x.OriginIndex
	}
	return 0
}

func (x *RouteMatrixElement) GetDestinationIndex() int32 {
	if x != nil && x.DestinationIndex != nil {
		return *x.DestinationIndex
	}
	return 0
}

func (x *RouteMatrixElement) GetDistance() int32 {
	if x != nil && x.Distance != nil {
		return *x.Distance
	}
	return 0
}

func (x *RouteMatrixElement) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *RouteMatrixElement) GetTravelAdvisory() *RouteTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

func (x *RouteMatrixElement) GetFallbackInfo() *FallbackInfo {
	if x != nil {
		return x.FallbackInfo
	}
	return nil
}

func (x *RouteMatrixElement) GetLocalizedValues() *LocalizedValues {
	if x != nil {
		return x.LocalizedValues
	}
	return nil
}

// FallbackInfo
// Information related to how and why a fallback result was used.
// If this field is set, then it means the server used a different routing mode from your preferred mode as fallback.
type FallbackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Routing mode used for the response.
	// If fallback was triggered, the mode may be different from routing preference set in the original client request.
	RoutingMode *FallbackRoutingMode `protobuf:"varint,1,opt,name=routing_mode,json=routingMode,proto3,enum=moego.models.map.v1.FallbackRoutingMode,oneof" json:"routing_mode,omitempty"`
	// The reason why fallback response was used instead of the original response.
	// This field is only populated when the fallback mode is triggered and the fallback response is returned.
	Reason *FallbackReason `protobuf:"varint,2,opt,name=reason,proto3,enum=moego.models.map.v1.FallbackReason,oneof" json:"reason,omitempty"`
}

func (x *FallbackInfo) Reset() {
	*x = FallbackInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FallbackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FallbackInfo) ProtoMessage() {}

func (x *FallbackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FallbackInfo.ProtoReflect.Descriptor instead.
func (*FallbackInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{20}
}

func (x *FallbackInfo) GetRoutingMode() FallbackRoutingMode {
	if x != nil && x.RoutingMode != nil {
		return *x.RoutingMode
	}
	return FallbackRoutingMode_FALLBACK_ROUTING_MODE_UNSPECIFIED
}

func (x *FallbackInfo) GetReason() FallbackReason {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return FallbackReason_FALLBACK_REASON_UNSPECIFIED
}

// LocalizedValues
// Text representations of certain properties.
type LocalizedValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Travel distance represented in text form.
	Distance *localized_text.LocalizedText `protobuf:"bytes,1,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	// Duration represented in text form taking traffic conditions into consideration.
	// Note: If traffic information was not requested, this value is the same value as static_duration.
	Duration *localized_text.LocalizedText `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Transit fare represented in text form.
	TransitFare *localized_text.LocalizedText `protobuf:"bytes,3,opt,name=transit_fare,json=transitFare,proto3,oneof" json:"transit_fare,omitempty"`
}

func (x *LocalizedValues) Reset() {
	*x = LocalizedValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalizedValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalizedValues) ProtoMessage() {}

func (x *LocalizedValues) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalizedValues.ProtoReflect.Descriptor instead.
func (*LocalizedValues) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{21}
}

func (x *LocalizedValues) GetDistance() *localized_text.LocalizedText {
	if x != nil {
		return x.Distance
	}
	return nil
}

func (x *LocalizedValues) GetDuration() *localized_text.LocalizedText {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *LocalizedValues) GetTransitFare() *localized_text.LocalizedText {
	if x != nil {
		return x.TransitFare
	}
	return nil
}

// LocalizedTime
// Localized description of time.
type LocalizedTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The time specified as a string in a given time zone.
	Time *localized_text.LocalizedText `protobuf:"bytes,1,opt,name=time,proto3,oneof" json:"time,omitempty"`
	// Contains the time zone.
	// The value is the name of the time zone as defined in the [IANA Time Zone Database](http://www.iana.org/time-zones),
	// e.g. "America/New_York".
	TimeZone *string `protobuf:"bytes,2,opt,name=time_zone,json=timeZone,proto3,oneof" json:"time_zone,omitempty"`
}

func (x *LocalizedTime) Reset() {
	*x = LocalizedTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalizedTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalizedTime) ProtoMessage() {}

func (x *LocalizedTime) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalizedTime.ProtoReflect.Descriptor instead.
func (*LocalizedTime) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{22}
}

func (x *LocalizedTime) GetTime() *localized_text.LocalizedText {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *LocalizedTime) GetTimeZone() string {
	if x != nil && x.TimeZone != nil {
		return *x.TimeZone
	}
	return ""
}

// Localized descriptions of values for RouteTransitDetails.
type RouteLegStepTransitDetails_LocalizedValuesType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Time in its formatted text representation with a corresponding time zone.
	ArrivalTime *LocalizedTime `protobuf:"bytes,1,opt,name=arrival_time,json=arrivalTime,proto3,oneof" json:"arrival_time,omitempty"`
	// Time in its formatted text representation with a corresponding time zone.
	DepartureTime *LocalizedTime `protobuf:"bytes,2,opt,name=departure_time,json=departureTime,proto3,oneof" json:"departure_time,omitempty"`
}

func (x *RouteLegStepTransitDetails_LocalizedValuesType) Reset() {
	*x = RouteLegStepTransitDetails_LocalizedValuesType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_route_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegStepTransitDetails_LocalizedValuesType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegStepTransitDetails_LocalizedValuesType) ProtoMessage() {}

func (x *RouteLegStepTransitDetails_LocalizedValuesType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_route_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegStepTransitDetails_LocalizedValuesType.ProtoReflect.Descriptor instead.
func (*RouteLegStepTransitDetails_LocalizedValuesType) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_route_models_proto_rawDescGZIP(), []int{13, 0}
}

func (x *RouteLegStepTransitDetails_LocalizedValuesType) GetArrivalTime() *LocalizedTime {
	if x != nil {
		return x.ArrivalTime
	}
	return nil
}

func (x *RouteLegStepTransitDetails_LocalizedValuesType) GetDepartureTime() *LocalizedTime {
	if x != nil {
		return x.DepartureTime
	}
	return nil
}

var File_moego_models_map_v1_route_models_proto protoreflect.FileDescriptor

var file_moego_models_map_v1_route_models_proto_rawDesc = []byte{
	0x0a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x61, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x02, 0x0a, 0x08, 0x57, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x00, 0x52,
	0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x15, 0x0a, 0x03, 0x76, 0x69, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x01, 0x52, 0x03, 0x76, 0x69, 0x61, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x6f, 0x76, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x74, 0x6f, 0x70, 0x6f, 0x76, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c, 0x73,
	0x69, 0x64, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x6f, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x03, 0x52, 0x0a, 0x73, 0x69, 0x64, 0x65, 0x4f, 0x66, 0x52, 0x6f, 0x61, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x76, 0x69, 0x61, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x6f, 0x76, 0x65, 0x72,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x6f, 0x61,
	0x64, 0x22, 0x94, 0x06, 0x0a, 0x05, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52,
	0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x48, 0x02, 0x52, 0x08, 0x70, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x65, 0x67, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c,
	0x65, 0x67, 0x52, 0x04, 0x6c, 0x65, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x76,
	0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x56, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x04, 0x52, 0x08, 0x76, 0x69, 0x65, 0x77,
	0x70, 0x6f, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x76, 0x65,
	0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72, 0x61, 0x76,
	0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x48, 0x05, 0x52, 0x0e, 0x74, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x24, 0x0a, 0x0b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0a, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x48, 0x07, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73,
	0x6f, 0x72, 0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xe3, 0x05, 0x0a, 0x08, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x4c, 0x65, 0x67, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c,
	0x69, 0x6e, 0x65, 0x48, 0x02, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x3f, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48,
	0x03, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x04,
	0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74,
	0x65, 0x70, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x56, 0x0a, 0x0f, 0x74, 0x72, 0x61,
	0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x48, 0x05, 0x52, 0x0e,
	0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x74, 0x65, 0x70, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x65, 0x70, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x48, 0x06, 0x52,
	0x0d, 0x73, 0x74, 0x65, 0x70, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x88, 0x01,
	0x01, 0x12, 0x54, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x48, 0x07, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64,
	0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x98,
	0x07, 0x0a, 0x0c, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x12,
	0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x3a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x01, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08,
	0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x48, 0x02, 0x52,
	0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x03, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a,
	0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x04, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x48, 0x05, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x66, 0x0a, 0x16, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x06, 0x52,
	0x15, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x0f, 0x74, 0x72, 0x61,
	0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x48, 0x07, 0x52, 0x0e,
	0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x5d, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x08, 0x52, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x54, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x48, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x81, 0x03, 0x0a, 0x0e, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0b,
	0x61, 0x76, 0x6f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x54, 0x6f, 0x6c, 0x6c, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x5f, 0x68, 0x69, 0x67, 0x68,
	0x77, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x76,
	0x6f, 0x69, 0x64, 0x48, 0x69, 0x67, 0x68, 0x77, 0x61, 0x79, 0x73, 0x88, 0x01, 0x01, 0x12, 0x28,
	0x0a, 0x0d, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x5f, 0x66, 0x65, 0x72, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0c, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x46, 0x65,
	0x72, 0x72, 0x69, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x61, 0x76, 0x6f, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03,
	0x52, 0x0b, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x49, 0x6e, 0x64, 0x6f, 0x6f, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x52, 0x0a, 0x0d, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x04, 0x52, 0x0c, 0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x6c, 0x6c, 0x50,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x5f,
	0x74, 0x6f, 0x6c, 0x6c, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x76, 0x6f, 0x69, 0x64, 0x5f,
	0x68, 0x69, 0x67, 0x68, 0x77, 0x61, 0x79, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61, 0x76, 0x6f,
	0x69, 0x64, 0x5f, 0x66, 0x65, 0x72, 0x72, 0x69, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61,
	0x76, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x6f, 0x6f, 0x72, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x65, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x93, 0x01,
	0x0a, 0x08, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2b, 0x0a, 0x10, 0x65, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x50,
	0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x49, 0x0a, 0x13, 0x67, 0x65, 0x6f, 0x5f, 0x6a,
	0x73, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x00, 0x52,
	0x11, 0x67, 0x65, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x42, 0x0f, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x47, 0x0a, 0x08, 0x54, 0x6f, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3b, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0xd7, 0x01, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x5f,
	0x0a, 0x12, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x11, 0x72, 0x6f, 0x75, 0x74, 0x69,
	0x6e, 0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x15, 0x0a, 0x13, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xcc, 0x01, 0x0a, 0x17, 0x54, 0x72, 0x61, 0x66, 0x66,
	0x69, 0x63, 0x44, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x08, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x05, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x00, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x73, 0x70, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xdb, 0x03, 0x0a, 0x13, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54,
	0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x12, 0x64, 0x0a,
	0x17, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x44, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x15, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x74, 0x6f, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6c,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x74, 0x6f, 0x6c, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x0f, 0x66, 0x75, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x24, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x72, 0x65,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x02, 0x52, 0x21, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x6c, 0x79,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x0c, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x66, 0x61, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x48, 0x03, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x46,
	0x61, 0x72, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x6f, 0x6c, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x27, 0x0a, 0x25, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x66,
	0x61, 0x72, 0x65, 0x22, 0x7c, 0x0a, 0x15, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x08,
	0x6d, 0x61, 0x6e, 0x65, 0x75, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x08, 0x6d, 0x61, 0x6e, 0x65, 0x75, 0x76, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6d, 0x61, 0x6e, 0x65, 0x75, 0x76, 0x65,
	0x72, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x69, 0x0a, 0x0d, 0x53, 0x74, 0x65, 0x70, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x58, 0x0a, 0x14, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x61,
	0x6c, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4d, 0x6f, 0x64, 0x61,
	0x6c, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x12, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x4d,
	0x6f, 0x64, 0x61, 0x6c, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xd3, 0x02, 0x0a,
	0x11, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x08, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x88, 0x01, 0x01, 0x12, 0x66, 0x0a, 0x16, 0x6e, 0x61,
	0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x02, 0x52, 0x15, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x45, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x61, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x48, 0x03, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x76,
	0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6e, 0x61, 0x76, 0x69,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x22, 0x92, 0x06, 0x0a, 0x1a, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53,
	0x74, 0x65, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x1f, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x73, 0x69, 0x67, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x01,
	0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x77, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a,
	0x73, 0x74, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x02, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x09, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x08, 0x74, 0x72, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x4f, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x73, 0x74,
	0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x48, 0x04, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70,
	0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x48, 0x05, 0x52, 0x0b, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x73, 0x0a,
	0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x48, 0x06, 0x52, 0x0f,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x88,
	0x01, 0x01, 0x1a, 0xd5, 0x01, 0x0a, 0x13, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x61, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x48, 0x01, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x73, 0x69, 0x67, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x77, 0x61, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x6f,
	0x70, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x80, 0x03, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x46,
	0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4c, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70,
	0x48, 0x01, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x6f,
	0x70, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70,
	0x48, 0x03, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x6f, 0x70, 0x88,
	0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x6f, 0x70, 0x22, 0x78, 0x0a, 0x0b, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x53, 0x74, 0x6f, 0x70, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x01, 0x52, 0x0a, 0x63,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x22, 0x92, 0x03, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74,
	0x4c, 0x69, 0x6e, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a,
	0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02,
	0x52, 0x03, 0x75, 0x72, 0x69, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x07, 0x69, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x69, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x06, 0x61, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x42, 0x0a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x69, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x48, 0x06, 0x52, 0x07, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42,
	0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72, 0x69, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x69, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0d, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x69, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x03, 0x75, 0x72, 0x69, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x06, 0x0a, 0x04,
	0x5f, 0x75, 0x72, 0x69, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xbf, 0x01, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x69, 0x63,
	0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x69, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x55,
	0x72, 0x69, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x69, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x69, 0x22, 0xee, 0x04, 0x0a, 0x12, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2a,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x88,
	0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52,
	0x10, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x03, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x56, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69,
	0x73, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69,
	0x73, 0x6f, 0x72, 0x79, 0x48, 0x04, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64,
	0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0d, 0x66, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x05, 0x52, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x48, 0x06, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72,
	0x79, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x0c, 0x46, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0c, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x6f, 0x75,
	0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x48, 0x01, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xfa, 0x01, 0x0a, 0x0f, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x3b, 0x0a,
	0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x69, 0x74, 0x5f, 0x66, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x48, 0x02, 0x52, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x69, 0x74, 0x46, 0x61, 0x72, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x5f, 0x66, 0x61, 0x72, 0x65, 0x22, 0x7d, 0x0a, 0x0d, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x65, 0x78,
	0x74, 0x48, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x42, 0x6f, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31,
	0x3b, 0x6d, 0x61, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_map_v1_route_models_proto_rawDescOnce sync.Once
	file_moego_models_map_v1_route_models_proto_rawDescData = file_moego_models_map_v1_route_models_proto_rawDesc
)

func file_moego_models_map_v1_route_models_proto_rawDescGZIP() []byte {
	file_moego_models_map_v1_route_models_proto_rawDescOnce.Do(func() {
		file_moego_models_map_v1_route_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_map_v1_route_models_proto_rawDescData)
	})
	return file_moego_models_map_v1_route_models_proto_rawDescData
}

var file_moego_models_map_v1_route_models_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_models_map_v1_route_models_proto_goTypes = []interface{}{
	(*Waypoint)(nil),                   // 0: moego.models.map.v1.Waypoint
	(*Route)(nil),                      // 1: moego.models.map.v1.Route
	(*RouteLeg)(nil),                   // 2: moego.models.map.v1.RouteLeg
	(*RouteLegStep)(nil),               // 3: moego.models.map.v1.RouteLegStep
	(*RouteModifiers)(nil),             // 4: moego.models.map.v1.RouteModifiers
	(*Polyline)(nil),                   // 5: moego.models.map.v1.Polyline
	(*TollInfo)(nil),                   // 6: moego.models.map.v1.TollInfo
	(*TransitPreferences)(nil),         // 7: moego.models.map.v1.TransitPreferences
	(*TrafficDensityIndicator)(nil),    // 8: moego.models.map.v1.TrafficDensityIndicator
	(*RouteTravelAdvisory)(nil),        // 9: moego.models.map.v1.RouteTravelAdvisory
	(*NavigationInstruction)(nil),      // 10: moego.models.map.v1.NavigationInstruction
	(*StepsOverview)(nil),              // 11: moego.models.map.v1.StepsOverview
	(*MultiModalSegment)(nil),          // 12: moego.models.map.v1.MultiModalSegment
	(*RouteLegStepTransitDetails)(nil), // 13: moego.models.map.v1.RouteLegStepTransitDetails
	(*TransitStopDetails)(nil),         // 14: moego.models.map.v1.TransitStopDetails
	(*TransitStop)(nil),                // 15: moego.models.map.v1.TransitStop
	(*TransitLine)(nil),                // 16: moego.models.map.v1.TransitLine
	(*TransitAgency)(nil),              // 17: moego.models.map.v1.TransitAgency
	(*TransitVehicle)(nil),             // 18: moego.models.map.v1.TransitVehicle
	(*RouteMatrixElement)(nil),         // 19: moego.models.map.v1.RouteMatrixElement
	(*FallbackInfo)(nil),               // 20: moego.models.map.v1.FallbackInfo
	(*LocalizedValues)(nil),            // 21: moego.models.map.v1.LocalizedValues
	(*LocalizedTime)(nil),              // 22: moego.models.map.v1.LocalizedTime
	(*RouteLegStepTransitDetails_LocalizedValuesType)(nil), // 23: moego.models.map.v1.RouteLegStepTransitDetails.LocalizedValuesType
	(*latlng.LatLng)(nil),                // 24: google.type.LatLng
	(*AddressSource)(nil),                // 25: moego.models.map.v1.AddressSource
	(*durationpb.Duration)(nil),          // 26: google.protobuf.Duration
	(RouteLabel)(0),                      // 27: moego.models.map.v1.RouteLabel
	(*viewport.Viewport)(nil),            // 28: google.geo.type.Viewport
	(TravelMode)(0),                      // 29: moego.models.map.v1.TravelMode
	(VehicleEmissionType)(0),             // 30: moego.models.map.v1.VehicleEmissionType
	(*structpb.Struct)(nil),              // 31: google.protobuf.Struct
	(*money.Money)(nil),                  // 32: google.type.Money
	(TransitTravelMode)(0),               // 33: moego.models.map.v1.TransitTravelMode
	(TransitRoutePreference)(0),          // 34: moego.models.map.v1.TransitRoutePreference
	(TrafficCondition)(0),                // 35: moego.models.map.v1.TrafficCondition
	(*timestamppb.Timestamp)(nil),        // 36: google.protobuf.Timestamp
	(*status.Status)(nil),                // 37: google.rpc.Status
	(FallbackRoutingMode)(0),             // 38: moego.models.map.v1.FallbackRoutingMode
	(FallbackReason)(0),                  // 39: moego.models.map.v1.FallbackReason
	(*localized_text.LocalizedText)(nil), // 40: google.type.LocalizedText
}
var file_moego_models_map_v1_route_models_proto_depIdxs = []int32{
	24, // 0: moego.models.map.v1.Waypoint.coordinate:type_name -> google.type.LatLng
	25, // 1: moego.models.map.v1.Waypoint.address_source:type_name -> moego.models.map.v1.AddressSource
	26, // 2: moego.models.map.v1.Route.duration:type_name -> google.protobuf.Duration
	5,  // 3: moego.models.map.v1.Route.polyline:type_name -> moego.models.map.v1.Polyline
	27, // 4: moego.models.map.v1.Route.labels:type_name -> moego.models.map.v1.RouteLabel
	2,  // 5: moego.models.map.v1.Route.legs:type_name -> moego.models.map.v1.RouteLeg
	28, // 6: moego.models.map.v1.Route.viewport:type_name -> google.geo.type.Viewport
	9,  // 7: moego.models.map.v1.Route.travel_advisory:type_name -> moego.models.map.v1.RouteTravelAdvisory
	21, // 8: moego.models.map.v1.Route.localized_values:type_name -> moego.models.map.v1.LocalizedValues
	26, // 9: moego.models.map.v1.RouteLeg.duration:type_name -> google.protobuf.Duration
	5,  // 10: moego.models.map.v1.RouteLeg.polyline:type_name -> moego.models.map.v1.Polyline
	24, // 11: moego.models.map.v1.RouteLeg.start_location:type_name -> google.type.LatLng
	24, // 12: moego.models.map.v1.RouteLeg.end_location:type_name -> google.type.LatLng
	3,  // 13: moego.models.map.v1.RouteLeg.steps:type_name -> moego.models.map.v1.RouteLegStep
	9,  // 14: moego.models.map.v1.RouteLeg.travel_advisory:type_name -> moego.models.map.v1.RouteTravelAdvisory
	11, // 15: moego.models.map.v1.RouteLeg.steps_overview:type_name -> moego.models.map.v1.StepsOverview
	21, // 16: moego.models.map.v1.RouteLeg.localized_values:type_name -> moego.models.map.v1.LocalizedValues
	26, // 17: moego.models.map.v1.RouteLegStep.duration:type_name -> google.protobuf.Duration
	5,  // 18: moego.models.map.v1.RouteLegStep.polyline:type_name -> moego.models.map.v1.Polyline
	24, // 19: moego.models.map.v1.RouteLegStep.start_location:type_name -> google.type.LatLng
	24, // 20: moego.models.map.v1.RouteLegStep.end_location:type_name -> google.type.LatLng
	29, // 21: moego.models.map.v1.RouteLegStep.travel_mode:type_name -> moego.models.map.v1.TravelMode
	10, // 22: moego.models.map.v1.RouteLegStep.navigation_instruction:type_name -> moego.models.map.v1.NavigationInstruction
	9,  // 23: moego.models.map.v1.RouteLegStep.travel_advisory:type_name -> moego.models.map.v1.RouteTravelAdvisory
	13, // 24: moego.models.map.v1.RouteLegStep.transit_details:type_name -> moego.models.map.v1.RouteLegStepTransitDetails
	21, // 25: moego.models.map.v1.RouteLegStep.localized_values:type_name -> moego.models.map.v1.LocalizedValues
	30, // 26: moego.models.map.v1.RouteModifiers.emission_type:type_name -> moego.models.map.v1.VehicleEmissionType
	31, // 27: moego.models.map.v1.Polyline.geo_json_linestring:type_name -> google.protobuf.Struct
	32, // 28: moego.models.map.v1.TollInfo.estimated_price:type_name -> google.type.Money
	33, // 29: moego.models.map.v1.TransitPreferences.transit_mode:type_name -> moego.models.map.v1.TransitTravelMode
	34, // 30: moego.models.map.v1.TransitPreferences.routing_preference:type_name -> moego.models.map.v1.TransitRoutePreference
	35, // 31: moego.models.map.v1.TrafficDensityIndicator.speed:type_name -> moego.models.map.v1.TrafficCondition
	8,  // 32: moego.models.map.v1.RouteTravelAdvisory.speed_reading_intervals:type_name -> moego.models.map.v1.TrafficDensityIndicator
	6,  // 33: moego.models.map.v1.RouteTravelAdvisory.toll_info:type_name -> moego.models.map.v1.TollInfo
	32, // 34: moego.models.map.v1.RouteTravelAdvisory.transit_fare:type_name -> google.type.Money
	12, // 35: moego.models.map.v1.StepsOverview.multi_modal_segments:type_name -> moego.models.map.v1.MultiModalSegment
	10, // 36: moego.models.map.v1.MultiModalSegment.navigation_instruction:type_name -> moego.models.map.v1.NavigationInstruction
	29, // 37: moego.models.map.v1.MultiModalSegment.travel_mode:type_name -> moego.models.map.v1.TravelMode
	26, // 38: moego.models.map.v1.RouteLegStepTransitDetails.headway:type_name -> google.protobuf.Duration
	14, // 39: moego.models.map.v1.RouteLegStepTransitDetails.transit_stop:type_name -> moego.models.map.v1.TransitStopDetails
	16, // 40: moego.models.map.v1.RouteLegStepTransitDetails.transit_line:type_name -> moego.models.map.v1.TransitLine
	23, // 41: moego.models.map.v1.RouteLegStepTransitDetails.localized_values:type_name -> moego.models.map.v1.RouteLegStepTransitDetails.LocalizedValuesType
	36, // 42: moego.models.map.v1.TransitStopDetails.departure_time:type_name -> google.protobuf.Timestamp
	15, // 43: moego.models.map.v1.TransitStopDetails.departure_stop:type_name -> moego.models.map.v1.TransitStop
	36, // 44: moego.models.map.v1.TransitStopDetails.arrival_time:type_name -> google.protobuf.Timestamp
	15, // 45: moego.models.map.v1.TransitStopDetails.arrival_stop:type_name -> moego.models.map.v1.TransitStop
	24, // 46: moego.models.map.v1.TransitStop.coordinate:type_name -> google.type.LatLng
	17, // 47: moego.models.map.v1.TransitLine.agency:type_name -> moego.models.map.v1.TransitAgency
	18, // 48: moego.models.map.v1.TransitLine.vehicle:type_name -> moego.models.map.v1.TransitVehicle
	37, // 49: moego.models.map.v1.RouteMatrixElement.status:type_name -> google.rpc.Status
	26, // 50: moego.models.map.v1.RouteMatrixElement.duration:type_name -> google.protobuf.Duration
	9,  // 51: moego.models.map.v1.RouteMatrixElement.travel_advisory:type_name -> moego.models.map.v1.RouteTravelAdvisory
	20, // 52: moego.models.map.v1.RouteMatrixElement.fallback_info:type_name -> moego.models.map.v1.FallbackInfo
	21, // 53: moego.models.map.v1.RouteMatrixElement.localized_values:type_name -> moego.models.map.v1.LocalizedValues
	38, // 54: moego.models.map.v1.FallbackInfo.routing_mode:type_name -> moego.models.map.v1.FallbackRoutingMode
	39, // 55: moego.models.map.v1.FallbackInfo.reason:type_name -> moego.models.map.v1.FallbackReason
	40, // 56: moego.models.map.v1.LocalizedValues.distance:type_name -> google.type.LocalizedText
	40, // 57: moego.models.map.v1.LocalizedValues.duration:type_name -> google.type.LocalizedText
	40, // 58: moego.models.map.v1.LocalizedValues.transit_fare:type_name -> google.type.LocalizedText
	40, // 59: moego.models.map.v1.LocalizedTime.time:type_name -> google.type.LocalizedText
	22, // 60: moego.models.map.v1.RouteLegStepTransitDetails.LocalizedValuesType.arrival_time:type_name -> moego.models.map.v1.LocalizedTime
	22, // 61: moego.models.map.v1.RouteLegStepTransitDetails.LocalizedValuesType.departure_time:type_name -> moego.models.map.v1.LocalizedTime
	62, // [62:62] is the sub-list for method output_type
	62, // [62:62] is the sub-list for method input_type
	62, // [62:62] is the sub-list for extension type_name
	62, // [62:62] is the sub-list for extension extendee
	0,  // [0:62] is the sub-list for field type_name
}

func init() { file_moego_models_map_v1_route_models_proto_init() }
func file_moego_models_map_v1_route_models_proto_init() {
	if File_moego_models_map_v1_route_models_proto != nil {
		return
	}
	file_moego_models_map_v1_map_models_proto_init()
	file_moego_models_map_v1_route_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_map_v1_route_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Waypoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Route); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLeg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegStep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteModifiers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Polyline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TollInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitPreferences); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficDensityIndicator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteTravelAdvisory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NavigationInstruction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StepsOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiModalSegment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegStepTransitDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitStopDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitStop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitAgency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransitVehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteMatrixElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FallbackInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocalizedValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocalizedTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_route_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegStepTransitDetails_LocalizedValuesType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_map_v1_route_models_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Waypoint_Coordinate)(nil),
		(*Waypoint_AddressSource)(nil),
		(*Waypoint_Address)(nil),
	}
	file_moego_models_map_v1_route_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Polyline_EncodedPolyline)(nil),
		(*Polyline_GeoJsonLinestring)(nil),
	}
	file_moego_models_map_v1_route_models_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*TrafficDensityIndicator_Speed)(nil),
	}
	file_moego_models_map_v1_route_models_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_route_models_proto_msgTypes[23].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_map_v1_route_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_map_v1_route_models_proto_goTypes,
		DependencyIndexes: file_moego_models_map_v1_route_models_proto_depIdxs,
		MessageInfos:      file_moego_models_map_v1_route_models_proto_msgTypes,
	}.Build()
	File_moego_models_map_v1_route_models_proto = out.File
	file_moego_models_map_v1_route_models_proto_rawDesc = nil
	file_moego_models_map_v1_route_models_proto_goTypes = nil
	file_moego_models_map_v1_route_models_proto_depIdxs = nil
}
