// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/pet_detail_models.proto

package groomingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the appointment pet detail model
type PetDetailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	GroomingId int64 `protobuf:"varint,2,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service type
	ServiceType ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,proto3,enum=moego.models.grooming.v1.ServiceType" json:"service_type,omitempty"`
	// service time, in minutes
	ServiceTime int32 `protobuf:"varint,7,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// start time, in minutes
	StartTime int32 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// status
	Status PetDetailStatus `protobuf:"varint,11,opt,name=status,proto3,enum=moego.models.grooming.v1.PetDetailStatus" json:"status,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// scope type price
	ScopeTypePrice ServiceScopeType `protobuf:"varint,13,opt,name=scope_type_price,json=scopeTypePrice,proto3,enum=moego.models.grooming.v1.ServiceScopeType" json:"scope_type_price,omitempty"`
	// scope type time
	ScopeTypeTime ServiceScopeType `protobuf:"varint,14,opt,name=scope_type_time,json=scopeTypeTime,proto3,enum=moego.models.grooming.v1.ServiceScopeType" json:"scope_type_time,omitempty"`
	// star staff id
	StarStaffId int64 `protobuf:"varint,15,opt,name=star_staff_id,json=starStaffId,proto3" json:"star_staff_id,omitempty"`
	// package service id
	PackageServiceId int64 `protobuf:"varint,16,opt,name=package_service_id,json=packageServiceId,proto3" json:"package_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,17,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// service description
	ServiceDescription string `protobuf:"bytes,18,opt,name=service_description,json=serviceDescription,proto3" json:"service_description,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,19,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// tax rate
	TaxRate float64 `protobuf:"fixed64,20,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// enable operation
	EnableOperation bool `protobuf:"varint,21,opt,name=enable_operation,json=enableOperation,proto3" json:"enable_operation,omitempty"`
	// work mode, 0-parallel, 1-sequence
	WorkMode WorkMode `protobuf:"varint,22,opt,name=work_mode,json=workMode,proto3,enum=moego.models.grooming.v1.WorkMode" json:"work_mode,omitempty"`
	// service color code
	ServiceColorCode string `protobuf:"bytes,23,opt,name=service_color_code,json=serviceColorCode,proto3" json:"service_color_code,omitempty"`
}

func (x *PetDetailModel) Reset() {
	*x = PetDetailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailModel) ProtoMessage() {}

func (x *PetDetailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailModel.ProtoReflect.Descriptor instead.
func (*PetDetailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_pet_detail_models_proto_rawDescGZIP(), []int{0}
}

func (x *PetDetailModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetDetailModel) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *PetDetailModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *PetDetailModel) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailModel) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *PetDetailModel) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *PetDetailModel) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *PetDetailModel) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *PetDetailModel) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *PetDetailModel) GetStatus() PetDetailStatus {
	if x != nil {
		return x.Status
	}
	return PetDetailStatus_PET_DETAIL_STATUS_UNSPECIFIED
}

func (x *PetDetailModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *PetDetailModel) GetScopeTypePrice() ServiceScopeType {
	if x != nil {
		return x.ScopeTypePrice
	}
	return ServiceScopeType_SERVICE_SCOPE_TYPE_UNSPECIFIED
}

func (x *PetDetailModel) GetScopeTypeTime() ServiceScopeType {
	if x != nil {
		return x.ScopeTypeTime
	}
	return ServiceScopeType_SERVICE_SCOPE_TYPE_UNSPECIFIED
}

func (x *PetDetailModel) GetStarStaffId() int64 {
	if x != nil {
		return x.StarStaffId
	}
	return 0
}

func (x *PetDetailModel) GetPackageServiceId() int64 {
	if x != nil {
		return x.PackageServiceId
	}
	return 0
}

func (x *PetDetailModel) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *PetDetailModel) GetServiceDescription() string {
	if x != nil {
		return x.ServiceDescription
	}
	return ""
}

func (x *PetDetailModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *PetDetailModel) GetTaxRate() float64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *PetDetailModel) GetEnableOperation() bool {
	if x != nil {
		return x.EnableOperation
	}
	return false
}

func (x *PetDetailModel) GetWorkMode() WorkMode {
	if x != nil {
		return x.WorkMode
	}
	return WorkMode_WORK_MODE_PARALLEL
}

func (x *PetDetailModel) GetServiceColorCode() string {
	if x != nil {
		return x.ServiceColorCode
	}
	return ""
}

// the appointment pet detail model client view
type PetDetailModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service time, in minutes
	ServiceTime int32 `protobuf:"varint,5,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *PetDetailModelClientView) Reset() {
	*x = PetDetailModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetDetailModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetailModelClientView) ProtoMessage() {}

func (x *PetDetailModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetailModelClientView.ProtoReflect.Descriptor instead.
func (*PetDetailModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_pet_detail_models_proto_rawDescGZIP(), []int{1}
}

func (x *PetDetailModelClientView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetailModelClientView) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *PetDetailModelClientView) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetDetailModelClientView) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *PetDetailModelClientView) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

var File_moego_models_grooming_v1_pet_detail_models_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_pet_detail_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xde, 0x07, 0x0a, 0x0e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x54, 0x0a, 0x10, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2f, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a,
	0x09, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xb3, 0x01, 0x0a,
	0x18, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_grooming_v1_pet_detail_models_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_pet_detail_models_proto_rawDescData = file_moego_models_grooming_v1_pet_detail_models_proto_rawDesc
)

func file_moego_models_grooming_v1_pet_detail_models_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_pet_detail_models_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_pet_detail_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_pet_detail_models_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_pet_detail_models_proto_rawDescData
}

var file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_grooming_v1_pet_detail_models_proto_goTypes = []interface{}{
	(*PetDetailModel)(nil),           // 0: moego.models.grooming.v1.PetDetailModel
	(*PetDetailModelClientView)(nil), // 1: moego.models.grooming.v1.PetDetailModelClientView
	(ServiceType)(0),                 // 2: moego.models.grooming.v1.ServiceType
	(PetDetailStatus)(0),             // 3: moego.models.grooming.v1.PetDetailStatus
	(ServiceScopeType)(0),            // 4: moego.models.grooming.v1.ServiceScopeType
	(WorkMode)(0),                    // 5: moego.models.grooming.v1.WorkMode
}
var file_moego_models_grooming_v1_pet_detail_models_proto_depIdxs = []int32{
	2, // 0: moego.models.grooming.v1.PetDetailModel.service_type:type_name -> moego.models.grooming.v1.ServiceType
	3, // 1: moego.models.grooming.v1.PetDetailModel.status:type_name -> moego.models.grooming.v1.PetDetailStatus
	4, // 2: moego.models.grooming.v1.PetDetailModel.scope_type_price:type_name -> moego.models.grooming.v1.ServiceScopeType
	4, // 3: moego.models.grooming.v1.PetDetailModel.scope_type_time:type_name -> moego.models.grooming.v1.ServiceScopeType
	5, // 4: moego.models.grooming.v1.PetDetailModel.work_mode:type_name -> moego.models.grooming.v1.WorkMode
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_pet_detail_models_proto_init() }
func file_moego_models_grooming_v1_pet_detail_models_proto_init() {
	if File_moego_models_grooming_v1_pet_detail_models_proto != nil {
		return
	}
	file_moego_models_grooming_v1_pet_detail_enums_proto_init()
	file_moego_models_grooming_v1_service_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetDetailModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_pet_detail_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_pet_detail_models_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_pet_detail_models_proto_depIdxs,
		MessageInfos:      file_moego_models_grooming_v1_pet_detail_models_proto_msgTypes,
	}.Build()
	File_moego_models_grooming_v1_pet_detail_models_proto = out.File
	file_moego_models_grooming_v1_pet_detail_models_proto_rawDesc = nil
	file_moego_models_grooming_v1_pet_detail_models_proto_goTypes = nil
	file_moego_models_grooming_v1_pet_detail_models_proto_depIdxs = nil
}
