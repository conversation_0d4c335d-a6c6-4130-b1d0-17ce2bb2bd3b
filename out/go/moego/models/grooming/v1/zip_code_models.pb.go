// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/zip_code_models.proto

package groomingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// zip code model
type ZipCodeModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the place
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Zip code of the place
	ZipCode string `protobuf:"bytes,2,opt,name=zip_code,json=zipCode,proto3" json:"zip_code,omitempty"`
	// Name of the place
	PlaceName string `protobuf:"bytes,3,opt,name=place_name,json=placeName,proto3" json:"place_name,omitempty"`
	// State where the place is located
	State string `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	// State abbreviation
	StateAbbreviation string `protobuf:"bytes,5,opt,name=state_abbreviation,json=stateAbbreviation,proto3" json:"state_abbreviation,omitempty"`
	// County where the place is located
	County string `protobuf:"bytes,6,opt,name=county,proto3" json:"county,omitempty"`
	// Country name (if applicable)
	CountryName string `protobuf:"bytes,7,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty"`
	// Latitude of the place
	Latitude string `protobuf:"bytes,8,opt,name=latitude,proto3" json:"latitude,omitempty"`
	// Longitude of the place
	Longitude string `protobuf:"bytes,9,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// Unique identifier for the place (e.g. Google Places ID)
	PlaceId string `protobuf:"bytes,10,opt,name=place_id,json=placeId,proto3" json:"place_id,omitempty"`
	// Status of the place (true/false)
	Status bool `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	// Timestamp when the place information was last updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Timestamp when the place information was created
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *ZipCodeModel) Reset() {
	*x = ZipCodeModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_zip_code_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZipCodeModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZipCodeModel) ProtoMessage() {}

func (x *ZipCodeModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_zip_code_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZipCodeModel.ProtoReflect.Descriptor instead.
func (*ZipCodeModel) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_zip_code_models_proto_rawDescGZIP(), []int{0}
}

func (x *ZipCodeModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ZipCodeModel) GetZipCode() string {
	if x != nil {
		return x.ZipCode
	}
	return ""
}

func (x *ZipCodeModel) GetPlaceName() string {
	if x != nil {
		return x.PlaceName
	}
	return ""
}

func (x *ZipCodeModel) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ZipCodeModel) GetStateAbbreviation() string {
	if x != nil {
		return x.StateAbbreviation
	}
	return ""
}

func (x *ZipCodeModel) GetCounty() string {
	if x != nil {
		return x.County
	}
	return ""
}

func (x *ZipCodeModel) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *ZipCodeModel) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *ZipCodeModel) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *ZipCodeModel) GetPlaceId() string {
	if x != nil {
		return x.PlaceId
	}
	return ""
}

func (x *ZipCodeModel) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *ZipCodeModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ZipCodeModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_moego_models_grooming_v1_zip_code_models_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_zip_code_models_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x7a, 0x69, 0x70, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbb, 0x03, 0x0a, 0x0c,
	0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x12,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x65, 0x41,
	0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_grooming_v1_zip_code_models_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_zip_code_models_proto_rawDescData = file_moego_models_grooming_v1_zip_code_models_proto_rawDesc
)

func file_moego_models_grooming_v1_zip_code_models_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_zip_code_models_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_zip_code_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_zip_code_models_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_zip_code_models_proto_rawDescData
}

var file_moego_models_grooming_v1_zip_code_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_grooming_v1_zip_code_models_proto_goTypes = []interface{}{
	(*ZipCodeModel)(nil),          // 0: moego.models.grooming.v1.ZipCodeModel
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_moego_models_grooming_v1_zip_code_models_proto_depIdxs = []int32{
	1, // 0: moego.models.grooming.v1.ZipCodeModel.updated_at:type_name -> google.protobuf.Timestamp
	1, // 1: moego.models.grooming.v1.ZipCodeModel.created_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_zip_code_models_proto_init() }
func file_moego_models_grooming_v1_zip_code_models_proto_init() {
	if File_moego_models_grooming_v1_zip_code_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_grooming_v1_zip_code_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZipCodeModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_zip_code_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_zip_code_models_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_zip_code_models_proto_depIdxs,
		MessageInfos:      file_moego_models_grooming_v1_zip_code_models_proto_msgTypes,
	}.Build()
	File_moego_models_grooming_v1_zip_code_models_proto = out.File
	file_moego_models_grooming_v1_zip_code_models_proto_rawDesc = nil
	file_moego_models_grooming_v1_zip_code_models_proto_goTypes = nil
	file_moego_models_grooming_v1_zip_code_models_proto_depIdxs = nil
}
