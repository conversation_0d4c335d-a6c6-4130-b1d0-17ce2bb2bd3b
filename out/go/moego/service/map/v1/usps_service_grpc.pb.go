// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/map/v1/usps_service.proto

package mapsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// UnitedStatesPostalServiceClient is the client API for UnitedStatesPostalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UnitedStatesPostalServiceClient interface {
	// USPS Addresses API: address
	// Standardizes street addresses including city and street abbreviations as well as providing missing information such as ZIP Code™ and ZIP + 4®.
	// Must specify a street address, a state, and either a city or a ZIP Code™.
	Address(ctx context.Context, in *AddressRequest, opts ...grpc.CallOption) (*AddressResponse, error)
	// USPS Addresses API: ZIPCode
	// Returns the ZIP Code™ and ZIP + 4® corresponding to the given address, city, and state (use USPS state abbreviations).
	Zipcode(ctx context.Context, in *ZipcodeRequest, opts ...grpc.CallOption) (*ZipcodeResponse, error)
	// USPS Addresses API: city-state
	// Returns the city and state corresponding to the given ZIP Code™.
	CityState(ctx context.Context, in *CityStateRequest, opts ...grpc.CallOption) (*CityStateResponse, error)
}

type unitedStatesPostalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUnitedStatesPostalServiceClient(cc grpc.ClientConnInterface) UnitedStatesPostalServiceClient {
	return &unitedStatesPostalServiceClient{cc}
}

func (c *unitedStatesPostalServiceClient) Address(ctx context.Context, in *AddressRequest, opts ...grpc.CallOption) (*AddressResponse, error) {
	out := new(AddressResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.UnitedStatesPostalService/Address", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unitedStatesPostalServiceClient) Zipcode(ctx context.Context, in *ZipcodeRequest, opts ...grpc.CallOption) (*ZipcodeResponse, error) {
	out := new(ZipcodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.UnitedStatesPostalService/Zipcode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *unitedStatesPostalServiceClient) CityState(ctx context.Context, in *CityStateRequest, opts ...grpc.CallOption) (*CityStateResponse, error) {
	out := new(CityStateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.UnitedStatesPostalService/CityState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UnitedStatesPostalServiceServer is the server API for UnitedStatesPostalService service.
// All implementations must embed UnimplementedUnitedStatesPostalServiceServer
// for forward compatibility
type UnitedStatesPostalServiceServer interface {
	// USPS Addresses API: address
	// Standardizes street addresses including city and street abbreviations as well as providing missing information such as ZIP Code™ and ZIP + 4®.
	// Must specify a street address, a state, and either a city or a ZIP Code™.
	Address(context.Context, *AddressRequest) (*AddressResponse, error)
	// USPS Addresses API: ZIPCode
	// Returns the ZIP Code™ and ZIP + 4® corresponding to the given address, city, and state (use USPS state abbreviations).
	Zipcode(context.Context, *ZipcodeRequest) (*ZipcodeResponse, error)
	// USPS Addresses API: city-state
	// Returns the city and state corresponding to the given ZIP Code™.
	CityState(context.Context, *CityStateRequest) (*CityStateResponse, error)
	mustEmbedUnimplementedUnitedStatesPostalServiceServer()
}

// UnimplementedUnitedStatesPostalServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUnitedStatesPostalServiceServer struct {
}

func (UnimplementedUnitedStatesPostalServiceServer) Address(context.Context, *AddressRequest) (*AddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Address not implemented")
}
func (UnimplementedUnitedStatesPostalServiceServer) Zipcode(context.Context, *ZipcodeRequest) (*ZipcodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Zipcode not implemented")
}
func (UnimplementedUnitedStatesPostalServiceServer) CityState(context.Context, *CityStateRequest) (*CityStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CityState not implemented")
}
func (UnimplementedUnitedStatesPostalServiceServer) mustEmbedUnimplementedUnitedStatesPostalServiceServer() {
}

// UnsafeUnitedStatesPostalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UnitedStatesPostalServiceServer will
// result in compilation errors.
type UnsafeUnitedStatesPostalServiceServer interface {
	mustEmbedUnimplementedUnitedStatesPostalServiceServer()
}

func RegisterUnitedStatesPostalServiceServer(s grpc.ServiceRegistrar, srv UnitedStatesPostalServiceServer) {
	s.RegisterService(&UnitedStatesPostalService_ServiceDesc, srv)
}

func _UnitedStatesPostalService_Address_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnitedStatesPostalServiceServer).Address(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.UnitedStatesPostalService/Address",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnitedStatesPostalServiceServer).Address(ctx, req.(*AddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnitedStatesPostalService_Zipcode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ZipcodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnitedStatesPostalServiceServer).Zipcode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.UnitedStatesPostalService/Zipcode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnitedStatesPostalServiceServer).Zipcode(ctx, req.(*ZipcodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UnitedStatesPostalService_CityState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CityStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnitedStatesPostalServiceServer).CityState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.UnitedStatesPostalService/CityState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnitedStatesPostalServiceServer).CityState(ctx, req.(*CityStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UnitedStatesPostalService_ServiceDesc is the grpc.ServiceDesc for UnitedStatesPostalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UnitedStatesPostalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.map.v1.UnitedStatesPostalService",
	HandlerType: (*UnitedStatesPostalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Address",
			Handler:    _UnitedStatesPostalService_Address_Handler,
		},
		{
			MethodName: "Zipcode",
			Handler:    _UnitedStatesPostalService_Zipcode_Handler,
		},
		{
			MethodName: "CityState",
			Handler:    _UnitedStatesPostalService_CityState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/map/v1/usps_service.proto",
}
