// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/engagement/v1/setting_service.proto

package engagementsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetSettingRequest
type GetSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetSettingRequest) Reset() {
	*x = GetSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSettingRequest) ProtoMessage() {}

func (x *GetSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSettingRequest.ProtoReflect.Descriptor instead.
func (*GetSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSettingRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetSettingResponse
type GetSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting model
	Setting *v1.Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// seats setting
	SeatsSetting *v1.SeatsSetting `protobuf:"bytes,2,opt,name=seats_setting,json=seatsSetting,proto3" json:"seats_setting,omitempty"`
}

func (x *GetSettingResponse) Reset() {
	*x = GetSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSettingResponse) ProtoMessage() {}

func (x *GetSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSettingResponse.ProtoReflect.Descriptor instead.
func (*GetSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSettingResponse) GetSetting() *v1.Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *GetSettingResponse) GetSeatsSetting() *v1.SeatsSetting {
	if x != nil {
		return x.SeatsSetting
	}
	return nil
}

// UpdateSettingRequest
type UpdateSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// setting model
	UpdateSetting *v1.UpdateSettingDef `protobuf:"bytes,4,opt,name=update_setting,json=updateSetting,proto3" json:"update_setting,omitempty"`
	// update seats setting
	UpdateSeatsSetting *v1.UpdateSeatsSettingDef `protobuf:"bytes,5,opt,name=update_seats_setting,json=updateSeatsSetting,proto3,oneof" json:"update_seats_setting,omitempty"`
}

func (x *UpdateSettingRequest) Reset() {
	*x = UpdateSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSettingRequest) ProtoMessage() {}

func (x *UpdateSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSettingRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateSettingRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateSettingRequest) GetUpdateSetting() *v1.UpdateSettingDef {
	if x != nil {
		return x.UpdateSetting
	}
	return nil
}

func (x *UpdateSettingRequest) GetUpdateSeatsSetting() *v1.UpdateSeatsSettingDef {
	if x != nil {
		return x.UpdateSeatsSetting
	}
	return nil
}

// UpdateSettingResponse
type UpdateSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting model
	Setting *v1.Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// seats setting
	SeatsSetting *v1.SeatsSetting `protobuf:"bytes,2,opt,name=seats_setting,json=seatsSetting,proto3" json:"seats_setting,omitempty"`
}

func (x *UpdateSettingResponse) Reset() {
	*x = UpdateSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSettingResponse) ProtoMessage() {}

func (x *UpdateSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSettingResponse) GetSetting() *v1.Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateSettingResponse) GetSeatsSetting() *v1.SeatsSetting {
	if x != nil {
		return x.SeatsSetting
	}
	return nil
}

// TmpCallingSeatsRequest
type TmpCallingSeatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TmpCallingSeatsRequest) Reset() {
	*x = TmpCallingSeatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmpCallingSeatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmpCallingSeatsRequest) ProtoMessage() {}

func (x *TmpCallingSeatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmpCallingSeatsRequest.ProtoReflect.Descriptor instead.
func (*TmpCallingSeatsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{4}
}

// TmpCallingSeatsResponse
type TmpCallingSeatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tmp calling seats
	TmpCallingSeats []*v1.TmpSeat `protobuf:"bytes,1,rep,name=tmp_calling_seats,json=tmpCallingSeats,proto3" json:"tmp_calling_seats,omitempty"`
	// seats limit
	SeatsLimit uint32 `protobuf:"varint,2,opt,name=seats_limit,json=seatsLimit,proto3" json:"seats_limit,omitempty"`
	// is seats limit reached
	IsSeatsLimitReached bool `protobuf:"varint,3,opt,name=is_seats_limit_reached,json=isSeatsLimitReached,proto3" json:"is_seats_limit_reached,omitempty"`
}

func (x *TmpCallingSeatsResponse) Reset() {
	*x = TmpCallingSeatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmpCallingSeatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmpCallingSeatsResponse) ProtoMessage() {}

func (x *TmpCallingSeatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_setting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmpCallingSeatsResponse.ProtoReflect.Descriptor instead.
func (*TmpCallingSeatsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP(), []int{5}
}

func (x *TmpCallingSeatsResponse) GetTmpCallingSeats() []*v1.TmpSeat {
	if x != nil {
		return x.TmpCallingSeats
	}
	return nil
}

func (x *TmpCallingSeatsResponse) GetSeatsLimit() uint32 {
	if x != nil {
		return x.SeatsLimit
	}
	return 0
}

func (x *TmpCallingSeatsResponse) GetIsSeatsLimitReached() bool {
	if x != nil {
		return x.IsSeatsLimitReached
	}
	return false
}

var File_moego_service_engagement_v1_setting_service_proto protoreflect.FileDescriptor

var file_moego_service_engagement_v1_setting_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x65, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x4d, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x0c, 0x73, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xe4,
	0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x53, 0x0a,
	0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x68, 0x0a, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x61,
	0x74, 0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x61,
	0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a, 0x15,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3d, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4d,
	0x0a, 0x0d, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x0c, 0x73, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x18, 0x0a,
	0x16, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xc0, 0x01, 0x0a, 0x17, 0x54, 0x6d, 0x70, 0x43,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x11, 0x74, 0x6d, 0x70, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x53,
	0x65, 0x61, 0x74, 0x52, 0x0f, 0x74, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x61, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x74, 0x73,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x74,
	0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x53, 0x65, 0x61, 0x74, 0x73, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x52, 0x65, 0x61, 0x63, 0x68, 0x65, 0x64, 0x32, 0xf5, 0x02, 0x0a, 0x0e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6d, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x43, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_engagement_v1_setting_service_proto_rawDescOnce sync.Once
	file_moego_service_engagement_v1_setting_service_proto_rawDescData = file_moego_service_engagement_v1_setting_service_proto_rawDesc
)

func file_moego_service_engagement_v1_setting_service_proto_rawDescGZIP() []byte {
	file_moego_service_engagement_v1_setting_service_proto_rawDescOnce.Do(func() {
		file_moego_service_engagement_v1_setting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_engagement_v1_setting_service_proto_rawDescData)
	})
	return file_moego_service_engagement_v1_setting_service_proto_rawDescData
}

var file_moego_service_engagement_v1_setting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_service_engagement_v1_setting_service_proto_goTypes = []interface{}{
	(*GetSettingRequest)(nil),        // 0: moego.service.engagement.v1.GetSettingRequest
	(*GetSettingResponse)(nil),       // 1: moego.service.engagement.v1.GetSettingResponse
	(*UpdateSettingRequest)(nil),     // 2: moego.service.engagement.v1.UpdateSettingRequest
	(*UpdateSettingResponse)(nil),    // 3: moego.service.engagement.v1.UpdateSettingResponse
	(*TmpCallingSeatsRequest)(nil),   // 4: moego.service.engagement.v1.TmpCallingSeatsRequest
	(*TmpCallingSeatsResponse)(nil),  // 5: moego.service.engagement.v1.TmpCallingSeatsResponse
	(*v1.Setting)(nil),               // 6: moego.models.engagement.v1.Setting
	(*v1.SeatsSetting)(nil),          // 7: moego.models.engagement.v1.SeatsSetting
	(*v1.UpdateSettingDef)(nil),      // 8: moego.models.engagement.v1.UpdateSettingDef
	(*v1.UpdateSeatsSettingDef)(nil), // 9: moego.models.engagement.v1.UpdateSeatsSettingDef
	(*v1.TmpSeat)(nil),               // 10: moego.models.engagement.v1.TmpSeat
}
var file_moego_service_engagement_v1_setting_service_proto_depIdxs = []int32{
	6,  // 0: moego.service.engagement.v1.GetSettingResponse.setting:type_name -> moego.models.engagement.v1.Setting
	7,  // 1: moego.service.engagement.v1.GetSettingResponse.seats_setting:type_name -> moego.models.engagement.v1.SeatsSetting
	8,  // 2: moego.service.engagement.v1.UpdateSettingRequest.update_setting:type_name -> moego.models.engagement.v1.UpdateSettingDef
	9,  // 3: moego.service.engagement.v1.UpdateSettingRequest.update_seats_setting:type_name -> moego.models.engagement.v1.UpdateSeatsSettingDef
	6,  // 4: moego.service.engagement.v1.UpdateSettingResponse.setting:type_name -> moego.models.engagement.v1.Setting
	7,  // 5: moego.service.engagement.v1.UpdateSettingResponse.seats_setting:type_name -> moego.models.engagement.v1.SeatsSetting
	10, // 6: moego.service.engagement.v1.TmpCallingSeatsResponse.tmp_calling_seats:type_name -> moego.models.engagement.v1.TmpSeat
	0,  // 7: moego.service.engagement.v1.SettingService.GetSetting:input_type -> moego.service.engagement.v1.GetSettingRequest
	2,  // 8: moego.service.engagement.v1.SettingService.UpdateSetting:input_type -> moego.service.engagement.v1.UpdateSettingRequest
	4,  // 9: moego.service.engagement.v1.SettingService.TmpCallingSeats:input_type -> moego.service.engagement.v1.TmpCallingSeatsRequest
	1,  // 10: moego.service.engagement.v1.SettingService.GetSetting:output_type -> moego.service.engagement.v1.GetSettingResponse
	3,  // 11: moego.service.engagement.v1.SettingService.UpdateSetting:output_type -> moego.service.engagement.v1.UpdateSettingResponse
	5,  // 12: moego.service.engagement.v1.SettingService.TmpCallingSeats:output_type -> moego.service.engagement.v1.TmpCallingSeatsResponse
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_engagement_v1_setting_service_proto_init() }
func file_moego_service_engagement_v1_setting_service_proto_init() {
	if File_moego_service_engagement_v1_setting_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmpCallingSeatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_setting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmpCallingSeatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_engagement_v1_setting_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_engagement_v1_setting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_engagement_v1_setting_service_proto_goTypes,
		DependencyIndexes: file_moego_service_engagement_v1_setting_service_proto_depIdxs,
		MessageInfos:      file_moego_service_engagement_v1_setting_service_proto_msgTypes,
	}.Build()
	File_moego_service_engagement_v1_setting_service_proto = out.File
	file_moego_service_engagement_v1_setting_service_proto_rawDesc = nil
	file_moego_service_engagement_v1_setting_service_proto_goTypes = nil
	file_moego_service_engagement_v1_setting_service_proto_depIdxs = nil
}
