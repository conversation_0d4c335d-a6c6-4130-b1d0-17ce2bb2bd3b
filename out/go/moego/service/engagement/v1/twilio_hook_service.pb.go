// @since 2024-08-21 11:33:15
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/engagement/v1/twilio_hook_service.proto

package engagementsvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create twilio_hook request
type TwilioHookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the content def
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// query map
	QueryMap map[string]string `protobuf:"bytes,4,rep,name=query_map,json=queryMap,proto3" json:"query_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TwilioHookRequest) Reset() {
	*x = TwilioHookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TwilioHookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwilioHookRequest) ProtoMessage() {}

func (x *TwilioHookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwilioHookRequest.ProtoReflect.Descriptor instead.
func (*TwilioHookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescGZIP(), []int{0}
}

func (x *TwilioHookRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TwilioHookRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *TwilioHookRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *TwilioHookRequest) GetQueryMap() map[string]string {
	if x != nil {
		return x.QueryMap
	}
	return nil
}

// create twilio_hook response
type TwilioHookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created def
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *TwilioHookResponse) Reset() {
	*x = TwilioHookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TwilioHookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwilioHookResponse) ProtoMessage() {}

func (x *TwilioHookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwilioHookResponse.ProtoReflect.Descriptor instead.
func (*TwilioHookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescGZIP(), []int{1}
}

func (x *TwilioHookResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// ForwardMessageRequest
type ForwardMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// receiver number
	ReceiverNumber string `protobuf:"bytes,1,opt,name=receiver_number,json=receiverNumber,proto3" json:"receiver_number,omitempty"`
	// sender number
	SenderNumber string `protobuf:"bytes,2,opt,name=sender_number,json=senderNumber,proto3" json:"sender_number,omitempty"`
	// string content
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// disable auto reply
	DisableAutoReply bool `protobuf:"varint,4,opt,name=disable_auto_reply,json=disableAutoReply,proto3" json:"disable_auto_reply,omitempty"`
}

func (x *ForwardMessageRequest) Reset() {
	*x = ForwardMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForwardMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageRequest) ProtoMessage() {}

func (x *ForwardMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageRequest.ProtoReflect.Descriptor instead.
func (*ForwardMessageRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescGZIP(), []int{2}
}

func (x *ForwardMessageRequest) GetReceiverNumber() string {
	if x != nil {
		return x.ReceiverNumber
	}
	return ""
}

func (x *ForwardMessageRequest) GetSenderNumber() string {
	if x != nil {
		return x.SenderNumber
	}
	return ""
}

func (x *ForwardMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ForwardMessageRequest) GetDisableAutoReply() bool {
	if x != nil {
		return x.DisableAutoReply
	}
	return false
}

// ForwardMessageResponse
type ForwardMessageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// redirect url
	RedirectUrl string `protobuf:"bytes,1,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
}

func (x *ForwardMessageResponse) Reset() {
	*x = ForwardMessageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForwardMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageResponse) ProtoMessage() {}

func (x *ForwardMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageResponse.ProtoReflect.Descriptor instead.
func (*ForwardMessageResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescGZIP(), []int{3}
}

func (x *ForwardMessageResponse) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

var File_moego_service_engagement_v1_twilio_hook_service_proto protoreflect.FileDescriptor

var file_moego_service_engagement_v1_twilio_hook_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x02,
	0x0a, 0x11, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x59, 0x0a,
	0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2e, 0x0a, 0x12, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xbf, 0x01, 0x0a, 0x15, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x75,
	0x74, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b, 0x0a, 0x16, 0x46, 0x6f, 0x72, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x55, 0x72, 0x6c, 0x32, 0x8b, 0x09, 0x0a, 0x11, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x04, 0x43, 0x61,
	0x6c, 0x6c, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x61,
	0x6c, 0x6c, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x08, 0x46, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x69, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0a, 0x44,
	0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x0c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x13, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x12, 0x4d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x4f, 0x75, 0x74,
	0x67, 0x6f, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x18, 0x4d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c,
	0x47, 0x61, 0x74, 0x68, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x48, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x0e, 0x46, 0x6f, 0x72, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x77,
	0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescOnce sync.Once
	file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescData = file_moego_service_engagement_v1_twilio_hook_service_proto_rawDesc
)

func file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescGZIP() []byte {
	file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescOnce.Do(func() {
		file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescData)
	})
	return file_moego_service_engagement_v1_twilio_hook_service_proto_rawDescData
}

var file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_engagement_v1_twilio_hook_service_proto_goTypes = []interface{}{
	(*TwilioHookRequest)(nil),      // 0: moego.service.engagement.v1.TwilioHookRequest
	(*TwilioHookResponse)(nil),     // 1: moego.service.engagement.v1.TwilioHookResponse
	(*ForwardMessageRequest)(nil),  // 2: moego.service.engagement.v1.ForwardMessageRequest
	(*ForwardMessageResponse)(nil), // 3: moego.service.engagement.v1.ForwardMessageResponse
	nil,                            // 4: moego.service.engagement.v1.TwilioHookRequest.QueryMapEntry
}
var file_moego_service_engagement_v1_twilio_hook_service_proto_depIdxs = []int32{
	4,  // 0: moego.service.engagement.v1.TwilioHookRequest.query_map:type_name -> moego.service.engagement.v1.TwilioHookRequest.QueryMapEntry
	0,  // 1: moego.service.engagement.v1.TwilioHookService.Call:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 2: moego.service.engagement.v1.TwilioHookService.ForwardCall:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 3: moego.service.engagement.v1.TwilioHookService.Fallback:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 4: moego.service.engagement.v1.TwilioHookService.Status:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 5: moego.service.engagement.v1.TwilioHookService.DialAction:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 6: moego.service.engagement.v1.TwilioHookService.RecordStatus:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 7: moego.service.engagement.v1.TwilioHookService.TranscriptionStatus:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 8: moego.service.engagement.v1.TwilioHookService.MaskedOutgoingCall:input_type -> moego.service.engagement.v1.TwilioHookRequest
	0,  // 9: moego.service.engagement.v1.TwilioHookService.MaskedOutgoingCallGather:input_type -> moego.service.engagement.v1.TwilioHookRequest
	2,  // 10: moego.service.engagement.v1.TwilioHookService.ForwardMessage:input_type -> moego.service.engagement.v1.ForwardMessageRequest
	1,  // 11: moego.service.engagement.v1.TwilioHookService.Call:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 12: moego.service.engagement.v1.TwilioHookService.ForwardCall:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 13: moego.service.engagement.v1.TwilioHookService.Fallback:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 14: moego.service.engagement.v1.TwilioHookService.Status:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 15: moego.service.engagement.v1.TwilioHookService.DialAction:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 16: moego.service.engagement.v1.TwilioHookService.RecordStatus:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 17: moego.service.engagement.v1.TwilioHookService.TranscriptionStatus:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 18: moego.service.engagement.v1.TwilioHookService.MaskedOutgoingCall:output_type -> moego.service.engagement.v1.TwilioHookResponse
	1,  // 19: moego.service.engagement.v1.TwilioHookService.MaskedOutgoingCallGather:output_type -> moego.service.engagement.v1.TwilioHookResponse
	3,  // 20: moego.service.engagement.v1.TwilioHookService.ForwardMessage:output_type -> moego.service.engagement.v1.ForwardMessageResponse
	11, // [11:21] is the sub-list for method output_type
	1,  // [1:11] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_engagement_v1_twilio_hook_service_proto_init() }
func file_moego_service_engagement_v1_twilio_hook_service_proto_init() {
	if File_moego_service_engagement_v1_twilio_hook_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TwilioHookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TwilioHookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForwardMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForwardMessageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_engagement_v1_twilio_hook_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_engagement_v1_twilio_hook_service_proto_goTypes,
		DependencyIndexes: file_moego_service_engagement_v1_twilio_hook_service_proto_depIdxs,
		MessageInfos:      file_moego_service_engagement_v1_twilio_hook_service_proto_msgTypes,
	}.Build()
	File_moego_service_engagement_v1_twilio_hook_service_proto = out.File
	file_moego_service_engagement_v1_twilio_hook_service_proto_rawDesc = nil
	file_moego_service_engagement_v1_twilio_hook_service_proto_goTypes = nil
	file_moego_service_engagement_v1_twilio_hook_service_proto_depIdxs = nil
}
