// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v2/addhoc_service.proto

package messagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AddHocServiceClient is the client API for AddHocService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AddHocServiceClient interface {
	// 添加短信转发规则
	AddMessageForwardRecord(ctx context.Context, in *AddMessageForwardRecordRequest, opts ...grpc.CallOption) (*AddMessageForwardRecordResponse, error)
}

type addHocServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAddHocServiceClient(cc grpc.ClientConnInterface) AddHocServiceClient {
	return &addHocServiceClient{cc}
}

func (c *addHocServiceClient) AddMessageForwardRecord(ctx context.Context, in *AddMessageForwardRecordRequest, opts ...grpc.CallOption) (*AddMessageForwardRecordResponse, error) {
	out := new(AddMessageForwardRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.AddHocService/AddMessageForwardRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AddHocServiceServer is the server API for AddHocService service.
// All implementations must embed UnimplementedAddHocServiceServer
// for forward compatibility
type AddHocServiceServer interface {
	// 添加短信转发规则
	AddMessageForwardRecord(context.Context, *AddMessageForwardRecordRequest) (*AddMessageForwardRecordResponse, error)
	mustEmbedUnimplementedAddHocServiceServer()
}

// UnimplementedAddHocServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAddHocServiceServer struct {
}

func (UnimplementedAddHocServiceServer) AddMessageForwardRecord(context.Context, *AddMessageForwardRecordRequest) (*AddMessageForwardRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMessageForwardRecord not implemented")
}
func (UnimplementedAddHocServiceServer) mustEmbedUnimplementedAddHocServiceServer() {}

// UnsafeAddHocServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AddHocServiceServer will
// result in compilation errors.
type UnsafeAddHocServiceServer interface {
	mustEmbedUnimplementedAddHocServiceServer()
}

func RegisterAddHocServiceServer(s grpc.ServiceRegistrar, srv AddHocServiceServer) {
	s.RegisterService(&AddHocService_ServiceDesc, srv)
}

func _AddHocService_AddMessageForwardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMessageForwardRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AddHocServiceServer).AddMessageForwardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.AddHocService/AddMessageForwardRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AddHocServiceServer).AddMessageForwardRecord(ctx, req.(*AddMessageForwardRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AddHocService_ServiceDesc is the grpc.ServiceDesc for AddHocService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AddHocService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v2.AddHocService",
	HandlerType: (*AddHocServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddMessageForwardRecord",
			Handler:    _AddHocService_AddMessageForwardRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v2/addhoc_service.proto",
}
