// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/message/v2/message_service.proto

package messagesvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 会话已存在时的处理方式
type CreateChatRequest_ActionOnDuplicated int32

const (
	// 未指定行为，效果等同于 RETURN_ERROR
	CreateChatRequest_ACTION_UNSPECIFIED CreateChatRequest_ActionOnDuplicated = 0
	// 返回报错
	CreateChatRequest_ACTION_RETURN_ERROR CreateChatRequest_ActionOnDuplicated = 1
	// 返回已存在的 Chat
	CreateChatRequest_ACTION_RETURN_EXIST_CHAT CreateChatRequest_ActionOnDuplicated = 2
)

// Enum value maps for CreateChatRequest_ActionOnDuplicated.
var (
	CreateChatRequest_ActionOnDuplicated_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ACTION_RETURN_ERROR",
		2: "ACTION_RETURN_EXIST_CHAT",
	}
	CreateChatRequest_ActionOnDuplicated_value = map[string]int32{
		"ACTION_UNSPECIFIED":       0,
		"ACTION_RETURN_ERROR":      1,
		"ACTION_RETURN_EXIST_CHAT": 2,
	}
)

func (x CreateChatRequest_ActionOnDuplicated) Enum() *CreateChatRequest_ActionOnDuplicated {
	p := new(CreateChatRequest_ActionOnDuplicated)
	*p = x
	return p
}

func (x CreateChatRequest_ActionOnDuplicated) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateChatRequest_ActionOnDuplicated) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_message_v2_message_service_proto_enumTypes[0].Descriptor()
}

func (CreateChatRequest_ActionOnDuplicated) Type() protoreflect.EnumType {
	return &file_moego_service_message_v2_message_service_proto_enumTypes[0]
}

func (x CreateChatRequest_ActionOnDuplicated) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateChatRequest_ActionOnDuplicated.Descriptor instead.
func (CreateChatRequest_ActionOnDuplicated) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{4, 0}
}

// 查询模式
type ListChatMessageRequest_Mode int32

const (
	// 未指定模式，等效于 MODE_NEW_MESSAGE
	ListChatMessageRequest_MODE_UNSPECIFIED ListChatMessageRequest_Mode = 0
	// 获取新消息
	ListChatMessageRequest_MODE_NEW_MESSAGE ListChatMessageRequest_Mode = 1
	// 获取历史消息
	ListChatMessageRequest_MODE_HISTORICAL_MESSAGE ListChatMessageRequest_Mode = 2
)

// Enum value maps for ListChatMessageRequest_Mode.
var (
	ListChatMessageRequest_Mode_name = map[int32]string{
		0: "MODE_UNSPECIFIED",
		1: "MODE_NEW_MESSAGE",
		2: "MODE_HISTORICAL_MESSAGE",
	}
	ListChatMessageRequest_Mode_value = map[string]int32{
		"MODE_UNSPECIFIED":        0,
		"MODE_NEW_MESSAGE":        1,
		"MODE_HISTORICAL_MESSAGE": 2,
	}
)

func (x ListChatMessageRequest_Mode) Enum() *ListChatMessageRequest_Mode {
	p := new(ListChatMessageRequest_Mode)
	*p = x
	return p
}

func (x ListChatMessageRequest_Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListChatMessageRequest_Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_message_v2_message_service_proto_enumTypes[1].Descriptor()
}

func (ListChatMessageRequest_Mode) Type() protoreflect.EnumType {
	return &file_moego_service_message_v2_message_service_proto_enumTypes[1]
}

func (x ListChatMessageRequest_Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListChatMessageRequest_Mode.Descriptor instead.
func (ListChatMessageRequest_Mode) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{6, 0}
}

// 获取 Chat 的请求
type GetChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Chat ID
	ChatId uint64 `protobuf:"varint,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
}

func (x *GetChatRequest) Reset() {
	*x = GetChatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatRequest) ProtoMessage() {}

func (x *GetChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatRequest.ProtoReflect.Descriptor instead.
func (*GetChatRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetChatRequest) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

// 获取对话的响应
type GetChatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对话
	Chat *v2.ChatModel `protobuf:"bytes,1,opt,name=chat,proto3" json:"chat,omitempty"`
}

func (x *GetChatResponse) Reset() {
	*x = GetChatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatResponse) ProtoMessage() {}

func (x *GetChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatResponse.ProtoReflect.Descriptor instead.
func (*GetChatResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetChatResponse) GetChat() *v2.ChatModel {
	if x != nil {
		return x.Chat
	}
	return nil
}

// 获取 Chat 列表的请求
// CompanyID, BusinessID, CustomerID 三者不能同时不传或为 0
type ListChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId *uint64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// Business ID
	BusinessId *uint64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// Customer ID
	CustomerId *uint64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// 筛选条件
	Filter *ListChatRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页查询参数
	Pagination *v21.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListChatRequest) Reset() {
	*x = ListChatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatRequest) ProtoMessage() {}

func (x *ListChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatRequest.ProtoReflect.Descriptor instead.
func (*ListChatRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListChatRequest) GetCompanyId() uint64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListChatRequest) GetBusinessId() uint64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ListChatRequest) GetCustomerId() uint64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListChatRequest) GetFilter() *ListChatRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListChatRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 获取 Chat 列表的响应
type ListChatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Chat 列表
	Chats []*v2.ChatModel `protobuf:"bytes,1,rep,name=chats,proto3" json:"chats,omitempty"`
	// 分页查询响应
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListChatResponse) Reset() {
	*x = ListChatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatResponse) ProtoMessage() {}

func (x *ListChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatResponse.ProtoReflect.Descriptor instead.
func (*ListChatResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListChatResponse) GetChats() []*v2.ChatModel {
	if x != nil {
		return x.Chats
	}
	return nil
}

func (x *ListChatResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 创建对话的请求
type CreateChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId uint64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId uint64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Customer ID
	CustomerId uint64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 会话已存在时如何处理
	ActionOnDuplicated CreateChatRequest_ActionOnDuplicated `protobuf:"varint,4,opt,name=action_on_duplicated,json=actionOnDuplicated,proto3,enum=moego.service.message.v2.CreateChatRequest_ActionOnDuplicated" json:"action_on_duplicated,omitempty"`
}

func (x *CreateChatRequest) Reset() {
	*x = CreateChatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatRequest) ProtoMessage() {}

func (x *CreateChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatRequest.ProtoReflect.Descriptor instead.
func (*CreateChatRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateChatRequest) GetCompanyId() uint64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateChatRequest) GetBusinessId() uint64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateChatRequest) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateChatRequest) GetActionOnDuplicated() CreateChatRequest_ActionOnDuplicated {
	if x != nil {
		return x.ActionOnDuplicated
	}
	return CreateChatRequest_ACTION_UNSPECIFIED
}

// 创建会话的响应
type CreateChatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 会话
	Chat *v2.ChatModel `protobuf:"bytes,1,opt,name=chat,proto3" json:"chat,omitempty"`
	// 是否为新创建的 chat
	// 当会话已存在，且 ActionOnDuplicated 设置为 ACTION_RETURN_EXIST_CHAT 时该字段为 true
	IsNewChat bool `protobuf:"varint,2,opt,name=is_new_chat,json=isNewChat,proto3" json:"is_new_chat,omitempty"`
}

func (x *CreateChatResponse) Reset() {
	*x = CreateChatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatResponse) ProtoMessage() {}

func (x *CreateChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatResponse.ProtoReflect.Descriptor instead.
func (*CreateChatResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateChatResponse) GetChat() *v2.ChatModel {
	if x != nil {
		return x.Chat
	}
	return nil
}

func (x *CreateChatResponse) GetIsNewChat() bool {
	if x != nil {
		return x.IsNewChat
	}
	return false
}

// 获取对话的消息的请求
type ListChatMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 会话 ID
	ChatId uint64 `protobuf:"varint,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	// 查询消息列表的锚点，当 next = 0 时会无视 mode，固定获取最新的 page_size 条消息
	Next uint64 `protobuf:"varint,2,opt,name=next,proto3" json:"next,omitempty"`
	// 本次获取消息的最大数量
	PageSize uint32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 查询模式
	// 仅当 next > 0 时有效
	Mode ListChatMessageRequest_Mode `protobuf:"varint,4,opt,name=mode,proto3,enum=moego.service.message.v2.ListChatMessageRequest_Mode" json:"mode,omitempty"`
}

func (x *ListChatMessageRequest) Reset() {
	*x = ListChatMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChatMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatMessageRequest) ProtoMessage() {}

func (x *ListChatMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatMessageRequest.ProtoReflect.Descriptor instead.
func (*ListChatMessageRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListChatMessageRequest) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *ListChatMessageRequest) GetNext() uint64 {
	if x != nil {
		return x.Next
	}
	return 0
}

func (x *ListChatMessageRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListChatMessageRequest) GetMode() ListChatMessageRequest_Mode {
	if x != nil {
		return x.Mode
	}
	return ListChatMessageRequest_MODE_UNSPECIFIED
}

// 获取对话消息的响应
type ListChatMessageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 获取到的消息列表
	Messages []*v2.MessageModel `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
}

func (x *ListChatMessageResponse) Reset() {
	*x = ListChatMessageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChatMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatMessageResponse) ProtoMessage() {}

func (x *ListChatMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatMessageResponse.ProtoReflect.Descriptor instead.
func (*ListChatMessageResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListChatMessageResponse) GetMessages() []*v2.MessageModel {
	if x != nil {
		return x.Messages
	}
	return nil
}

// 发送消息的请求
type SendMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// UUID v7
	// 用于处理幂等逻辑，不设置时会自动生成
	Uuid *string `protobuf:"bytes,1,opt,name=uuid,proto3,oneof" json:"uuid,omitempty"`
	// 目标对话的 ID
	ChatId uint64 `protobuf:"varint,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	// 消息发送方的角色
	SenderRole v2.Role `protobuf:"varint,11,opt,name=sender_role,json=senderRole,proto3,enum=moego.models.message.v2.Role" json:"sender_role,omitempty"`
	// 消息发送方的 ID，如果是角色是平台，该字段 **必须** 为 0
	SenderId uint64 `protobuf:"varint,12,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// 消息接收方的角色，不能与发送方角色相同
	ReceiverRole v2.Role `protobuf:"varint,13,opt,name=receiver_role,json=receiverRole,proto3,enum=moego.models.message.v2.Role" json:"receiver_role,omitempty"`
	// 消息接收方的 ID
	ReceiverId uint64 `protobuf:"varint,14,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	// 消息投送的渠道
	Channel v2.Channel `protobuf:"varint,21,opt,name=channel,proto3,enum=moego.models.message.v2.Channel" json:"channel,omitempty"`
	// 消息内容，为了兼容图片等会把链接放在这里
	Content string `protobuf:"bytes,22,opt,name=content,proto3" json:"content,omitempty"`
	// 消息内容的版本号
	Version uint32 `protobuf:"varint,31,opt,name=version,proto3" json:"version,omitempty"`
	// 消息内容的类型
	ContentType v2.ContentType `protobuf:"varint,32,opt,name=content_type,json=contentType,proto3,enum=moego.models.message.v2.ContentType" json:"content_type,omitempty"`
	// 元信息
	Metadata *v2.MessageModel_Metadata `protobuf:"bytes,42,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{8}
}

func (x *SendMessageRequest) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *SendMessageRequest) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *SendMessageRequest) GetSenderRole() v2.Role {
	if x != nil {
		return x.SenderRole
	}
	return v2.Role(0)
}

func (x *SendMessageRequest) GetSenderId() uint64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *SendMessageRequest) GetReceiverRole() v2.Role {
	if x != nil {
		return x.ReceiverRole
	}
	return v2.Role(0)
}

func (x *SendMessageRequest) GetReceiverId() uint64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *SendMessageRequest) GetChannel() v2.Channel {
	if x != nil {
		return x.Channel
	}
	return v2.Channel(0)
}

func (x *SendMessageRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendMessageRequest) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *SendMessageRequest) GetContentType() v2.ContentType {
	if x != nil {
		return x.ContentType
	}
	return v2.ContentType(0)
}

func (x *SendMessageRequest) GetMetadata() *v2.MessageModel_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// 发送消息的响应
type SendMessageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发送的消息
	Message *v2.MessageModel `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{9}
}

func (x *SendMessageResponse) GetMessage() *v2.MessageModel {
	if x != nil {
		return x.Message
	}
	return nil
}

// 获取全部的未读消息数量的请求
type GetAllChatUnreadMessageCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 查询的角色
	ReceiverRole v2.Role `protobuf:"varint,1,opt,name=receiver_role,json=receiverRole,proto3,enum=moego.models.message.v2.Role" json:"receiver_role,omitempty"`
	// 查询的角色 ID
	ReceiverId uint64 `protobuf:"varint,2,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
}

func (x *GetAllChatUnreadMessageCountRequest) Reset() {
	*x = GetAllChatUnreadMessageCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllChatUnreadMessageCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllChatUnreadMessageCountRequest) ProtoMessage() {}

func (x *GetAllChatUnreadMessageCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllChatUnreadMessageCountRequest.ProtoReflect.Descriptor instead.
func (*GetAllChatUnreadMessageCountRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetAllChatUnreadMessageCountRequest) GetReceiverRole() v2.Role {
	if x != nil {
		return x.ReceiverRole
	}
	return v2.Role(0)
}

func (x *GetAllChatUnreadMessageCountRequest) GetReceiverId() uint64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

// 获取全部的未读消息数量的响应
type GetAllChatUnreadMessageCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 未读消息数
	UnreadMessageCount uint64 `protobuf:"varint,1,opt,name=unread_message_count,json=unreadMessageCount,proto3" json:"unread_message_count,omitempty"`
}

func (x *GetAllChatUnreadMessageCountResponse) Reset() {
	*x = GetAllChatUnreadMessageCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllChatUnreadMessageCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllChatUnreadMessageCountResponse) ProtoMessage() {}

func (x *GetAllChatUnreadMessageCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllChatUnreadMessageCountResponse.ProtoReflect.Descriptor instead.
func (*GetAllChatUnreadMessageCountResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetAllChatUnreadMessageCountResponse) GetUnreadMessageCount() uint64 {
	if x != nil {
		return x.UnreadMessageCount
	}
	return 0
}

// 获取 customer 在指定 company 和 business 内的未读消息数量的请求
type GetCustomerUnreadMessageCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId uint64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business IDs, empty for all business
	BusinessIds []uint64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// Customer ID
	CustomerId uint64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *GetCustomerUnreadMessageCountRequest) Reset() {
	*x = GetCustomerUnreadMessageCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerUnreadMessageCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerUnreadMessageCountRequest) ProtoMessage() {}

func (x *GetCustomerUnreadMessageCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerUnreadMessageCountRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerUnreadMessageCountRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetCustomerUnreadMessageCountRequest) GetCompanyId() uint64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCustomerUnreadMessageCountRequest) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *GetCustomerUnreadMessageCountRequest) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// 获取customer 在指定 company 和 business 内的未读消息数量的响应
type GetCustomerUnreadMessageCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 未读消息数
	UnreadMessageCount uint64 `protobuf:"varint,1,opt,name=unread_message_count,json=unreadMessageCount,proto3" json:"unread_message_count,omitempty"`
}

func (x *GetCustomerUnreadMessageCountResponse) Reset() {
	*x = GetCustomerUnreadMessageCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerUnreadMessageCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerUnreadMessageCountResponse) ProtoMessage() {}

func (x *GetCustomerUnreadMessageCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerUnreadMessageCountResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerUnreadMessageCountResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetCustomerUnreadMessageCountResponse) GetUnreadMessageCount() uint64 {
	if x != nil {
		return x.UnreadMessageCount
	}
	return 0
}

// 批量获取消息的请求
type BatchGetMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer ID, 与 Business ID 至少填 1 个
	CustomerId *uint64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// Business ID，与 Customer ID 至少填 1 个
	BusinessId *uint64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// 消息 ID 列表
	MessageIds []uint64 `protobuf:"varint,11,rep,packed,name=message_ids,json=messageIds,proto3" json:"message_ids,omitempty"`
}

func (x *BatchGetMessageRequest) Reset() {
	*x = BatchGetMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetMessageRequest) ProtoMessage() {}

func (x *BatchGetMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetMessageRequest.ProtoReflect.Descriptor instead.
func (*BatchGetMessageRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{14}
}

func (x *BatchGetMessageRequest) GetCustomerId() uint64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *BatchGetMessageRequest) GetBusinessId() uint64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *BatchGetMessageRequest) GetMessageIds() []uint64 {
	if x != nil {
		return x.MessageIds
	}
	return nil
}

// 批量获取消息的响应
type BatchGetMessageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息列表
	Messages []*v2.MessageModel `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
}

func (x *BatchGetMessageResponse) Reset() {
	*x = BatchGetMessageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetMessageResponse) ProtoMessage() {}

func (x *BatchGetMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetMessageResponse.ProtoReflect.Descriptor instead.
func (*BatchGetMessageResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{15}
}

func (x *BatchGetMessageResponse) GetMessages() []*v2.MessageModel {
	if x != nil {
		return x.Messages
	}
	return nil
}

// 合并消息记录的请求
type MergeMessagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// merge relation
	MergeRelation *v1.BusinessCustomerMergeRelationDef `protobuf:"bytes,2,opt,name=merge_relation,json=mergeRelation,proto3" json:"merge_relation,omitempty"`
}

func (x *MergeMessagesRequest) Reset() {
	*x = MergeMessagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeMessagesRequest) ProtoMessage() {}

func (x *MergeMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeMessagesRequest.ProtoReflect.Descriptor instead.
func (*MergeMessagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{16}
}

func (x *MergeMessagesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MergeMessagesRequest) GetMergeRelation() *v1.BusinessCustomerMergeRelationDef {
	if x != nil {
		return x.MergeRelation
	}
	return nil
}

// 合并消息记录的响应
type MergeMessagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *MergeMessagesResponse) Reset() {
	*x = MergeMessagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeMessagesResponse) ProtoMessage() {}

func (x *MergeMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeMessagesResponse.ProtoReflect.Descriptor instead.
func (*MergeMessagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{17}
}

func (x *MergeMessagesResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Filter
type ListChatRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否星标
	IsStarred *bool `protobuf:"varint,1,opt,name=is_starred,json=isStarred,proto3,oneof" json:"is_starred,omitempty"`
	// 按对话的状态过滤
	ChatStatuses []v2.ChatStatus `protobuf:"varint,2,rep,packed,name=chat_statuses,json=chatStatuses,proto3,enum=moego.models.message.v2.ChatStatus" json:"chat_statuses,omitempty"`
}

func (x *ListChatRequest_Filter) Reset() {
	*x = ListChatRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_message_v2_message_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChatRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChatRequest_Filter) ProtoMessage() {}

func (x *ListChatRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_message_v2_message_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChatRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListChatRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_message_v2_message_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListChatRequest_Filter) GetIsStarred() bool {
	if x != nil && x.IsStarred != nil {
		return *x.IsStarred
	}
	return false
}

func (x *ListChatRequest_Filter) GetChatStatuses() []v2.ChatStatus {
	if x != nil {
		return x.ChatStatuses
	}
	return nil
}

var File_moego_service_message_v2_message_service_proto protoreflect.FileDescriptor

var file_moego_service_message_v2_message_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x32,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74,
	0x49, 0x64, 0x22, 0x49, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x63, 0x68, 0x61, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x63, 0x68, 0x61, 0x74, 0x22, 0xe9, 0x03,
	0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x48, 0x02, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x4d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x99, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x22,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73, 0x53, 0x74, 0x61, 0x72, 0x72, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x5c, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x12,
	0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x18, 0x01, 0x22, 0x08, 0x82, 0x01, 0x05, 0x10, 0x01, 0x20,
	0x90, 0x03, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x72, 0x65, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x05, 0x63, 0x68, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x05, 0x63, 0x68, 0x61, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe6, 0x02, 0x0a,
	0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x70,
	0x0a, 0x14, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x6e, 0x5f, 0x64, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x6e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x12, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x22, 0x63, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e, 0x44, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x43,
	0x48, 0x41, 0x54, 0x10, 0x02, 0x22, 0x6c, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x63,
	0x68, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x63,
	0x68, 0x61, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x68,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x43,
	0x68, 0x61, 0x74, 0x22, 0x94, 0x02, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6e, 0x65, 0x78, 0x74, 0x12, 0x27, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x2a, 0x05, 0x18, 0xe8, 0x07, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x53, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x4f, 0x0a, 0x04, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x1b, 0x0a,
	0x17, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x43, 0x41, 0x4c,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x02, 0x22, 0x5c, 0x0a, 0x17, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0xfd, 0x04, 0x0a, 0x12, 0x53, 0x65, 0x6e,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x72, 0x06, 0x98, 0x01, 0x24, 0xd0, 0x01, 0x01, 0x48, 0x00, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x6f, 0x6c,
	0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x00, 0x52, 0x08, 0x73,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x0c, 0xfa,
	0x42, 0x09, 0x82, 0x01, 0x06, 0x10, 0x01, 0x18, 0x01, 0x18, 0x02, 0x52, 0x0c, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0x80, 0x08, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x51, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x22, 0x56, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3f, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x9d, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x55,
	0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x58, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x55, 0x6e,
	0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x6e, 0x72, 0x65,
	0x61, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0c, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x04, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x75,
	0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xc7, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x5c, 0x0a, 0x17, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0xaa, 0x01, 0x0a, 0x14, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x0e, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x31, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xcd, 0x08, 0x0a, 0x0e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x07,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x08,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x67, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x12, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6a, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x61,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43,
	0x68, 0x61, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x68,
	0x61, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x6e, 0x72, 0x65,
	0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x76, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01,
	0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x3b,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_message_v2_message_service_proto_rawDescOnce sync.Once
	file_moego_service_message_v2_message_service_proto_rawDescData = file_moego_service_message_v2_message_service_proto_rawDesc
)

func file_moego_service_message_v2_message_service_proto_rawDescGZIP() []byte {
	file_moego_service_message_v2_message_service_proto_rawDescOnce.Do(func() {
		file_moego_service_message_v2_message_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_message_v2_message_service_proto_rawDescData)
	})
	return file_moego_service_message_v2_message_service_proto_rawDescData
}

var file_moego_service_message_v2_message_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_service_message_v2_message_service_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_moego_service_message_v2_message_service_proto_goTypes = []interface{}{
	(CreateChatRequest_ActionOnDuplicated)(0),     // 0: moego.service.message.v2.CreateChatRequest.ActionOnDuplicated
	(ListChatMessageRequest_Mode)(0),              // 1: moego.service.message.v2.ListChatMessageRequest.Mode
	(*GetChatRequest)(nil),                        // 2: moego.service.message.v2.GetChatRequest
	(*GetChatResponse)(nil),                       // 3: moego.service.message.v2.GetChatResponse
	(*ListChatRequest)(nil),                       // 4: moego.service.message.v2.ListChatRequest
	(*ListChatResponse)(nil),                      // 5: moego.service.message.v2.ListChatResponse
	(*CreateChatRequest)(nil),                     // 6: moego.service.message.v2.CreateChatRequest
	(*CreateChatResponse)(nil),                    // 7: moego.service.message.v2.CreateChatResponse
	(*ListChatMessageRequest)(nil),                // 8: moego.service.message.v2.ListChatMessageRequest
	(*ListChatMessageResponse)(nil),               // 9: moego.service.message.v2.ListChatMessageResponse
	(*SendMessageRequest)(nil),                    // 10: moego.service.message.v2.SendMessageRequest
	(*SendMessageResponse)(nil),                   // 11: moego.service.message.v2.SendMessageResponse
	(*GetAllChatUnreadMessageCountRequest)(nil),   // 12: moego.service.message.v2.GetAllChatUnreadMessageCountRequest
	(*GetAllChatUnreadMessageCountResponse)(nil),  // 13: moego.service.message.v2.GetAllChatUnreadMessageCountResponse
	(*GetCustomerUnreadMessageCountRequest)(nil),  // 14: moego.service.message.v2.GetCustomerUnreadMessageCountRequest
	(*GetCustomerUnreadMessageCountResponse)(nil), // 15: moego.service.message.v2.GetCustomerUnreadMessageCountResponse
	(*BatchGetMessageRequest)(nil),                // 16: moego.service.message.v2.BatchGetMessageRequest
	(*BatchGetMessageResponse)(nil),               // 17: moego.service.message.v2.BatchGetMessageResponse
	(*MergeMessagesRequest)(nil),                  // 18: moego.service.message.v2.MergeMessagesRequest
	(*MergeMessagesResponse)(nil),                 // 19: moego.service.message.v2.MergeMessagesResponse
	(*ListChatRequest_Filter)(nil),                // 20: moego.service.message.v2.ListChatRequest.Filter
	(*v2.ChatModel)(nil),                          // 21: moego.models.message.v2.ChatModel
	(*v21.PaginationRequest)(nil),                 // 22: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                // 23: moego.utils.v2.PaginationResponse
	(*v2.MessageModel)(nil),                       // 24: moego.models.message.v2.MessageModel
	(v2.Role)(0),                                  // 25: moego.models.message.v2.Role
	(v2.Channel)(0),                               // 26: moego.models.message.v2.Channel
	(v2.ContentType)(0),                           // 27: moego.models.message.v2.ContentType
	(*v2.MessageModel_Metadata)(nil),              // 28: moego.models.message.v2.MessageModel.Metadata
	(*v1.BusinessCustomerMergeRelationDef)(nil),   // 29: moego.models.business_customer.v1.BusinessCustomerMergeRelationDef
	(v2.ChatStatus)(0),                            // 30: moego.models.message.v2.ChatStatus
}
var file_moego_service_message_v2_message_service_proto_depIdxs = []int32{
	21, // 0: moego.service.message.v2.GetChatResponse.chat:type_name -> moego.models.message.v2.ChatModel
	20, // 1: moego.service.message.v2.ListChatRequest.filter:type_name -> moego.service.message.v2.ListChatRequest.Filter
	22, // 2: moego.service.message.v2.ListChatRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	21, // 3: moego.service.message.v2.ListChatResponse.chats:type_name -> moego.models.message.v2.ChatModel
	23, // 4: moego.service.message.v2.ListChatResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 5: moego.service.message.v2.CreateChatRequest.action_on_duplicated:type_name -> moego.service.message.v2.CreateChatRequest.ActionOnDuplicated
	21, // 6: moego.service.message.v2.CreateChatResponse.chat:type_name -> moego.models.message.v2.ChatModel
	1,  // 7: moego.service.message.v2.ListChatMessageRequest.mode:type_name -> moego.service.message.v2.ListChatMessageRequest.Mode
	24, // 8: moego.service.message.v2.ListChatMessageResponse.messages:type_name -> moego.models.message.v2.MessageModel
	25, // 9: moego.service.message.v2.SendMessageRequest.sender_role:type_name -> moego.models.message.v2.Role
	25, // 10: moego.service.message.v2.SendMessageRequest.receiver_role:type_name -> moego.models.message.v2.Role
	26, // 11: moego.service.message.v2.SendMessageRequest.channel:type_name -> moego.models.message.v2.Channel
	27, // 12: moego.service.message.v2.SendMessageRequest.content_type:type_name -> moego.models.message.v2.ContentType
	28, // 13: moego.service.message.v2.SendMessageRequest.metadata:type_name -> moego.models.message.v2.MessageModel.Metadata
	24, // 14: moego.service.message.v2.SendMessageResponse.message:type_name -> moego.models.message.v2.MessageModel
	25, // 15: moego.service.message.v2.GetAllChatUnreadMessageCountRequest.receiver_role:type_name -> moego.models.message.v2.Role
	24, // 16: moego.service.message.v2.BatchGetMessageResponse.messages:type_name -> moego.models.message.v2.MessageModel
	29, // 17: moego.service.message.v2.MergeMessagesRequest.merge_relation:type_name -> moego.models.business_customer.v1.BusinessCustomerMergeRelationDef
	30, // 18: moego.service.message.v2.ListChatRequest.Filter.chat_statuses:type_name -> moego.models.message.v2.ChatStatus
	2,  // 19: moego.service.message.v2.MessageService.GetChat:input_type -> moego.service.message.v2.GetChatRequest
	4,  // 20: moego.service.message.v2.MessageService.ListChat:input_type -> moego.service.message.v2.ListChatRequest
	6,  // 21: moego.service.message.v2.MessageService.CreateChat:input_type -> moego.service.message.v2.CreateChatRequest
	8,  // 22: moego.service.message.v2.MessageService.ListChatMessage:input_type -> moego.service.message.v2.ListChatMessageRequest
	10, // 23: moego.service.message.v2.MessageService.SendMessage:input_type -> moego.service.message.v2.SendMessageRequest
	12, // 24: moego.service.message.v2.MessageService.GetAllChatUnreadMessageCount:input_type -> moego.service.message.v2.GetAllChatUnreadMessageCountRequest
	14, // 25: moego.service.message.v2.MessageService.GetCustomerUnreadMessageCount:input_type -> moego.service.message.v2.GetCustomerUnreadMessageCountRequest
	16, // 26: moego.service.message.v2.MessageService.BatchGetMessage:input_type -> moego.service.message.v2.BatchGetMessageRequest
	18, // 27: moego.service.message.v2.MessageService.MergeMessages:input_type -> moego.service.message.v2.MergeMessagesRequest
	3,  // 28: moego.service.message.v2.MessageService.GetChat:output_type -> moego.service.message.v2.GetChatResponse
	5,  // 29: moego.service.message.v2.MessageService.ListChat:output_type -> moego.service.message.v2.ListChatResponse
	7,  // 30: moego.service.message.v2.MessageService.CreateChat:output_type -> moego.service.message.v2.CreateChatResponse
	9,  // 31: moego.service.message.v2.MessageService.ListChatMessage:output_type -> moego.service.message.v2.ListChatMessageResponse
	11, // 32: moego.service.message.v2.MessageService.SendMessage:output_type -> moego.service.message.v2.SendMessageResponse
	13, // 33: moego.service.message.v2.MessageService.GetAllChatUnreadMessageCount:output_type -> moego.service.message.v2.GetAllChatUnreadMessageCountResponse
	15, // 34: moego.service.message.v2.MessageService.GetCustomerUnreadMessageCount:output_type -> moego.service.message.v2.GetCustomerUnreadMessageCountResponse
	17, // 35: moego.service.message.v2.MessageService.BatchGetMessage:output_type -> moego.service.message.v2.BatchGetMessageResponse
	19, // 36: moego.service.message.v2.MessageService.MergeMessages:output_type -> moego.service.message.v2.MergeMessagesResponse
	28, // [28:37] is the sub-list for method output_type
	19, // [19:28] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_service_message_v2_message_service_proto_init() }
func file_moego_service_message_v2_message_service_proto_init() {
	if File_moego_service_message_v2_message_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_message_v2_message_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateChatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateChatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChatMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChatMessageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllChatUnreadMessageCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllChatUnreadMessageCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerUnreadMessageCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerUnreadMessageCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetMessageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeMessagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeMessagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_message_v2_message_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChatRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_message_v2_message_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_message_v2_message_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_message_v2_message_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_service_message_v2_message_service_proto_msgTypes[18].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_message_v2_message_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_message_v2_message_service_proto_goTypes,
		DependencyIndexes: file_moego_service_message_v2_message_service_proto_depIdxs,
		EnumInfos:         file_moego_service_message_v2_message_service_proto_enumTypes,
		MessageInfos:      file_moego_service_message_v2_message_service_proto_msgTypes,
	}.Build()
	File_moego_service_message_v2_message_service_proto = out.File
	file_moego_service_message_v2_message_service_proto_rawDesc = nil
	file_moego_service_message_v2_message_service_proto_goTypes = nil
	file_moego_service_message_v2_message_service_proto_depIdxs = nil
}
