// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/ratelimit/v1/ratelimit_service.proto

package ratelimitsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ratelimit/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 限流请求
type AllowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则空间的唯一标识
	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	// 限流资源，用于匹配限流规则，形式为 <Label, Value>，Label 是限流维度标签，Value 是具体值
	Resources map[string]string `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 请求发生时的时间戳（毫秒级）
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 一条限流请求可以代表多个相同的资源请求来命中限流规则
	Hits int64 `protobuf:"varint,4,opt,name=hits,proto3" json:"hits,omitempty"`
}

func (x *AllowRequest) Reset() {
	*x = AllowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowRequest) ProtoMessage() {}

func (x *AllowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowRequest.ProtoReflect.Descriptor instead.
func (*AllowRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescGZIP(), []int{0}
}

func (x *AllowRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AllowRequest) GetResources() map[string]string {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *AllowRequest) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *AllowRequest) GetHits() int64 {
	if x != nil {
		return x.Hits
	}
	return 0
}

// 限流响应
type AllowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否允许通过
	Allow bool `protobuf:"varint,1,opt,name=allow,proto3" json:"allow,omitempty"`
	// 触发限流后的重试时间
	RetryAfter *durationpb.Duration `protobuf:"bytes,2,opt,name=retry_after,json=retryAfter,proto3" json:"retry_after,omitempty"`
}

func (x *AllowResponse) Reset() {
	*x = AllowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllowResponse) ProtoMessage() {}

func (x *AllowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllowResponse.ProtoReflect.Descriptor instead.
func (*AllowResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescGZIP(), []int{1}
}

func (x *AllowResponse) GetAllow() bool {
	if x != nil {
		return x.Allow
	}
	return false
}

func (x *AllowResponse) GetRetryAfter() *durationpb.Duration {
	if x != nil {
		return x.RetryAfter
	}
	return nil
}

// 注册限流规则请求
type RegisterRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则空间的唯一标识
	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	// 每个 Domain 里都可以有多条限流规则
	Rules []*v1.Rule `protobuf:"bytes,2,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *RegisterRulesRequest) Reset() {
	*x = RegisterRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterRulesRequest) ProtoMessage() {}

func (x *RegisterRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterRulesRequest.ProtoReflect.Descriptor instead.
func (*RegisterRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterRulesRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RegisterRulesRequest) GetRules() []*v1.Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

// 注册限流规则响应
type RegisterRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 限流规则注册成功确认
	Succeed bool `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
}

func (x *RegisterRulesResponse) Reset() {
	*x = RegisterRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterRulesResponse) ProtoMessage() {}

func (x *RegisterRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterRulesResponse.ProtoReflect.Descriptor instead.
func (*RegisterRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescGZIP(), []int{3}
}

func (x *RegisterRulesResponse) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

var File_moego_service_ratelimit_v1_ratelimit_service_proto protoreflect.FileDescriptor

var file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x61, 0x74,
	0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x92, 0x02, 0x0a, 0x0c, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x55, 0x0a, 0x09, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x61,
	0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x04, 0x68,
	0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x01, 0x52, 0x04, 0x68, 0x69, 0x74, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x61, 0x0a, 0x0d, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x3a, 0x0a,
	0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22, 0x65, 0x0a, 0x14, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x35, 0x0a, 0x05, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x22, 0x31, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x65, 0x64, 0x32, 0xe6, 0x01, 0x0a, 0x10, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x05, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x61, 0x74, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x0d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x86, 0x01, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescOnce sync.Once
	file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescData = file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDesc
)

func file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescGZIP() []byte {
	file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescOnce.Do(func() {
		file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescData)
	})
	return file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDescData
}

var file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_ratelimit_v1_ratelimit_service_proto_goTypes = []interface{}{
	(*AllowRequest)(nil),          // 0: moego.service.ratelimit.v1.AllowRequest
	(*AllowResponse)(nil),         // 1: moego.service.ratelimit.v1.AllowResponse
	(*RegisterRulesRequest)(nil),  // 2: moego.service.ratelimit.v1.RegisterRulesRequest
	(*RegisterRulesResponse)(nil), // 3: moego.service.ratelimit.v1.RegisterRulesResponse
	nil,                           // 4: moego.service.ratelimit.v1.AllowRequest.ResourcesEntry
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 6: google.protobuf.Duration
	(*v1.Rule)(nil),               // 7: moego.models.ratelimit.v1.Rule
}
var file_moego_service_ratelimit_v1_ratelimit_service_proto_depIdxs = []int32{
	4, // 0: moego.service.ratelimit.v1.AllowRequest.resources:type_name -> moego.service.ratelimit.v1.AllowRequest.ResourcesEntry
	5, // 1: moego.service.ratelimit.v1.AllowRequest.timestamp:type_name -> google.protobuf.Timestamp
	6, // 2: moego.service.ratelimit.v1.AllowResponse.retry_after:type_name -> google.protobuf.Duration
	7, // 3: moego.service.ratelimit.v1.RegisterRulesRequest.rules:type_name -> moego.models.ratelimit.v1.Rule
	0, // 4: moego.service.ratelimit.v1.RateLimitService.Allow:input_type -> moego.service.ratelimit.v1.AllowRequest
	2, // 5: moego.service.ratelimit.v1.RateLimitService.RegisterRules:input_type -> moego.service.ratelimit.v1.RegisterRulesRequest
	1, // 6: moego.service.ratelimit.v1.RateLimitService.Allow:output_type -> moego.service.ratelimit.v1.AllowResponse
	3, // 7: moego.service.ratelimit.v1.RateLimitService.RegisterRules:output_type -> moego.service.ratelimit.v1.RegisterRulesResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_ratelimit_v1_ratelimit_service_proto_init() }
func file_moego_service_ratelimit_v1_ratelimit_service_proto_init() {
	if File_moego_service_ratelimit_v1_ratelimit_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_ratelimit_v1_ratelimit_service_proto_goTypes,
		DependencyIndexes: file_moego_service_ratelimit_v1_ratelimit_service_proto_depIdxs,
		MessageInfos:      file_moego_service_ratelimit_v1_ratelimit_service_proto_msgTypes,
	}.Build()
	File_moego_service_ratelimit_v1_ratelimit_service_proto = out.File
	file_moego_service_ratelimit_v1_ratelimit_service_proto_rawDesc = nil
	file_moego_service_ratelimit_v1_ratelimit_service_proto_goTypes = nil
	file_moego_service_ratelimit_v1_ratelimit_service_proto_depIdxs = nil
}
