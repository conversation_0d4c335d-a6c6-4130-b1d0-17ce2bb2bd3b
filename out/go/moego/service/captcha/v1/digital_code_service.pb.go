// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/captcha/v1/digital_code_service.proto

package captchasvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/captcha/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create digital code by email request
type CreateDigitalCodeByEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification scenario
	VerificationScenario v1.VerificationScenario `protobuf:"varint,1,opt,name=verification_scenario,json=verificationScenario,proto3,enum=moego.models.captcha.v1.VerificationScenario" json:"verification_scenario,omitempty"`
	// email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *CreateDigitalCodeByEmailRequest) Reset() {
	*x = CreateDigitalCodeByEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDigitalCodeByEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDigitalCodeByEmailRequest) ProtoMessage() {}

func (x *CreateDigitalCodeByEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDigitalCodeByEmailRequest.ProtoReflect.Descriptor instead.
func (*CreateDigitalCodeByEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_captcha_v1_digital_code_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDigitalCodeByEmailRequest) GetVerificationScenario() v1.VerificationScenario {
	if x != nil {
		return x.VerificationScenario
	}
	return v1.VerificationScenario(0)
}

func (x *CreateDigitalCodeByEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// create digital code by SMS request
type CreateDigitalCodeBySMSRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification scenario
	VerificationScenario v1.VerificationScenario `protobuf:"varint,1,opt,name=verification_scenario,json=verificationScenario,proto3,enum=moego.models.captcha.v1.VerificationScenario" json:"verification_scenario,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *CreateDigitalCodeBySMSRequest) Reset() {
	*x = CreateDigitalCodeBySMSRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDigitalCodeBySMSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDigitalCodeBySMSRequest) ProtoMessage() {}

func (x *CreateDigitalCodeBySMSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDigitalCodeBySMSRequest.ProtoReflect.Descriptor instead.
func (*CreateDigitalCodeBySMSRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_captcha_v1_digital_code_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDigitalCodeBySMSRequest) GetVerificationScenario() v1.VerificationScenario {
	if x != nil {
		return x.VerificationScenario
	}
	return v1.VerificationScenario(0)
}

func (x *CreateDigitalCodeBySMSRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// create digital code response
type CreateDigitalCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification token
	VerificationToken string `protobuf:"bytes,1,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
}

func (x *CreateDigitalCodeResponse) Reset() {
	*x = CreateDigitalCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDigitalCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDigitalCodeResponse) ProtoMessage() {}

func (x *CreateDigitalCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDigitalCodeResponse.ProtoReflect.Descriptor instead.
func (*CreateDigitalCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_captcha_v1_digital_code_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDigitalCodeResponse) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

// validate digital code request
type ValidateDigitalCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification scenario
	VerificationScenario v1.VerificationScenario `protobuf:"varint,1,opt,name=verification_scenario,json=verificationScenario,proto3,enum=moego.models.captcha.v1.VerificationScenario" json:"verification_scenario,omitempty"`
	// verification token
	VerificationToken string `protobuf:"bytes,2,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	// digital code
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// resource key, email or phone number
	ResourceKey string `protobuf:"bytes,4,opt,name=resource_key,json=resourceKey,proto3" json:"resource_key,omitempty"`
	// keep alive
	KeepAlive bool `protobuf:"varint,5,opt,name=keep_alive,json=keepAlive,proto3" json:"keep_alive,omitempty"`
	// renewal expiration time if code is valid and need keep alive
	Renewal bool `protobuf:"varint,6,opt,name=renewal,proto3" json:"renewal,omitempty"`
}

func (x *ValidateDigitalCodeRequest) Reset() {
	*x = ValidateDigitalCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateDigitalCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateDigitalCodeRequest) ProtoMessage() {}

func (x *ValidateDigitalCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateDigitalCodeRequest.ProtoReflect.Descriptor instead.
func (*ValidateDigitalCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_captcha_v1_digital_code_service_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateDigitalCodeRequest) GetVerificationScenario() v1.VerificationScenario {
	if x != nil {
		return x.VerificationScenario
	}
	return v1.VerificationScenario(0)
}

func (x *ValidateDigitalCodeRequest) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

func (x *ValidateDigitalCodeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ValidateDigitalCodeRequest) GetResourceKey() string {
	if x != nil {
		return x.ResourceKey
	}
	return ""
}

func (x *ValidateDigitalCodeRequest) GetKeepAlive() bool {
	if x != nil {
		return x.KeepAlive
	}
	return false
}

func (x *ValidateDigitalCodeRequest) GetRenewal() bool {
	if x != nil {
		return x.Renewal
	}
	return false
}

var File_moego_service_captcha_v1_digital_code_service_proto protoreflect.FileDescriptor

var file_moego_service_captcha_v1_digital_code_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x67, 0x69, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x15, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x65, 0x6e,
	0x61, 0x72, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x52, 0x14, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12,
	0x20, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x18, 0x80, 0x02, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x22, 0xc0, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67, 0x69,
	0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x53, 0x4d, 0x53, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x15, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69,
	0x6f, 0x52, 0x14, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa,
	0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5c, 0x2b, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5c, 0x64,
	0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x22, 0x4a, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2d, 0x0a, 0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0xab, 0x02, 0x0a, 0x1a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x62, 0x0a, 0x15, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x52, 0x14, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x65, 0x6e, 0x61,
	0x72, 0x69, 0x6f, 0x12, 0x39, 0x0a, 0x12, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x04, 0x52, 0x11, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6b, 0x65, 0x65, 0x70, 0x5f, 0x61, 0x6c,
	0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x41,
	0x6c, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x32, 0x8f,
	0x03, 0x0a, 0x12, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42,
	0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67,
	0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x53, 0x4d, 0x53, 0x12, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x53, 0x4d, 0x53, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x13, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_captcha_v1_digital_code_service_proto_rawDescOnce sync.Once
	file_moego_service_captcha_v1_digital_code_service_proto_rawDescData = file_moego_service_captcha_v1_digital_code_service_proto_rawDesc
)

func file_moego_service_captcha_v1_digital_code_service_proto_rawDescGZIP() []byte {
	file_moego_service_captcha_v1_digital_code_service_proto_rawDescOnce.Do(func() {
		file_moego_service_captcha_v1_digital_code_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_captcha_v1_digital_code_service_proto_rawDescData)
	})
	return file_moego_service_captcha_v1_digital_code_service_proto_rawDescData
}

var file_moego_service_captcha_v1_digital_code_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_captcha_v1_digital_code_service_proto_goTypes = []interface{}{
	(*CreateDigitalCodeByEmailRequest)(nil), // 0: moego.service.captcha.v1.CreateDigitalCodeByEmailRequest
	(*CreateDigitalCodeBySMSRequest)(nil),   // 1: moego.service.captcha.v1.CreateDigitalCodeBySMSRequest
	(*CreateDigitalCodeResponse)(nil),       // 2: moego.service.captcha.v1.CreateDigitalCodeResponse
	(*ValidateDigitalCodeRequest)(nil),      // 3: moego.service.captcha.v1.ValidateDigitalCodeRequest
	(v1.VerificationScenario)(0),            // 4: moego.models.captcha.v1.VerificationScenario
	(*emptypb.Empty)(nil),                   // 5: google.protobuf.Empty
}
var file_moego_service_captcha_v1_digital_code_service_proto_depIdxs = []int32{
	4, // 0: moego.service.captcha.v1.CreateDigitalCodeByEmailRequest.verification_scenario:type_name -> moego.models.captcha.v1.VerificationScenario
	4, // 1: moego.service.captcha.v1.CreateDigitalCodeBySMSRequest.verification_scenario:type_name -> moego.models.captcha.v1.VerificationScenario
	4, // 2: moego.service.captcha.v1.ValidateDigitalCodeRequest.verification_scenario:type_name -> moego.models.captcha.v1.VerificationScenario
	0, // 3: moego.service.captcha.v1.DigitalCodeService.CreateDigitalCodeByEmail:input_type -> moego.service.captcha.v1.CreateDigitalCodeByEmailRequest
	1, // 4: moego.service.captcha.v1.DigitalCodeService.CreateDigitalCodeBySMS:input_type -> moego.service.captcha.v1.CreateDigitalCodeBySMSRequest
	3, // 5: moego.service.captcha.v1.DigitalCodeService.ValidateDigitalCode:input_type -> moego.service.captcha.v1.ValidateDigitalCodeRequest
	2, // 6: moego.service.captcha.v1.DigitalCodeService.CreateDigitalCodeByEmail:output_type -> moego.service.captcha.v1.CreateDigitalCodeResponse
	2, // 7: moego.service.captcha.v1.DigitalCodeService.CreateDigitalCodeBySMS:output_type -> moego.service.captcha.v1.CreateDigitalCodeResponse
	5, // 8: moego.service.captcha.v1.DigitalCodeService.ValidateDigitalCode:output_type -> google.protobuf.Empty
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_captcha_v1_digital_code_service_proto_init() }
func file_moego_service_captcha_v1_digital_code_service_proto_init() {
	if File_moego_service_captcha_v1_digital_code_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDigitalCodeByEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDigitalCodeBySMSRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDigitalCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_captcha_v1_digital_code_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateDigitalCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_captcha_v1_digital_code_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_captcha_v1_digital_code_service_proto_goTypes,
		DependencyIndexes: file_moego_service_captcha_v1_digital_code_service_proto_depIdxs,
		MessageInfos:      file_moego_service_captcha_v1_digital_code_service_proto_msgTypes,
	}.Build()
	File_moego_service_captcha_v1_digital_code_service_proto = out.File
	file_moego_service_captcha_v1_digital_code_service_proto_rawDesc = nil
	file_moego_service_captcha_v1_digital_code_service_proto_goTypes = nil
	file_moego_service_captcha_v1_digital_code_service_proto_depIdxs = nil
}
