// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/ai_assistant/v1/conversation_template_service.proto

package aiassistantsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpsertConversationTemplateRequest is the request message for UpsertConversationTemplate
type UpsertConversationTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id, set means update
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// the scenario
	Scenario string `protobuf:"bytes,2,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// the template
	Template string `protobuf:"bytes,3,opt,name=template,proto3" json:"template,omitempty"`
	// the trailer template
	TrailerTemplate string `protobuf:"bytes,5,opt,name=trailer_template,json=trailerTemplate,proto3" json:"trailer_template,omitempty"`
	// the temperature, default is 1
	Temperature float64 `protobuf:"fixed64,6,opt,name=temperature,proto3" json:"temperature,omitempty"`
	// the operator
	OperatorId string `protobuf:"bytes,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
}

func (x *UpsertConversationTemplateRequest) Reset() {
	*x = UpsertConversationTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertConversationTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertConversationTemplateRequest) ProtoMessage() {}

func (x *UpsertConversationTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertConversationTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpsertConversationTemplateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertConversationTemplateRequest) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpsertConversationTemplateRequest) GetScenario() string {
	if x != nil {
		return x.Scenario
	}
	return ""
}

func (x *UpsertConversationTemplateRequest) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *UpsertConversationTemplateRequest) GetTrailerTemplate() string {
	if x != nil {
		return x.TrailerTemplate
	}
	return ""
}

func (x *UpsertConversationTemplateRequest) GetTemperature() float64 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *UpsertConversationTemplateRequest) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

// DescribeConversationTemplatesRequest is the request message for DescribeConversationTemplates
type DescribeConversationTemplatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the scenario
	Scenario *string `protobuf:"bytes,1,opt,name=scenario,proto3,oneof" json:"scenario,omitempty"`
	// include deleted
	IncludeDeleted bool `protobuf:"varint,2,opt,name=include_deleted,json=includeDeleted,proto3" json:"include_deleted,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeConversationTemplatesRequest) Reset() {
	*x = DescribeConversationTemplatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeConversationTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeConversationTemplatesRequest) ProtoMessage() {}

func (x *DescribeConversationTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeConversationTemplatesRequest.ProtoReflect.Descriptor instead.
func (*DescribeConversationTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeConversationTemplatesRequest) GetScenario() string {
	if x != nil && x.Scenario != nil {
		return *x.Scenario
	}
	return ""
}

func (x *DescribeConversationTemplatesRequest) GetIncludeDeleted() bool {
	if x != nil {
		return x.IncludeDeleted
	}
	return false
}

func (x *DescribeConversationTemplatesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// DescribeConversationTemplatesResponse is the response message for DescribeConversationTemplates
type DescribeConversationTemplatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation templates
	ConversationTemplates []*v1.ConversationTemplateModel `protobuf:"bytes,1,rep,name=conversation_templates,json=conversationTemplates,proto3" json:"conversation_templates,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeConversationTemplatesResponse) Reset() {
	*x = DescribeConversationTemplatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeConversationTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeConversationTemplatesResponse) ProtoMessage() {}

func (x *DescribeConversationTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeConversationTemplatesResponse.ProtoReflect.Descriptor instead.
func (*DescribeConversationTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeConversationTemplatesResponse) GetConversationTemplates() []*v1.ConversationTemplateModel {
	if x != nil {
		return x.ConversationTemplates
	}
	return nil
}

func (x *DescribeConversationTemplatesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// DeleteConversationTemplateRequest is the request message for DeleteConversationTemplate
type DeleteConversationTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Scenario string `protobuf:"bytes,1,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// operator id
	OperatorId string `protobuf:"bytes,2,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
}

func (x *DeleteConversationTemplateRequest) Reset() {
	*x = DeleteConversationTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConversationTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConversationTemplateRequest) ProtoMessage() {}

func (x *DeleteConversationTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConversationTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteConversationTemplateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteConversationTemplateRequest) GetScenario() string {
	if x != nil {
		return x.Scenario
	}
	return ""
}

func (x *DeleteConversationTemplateRequest) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

var File_moego_service_ai_assistant_v1_conversation_template_service_proto protoreflect.FileDescriptor

var file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x02, 0x0a, 0x21, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61,
	0x72, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x02, 0x18, 0x40, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x24,
	0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x40, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x40, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65,
	0x72, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17,
	0xfa, 0x42, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x29, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x40, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x24, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x48, 0x00, 0x52, 0x08, 0x73,
	0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x61,
	0x72, 0x69, 0x6f, 0x22, 0xdb, 0x01, 0x0a, 0x25, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a,
	0x16, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x74, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72,
	0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x40, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x2a, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x32, 0xe2, 0x03, 0x0a, 0x1b, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x22, 0x00, 0x12, 0xac, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x78, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x8e, 0x01, 0x0a,
	0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescOnce sync.Once
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescData = file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDesc
)

func file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescGZIP() []byte {
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescOnce.Do(func() {
		file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescData)
	})
	return file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDescData
}

var file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_ai_assistant_v1_conversation_template_service_proto_goTypes = []interface{}{
	(*UpsertConversationTemplateRequest)(nil),     // 0: moego.service.ai_assistant.v1.UpsertConversationTemplateRequest
	(*DescribeConversationTemplatesRequest)(nil),  // 1: moego.service.ai_assistant.v1.DescribeConversationTemplatesRequest
	(*DescribeConversationTemplatesResponse)(nil), // 2: moego.service.ai_assistant.v1.DescribeConversationTemplatesResponse
	(*DeleteConversationTemplateRequest)(nil),     // 3: moego.service.ai_assistant.v1.DeleteConversationTemplateRequest
	(*v2.PaginationRequest)(nil),                  // 4: moego.utils.v2.PaginationRequest
	(*v1.ConversationTemplateModel)(nil),          // 5: moego.models.ai_assistant.v1.ConversationTemplateModel
	(*v2.PaginationResponse)(nil),                 // 6: moego.utils.v2.PaginationResponse
	(*emptypb.Empty)(nil),                         // 7: google.protobuf.Empty
}
var file_moego_service_ai_assistant_v1_conversation_template_service_proto_depIdxs = []int32{
	4, // 0: moego.service.ai_assistant.v1.DescribeConversationTemplatesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	5, // 1: moego.service.ai_assistant.v1.DescribeConversationTemplatesResponse.conversation_templates:type_name -> moego.models.ai_assistant.v1.ConversationTemplateModel
	6, // 2: moego.service.ai_assistant.v1.DescribeConversationTemplatesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0, // 3: moego.service.ai_assistant.v1.ConversationTemplateService.UpsertConversationTemplate:input_type -> moego.service.ai_assistant.v1.UpsertConversationTemplateRequest
	1, // 4: moego.service.ai_assistant.v1.ConversationTemplateService.DescribeConversationTemplates:input_type -> moego.service.ai_assistant.v1.DescribeConversationTemplatesRequest
	3, // 5: moego.service.ai_assistant.v1.ConversationTemplateService.DeleteConversationTemplate:input_type -> moego.service.ai_assistant.v1.DeleteConversationTemplateRequest
	5, // 6: moego.service.ai_assistant.v1.ConversationTemplateService.UpsertConversationTemplate:output_type -> moego.models.ai_assistant.v1.ConversationTemplateModel
	2, // 7: moego.service.ai_assistant.v1.ConversationTemplateService.DescribeConversationTemplates:output_type -> moego.service.ai_assistant.v1.DescribeConversationTemplatesResponse
	7, // 8: moego.service.ai_assistant.v1.ConversationTemplateService.DeleteConversationTemplate:output_type -> google.protobuf.Empty
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_ai_assistant_v1_conversation_template_service_proto_init() }
func file_moego_service_ai_assistant_v1_conversation_template_service_proto_init() {
	if File_moego_service_ai_assistant_v1_conversation_template_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertConversationTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeConversationTemplatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeConversationTemplatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConversationTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_ai_assistant_v1_conversation_template_service_proto_goTypes,
		DependencyIndexes: file_moego_service_ai_assistant_v1_conversation_template_service_proto_depIdxs,
		MessageInfos:      file_moego_service_ai_assistant_v1_conversation_template_service_proto_msgTypes,
	}.Build()
	File_moego_service_ai_assistant_v1_conversation_template_service_proto = out.File
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_rawDesc = nil
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_goTypes = nil
	file_moego_service_ai_assistant_v1_conversation_template_service_proto_depIdxs = nil
}
