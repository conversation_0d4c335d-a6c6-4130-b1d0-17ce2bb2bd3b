// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/reporting/v2/reports_service.proto

package reportingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ReportServiceClient is the client API for ReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportServiceClient interface {
	// Query report pages returns a list of report pages
	QueryReportPages(ctx context.Context, in *QueryReportPagesParams, opts ...grpc.CallOption) (*QueryReportPagesResult, error)
	// Mark report favorite marks/removes a report as favorite, return the reports with new sequence
	MarkReportFavorite(ctx context.Context, in *MarkReportFavoriteParams, opts ...grpc.CallOption) (*MarkReportFavoriteResult, error)
	// Save report customized config
	SaveReportCustomizeConfig(ctx context.Context, in *SaveReportCustomizeConfigParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Query metadata of reports
	QueryReportMetas(ctx context.Context, in *QueryReportMetasParams, opts ...grpc.CallOption) (*QueryReportsMetasResult, error)
	// Fetch report data
	FetchReportData(ctx context.Context, in *FetchReportDataParams, opts ...grpc.CallOption) (*FetchReportDataResult, error)
	// Export report data
	ExportReportData(ctx context.Context, in *ExportReportDataParams, opts ...grpc.CallOption) (*ExportReportDataResult, error)
	// Common page api
	QueryPages(ctx context.Context, in *QueryPageMetaRequest, opts ...grpc.CallOption) (*QueryPageMetaResponse, error)
	// Common meta api
	QueryMetas(ctx context.Context, in *QueryMetasRequest, opts ...grpc.CallOption) (*QueryMetasResponse, error)
	// Common fetch data api
	FetchData(ctx context.Context, in *FetchDataRequest, opts ...grpc.CallOption) (*FetchDataResponse, error)
	// Common export data api
	ExportData(ctx context.Context, in *ExportDataRequest, opts ...grpc.CallOption) (*ExportDataResponse, error)
}

type reportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReportServiceClient(cc grpc.ClientConnInterface) ReportServiceClient {
	return &reportServiceClient{cc}
}

func (c *reportServiceClient) QueryReportPages(ctx context.Context, in *QueryReportPagesParams, opts ...grpc.CallOption) (*QueryReportPagesResult, error) {
	out := new(QueryReportPagesResult)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/QueryReportPages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) MarkReportFavorite(ctx context.Context, in *MarkReportFavoriteParams, opts ...grpc.CallOption) (*MarkReportFavoriteResult, error) {
	out := new(MarkReportFavoriteResult)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/MarkReportFavorite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) SaveReportCustomizeConfig(ctx context.Context, in *SaveReportCustomizeConfigParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/SaveReportCustomizeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryReportMetas(ctx context.Context, in *QueryReportMetasParams, opts ...grpc.CallOption) (*QueryReportsMetasResult, error) {
	out := new(QueryReportsMetasResult)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/QueryReportMetas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) FetchReportData(ctx context.Context, in *FetchReportDataParams, opts ...grpc.CallOption) (*FetchReportDataResult, error) {
	out := new(FetchReportDataResult)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/FetchReportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) ExportReportData(ctx context.Context, in *ExportReportDataParams, opts ...grpc.CallOption) (*ExportReportDataResult, error) {
	out := new(ExportReportDataResult)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/ExportReportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryPages(ctx context.Context, in *QueryPageMetaRequest, opts ...grpc.CallOption) (*QueryPageMetaResponse, error) {
	out := new(QueryPageMetaResponse)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/QueryPages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) QueryMetas(ctx context.Context, in *QueryMetasRequest, opts ...grpc.CallOption) (*QueryMetasResponse, error) {
	out := new(QueryMetasResponse)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/QueryMetas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) FetchData(ctx context.Context, in *FetchDataRequest, opts ...grpc.CallOption) (*FetchDataResponse, error) {
	out := new(FetchDataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/FetchData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reportServiceClient) ExportData(ctx context.Context, in *ExportDataRequest, opts ...grpc.CallOption) (*ExportDataResponse, error) {
	out := new(ExportDataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.reporting.v2.ReportService/ExportData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportServiceServer is the server API for ReportService service.
// All implementations must embed UnimplementedReportServiceServer
// for forward compatibility
type ReportServiceServer interface {
	// Query report pages returns a list of report pages
	QueryReportPages(context.Context, *QueryReportPagesParams) (*QueryReportPagesResult, error)
	// Mark report favorite marks/removes a report as favorite, return the reports with new sequence
	MarkReportFavorite(context.Context, *MarkReportFavoriteParams) (*MarkReportFavoriteResult, error)
	// Save report customized config
	SaveReportCustomizeConfig(context.Context, *SaveReportCustomizeConfigParams) (*emptypb.Empty, error)
	// Query metadata of reports
	QueryReportMetas(context.Context, *QueryReportMetasParams) (*QueryReportsMetasResult, error)
	// Fetch report data
	FetchReportData(context.Context, *FetchReportDataParams) (*FetchReportDataResult, error)
	// Export report data
	ExportReportData(context.Context, *ExportReportDataParams) (*ExportReportDataResult, error)
	// Common page api
	QueryPages(context.Context, *QueryPageMetaRequest) (*QueryPageMetaResponse, error)
	// Common meta api
	QueryMetas(context.Context, *QueryMetasRequest) (*QueryMetasResponse, error)
	// Common fetch data api
	FetchData(context.Context, *FetchDataRequest) (*FetchDataResponse, error)
	// Common export data api
	ExportData(context.Context, *ExportDataRequest) (*ExportDataResponse, error)
	mustEmbedUnimplementedReportServiceServer()
}

// UnimplementedReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReportServiceServer struct {
}

func (UnimplementedReportServiceServer) QueryReportPages(context.Context, *QueryReportPagesParams) (*QueryReportPagesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryReportPages not implemented")
}
func (UnimplementedReportServiceServer) MarkReportFavorite(context.Context, *MarkReportFavoriteParams) (*MarkReportFavoriteResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkReportFavorite not implemented")
}
func (UnimplementedReportServiceServer) SaveReportCustomizeConfig(context.Context, *SaveReportCustomizeConfigParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveReportCustomizeConfig not implemented")
}
func (UnimplementedReportServiceServer) QueryReportMetas(context.Context, *QueryReportMetasParams) (*QueryReportsMetasResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryReportMetas not implemented")
}
func (UnimplementedReportServiceServer) FetchReportData(context.Context, *FetchReportDataParams) (*FetchReportDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchReportData not implemented")
}
func (UnimplementedReportServiceServer) ExportReportData(context.Context, *ExportReportDataParams) (*ExportReportDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportReportData not implemented")
}
func (UnimplementedReportServiceServer) QueryPages(context.Context, *QueryPageMetaRequest) (*QueryPageMetaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPages not implemented")
}
func (UnimplementedReportServiceServer) QueryMetas(context.Context, *QueryMetasRequest) (*QueryMetasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryMetas not implemented")
}
func (UnimplementedReportServiceServer) FetchData(context.Context, *FetchDataRequest) (*FetchDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchData not implemented")
}
func (UnimplementedReportServiceServer) ExportData(context.Context, *ExportDataRequest) (*ExportDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportData not implemented")
}
func (UnimplementedReportServiceServer) mustEmbedUnimplementedReportServiceServer() {}

// UnsafeReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportServiceServer will
// result in compilation errors.
type UnsafeReportServiceServer interface {
	mustEmbedUnimplementedReportServiceServer()
}

func RegisterReportServiceServer(s grpc.ServiceRegistrar, srv ReportServiceServer) {
	s.RegisterService(&ReportService_ServiceDesc, srv)
}

func _ReportService_QueryReportPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReportPagesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryReportPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/QueryReportPages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryReportPages(ctx, req.(*QueryReportPagesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_MarkReportFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReportFavoriteParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).MarkReportFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/MarkReportFavorite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).MarkReportFavorite(ctx, req.(*MarkReportFavoriteParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_SaveReportCustomizeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveReportCustomizeConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).SaveReportCustomizeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/SaveReportCustomizeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).SaveReportCustomizeConfig(ctx, req.(*SaveReportCustomizeConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryReportMetas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReportMetasParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryReportMetas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/QueryReportMetas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryReportMetas(ctx, req.(*QueryReportMetasParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_FetchReportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchReportDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).FetchReportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/FetchReportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).FetchReportData(ctx, req.(*FetchReportDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_ExportReportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportReportDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).ExportReportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/ExportReportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).ExportReportData(ctx, req.(*ExportReportDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPageMetaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/QueryPages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryPages(ctx, req.(*QueryPageMetaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_QueryMetas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryMetasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).QueryMetas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/QueryMetas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).QueryMetas(ctx, req.(*QueryMetasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_FetchData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).FetchData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/FetchData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).FetchData(ctx, req.(*FetchDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReportService_ExportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportServiceServer).ExportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.reporting.v2.ReportService/ExportData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportServiceServer).ExportData(ctx, req.(*ExportDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportService_ServiceDesc is the grpc.ServiceDesc for ReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.reporting.v2.ReportService",
	HandlerType: (*ReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryReportPages",
			Handler:    _ReportService_QueryReportPages_Handler,
		},
		{
			MethodName: "MarkReportFavorite",
			Handler:    _ReportService_MarkReportFavorite_Handler,
		},
		{
			MethodName: "SaveReportCustomizeConfig",
			Handler:    _ReportService_SaveReportCustomizeConfig_Handler,
		},
		{
			MethodName: "QueryReportMetas",
			Handler:    _ReportService_QueryReportMetas_Handler,
		},
		{
			MethodName: "FetchReportData",
			Handler:    _ReportService_FetchReportData_Handler,
		},
		{
			MethodName: "ExportReportData",
			Handler:    _ReportService_ExportReportData_Handler,
		},
		{
			MethodName: "QueryPages",
			Handler:    _ReportService_QueryPages_Handler,
		},
		{
			MethodName: "QueryMetas",
			Handler:    _ReportService_QueryMetas_Handler,
		},
		{
			MethodName: "FetchData",
			Handler:    _ReportService_FetchData_Handler,
		},
		{
			MethodName: "ExportData",
			Handler:    _ReportService_ExportData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/reporting/v2/reports_service.proto",
}
