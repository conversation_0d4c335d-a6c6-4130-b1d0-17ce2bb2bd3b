// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/marketing/v1/marketing_merge_service.proto

package marketingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MarketingMergeServiceClient is the client API for MarketingMergeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketingMergeServiceClient interface {
	// marketing merge customer
	MergeCustomerMarketingRecord(ctx context.Context, in *MergeCustomerMarketingRecordRequest, opts ...grpc.CallOption) (*MergeCustomerMarketingRecordResponse, error)
}

type marketingMergeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketingMergeServiceClient(cc grpc.ClientConnInterface) MarketingMergeServiceClient {
	return &marketingMergeServiceClient{cc}
}

func (c *marketingMergeServiceClient) MergeCustomerMarketingRecord(ctx context.Context, in *MergeCustomerMarketingRecordRequest, opts ...grpc.CallOption) (*MergeCustomerMarketingRecordResponse, error) {
	out := new(MergeCustomerMarketingRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.MarketingMergeService/MergeCustomerMarketingRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketingMergeServiceServer is the server API for MarketingMergeService service.
// All implementations must embed UnimplementedMarketingMergeServiceServer
// for forward compatibility
type MarketingMergeServiceServer interface {
	// marketing merge customer
	MergeCustomerMarketingRecord(context.Context, *MergeCustomerMarketingRecordRequest) (*MergeCustomerMarketingRecordResponse, error)
	mustEmbedUnimplementedMarketingMergeServiceServer()
}

// UnimplementedMarketingMergeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMarketingMergeServiceServer struct {
}

func (UnimplementedMarketingMergeServiceServer) MergeCustomerMarketingRecord(context.Context, *MergeCustomerMarketingRecordRequest) (*MergeCustomerMarketingRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeCustomerMarketingRecord not implemented")
}
func (UnimplementedMarketingMergeServiceServer) mustEmbedUnimplementedMarketingMergeServiceServer() {}

// UnsafeMarketingMergeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketingMergeServiceServer will
// result in compilation errors.
type UnsafeMarketingMergeServiceServer interface {
	mustEmbedUnimplementedMarketingMergeServiceServer()
}

func RegisterMarketingMergeServiceServer(s grpc.ServiceRegistrar, srv MarketingMergeServiceServer) {
	s.RegisterService(&MarketingMergeService_ServiceDesc, srv)
}

func _MarketingMergeService_MergeCustomerMarketingRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeCustomerMarketingRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingMergeServiceServer).MergeCustomerMarketingRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.MarketingMergeService/MergeCustomerMarketingRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingMergeServiceServer).MergeCustomerMarketingRecord(ctx, req.(*MergeCustomerMarketingRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketingMergeService_ServiceDesc is the grpc.ServiceDesc for MarketingMergeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketingMergeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.marketing.v1.MarketingMergeService",
	HandlerType: (*MarketingMergeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MergeCustomerMarketingRecord",
			Handler:    _MarketingMergeService_MergeCustomerMarketingRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/marketing/v1/marketing_merge_service.proto",
}
