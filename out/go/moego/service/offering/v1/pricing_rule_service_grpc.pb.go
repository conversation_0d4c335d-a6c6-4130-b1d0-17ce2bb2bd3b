// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/pricing_rule_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PricingRuleServiceClient is the client API for PricingRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingRuleServiceClient interface {
	// Deprecated: Do not use.
	// create pricing_rule, deprecated, use v2 instead
	UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleRequest, opts ...grpc.CallOption) (*UpsertPricingRuleResponse, error)
	// Deprecated: Do not use.
	// get pricing_rule, deprecated, use v2 instead
	GetPricingRule(ctx context.Context, in *GetPricingRuleRequest, opts ...grpc.CallOption) (*GetPricingRuleResponse, error)
	// Deprecated: Do not use.
	// list pricing_rule, deprecated, use v2 instead
	ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error)
	// Deprecated: Do not use.
	// calculate pricing rule, deprecated, use v2 instead
	CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleRequest, opts ...grpc.CallOption) (*CalculatePricingRuleResponse, error)
	// Deprecated: Do not use.
	// delete pricing rule, deprecated, use v2 instead
	DeletePricingRule(ctx context.Context, in *DeletePricingRuleRequest, opts ...grpc.CallOption) (*DeletePricingRuleResponse, error)
	// Deprecated: Do not use.
	// list associated services, used for pricing rule selecting services, deprecated, use v2 instead
	ListAssociatedServices(ctx context.Context, in *ListAssociatedServicesRequest, opts ...grpc.CallOption) (*ListAssociatedServicesResponse, error)
	// Deprecated: Do not use.
	// check rule name is exist, deprecated, use v2 instead
	CheckRuleName(ctx context.Context, in *CheckRuleNameRequest, opts ...grpc.CallOption) (*CheckRuleNameResponse, error)
}

type pricingRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingRuleServiceClient(cc grpc.ClientConnInterface) PricingRuleServiceClient {
	return &pricingRuleServiceClient{cc}
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleRequest, opts ...grpc.CallOption) (*UpsertPricingRuleResponse, error) {
	out := new(UpsertPricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/UpsertPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) GetPricingRule(ctx context.Context, in *GetPricingRuleRequest, opts ...grpc.CallOption) (*GetPricingRuleResponse, error) {
	out := new(GetPricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/GetPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error) {
	out := new(ListPricingRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/ListPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleRequest, opts ...grpc.CallOption) (*CalculatePricingRuleResponse, error) {
	out := new(CalculatePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/CalculatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) DeletePricingRule(ctx context.Context, in *DeletePricingRuleRequest, opts ...grpc.CallOption) (*DeletePricingRuleResponse, error) {
	out := new(DeletePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/DeletePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) ListAssociatedServices(ctx context.Context, in *ListAssociatedServicesRequest, opts ...grpc.CallOption) (*ListAssociatedServicesResponse, error) {
	out := new(ListAssociatedServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/ListAssociatedServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *pricingRuleServiceClient) CheckRuleName(ctx context.Context, in *CheckRuleNameRequest, opts ...grpc.CallOption) (*CheckRuleNameResponse, error) {
	out := new(CheckRuleNameResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.PricingRuleService/CheckRuleName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingRuleServiceServer is the server API for PricingRuleService service.
// All implementations must embed UnimplementedPricingRuleServiceServer
// for forward compatibility
type PricingRuleServiceServer interface {
	// Deprecated: Do not use.
	// create pricing_rule, deprecated, use v2 instead
	UpsertPricingRule(context.Context, *UpsertPricingRuleRequest) (*UpsertPricingRuleResponse, error)
	// Deprecated: Do not use.
	// get pricing_rule, deprecated, use v2 instead
	GetPricingRule(context.Context, *GetPricingRuleRequest) (*GetPricingRuleResponse, error)
	// Deprecated: Do not use.
	// list pricing_rule, deprecated, use v2 instead
	ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error)
	// Deprecated: Do not use.
	// calculate pricing rule, deprecated, use v2 instead
	CalculatePricingRule(context.Context, *CalculatePricingRuleRequest) (*CalculatePricingRuleResponse, error)
	// Deprecated: Do not use.
	// delete pricing rule, deprecated, use v2 instead
	DeletePricingRule(context.Context, *DeletePricingRuleRequest) (*DeletePricingRuleResponse, error)
	// Deprecated: Do not use.
	// list associated services, used for pricing rule selecting services, deprecated, use v2 instead
	ListAssociatedServices(context.Context, *ListAssociatedServicesRequest) (*ListAssociatedServicesResponse, error)
	// Deprecated: Do not use.
	// check rule name is exist, deprecated, use v2 instead
	CheckRuleName(context.Context, *CheckRuleNameRequest) (*CheckRuleNameResponse, error)
	mustEmbedUnimplementedPricingRuleServiceServer()
}

// UnimplementedPricingRuleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPricingRuleServiceServer struct {
}

func (UnimplementedPricingRuleServiceServer) UpsertPricingRule(context.Context, *UpsertPricingRuleRequest) (*UpsertPricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetPricingRule(context.Context, *GetPricingRuleRequest) (*GetPricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPricingRules not implemented")
}
func (UnimplementedPricingRuleServiceServer) CalculatePricingRule(context.Context, *CalculatePricingRuleRequest) (*CalculatePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) DeletePricingRule(context.Context, *DeletePricingRuleRequest) (*DeletePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) ListAssociatedServices(context.Context, *ListAssociatedServicesRequest) (*ListAssociatedServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssociatedServices not implemented")
}
func (UnimplementedPricingRuleServiceServer) CheckRuleName(context.Context, *CheckRuleNameRequest) (*CheckRuleNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckRuleName not implemented")
}
func (UnimplementedPricingRuleServiceServer) mustEmbedUnimplementedPricingRuleServiceServer() {}

// UnsafePricingRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingRuleServiceServer will
// result in compilation errors.
type UnsafePricingRuleServiceServer interface {
	mustEmbedUnimplementedPricingRuleServiceServer()
}

func RegisterPricingRuleServiceServer(s grpc.ServiceRegistrar, srv PricingRuleServiceServer) {
	s.RegisterService(&PricingRuleService_ServiceDesc, srv)
}

func _PricingRuleService_UpsertPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertPricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/UpsertPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, req.(*UpsertPricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/GetPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, req.(*GetPricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_ListPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricingRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/ListPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, req.(*ListPricingRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_CalculatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/CalculatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, req.(*CalculatePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_DeletePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/DeletePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, req.(*DeletePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_ListAssociatedServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAssociatedServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).ListAssociatedServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/ListAssociatedServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).ListAssociatedServices(ctx, req.(*ListAssociatedServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_CheckRuleName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRuleNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).CheckRuleName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.PricingRuleService/CheckRuleName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).CheckRuleName(ctx, req.(*CheckRuleNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PricingRuleService_ServiceDesc is the grpc.ServiceDesc for PricingRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PricingRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.PricingRuleService",
	HandlerType: (*PricingRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertPricingRule",
			Handler:    _PricingRuleService_UpsertPricingRule_Handler,
		},
		{
			MethodName: "GetPricingRule",
			Handler:    _PricingRuleService_GetPricingRule_Handler,
		},
		{
			MethodName: "ListPricingRules",
			Handler:    _PricingRuleService_ListPricingRules_Handler,
		},
		{
			MethodName: "CalculatePricingRule",
			Handler:    _PricingRuleService_CalculatePricingRule_Handler,
		},
		{
			MethodName: "DeletePricingRule",
			Handler:    _PricingRuleService_DeletePricingRule_Handler,
		},
		{
			MethodName: "ListAssociatedServices",
			Handler:    _PricingRuleService_ListAssociatedServices_Handler,
		},
		{
			MethodName: "CheckRuleName",
			Handler:    _PricingRuleService_CheckRuleName_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/pricing_rule_service.proto",
}
