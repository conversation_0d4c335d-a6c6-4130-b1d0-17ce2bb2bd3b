// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v2/pricing_rule_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PricingRuleServiceClient is the client API for PricingRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingRuleServiceClient interface {
	// upsert pricing rule
	UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleRequest, opts ...grpc.CallOption) (*UpsertPricingRuleResponse, error)
	// get pricing rule
	GetPricingRule(ctx context.Context, in *GetPricingRuleRequest, opts ...grpc.CallOption) (*GetPricingRuleResponse, error)
	// list pricing rule
	ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error)
	// calculate pricing rule
	CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleRequest, opts ...grpc.CallOption) (*CalculatePricingRuleResponse, error)
	// delete pricing rule
	DeletePricingRule(ctx context.Context, in *DeletePricingRuleRequest, opts ...grpc.CallOption) (*DeletePricingRuleResponse, error)
	// get discount setting
	GetDiscountSetting(ctx context.Context, in *GetDiscountSettingRequest, opts ...grpc.CallOption) (*GetDiscountSettingResponse, error)
	// update discount setting
	UpdateDiscountSetting(ctx context.Context, in *UpdateDiscountSettingRequest, opts ...grpc.CallOption) (*UpdateDiscountSettingResponse, error)
}

type pricingRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingRuleServiceClient(cc grpc.ClientConnInterface) PricingRuleServiceClient {
	return &pricingRuleServiceClient{cc}
}

func (c *pricingRuleServiceClient) UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleRequest, opts ...grpc.CallOption) (*UpsertPricingRuleResponse, error) {
	out := new(UpsertPricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/UpsertPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) GetPricingRule(ctx context.Context, in *GetPricingRuleRequest, opts ...grpc.CallOption) (*GetPricingRuleResponse, error) {
	out := new(GetPricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/GetPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) ListPricingRules(ctx context.Context, in *ListPricingRulesRequest, opts ...grpc.CallOption) (*ListPricingRulesResponse, error) {
	out := new(ListPricingRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/ListPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleRequest, opts ...grpc.CallOption) (*CalculatePricingRuleResponse, error) {
	out := new(CalculatePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/CalculatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) DeletePricingRule(ctx context.Context, in *DeletePricingRuleRequest, opts ...grpc.CallOption) (*DeletePricingRuleResponse, error) {
	out := new(DeletePricingRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/DeletePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) GetDiscountSetting(ctx context.Context, in *GetDiscountSettingRequest, opts ...grpc.CallOption) (*GetDiscountSettingResponse, error) {
	out := new(GetDiscountSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/GetDiscountSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) UpdateDiscountSetting(ctx context.Context, in *UpdateDiscountSettingRequest, opts ...grpc.CallOption) (*UpdateDiscountSettingResponse, error) {
	out := new(UpdateDiscountSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v2.PricingRuleService/UpdateDiscountSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingRuleServiceServer is the server API for PricingRuleService service.
// All implementations must embed UnimplementedPricingRuleServiceServer
// for forward compatibility
type PricingRuleServiceServer interface {
	// upsert pricing rule
	UpsertPricingRule(context.Context, *UpsertPricingRuleRequest) (*UpsertPricingRuleResponse, error)
	// get pricing rule
	GetPricingRule(context.Context, *GetPricingRuleRequest) (*GetPricingRuleResponse, error)
	// list pricing rule
	ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error)
	// calculate pricing rule
	CalculatePricingRule(context.Context, *CalculatePricingRuleRequest) (*CalculatePricingRuleResponse, error)
	// delete pricing rule
	DeletePricingRule(context.Context, *DeletePricingRuleRequest) (*DeletePricingRuleResponse, error)
	// get discount setting
	GetDiscountSetting(context.Context, *GetDiscountSettingRequest) (*GetDiscountSettingResponse, error)
	// update discount setting
	UpdateDiscountSetting(context.Context, *UpdateDiscountSettingRequest) (*UpdateDiscountSettingResponse, error)
	mustEmbedUnimplementedPricingRuleServiceServer()
}

// UnimplementedPricingRuleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPricingRuleServiceServer struct {
}

func (UnimplementedPricingRuleServiceServer) UpsertPricingRule(context.Context, *UpsertPricingRuleRequest) (*UpsertPricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetPricingRule(context.Context, *GetPricingRuleRequest) (*GetPricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) ListPricingRules(context.Context, *ListPricingRulesRequest) (*ListPricingRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPricingRules not implemented")
}
func (UnimplementedPricingRuleServiceServer) CalculatePricingRule(context.Context, *CalculatePricingRuleRequest) (*CalculatePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) DeletePricingRule(context.Context, *DeletePricingRuleRequest) (*DeletePricingRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetDiscountSetting(context.Context, *GetDiscountSettingRequest) (*GetDiscountSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountSetting not implemented")
}
func (UnimplementedPricingRuleServiceServer) UpdateDiscountSetting(context.Context, *UpdateDiscountSettingRequest) (*UpdateDiscountSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDiscountSetting not implemented")
}
func (UnimplementedPricingRuleServiceServer) mustEmbedUnimplementedPricingRuleServiceServer() {}

// UnsafePricingRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingRuleServiceServer will
// result in compilation errors.
type UnsafePricingRuleServiceServer interface {
	mustEmbedUnimplementedPricingRuleServiceServer()
}

func RegisterPricingRuleServiceServer(s grpc.ServiceRegistrar, srv PricingRuleServiceServer) {
	s.RegisterService(&PricingRuleService_ServiceDesc, srv)
}

func _PricingRuleService_UpsertPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertPricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/UpsertPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, req.(*UpsertPricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/GetPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, req.(*GetPricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_ListPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricingRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/ListPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, req.(*ListPricingRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_CalculatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/CalculatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, req.(*CalculatePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_DeletePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePricingRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/DeletePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, req.(*DeletePricingRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetDiscountSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetDiscountSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/GetDiscountSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetDiscountSetting(ctx, req.(*GetDiscountSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_UpdateDiscountSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDiscountSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).UpdateDiscountSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v2.PricingRuleService/UpdateDiscountSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).UpdateDiscountSetting(ctx, req.(*UpdateDiscountSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PricingRuleService_ServiceDesc is the grpc.ServiceDesc for PricingRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PricingRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v2.PricingRuleService",
	HandlerType: (*PricingRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertPricingRule",
			Handler:    _PricingRuleService_UpsertPricingRule_Handler,
		},
		{
			MethodName: "GetPricingRule",
			Handler:    _PricingRuleService_GetPricingRule_Handler,
		},
		{
			MethodName: "ListPricingRules",
			Handler:    _PricingRuleService_ListPricingRules_Handler,
		},
		{
			MethodName: "CalculatePricingRule",
			Handler:    _PricingRuleService_CalculatePricingRule_Handler,
		},
		{
			MethodName: "DeletePricingRule",
			Handler:    _PricingRuleService_DeletePricingRule_Handler,
		},
		{
			MethodName: "GetDiscountSetting",
			Handler:    _PricingRuleService_GetDiscountSetting_Handler,
		},
		{
			MethodName: "UpdateDiscountSetting",
			Handler:    _PricingRuleService_UpdateDiscountSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v2/pricing_rule_service.proto",
}
