// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/billing/v1/billing_webhook_service.proto

package billingsvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WebhookServiceClient is the client API for WebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookServiceClient interface {
	// receive webhook from financial gateway
	HandleWebhookEvent(ctx context.Context, in *v1.HandleWebhookEventRequest, opts ...grpc.CallOption) (*v1.HandleWebhookEventResponse, error)
}

type webhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookServiceClient(cc grpc.ClientConnInterface) WebhookServiceClient {
	return &webhookServiceClient{cc}
}

func (c *webhookServiceClient) HandleWebhookEvent(ctx context.Context, in *v1.HandleWebhookEventRequest, opts ...grpc.CallOption) (*v1.HandleWebhookEventResponse, error) {
	out := new(v1.HandleWebhookEventResponse)
	err := c.cc.Invoke(ctx, "/moego.service.billing.v1.WebhookService/HandleWebhookEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookServiceServer is the server API for WebhookService service.
// All implementations must embed UnimplementedWebhookServiceServer
// for forward compatibility
type WebhookServiceServer interface {
	// receive webhook from financial gateway
	HandleWebhookEvent(context.Context, *v1.HandleWebhookEventRequest) (*v1.HandleWebhookEventResponse, error)
	mustEmbedUnimplementedWebhookServiceServer()
}

// UnimplementedWebhookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWebhookServiceServer struct {
}

func (UnimplementedWebhookServiceServer) HandleWebhookEvent(context.Context, *v1.HandleWebhookEventRequest) (*v1.HandleWebhookEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleWebhookEvent not implemented")
}
func (UnimplementedWebhookServiceServer) mustEmbedUnimplementedWebhookServiceServer() {}

// UnsafeWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookServiceServer will
// result in compilation errors.
type UnsafeWebhookServiceServer interface {
	mustEmbedUnimplementedWebhookServiceServer()
}

func RegisterWebhookServiceServer(s grpc.ServiceRegistrar, srv WebhookServiceServer) {
	s.RegisterService(&WebhookService_ServiceDesc, srv)
}

func _WebhookService_HandleWebhookEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.HandleWebhookEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServiceServer).HandleWebhookEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.billing.v1.WebhookService/HandleWebhookEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServiceServer).HandleWebhookEvent(ctx, req.(*v1.HandleWebhookEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WebhookService_ServiceDesc is the grpc.ServiceDesc for WebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.billing.v1.WebhookService",
	HandlerType: (*WebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleWebhookEvent",
			Handler:    _WebhookService_HandleWebhookEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/billing/v1/billing_webhook_service.proto",
}
