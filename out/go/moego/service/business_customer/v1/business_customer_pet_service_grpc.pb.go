// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_pet_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerPetServiceClient is the client API for BusinessCustomerPetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerPetServiceClient interface {
	// Deprecated: Do not use.
	// get a pet by id
	// use `GetPetInfo` instead
	GetPet(ctx context.Context, in *GetPetRequest, opts ...grpc.CallOption) (*GetPetResponse, error)
	// get a pet's info by id
	GetPetInfo(ctx context.Context, in *GetPetInfoRequest, opts ...grpc.CallOption) (*GetPetInfoResponse, error)
	// Deprecated: Do not use.
	// get pets by ids
	// use `BatchGetPetInfo` instead
	BatchGetPet(ctx context.Context, in *BatchGetPetRequest, opts ...grpc.CallOption) (*BatchGetPetResponse, error)
	// get pets' info by ids
	BatchGetPetInfo(ctx context.Context, in *BatchGetPetInfoRequest, opts ...grpc.CallOption) (*BatchGetPetInfoResponse, error)
	// list pets of a customer
	ListPet(ctx context.Context, in *ListPetRequest, opts ...grpc.CallOption) (*ListPetResponse, error)
	// create a pet with additional info
	CreatePetWithAdditionalInfo(ctx context.Context, in *CreatePetWithAdditionalInfoRequest, opts ...grpc.CallOption) (*CreatePetWithAdditionalInfoResponse, error)
	// update a pet
	UpdatePet(ctx context.Context, in *UpdatePetRequest, opts ...grpc.CallOption) (*UpdatePetResponse, error)
	// list pet info
	ListPetInfo(ctx context.Context, in *ListPetInfoRequest, opts ...grpc.CallOption) (*ListPetInfoResponse, error)
}

type businessCustomerPetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerPetServiceClient(cc grpc.ClientConnInterface) BusinessCustomerPetServiceClient {
	return &businessCustomerPetServiceClient{cc}
}

// Deprecated: Do not use.
func (c *businessCustomerPetServiceClient) GetPet(ctx context.Context, in *GetPetRequest, opts ...grpc.CallOption) (*GetPetResponse, error) {
	out := new(GetPetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/GetPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) GetPetInfo(ctx context.Context, in *GetPetInfoRequest, opts ...grpc.CallOption) (*GetPetInfoResponse, error) {
	out := new(GetPetInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/GetPetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *businessCustomerPetServiceClient) BatchGetPet(ctx context.Context, in *BatchGetPetRequest, opts ...grpc.CallOption) (*BatchGetPetResponse, error) {
	out := new(BatchGetPetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/BatchGetPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) BatchGetPetInfo(ctx context.Context, in *BatchGetPetInfoRequest, opts ...grpc.CallOption) (*BatchGetPetInfoResponse, error) {
	out := new(BatchGetPetInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/BatchGetPetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) ListPet(ctx context.Context, in *ListPetRequest, opts ...grpc.CallOption) (*ListPetResponse, error) {
	out := new(ListPetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/ListPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) CreatePetWithAdditionalInfo(ctx context.Context, in *CreatePetWithAdditionalInfoRequest, opts ...grpc.CallOption) (*CreatePetWithAdditionalInfoResponse, error) {
	out := new(CreatePetWithAdditionalInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/CreatePetWithAdditionalInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) UpdatePet(ctx context.Context, in *UpdatePetRequest, opts ...grpc.CallOption) (*UpdatePetResponse, error) {
	out := new(UpdatePetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/UpdatePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPetServiceClient) ListPetInfo(ctx context.Context, in *ListPetInfoRequest, opts ...grpc.CallOption) (*ListPetInfoResponse, error) {
	out := new(ListPetInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPetService/ListPetInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerPetServiceServer is the server API for BusinessCustomerPetService service.
// All implementations must embed UnimplementedBusinessCustomerPetServiceServer
// for forward compatibility
type BusinessCustomerPetServiceServer interface {
	// Deprecated: Do not use.
	// get a pet by id
	// use `GetPetInfo` instead
	GetPet(context.Context, *GetPetRequest) (*GetPetResponse, error)
	// get a pet's info by id
	GetPetInfo(context.Context, *GetPetInfoRequest) (*GetPetInfoResponse, error)
	// Deprecated: Do not use.
	// get pets by ids
	// use `BatchGetPetInfo` instead
	BatchGetPet(context.Context, *BatchGetPetRequest) (*BatchGetPetResponse, error)
	// get pets' info by ids
	BatchGetPetInfo(context.Context, *BatchGetPetInfoRequest) (*BatchGetPetInfoResponse, error)
	// list pets of a customer
	ListPet(context.Context, *ListPetRequest) (*ListPetResponse, error)
	// create a pet with additional info
	CreatePetWithAdditionalInfo(context.Context, *CreatePetWithAdditionalInfoRequest) (*CreatePetWithAdditionalInfoResponse, error)
	// update a pet
	UpdatePet(context.Context, *UpdatePetRequest) (*UpdatePetResponse, error)
	// list pet info
	ListPetInfo(context.Context, *ListPetInfoRequest) (*ListPetInfoResponse, error)
	mustEmbedUnimplementedBusinessCustomerPetServiceServer()
}

// UnimplementedBusinessCustomerPetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerPetServiceServer struct {
}

func (UnimplementedBusinessCustomerPetServiceServer) GetPet(context.Context, *GetPetRequest) (*GetPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPet not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) GetPetInfo(context.Context, *GetPetInfoRequest) (*GetPetInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetInfo not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) BatchGetPet(context.Context, *BatchGetPetRequest) (*BatchGetPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetPet not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) BatchGetPetInfo(context.Context, *BatchGetPetInfoRequest) (*BatchGetPetInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetPetInfo not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) ListPet(context.Context, *ListPetRequest) (*ListPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPet not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) CreatePetWithAdditionalInfo(context.Context, *CreatePetWithAdditionalInfoRequest) (*CreatePetWithAdditionalInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetWithAdditionalInfo not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) UpdatePet(context.Context, *UpdatePetRequest) (*UpdatePetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePet not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) ListPetInfo(context.Context, *ListPetInfoRequest) (*ListPetInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetInfo not implemented")
}
func (UnimplementedBusinessCustomerPetServiceServer) mustEmbedUnimplementedBusinessCustomerPetServiceServer() {
}

// UnsafeBusinessCustomerPetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerPetServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerPetServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerPetServiceServer()
}

func RegisterBusinessCustomerPetServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerPetServiceServer) {
	s.RegisterService(&BusinessCustomerPetService_ServiceDesc, srv)
}

func _BusinessCustomerPetService_GetPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).GetPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/GetPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).GetPet(ctx, req.(*GetPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_GetPetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).GetPetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/GetPetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).GetPetInfo(ctx, req.(*GetPetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_BatchGetPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).BatchGetPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/BatchGetPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).BatchGetPet(ctx, req.(*BatchGetPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_BatchGetPetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).BatchGetPetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/BatchGetPetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).BatchGetPetInfo(ctx, req.(*BatchGetPetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_ListPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).ListPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/ListPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).ListPet(ctx, req.(*ListPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_CreatePetWithAdditionalInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetWithAdditionalInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).CreatePetWithAdditionalInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/CreatePetWithAdditionalInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).CreatePetWithAdditionalInfo(ctx, req.(*CreatePetWithAdditionalInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_UpdatePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).UpdatePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/UpdatePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).UpdatePet(ctx, req.(*UpdatePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPetService_ListPetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPetServiceServer).ListPetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPetService/ListPetInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPetServiceServer).ListPetInfo(ctx, req.(*ListPetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerPetService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerPetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerPetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerPetService",
	HandlerType: (*BusinessCustomerPetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPet",
			Handler:    _BusinessCustomerPetService_GetPet_Handler,
		},
		{
			MethodName: "GetPetInfo",
			Handler:    _BusinessCustomerPetService_GetPetInfo_Handler,
		},
		{
			MethodName: "BatchGetPet",
			Handler:    _BusinessCustomerPetService_BatchGetPet_Handler,
		},
		{
			MethodName: "BatchGetPetInfo",
			Handler:    _BusinessCustomerPetService_BatchGetPetInfo_Handler,
		},
		{
			MethodName: "ListPet",
			Handler:    _BusinessCustomerPetService_ListPet_Handler,
		},
		{
			MethodName: "CreatePetWithAdditionalInfo",
			Handler:    _BusinessCustomerPetService_CreatePetWithAdditionalInfo_Handler,
		},
		{
			MethodName: "UpdatePet",
			Handler:    _BusinessCustomerPetService_UpdatePet_Handler,
		},
		{
			MethodName: "ListPetInfo",
			Handler:    _BusinessCustomerPetService_ListPetInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_pet_service.proto",
}
