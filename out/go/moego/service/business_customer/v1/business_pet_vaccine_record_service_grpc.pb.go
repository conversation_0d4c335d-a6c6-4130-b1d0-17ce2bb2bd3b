// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_vaccine_record_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetVaccineRecordServiceClient is the client API for BusinessPetVaccineRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetVaccineRecordServiceClient interface {
	// Get a vaccine record
	GetPetVaccineRecord(ctx context.Context, in *GetPetVaccineRecordRequest, opts ...grpc.CallOption) (*GetPetVaccineRecordResponse, error)
	// List vaccine record of a pet
	ListPetVaccineRecord(ctx context.Context, in *ListPetVaccineRecordRequest, opts ...grpc.CallOption) (*ListPetVaccineRecordResponse, error)
	// Batch list vaccine record of several pets
	BatchListVaccineRecord(ctx context.Context, in *BatchListVaccineRecordRequest, opts ...grpc.CallOption) (*BatchListVaccineRecordResponse, error)
	// Batch create vaccine records for a pet
	BatchCreatePetVaccineRecord(ctx context.Context, in *BatchCreatePetVaccineRecordRequest, opts ...grpc.CallOption) (*BatchCreatePetVaccineRecordResponse, error)
	// Update pet vaccine records
	UpdatePetVaccineRecords(ctx context.Context, in *UpdatePetVaccineRecordsRequest, opts ...grpc.CallOption) (*UpdatePetVaccineRecordsResponse, error)
}

type businessPetVaccineRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetVaccineRecordServiceClient(cc grpc.ClientConnInterface) BusinessPetVaccineRecordServiceClient {
	return &businessPetVaccineRecordServiceClient{cc}
}

func (c *businessPetVaccineRecordServiceClient) GetPetVaccineRecord(ctx context.Context, in *GetPetVaccineRecordRequest, opts ...grpc.CallOption) (*GetPetVaccineRecordResponse, error) {
	out := new(GetPetVaccineRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/GetPetVaccineRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineRecordServiceClient) ListPetVaccineRecord(ctx context.Context, in *ListPetVaccineRecordRequest, opts ...grpc.CallOption) (*ListPetVaccineRecordResponse, error) {
	out := new(ListPetVaccineRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/ListPetVaccineRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineRecordServiceClient) BatchListVaccineRecord(ctx context.Context, in *BatchListVaccineRecordRequest, opts ...grpc.CallOption) (*BatchListVaccineRecordResponse, error) {
	out := new(BatchListVaccineRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/BatchListVaccineRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineRecordServiceClient) BatchCreatePetVaccineRecord(ctx context.Context, in *BatchCreatePetVaccineRecordRequest, opts ...grpc.CallOption) (*BatchCreatePetVaccineRecordResponse, error) {
	out := new(BatchCreatePetVaccineRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/BatchCreatePetVaccineRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineRecordServiceClient) UpdatePetVaccineRecords(ctx context.Context, in *UpdatePetVaccineRecordsRequest, opts ...grpc.CallOption) (*UpdatePetVaccineRecordsResponse, error) {
	out := new(UpdatePetVaccineRecordsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/UpdatePetVaccineRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetVaccineRecordServiceServer is the server API for BusinessPetVaccineRecordService service.
// All implementations must embed UnimplementedBusinessPetVaccineRecordServiceServer
// for forward compatibility
type BusinessPetVaccineRecordServiceServer interface {
	// Get a vaccine record
	GetPetVaccineRecord(context.Context, *GetPetVaccineRecordRequest) (*GetPetVaccineRecordResponse, error)
	// List vaccine record of a pet
	ListPetVaccineRecord(context.Context, *ListPetVaccineRecordRequest) (*ListPetVaccineRecordResponse, error)
	// Batch list vaccine record of several pets
	BatchListVaccineRecord(context.Context, *BatchListVaccineRecordRequest) (*BatchListVaccineRecordResponse, error)
	// Batch create vaccine records for a pet
	BatchCreatePetVaccineRecord(context.Context, *BatchCreatePetVaccineRecordRequest) (*BatchCreatePetVaccineRecordResponse, error)
	// Update pet vaccine records
	UpdatePetVaccineRecords(context.Context, *UpdatePetVaccineRecordsRequest) (*UpdatePetVaccineRecordsResponse, error)
	mustEmbedUnimplementedBusinessPetVaccineRecordServiceServer()
}

// UnimplementedBusinessPetVaccineRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetVaccineRecordServiceServer struct {
}

func (UnimplementedBusinessPetVaccineRecordServiceServer) GetPetVaccineRecord(context.Context, *GetPetVaccineRecordRequest) (*GetPetVaccineRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetVaccineRecord not implemented")
}
func (UnimplementedBusinessPetVaccineRecordServiceServer) ListPetVaccineRecord(context.Context, *ListPetVaccineRecordRequest) (*ListPetVaccineRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetVaccineRecord not implemented")
}
func (UnimplementedBusinessPetVaccineRecordServiceServer) BatchListVaccineRecord(context.Context, *BatchListVaccineRecordRequest) (*BatchListVaccineRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchListVaccineRecord not implemented")
}
func (UnimplementedBusinessPetVaccineRecordServiceServer) BatchCreatePetVaccineRecord(context.Context, *BatchCreatePetVaccineRecordRequest) (*BatchCreatePetVaccineRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreatePetVaccineRecord not implemented")
}
func (UnimplementedBusinessPetVaccineRecordServiceServer) UpdatePetVaccineRecords(context.Context, *UpdatePetVaccineRecordsRequest) (*UpdatePetVaccineRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetVaccineRecords not implemented")
}
func (UnimplementedBusinessPetVaccineRecordServiceServer) mustEmbedUnimplementedBusinessPetVaccineRecordServiceServer() {
}

// UnsafeBusinessPetVaccineRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetVaccineRecordServiceServer will
// result in compilation errors.
type UnsafeBusinessPetVaccineRecordServiceServer interface {
	mustEmbedUnimplementedBusinessPetVaccineRecordServiceServer()
}

func RegisterBusinessPetVaccineRecordServiceServer(s grpc.ServiceRegistrar, srv BusinessPetVaccineRecordServiceServer) {
	s.RegisterService(&BusinessPetVaccineRecordService_ServiceDesc, srv)
}

func _BusinessPetVaccineRecordService_GetPetVaccineRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetVaccineRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineRecordServiceServer).GetPetVaccineRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/GetPetVaccineRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineRecordServiceServer).GetPetVaccineRecord(ctx, req.(*GetPetVaccineRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineRecordService_ListPetVaccineRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetVaccineRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineRecordServiceServer).ListPetVaccineRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/ListPetVaccineRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineRecordServiceServer).ListPetVaccineRecord(ctx, req.(*ListPetVaccineRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineRecordService_BatchListVaccineRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchListVaccineRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineRecordServiceServer).BatchListVaccineRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/BatchListVaccineRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineRecordServiceServer).BatchListVaccineRecord(ctx, req.(*BatchListVaccineRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineRecordService_BatchCreatePetVaccineRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreatePetVaccineRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineRecordServiceServer).BatchCreatePetVaccineRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/BatchCreatePetVaccineRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineRecordServiceServer).BatchCreatePetVaccineRecord(ctx, req.(*BatchCreatePetVaccineRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineRecordService_UpdatePetVaccineRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetVaccineRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineRecordServiceServer).UpdatePetVaccineRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineRecordService/UpdatePetVaccineRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineRecordServiceServer).UpdatePetVaccineRecords(ctx, req.(*UpdatePetVaccineRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetVaccineRecordService_ServiceDesc is the grpc.ServiceDesc for BusinessPetVaccineRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetVaccineRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetVaccineRecordService",
	HandlerType: (*BusinessPetVaccineRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetVaccineRecord",
			Handler:    _BusinessPetVaccineRecordService_GetPetVaccineRecord_Handler,
		},
		{
			MethodName: "ListPetVaccineRecord",
			Handler:    _BusinessPetVaccineRecordService_ListPetVaccineRecord_Handler,
		},
		{
			MethodName: "BatchListVaccineRecord",
			Handler:    _BusinessPetVaccineRecordService_BatchListVaccineRecord_Handler,
		},
		{
			MethodName: "BatchCreatePetVaccineRecord",
			Handler:    _BusinessPetVaccineRecordService_BatchCreatePetVaccineRecord_Handler,
		},
		{
			MethodName: "UpdatePetVaccineRecords",
			Handler:    _BusinessPetVaccineRecordService_UpdatePetVaccineRecords_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_vaccine_record_service.proto",
}
