// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_preferred_frequency_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerPreferredFrequencyServiceClient is the client API for BusinessCustomerPreferredFrequencyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerPreferredFrequencyServiceClient interface {
	// Get customer grooming frequency
	GetCustomerGroomingFrequency(ctx context.Context, in *GetCustomerGroomingFrequencyRequest, opts ...grpc.CallOption) (*GetCustomerGroomingFrequencyResponse, error)
	// Upsert customer grooming frequency
	UpsertCustomerGroomingFrequency(ctx context.Context, in *UpsertCustomerGroomingFrequencyRequest, opts ...grpc.CallOption) (*UpsertCustomerGroomingFrequencyResponse, error)
}

type businessCustomerPreferredFrequencyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerPreferredFrequencyServiceClient(cc grpc.ClientConnInterface) BusinessCustomerPreferredFrequencyServiceClient {
	return &businessCustomerPreferredFrequencyServiceClient{cc}
}

func (c *businessCustomerPreferredFrequencyServiceClient) GetCustomerGroomingFrequency(ctx context.Context, in *GetCustomerGroomingFrequencyRequest, opts ...grpc.CallOption) (*GetCustomerGroomingFrequencyResponse, error) {
	out := new(GetCustomerGroomingFrequencyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService/GetCustomerGroomingFrequency", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerPreferredFrequencyServiceClient) UpsertCustomerGroomingFrequency(ctx context.Context, in *UpsertCustomerGroomingFrequencyRequest, opts ...grpc.CallOption) (*UpsertCustomerGroomingFrequencyResponse, error) {
	out := new(UpsertCustomerGroomingFrequencyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService/UpsertCustomerGroomingFrequency", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerPreferredFrequencyServiceServer is the server API for BusinessCustomerPreferredFrequencyService service.
// All implementations must embed UnimplementedBusinessCustomerPreferredFrequencyServiceServer
// for forward compatibility
type BusinessCustomerPreferredFrequencyServiceServer interface {
	// Get customer grooming frequency
	GetCustomerGroomingFrequency(context.Context, *GetCustomerGroomingFrequencyRequest) (*GetCustomerGroomingFrequencyResponse, error)
	// Upsert customer grooming frequency
	UpsertCustomerGroomingFrequency(context.Context, *UpsertCustomerGroomingFrequencyRequest) (*UpsertCustomerGroomingFrequencyResponse, error)
	mustEmbedUnimplementedBusinessCustomerPreferredFrequencyServiceServer()
}

// UnimplementedBusinessCustomerPreferredFrequencyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerPreferredFrequencyServiceServer struct {
}

func (UnimplementedBusinessCustomerPreferredFrequencyServiceServer) GetCustomerGroomingFrequency(context.Context, *GetCustomerGroomingFrequencyRequest) (*GetCustomerGroomingFrequencyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerGroomingFrequency not implemented")
}
func (UnimplementedBusinessCustomerPreferredFrequencyServiceServer) UpsertCustomerGroomingFrequency(context.Context, *UpsertCustomerGroomingFrequencyRequest) (*UpsertCustomerGroomingFrequencyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertCustomerGroomingFrequency not implemented")
}
func (UnimplementedBusinessCustomerPreferredFrequencyServiceServer) mustEmbedUnimplementedBusinessCustomerPreferredFrequencyServiceServer() {
}

// UnsafeBusinessCustomerPreferredFrequencyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerPreferredFrequencyServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerPreferredFrequencyServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerPreferredFrequencyServiceServer()
}

func RegisterBusinessCustomerPreferredFrequencyServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerPreferredFrequencyServiceServer) {
	s.RegisterService(&BusinessCustomerPreferredFrequencyService_ServiceDesc, srv)
}

func _BusinessCustomerPreferredFrequencyService_GetCustomerGroomingFrequency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerGroomingFrequencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPreferredFrequencyServiceServer).GetCustomerGroomingFrequency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService/GetCustomerGroomingFrequency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPreferredFrequencyServiceServer).GetCustomerGroomingFrequency(ctx, req.(*GetCustomerGroomingFrequencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerPreferredFrequencyService_UpsertCustomerGroomingFrequency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertCustomerGroomingFrequencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerPreferredFrequencyServiceServer).UpsertCustomerGroomingFrequency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService/UpsertCustomerGroomingFrequency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerPreferredFrequencyServiceServer).UpsertCustomerGroomingFrequency(ctx, req.(*UpsertCustomerGroomingFrequencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerPreferredFrequencyService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerPreferredFrequencyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerPreferredFrequencyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService",
	HandlerType: (*BusinessCustomerPreferredFrequencyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerGroomingFrequency",
			Handler:    _BusinessCustomerPreferredFrequencyService_GetCustomerGroomingFrequency_Handler,
		},
		{
			MethodName: "UpsertCustomerGroomingFrequency",
			Handler:    _BusinessCustomerPreferredFrequencyService_UpsertCustomerGroomingFrequency_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_preferred_frequency_service.proto",
}
