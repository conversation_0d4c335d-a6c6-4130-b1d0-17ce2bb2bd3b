// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_customer_merge_service.proto

package businesscustomersvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list duplicate customer groups request
type ListDuplicateCustomerGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// 最多返回多少组
	MaxGroupCount int32 `protobuf:"varint,2,opt,name=max_group_count,json=maxGroupCount,proto3" json:"max_group_count,omitempty"`
	// 在给定的 preferred_business_ids 中的查询 duplicate customers
	// 如果为空，则查询 tenant 下所有的 duplicate customers
	PreferredBusinessIds []int64 `protobuf:"varint,3,rep,packed,name=preferred_business_ids,json=preferredBusinessIds,proto3" json:"preferred_business_ids,omitempty"`
}

func (x *ListDuplicateCustomerGroupsRequest) Reset() {
	*x = ListDuplicateCustomerGroupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDuplicateCustomerGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDuplicateCustomerGroupsRequest) ProtoMessage() {}

func (x *ListDuplicateCustomerGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDuplicateCustomerGroupsRequest.ProtoReflect.Descriptor instead.
func (*ListDuplicateCustomerGroupsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListDuplicateCustomerGroupsRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *ListDuplicateCustomerGroupsRequest) GetMaxGroupCount() int32 {
	if x != nil {
		return x.MaxGroupCount
	}
	return 0
}

func (x *ListDuplicateCustomerGroupsRequest) GetPreferredBusinessIds() []int64 {
	if x != nil {
		return x.PreferredBusinessIds
	}
	return nil
}

// list duplicate customer groups response
type ListDuplicateCustomerGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer groups, no more than max_group_count
	Groups []*v11.DuplicateCustomerGroup `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *ListDuplicateCustomerGroupsResponse) Reset() {
	*x = ListDuplicateCustomerGroupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDuplicateCustomerGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDuplicateCustomerGroupsResponse) ProtoMessage() {}

func (x *ListDuplicateCustomerGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDuplicateCustomerGroupsResponse.ProtoReflect.Descriptor instead.
func (*ListDuplicateCustomerGroupsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListDuplicateCustomerGroupsResponse) GetGroups() []*v11.DuplicateCustomerGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

// check customer duplication request
type CheckCustomerDuplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// customer ids
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
}

func (x *CheckCustomerDuplicationRequest) Reset() {
	*x = CheckCustomerDuplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerDuplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerDuplicationRequest) ProtoMessage() {}

func (x *CheckCustomerDuplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerDuplicationRequest.ProtoReflect.Descriptor instead.
func (*CheckCustomerDuplicationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckCustomerDuplicationRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *CheckCustomerDuplicationRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// check customer duplication response
type CheckCustomerDuplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duplicate
	Duplicate bool `protobuf:"varint,1,opt,name=duplicate,proto3" json:"duplicate,omitempty"`
}

func (x *CheckCustomerDuplicationResponse) Reset() {
	*x = CheckCustomerDuplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCustomerDuplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCustomerDuplicationResponse) ProtoMessage() {}

func (x *CheckCustomerDuplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCustomerDuplicationResponse.ProtoReflect.Descriptor instead.
func (*CheckCustomerDuplicationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckCustomerDuplicationResponse) GetDuplicate() bool {
	if x != nil {
		return x.Duplicate
	}
	return false
}

// preview pet merge relation request
type PreviewPetMergeRelationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// customer merge relation
	CustomerMergeRelation *v11.MergeRelationDef `protobuf:"bytes,2,opt,name=customer_merge_relation,json=customerMergeRelation,proto3" json:"customer_merge_relation,omitempty"`
}

func (x *PreviewPetMergeRelationRequest) Reset() {
	*x = PreviewPetMergeRelationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPetMergeRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPetMergeRelationRequest) ProtoMessage() {}

func (x *PreviewPetMergeRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPetMergeRelationRequest.ProtoReflect.Descriptor instead.
func (*PreviewPetMergeRelationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{4}
}

func (x *PreviewPetMergeRelationRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *PreviewPetMergeRelationRequest) GetCustomerMergeRelation() *v11.MergeRelationDef {
	if x != nil {
		return x.CustomerMergeRelation
	}
	return nil
}

// preview pet merge relation response
type PreviewPetMergeRelationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet merge relations
	PetMergeRelations []*v11.MergeRelationDef `protobuf:"bytes,1,rep,name=pet_merge_relations,json=petMergeRelations,proto3" json:"pet_merge_relations,omitempty"`
}

func (x *PreviewPetMergeRelationResponse) Reset() {
	*x = PreviewPetMergeRelationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPetMergeRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPetMergeRelationResponse) ProtoMessage() {}

func (x *PreviewPetMergeRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPetMergeRelationResponse.ProtoReflect.Descriptor instead.
func (*PreviewPetMergeRelationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{5}
}

func (x *PreviewPetMergeRelationResponse) GetPetMergeRelations() []*v11.MergeRelationDef {
	if x != nil {
		return x.PetMergeRelations
	}
	return nil
}

// merge customers request
type MergeCustomersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// customer merge relation
	CustomerMergeRelation *v11.MergeRelationDef `protobuf:"bytes,2,opt,name=customer_merge_relation,json=customerMergeRelation,proto3" json:"customer_merge_relation,omitempty"`
}

func (x *MergeCustomersRequest) Reset() {
	*x = MergeCustomersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeCustomersRequest) ProtoMessage() {}

func (x *MergeCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeCustomersRequest.ProtoReflect.Descriptor instead.
func (*MergeCustomersRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{6}
}

func (x *MergeCustomersRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *MergeCustomersRequest) GetCustomerMergeRelation() *v11.MergeRelationDef {
	if x != nil {
		return x.CustomerMergeRelation
	}
	return nil
}

// merge customers response
type MergeCustomersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MergeCustomersResponse) Reset() {
	*x = MergeCustomersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeCustomersResponse) ProtoMessage() {}

func (x *MergeCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeCustomersResponse.ProtoReflect.Descriptor instead.
func (*MergeCustomersResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{7}
}

// batch check customer merge status request
type BatchCheckCustomerMergeStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer ids
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
}

func (x *BatchCheckCustomerMergeStatusRequest) Reset() {
	*x = BatchCheckCustomerMergeStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckCustomerMergeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckCustomerMergeStatusRequest) ProtoMessage() {}

func (x *BatchCheckCustomerMergeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckCustomerMergeStatusRequest.ProtoReflect.Descriptor instead.
func (*BatchCheckCustomerMergeStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{8}
}

func (x *BatchCheckCustomerMergeStatusRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// batch check customer merge status response
type BatchCheckCustomerMergeStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// merge status for each customer
	Status map[int64]v11.CustomerMergeStatus `protobuf:"bytes,1,rep,name=status,proto3" json:"status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=moego.models.business_customer.v1.CustomerMergeStatus"`
}

func (x *BatchCheckCustomerMergeStatusResponse) Reset() {
	*x = BatchCheckCustomerMergeStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckCustomerMergeStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckCustomerMergeStatusResponse) ProtoMessage() {}

func (x *BatchCheckCustomerMergeStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckCustomerMergeStatusResponse.ProtoReflect.Descriptor instead.
func (*BatchCheckCustomerMergeStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP(), []int{9}
}

func (x *BatchCheckCustomerMergeStatusResponse) GetStatus() map[int64]v11.CustomerMergeStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_moego_service_business_customer_v1_business_customer_merge_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDesc = []byte{
	0x0a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x44,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x46, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3, 0x01, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46,
	0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x78, 0x0a,
	0x23, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x1f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x10, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x40, 0x0a, 0x20, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x1e, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a,
	0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x75, 0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x86, 0x01, 0x0a,
	0x1f, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x13, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x52, 0x11, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xd6, 0x01, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x75, 0x0a, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x18,
	0x0a, 0x16, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5c, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xe8,
	0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x89, 0x02, 0x0a, 0x25, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x55, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a,
	0x71, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x4c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x32, 0xdd, 0x06, 0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0xae, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa2, 0x01, 0x0a,
	0x17, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x87, 0x01, 0x0a, 0x0e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb4, 0x01, 0x0a, 0x1d,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x9d, 0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x6d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescData = file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_service_business_customer_v1_business_customer_merge_service_proto_goTypes = []interface{}{
	(*ListDuplicateCustomerGroupsRequest)(nil),    // 0: moego.service.business_customer.v1.ListDuplicateCustomerGroupsRequest
	(*ListDuplicateCustomerGroupsResponse)(nil),   // 1: moego.service.business_customer.v1.ListDuplicateCustomerGroupsResponse
	(*CheckCustomerDuplicationRequest)(nil),       // 2: moego.service.business_customer.v1.CheckCustomerDuplicationRequest
	(*CheckCustomerDuplicationResponse)(nil),      // 3: moego.service.business_customer.v1.CheckCustomerDuplicationResponse
	(*PreviewPetMergeRelationRequest)(nil),        // 4: moego.service.business_customer.v1.PreviewPetMergeRelationRequest
	(*PreviewPetMergeRelationResponse)(nil),       // 5: moego.service.business_customer.v1.PreviewPetMergeRelationResponse
	(*MergeCustomersRequest)(nil),                 // 6: moego.service.business_customer.v1.MergeCustomersRequest
	(*MergeCustomersResponse)(nil),                // 7: moego.service.business_customer.v1.MergeCustomersResponse
	(*BatchCheckCustomerMergeStatusRequest)(nil),  // 8: moego.service.business_customer.v1.BatchCheckCustomerMergeStatusRequest
	(*BatchCheckCustomerMergeStatusResponse)(nil), // 9: moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse
	nil,                                // 10: moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse.StatusEntry
	(*v1.Tenant)(nil),                  // 11: moego.models.organization.v1.Tenant
	(*v11.DuplicateCustomerGroup)(nil), // 12: moego.models.business_customer.v1.DuplicateCustomerGroup
	(*v11.MergeRelationDef)(nil),       // 13: moego.models.business_customer.v1.MergeRelationDef
	(v11.CustomerMergeStatus)(0),       // 14: moego.models.business_customer.v1.CustomerMergeStatus
}
var file_moego_service_business_customer_v1_business_customer_merge_service_proto_depIdxs = []int32{
	11, // 0: moego.service.business_customer.v1.ListDuplicateCustomerGroupsRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	12, // 1: moego.service.business_customer.v1.ListDuplicateCustomerGroupsResponse.groups:type_name -> moego.models.business_customer.v1.DuplicateCustomerGroup
	11, // 2: moego.service.business_customer.v1.CheckCustomerDuplicationRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	11, // 3: moego.service.business_customer.v1.PreviewPetMergeRelationRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	13, // 4: moego.service.business_customer.v1.PreviewPetMergeRelationRequest.customer_merge_relation:type_name -> moego.models.business_customer.v1.MergeRelationDef
	13, // 5: moego.service.business_customer.v1.PreviewPetMergeRelationResponse.pet_merge_relations:type_name -> moego.models.business_customer.v1.MergeRelationDef
	11, // 6: moego.service.business_customer.v1.MergeCustomersRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	13, // 7: moego.service.business_customer.v1.MergeCustomersRequest.customer_merge_relation:type_name -> moego.models.business_customer.v1.MergeRelationDef
	10, // 8: moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse.status:type_name -> moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse.StatusEntry
	14, // 9: moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse.StatusEntry.value:type_name -> moego.models.business_customer.v1.CustomerMergeStatus
	0,  // 10: moego.service.business_customer.v1.BusinessCustomerMergeService.ListDuplicateCustomerGroups:input_type -> moego.service.business_customer.v1.ListDuplicateCustomerGroupsRequest
	2,  // 11: moego.service.business_customer.v1.BusinessCustomerMergeService.CheckCustomerDuplication:input_type -> moego.service.business_customer.v1.CheckCustomerDuplicationRequest
	4,  // 12: moego.service.business_customer.v1.BusinessCustomerMergeService.PreviewPetMergeRelation:input_type -> moego.service.business_customer.v1.PreviewPetMergeRelationRequest
	6,  // 13: moego.service.business_customer.v1.BusinessCustomerMergeService.MergeCustomers:input_type -> moego.service.business_customer.v1.MergeCustomersRequest
	8,  // 14: moego.service.business_customer.v1.BusinessCustomerMergeService.BatchCheckCustomerMergeStatus:input_type -> moego.service.business_customer.v1.BatchCheckCustomerMergeStatusRequest
	1,  // 15: moego.service.business_customer.v1.BusinessCustomerMergeService.ListDuplicateCustomerGroups:output_type -> moego.service.business_customer.v1.ListDuplicateCustomerGroupsResponse
	3,  // 16: moego.service.business_customer.v1.BusinessCustomerMergeService.CheckCustomerDuplication:output_type -> moego.service.business_customer.v1.CheckCustomerDuplicationResponse
	5,  // 17: moego.service.business_customer.v1.BusinessCustomerMergeService.PreviewPetMergeRelation:output_type -> moego.service.business_customer.v1.PreviewPetMergeRelationResponse
	7,  // 18: moego.service.business_customer.v1.BusinessCustomerMergeService.MergeCustomers:output_type -> moego.service.business_customer.v1.MergeCustomersResponse
	9,  // 19: moego.service.business_customer.v1.BusinessCustomerMergeService.BatchCheckCustomerMergeStatus:output_type -> moego.service.business_customer.v1.BatchCheckCustomerMergeStatusResponse
	15, // [15:20] is the sub-list for method output_type
	10, // [10:15] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_customer_merge_service_proto_init() }
func file_moego_service_business_customer_v1_business_customer_merge_service_proto_init() {
	if File_moego_service_business_customer_v1_business_customer_merge_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDuplicateCustomerGroupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDuplicateCustomerGroupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerDuplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCustomerDuplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPetMergeRelationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPetMergeRelationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeCustomersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeCustomersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckCustomerMergeStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckCustomerMergeStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_customer_merge_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_customer_merge_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_customer_merge_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_customer_merge_service_proto = out.File
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_customer_merge_service_proto_depIdxs = nil
}
