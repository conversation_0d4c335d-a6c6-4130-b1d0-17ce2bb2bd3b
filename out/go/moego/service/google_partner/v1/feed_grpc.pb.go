// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/google_partner/v1/feed.proto

package googlepartnersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FeedServiceClient is the client API for FeedService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FeedServiceClient interface {
	// Deprecated: Do not use.
	// Send feeds to Google for the given business ids, deprecated
	SendFeeds(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Deprecated: Do not use.
	// Send feeds to Google for all businesses
	SendFeedsForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Send feeds to sandbox environment for the given business ids
	SendFeedsToSandbox(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Send feeds to prod environment for the given business ids
	SendFeedsToProd(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Send feeds to sandbox environment for all businesses
	SendFeedsToSandboxForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Send feeds to prod environment for all businesses
	SendFeedsToProdForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type feedServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFeedServiceClient(cc grpc.ClientConnInterface) FeedServiceClient {
	return &feedServiceClient{cc}
}

// Deprecated: Do not use.
func (c *feedServiceClient) SendFeeds(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *feedServiceClient) SendFeedsForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeedsForAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedServiceClient) SendFeedsToSandbox(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeedsToSandbox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedServiceClient) SendFeedsToProd(ctx context.Context, in *SendFeedsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeedsToProd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedServiceClient) SendFeedsToSandboxForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeedsToSandboxForAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *feedServiceClient) SendFeedsToProdForAll(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.FeedService/SendFeedsToProdForAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeedServiceServer is the server API for FeedService service.
// All implementations must embed UnimplementedFeedServiceServer
// for forward compatibility
type FeedServiceServer interface {
	// Deprecated: Do not use.
	// Send feeds to Google for the given business ids, deprecated
	SendFeeds(context.Context, *SendFeedsRequest) (*emptypb.Empty, error)
	// Deprecated: Do not use.
	// Send feeds to Google for all businesses
	SendFeedsForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Send feeds to sandbox environment for the given business ids
	SendFeedsToSandbox(context.Context, *SendFeedsRequest) (*emptypb.Empty, error)
	// Send feeds to prod environment for the given business ids
	SendFeedsToProd(context.Context, *SendFeedsRequest) (*emptypb.Empty, error)
	// Send feeds to sandbox environment for all businesses
	SendFeedsToSandboxForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Send feeds to prod environment for all businesses
	SendFeedsToProdForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedFeedServiceServer()
}

// UnimplementedFeedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFeedServiceServer struct {
}

func (UnimplementedFeedServiceServer) SendFeeds(context.Context, *SendFeedsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeeds not implemented")
}
func (UnimplementedFeedServiceServer) SendFeedsForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeedsForAll not implemented")
}
func (UnimplementedFeedServiceServer) SendFeedsToSandbox(context.Context, *SendFeedsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeedsToSandbox not implemented")
}
func (UnimplementedFeedServiceServer) SendFeedsToProd(context.Context, *SendFeedsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeedsToProd not implemented")
}
func (UnimplementedFeedServiceServer) SendFeedsToSandboxForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeedsToSandboxForAll not implemented")
}
func (UnimplementedFeedServiceServer) SendFeedsToProdForAll(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFeedsToProdForAll not implemented")
}
func (UnimplementedFeedServiceServer) mustEmbedUnimplementedFeedServiceServer() {}

// UnsafeFeedServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FeedServiceServer will
// result in compilation errors.
type UnsafeFeedServiceServer interface {
	mustEmbedUnimplementedFeedServiceServer()
}

func RegisterFeedServiceServer(s grpc.ServiceRegistrar, srv FeedServiceServer) {
	s.RegisterService(&FeedService_ServiceDesc, srv)
}

func _FeedService_SendFeeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFeedsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeeds(ctx, req.(*SendFeedsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedService_SendFeedsForAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeedsForAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeedsForAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeedsForAll(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedService_SendFeedsToSandbox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFeedsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeedsToSandbox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeedsToSandbox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeedsToSandbox(ctx, req.(*SendFeedsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedService_SendFeedsToProd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFeedsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeedsToProd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeedsToProd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeedsToProd(ctx, req.(*SendFeedsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedService_SendFeedsToSandboxForAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeedsToSandboxForAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeedsToSandboxForAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeedsToSandboxForAll(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeedService_SendFeedsToProdForAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedServiceServer).SendFeedsToProdForAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.FeedService/SendFeedsToProdForAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedServiceServer).SendFeedsToProdForAll(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// FeedService_ServiceDesc is the grpc.ServiceDesc for FeedService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FeedService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.google_partner.v1.FeedService",
	HandlerType: (*FeedServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendFeeds",
			Handler:    _FeedService_SendFeeds_Handler,
		},
		{
			MethodName: "SendFeedsForAll",
			Handler:    _FeedService_SendFeedsForAll_Handler,
		},
		{
			MethodName: "SendFeedsToSandbox",
			Handler:    _FeedService_SendFeedsToSandbox_Handler,
		},
		{
			MethodName: "SendFeedsToProd",
			Handler:    _FeedService_SendFeedsToProd_Handler,
		},
		{
			MethodName: "SendFeedsToSandboxForAll",
			Handler:    _FeedService_SendFeedsToSandboxForAll_Handler,
		},
		{
			MethodName: "SendFeedsToProdForAll",
			Handler:    _FeedService_SendFeedsToProdForAll_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/google_partner/v1/feed.proto",
}
