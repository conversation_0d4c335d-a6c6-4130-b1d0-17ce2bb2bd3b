// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/finance_gw/v1/stripe_webhook_service.proto

package financegwsvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for HandleStripeEvent
type HandleStripeEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The signature of the event request
	Signature string `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	// The raw event body in JSON.
	EventBody []byte `protobuf:"bytes,2,opt,name=event_body,json=eventBody,proto3" json:"event_body,omitempty"`
}

func (x *HandleStripeEventRequest) Reset() {
	*x = HandleStripeEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_gw_v1_stripe_webhook_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleStripeEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleStripeEventRequest) ProtoMessage() {}

func (x *HandleStripeEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_gw_v1_stripe_webhook_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleStripeEventRequest.ProtoReflect.Descriptor instead.
func (*HandleStripeEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleStripeEventRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *HandleStripeEventRequest) GetEventBody() []byte {
	if x != nil {
		return x.EventBody
	}
	return nil
}

var File_moego_service_finance_gw_v1_stripe_webhook_service_proto protoreflect.FileDescriptor

var file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a,
	0x18, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x26, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x7a, 0x02, 0x10, 0x01, 0x52, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x32, 0x7a, 0x0a, 0x14, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x62, 0x0a, 0x11, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x42, 0x88, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31,
	0x3b, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x67, 0x77, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescOnce sync.Once
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescData = file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDesc
)

func file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescGZIP() []byte {
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescOnce.Do(func() {
		file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescData)
	})
	return file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDescData
}

var file_moego_service_finance_gw_v1_stripe_webhook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_service_finance_gw_v1_stripe_webhook_service_proto_goTypes = []interface{}{
	(*HandleStripeEventRequest)(nil), // 0: moego.service.finance_gw.v1.HandleStripeEventRequest
	(*emptypb.Empty)(nil),            // 1: google.protobuf.Empty
}
var file_moego_service_finance_gw_v1_stripe_webhook_service_proto_depIdxs = []int32{
	0, // 0: moego.service.finance_gw.v1.StripeWebhookService.HandleStripeEvent:input_type -> moego.service.finance_gw.v1.HandleStripeEventRequest
	1, // 1: moego.service.finance_gw.v1.StripeWebhookService.HandleStripeEvent:output_type -> google.protobuf.Empty
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_service_finance_gw_v1_stripe_webhook_service_proto_init() }
func file_moego_service_finance_gw_v1_stripe_webhook_service_proto_init() {
	if File_moego_service_finance_gw_v1_stripe_webhook_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_finance_gw_v1_stripe_webhook_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleStripeEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_finance_gw_v1_stripe_webhook_service_proto_goTypes,
		DependencyIndexes: file_moego_service_finance_gw_v1_stripe_webhook_service_proto_depIdxs,
		MessageInfos:      file_moego_service_finance_gw_v1_stripe_webhook_service_proto_msgTypes,
	}.Build()
	File_moego_service_finance_gw_v1_stripe_webhook_service_proto = out.File
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_rawDesc = nil
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_goTypes = nil
	file_moego_service_finance_gw_v1_stripe_webhook_service_proto_depIdxs = nil
}
