// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/ob_staff_availability_service.proto

package onlinebookingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetStaffAvailabilityRequest
type GetStaffAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list, staff will init when staff_id no exist
	StaffIdList []int64 `protobuf:"varint,3,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityRequest) Reset() {
	*x = GetStaffAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityRequest) ProtoMessage() {}

func (x *GetStaffAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetStaffAvailabilityRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffAvailabilityRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityRequest) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

func (x *GetStaffAvailabilityRequest) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// StaffAvailabilityResponse
type GetStaffAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffAvailabilityList []*v1.StaffAvailability `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *GetStaffAvailabilityResponse) Reset() {
	*x = GetStaffAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityResponse) ProtoMessage() {}

func (x *GetStaffAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetStaffAvailabilityResponse) GetStaffAvailabilityList() []*v1.StaffAvailability {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// UpdateStaffAvailabilityRequest
type UpdateStaffAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff available list
	StaffAvailabilityList []*v1.StaffAvailabilityDef `protobuf:"bytes,3,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
}

func (x *UpdateStaffAvailabilityRequest) Reset() {
	*x = UpdateStaffAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityRequest) ProtoMessage() {}

func (x *UpdateStaffAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateStaffAvailabilityRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateStaffAvailabilityRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateStaffAvailabilityRequest) GetStaffAvailabilityList() []*v1.StaffAvailabilityDef {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

// get staff availability status
type GetStaffAvailabilityStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list
	StaffIdList []int64 `protobuf:"varint,2,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
}

func (x *GetStaffAvailabilityStatusRequest) Reset() {
	*x = GetStaffAvailabilityStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityStatusRequest) ProtoMessage() {}

func (x *GetStaffAvailabilityStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityStatusRequest.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetStaffAvailabilityStatusRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityStatusRequest) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

// GetStaffAvailabilityStatusResponse
type GetStaffAvailabilityStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// map staff id to is available
	StaffAvailability map[int64]bool `protobuf:"bytes,1,rep,name=staff_availability,json=staffAvailability,proto3" json:"staff_availability,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetStaffAvailabilityStatusResponse) Reset() {
	*x = GetStaffAvailabilityStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityStatusResponse) ProtoMessage() {}

func (x *GetStaffAvailabilityStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityStatusResponse.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetStaffAvailabilityStatusResponse) GetStaffAvailability() map[int64]bool {
	if x != nil {
		return x.StaffAvailability
	}
	return nil
}

// UpdateStaffAvailabilityResponse
type UpdateStaffAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStaffAvailabilityResponse) Reset() {
	*x = UpdateStaffAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityResponse) ProtoMessage() {}

func (x *UpdateStaffAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{5}
}

// ListSlotFreeServicesRequest
type ListSlotFreeServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *ListSlotFreeServicesRequest) Reset() {
	*x = ListSlotFreeServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesRequest) ProtoMessage() {}

func (x *ListSlotFreeServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesRequest.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListSlotFreeServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListSlotFreeServicesRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// ListSlotFreeServicesResponse
type ListSlotFreeServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,1,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *ListSlotFreeServicesResponse) Reset() {
	*x = ListSlotFreeServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlotFreeServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlotFreeServicesResponse) ProtoMessage() {}

func (x *ListSlotFreeServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlotFreeServicesResponse.ProtoReflect.Descriptor instead.
func (*ListSlotFreeServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListSlotFreeServicesResponse) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesRequest
type UpdateSlotFreeServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// slot free staff service defs
	Defs []*v1.SlotFreeStaffServiceDef `protobuf:"bytes,3,rep,name=defs,proto3" json:"defs,omitempty"`
}

func (x *UpdateSlotFreeServicesRequest) Reset() {
	*x = UpdateSlotFreeServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesRequest) ProtoMessage() {}

func (x *UpdateSlotFreeServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesRequest.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateSlotFreeServicesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateSlotFreeServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateSlotFreeServicesRequest) GetDefs() []*v1.SlotFreeStaffServiceDef {
	if x != nil {
		return x.Defs
	}
	return nil
}

// UpdateSlotFreeServicesResponse
type UpdateSlotFreeServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSlotFreeServicesResponse) Reset() {
	*x = UpdateSlotFreeServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSlotFreeServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSlotFreeServicesResponse) ProtoMessage() {}

func (x *UpdateSlotFreeServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSlotFreeServicesResponse.ProtoReflect.Descriptor instead.
func (*UpdateSlotFreeServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP(), []int{9}
}

var File_moego_service_online_booking_v1_ob_staff_availability_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa1, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x87, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a,
	0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xe8, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x74, 0x0a, 0x17, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x7b, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xf6,
	0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x1a, 0x44, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x21, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6e, 0x0a, 0x1b, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0x69, 0x0a, 0x1c, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x04, 0x64, 0x65,
	0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x04, 0x64, 0x65, 0x66, 0x73, 0x22, 0xc6, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x04, 0x64, 0x65, 0x66,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x04, 0x64, 0x65, 0x66, 0x73, 0x22, 0x20,
	0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x32, 0xab, 0x06, 0x0a, 0x1a, 0x4f, 0x42, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x93, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74,
	0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72,
	0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f,
	0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x72, 0x65, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x94,
	0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescData = file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_service_online_booking_v1_ob_staff_availability_service_proto_goTypes = []interface{}{
	(*GetStaffAvailabilityRequest)(nil),        // 0: moego.service.online_booking.v1.GetStaffAvailabilityRequest
	(*GetStaffAvailabilityResponse)(nil),       // 1: moego.service.online_booking.v1.GetStaffAvailabilityResponse
	(*UpdateStaffAvailabilityRequest)(nil),     // 2: moego.service.online_booking.v1.UpdateStaffAvailabilityRequest
	(*GetStaffAvailabilityStatusRequest)(nil),  // 3: moego.service.online_booking.v1.GetStaffAvailabilityStatusRequest
	(*GetStaffAvailabilityStatusResponse)(nil), // 4: moego.service.online_booking.v1.GetStaffAvailabilityStatusResponse
	(*UpdateStaffAvailabilityResponse)(nil),    // 5: moego.service.online_booking.v1.UpdateStaffAvailabilityResponse
	(*ListSlotFreeServicesRequest)(nil),        // 6: moego.service.online_booking.v1.ListSlotFreeServicesRequest
	(*ListSlotFreeServicesResponse)(nil),       // 7: moego.service.online_booking.v1.ListSlotFreeServicesResponse
	(*UpdateSlotFreeServicesRequest)(nil),      // 8: moego.service.online_booking.v1.UpdateSlotFreeServicesRequest
	(*UpdateSlotFreeServicesResponse)(nil),     // 9: moego.service.online_booking.v1.UpdateSlotFreeServicesResponse
	nil,                                        // 10: moego.service.online_booking.v1.GetStaffAvailabilityStatusResponse.StaffAvailabilityEntry
	(v1.AvailabilityType)(0),                   // 11: moego.models.organization.v1.AvailabilityType
	(*v1.StaffAvailability)(nil),               // 12: moego.models.organization.v1.StaffAvailability
	(*v1.StaffAvailabilityDef)(nil),            // 13: moego.models.organization.v1.StaffAvailabilityDef
	(*v1.SlotFreeStaffServiceDef)(nil),         // 14: moego.models.organization.v1.SlotFreeStaffServiceDef
}
var file_moego_service_online_booking_v1_ob_staff_availability_service_proto_depIdxs = []int32{
	11, // 0: moego.service.online_booking.v1.GetStaffAvailabilityRequest.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	12, // 1: moego.service.online_booking.v1.GetStaffAvailabilityResponse.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailability
	13, // 2: moego.service.online_booking.v1.UpdateStaffAvailabilityRequest.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailabilityDef
	10, // 3: moego.service.online_booking.v1.GetStaffAvailabilityStatusResponse.staff_availability:type_name -> moego.service.online_booking.v1.GetStaffAvailabilityStatusResponse.StaffAvailabilityEntry
	14, // 4: moego.service.online_booking.v1.ListSlotFreeServicesResponse.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	14, // 5: moego.service.online_booking.v1.UpdateSlotFreeServicesRequest.defs:type_name -> moego.models.organization.v1.SlotFreeStaffServiceDef
	0,  // 6: moego.service.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailability:input_type -> moego.service.online_booking.v1.GetStaffAvailabilityRequest
	2,  // 7: moego.service.online_booking.v1.OBStaffAvailabilityService.UpdateStaffAvailability:input_type -> moego.service.online_booking.v1.UpdateStaffAvailabilityRequest
	3,  // 8: moego.service.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailabilityStatus:input_type -> moego.service.online_booking.v1.GetStaffAvailabilityStatusRequest
	6,  // 9: moego.service.online_booking.v1.OBStaffAvailabilityService.ListSlotFreeServices:input_type -> moego.service.online_booking.v1.ListSlotFreeServicesRequest
	8,  // 10: moego.service.online_booking.v1.OBStaffAvailabilityService.UpdateSlotFreeServices:input_type -> moego.service.online_booking.v1.UpdateSlotFreeServicesRequest
	1,  // 11: moego.service.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailability:output_type -> moego.service.online_booking.v1.GetStaffAvailabilityResponse
	5,  // 12: moego.service.online_booking.v1.OBStaffAvailabilityService.UpdateStaffAvailability:output_type -> moego.service.online_booking.v1.UpdateStaffAvailabilityResponse
	4,  // 13: moego.service.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailabilityStatus:output_type -> moego.service.online_booking.v1.GetStaffAvailabilityStatusResponse
	7,  // 14: moego.service.online_booking.v1.OBStaffAvailabilityService.ListSlotFreeServices:output_type -> moego.service.online_booking.v1.ListSlotFreeServicesResponse
	9,  // 15: moego.service.online_booking.v1.OBStaffAvailabilityService.UpdateSlotFreeServices:output_type -> moego.service.online_booking.v1.UpdateSlotFreeServicesResponse
	11, // [11:16] is the sub-list for method output_type
	6,  // [6:11] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_ob_staff_availability_service_proto_init() }
func file_moego_service_online_booking_v1_ob_staff_availability_service_proto_init() {
	if File_moego_service_online_booking_v1_ob_staff_availability_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlotFreeServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSlotFreeServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_ob_staff_availability_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_ob_staff_availability_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_ob_staff_availability_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_ob_staff_availability_service_proto = out.File
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_ob_staff_availability_service_proto_depIdxs = nil
}
