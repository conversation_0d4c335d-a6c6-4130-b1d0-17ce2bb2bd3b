// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/van_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VanServiceClient is the client API for VanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VanServiceClient interface {
	// get van list
	GetVanList(ctx context.Context, in *GetVanListRequest, opts ...grpc.CallOption) (*GetVanListResponse, error)
	// get van list by staff ids
	GetVanListByStaffIds(ctx context.Context, in *GetVanListByStaffIdsRequest, opts ...grpc.CallOption) (*GetVanListByStaffIdsResponse, error)
	// get van list by multi company id
	GetVanListByMultiCompanyId(ctx context.Context, in *GetVanListByMultiCompanyIdRequest, opts ...grpc.CallOption) (*GetVanListByMultiCompanyIdResponse, error)
	// get assigned van for staff id
	GetAssignedVanForStaffId(ctx context.Context, in *GetAssignedVanForStaffIdRequest, opts ...grpc.CallOption) (*GetAssignedVanForStaffIdResponse, error)
	// force assign staff to van
	ForceAssignStaffToVan(ctx context.Context, in *ForceAssignStaffToVanRequest, opts ...grpc.CallOption) (*ForceAssignStaffToVanResponse, error)
}

type vanServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVanServiceClient(cc grpc.ClientConnInterface) VanServiceClient {
	return &vanServiceClient{cc}
}

func (c *vanServiceClient) GetVanList(ctx context.Context, in *GetVanListRequest, opts ...grpc.CallOption) (*GetVanListResponse, error) {
	out := new(GetVanListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.VanService/GetVanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vanServiceClient) GetVanListByStaffIds(ctx context.Context, in *GetVanListByStaffIdsRequest, opts ...grpc.CallOption) (*GetVanListByStaffIdsResponse, error) {
	out := new(GetVanListByStaffIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.VanService/GetVanListByStaffIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vanServiceClient) GetVanListByMultiCompanyId(ctx context.Context, in *GetVanListByMultiCompanyIdRequest, opts ...grpc.CallOption) (*GetVanListByMultiCompanyIdResponse, error) {
	out := new(GetVanListByMultiCompanyIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.VanService/GetVanListByMultiCompanyId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vanServiceClient) GetAssignedVanForStaffId(ctx context.Context, in *GetAssignedVanForStaffIdRequest, opts ...grpc.CallOption) (*GetAssignedVanForStaffIdResponse, error) {
	out := new(GetAssignedVanForStaffIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.VanService/GetAssignedVanForStaffId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vanServiceClient) ForceAssignStaffToVan(ctx context.Context, in *ForceAssignStaffToVanRequest, opts ...grpc.CallOption) (*ForceAssignStaffToVanResponse, error) {
	out := new(ForceAssignStaffToVanResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.VanService/ForceAssignStaffToVan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VanServiceServer is the server API for VanService service.
// All implementations must embed UnimplementedVanServiceServer
// for forward compatibility
type VanServiceServer interface {
	// get van list
	GetVanList(context.Context, *GetVanListRequest) (*GetVanListResponse, error)
	// get van list by staff ids
	GetVanListByStaffIds(context.Context, *GetVanListByStaffIdsRequest) (*GetVanListByStaffIdsResponse, error)
	// get van list by multi company id
	GetVanListByMultiCompanyId(context.Context, *GetVanListByMultiCompanyIdRequest) (*GetVanListByMultiCompanyIdResponse, error)
	// get assigned van for staff id
	GetAssignedVanForStaffId(context.Context, *GetAssignedVanForStaffIdRequest) (*GetAssignedVanForStaffIdResponse, error)
	// force assign staff to van
	ForceAssignStaffToVan(context.Context, *ForceAssignStaffToVanRequest) (*ForceAssignStaffToVanResponse, error)
	mustEmbedUnimplementedVanServiceServer()
}

// UnimplementedVanServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVanServiceServer struct {
}

func (UnimplementedVanServiceServer) GetVanList(context.Context, *GetVanListRequest) (*GetVanListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVanList not implemented")
}
func (UnimplementedVanServiceServer) GetVanListByStaffIds(context.Context, *GetVanListByStaffIdsRequest) (*GetVanListByStaffIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVanListByStaffIds not implemented")
}
func (UnimplementedVanServiceServer) GetVanListByMultiCompanyId(context.Context, *GetVanListByMultiCompanyIdRequest) (*GetVanListByMultiCompanyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVanListByMultiCompanyId not implemented")
}
func (UnimplementedVanServiceServer) GetAssignedVanForStaffId(context.Context, *GetAssignedVanForStaffIdRequest) (*GetAssignedVanForStaffIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignedVanForStaffId not implemented")
}
func (UnimplementedVanServiceServer) ForceAssignStaffToVan(context.Context, *ForceAssignStaffToVanRequest) (*ForceAssignStaffToVanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceAssignStaffToVan not implemented")
}
func (UnimplementedVanServiceServer) mustEmbedUnimplementedVanServiceServer() {}

// UnsafeVanServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VanServiceServer will
// result in compilation errors.
type UnsafeVanServiceServer interface {
	mustEmbedUnimplementedVanServiceServer()
}

func RegisterVanServiceServer(s grpc.ServiceRegistrar, srv VanServiceServer) {
	s.RegisterService(&VanService_ServiceDesc, srv)
}

func _VanService_GetVanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVanListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VanServiceServer).GetVanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.VanService/GetVanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VanServiceServer).GetVanList(ctx, req.(*GetVanListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VanService_GetVanListByStaffIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVanListByStaffIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VanServiceServer).GetVanListByStaffIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.VanService/GetVanListByStaffIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VanServiceServer).GetVanListByStaffIds(ctx, req.(*GetVanListByStaffIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VanService_GetVanListByMultiCompanyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVanListByMultiCompanyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VanServiceServer).GetVanListByMultiCompanyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.VanService/GetVanListByMultiCompanyId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VanServiceServer).GetVanListByMultiCompanyId(ctx, req.(*GetVanListByMultiCompanyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VanService_GetAssignedVanForStaffId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssignedVanForStaffIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VanServiceServer).GetAssignedVanForStaffId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.VanService/GetAssignedVanForStaffId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VanServiceServer).GetAssignedVanForStaffId(ctx, req.(*GetAssignedVanForStaffIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VanService_ForceAssignStaffToVan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceAssignStaffToVanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VanServiceServer).ForceAssignStaffToVan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.VanService/ForceAssignStaffToVan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VanServiceServer).ForceAssignStaffToVan(ctx, req.(*ForceAssignStaffToVanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VanService_ServiceDesc is the grpc.ServiceDesc for VanService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VanService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.VanService",
	HandlerType: (*VanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVanList",
			Handler:    _VanService_GetVanList_Handler,
		},
		{
			MethodName: "GetVanListByStaffIds",
			Handler:    _VanService_GetVanListByStaffIds_Handler,
		},
		{
			MethodName: "GetVanListByMultiCompanyId",
			Handler:    _VanService_GetVanListByMultiCompanyId_Handler,
		},
		{
			MethodName: "GetAssignedVanForStaffId",
			Handler:    _VanService_GetAssignedVanForStaffId_Handler,
		},
		{
			MethodName: "ForceAssignStaffToVan",
			Handler:    _VanService_ForceAssignStaffToVan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/van_service.proto",
}
