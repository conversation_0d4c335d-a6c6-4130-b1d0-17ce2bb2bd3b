// @since 2024-03-19 10:44:46
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/date_time_service.proto

package organizationsvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get company current time request
type GetCompanyCurrentDayAndTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetCompanyCurrentDayAndTimeRequest) Reset() {
	*x = GetCompanyCurrentDayAndTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_date_time_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyCurrentDayAndTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyCurrentDayAndTimeRequest) ProtoMessage() {}

func (x *GetCompanyCurrentDayAndTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_date_time_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyCurrentDayAndTimeRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyCurrentDayAndTimeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_date_time_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCompanyCurrentDayAndTimeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get company current time response
type GetCompanyCurrentDayAndTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// current date
	CurrentDate string `protobuf:"bytes,2,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`
	// current minutes
	CurrentMinutes int32 `protobuf:"varint,3,opt,name=current_minutes,json=currentMinutes,proto3" json:"current_minutes,omitempty"`
	// local date time
	LocalDateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=local_date_time,json=localDateTime,proto3" json:"local_date_time,omitempty"`
}

func (x *GetCompanyCurrentDayAndTimeResponse) Reset() {
	*x = GetCompanyCurrentDayAndTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_date_time_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyCurrentDayAndTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyCurrentDayAndTimeResponse) ProtoMessage() {}

func (x *GetCompanyCurrentDayAndTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_date_time_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyCurrentDayAndTimeResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyCurrentDayAndTimeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_date_time_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCompanyCurrentDayAndTimeResponse) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCompanyCurrentDayAndTimeResponse) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

func (x *GetCompanyCurrentDayAndTimeResponse) GetCurrentMinutes() int32 {
	if x != nil {
		return x.CurrentMinutes
	}
	return 0
}

func (x *GetCompanyCurrentDayAndTimeResponse) GetLocalDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LocalDateTime
	}
	return nil
}

var File_moego_service_organization_v1_date_time_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_date_time_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x4c, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0xd4,
	0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65,
	0x73, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x32, 0xb8, 0x01, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa4, 0x01, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x41, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x79, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_date_time_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_date_time_service_proto_rawDescData = file_moego_service_organization_v1_date_time_service_proto_rawDesc
)

func file_moego_service_organization_v1_date_time_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_date_time_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_date_time_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_date_time_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_date_time_service_proto_rawDescData
}

var file_moego_service_organization_v1_date_time_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_organization_v1_date_time_service_proto_goTypes = []interface{}{
	(*GetCompanyCurrentDayAndTimeRequest)(nil),  // 0: moego.service.organization.v1.GetCompanyCurrentDayAndTimeRequest
	(*GetCompanyCurrentDayAndTimeResponse)(nil), // 1: moego.service.organization.v1.GetCompanyCurrentDayAndTimeResponse
	(*timestamppb.Timestamp)(nil),               // 2: google.protobuf.Timestamp
}
var file_moego_service_organization_v1_date_time_service_proto_depIdxs = []int32{
	2, // 0: moego.service.organization.v1.GetCompanyCurrentDayAndTimeResponse.local_date_time:type_name -> google.protobuf.Timestamp
	0, // 1: moego.service.organization.v1.DateTimeService.GetCompanyCurrentDayAndTime:input_type -> moego.service.organization.v1.GetCompanyCurrentDayAndTimeRequest
	1, // 2: moego.service.organization.v1.DateTimeService.GetCompanyCurrentDayAndTime:output_type -> moego.service.organization.v1.GetCompanyCurrentDayAndTimeResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_date_time_service_proto_init() }
func file_moego_service_organization_v1_date_time_service_proto_init() {
	if File_moego_service_organization_v1_date_time_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_date_time_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyCurrentDayAndTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_date_time_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyCurrentDayAndTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_date_time_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_date_time_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_date_time_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_date_time_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_date_time_service_proto = out.File
	file_moego_service_organization_v1_date_time_service_proto_rawDesc = nil
	file_moego_service_organization_v1_date_time_service_proto_goTypes = nil
	file_moego_service_organization_v1_date_time_service_proto_depIdxs = nil
}
