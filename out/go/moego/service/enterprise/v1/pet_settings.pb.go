// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/pet_settings.proto

package enterprisesvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet codes request
type ListPetCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListPetCodesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPetCodesRequest) Reset() {
	*x = ListPetCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesRequest) ProtoMessage() {}

func (x *ListPetCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesRequest.ProtoReflect.Descriptor instead.
func (*ListPetCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{0}
}

func (x *ListPetCodesRequest) GetFilter() *ListPetCodesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list pet codes response
type ListPetCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code list
	PetCodes []*v1.PetCode `protobuf:"bytes,1,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
}

func (x *ListPetCodesResponse) Reset() {
	*x = ListPetCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesResponse) ProtoMessage() {}

func (x *ListPetCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesResponse.ProtoReflect.Descriptor instead.
func (*ListPetCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetCodesResponse) GetPetCodes() []*v1.PetCode {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

// create pet code request
type CreatePetCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// pet code
	PetCode *v1.PetCodeCreateDef `protobuf:"bytes,2,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *CreatePetCodeRequest) Reset() {
	*x = CreatePetCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCodeRequest) ProtoMessage() {}

func (x *CreatePetCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCodeRequest.ProtoReflect.Descriptor instead.
func (*CreatePetCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePetCodeRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreatePetCodeRequest) GetPetCode() *v1.PetCodeCreateDef {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// create pet code response
type CreatePetCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code
	PetCode *v1.PetCode `protobuf:"bytes,1,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *CreatePetCodeResponse) Reset() {
	*x = CreatePetCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCodeResponse) ProtoMessage() {}

func (x *CreatePetCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCodeResponse.ProtoReflect.Descriptor instead.
func (*CreatePetCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePetCodeResponse) GetPetCode() *v1.PetCode {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// update pet code request
type UpdatePetCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet code
	PetCode *v1.PetCodeUpdateDef `protobuf:"bytes,2,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *UpdatePetCodeRequest) Reset() {
	*x = UpdatePetCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCodeRequest) ProtoMessage() {}

func (x *UpdatePetCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCodeRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePetCodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetCodeRequest) GetPetCode() *v1.PetCodeUpdateDef {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// update pet code response
type UpdatePetCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code
	PetCode *v1.PetCode `protobuf:"bytes,1,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *UpdatePetCodeResponse) Reset() {
	*x = UpdatePetCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCodeResponse) ProtoMessage() {}

func (x *UpdatePetCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCodeResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{5}
}

func (x *UpdatePetCodeResponse) GetPetCode() *v1.PetCode {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// sort pet codes request
type SortPetCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id list
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetCodesRequest) Reset() {
	*x = SortPetCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCodesRequest) ProtoMessage() {}

func (x *SortPetCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCodesRequest.ProtoReflect.Descriptor instead.
func (*SortPetCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{6}
}

func (x *SortPetCodesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet codes response
type SortPetCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetCodesResponse) Reset() {
	*x = SortPetCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCodesResponse) ProtoMessage() {}

func (x *SortPetCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCodesResponse.ProtoReflect.Descriptor instead.
func (*SortPetCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{7}
}

// delete pet code request
type DeletePetCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetCodeRequest) Reset() {
	*x = DeletePetCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCodeRequest) ProtoMessage() {}

func (x *DeletePetCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCodeRequest.ProtoReflect.Descriptor instead.
func (*DeletePetCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePetCodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet code response
type DeletePetCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetCodeResponse) Reset() {
	*x = DeletePetCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCodeResponse) ProtoMessage() {}

func (x *DeletePetCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCodeResponse.ProtoReflect.Descriptor instead.
func (*DeletePetCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{9}
}

// PushPetCodesRequest
type PushPetCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// ids
	PetCodeIds []int64 `protobuf:"varint,2,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPetCodesRequest) Reset() {
	*x = PushPetCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetCodesRequest) ProtoMessage() {}

func (x *PushPetCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetCodesRequest.ProtoReflect.Descriptor instead.
func (*PushPetCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{10}
}

func (x *PushPetCodesRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushPetCodesRequest) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

func (x *PushPetCodesRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushPetCodesResponse
type PushPetCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPetCodesResponse) Reset() {
	*x = PushPetCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetCodesResponse) ProtoMessage() {}

func (x *PushPetCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetCodesResponse.ProtoReflect.Descriptor instead.
func (*PushPetCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{11}
}

func (x *PushPetCodesResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPetCodesResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// init pet codes request
type InitPetCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
}

func (x *InitPetCodesRequest) Reset() {
	*x = InitPetCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPetCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPetCodesRequest) ProtoMessage() {}

func (x *InitPetCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPetCodesRequest.ProtoReflect.Descriptor instead.
func (*InitPetCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{12}
}

func (x *InitPetCodesRequest) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

// init pet codes response
type InitPetCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InitPetCodesResponse) Reset() {
	*x = InitPetCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPetCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPetCodesResponse) ProtoMessage() {}

func (x *InitPetCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPetCodesResponse.ProtoReflect.Descriptor instead.
func (*InitPetCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{13}
}

// create pet metadata request
type CreatePetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// pet metadata
	PetMetadata *v1.PetMetadataCreateDef `protobuf:"bytes,2,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *CreatePetMetadataRequest) Reset() {
	*x = CreatePetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetMetadataRequest) ProtoMessage() {}

func (x *CreatePetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetMetadataRequest.ProtoReflect.Descriptor instead.
func (*CreatePetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{14}
}

func (x *CreatePetMetadataRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreatePetMetadataRequest) GetPetMetadata() *v1.PetMetadataCreateDef {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// create pet metadata response
type CreatePetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata
	PetMetadata *v1.PetMetadata `protobuf:"bytes,1,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *CreatePetMetadataResponse) Reset() {
	*x = CreatePetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetMetadataResponse) ProtoMessage() {}

func (x *CreatePetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetMetadataResponse.ProtoReflect.Descriptor instead.
func (*CreatePetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{15}
}

func (x *CreatePetMetadataResponse) GetPetMetadata() *v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// update pet metadata request
type UpdatePetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet metadata
	PetMetadata *v1.PetMetadataUpdateDef `protobuf:"bytes,2,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *UpdatePetMetadataRequest) Reset() {
	*x = UpdatePetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetMetadataRequest) ProtoMessage() {}

func (x *UpdatePetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetMetadataRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{16}
}

func (x *UpdatePetMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetMetadataRequest) GetPetMetadata() *v1.PetMetadataUpdateDef {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// update pet metadata response
type UpdatePetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata
	PetMetadata *v1.PetMetadata `protobuf:"bytes,1,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *UpdatePetMetadataResponse) Reset() {
	*x = UpdatePetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetMetadataResponse) ProtoMessage() {}

func (x *UpdatePetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetMetadataResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{17}
}

func (x *UpdatePetMetadataResponse) GetPetMetadata() *v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// sort pet metadata request
type SortPetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetMetadataRequest) Reset() {
	*x = SortPetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetMetadataRequest) ProtoMessage() {}

func (x *SortPetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetMetadataRequest.ProtoReflect.Descriptor instead.
func (*SortPetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{18}
}

func (x *SortPetMetadataRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet metadata response
type SortPetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetMetadataResponse) Reset() {
	*x = SortPetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetMetadataResponse) ProtoMessage() {}

func (x *SortPetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetMetadataResponse.ProtoReflect.Descriptor instead.
func (*SortPetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{19}
}

// delete pet metadata request
type DeletePetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetMetadataRequest) Reset() {
	*x = DeletePetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetMetadataRequest) ProtoMessage() {}

func (x *DeletePetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetMetadataRequest.ProtoReflect.Descriptor instead.
func (*DeletePetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{20}
}

func (x *DeletePetMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet metadata response
type DeletePetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetMetadataResponse) Reset() {
	*x = DeletePetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetMetadataResponse) ProtoMessage() {}

func (x *DeletePetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetMetadataResponse.ProtoReflect.Descriptor instead.
func (*DeletePetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{21}
}

// list pet metadata request
type ListPetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListPetMetadataRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPetMetadataRequest) Reset() {
	*x = ListPetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataRequest) ProtoMessage() {}

func (x *ListPetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataRequest.ProtoReflect.Descriptor instead.
func (*ListPetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{22}
}

func (x *ListPetMetadataRequest) GetFilter() *ListPetMetadataRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list pet metadata response
type ListPetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata list
	PetMetadata []*v1.PetMetadata `protobuf:"bytes,1,rep,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *ListPetMetadataResponse) Reset() {
	*x = ListPetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataResponse) ProtoMessage() {}

func (x *ListPetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataResponse.ProtoReflect.Descriptor instead.
func (*ListPetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{23}
}

func (x *ListPetMetadataResponse) GetPetMetadata() []*v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// push pet metadata request
type PushPetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// ids
	PetMetadataIds []int64 `protobuf:"varint,2,rep,packed,name=pet_metadata_ids,json=petMetadataIds,proto3" json:"pet_metadata_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPetMetadataRequest) Reset() {
	*x = PushPetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetMetadataRequest) ProtoMessage() {}

func (x *PushPetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetMetadataRequest.ProtoReflect.Descriptor instead.
func (*PushPetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{24}
}

func (x *PushPetMetadataRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *PushPetMetadataRequest) GetPetMetadataIds() []int64 {
	if x != nil {
		return x.PetMetadataIds
	}
	return nil
}

func (x *PushPetMetadataRequest) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// push pet metadata response
type PushPetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPetMetadataResponse) Reset() {
	*x = PushPetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetMetadataResponse) ProtoMessage() {}

func (x *PushPetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetMetadataResponse.ProtoReflect.Descriptor instead.
func (*PushPetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{25}
}

func (x *PushPetMetadataResponse) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPetMetadataResponse) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// init pet metadata request
type InitPetMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// pet metadata names
	PetMetadataNames []v11.BusinessPetMetadataName `protobuf:"varint,2,rep,packed,name=pet_metadata_names,json=petMetadataNames,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"pet_metadata_names,omitempty"`
}

func (x *InitPetMetadataRequest) Reset() {
	*x = InitPetMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPetMetadataRequest) ProtoMessage() {}

func (x *InitPetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPetMetadataRequest.ProtoReflect.Descriptor instead.
func (*InitPetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{26}
}

func (x *InitPetMetadataRequest) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *InitPetMetadataRequest) GetPetMetadataNames() []v11.BusinessPetMetadataName {
	if x != nil {
		return x.PetMetadataNames
	}
	return nil
}

// init pet metadata response
type InitPetMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InitPetMetadataResponse) Reset() {
	*x = InitPetMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPetMetadataResponse) ProtoMessage() {}

func (x *InitPetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPetMetadataResponse.ProtoReflect.Descriptor instead.
func (*InitPetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{27}
}

// list associated food source request
type ListSurchargeAssociatedFoodSourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *ListSurchargeAssociatedFoodSourceRequest) Reset() {
	*x = ListSurchargeAssociatedFoodSourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceRequest) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceRequest.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{28}
}

func (x *ListSurchargeAssociatedFoodSourceRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// list associated food source response
type ListSurchargeAssociatedFoodSourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,1,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
}

func (x *ListSurchargeAssociatedFoodSourceResponse) Reset() {
	*x = ListSurchargeAssociatedFoodSourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceResponse) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceResponse.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{29}
}

func (x *ListSurchargeAssociatedFoodSourceResponse) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

// filter
type ListPetCodesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListPetCodesRequest_Filter) Reset() {
	*x = ListPetCodesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesRequest_Filter) ProtoMessage() {}

func (x *ListPetCodesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPetCodesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ListPetCodesRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListPetCodesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// filter
type ListPetMetadataRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseIds []int64 `protobuf:"varint,1,rep,packed,name=enterprise_ids,json=enterpriseIds,proto3" json:"enterprise_ids,omitempty"`
	// metadata names
	MetadataNames []v11.BusinessPetMetadataName `protobuf:"varint,2,rep,packed,name=metadata_names,json=metadataNames,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"metadata_names,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListPetMetadataRequest_Filter) Reset() {
	*x = ListPetMetadataRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataRequest_Filter) ProtoMessage() {}

func (x *ListPetMetadataRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPetMetadataRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ListPetMetadataRequest_Filter) GetEnterpriseIds() []int64 {
	if x != nil {
		return x.EnterpriseIds
	}
	return nil
}

func (x *ListPetMetadataRequest_Filter) GetMetadataNames() []v11.BusinessPetMetadataName {
	if x != nil {
		return x.MetadataNames
	}
	return nil
}

func (x *ListPetMetadataRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_moego_service_enterprise_v1_pet_settings_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_pet_settings_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x43, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xa9, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x41, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x58, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x70,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x51,
	0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x57, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x51, 0x0a, 0x08,
	0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x57, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x37, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x0a, 0x14, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x74, 0x0a, 0x14, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x3c, 0x0a, 0x13,
	0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x49, 0x6e,
	0x69, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x5d, 0x0a,
	0x0c, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x67, 0x0a, 0x19,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x92, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5d, 0x0a, 0x0c, 0x70,
	0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x67, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x3c, 0x0a, 0x16, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92,
	0x01, 0x0a, 0x08, 0x01, 0x10, 0x64, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0x0a, 0x18,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9d,
	0x02, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5c, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xa4, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x61, 0x0a, 0x0e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x65,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb4, 0x01, 0x0a, 0x16, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x77, 0x0a, 0x17,
	0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x16, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x68, 0x0a, 0x12, 0x70, 0x65, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x10, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x22, 0x19, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x58, 0x0a, 0x28,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x29, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x42, 0x89, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_pet_settings_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_pet_settings_proto_rawDescData = file_moego_service_enterprise_v1_pet_settings_proto_rawDesc
)

func file_moego_service_enterprise_v1_pet_settings_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_pet_settings_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_pet_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_pet_settings_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_pet_settings_proto_rawDescData
}

var file_moego_service_enterprise_v1_pet_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_moego_service_enterprise_v1_pet_settings_proto_goTypes = []interface{}{
	(*ListPetCodesRequest)(nil),                       // 0: moego.service.enterprise.v1.ListPetCodesRequest
	(*ListPetCodesResponse)(nil),                      // 1: moego.service.enterprise.v1.ListPetCodesResponse
	(*CreatePetCodeRequest)(nil),                      // 2: moego.service.enterprise.v1.CreatePetCodeRequest
	(*CreatePetCodeResponse)(nil),                     // 3: moego.service.enterprise.v1.CreatePetCodeResponse
	(*UpdatePetCodeRequest)(nil),                      // 4: moego.service.enterprise.v1.UpdatePetCodeRequest
	(*UpdatePetCodeResponse)(nil),                     // 5: moego.service.enterprise.v1.UpdatePetCodeResponse
	(*SortPetCodesRequest)(nil),                       // 6: moego.service.enterprise.v1.SortPetCodesRequest
	(*SortPetCodesResponse)(nil),                      // 7: moego.service.enterprise.v1.SortPetCodesResponse
	(*DeletePetCodeRequest)(nil),                      // 8: moego.service.enterprise.v1.DeletePetCodeRequest
	(*DeletePetCodeResponse)(nil),                     // 9: moego.service.enterprise.v1.DeletePetCodeResponse
	(*PushPetCodesRequest)(nil),                       // 10: moego.service.enterprise.v1.PushPetCodesRequest
	(*PushPetCodesResponse)(nil),                      // 11: moego.service.enterprise.v1.PushPetCodesResponse
	(*InitPetCodesRequest)(nil),                       // 12: moego.service.enterprise.v1.InitPetCodesRequest
	(*InitPetCodesResponse)(nil),                      // 13: moego.service.enterprise.v1.InitPetCodesResponse
	(*CreatePetMetadataRequest)(nil),                  // 14: moego.service.enterprise.v1.CreatePetMetadataRequest
	(*CreatePetMetadataResponse)(nil),                 // 15: moego.service.enterprise.v1.CreatePetMetadataResponse
	(*UpdatePetMetadataRequest)(nil),                  // 16: moego.service.enterprise.v1.UpdatePetMetadataRequest
	(*UpdatePetMetadataResponse)(nil),                 // 17: moego.service.enterprise.v1.UpdatePetMetadataResponse
	(*SortPetMetadataRequest)(nil),                    // 18: moego.service.enterprise.v1.SortPetMetadataRequest
	(*SortPetMetadataResponse)(nil),                   // 19: moego.service.enterprise.v1.SortPetMetadataResponse
	(*DeletePetMetadataRequest)(nil),                  // 20: moego.service.enterprise.v1.DeletePetMetadataRequest
	(*DeletePetMetadataResponse)(nil),                 // 21: moego.service.enterprise.v1.DeletePetMetadataResponse
	(*ListPetMetadataRequest)(nil),                    // 22: moego.service.enterprise.v1.ListPetMetadataRequest
	(*ListPetMetadataResponse)(nil),                   // 23: moego.service.enterprise.v1.ListPetMetadataResponse
	(*PushPetMetadataRequest)(nil),                    // 24: moego.service.enterprise.v1.PushPetMetadataRequest
	(*PushPetMetadataResponse)(nil),                   // 25: moego.service.enterprise.v1.PushPetMetadataResponse
	(*InitPetMetadataRequest)(nil),                    // 26: moego.service.enterprise.v1.InitPetMetadataRequest
	(*InitPetMetadataResponse)(nil),                   // 27: moego.service.enterprise.v1.InitPetMetadataResponse
	(*ListSurchargeAssociatedFoodSourceRequest)(nil),  // 28: moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceRequest
	(*ListSurchargeAssociatedFoodSourceResponse)(nil), // 29: moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceResponse
	(*ListPetCodesRequest_Filter)(nil),                // 30: moego.service.enterprise.v1.ListPetCodesRequest.Filter
	(*ListPetMetadataRequest_Filter)(nil),             // 31: moego.service.enterprise.v1.ListPetMetadataRequest.Filter
	(*v1.PetCode)(nil),                                // 32: moego.models.enterprise.v1.PetCode
	(*v1.PetCodeCreateDef)(nil),                       // 33: moego.models.enterprise.v1.PetCodeCreateDef
	(*v1.PetCodeUpdateDef)(nil),                       // 34: moego.models.enterprise.v1.PetCodeUpdateDef
	(*v1.TenantObject)(nil),                           // 35: moego.models.enterprise.v1.TenantObject
	(*v1.PetMetadataCreateDef)(nil),                   // 36: moego.models.enterprise.v1.PetMetadataCreateDef
	(*v1.PetMetadata)(nil),                            // 37: moego.models.enterprise.v1.PetMetadata
	(*v1.PetMetadataUpdateDef)(nil),                   // 38: moego.models.enterprise.v1.PetMetadataUpdateDef
	(v11.BusinessPetMetadataName)(0),                  // 39: moego.models.business_customer.v1.BusinessPetMetadataName
}
var file_moego_service_enterprise_v1_pet_settings_proto_depIdxs = []int32{
	30, // 0: moego.service.enterprise.v1.ListPetCodesRequest.filter:type_name -> moego.service.enterprise.v1.ListPetCodesRequest.Filter
	32, // 1: moego.service.enterprise.v1.ListPetCodesResponse.pet_codes:type_name -> moego.models.enterprise.v1.PetCode
	33, // 2: moego.service.enterprise.v1.CreatePetCodeRequest.pet_code:type_name -> moego.models.enterprise.v1.PetCodeCreateDef
	32, // 3: moego.service.enterprise.v1.CreatePetCodeResponse.pet_code:type_name -> moego.models.enterprise.v1.PetCode
	34, // 4: moego.service.enterprise.v1.UpdatePetCodeRequest.pet_code:type_name -> moego.models.enterprise.v1.PetCodeUpdateDef
	32, // 5: moego.service.enterprise.v1.UpdatePetCodeResponse.pet_code:type_name -> moego.models.enterprise.v1.PetCode
	35, // 6: moego.service.enterprise.v1.PushPetCodesRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	36, // 7: moego.service.enterprise.v1.CreatePetMetadataRequest.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadataCreateDef
	37, // 8: moego.service.enterprise.v1.CreatePetMetadataResponse.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	38, // 9: moego.service.enterprise.v1.UpdatePetMetadataRequest.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadataUpdateDef
	37, // 10: moego.service.enterprise.v1.UpdatePetMetadataResponse.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	31, // 11: moego.service.enterprise.v1.ListPetMetadataRequest.filter:type_name -> moego.service.enterprise.v1.ListPetMetadataRequest.Filter
	37, // 12: moego.service.enterprise.v1.ListPetMetadataResponse.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	35, // 13: moego.service.enterprise.v1.PushPetMetadataRequest.targets:type_name -> moego.models.enterprise.v1.TenantObject
	39, // 14: moego.service.enterprise.v1.InitPetMetadataRequest.pet_metadata_names:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	39, // 15: moego.service.enterprise.v1.ListPetMetadataRequest.Filter.metadata_names:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_pet_settings_proto_init() }
func file_moego_service_enterprise_v1_pet_settings_proto_init() {
	if File_moego_service_enterprise_v1_pet_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPetCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPetCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPetMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPetMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_pet_settings_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_pet_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_service_enterprise_v1_pet_settings_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_pet_settings_proto_depIdxs,
		MessageInfos:      file_moego_service_enterprise_v1_pet_settings_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_pet_settings_proto = out.File
	file_moego_service_enterprise_v1_pet_settings_proto_rawDesc = nil
	file_moego_service_enterprise_v1_pet_settings_proto_goTypes = nil
	file_moego_service_enterprise_v1_pet_settings_proto_depIdxs = nil
}
