// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/territory_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TerritoryServiceClient is the client API for TerritoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TerritoryServiceClient interface {
	// get territory by id
	GetTerritory(ctx context.Context, in *GetTerritoryRequest, opts ...grpc.CallOption) (*GetTerritoryResponse, error)
	// create territory
	CreateTerritory(ctx context.Context, in *CreateTerritoryRequest, opts ...grpc.CallOption) (*CreateTerritoryResponse, error)
	// update territory
	UpdateTerritory(ctx context.Context, in *UpdateTerritoryRequest, opts ...grpc.CallOption) (*UpdateTerritoryResponse, error)
	// get tenant territory
	ListTerritory(ctx context.Context, in *ListTerritoryRequest, opts ...grpc.CallOption) (*ListTerritoryResponse, error)
	// delete territory
	DeleteTerritory(ctx context.Context, in *DeleteTerritoryRequest, opts ...grpc.CallOption) (*DeleteTerritoryResponse, error)
}

type territoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTerritoryServiceClient(cc grpc.ClientConnInterface) TerritoryServiceClient {
	return &territoryServiceClient{cc}
}

func (c *territoryServiceClient) GetTerritory(ctx context.Context, in *GetTerritoryRequest, opts ...grpc.CallOption) (*GetTerritoryResponse, error) {
	out := new(GetTerritoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TerritoryService/GetTerritory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *territoryServiceClient) CreateTerritory(ctx context.Context, in *CreateTerritoryRequest, opts ...grpc.CallOption) (*CreateTerritoryResponse, error) {
	out := new(CreateTerritoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TerritoryService/CreateTerritory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *territoryServiceClient) UpdateTerritory(ctx context.Context, in *UpdateTerritoryRequest, opts ...grpc.CallOption) (*UpdateTerritoryResponse, error) {
	out := new(UpdateTerritoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TerritoryService/UpdateTerritory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *territoryServiceClient) ListTerritory(ctx context.Context, in *ListTerritoryRequest, opts ...grpc.CallOption) (*ListTerritoryResponse, error) {
	out := new(ListTerritoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TerritoryService/ListTerritory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *territoryServiceClient) DeleteTerritory(ctx context.Context, in *DeleteTerritoryRequest, opts ...grpc.CallOption) (*DeleteTerritoryResponse, error) {
	out := new(DeleteTerritoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TerritoryService/DeleteTerritory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerritoryServiceServer is the server API for TerritoryService service.
// All implementations must embed UnimplementedTerritoryServiceServer
// for forward compatibility
type TerritoryServiceServer interface {
	// get territory by id
	GetTerritory(context.Context, *GetTerritoryRequest) (*GetTerritoryResponse, error)
	// create territory
	CreateTerritory(context.Context, *CreateTerritoryRequest) (*CreateTerritoryResponse, error)
	// update territory
	UpdateTerritory(context.Context, *UpdateTerritoryRequest) (*UpdateTerritoryResponse, error)
	// get tenant territory
	ListTerritory(context.Context, *ListTerritoryRequest) (*ListTerritoryResponse, error)
	// delete territory
	DeleteTerritory(context.Context, *DeleteTerritoryRequest) (*DeleteTerritoryResponse, error)
	mustEmbedUnimplementedTerritoryServiceServer()
}

// UnimplementedTerritoryServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTerritoryServiceServer struct {
}

func (UnimplementedTerritoryServiceServer) GetTerritory(context.Context, *GetTerritoryRequest) (*GetTerritoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerritory not implemented")
}
func (UnimplementedTerritoryServiceServer) CreateTerritory(context.Context, *CreateTerritoryRequest) (*CreateTerritoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTerritory not implemented")
}
func (UnimplementedTerritoryServiceServer) UpdateTerritory(context.Context, *UpdateTerritoryRequest) (*UpdateTerritoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTerritory not implemented")
}
func (UnimplementedTerritoryServiceServer) ListTerritory(context.Context, *ListTerritoryRequest) (*ListTerritoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTerritory not implemented")
}
func (UnimplementedTerritoryServiceServer) DeleteTerritory(context.Context, *DeleteTerritoryRequest) (*DeleteTerritoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTerritory not implemented")
}
func (UnimplementedTerritoryServiceServer) mustEmbedUnimplementedTerritoryServiceServer() {}

// UnsafeTerritoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TerritoryServiceServer will
// result in compilation errors.
type UnsafeTerritoryServiceServer interface {
	mustEmbedUnimplementedTerritoryServiceServer()
}

func RegisterTerritoryServiceServer(s grpc.ServiceRegistrar, srv TerritoryServiceServer) {
	s.RegisterService(&TerritoryService_ServiceDesc, srv)
}

func _TerritoryService_GetTerritory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTerritoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerritoryServiceServer).GetTerritory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TerritoryService/GetTerritory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerritoryServiceServer).GetTerritory(ctx, req.(*GetTerritoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerritoryService_CreateTerritory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTerritoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerritoryServiceServer).CreateTerritory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TerritoryService/CreateTerritory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerritoryServiceServer).CreateTerritory(ctx, req.(*CreateTerritoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerritoryService_UpdateTerritory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTerritoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerritoryServiceServer).UpdateTerritory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TerritoryService/UpdateTerritory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerritoryServiceServer).UpdateTerritory(ctx, req.(*UpdateTerritoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerritoryService_ListTerritory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTerritoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerritoryServiceServer).ListTerritory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TerritoryService/ListTerritory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerritoryServiceServer).ListTerritory(ctx, req.(*ListTerritoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerritoryService_DeleteTerritory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTerritoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerritoryServiceServer).DeleteTerritory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TerritoryService/DeleteTerritory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerritoryServiceServer).DeleteTerritory(ctx, req.(*DeleteTerritoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TerritoryService_ServiceDesc is the grpc.ServiceDesc for TerritoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TerritoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.TerritoryService",
	HandlerType: (*TerritoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTerritory",
			Handler:    _TerritoryService_GetTerritory_Handler,
		},
		{
			MethodName: "CreateTerritory",
			Handler:    _TerritoryService_CreateTerritory_Handler,
		},
		{
			MethodName: "UpdateTerritory",
			Handler:    _TerritoryService_UpdateTerritory_Handler,
		},
		{
			MethodName: "ListTerritory",
			Handler:    _TerritoryService_ListTerritory_Handler,
		},
		{
			MethodName: "DeleteTerritory",
			Handler:    _TerritoryService_DeleteTerritory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/territory_service.proto",
}
