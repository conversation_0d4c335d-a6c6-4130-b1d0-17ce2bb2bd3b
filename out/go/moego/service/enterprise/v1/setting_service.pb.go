// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/setting_service.proto

package enterprisesvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// copy intake form
type CopyIntakeFormsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyIntakeFormsRequest) Reset() {
	*x = CopyIntakeFormsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyIntakeFormsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyIntakeFormsRequest) ProtoMessage() {}

func (x *CopyIntakeFormsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyIntakeFormsRequest.ProtoReflect.Descriptor instead.
func (*CopyIntakeFormsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{0}
}

func (x *CopyIntakeFormsRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyIntakeFormsRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy intake form
type CopyIntakeFormsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyIntakeFormsResponse) Reset() {
	*x = CopyIntakeFormsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyIntakeFormsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyIntakeFormsResponse) ProtoMessage() {}

func (x *CopyIntakeFormsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyIntakeFormsResponse.ProtoReflect.Descriptor instead.
func (*CopyIntakeFormsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{1}
}

// copy discount code
type CopyDiscountCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyDiscountCodesRequest) Reset() {
	*x = CopyDiscountCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyDiscountCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyDiscountCodesRequest) ProtoMessage() {}

func (x *CopyDiscountCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyDiscountCodesRequest.ProtoReflect.Descriptor instead.
func (*CopyDiscountCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{2}
}

func (x *CopyDiscountCodesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyDiscountCodesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy discount code
type CopyDiscountCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyDiscountCodesResponse) Reset() {
	*x = CopyDiscountCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyDiscountCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyDiscountCodesResponse) ProtoMessage() {}

func (x *CopyDiscountCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyDiscountCodesResponse.ProtoReflect.Descriptor instead.
func (*CopyDiscountCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{3}
}

// copy roles
type CopyRolesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyRolesRequest) Reset() {
	*x = CopyRolesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyRolesRequest) ProtoMessage() {}

func (x *CopyRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyRolesRequest.ProtoReflect.Descriptor instead.
func (*CopyRolesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{4}
}

func (x *CopyRolesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyRolesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy roles
type CopyRolesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyRolesResponse) Reset() {
	*x = CopyRolesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyRolesResponse) ProtoMessage() {}

func (x *CopyRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyRolesResponse.ProtoReflect.Descriptor instead.
func (*CopyRolesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{5}
}

// copy packages
type CopyPackagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyPackagesRequest) Reset() {
	*x = CopyPackagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyPackagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyPackagesRequest) ProtoMessage() {}

func (x *CopyPackagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyPackagesRequest.ProtoReflect.Descriptor instead.
func (*CopyPackagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{6}
}

func (x *CopyPackagesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyPackagesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy packages
type CopyPackagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyPackagesResponse) Reset() {
	*x = CopyPackagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyPackagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyPackagesResponse) ProtoMessage() {}

func (x *CopyPackagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyPackagesResponse.ProtoReflect.Descriptor instead.
func (*CopyPackagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{7}
}

// copy membership
type CopyMembershipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyMembershipsRequest) Reset() {
	*x = CopyMembershipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyMembershipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyMembershipsRequest) ProtoMessage() {}

func (x *CopyMembershipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyMembershipsRequest.ProtoReflect.Descriptor instead.
func (*CopyMembershipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{8}
}

func (x *CopyMembershipsRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyMembershipsRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy membership
type CopyMembershipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyMembershipsResponse) Reset() {
	*x = CopyMembershipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyMembershipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyMembershipsResponse) ProtoMessage() {}

func (x *CopyMembershipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyMembershipsResponse.ProtoReflect.Descriptor instead.
func (*CopyMembershipsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{9}
}

// copy product
type CopyProductsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyProductsRequest) Reset() {
	*x = CopyProductsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyProductsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyProductsRequest) ProtoMessage() {}

func (x *CopyProductsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyProductsRequest.ProtoReflect.Descriptor instead.
func (*CopyProductsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{10}
}

func (x *CopyProductsRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyProductsRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy product
type CopyProductsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyProductsResponse) Reset() {
	*x = CopyProductsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyProductsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyProductsResponse) ProtoMessage() {}

func (x *CopyProductsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyProductsResponse.ProtoReflect.Descriptor instead.
func (*CopyProductsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{11}
}

var File_moego_service_enterprise_v1_setting_service_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_setting_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01,
	0x0a, 0x16, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a,
	0x17, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x70,
	0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01,
	0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x6f,
	0x70, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x10, 0x43, 0x6f, 0x70, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa,
	0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x73, 0x22, 0x13, 0x0a, 0x11, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x13, 0x43, 0x6f, 0x70, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33,
	0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x16,
	0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08,
	0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x43,
	0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x13, 0x43, 0x6f, 0x70, 0x79, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33,
	0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xf2, 0x1b, 0x0a, 0x0e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c,
	0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d,
	0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46,
	0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a,
	0x11, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6a, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70,
	0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a,
	0x0c, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x12, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x73, 0x0a, 0x0c, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73,
	0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x70, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x82, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x49, 0x6e, 0x69, 0x74, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a,
	0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a,
	0x0c, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x76, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x50, 0x75,
	0x73, 0x68, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x73, 0x0a, 0x0c, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x69, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82,
	0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7c, 0x0a, 0x0f, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a,
	0x0f, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb2, 0x01, 0x0a, 0x21,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_setting_service_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_setting_service_proto_rawDescData = file_moego_service_enterprise_v1_setting_service_proto_rawDesc
)

func file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_setting_service_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_setting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_setting_service_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescData
}

var file_moego_service_enterprise_v1_setting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_service_enterprise_v1_setting_service_proto_goTypes = []interface{}{
	(*CopyIntakeFormsRequest)(nil),                    // 0: moego.service.enterprise.v1.CopyIntakeFormsRequest
	(*CopyIntakeFormsResponse)(nil),                   // 1: moego.service.enterprise.v1.CopyIntakeFormsResponse
	(*CopyDiscountCodesRequest)(nil),                  // 2: moego.service.enterprise.v1.CopyDiscountCodesRequest
	(*CopyDiscountCodesResponse)(nil),                 // 3: moego.service.enterprise.v1.CopyDiscountCodesResponse
	(*CopyRolesRequest)(nil),                          // 4: moego.service.enterprise.v1.CopyRolesRequest
	(*CopyRolesResponse)(nil),                         // 5: moego.service.enterprise.v1.CopyRolesResponse
	(*CopyPackagesRequest)(nil),                       // 6: moego.service.enterprise.v1.CopyPackagesRequest
	(*CopyPackagesResponse)(nil),                      // 7: moego.service.enterprise.v1.CopyPackagesResponse
	(*CopyMembershipsRequest)(nil),                    // 8: moego.service.enterprise.v1.CopyMembershipsRequest
	(*CopyMembershipsResponse)(nil),                   // 9: moego.service.enterprise.v1.CopyMembershipsResponse
	(*CopyProductsRequest)(nil),                       // 10: moego.service.enterprise.v1.CopyProductsRequest
	(*CopyProductsResponse)(nil),                      // 11: moego.service.enterprise.v1.CopyProductsResponse
	(*CreateLodgingTypeRequest)(nil),                  // 12: moego.service.enterprise.v1.CreateLodgingTypeRequest
	(*UpdateLodgingTypeRequest)(nil),                  // 13: moego.service.enterprise.v1.UpdateLodgingTypeRequest
	(*DeleteLodgingTypeRequest)(nil),                  // 14: moego.service.enterprise.v1.DeleteLodgingTypeRequest
	(*SortLodgingTypesRequest)(nil),                   // 15: moego.service.enterprise.v1.SortLodgingTypesRequest
	(*ListLodgingTypesRequest)(nil),                   // 16: moego.service.enterprise.v1.ListLodgingTypesRequest
	(*PushLodgingTypesRequest)(nil),                   // 17: moego.service.enterprise.v1.PushLodgingTypesRequest
	(*InitLodgingTypesRequest)(nil),                   // 18: moego.service.enterprise.v1.InitLodgingTypesRequest
	(*ListPetCodesRequest)(nil),                       // 19: moego.service.enterprise.v1.ListPetCodesRequest
	(*CreatePetCodeRequest)(nil),                      // 20: moego.service.enterprise.v1.CreatePetCodeRequest
	(*UpdatePetCodeRequest)(nil),                      // 21: moego.service.enterprise.v1.UpdatePetCodeRequest
	(*SortPetCodesRequest)(nil),                       // 22: moego.service.enterprise.v1.SortPetCodesRequest
	(*DeletePetCodeRequest)(nil),                      // 23: moego.service.enterprise.v1.DeletePetCodeRequest
	(*PushPetCodesRequest)(nil),                       // 24: moego.service.enterprise.v1.PushPetCodesRequest
	(*InitPetCodesRequest)(nil),                       // 25: moego.service.enterprise.v1.InitPetCodesRequest
	(*CreatePetMetadataRequest)(nil),                  // 26: moego.service.enterprise.v1.CreatePetMetadataRequest
	(*UpdatePetMetadataRequest)(nil),                  // 27: moego.service.enterprise.v1.UpdatePetMetadataRequest
	(*DeletePetMetadataRequest)(nil),                  // 28: moego.service.enterprise.v1.DeletePetMetadataRequest
	(*ListPetMetadataRequest)(nil),                    // 29: moego.service.enterprise.v1.ListPetMetadataRequest
	(*PushPetMetadataRequest)(nil),                    // 30: moego.service.enterprise.v1.PushPetMetadataRequest
	(*InitPetMetadataRequest)(nil),                    // 31: moego.service.enterprise.v1.InitPetMetadataRequest
	(*SortPetMetadataRequest)(nil),                    // 32: moego.service.enterprise.v1.SortPetMetadataRequest
	(*ListSurchargeAssociatedFoodSourceRequest)(nil),  // 33: moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceRequest
	(*CreateLodgingTypeResponse)(nil),                 // 34: moego.service.enterprise.v1.CreateLodgingTypeResponse
	(*UpdateLodgingTypeResponse)(nil),                 // 35: moego.service.enterprise.v1.UpdateLodgingTypeResponse
	(*DeleteLodgingTypeResponse)(nil),                 // 36: moego.service.enterprise.v1.DeleteLodgingTypeResponse
	(*SortLodgingTypesResponse)(nil),                  // 37: moego.service.enterprise.v1.SortLodgingTypesResponse
	(*ListLodgingTypesResponse)(nil),                  // 38: moego.service.enterprise.v1.ListLodgingTypesResponse
	(*PushLodgingTypesResponse)(nil),                  // 39: moego.service.enterprise.v1.PushLodgingTypesResponse
	(*InitLodgingTypesResponse)(nil),                  // 40: moego.service.enterprise.v1.InitLodgingTypesResponse
	(*ListPetCodesResponse)(nil),                      // 41: moego.service.enterprise.v1.ListPetCodesResponse
	(*CreatePetCodeResponse)(nil),                     // 42: moego.service.enterprise.v1.CreatePetCodeResponse
	(*UpdatePetCodeResponse)(nil),                     // 43: moego.service.enterprise.v1.UpdatePetCodeResponse
	(*SortPetCodesResponse)(nil),                      // 44: moego.service.enterprise.v1.SortPetCodesResponse
	(*DeletePetCodeResponse)(nil),                     // 45: moego.service.enterprise.v1.DeletePetCodeResponse
	(*PushPetCodesResponse)(nil),                      // 46: moego.service.enterprise.v1.PushPetCodesResponse
	(*InitPetCodesResponse)(nil),                      // 47: moego.service.enterprise.v1.InitPetCodesResponse
	(*CreatePetMetadataResponse)(nil),                 // 48: moego.service.enterprise.v1.CreatePetMetadataResponse
	(*UpdatePetMetadataResponse)(nil),                 // 49: moego.service.enterprise.v1.UpdatePetMetadataResponse
	(*DeletePetMetadataResponse)(nil),                 // 50: moego.service.enterprise.v1.DeletePetMetadataResponse
	(*ListPetMetadataResponse)(nil),                   // 51: moego.service.enterprise.v1.ListPetMetadataResponse
	(*PushPetMetadataResponse)(nil),                   // 52: moego.service.enterprise.v1.PushPetMetadataResponse
	(*InitPetMetadataResponse)(nil),                   // 53: moego.service.enterprise.v1.InitPetMetadataResponse
	(*SortPetMetadataResponse)(nil),                   // 54: moego.service.enterprise.v1.SortPetMetadataResponse
	(*ListSurchargeAssociatedFoodSourceResponse)(nil), // 55: moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceResponse
}
var file_moego_service_enterprise_v1_setting_service_proto_depIdxs = []int32{
	0,  // 0: moego.service.enterprise.v1.SettingService.CopyIntakeForms:input_type -> moego.service.enterprise.v1.CopyIntakeFormsRequest
	2,  // 1: moego.service.enterprise.v1.SettingService.CopyDiscountCodes:input_type -> moego.service.enterprise.v1.CopyDiscountCodesRequest
	4,  // 2: moego.service.enterprise.v1.SettingService.CopyRoles:input_type -> moego.service.enterprise.v1.CopyRolesRequest
	6,  // 3: moego.service.enterprise.v1.SettingService.CopyPackages:input_type -> moego.service.enterprise.v1.CopyPackagesRequest
	8,  // 4: moego.service.enterprise.v1.SettingService.CopyMemberships:input_type -> moego.service.enterprise.v1.CopyMembershipsRequest
	10, // 5: moego.service.enterprise.v1.SettingService.CopyProducts:input_type -> moego.service.enterprise.v1.CopyProductsRequest
	12, // 6: moego.service.enterprise.v1.SettingService.CreateLodgingType:input_type -> moego.service.enterprise.v1.CreateLodgingTypeRequest
	13, // 7: moego.service.enterprise.v1.SettingService.UpdateLodgingType:input_type -> moego.service.enterprise.v1.UpdateLodgingTypeRequest
	14, // 8: moego.service.enterprise.v1.SettingService.DeleteLodgingType:input_type -> moego.service.enterprise.v1.DeleteLodgingTypeRequest
	15, // 9: moego.service.enterprise.v1.SettingService.SortLodgingTypes:input_type -> moego.service.enterprise.v1.SortLodgingTypesRequest
	16, // 10: moego.service.enterprise.v1.SettingService.ListLodgingTypes:input_type -> moego.service.enterprise.v1.ListLodgingTypesRequest
	17, // 11: moego.service.enterprise.v1.SettingService.PushLodgingTypes:input_type -> moego.service.enterprise.v1.PushLodgingTypesRequest
	18, // 12: moego.service.enterprise.v1.SettingService.InitLodgingTypes:input_type -> moego.service.enterprise.v1.InitLodgingTypesRequest
	19, // 13: moego.service.enterprise.v1.SettingService.ListPetCodes:input_type -> moego.service.enterprise.v1.ListPetCodesRequest
	20, // 14: moego.service.enterprise.v1.SettingService.CreatePetCode:input_type -> moego.service.enterprise.v1.CreatePetCodeRequest
	21, // 15: moego.service.enterprise.v1.SettingService.UpdatePetCode:input_type -> moego.service.enterprise.v1.UpdatePetCodeRequest
	22, // 16: moego.service.enterprise.v1.SettingService.SortPetCodes:input_type -> moego.service.enterprise.v1.SortPetCodesRequest
	23, // 17: moego.service.enterprise.v1.SettingService.DeletePetCode:input_type -> moego.service.enterprise.v1.DeletePetCodeRequest
	24, // 18: moego.service.enterprise.v1.SettingService.PushPetCodes:input_type -> moego.service.enterprise.v1.PushPetCodesRequest
	25, // 19: moego.service.enterprise.v1.SettingService.InitPetCodes:input_type -> moego.service.enterprise.v1.InitPetCodesRequest
	26, // 20: moego.service.enterprise.v1.SettingService.CreatePetMetadata:input_type -> moego.service.enterprise.v1.CreatePetMetadataRequest
	27, // 21: moego.service.enterprise.v1.SettingService.UpdatePetMetadata:input_type -> moego.service.enterprise.v1.UpdatePetMetadataRequest
	28, // 22: moego.service.enterprise.v1.SettingService.DeletePetMetadata:input_type -> moego.service.enterprise.v1.DeletePetMetadataRequest
	29, // 23: moego.service.enterprise.v1.SettingService.ListPetMetadata:input_type -> moego.service.enterprise.v1.ListPetMetadataRequest
	30, // 24: moego.service.enterprise.v1.SettingService.PushPetMetadata:input_type -> moego.service.enterprise.v1.PushPetMetadataRequest
	31, // 25: moego.service.enterprise.v1.SettingService.InitPetMetadata:input_type -> moego.service.enterprise.v1.InitPetMetadataRequest
	32, // 26: moego.service.enterprise.v1.SettingService.SortPetMetadata:input_type -> moego.service.enterprise.v1.SortPetMetadataRequest
	33, // 27: moego.service.enterprise.v1.SettingService.ListSurchargeAssociatedFoodSource:input_type -> moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceRequest
	1,  // 28: moego.service.enterprise.v1.SettingService.CopyIntakeForms:output_type -> moego.service.enterprise.v1.CopyIntakeFormsResponse
	3,  // 29: moego.service.enterprise.v1.SettingService.CopyDiscountCodes:output_type -> moego.service.enterprise.v1.CopyDiscountCodesResponse
	5,  // 30: moego.service.enterprise.v1.SettingService.CopyRoles:output_type -> moego.service.enterprise.v1.CopyRolesResponse
	7,  // 31: moego.service.enterprise.v1.SettingService.CopyPackages:output_type -> moego.service.enterprise.v1.CopyPackagesResponse
	9,  // 32: moego.service.enterprise.v1.SettingService.CopyMemberships:output_type -> moego.service.enterprise.v1.CopyMembershipsResponse
	11, // 33: moego.service.enterprise.v1.SettingService.CopyProducts:output_type -> moego.service.enterprise.v1.CopyProductsResponse
	34, // 34: moego.service.enterprise.v1.SettingService.CreateLodgingType:output_type -> moego.service.enterprise.v1.CreateLodgingTypeResponse
	35, // 35: moego.service.enterprise.v1.SettingService.UpdateLodgingType:output_type -> moego.service.enterprise.v1.UpdateLodgingTypeResponse
	36, // 36: moego.service.enterprise.v1.SettingService.DeleteLodgingType:output_type -> moego.service.enterprise.v1.DeleteLodgingTypeResponse
	37, // 37: moego.service.enterprise.v1.SettingService.SortLodgingTypes:output_type -> moego.service.enterprise.v1.SortLodgingTypesResponse
	38, // 38: moego.service.enterprise.v1.SettingService.ListLodgingTypes:output_type -> moego.service.enterprise.v1.ListLodgingTypesResponse
	39, // 39: moego.service.enterprise.v1.SettingService.PushLodgingTypes:output_type -> moego.service.enterprise.v1.PushLodgingTypesResponse
	40, // 40: moego.service.enterprise.v1.SettingService.InitLodgingTypes:output_type -> moego.service.enterprise.v1.InitLodgingTypesResponse
	41, // 41: moego.service.enterprise.v1.SettingService.ListPetCodes:output_type -> moego.service.enterprise.v1.ListPetCodesResponse
	42, // 42: moego.service.enterprise.v1.SettingService.CreatePetCode:output_type -> moego.service.enterprise.v1.CreatePetCodeResponse
	43, // 43: moego.service.enterprise.v1.SettingService.UpdatePetCode:output_type -> moego.service.enterprise.v1.UpdatePetCodeResponse
	44, // 44: moego.service.enterprise.v1.SettingService.SortPetCodes:output_type -> moego.service.enterprise.v1.SortPetCodesResponse
	45, // 45: moego.service.enterprise.v1.SettingService.DeletePetCode:output_type -> moego.service.enterprise.v1.DeletePetCodeResponse
	46, // 46: moego.service.enterprise.v1.SettingService.PushPetCodes:output_type -> moego.service.enterprise.v1.PushPetCodesResponse
	47, // 47: moego.service.enterprise.v1.SettingService.InitPetCodes:output_type -> moego.service.enterprise.v1.InitPetCodesResponse
	48, // 48: moego.service.enterprise.v1.SettingService.CreatePetMetadata:output_type -> moego.service.enterprise.v1.CreatePetMetadataResponse
	49, // 49: moego.service.enterprise.v1.SettingService.UpdatePetMetadata:output_type -> moego.service.enterprise.v1.UpdatePetMetadataResponse
	50, // 50: moego.service.enterprise.v1.SettingService.DeletePetMetadata:output_type -> moego.service.enterprise.v1.DeletePetMetadataResponse
	51, // 51: moego.service.enterprise.v1.SettingService.ListPetMetadata:output_type -> moego.service.enterprise.v1.ListPetMetadataResponse
	52, // 52: moego.service.enterprise.v1.SettingService.PushPetMetadata:output_type -> moego.service.enterprise.v1.PushPetMetadataResponse
	53, // 53: moego.service.enterprise.v1.SettingService.InitPetMetadata:output_type -> moego.service.enterprise.v1.InitPetMetadataResponse
	54, // 54: moego.service.enterprise.v1.SettingService.SortPetMetadata:output_type -> moego.service.enterprise.v1.SortPetMetadataResponse
	55, // 55: moego.service.enterprise.v1.SettingService.ListSurchargeAssociatedFoodSource:output_type -> moego.service.enterprise.v1.ListSurchargeAssociatedFoodSourceResponse
	28, // [28:56] is the sub-list for method output_type
	0,  // [0:28] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_setting_service_proto_init() }
func file_moego_service_enterprise_v1_setting_service_proto_init() {
	if File_moego_service_enterprise_v1_setting_service_proto != nil {
		return
	}
	file_moego_service_enterprise_v1_pet_settings_proto_init()
	file_moego_service_enterprise_v1_service_settings_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyIntakeFormsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyIntakeFormsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyDiscountCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyDiscountCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyRolesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyRolesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyPackagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyPackagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyMembershipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyMembershipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyProductsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyProductsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_setting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_enterprise_v1_setting_service_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_setting_service_proto_depIdxs,
		MessageInfos:      file_moego_service_enterprise_v1_setting_service_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_setting_service_proto = out.File
	file_moego_service_enterprise_v1_setting_service_proto_rawDesc = nil
	file_moego_service_enterprise_v1_setting_service_proto_goTypes = nil
	file_moego_service_enterprise_v1_setting_service_proto_depIdxs = nil
}
