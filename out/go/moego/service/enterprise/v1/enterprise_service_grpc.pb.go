// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/enterprise_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EnterpriseServiceClient is the client API for EnterpriseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EnterpriseServiceClient interface {
	// get enterprise by id
	GetEnterprise(ctx context.Context, in *GetEnterpriseRequest, opts ...grpc.CallOption) (*GetEnterpriseResponse, error)
	// list enterprise
	ListEnterprise(ctx context.Context, in *ListEnterpriseRequest, opts ...grpc.CallOption) (*ListEnterpriseResponse, error)
	// create enterprise
	CreateEnterprise(ctx context.Context, in *CreateEnterpriseRequest, opts ...grpc.CallOption) (*CreateEnterpriseResponse, error)
	// update enterprise
	UpdateEnterprise(ctx context.Context, in *UpdateEnterpriseRequest, opts ...grpc.CallOption) (*UpdateEnterpriseResponse, error)
	// update tenant template
	UpdateTenantTemplate(ctx context.Context, in *UpdateTenantTemplateRequest, opts ...grpc.CallOption) (*UpdateTenantTemplateResponse, error)
	// create tenant template
	CreateTenantTemplate(ctx context.Context, in *CreateTenantTemplateRequest, opts ...grpc.CallOption) (*CreateTenantTemplateResponse, error)
	// list tenant templates
	ListTenantTemplates(ctx context.Context, in *ListTenantTemplatesRequest, opts ...grpc.CallOption) (*ListTenantTemplatesResponse, error)
	// sync franchisee
	SyncFranchisee(ctx context.Context, in *SyncFranchiseeRequest, opts ...grpc.CallOption) (*SyncFranchiseeResponse, error)
	// sync franchisee owner permission
	SyncFranchiseeOwnerPermission(ctx context.Context, in *SyncFranchiseeOwnerPermissionRequest, opts ...grpc.CallOption) (*SyncFranchiseeOwnerPermissionResponse, error)
}

type enterpriseServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnterpriseServiceClient(cc grpc.ClientConnInterface) EnterpriseServiceClient {
	return &enterpriseServiceClient{cc}
}

func (c *enterpriseServiceClient) GetEnterprise(ctx context.Context, in *GetEnterpriseRequest, opts ...grpc.CallOption) (*GetEnterpriseResponse, error) {
	out := new(GetEnterpriseResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/GetEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) ListEnterprise(ctx context.Context, in *ListEnterpriseRequest, opts ...grpc.CallOption) (*ListEnterpriseResponse, error) {
	out := new(ListEnterpriseResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/ListEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) CreateEnterprise(ctx context.Context, in *CreateEnterpriseRequest, opts ...grpc.CallOption) (*CreateEnterpriseResponse, error) {
	out := new(CreateEnterpriseResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/CreateEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) UpdateEnterprise(ctx context.Context, in *UpdateEnterpriseRequest, opts ...grpc.CallOption) (*UpdateEnterpriseResponse, error) {
	out := new(UpdateEnterpriseResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/UpdateEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) UpdateTenantTemplate(ctx context.Context, in *UpdateTenantTemplateRequest, opts ...grpc.CallOption) (*UpdateTenantTemplateResponse, error) {
	out := new(UpdateTenantTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/UpdateTenantTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) CreateTenantTemplate(ctx context.Context, in *CreateTenantTemplateRequest, opts ...grpc.CallOption) (*CreateTenantTemplateResponse, error) {
	out := new(CreateTenantTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/CreateTenantTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) ListTenantTemplates(ctx context.Context, in *ListTenantTemplatesRequest, opts ...grpc.CallOption) (*ListTenantTemplatesResponse, error) {
	out := new(ListTenantTemplatesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/ListTenantTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) SyncFranchisee(ctx context.Context, in *SyncFranchiseeRequest, opts ...grpc.CallOption) (*SyncFranchiseeResponse, error) {
	out := new(SyncFranchiseeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/SyncFranchisee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) SyncFranchiseeOwnerPermission(ctx context.Context, in *SyncFranchiseeOwnerPermissionRequest, opts ...grpc.CallOption) (*SyncFranchiseeOwnerPermissionResponse, error) {
	out := new(SyncFranchiseeOwnerPermissionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.EnterpriseService/SyncFranchiseeOwnerPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnterpriseServiceServer is the server API for EnterpriseService service.
// All implementations must embed UnimplementedEnterpriseServiceServer
// for forward compatibility
type EnterpriseServiceServer interface {
	// get enterprise by id
	GetEnterprise(context.Context, *GetEnterpriseRequest) (*GetEnterpriseResponse, error)
	// list enterprise
	ListEnterprise(context.Context, *ListEnterpriseRequest) (*ListEnterpriseResponse, error)
	// create enterprise
	CreateEnterprise(context.Context, *CreateEnterpriseRequest) (*CreateEnterpriseResponse, error)
	// update enterprise
	UpdateEnterprise(context.Context, *UpdateEnterpriseRequest) (*UpdateEnterpriseResponse, error)
	// update tenant template
	UpdateTenantTemplate(context.Context, *UpdateTenantTemplateRequest) (*UpdateTenantTemplateResponse, error)
	// create tenant template
	CreateTenantTemplate(context.Context, *CreateTenantTemplateRequest) (*CreateTenantTemplateResponse, error)
	// list tenant templates
	ListTenantTemplates(context.Context, *ListTenantTemplatesRequest) (*ListTenantTemplatesResponse, error)
	// sync franchisee
	SyncFranchisee(context.Context, *SyncFranchiseeRequest) (*SyncFranchiseeResponse, error)
	// sync franchisee owner permission
	SyncFranchiseeOwnerPermission(context.Context, *SyncFranchiseeOwnerPermissionRequest) (*SyncFranchiseeOwnerPermissionResponse, error)
	mustEmbedUnimplementedEnterpriseServiceServer()
}

// UnimplementedEnterpriseServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEnterpriseServiceServer struct {
}

func (UnimplementedEnterpriseServiceServer) GetEnterprise(context.Context, *GetEnterpriseRequest) (*GetEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) ListEnterprise(context.Context, *ListEnterpriseRequest) (*ListEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) CreateEnterprise(context.Context, *CreateEnterpriseRequest) (*CreateEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) UpdateEnterprise(context.Context, *UpdateEnterpriseRequest) (*UpdateEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) UpdateTenantTemplate(context.Context, *UpdateTenantTemplateRequest) (*UpdateTenantTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTenantTemplate not implemented")
}
func (UnimplementedEnterpriseServiceServer) CreateTenantTemplate(context.Context, *CreateTenantTemplateRequest) (*CreateTenantTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTenantTemplate not implemented")
}
func (UnimplementedEnterpriseServiceServer) ListTenantTemplates(context.Context, *ListTenantTemplatesRequest) (*ListTenantTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantTemplates not implemented")
}
func (UnimplementedEnterpriseServiceServer) SyncFranchisee(context.Context, *SyncFranchiseeRequest) (*SyncFranchiseeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFranchisee not implemented")
}
func (UnimplementedEnterpriseServiceServer) SyncFranchiseeOwnerPermission(context.Context, *SyncFranchiseeOwnerPermissionRequest) (*SyncFranchiseeOwnerPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFranchiseeOwnerPermission not implemented")
}
func (UnimplementedEnterpriseServiceServer) mustEmbedUnimplementedEnterpriseServiceServer() {}

// UnsafeEnterpriseServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnterpriseServiceServer will
// result in compilation errors.
type UnsafeEnterpriseServiceServer interface {
	mustEmbedUnimplementedEnterpriseServiceServer()
}

func RegisterEnterpriseServiceServer(s grpc.ServiceRegistrar, srv EnterpriseServiceServer) {
	s.RegisterService(&EnterpriseService_ServiceDesc, srv)
}

func _EnterpriseService_GetEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).GetEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/GetEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).GetEnterprise(ctx, req.(*GetEnterpriseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_ListEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnterpriseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).ListEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/ListEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).ListEnterprise(ctx, req.(*ListEnterpriseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_CreateEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEnterpriseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).CreateEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/CreateEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).CreateEnterprise(ctx, req.(*CreateEnterpriseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_UpdateEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnterpriseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).UpdateEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/UpdateEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).UpdateEnterprise(ctx, req.(*UpdateEnterpriseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_UpdateTenantTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTenantTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).UpdateTenantTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/UpdateTenantTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).UpdateTenantTemplate(ctx, req.(*UpdateTenantTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_CreateTenantTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).CreateTenantTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/CreateTenantTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).CreateTenantTemplate(ctx, req.(*CreateTenantTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_ListTenantTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).ListTenantTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/ListTenantTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).ListTenantTemplates(ctx, req.(*ListTenantTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_SyncFranchisee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFranchiseeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).SyncFranchisee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/SyncFranchisee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).SyncFranchisee(ctx, req.(*SyncFranchiseeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_SyncFranchiseeOwnerPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFranchiseeOwnerPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).SyncFranchiseeOwnerPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.EnterpriseService/SyncFranchiseeOwnerPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).SyncFranchiseeOwnerPermission(ctx, req.(*SyncFranchiseeOwnerPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EnterpriseService_ServiceDesc is the grpc.ServiceDesc for EnterpriseService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnterpriseService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.EnterpriseService",
	HandlerType: (*EnterpriseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEnterprise",
			Handler:    _EnterpriseService_GetEnterprise_Handler,
		},
		{
			MethodName: "ListEnterprise",
			Handler:    _EnterpriseService_ListEnterprise_Handler,
		},
		{
			MethodName: "CreateEnterprise",
			Handler:    _EnterpriseService_CreateEnterprise_Handler,
		},
		{
			MethodName: "UpdateEnterprise",
			Handler:    _EnterpriseService_UpdateEnterprise_Handler,
		},
		{
			MethodName: "UpdateTenantTemplate",
			Handler:    _EnterpriseService_UpdateTenantTemplate_Handler,
		},
		{
			MethodName: "CreateTenantTemplate",
			Handler:    _EnterpriseService_CreateTenantTemplate_Handler,
		},
		{
			MethodName: "ListTenantTemplates",
			Handler:    _EnterpriseService_ListTenantTemplates_Handler,
		},
		{
			MethodName: "SyncFranchisee",
			Handler:    _EnterpriseService_SyncFranchisee_Handler,
		},
		{
			MethodName: "SyncFranchiseeOwnerPermission",
			Handler:    _EnterpriseService_SyncFranchiseeOwnerPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/enterprise_service.proto",
}
