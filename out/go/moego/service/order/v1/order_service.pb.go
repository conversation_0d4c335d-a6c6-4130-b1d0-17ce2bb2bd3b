// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v1/order_service.proto

package ordersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Request body for GetOrder service
type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order
	Order *v1.OrderModel `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// line items
	LineItems []*v1.OrderLineItemModel `protobuf:"bytes,2,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// if not calculate again
	NotCalculate *bool `protobuf:"varint,3,opt,name=not_calculate,json=notCalculate,proto3,oneof" json:"not_calculate,omitempty"`
	// auto apply discount code
	AutoApplyDiscount *bool `protobuf:"varint,4,opt,name=auto_apply_discount,json=autoApplyDiscount,proto3,oneof" json:"auto_apply_discount,omitempty"`
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOrderRequest) GetOrder() *v1.OrderModel {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *CreateOrderRequest) GetLineItems() []*v1.OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *CreateOrderRequest) GetNotCalculate() bool {
	if x != nil && x.NotCalculate != nil {
		return *x.NotCalculate
	}
	return false
}

func (x *CreateOrderRequest) GetAutoApplyDiscount() bool {
	if x != nil && x.AutoApplyDiscount != nil {
		return *x.AutoApplyDiscount
	}
	return false
}

// *
// Request body for UpdateOrder service
type UpdateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order
	Order *v1.OrderModel `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// line items
	LineItems []*v1.OrderLineItemModel `protobuf:"bytes,2,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// line taxes
	LineTaxes []*v1.OrderLineTaxModel `protobuf:"bytes,3,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// line discounts
	LineDiscounts []*v1.OrderLineDiscountModel `protobuf:"bytes,4,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// line extra fees
	LineExtraFees []*v1.OrderLineExtraFeeModel `protobuf:"bytes,5,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// last update version for verification
	LatestVersion *int32 `protobuf:"varint,6,opt,name=latest_version,json=latestVersion,proto3,oneof" json:"latest_version,omitempty"`
}

func (x *UpdateOrderRequest) Reset() {
	*x = UpdateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderRequest) ProtoMessage() {}

func (x *UpdateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateOrderRequest) GetOrder() *v1.OrderModel {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *UpdateOrderRequest) GetLineItems() []*v1.OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *UpdateOrderRequest) GetLineTaxes() []*v1.OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *UpdateOrderRequest) GetLineDiscounts() []*v1.OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *UpdateOrderRequest) GetLineExtraFees() []*v1.OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *UpdateOrderRequest) GetLatestVersion() int32 {
	if x != nil && x.LatestVersion != nil {
		return *x.LatestVersion
	}
	return 0
}

// *
// Response body for create order
type CreateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrderResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// *
// Response body for update order
type UpdateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update record count
	Record int32 `protobuf:"varint,1,opt,name=record,proto3" json:"record,omitempty"`
}

func (x *UpdateOrderResponse) Reset() {
	*x = UpdateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderResponse) ProtoMessage() {}

func (x *UpdateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateOrderResponse) GetRecord() int32 {
	if x != nil {
		return x.Record
	}
	return 0
}

// *
// Request body for GetOrder service
type GetOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// order id
	Id *int64 `protobuf:"varint,2,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// source id
	SourceId *int64 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// source type
	SourceType *string `protobuf:"bytes,4,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"`
	// guid
	Guid *string `protobuf:"bytes,5,opt,name=guid,proto3,oneof" json:"guid,omitempty"`
	// status
	Status *int32 `protobuf:"zigzag32,6,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// get latest order(与 source_id and source_type 一起使用)
	Latest *bool `protobuf:"varint,7,opt,name=latest,proto3,oneof" json:"latest,omitempty"`
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetOrderRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetOrderRequest) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetOrderRequest) GetSourceId() int64 {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return 0
}

func (x *GetOrderRequest) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *GetOrderRequest) GetGuid() string {
	if x != nil && x.Guid != nil {
		return *x.Guid
	}
	return ""
}

func (x *GetOrderRequest) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *GetOrderRequest) GetLatest() bool {
	if x != nil && x.Latest != nil {
		return *x.Latest
	}
	return false
}

// *
// get order list request params
type GetOrderListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// order ids
	OrderIds []int64 `protobuf:"varint,2,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// order source type
	SourceType *string `protobuf:"bytes,3,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"`
	// source ids
	SourceIds []int64 `protobuf:"varint,4,rep,packed,name=source_ids,json=sourceIds,proto3" json:"source_ids,omitempty"`
	// if query detail: if true, return order with items, tax, discount, extra fee else only order
	QueryDetail *bool `protobuf:"varint,5,opt,name=query_detail,json=queryDetail,proto3,oneof" json:"query_detail,omitempty"`
	// status
	Status []int32 `protobuf:"varint,6,rep,packed,name=status,proto3" json:"status,omitempty"`
	// include all orders (extra, deposit, etc.), if false, only return origin order
	IncludeExtraOrder *bool `protobuf:"varint,7,opt,name=include_extra_order,json=includeExtraOrder,proto3,oneof" json:"include_extra_order,omitempty"`
}

func (x *GetOrderListRequest) Reset() {
	*x = GetOrderListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderListRequest) ProtoMessage() {}

func (x *GetOrderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderListRequest.ProtoReflect.Descriptor instead.
func (*GetOrderListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetOrderListRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetOrderListRequest) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *GetOrderListRequest) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *GetOrderListRequest) GetSourceIds() []int64 {
	if x != nil {
		return x.SourceIds
	}
	return nil
}

func (x *GetOrderListRequest) GetQueryDetail() bool {
	if x != nil && x.QueryDetail != nil {
		return *x.QueryDetail
	}
	return false
}

func (x *GetOrderListRequest) GetStatus() []int32 {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrderListRequest) GetIncludeExtraOrder() bool {
	if x != nil && x.IncludeExtraOrder != nil {
		return *x.IncludeExtraOrder
	}
	return false
}

// *
// get order list response
type GetOrderListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order list
	OrderList []*v1.OrderDetailModel `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
}

func (x *GetOrderListResponse) Reset() {
	*x = GetOrderListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderListResponse) ProtoMessage() {}

func (x *GetOrderListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderListResponse.ProtoReflect.Descriptor instead.
func (*GetOrderListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetOrderListResponse) GetOrderList() []*v1.OrderDetailModel {
	if x != nil {
		return x.OrderList
	}
	return nil
}

// *
// incremental updating order
type UpdateOrderIncrRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// order for update
	Order *v1.OrderModel `protobuf:"bytes,2,opt,name=order,proto3,oneof" json:"order,omitempty"`
	// line items
	LineItems []*v1.OrderLineItemModel `protobuf:"bytes,3,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// line taxes
	LineTaxes []*v1.OrderLineTaxModel `protobuf:"bytes,4,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// line discounts
	LineDiscounts []*v1.OrderLineDiscountModel `protobuf:"bytes,5,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// line extra fees
	LineExtraFees []*v1.OrderLineExtraFeeModel `protobuf:"bytes,6,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// last update version for verification
	LatestVersion *int32 `protobuf:"varint,7,opt,name=latest_version,json=latestVersion,proto3,oneof" json:"latest_version,omitempty"`
	// true will check amount and return refund channel wont save change
	CheckRefund *bool `protobuf:"varint,8,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *UpdateOrderIncrRequest) Reset() {
	*x = UpdateOrderIncrRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderIncrRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderIncrRequest) ProtoMessage() {}

func (x *UpdateOrderIncrRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderIncrRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderIncrRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateOrderIncrRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *UpdateOrderIncrRequest) GetOrder() *v1.OrderModel {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *UpdateOrderIncrRequest) GetLineItems() []*v1.OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *UpdateOrderIncrRequest) GetLineTaxes() []*v1.OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *UpdateOrderIncrRequest) GetLineDiscounts() []*v1.OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *UpdateOrderIncrRequest) GetLineExtraFees() []*v1.OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *UpdateOrderIncrRequest) GetLatestVersion() int32 {
	if x != nil && x.LatestVersion != nil {
		return *x.LatestVersion
	}
	return 0
}

func (x *UpdateOrderIncrRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// *
// update response
type UpdateOrderIncrResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update records count
	Record int32 `protobuf:"varint,1,opt,name=record,proto3" json:"record,omitempty"`
	// trigger refund
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3" json:"refund_channel,omitempty"`
}

func (x *UpdateOrderIncrResponse) Reset() {
	*x = UpdateOrderIncrResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderIncrResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderIncrResponse) ProtoMessage() {}

func (x *UpdateOrderIncrResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderIncrResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderIncrResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateOrderIncrResponse) GetRecord() int32 {
	if x != nil {
		return x.Record
	}
	return 0
}

func (x *UpdateOrderIncrResponse) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// *
// retail invoice query request params
type GetRetailInvoicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// order type
	Type *string `protobuf:"bytes,3,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// query detail
	QueryDetail *bool `protobuf:"varint,4,opt,name=query_detail,json=queryDetail,proto3,oneof" json:"query_detail,omitempty"`
	// keyword for title and description
	Keyword *string `protobuf:"bytes,5,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// page size
	PageSize *int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// page num
	PageNum *int32 `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3,oneof" json:"page_num,omitempty"`
	// sort by
	SortBy *string `protobuf:"bytes,8,opt,name=sort_by,json=sortBy,proto3,oneof" json:"sort_by,omitempty"`
	// order by
	OrderBy *string `protobuf:"bytes,9,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// query by create time start
	StartTime *int64 `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// query by create time end
	EndTime *int64 `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *GetRetailInvoicesRequest) Reset() {
	*x = GetRetailInvoicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRetailInvoicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRetailInvoicesRequest) ProtoMessage() {}

func (x *GetRetailInvoicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRetailInvoicesRequest.ProtoReflect.Descriptor instead.
func (*GetRetailInvoicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetRetailInvoicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRetailInvoicesRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *GetRetailInvoicesRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *GetRetailInvoicesRequest) GetQueryDetail() bool {
	if x != nil && x.QueryDetail != nil {
		return *x.QueryDetail
	}
	return false
}

func (x *GetRetailInvoicesRequest) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetRetailInvoicesRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *GetRetailInvoicesRequest) GetPageNum() int32 {
	if x != nil && x.PageNum != nil {
		return *x.PageNum
	}
	return 0
}

func (x *GetRetailInvoicesRequest) GetSortBy() string {
	if x != nil && x.SortBy != nil {
		return *x.SortBy
	}
	return ""
}

func (x *GetRetailInvoicesRequest) GetOrderBy() string {
	if x != nil && x.OrderBy != nil {
		return *x.OrderBy
	}
	return ""
}

func (x *GetRetailInvoicesRequest) GetStartTime() int64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *GetRetailInvoicesRequest) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

// *
// retail invoice query response
type GetRetailInvoicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// query total count
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// query result list, paging
	Orders []*v1.OrderDetailModel `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *GetRetailInvoicesResponse) Reset() {
	*x = GetRetailInvoicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRetailInvoicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRetailInvoicesResponse) ProtoMessage() {}

func (x *GetRetailInvoicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRetailInvoicesResponse.ProtoReflect.Descriptor instead.
func (*GetRetailInvoicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetRetailInvoicesResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetRetailInvoicesResponse) GetOrders() []*v1.OrderDetailModel {
	if x != nil {
		return x.Orders
	}
	return nil
}

// *
// query order item detail request params
type GetOrderItemDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id, query by primary key, not necessary
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// order id, query by primary key, not necessary
	OrderIds []int64 `protobuf:"varint,2,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// item id list for batch query
	LineItemIds []int64 `protobuf:"varint,3,rep,packed,name=line_item_ids,json=lineItemIds,proto3" json:"line_item_ids,omitempty"`
	// item type
	ItemType *string `protobuf:"bytes,4,opt,name=item_type,json=itemType,proto3,oneof" json:"item_type,omitempty"`
}

func (x *GetOrderItemDetailRequest) Reset() {
	*x = GetOrderItemDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderItemDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderItemDetailRequest) ProtoMessage() {}

func (x *GetOrderItemDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderItemDetailRequest.ProtoReflect.Descriptor instead.
func (*GetOrderItemDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetOrderItemDetailRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetOrderItemDetailRequest) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *GetOrderItemDetailRequest) GetLineItemIds() []int64 {
	if x != nil {
		return x.LineItemIds
	}
	return nil
}

func (x *GetOrderItemDetailRequest) GetItemType() string {
	if x != nil && x.ItemType != nil {
		return *x.ItemType
	}
	return ""
}

// *
// query order item detail response
type GetOrderItemDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item id list for batch query
	LineItems []*v1.OrderLineItemModel `protobuf:"bytes,1,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// item tax list
	LineTaxes []*v1.OrderLineTaxModel `protobuf:"bytes,2,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
}

func (x *GetOrderItemDetailResponse) Reset() {
	*x = GetOrderItemDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderItemDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderItemDetailResponse) ProtoMessage() {}

func (x *GetOrderItemDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderItemDetailResponse.ProtoReflect.Descriptor instead.
func (*GetOrderItemDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetOrderItemDetailResponse) GetLineItems() []*v1.OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *GetOrderItemDetailResponse) GetLineTaxes() []*v1.OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

// *
// query tips order list request params
type GetTipsOrderListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// source id list, tips only for grooming id
	SourceIds []int64 `protobuf:"varint,2,rep,packed,name=source_ids,json=sourceIds,proto3" json:"source_ids,omitempty"`
	// source type
	SourceType *string `protobuf:"bytes,3,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"`
	// paging num
	PageNum int32 `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	// paging size
	PageSize int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// business id list
	BusinessIds []int64 `protobuf:"varint,6,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetTipsOrderListRequest) Reset() {
	*x = GetTipsOrderListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsOrderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsOrderListRequest) ProtoMessage() {}

func (x *GetTipsOrderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsOrderListRequest.ProtoReflect.Descriptor instead.
func (*GetTipsOrderListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTipsOrderListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetTipsOrderListRequest) GetSourceIds() []int64 {
	if x != nil {
		return x.SourceIds
	}
	return nil
}

func (x *GetTipsOrderListRequest) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *GetTipsOrderListRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetTipsOrderListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetTipsOrderListRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// *
// query tips order list request params
type GetTipsOrderListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total count
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// order list
	Orders []*v1.OrderModel `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *GetTipsOrderListResponse) Reset() {
	*x = GetTipsOrderListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsOrderListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsOrderListResponse) ProtoMessage() {}

func (x *GetTipsOrderListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsOrderListResponse.ProtoReflect.Descriptor instead.
func (*GetTipsOrderListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetTipsOrderListResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetTipsOrderListResponse) GetOrders() []*v1.OrderModel {
	if x != nil {
		return x.Orders
	}
	return nil
}

// modify item tax input
type ModifyItemTaxInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// order id
	OrderId int32 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// service/product/service charge id
	ObjectId int32 `protobuf:"varint,3,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	ItemType string `protobuf:"bytes,4,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// tax id
	TaxId int32 `protobuf:"varint,5,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// operator id
	OperatorId int64 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// trigger refund
	CheckRefund *bool `protobuf:"varint,7,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,8,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ModifyItemTaxInput) Reset() {
	*x = ModifyItemTaxInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyItemTaxInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyItemTaxInput) ProtoMessage() {}

func (x *ModifyItemTaxInput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyItemTaxInput.ProtoReflect.Descriptor instead.
func (*ModifyItemTaxInput) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{15}
}

func (x *ModifyItemTaxInput) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ModifyItemTaxInput) GetOrderId() int32 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *ModifyItemTaxInput) GetObjectId() int32 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *ModifyItemTaxInput) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *ModifyItemTaxInput) GetTaxId() int32 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ModifyItemTaxInput) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *ModifyItemTaxInput) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

func (x *ModifyItemTaxInput) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// modify item tax response
type ModifyItemTaxOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// refund info
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3,oneof" json:"refund_channel,omitempty"`
}

func (x *ModifyItemTaxOutput) Reset() {
	*x = ModifyItemTaxOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyItemTaxOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyItemTaxOutput) ProtoMessage() {}

func (x *ModifyItemTaxOutput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyItemTaxOutput.ProtoReflect.Descriptor instead.
func (*ModifyItemTaxOutput) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{16}
}

func (x *ModifyItemTaxOutput) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *ModifyItemTaxOutput) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// add/remove service charge input
type OperateOrderServiceChargeInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// service charge id list
	ServiceChargeId []int64 `protobuf:"varint,3,rep,packed,name=service_charge_id,json=serviceChargeId,proto3" json:"service_charge_id,omitempty"`
	// operator id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// true will check amount and return refund channel wont save change
	CheckRefund *bool `protobuf:"varint,5,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *OperateOrderServiceChargeInput) Reset() {
	*x = OperateOrderServiceChargeInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateOrderServiceChargeInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateOrderServiceChargeInput) ProtoMessage() {}

func (x *OperateOrderServiceChargeInput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateOrderServiceChargeInput.ProtoReflect.Descriptor instead.
func (*OperateOrderServiceChargeInput) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{17}
}

func (x *OperateOrderServiceChargeInput) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OperateOrderServiceChargeInput) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OperateOrderServiceChargeInput) GetServiceChargeId() []int64 {
	if x != nil {
		return x.ServiceChargeId
	}
	return nil
}

func (x *OperateOrderServiceChargeInput) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *OperateOrderServiceChargeInput) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

func (x *OperateOrderServiceChargeInput) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// add service charge to order output
type OperateOrderServiceChargeOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// trigger refund
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3,oneof" json:"refund_channel,omitempty"`
}

func (x *OperateOrderServiceChargeOutput) Reset() {
	*x = OperateOrderServiceChargeOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateOrderServiceChargeOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateOrderServiceChargeOutput) ProtoMessage() {}

func (x *OperateOrderServiceChargeOutput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateOrderServiceChargeOutput.ProtoReflect.Descriptor instead.
func (*OperateOrderServiceChargeOutput) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{18}
}

func (x *OperateOrderServiceChargeOutput) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *OperateOrderServiceChargeOutput) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// set tip request
type SetTipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// value type
	ValueType string `protobuf:"bytes,4,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,5,opt,name=value,proto3" json:"value,omitempty"`
	// omit result
	OmitResult bool `protobuf:"varint,6,opt,name=omit_result,json=omitResult,proto3" json:"omit_result,omitempty"`
	// last modified time
	LastModifiedTime int64 `protobuf:"varint,7,opt,name=last_modified_time,json=lastModifiedTime,proto3" json:"last_modified_time,omitempty"`
	// true will check amount and return refund channel wont save change
	CheckRefund *bool `protobuf:"varint,8,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *SetTipsRequest) Reset() {
	*x = SetTipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTipsRequest) ProtoMessage() {}

func (x *SetTipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTipsRequest.ProtoReflect.Descriptor instead.
func (*SetTipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{19}
}

func (x *SetTipsRequest) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *SetTipsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SetTipsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SetTipsRequest) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

func (x *SetTipsRequest) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *SetTipsRequest) GetOmitResult() bool {
	if x != nil {
		return x.OmitResult
	}
	return false
}

func (x *SetTipsRequest) GetLastModifiedTime() int64 {
	if x != nil {
		return x.LastModifiedTime
	}
	return 0
}

func (x *SetTipsRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// set tip response
type SetTipsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// trigger refund
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3,oneof" json:"refund_channel,omitempty"`
}

func (x *SetTipsResult) Reset() {
	*x = SetTipsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTipsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTipsResult) ProtoMessage() {}

func (x *SetTipsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTipsResult.ProtoReflect.Descriptor instead.
func (*SetTipsResult) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{20}
}

func (x *SetTipsResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *SetTipsResult) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// ListOrdersRequest
type ListOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// filter
	Filter *ListOrdersRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListOrdersRequest) Reset() {
	*x = ListOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersRequest) ProtoMessage() {}

func (x *ListOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListOrdersRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOrdersRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListOrdersRequest) GetFilter() *ListOrdersRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListOrdersResponse
type ListOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// appointment detail
	Orders []*v1.OrderModel `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *ListOrdersResponse) Reset() {
	*x = ListOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersResponse) ProtoMessage() {}

func (x *ListOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListOrdersResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOrdersResponse) GetOrders() []*v1.OrderModel {
	if x != nil {
		return x.Orders
	}
	return nil
}

// get related order request
type GetOrderHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// origin order id
	OriginOrderId int64 `protobuf:"varint,1,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *GetOrderHistoryRequest) Reset() {
	*x = GetOrderHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderHistoryRequest) ProtoMessage() {}

func (x *GetOrderHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetOrderHistoryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetOrderHistoryRequest) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// get related order response
type GetOrderHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order history view
	OrderModels []*v1.OrderModel `protobuf:"bytes,1,rep,name=order_models,json=orderModels,proto3" json:"order_models,omitempty"`
}

func (x *GetOrderHistoryResponse) Reset() {
	*x = GetOrderHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderHistoryResponse) ProtoMessage() {}

func (x *GetOrderHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetOrderHistoryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetOrderHistoryResponse) GetOrderModels() []*v1.OrderModel {
	if x != nil {
		return x.OrderModels
	}
	return nil
}

// update extra order request
type UpdateExtraOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra order id
	ExtraOrderId int64 `protobuf:"varint,1,opt,name=extra_order_id,json=extraOrderId,proto3" json:"extra_order_id,omitempty"`
	// extra charge model
	Order *v1.OrderModel `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	// line items
	LineItems []*v1.OrderLineItemModel `protobuf:"bytes,3,rep,name=line_items,json=lineItems,proto3" json:"line_items,omitempty"`
	// line taxes
	LineTaxes []*v1.OrderLineTaxModel `protobuf:"bytes,4,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// line discounts
	LineDiscounts []*v1.OrderLineDiscountModel `protobuf:"bytes,5,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// line extra fees
	LineExtraFees []*v1.OrderLineExtraFeeModel `protobuf:"bytes,6,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// added pet detail ids
	AddedPetDetailIds []int64 `protobuf:"varint,7,rep,packed,name=added_pet_detail_ids,json=addedPetDetailIds,proto3" json:"added_pet_detail_ids,omitempty"`
	// deleted pet detail ids
	DeletedPetDetailIds []int64 `protobuf:"varint,8,rep,packed,name=deleted_pet_detail_ids,json=deletedPetDetailIds,proto3" json:"deleted_pet_detail_ids,omitempty"`
}

func (x *UpdateExtraOrderRequest) Reset() {
	*x = UpdateExtraOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExtraOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExtraOrderRequest) ProtoMessage() {}

func (x *UpdateExtraOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExtraOrderRequest.ProtoReflect.Descriptor instead.
func (*UpdateExtraOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateExtraOrderRequest) GetExtraOrderId() int64 {
	if x != nil {
		return x.ExtraOrderId
	}
	return 0
}

func (x *UpdateExtraOrderRequest) GetOrder() *v1.OrderModel {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetLineItems() []*v1.OrderLineItemModel {
	if x != nil {
		return x.LineItems
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetLineTaxes() []*v1.OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetLineDiscounts() []*v1.OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetLineExtraFees() []*v1.OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetAddedPetDetailIds() []int64 {
	if x != nil {
		return x.AddedPetDetailIds
	}
	return nil
}

func (x *UpdateExtraOrderRequest) GetDeletedPetDetailIds() []int64 {
	if x != nil {
		return x.DeletedPetDetailIds
	}
	return nil
}

// update extra order response
type UpdateExtraOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update record count
	Record int32 `protobuf:"varint,1,opt,name=record,proto3" json:"record,omitempty"`
}

func (x *UpdateExtraOrderResponse) Reset() {
	*x = UpdateExtraOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExtraOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExtraOrderResponse) ProtoMessage() {}

func (x *UpdateExtraOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExtraOrderResponse.ProtoReflect.Descriptor instead.
func (*UpdateExtraOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{26}
}

func (x *UpdateExtraOrderResponse) GetRecord() int32 {
	if x != nil {
		return x.Record
	}
	return 0
}

// get grooming detail relation request
type GetGroomingDetailRelationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// is origin order
	IsOriginOrder bool `protobuf:"varint,2,opt,name=is_origin_order,json=isOriginOrder,proto3" json:"is_origin_order,omitempty"`
}

func (x *GetGroomingDetailRelationRequest) Reset() {
	*x = GetGroomingDetailRelationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingDetailRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingDetailRelationRequest) ProtoMessage() {}

func (x *GetGroomingDetailRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingDetailRelationRequest.ProtoReflect.Descriptor instead.
func (*GetGroomingDetailRelationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetGroomingDetailRelationRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetGroomingDetailRelationRequest) GetIsOriginOrder() bool {
	if x != nil {
		return x.IsOriginOrder
	}
	return false
}

// get grooming detail relation response
type GetGroomingDetailRelationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming detail relation models
	GroomingDetailRelationModels []*v1.GroomingDetailRelationModel `protobuf:"bytes,1,rep,name=grooming_detail_relation_models,json=groomingDetailRelationModels,proto3" json:"grooming_detail_relation_models,omitempty"`
}

func (x *GetGroomingDetailRelationResponse) Reset() {
	*x = GetGroomingDetailRelationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingDetailRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingDetailRelationResponse) ProtoMessage() {}

func (x *GetGroomingDetailRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingDetailRelationResponse.ProtoReflect.Descriptor instead.
func (*GetGroomingDetailRelationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetGroomingDetailRelationResponse) GetGroomingDetailRelationModels() []*v1.GroomingDetailRelationModel {
	if x != nil {
		return x.GroomingDetailRelationModels
	}
	return nil
}

// edit staff params
type EditStaffCommissionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// pet detail list
	EditStaffCommissionItems []*v1.EditStaffCommissionItem `protobuf:"bytes,2,rep,name=edit_staff_commission_items,json=editStaffCommissionItems,proto3" json:"edit_staff_commission_items,omitempty"`
}

func (x *EditStaffCommissionParams) Reset() {
	*x = EditStaffCommissionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionParams) ProtoMessage() {}

func (x *EditStaffCommissionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionParams.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionParams) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{29}
}

func (x *EditStaffCommissionParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *EditStaffCommissionParams) GetEditStaffCommissionItems() []*v1.EditStaffCommissionItem {
	if x != nil {
		return x.EditStaffCommissionItems
	}
	return nil
}

// edit staff result
type EditStaffCommissionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EditStaffCommissionResult) Reset() {
	*x = EditStaffCommissionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionResult) ProtoMessage() {}

func (x *EditStaffCommissionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionResult.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionResult) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{30}
}

// upgrade invoice reinvent request
type UpgradeInvoiceReinventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *UpgradeInvoiceReinventRequest) Reset() {
	*x = UpgradeInvoiceReinventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeInvoiceReinventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeInvoiceReinventRequest) ProtoMessage() {}

func (x *UpgradeInvoiceReinventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeInvoiceReinventRequest.ProtoReflect.Descriptor instead.
func (*UpgradeInvoiceReinventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{31}
}

func (x *UpgradeInvoiceReinventRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// upgrade invoice reinvent response
type UpgradeInvoiceReinventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpgradeInvoiceReinventResponse) Reset() {
	*x = UpgradeInvoiceReinventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeInvoiceReinventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeInvoiceReinventResponse) ProtoMessage() {}

func (x *UpgradeInvoiceReinventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeInvoiceReinventResponse.ProtoReflect.Descriptor instead.
func (*UpgradeInvoiceReinventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{32}
}

// check invoice reinvent request
type CheckInvoiceReinventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CheckInvoiceReinventRequest) Reset() {
	*x = CheckInvoiceReinventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInvoiceReinventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInvoiceReinventRequest) ProtoMessage() {}

func (x *CheckInvoiceReinventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInvoiceReinventRequest.ProtoReflect.Descriptor instead.
func (*CheckInvoiceReinventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{33}
}

func (x *CheckInvoiceReinventRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// check invoice reinvent response
type CheckInvoiceReinventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is in invoice reinvent whitelist
	IsInInvoiceReinventWhitelist bool `protobuf:"varint,1,opt,name=is_in_invoice_reinvent_whitelist,json=isInInvoiceReinventWhitelist,proto3" json:"is_in_invoice_reinvent_whitelist,omitempty"`
}

func (x *CheckInvoiceReinventResponse) Reset() {
	*x = CheckInvoiceReinventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInvoiceReinventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInvoiceReinventResponse) ProtoMessage() {}

func (x *CheckInvoiceReinventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInvoiceReinventResponse.ProtoReflect.Descriptor instead.
func (*CheckInvoiceReinventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{34}
}

func (x *CheckInvoiceReinventResponse) GetIsInInvoiceReinventWhitelist() bool {
	if x != nil {
		return x.IsInInvoiceReinventWhitelist
	}
	return false
}

// create order payment request
type PayOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Company ID.
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Staff ID. 0 for pay online
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Payment method id 对应 payment的method_id
	PaymentMethodId int64 `protobuf:"varint,6,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
	// Payment method  对应 payment的method
	PaymentMethod string `protobuf:"bytes,7,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// Payment method extra
	PaymentMethodExtra *v11.PaymentMethodExtra `protobuf:"bytes,8,opt,name=payment_method_extra,json=paymentMethodExtra,proto3" json:"payment_method_extra,omitempty"`
	// Payment method vendor 对应 payment的vendor
	PaymentMethodVendor string `protobuf:"bytes,9,opt,name=payment_method_vendor,json=paymentMethodVendor,proto3" json:"payment_method_vendor,omitempty"`
	// is online
	IsOnline bool `protobuf:"varint,10,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	// is deposit
	IsDeposit bool `protobuf:"varint,11,opt,name=is_deposit,json=isDeposit,proto3" json:"is_deposit,omitempty"`
	// paid by
	PaidBy string `protobuf:"bytes,12,opt,name=paid_by,json=paidBy,proto3" json:"paid_by,omitempty"`
	// payment status
	PaymentStatus v1.OrderPaymentStatus `protobuf:"varint,13,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderPaymentStatus" json:"payment_status,omitempty"`
	// total amount：  total_amount = amount + payment_tips + convenience_fee.
	TotalAmount *money.Money `protobuf:"bytes,14,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// Payment amount.
	Amount *money.Money `protobuf:"bytes,15,opt,name=amount,proto3" json:"amount,omitempty"`
	// payment tips before create
	PaymentTipsBeforeCreate *money.Money `protobuf:"bytes,16,opt,name=payment_tips_before_create,json=paymentTipsBeforeCreate,proto3,oneof" json:"payment_tips_before_create,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,17,opt,name=convenience_fee,json=convenienceFee,proto3,oneof" json:"convenience_fee,omitempty"`
	// payment id
	PaymentId *int64 `protobuf:"varint,18,opt,name=payment_id,json=paymentId,proto3,oneof" json:"payment_id,omitempty"`
}

func (x *PayOrderRequest) Reset() {
	*x = PayOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderRequest) ProtoMessage() {}

func (x *PayOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderRequest.ProtoReflect.Descriptor instead.
func (*PayOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{35}
}

func (x *PayOrderRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PayOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PayOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PayOrderRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PayOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *PayOrderRequest) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

func (x *PayOrderRequest) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *PayOrderRequest) GetPaymentMethodExtra() *v11.PaymentMethodExtra {
	if x != nil {
		return x.PaymentMethodExtra
	}
	return nil
}

func (x *PayOrderRequest) GetPaymentMethodVendor() string {
	if x != nil {
		return x.PaymentMethodVendor
	}
	return ""
}

func (x *PayOrderRequest) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *PayOrderRequest) GetIsDeposit() bool {
	if x != nil {
		return x.IsDeposit
	}
	return false
}

func (x *PayOrderRequest) GetPaidBy() string {
	if x != nil {
		return x.PaidBy
	}
	return ""
}

func (x *PayOrderRequest) GetPaymentStatus() v1.OrderPaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return v1.OrderPaymentStatus(0)
}

func (x *PayOrderRequest) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *PayOrderRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PayOrderRequest) GetPaymentTipsBeforeCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsBeforeCreate
	}
	return nil
}

func (x *PayOrderRequest) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *PayOrderRequest) GetPaymentId() int64 {
	if x != nil && x.PaymentId != nil {
		return *x.PaymentId
	}
	return 0
}

// create order payment response
type PayOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment.
	OrderPayment *v1.OrderPaymentModel `protobuf:"bytes,1,opt,name=order_payment,json=orderPayment,proto3" json:"order_payment,omitempty"`
}

func (x *PayOrderResponse) Reset() {
	*x = PayOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderResponse) ProtoMessage() {}

func (x *PayOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderResponse.ProtoReflect.Descriptor instead.
func (*PayOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{36}
}

func (x *PayOrderResponse) GetOrderPayment() *v1.OrderPaymentModel {
	if x != nil {
		return x.OrderPayment
	}
	return nil
}

// update order payment request
type UpdateOrderPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment ID.
	OrderPaymentId int64 `protobuf:"varint,1,opt,name=order_payment_id,json=orderPaymentId,proto3" json:"order_payment_id,omitempty"`
	// payment status
	PaymentStatus *v1.OrderPaymentStatus `protobuf:"varint,2,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderPaymentStatus,oneof" json:"payment_status,omitempty"`
	// Payment method extra
	PaymentMethodExtra *v11.PaymentMethodExtra `protobuf:"bytes,3,opt,name=payment_method_extra,json=paymentMethodExtra,proto3,oneof" json:"payment_method_extra,omitempty"`
	// Payment method vendor
	PaymentMethodVendor *string `protobuf:"bytes,4,opt,name=payment_method_vendor,json=paymentMethodVendor,proto3,oneof" json:"payment_method_vendor,omitempty"`
	// total amount：  total_amount = amount + payment_tips.
	// 更新场景： 1. terminal 添加 tips 2. debit card 取消convenience fee
	TotalAmount *money.Money `protobuf:"bytes,5,opt,name=total_amount,json=totalAmount,proto3,oneof" json:"total_amount,omitempty"`
	// payment tips after create
	PaymentTipsAfterCreate *money.Money `protobuf:"bytes,6,opt,name=payment_tips_after_create,json=paymentTipsAfterCreate,proto3,oneof" json:"payment_tips_after_create,omitempty"`
	// processing fee
	ProcessingFee *money.Money `protobuf:"bytes,7,opt,name=processing_fee,json=processingFee,proto3,oneof" json:"processing_fee,omitempty"`
	// payment status reason
	// 搭配 Payment status 展示，进入该状态的原因:
	//
	//	Failed -> 失败的原因
	//	Canceled -> 取消的原因
	PaymentStatusReason *string `protobuf:"bytes,8,opt,name=payment_status_reason,json=paymentStatusReason,proto3,oneof" json:"payment_status_reason,omitempty"`
	// order id
	OrderId *int64 `protobuf:"varint,9,opt,name=order_id,json=orderId,proto3,oneof" json:"order_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,10,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// convenience fee
	ConvenienceFee *money.Money `protobuf:"bytes,11,opt,name=convenience_fee,json=convenienceFee,proto3,oneof" json:"convenience_fee,omitempty"`
	// payment id
	PaymentId *int64 `protobuf:"varint,12,opt,name=payment_id,json=paymentId,proto3,oneof" json:"payment_id,omitempty"`
	// payment tips before create
	PaymentTipsBeforeCreate *money.Money `protobuf:"bytes,13,opt,name=payment_tips_before_create,json=paymentTipsBeforeCreate,proto3,oneof" json:"payment_tips_before_create,omitempty"`
}

func (x *UpdateOrderPaymentRequest) Reset() {
	*x = UpdateOrderPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderPaymentRequest) ProtoMessage() {}

func (x *UpdateOrderPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderPaymentRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateOrderPaymentRequest) GetOrderPaymentId() int64 {
	if x != nil {
		return x.OrderPaymentId
	}
	return 0
}

func (x *UpdateOrderPaymentRequest) GetPaymentStatus() v1.OrderPaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.OrderPaymentStatus(0)
}

func (x *UpdateOrderPaymentRequest) GetPaymentMethodExtra() *v11.PaymentMethodExtra {
	if x != nil {
		return x.PaymentMethodExtra
	}
	return nil
}

func (x *UpdateOrderPaymentRequest) GetPaymentMethodVendor() string {
	if x != nil && x.PaymentMethodVendor != nil {
		return *x.PaymentMethodVendor
	}
	return ""
}

func (x *UpdateOrderPaymentRequest) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *UpdateOrderPaymentRequest) GetPaymentTipsAfterCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsAfterCreate
	}
	return nil
}

func (x *UpdateOrderPaymentRequest) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *UpdateOrderPaymentRequest) GetPaymentStatusReason() string {
	if x != nil && x.PaymentStatusReason != nil {
		return *x.PaymentStatusReason
	}
	return ""
}

func (x *UpdateOrderPaymentRequest) GetOrderId() int64 {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return 0
}

func (x *UpdateOrderPaymentRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *UpdateOrderPaymentRequest) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *UpdateOrderPaymentRequest) GetPaymentId() int64 {
	if x != nil && x.PaymentId != nil {
		return *x.PaymentId
	}
	return 0
}

func (x *UpdateOrderPaymentRequest) GetPaymentTipsBeforeCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsBeforeCreate
	}
	return nil
}

// update order payment response
type UpdateOrderPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment.
	OrderPayment *v1.OrderPaymentModel `protobuf:"bytes,1,opt,name=order_payment,json=orderPayment,proto3" json:"order_payment,omitempty"`
}

func (x *UpdateOrderPaymentResponse) Reset() {
	*x = UpdateOrderPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderPaymentResponse) ProtoMessage() {}

func (x *UpdateOrderPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderPaymentResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateOrderPaymentResponse) GetOrderPayment() *v1.OrderPaymentModel {
	if x != nil {
		return x.OrderPayment
	}
	return nil
}

// sync order payment and order request
type SyncOrderPaymentAndOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check count
	CheckCount int32 `protobuf:"varint,1,opt,name=check_count,json=checkCount,proto3" json:"check_count,omitempty"`
	// sync count
	SyncCount int32 `protobuf:"varint,2,opt,name=sync_count,json=syncCount,proto3" json:"sync_count,omitempty"`
}

func (x *SyncOrderPaymentAndOrderResponse) Reset() {
	*x = SyncOrderPaymentAndOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOrderPaymentAndOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOrderPaymentAndOrderResponse) ProtoMessage() {}

func (x *SyncOrderPaymentAndOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOrderPaymentAndOrderResponse.ProtoReflect.Descriptor instead.
func (*SyncOrderPaymentAndOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{39}
}

func (x *SyncOrderPaymentAndOrderResponse) GetCheckCount() int32 {
	if x != nil {
		return x.CheckCount
	}
	return 0
}

func (x *SyncOrderPaymentAndOrderResponse) GetSyncCount() int32 {
	if x != nil {
		return x.SyncCount
	}
	return 0
}

// Preview refund order request.
type PreviewRefundOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Refund mode.
	RefundMode v1.RefundMode `protobuf:"varint,2,opt,name=refund_mode,json=refundMode,proto3,enum=moego.models.order.v1.RefundMode" json:"refund_mode,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 从哪 一/几 笔 Order Payment 中退款.
	SourceOrderPayments []*RefundOrderRequest_OrderPayment `protobuf:"bytes,4,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
	// Refund by.
	//
	// Types that are assignable to RefundBy:
	//
	//	*PreviewRefundOrderRequest_RefundByItem
	//	*PreviewRefundOrderRequest_RefundByPayment
	RefundBy isPreviewRefundOrderRequest_RefundBy `protobuf_oneof:"refund_by"`
}

func (x *PreviewRefundOrderRequest) Reset() {
	*x = PreviewRefundOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderRequest) ProtoMessage() {}

func (x *PreviewRefundOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderRequest.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{40}
}

func (x *PreviewRefundOrderRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PreviewRefundOrderRequest) GetRefundMode() v1.RefundMode {
	if x != nil {
		return x.RefundMode
	}
	return v1.RefundMode(0)
}

func (x *PreviewRefundOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewRefundOrderRequest) GetSourceOrderPayments() []*RefundOrderRequest_OrderPayment {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

func (m *PreviewRefundOrderRequest) GetRefundBy() isPreviewRefundOrderRequest_RefundBy {
	if m != nil {
		return m.RefundBy
	}
	return nil
}

func (x *PreviewRefundOrderRequest) GetRefundByItem() *RefundOrderRequest_RefundByItem {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderRequest_RefundByItem); ok {
		return x.RefundByItem
	}
	return nil
}

func (x *PreviewRefundOrderRequest) GetRefundByPayment() *RefundOrderRequest_RefundByPayment {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderRequest_RefundByPayment); ok {
		return x.RefundByPayment
	}
	return nil
}

type isPreviewRefundOrderRequest_RefundBy interface {
	isPreviewRefundOrderRequest_RefundBy()
}

type PreviewRefundOrderRequest_RefundByItem struct {
	// For REFUND_BY_ITEM.
	RefundByItem *RefundOrderRequest_RefundByItem `protobuf:"bytes,11,opt,name=refund_by_item,json=refundByItem,proto3,oneof"`
}

type PreviewRefundOrderRequest_RefundByPayment struct {
	// For REFUND_BY_PAYMENT.
	RefundByPayment *RefundOrderRequest_RefundByPayment `protobuf:"bytes,12,opt,name=refund_by_payment,json=refundByPayment,proto3,oneof"`
}

func (*PreviewRefundOrderRequest_RefundByItem) isPreviewRefundOrderRequest_RefundBy() {}

func (*PreviewRefundOrderRequest_RefundByPayment) isPreviewRefundOrderRequest_RefundBy() {}

// Preview refund order response.
type PreviewRefundOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// Previewed refund order.
	PreviewRefundOrder *v1.RefundOrderDetailModel `protobuf:"bytes,2,opt,name=preview_refund_order,json=previewRefundOrder,proto3" json:"preview_refund_order,omitempty"`
	// Previewed related refund orders. On refunding deducted deposit, multiple refund orders may be created. The related
	// refund orders will be set to this field.
	RelatedRefundOrders []*PreviewRefundOrderResponse_RelatedRefundOrder `protobuf:"bytes,8,rep,name=related_refund_orders,json=relatedRefundOrders,proto3" json:"related_refund_orders,omitempty"`
	// 与 Order item 一一对应，表示每一个 item 可以退的数量/金额.
	// 特别的，如果退款模式是 By Payment，这里不会有内容.
	RefundableItems []*PreviewRefundOrderResponse_RefundableItem `protobuf:"bytes,3,rep,name=refundable_items,json=refundableItems,proto3" json:"refundable_items,omitempty"`
	// 可以退的 Tips.
	RefundableTips *money.Money `protobuf:"bytes,4,opt,name=refundable_tips,json=refundableTips,proto3" json:"refundable_tips,omitempty"`
	// Refund flags.
	RefundFlags *PreviewRefundOrderResponse_RefundFlags `protobuf:"bytes,5,opt,name=refund_flags,json=refundFlags,proto3" json:"refund_flags,omitempty"`
	// 可以退的 Order Payments.
	RefundableOrderPayments []*PreviewRefundOrderResponse_RefundableOrderPayment `protobuf:"bytes,6,rep,name=refundable_order_payments,json=refundableOrderPayments,proto3" json:"refundable_order_payments,omitempty"`
	// 可以退的 ConvenienceFee.
	RefundableConvenienceFee *money.Money `protobuf:"bytes,7,opt,name=refundable_convenience_fee,json=refundableConvenienceFee,proto3" json:"refundable_convenience_fee,omitempty"`
}

func (x *PreviewRefundOrderResponse) Reset() {
	*x = PreviewRefundOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResponse) ProtoMessage() {}

func (x *PreviewRefundOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResponse.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{41}
}

func (x *PreviewRefundOrderResponse) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetPreviewRefundOrder() *v1.RefundOrderDetailModel {
	if x != nil {
		return x.PreviewRefundOrder
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRelatedRefundOrders() []*PreviewRefundOrderResponse_RelatedRefundOrder {
	if x != nil {
		return x.RelatedRefundOrders
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRefundableItems() []*PreviewRefundOrderResponse_RefundableItem {
	if x != nil {
		return x.RefundableItems
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRefundableTips() *money.Money {
	if x != nil {
		return x.RefundableTips
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRefundFlags() *PreviewRefundOrderResponse_RefundFlags {
	if x != nil {
		return x.RefundFlags
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRefundableOrderPayments() []*PreviewRefundOrderResponse_RefundableOrderPayment {
	if x != nil {
		return x.RefundableOrderPayments
	}
	return nil
}

func (x *PreviewRefundOrderResponse) GetRefundableConvenienceFee() *money.Money {
	if x != nil {
		return x.RefundableConvenienceFee
	}
	return nil
}

// Preview refund order payments request.
// 纯计算接口.
type PreviewRefundOrderPaymentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要退款的金额.
	RefundAmount *money.Money `protobuf:"bytes,1,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// 需要退款的金额的标志.
	RefundAmountFlag *RefundOrderRequest_RefundByPayment_RefundAmountFlags `protobuf:"bytes,2,opt,name=refund_amount_flag,json=refundAmountFlag,proto3" json:"refund_amount_flag,omitempty"`
	// 需要分摊退款金额的 Order Payments.
	// 这里不需要真实的 Order Payments, 只是复用结构.
	// 只需要金额相关的字段齐全即可.
	SourceOrderPayments []*v1.OrderPaymentModel `protobuf:"bytes,3,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
}

func (x *PreviewRefundOrderPaymentsRequest) Reset() {
	*x = PreviewRefundOrderPaymentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderPaymentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderPaymentsRequest) ProtoMessage() {}

func (x *PreviewRefundOrderPaymentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderPaymentsRequest.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderPaymentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{42}
}

func (x *PreviewRefundOrderPaymentsRequest) GetRefundAmount() *money.Money {
	if x != nil {
		return x.RefundAmount
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsRequest) GetRefundAmountFlag() *RefundOrderRequest_RefundByPayment_RefundAmountFlags {
	if x != nil {
		return x.RefundAmountFlag
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsRequest) GetSourceOrderPayments() []*v1.OrderPaymentModel {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

// Preview refund order payments response.
type PreviewRefundOrderPaymentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总的退款金额.
	RefundTotalAmount *money.Money `protobuf:"bytes,1,opt,name=refund_total_amount,json=refundTotalAmount,proto3" json:"refund_total_amount,omitempty"`
	// 总的退款金额中 **包含** 的 Convenience Fee.
	RefundConvenienceFee *money.Money `protobuf:"bytes,2,opt,name=refund_convenience_fee,json=refundConvenienceFee,proto3" json:"refund_convenience_fee,omitempty"`
	// 各 Order Payment 的退款明细.
	RefundOrderPayments []*v1.RefundOrderPaymentModel `protobuf:"bytes,3,rep,name=refund_order_payments,json=refundOrderPayments,proto3" json:"refund_order_payments,omitempty"`
}

func (x *PreviewRefundOrderPaymentsResponse) Reset() {
	*x = PreviewRefundOrderPaymentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderPaymentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderPaymentsResponse) ProtoMessage() {}

func (x *PreviewRefundOrderPaymentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderPaymentsResponse.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderPaymentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{43}
}

func (x *PreviewRefundOrderPaymentsResponse) GetRefundTotalAmount() *money.Money {
	if x != nil {
		return x.RefundTotalAmount
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsResponse) GetRefundConvenienceFee() *money.Money {
	if x != nil {
		return x.RefundConvenienceFee
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsResponse) GetRefundOrderPayments() []*v1.RefundOrderPaymentModel {
	if x != nil {
		return x.RefundOrderPayments
	}
	return nil
}

// Refund order request
type RefundOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 退款原因.
	RefundReason string `protobuf:"bytes,4,opt,name=refund_reason,json=refundReason,proto3" json:"refund_reason,omitempty"`
	// Refund mode.
	RefundMode v1.RefundMode `protobuf:"varint,5,opt,name=refund_mode,json=refundMode,proto3,enum=moego.models.order.v1.RefundMode" json:"refund_mode,omitempty"`
	// 从哪 一/几 笔 Order Payment 中退款.
	// 移除了 min_items 校验：在支持 deposit 抵扣后，一笔订单可能会被订金全额抵扣，因此可能不需要传 source_order_payments.
	SourceOrderPayments []*RefundOrderRequest_OrderPayment `protobuf:"bytes,6,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
	// Refund by.
	//
	// Types that are assignable to RefundBy:
	//
	//	*RefundOrderRequest_RefundByItem_
	//	*RefundOrderRequest_RefundByPayment_
	RefundBy isRefundOrderRequest_RefundBy `protobuf_oneof:"refund_by"`
}

func (x *RefundOrderRequest) Reset() {
	*x = RefundOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest) ProtoMessage() {}

func (x *RefundOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44}
}

func (x *RefundOrderRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RefundOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *RefundOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *RefundOrderRequest) GetRefundReason() string {
	if x != nil {
		return x.RefundReason
	}
	return ""
}

func (x *RefundOrderRequest) GetRefundMode() v1.RefundMode {
	if x != nil {
		return x.RefundMode
	}
	return v1.RefundMode(0)
}

func (x *RefundOrderRequest) GetSourceOrderPayments() []*RefundOrderRequest_OrderPayment {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

func (m *RefundOrderRequest) GetRefundBy() isRefundOrderRequest_RefundBy {
	if m != nil {
		return m.RefundBy
	}
	return nil
}

func (x *RefundOrderRequest) GetRefundByItem() *RefundOrderRequest_RefundByItem {
	if x, ok := x.GetRefundBy().(*RefundOrderRequest_RefundByItem_); ok {
		return x.RefundByItem
	}
	return nil
}

func (x *RefundOrderRequest) GetRefundByPayment() *RefundOrderRequest_RefundByPayment {
	if x, ok := x.GetRefundBy().(*RefundOrderRequest_RefundByPayment_); ok {
		return x.RefundByPayment
	}
	return nil
}

type isRefundOrderRequest_RefundBy interface {
	isRefundOrderRequest_RefundBy()
}

type RefundOrderRequest_RefundByItem_ struct {
	// For REFUND_BY_ITEM.
	RefundByItem *RefundOrderRequest_RefundByItem `protobuf:"bytes,11,opt,name=refund_by_item,json=refundByItem,proto3,oneof"`
}

type RefundOrderRequest_RefundByPayment_ struct {
	// For REFUND_BY_PAYMENT.
	RefundByPayment *RefundOrderRequest_RefundByPayment `protobuf:"bytes,12,opt,name=refund_by_payment,json=refundByPayment,proto3,oneof"`
}

func (*RefundOrderRequest_RefundByItem_) isRefundOrderRequest_RefundBy() {}

func (*RefundOrderRequest_RefundByPayment_) isRefundOrderRequest_RefundBy() {}

// refund order response
type RefundOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order.
	RefundOrder *v1.RefundOrderModel `protobuf:"bytes,1,opt,name=refund_order,json=refundOrder,proto3" json:"refund_order,omitempty"`
	// Refund order items. Empty if REFUND_BY_AMOUNT.
	RefundOrderItems []*v1.RefundOrderItemModel `protobuf:"bytes,2,rep,name=refund_order_items,json=refundOrderItems,proto3" json:"refund_order_items,omitempty"`
	// Refund order payment.
	RefundOrderPayment []*v1.RefundOrderPaymentModel `protobuf:"bytes,3,rep,name=refund_order_payment,json=refundOrderPayment,proto3" json:"refund_order_payment,omitempty"`
	// On refunding deducted deposit, multiple refund orders may be created. The related refund orders will be set to this
	// field.
	RelatedRefundOrders []*RefundOrderResponse_RelatedRefundOrder `protobuf:"bytes,4,rep,name=related_refund_orders,json=relatedRefundOrders,proto3" json:"related_refund_orders,omitempty"`
}

func (x *RefundOrderResponse) Reset() {
	*x = RefundOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderResponse) ProtoMessage() {}

func (x *RefundOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderResponse.ProtoReflect.Descriptor instead.
func (*RefundOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{45}
}

func (x *RefundOrderResponse) GetRefundOrder() *v1.RefundOrderModel {
	if x != nil {
		return x.RefundOrder
	}
	return nil
}

func (x *RefundOrderResponse) GetRefundOrderItems() []*v1.RefundOrderItemModel {
	if x != nil {
		return x.RefundOrderItems
	}
	return nil
}

func (x *RefundOrderResponse) GetRefundOrderPayment() []*v1.RefundOrderPaymentModel {
	if x != nil {
		return x.RefundOrderPayment
	}
	return nil
}

func (x *RefundOrderResponse) GetRelatedRefundOrders() []*RefundOrderResponse_RelatedRefundOrder {
	if x != nil {
		return x.RelatedRefundOrders
	}
	return nil
}

// SyncRefundOrderPaymentRequest.
// 用于定时任务触发异常中断的 RefundOrderPayment 流程继续走下去.
type SyncRefundOrderPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要触发的 RefundOrderPayment 的 ID.
	RefundOrderPaymentId int64 `protobuf:"varint,1,opt,name=refund_order_payment_id,json=refundOrderPaymentId,proto3" json:"refund_order_payment_id,omitempty"`
}

func (x *SyncRefundOrderPaymentRequest) Reset() {
	*x = SyncRefundOrderPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncRefundOrderPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRefundOrderPaymentRequest) ProtoMessage() {}

func (x *SyncRefundOrderPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRefundOrderPaymentRequest.ProtoReflect.Descriptor instead.
func (*SyncRefundOrderPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{46}
}

func (x *SyncRefundOrderPaymentRequest) GetRefundOrderPaymentId() int64 {
	if x != nil {
		return x.RefundOrderPaymentId
	}
	return 0
}

// SyncRefundOrderPaymentResponse.
type SyncRefundOrderPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总共发现的需要处理的 RefundOrderPayment 数量.
	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 同步成功的数量.
	Synced int64 `protobuf:"varint,2,opt,name=synced,proto3" json:"synced,omitempty"`
}

func (x *SyncRefundOrderPaymentResponse) Reset() {
	*x = SyncRefundOrderPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncRefundOrderPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRefundOrderPaymentResponse) ProtoMessage() {}

func (x *SyncRefundOrderPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRefundOrderPaymentResponse.ProtoReflect.Descriptor instead.
func (*SyncRefundOrderPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{47}
}

func (x *SyncRefundOrderPaymentResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SyncRefundOrderPaymentResponse) GetSynced() int64 {
	if x != nil {
		return x.Synced
	}
	return 0
}

// List orders request.
// 依据源单 ID 查询关联的订单、退款单详情.
type ListOrdersV1Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 源单 ID.
	OriginOrderId int64 `protobuf:"varint,2,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *ListOrdersV1Request) Reset() {
	*x = ListOrdersV1Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersV1Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersV1Request) ProtoMessage() {}

func (x *ListOrdersV1Request) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersV1Request.ProtoReflect.Descriptor instead.
func (*ListOrdersV1Request) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{48}
}

func (x *ListOrdersV1Request) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListOrdersV1Request) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// List orders response.
type ListOrdersV1Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderModelV1 `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refund orders.
	RefundOrders []*v1.RefundOrderModel `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *ListOrdersV1Response) Reset() {
	*x = ListOrdersV1Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersV1Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersV1Response) ProtoMessage() {}

func (x *ListOrdersV1Response) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersV1Response.ProtoReflect.Descriptor instead.
func (*ListOrdersV1Response) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{49}
}

func (x *ListOrdersV1Response) GetOrders() []*v1.OrderModelV1 {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersV1Response) GetRefundOrders() []*v1.RefundOrderModel {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// List order detail request.
// 依据源单 ID 查询关联的订单、退款单详情.
type ListOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 源单 ID
	OriginOrderId int64 `protobuf:"varint,2,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *ListOrderDetailRequest) Reset() {
	*x = ListOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderDetailRequest) ProtoMessage() {}

func (x *ListOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*ListOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{50}
}

func (x *ListOrderDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListOrderDetailRequest) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// List order detail response.
type ListOrderDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderDetailModelV1 `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refund orders.
	RefundOrders []*v1.RefundOrderDetailModel `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *ListOrderDetailResponse) Reset() {
	*x = ListOrderDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderDetailResponse) ProtoMessage() {}

func (x *ListOrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderDetailResponse.ProtoReflect.Descriptor instead.
func (*ListOrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{51}
}

func (x *ListOrderDetailResponse) GetOrders() []*v1.OrderDetailModelV1 {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrderDetailResponse) GetRefundOrders() []*v1.RefundOrderDetailModel {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// Query order detail request.
// 查询 Company 内的订单. 支持多条件筛选.
type QueryOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID，为 0 时表示不限制.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// SourceType.
	// 传 0 表示不限制.
	SourceType v1.OrderSourceType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *QueryOrderDetailRequest) Reset() {
	*x = QueryOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOrderDetailRequest) ProtoMessage() {}

func (x *QueryOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*QueryOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{52}
}

func (x *QueryOrderDetailRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *QueryOrderDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *QueryOrderDetailRequest) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *QueryOrderDetailRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

// Query order detail response.
type QueryOrderDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderDetailModelV1 `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refund orders.
	RefundOrders []*v1.RefundOrderDetailModel `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *QueryOrderDetailResponse) Reset() {
	*x = QueryOrderDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryOrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOrderDetailResponse) ProtoMessage() {}

func (x *QueryOrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOrderDetailResponse.ProtoReflect.Descriptor instead.
func (*QueryOrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{53}
}

func (x *QueryOrderDetailResponse) GetOrders() []*v1.OrderDetailModelV1 {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *QueryOrderDetailResponse) GetRefundOrders() []*v1.RefundOrderDetailModel {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// Get order detail request.
type GetOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Order ID.
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *GetOrderDetailRequest) Reset() {
	*x = GetOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailRequest) ProtoMessage() {}

func (x *GetOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*GetOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetOrderDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOrderDetailRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// Get order detail response.
type GetOrderDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order detail.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetOrderDetailResponse) Reset() {
	*x = GetOrderDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailResponse) ProtoMessage() {}

func (x *GetOrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailResponse.ProtoReflect.Descriptor instead.
func (*GetOrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetOrderDetailResponse) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

// Get refund order detail request.
type GetRefundOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Refund order ID.
	RefundOrderId int64 `protobuf:"varint,2,opt,name=refund_order_id,json=refundOrderId,proto3" json:"refund_order_id,omitempty"`
}

func (x *GetRefundOrderDetailRequest) Reset() {
	*x = GetRefundOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundOrderDetailRequest) ProtoMessage() {}

func (x *GetRefundOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*GetRefundOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetRefundOrderDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRefundOrderDetailRequest) GetRefundOrderId() int64 {
	if x != nil {
		return x.RefundOrderId
	}
	return 0
}

// Get refund order detail response.
type GetRefundOrderDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order detail.
	RefundOrder *v1.RefundOrderDetailModel `protobuf:"bytes,1,opt,name=refund_order,json=refundOrder,proto3" json:"refund_order,omitempty"`
}

func (x *GetRefundOrderDetailResponse) Reset() {
	*x = GetRefundOrderDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundOrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundOrderDetailResponse) ProtoMessage() {}

func (x *GetRefundOrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundOrderDetailResponse.ProtoReflect.Descriptor instead.
func (*GetRefundOrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetRefundOrderDetailResponse) GetRefundOrder() *v1.RefundOrderDetailModel {
	if x != nil {
		return x.RefundOrder
	}
	return nil
}

// Create noshow order request.
type CreateNoShowOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,12,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Description.
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// NoShow fee. This amount should greater than ZERO.
	NoShowFeeAmount *money.Money `protobuf:"bytes,14,opt,name=no_show_fee_amount,json=noShowFeeAmount,proto3" json:"no_show_fee_amount,omitempty"`
}

func (x *CreateNoShowOrderRequest) Reset() {
	*x = CreateNoShowOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNoShowOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNoShowOrderRequest) ProtoMessage() {}

func (x *CreateNoShowOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNoShowOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateNoShowOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{58}
}

func (x *CreateNoShowOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateNoShowOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateNoShowOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateNoShowOrderRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateNoShowOrderRequest) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateNoShowOrderRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateNoShowOrderRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateNoShowOrderRequest) GetNoShowFeeAmount() *money.Money {
	if x != nil {
		return x.NoShowFeeAmount
	}
	return nil
}

// Create noshow order response.
type CreateNoShowOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created no-show order.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateNoShowOrderResponse) Reset() {
	*x = CreateNoShowOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNoShowOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNoShowOrderResponse) ProtoMessage() {}

func (x *CreateNoShowOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNoShowOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateNoShowOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{59}
}

func (x *CreateNoShowOrderResponse) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

// Create extra tip order request.
type CreateTipOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,12,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Description.
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// Tip amount. This amount should be greater than ZERO.
	TipAmount *money.Money `protobuf:"bytes,14,opt,name=tip_amount,json=tipAmount,proto3" json:"tip_amount,omitempty"`
	// tips based amount
	TipBasedAmount *money.Money `protobuf:"bytes,15,opt,name=tip_based_amount,json=tipBasedAmount,proto3" json:"tip_based_amount,omitempty"`
}

func (x *CreateTipOrderRequest) Reset() {
	*x = CreateTipOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTipOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTipOrderRequest) ProtoMessage() {}

func (x *CreateTipOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTipOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateTipOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{60}
}

func (x *CreateTipOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateTipOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateTipOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateTipOrderRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateTipOrderRequest) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateTipOrderRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateTipOrderRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTipOrderRequest) GetTipAmount() *money.Money {
	if x != nil {
		return x.TipAmount
	}
	return nil
}

func (x *CreateTipOrderRequest) GetTipBasedAmount() *money.Money {
	if x != nil {
		return x.TipBasedAmount
	}
	return nil
}

// Create extra tip order response.
type CreateTipOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created extra tip order.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateTipOrderResponse) Reset() {
	*x = CreateTipOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTipOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTipOrderResponse) ProtoMessage() {}

func (x *CreateTipOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTipOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateTipOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{61}
}

func (x *CreateTipOrderResponse) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

// Create deposit order.
type CreateDepositOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Deposit amount.
	DepositAmount *money.Money `protobuf:"bytes,7,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// Description (like "By percentage, $250.00*30%").
	DepositDescription string `protobuf:"bytes,8,opt,name=deposit_description,json=depositDescription,proto3" json:"deposit_description,omitempty"`
	// Deposit price details to be created.
	DepositPriceDetail *v1.PriceDetailModel `protobuf:"bytes,9,opt,name=deposit_price_detail,json=depositPriceDetail,proto3" json:"deposit_price_detail,omitempty"`
}

func (x *CreateDepositOrderRequest) Reset() {
	*x = CreateDepositOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositOrderRequest) ProtoMessage() {}

func (x *CreateDepositOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateDepositOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{62}
}

func (x *CreateDepositOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateDepositOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateDepositOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateDepositOrderRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateDepositOrderRequest) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateDepositOrderRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateDepositOrderRequest) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *CreateDepositOrderRequest) GetDepositDescription() string {
	if x != nil {
		return x.DepositDescription
	}
	return ""
}

func (x *CreateDepositOrderRequest) GetDepositPriceDetail() *v1.PriceDetailModel {
	if x != nil {
		return x.DepositPriceDetail
	}
	return nil
}

// CreateDepositOrderResponse
type CreateDepositOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateDepositOrderResponse) Reset() {
	*x = CreateDepositOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositOrderResponse) ProtoMessage() {}

func (x *CreateDepositOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateDepositOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{63}
}

func (x *CreateDepositOrderResponse) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

// Request for GetDepositDetail.
type GetDepositDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deposit order ID.
	DepositOrderId int64 `protobuf:"varint,1,opt,name=deposit_order_id,json=depositOrderId,proto3" json:"deposit_order_id,omitempty"`
}

func (x *GetDepositDetailRequest) Reset() {
	*x = GetDepositDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepositDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepositDetailRequest) ProtoMessage() {}

func (x *GetDepositDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepositDetailRequest.ProtoReflect.Descriptor instead.
func (*GetDepositDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetDepositDetailRequest) GetDepositOrderId() int64 {
	if x != nil {
		return x.DepositOrderId
	}
	return 0
}

// Response for GetDepositDetail.
type GetDepositDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Collected deposit (including refunded amount).
	CollectedAmount *money.Money `protobuf:"bytes,1,opt,name=collected_amount,json=collectedAmount,proto3" json:"collected_amount,omitempty"`
	// Refunded overpaid amount.
	ReversedAmount *money.Money `protobuf:"bytes,2,opt,name=reversed_amount,json=reversedAmount,proto3" json:"reversed_amount,omitempty"`
	// Deducted amount.
	DeductedAmount *money.Money `protobuf:"bytes,3,opt,name=deducted_amount,json=deductedAmount,proto3" json:"deducted_amount,omitempty"`
	// The deposit balance (collected_amount - reversed_amount - deducted_amount).
	Balance *money.Money `protobuf:"bytes,4,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *GetDepositDetailResponse) Reset() {
	*x = GetDepositDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepositDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepositDetailResponse) ProtoMessage() {}

func (x *GetDepositDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepositDetailResponse.ProtoReflect.Descriptor instead.
func (*GetDepositDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetDepositDetailResponse) GetCollectedAmount() *money.Money {
	if x != nil {
		return x.CollectedAmount
	}
	return nil
}

func (x *GetDepositDetailResponse) GetReversedAmount() *money.Money {
	if x != nil {
		return x.ReversedAmount
	}
	return nil
}

func (x *GetDepositDetailResponse) GetDeductedAmount() *money.Money {
	if x != nil {
		return x.DeductedAmount
	}
	return nil
}

func (x *GetDepositDetailResponse) GetBalance() *money.Money {
	if x != nil {
		return x.Balance
	}
	return nil
}

// Request for UpdateDepositOrderSource
type UpdateDepositOrderSourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Old source type.
	OldSourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=old_source_type,json=oldSourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"old_source_type,omitempty"`
	// Old source ID.
	OldSourceId int64 `protobuf:"varint,2,opt,name=old_source_id,json=oldSourceId,proto3" json:"old_source_id,omitempty"`
	// New source type.
	NewSourceType v1.OrderSourceType `protobuf:"varint,3,opt,name=new_source_type,json=newSourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"new_source_type,omitempty"`
	// New source ID.
	NewSourceId int64 `protobuf:"varint,4,opt,name=new_source_id,json=newSourceId,proto3" json:"new_source_id,omitempty"`
}

func (x *UpdateDepositOrderSourceRequest) Reset() {
	*x = UpdateDepositOrderSourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDepositOrderSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepositOrderSourceRequest) ProtoMessage() {}

func (x *UpdateDepositOrderSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepositOrderSourceRequest.ProtoReflect.Descriptor instead.
func (*UpdateDepositOrderSourceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{66}
}

func (x *UpdateDepositOrderSourceRequest) GetOldSourceType() v1.OrderSourceType {
	if x != nil {
		return x.OldSourceType
	}
	return v1.OrderSourceType(0)
}

func (x *UpdateDepositOrderSourceRequest) GetOldSourceId() int64 {
	if x != nil {
		return x.OldSourceId
	}
	return 0
}

func (x *UpdateDepositOrderSourceRequest) GetNewSourceType() v1.OrderSourceType {
	if x != nil {
		return x.NewSourceType
	}
	return v1.OrderSourceType(0)
}

func (x *UpdateDepositOrderSourceRequest) GetNewSourceId() int64 {
	if x != nil {
		return x.NewSourceId
	}
	return 0
}

// Response for UpdateDepositOrderSource
type UpdateDepositOrderSourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateDepositOrderSourceResponse) Reset() {
	*x = UpdateDepositOrderSourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDepositOrderSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepositOrderSourceResponse) ProtoMessage() {}

func (x *UpdateDepositOrderSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepositOrderSourceResponse.ProtoReflect.Descriptor instead.
func (*UpdateDepositOrderSourceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{67}
}

// Cancel order request.
type CancelOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Order ID.
	OrderId int64 `protobuf:"varint,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *CancelOrderRequest) Reset() {
	*x = CancelOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOrderRequest) ProtoMessage() {}

func (x *CancelOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOrderRequest.ProtoReflect.Descriptor instead.
func (*CancelOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{68}
}

func (x *CancelOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CancelOrderRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CancelOrderRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// Cancel order response.
type CancelOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelOrderResponse) Reset() {
	*x = CancelOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOrderResponse) ProtoMessage() {}

func (x *CancelOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOrderResponse.ProtoReflect.Descriptor instead.
func (*CancelOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{69}
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
// 用途是在 check in 的时候，获取一个稳定的 invoice id.
// 同一组 SourceType + SourceID 会固定返回同样的 Invoice ID。
// 其余参数是为了创建空订单而设置的。
type CreateInvoiceIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Company ID.
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID. 0 for pay online
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *CreateInvoiceIDRequest) Reset() {
	*x = CreateInvoiceIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInvoiceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInvoiceIDRequest) ProtoMessage() {}

func (x *CreateInvoiceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInvoiceIDRequest.ProtoReflect.Descriptor instead.
func (*CreateInvoiceIDRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{70}
}

func (x *CreateInvoiceIDRequest) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateInvoiceIDRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateInvoiceIDRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateInvoiceIDRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateInvoiceIDRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateInvoiceIDRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type CreateInvoiceIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Invoice ID.
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *CreateInvoiceIDResponse) Reset() {
	*x = CreateInvoiceIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInvoiceIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInvoiceIDResponse) ProtoMessage() {}

func (x *CreateInvoiceIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInvoiceIDResponse.ProtoReflect.Descriptor instead.
func (*CreateInvoiceIDResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{71}
}

func (x *CreateInvoiceIDResponse) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

// filter
type ListOrdersRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// status
	Statuses []int32 `protobuf:"varint,2,rep,packed,name=statuses,proto3" json:"statuses,omitempty"`
	// last updated time range
	LastUpdatedTimeRange *interval.Interval `protobuf:"bytes,3,opt,name=last_updated_time_range,json=lastUpdatedTimeRange,proto3" json:"last_updated_time_range,omitempty"`
}

func (x *ListOrdersRequest_Filter) Reset() {
	*x = ListOrdersRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersRequest_Filter) ProtoMessage() {}

func (x *ListOrdersRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListOrdersRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{21, 0}
}

func (x *ListOrdersRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListOrdersRequest_Filter) GetStatuses() []int32 {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListOrdersRequest_Filter) GetLastUpdatedTimeRange() *interval.Interval {
	if x != nil {
		return x.LastUpdatedTimeRange
	}
	return nil
}

// Related refund order detail.
type PreviewRefundOrderResponse_RelatedRefundOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// Previewed refund order detail.
	PreviewRefundOrder *v1.RefundOrderDetailModel `protobuf:"bytes,2,opt,name=preview_refund_order,json=previewRefundOrder,proto3" json:"preview_refund_order,omitempty"`
}

func (x *PreviewRefundOrderResponse_RelatedRefundOrder) Reset() {
	*x = PreviewRefundOrderResponse_RelatedRefundOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResponse_RelatedRefundOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResponse_RelatedRefundOrder) ProtoMessage() {}

func (x *PreviewRefundOrderResponse_RelatedRefundOrder) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResponse_RelatedRefundOrder.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResponse_RelatedRefundOrder) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{41, 0}
}

func (x *PreviewRefundOrderResponse_RelatedRefundOrder) GetOrder() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *PreviewRefundOrderResponse_RelatedRefundOrder) GetPreviewRefundOrder() *v1.RefundOrderDetailModel {
	if x != nil {
		return x.PreviewRefundOrder
	}
	return nil
}

// Refund flags.
type PreviewRefundOrderResponse_RefundFlags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 退款的 Order Payment 是否支持组合退款.
	// TODO(yunxiang): 这个逻辑有点子怪，考虑列出所有能退的。在提交的时候判断一下选中的能否覆盖需要退的总额。
	CanCombineOrderPayments bool `protobuf:"varint,1,opt,name=can_combine_order_payments,json=canCombineOrderPayments,proto3" json:"can_combine_order_payments,omitempty"`
	// Convenience Fee 是否可选.
	// 当设置为 False 时，Convenience Fee 必须选中.
	IsRefundConvenienceFeeOptional bool `protobuf:"varint,2,opt,name=is_refund_convenience_fee_optional,json=isRefundConvenienceFeeOptional,proto3" json:"is_refund_convenience_fee_optional,omitempty"`
}

func (x *PreviewRefundOrderResponse_RefundFlags) Reset() {
	*x = PreviewRefundOrderResponse_RefundFlags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResponse_RefundFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResponse_RefundFlags) ProtoMessage() {}

func (x *PreviewRefundOrderResponse_RefundFlags) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResponse_RefundFlags.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResponse_RefundFlags) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{41, 1}
}

func (x *PreviewRefundOrderResponse_RefundFlags) GetCanCombineOrderPayments() bool {
	if x != nil {
		return x.CanCombineOrderPayments
	}
	return false
}

func (x *PreviewRefundOrderResponse_RefundFlags) GetIsRefundConvenienceFeeOptional() bool {
	if x != nil {
		return x.IsRefundConvenienceFeeOptional
	}
	return false
}

// Refundable item.
type PreviewRefundOrderResponse_RefundableItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order item ID.
	OrderItemId int64 `protobuf:"varint,1,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// Refund item mode.
	RefundItemMode v1.RefundItemMode `protobuf:"varint,2,opt,name=refund_item_mode,json=refundItemMode,proto3,enum=moego.models.order.v1.RefundItemMode" json:"refund_item_mode,omitempty"`
	// 是否可以退款.
	IsRefundable bool `protobuf:"varint,3,opt,name=is_refundable,json=isRefundable,proto3" json:"is_refundable,omitempty"`
	// 依照 RefundItemMode 的取值有不同的内容.
	//
	// Types that are assignable to RefundBy:
	//
	//	*PreviewRefundOrderResponse_RefundableItem_RefundableQuantity
	//	*PreviewRefundOrderResponse_RefundableItem_RefundableAmount
	RefundBy isPreviewRefundOrderResponse_RefundableItem_RefundBy `protobuf_oneof:"refund_by"`
}

func (x *PreviewRefundOrderResponse_RefundableItem) Reset() {
	*x = PreviewRefundOrderResponse_RefundableItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResponse_RefundableItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResponse_RefundableItem) ProtoMessage() {}

func (x *PreviewRefundOrderResponse_RefundableItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResponse_RefundableItem.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResponse_RefundableItem) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{41, 2}
}

func (x *PreviewRefundOrderResponse_RefundableItem) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *PreviewRefundOrderResponse_RefundableItem) GetRefundItemMode() v1.RefundItemMode {
	if x != nil {
		return x.RefundItemMode
	}
	return v1.RefundItemMode(0)
}

func (x *PreviewRefundOrderResponse_RefundableItem) GetIsRefundable() bool {
	if x != nil {
		return x.IsRefundable
	}
	return false
}

func (m *PreviewRefundOrderResponse_RefundableItem) GetRefundBy() isPreviewRefundOrderResponse_RefundableItem_RefundBy {
	if m != nil {
		return m.RefundBy
	}
	return nil
}

func (x *PreviewRefundOrderResponse_RefundableItem) GetRefundableQuantity() int32 {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderResponse_RefundableItem_RefundableQuantity); ok {
		return x.RefundableQuantity
	}
	return 0
}

func (x *PreviewRefundOrderResponse_RefundableItem) GetRefundableAmount() *money.Money {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderResponse_RefundableItem_RefundableAmount); ok {
		return x.RefundableAmount
	}
	return nil
}

type isPreviewRefundOrderResponse_RefundableItem_RefundBy interface {
	isPreviewRefundOrderResponse_RefundableItem_RefundBy()
}

type PreviewRefundOrderResponse_RefundableItem_RefundableQuantity struct {
	// 按数量退的时候，可以退的数量.
	RefundableQuantity int32 `protobuf:"varint,11,opt,name=refundable_quantity,json=refundableQuantity,proto3,oneof"`
}

type PreviewRefundOrderResponse_RefundableItem_RefundableAmount struct {
	// 按金额退的时候，可以退的金额.
	RefundableAmount *money.Money `protobuf:"bytes,12,opt,name=refundable_amount,json=refundableAmount,proto3,oneof"`
}

func (*PreviewRefundOrderResponse_RefundableItem_RefundableQuantity) isPreviewRefundOrderResponse_RefundableItem_RefundBy() {
}

func (*PreviewRefundOrderResponse_RefundableItem_RefundableAmount) isPreviewRefundOrderResponse_RefundableItem_RefundBy() {
}

// 可以退的 Order Payment.
type PreviewRefundOrderResponse_RefundableOrderPayment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order payment ID.
	OrderPaymentId int64 `protobuf:"varint,1,opt,name=order_payment_id,json=orderPaymentId,proto3" json:"order_payment_id,omitempty"`
	// 该渠道还能退的金额.
	RefundableAmount *money.Money `protobuf:"bytes,2,opt,name=refundable_amount,json=refundableAmount,proto3" json:"refundable_amount,omitempty"`
}

func (x *PreviewRefundOrderResponse_RefundableOrderPayment) Reset() {
	*x = PreviewRefundOrderResponse_RefundableOrderPayment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResponse_RefundableOrderPayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResponse_RefundableOrderPayment) ProtoMessage() {}

func (x *PreviewRefundOrderResponse_RefundableOrderPayment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResponse_RefundableOrderPayment.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResponse_RefundableOrderPayment) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{41, 3}
}

func (x *PreviewRefundOrderResponse_RefundableOrderPayment) GetOrderPaymentId() int64 {
	if x != nil {
		return x.OrderPaymentId
	}
	return 0
}

func (x *PreviewRefundOrderResponse_RefundableOrderPayment) GetRefundableAmount() *money.Money {
	if x != nil {
		return x.RefundableAmount
	}
	return nil
}

// 待退款的 OrderPayment.
type RefundOrderRequest_OrderPayment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order Payment ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RefundOrderRequest_OrderPayment) Reset() {
	*x = RefundOrderRequest_OrderPayment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest_OrderPayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest_OrderPayment) ProtoMessage() {}

func (x *RefundOrderRequest_OrderPayment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest_OrderPayment.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest_OrderPayment) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44, 0}
}

func (x *RefundOrderRequest_OrderPayment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Refund by item.
type RefundOrderRequest_RefundByItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Items.
	RefundItems []*RefundOrderRequest_RefundByItem_RefundItem `protobuf:"bytes,1,rep,name=refund_items,json=refundItems,proto3" json:"refund_items,omitempty"`
	// Tips.
	RefundTips *money.Money `protobuf:"bytes,2,opt,name=refund_tips,json=refundTips,proto3" json:"refund_tips,omitempty"`
}

func (x *RefundOrderRequest_RefundByItem) Reset() {
	*x = RefundOrderRequest_RefundByItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest_RefundByItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest_RefundByItem) ProtoMessage() {}

func (x *RefundOrderRequest_RefundByItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest_RefundByItem.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest_RefundByItem) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44, 1}
}

func (x *RefundOrderRequest_RefundByItem) GetRefundItems() []*RefundOrderRequest_RefundByItem_RefundItem {
	if x != nil {
		return x.RefundItems
	}
	return nil
}

func (x *RefundOrderRequest_RefundByItem) GetRefundTips() *money.Money {
	if x != nil {
		return x.RefundTips
	}
	return nil
}

// Refund by payment.
type RefundOrderRequest_RefundByPayment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 退款的金额.
	RefundAmount *money.Money `protobuf:"bytes,1,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// 退款的金额的标记.
	RefundAmountFlags *RefundOrderRequest_RefundByPayment_RefundAmountFlags `protobuf:"bytes,2,opt,name=refund_amount_flags,json=refundAmountFlags,proto3" json:"refund_amount_flags,omitempty"`
	// 指定的要退款的 Order Payment 的 ID 列表.
	OrderPaymentIds []int64 `protobuf:"varint,3,rep,packed,name=order_payment_ids,json=orderPaymentIds,proto3" json:"order_payment_ids,omitempty"`
}

func (x *RefundOrderRequest_RefundByPayment) Reset() {
	*x = RefundOrderRequest_RefundByPayment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest_RefundByPayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest_RefundByPayment) ProtoMessage() {}

func (x *RefundOrderRequest_RefundByPayment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest_RefundByPayment.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest_RefundByPayment) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44, 2}
}

func (x *RefundOrderRequest_RefundByPayment) GetRefundAmount() *money.Money {
	if x != nil {
		return x.RefundAmount
	}
	return nil
}

func (x *RefundOrderRequest_RefundByPayment) GetRefundAmountFlags() *RefundOrderRequest_RefundByPayment_RefundAmountFlags {
	if x != nil {
		return x.RefundAmountFlags
	}
	return nil
}

func (x *RefundOrderRequest_RefundByPayment) GetOrderPaymentIds() []int64 {
	if x != nil {
		return x.OrderPaymentIds
	}
	return nil
}

// Refund item.
type RefundOrderRequest_RefundByItem_RefundItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order item ID.
	OrderItemId int64 `protobuf:"varint,1,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// Refund item mode.
	RefundItemMode v1.RefundItemMode `protobuf:"varint,2,opt,name=refund_item_mode,json=refundItemMode,proto3,enum=moego.models.order.v1.RefundItemMode" json:"refund_item_mode,omitempty"`
	// Refund param by mode.
	//
	// Types that are assignable to RefundItemModeParam:
	//
	//	*RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity
	//	*RefundOrderRequest_RefundByItem_RefundItem_RefundAmount
	RefundItemModeParam isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam `protobuf_oneof:"refund_item_mode_param"`
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) Reset() {
	*x = RefundOrderRequest_RefundByItem_RefundItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest_RefundByItem_RefundItem) ProtoMessage() {}

func (x *RefundOrderRequest_RefundByItem_RefundItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest_RefundByItem_RefundItem.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest_RefundByItem_RefundItem) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44, 1, 0}
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) GetRefundItemMode() v1.RefundItemMode {
	if x != nil {
		return x.RefundItemMode
	}
	return v1.RefundItemMode(0)
}

func (m *RefundOrderRequest_RefundByItem_RefundItem) GetRefundItemModeParam() isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam {
	if m != nil {
		return m.RefundItemModeParam
	}
	return nil
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) GetRefundQuantity() int64 {
	if x, ok := x.GetRefundItemModeParam().(*RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity); ok {
		return x.RefundQuantity
	}
	return 0
}

func (x *RefundOrderRequest_RefundByItem_RefundItem) GetRefundAmount() *money.Money {
	if x, ok := x.GetRefundItemModeParam().(*RefundOrderRequest_RefundByItem_RefundItem_RefundAmount); ok {
		return x.RefundAmount
	}
	return nil
}

type isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam interface {
	isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam()
}

type RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity struct {
	// Refund quantity. For REFUND_BY_QUANTITY.
	RefundQuantity int64 `protobuf:"varint,11,opt,name=refund_quantity,json=refundQuantity,proto3,oneof"`
}

type RefundOrderRequest_RefundByItem_RefundItem_RefundAmount struct {
	// Refund amount. For REFUND_BY_AMOUNT.
	RefundAmount *money.Money `protobuf:"bytes,12,opt,name=refund_amount,json=refundAmount,proto3,oneof"`
}

func (*RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity) isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam() {
}

func (*RefundOrderRequest_RefundByItem_RefundItem_RefundAmount) isRefundOrderRequest_RefundByItem_RefundItem_RefundItemModeParam() {
}

// 退款金额的标志.
type RefundOrderRequest_RefundByPayment_RefundAmountFlags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要退款的金额中是否包含了 Convenience Fee.
	IsConvenienceFeeIncluded bool `protobuf:"varint,1,opt,name=is_convenience_fee_included,json=isConvenienceFeeIncluded,proto3" json:"is_convenience_fee_included,omitempty"`
	// 内部使用，前端在接口中传这个值会被忽略
	// 退的是否是 deposit 金额，如果是，则不需要传 payment，金额全部退到 refund_deposit_amount
	IsDepositAmount *bool `protobuf:"varint,2,opt,name=is_deposit_amount,json=isDepositAmount,proto3,oneof" json:"is_deposit_amount,omitempty"`
}

func (x *RefundOrderRequest_RefundByPayment_RefundAmountFlags) Reset() {
	*x = RefundOrderRequest_RefundByPayment_RefundAmountFlags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderRequest_RefundByPayment_RefundAmountFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderRequest_RefundByPayment_RefundAmountFlags) ProtoMessage() {}

func (x *RefundOrderRequest_RefundByPayment_RefundAmountFlags) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderRequest_RefundByPayment_RefundAmountFlags.ProtoReflect.Descriptor instead.
func (*RefundOrderRequest_RefundByPayment_RefundAmountFlags) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{44, 2, 0}
}

func (x *RefundOrderRequest_RefundByPayment_RefundAmountFlags) GetIsConvenienceFeeIncluded() bool {
	if x != nil {
		return x.IsConvenienceFeeIncluded
	}
	return false
}

func (x *RefundOrderRequest_RefundByPayment_RefundAmountFlags) GetIsDepositAmount() bool {
	if x != nil && x.IsDepositAmount != nil {
		return *x.IsDepositAmount
	}
	return false
}

// Refund order fields for a related refund order.
type RefundOrderResponse_RelatedRefundOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order.
	RefundOrder *v1.RefundOrderModel `protobuf:"bytes,1,opt,name=refund_order,json=refundOrder,proto3" json:"refund_order,omitempty"`
	// Refund order items. Empty if REFUND_BY_AMOUNT.
	RefundOrderItems []*v1.RefundOrderItemModel `protobuf:"bytes,2,rep,name=refund_order_items,json=refundOrderItems,proto3" json:"refund_order_items,omitempty"`
	// Refund order payment.
	RefundOrderPayment []*v1.RefundOrderPaymentModel `protobuf:"bytes,3,rep,name=refund_order_payment,json=refundOrderPayment,proto3" json:"refund_order_payment,omitempty"`
}

func (x *RefundOrderResponse_RelatedRefundOrder) Reset() {
	*x = RefundOrderResponse_RelatedRefundOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderResponse_RelatedRefundOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderResponse_RelatedRefundOrder) ProtoMessage() {}

func (x *RefundOrderResponse_RelatedRefundOrder) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderResponse_RelatedRefundOrder.ProtoReflect.Descriptor instead.
func (*RefundOrderResponse_RelatedRefundOrder) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_service_proto_rawDescGZIP(), []int{45, 0}
}

func (x *RefundOrderResponse_RelatedRefundOrder) GetRefundOrder() *v1.RefundOrderModel {
	if x != nil {
		return x.RefundOrder
	}
	return nil
}

func (x *RefundOrderResponse_RelatedRefundOrder) GetRefundOrderItems() []*v1.RefundOrderItemModel {
	if x != nil {
		return x.RefundOrderItems
	}
	return nil
}

func (x *RefundOrderResponse_RelatedRefundOrder) GetRefundOrderPayment() []*v1.RefundOrderPaymentModel {
	if x != nil {
		return x.RefundOrderPayment
	}
	return nil
}

var File_moego_service_order_v1_order_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v1_order_service_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x02, 0x0a, 0x12,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0a, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x5f, 0x63, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c,
	0x6e, 0x6f, 0x74, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x33, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x11,
	0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x63, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xcc,
	0x03, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48,
	0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61,
	0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x65,
	0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x73, 0x12, 0x2a,
	0x0a, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x25, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x2d, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x22, 0xbb, 0x02, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x13, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x02, 0x69, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x67, 0x75,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x11, 0x48, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x06, 0x52, 0x06, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x05, 0x0a,
	0x03, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x22, 0xdb, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x26, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x33, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03,
	0x52, 0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22,
	0x5e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xb3, 0x04, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x6e, 0x63, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x47, 0x0a,
	0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x61, 0x78, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c,
	0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0f,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46,
	0x65, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0d, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x53, 0x0a, 0x0e, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x8b,
	0x04, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x02, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x08, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x6f, 0x72, 0x74,
	0x5f, 0x62, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x72, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3f, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x22, 0xc2, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xaf, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x47,
	0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69,
	0x6e, 0x65, 0x54, 0x61, 0x78, 0x65, 0x73, 0x22, 0xea, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x6b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x22, 0xd0, 0x02, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x61, 0x78, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x22, 0xa1, 0x02, 0x0a, 0x1e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa,
	0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x1f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x58, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xa8,
	0x02, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6f, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x0d, 0x53, 0x65,
	0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x22, 0x92, 0x03, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x73, 0x12, 0x4d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x1a, 0xa7, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d,
	0x08, 0x01, 0x10, 0xf4, 0x03, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x08, 0x01, 0x10, 0x14,
	0x28, 0x01, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x17,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xa7, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x49, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xc7, 0x04, 0x0a, 0x17,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x48, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78,
	0x65, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x73, 0x12,
	0x3f, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x11, 0x61,
	0x64, 0x64, 0x65, 0x64, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x73,
	0x12, 0x43, 0x0a, 0x16, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x13, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x73, 0x22, 0x32, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x6e, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x79, 0x0a, 0x1f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x1c, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x19, 0x45,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x6d, 0x0a, 0x1b,
	0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x18, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x45,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x47, 0x0a, 0x1d, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x22, 0x20, 0x0a, 0x1e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x45, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x66, 0x0a, 0x1c, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x20, 0x69, 0x73,
	0x5f, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x69, 0x73, 0x49, 0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x88, 0x08, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x11, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x67, 0x0a, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x3c, 0x0a, 0x15, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xff, 0x01, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x69, 0x64, 0x42, 0x79, 0x12, 0x5a, 0x0a, 0x0e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x54, 0x0a, 0x1a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x17, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x48, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x09, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x6b, 0x0a,
	0x10, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x57, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x80, 0x09, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x14,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x48, 0x01, 0x52, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x15, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x48, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x19, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x04, 0x52, 0x16, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x41, 0x66, 0x74, 0x65, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x48, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x07,
	0x48, 0x06, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x07, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x08, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x40, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x09, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0a, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x1a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x0b,
	0x52, 0x17, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x1d,
	0x0a, 0x1b, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0d, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x62, 0x0a, 0x20, 0x53, 0x79,
	0x6e, 0x63, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xf3,
	0x03, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x6b, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x0e,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52,
	0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x68, 0x0a,
	0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x62, 0x79, 0x22, 0xae, 0x0c, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x12, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x79, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x13, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x6c, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3b,
	0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x70,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x70, 0x73, 0x12, 0x61, 0x0a, 0x0c, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x85,
	0x01, 0x0a, 0x19, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x50, 0x0a, 0x1a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x66, 0x65, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x1a, 0xb6, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x3f, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x5f, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x12, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x1a, 0x96, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x4a,
	0x0a, 0x22, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x69, 0x73, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x65, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x1a, 0xb2, 0x02, 0x0a, 0x0e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x22, 0x0a,
	0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x12, 0x4f, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x41, 0x0a, 0x11, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x10, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x10, 0x0a,
	0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x1a,
	0x83, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb6, 0x02, 0x0a, 0x21, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x7a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x10,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x5c, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x96,
	0x02, 0x0a, 0x22, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x16, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x46, 0x65, 0x65, 0x12, 0x62, 0x0a, 0x15, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xea, 0x0b, 0x0a, 0x12, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x07, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x4c, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x6b, 0x0a,
	0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x0e, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x0c, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x68, 0x0a, 0x11, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x27, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x1a, 0xd3,
	0x03, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x65, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x69, 0x70, 0x73, 0x1a, 0xa6, 0x02, 0x0a, 0x0a,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2b, 0x0a, 0x0d, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0d, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x1d, 0x0a, 0x16, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x1a, 0xa0, 0x03, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42,
	0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x7c, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x11, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12,
	0x3a, 0x0a, 0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92,
	0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x1a, 0x99, 0x01, 0x0a, 0x11,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64,
	0x12, 0x2f, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0f, 0x69,
	0x73, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x62, 0x79, 0x22, 0xb2, 0x05, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x60, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x72, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x13, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x9d, 0x02, 0x0a, 0x12, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x4a, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x12,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x60, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x56, 0x0a, 0x1d, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x4e, 0x0a, 0x1e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e,
	0x63, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x64, 0x22, 0x70, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x56,
	0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56,
	0x31, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x73, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb0, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x56, 0x31, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22,
	0xe4, 0x01, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x51, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0xb1, 0x01, 0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x65, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x59, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x78, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x98, 0x03, 0x0a, 0x18, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x12, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0f, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x5c, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x53,
	0x68, 0x6f, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3f, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0xc5, 0x03, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x69, 0x70, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x09, 0x74, 0x69, 0x70, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x74,
	0x69, 0x70, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x69, 0x70, 0x42, 0x61,
	0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x59, 0x0a, 0x16, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x22, 0xfd, 0x03, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x14, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x22, 0x5d, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0x4c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31,
	0x0a, 0x10, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x81, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a,
	0x0f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x76, 0x65,
	0x72, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x65,
	0x64, 0x75, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xb3, 0x02, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x0f, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x6c, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x0d, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0f, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0d, 0x6e, 0x65, 0x77, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b,
	0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x6e, 0x65, 0x77, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0x22, 0x0a, 0x20, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x6b, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0xb3, 0x02, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x32, 0xa7, 0x24, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x66, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0b,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x63, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x56, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x62, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x69, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x70, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12,
	0x8a, 0x01, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x8f, 0x01, 0x0a,
	0x1c, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x58,
	0x0a, 0x07, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x69,
	0x70, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x63, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x75, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x13, 0x45, 0x64, 0x69, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x08, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x6c, 0x0a, 0x18, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7b, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01,
	0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x16,
	0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x56, 0x31, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x72, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x31, 0x12,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x78, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x53, 0x68,
	0x6f, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8d, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x66, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x0f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7a, 0x0a,
	0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_order_v1_order_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v1_order_service_proto_rawDescData = file_moego_service_order_v1_order_service_proto_rawDesc
)

func file_moego_service_order_v1_order_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v1_order_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v1_order_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v1_order_service_proto_rawDescData)
	})
	return file_moego_service_order_v1_order_service_proto_rawDescData
}

var file_moego_service_order_v1_order_service_proto_msgTypes = make([]protoimpl.MessageInfo, 83)
var file_moego_service_order_v1_order_service_proto_goTypes = []interface{}{
	(*CreateOrderRequest)(nil),                                   // 0: moego.service.order.v1.CreateOrderRequest
	(*UpdateOrderRequest)(nil),                                   // 1: moego.service.order.v1.UpdateOrderRequest
	(*CreateOrderResponse)(nil),                                  // 2: moego.service.order.v1.CreateOrderResponse
	(*UpdateOrderResponse)(nil),                                  // 3: moego.service.order.v1.UpdateOrderResponse
	(*GetOrderRequest)(nil),                                      // 4: moego.service.order.v1.GetOrderRequest
	(*GetOrderListRequest)(nil),                                  // 5: moego.service.order.v1.GetOrderListRequest
	(*GetOrderListResponse)(nil),                                 // 6: moego.service.order.v1.GetOrderListResponse
	(*UpdateOrderIncrRequest)(nil),                               // 7: moego.service.order.v1.UpdateOrderIncrRequest
	(*UpdateOrderIncrResponse)(nil),                              // 8: moego.service.order.v1.UpdateOrderIncrResponse
	(*GetRetailInvoicesRequest)(nil),                             // 9: moego.service.order.v1.GetRetailInvoicesRequest
	(*GetRetailInvoicesResponse)(nil),                            // 10: moego.service.order.v1.GetRetailInvoicesResponse
	(*GetOrderItemDetailRequest)(nil),                            // 11: moego.service.order.v1.GetOrderItemDetailRequest
	(*GetOrderItemDetailResponse)(nil),                           // 12: moego.service.order.v1.GetOrderItemDetailResponse
	(*GetTipsOrderListRequest)(nil),                              // 13: moego.service.order.v1.GetTipsOrderListRequest
	(*GetTipsOrderListResponse)(nil),                             // 14: moego.service.order.v1.GetTipsOrderListResponse
	(*ModifyItemTaxInput)(nil),                                   // 15: moego.service.order.v1.ModifyItemTaxInput
	(*ModifyItemTaxOutput)(nil),                                  // 16: moego.service.order.v1.ModifyItemTaxOutput
	(*OperateOrderServiceChargeInput)(nil),                       // 17: moego.service.order.v1.OperateOrderServiceChargeInput
	(*OperateOrderServiceChargeOutput)(nil),                      // 18: moego.service.order.v1.OperateOrderServiceChargeOutput
	(*SetTipsRequest)(nil),                                       // 19: moego.service.order.v1.SetTipsRequest
	(*SetTipsResult)(nil),                                        // 20: moego.service.order.v1.SetTipsResult
	(*ListOrdersRequest)(nil),                                    // 21: moego.service.order.v1.ListOrdersRequest
	(*ListOrdersResponse)(nil),                                   // 22: moego.service.order.v1.ListOrdersResponse
	(*GetOrderHistoryRequest)(nil),                               // 23: moego.service.order.v1.GetOrderHistoryRequest
	(*GetOrderHistoryResponse)(nil),                              // 24: moego.service.order.v1.GetOrderHistoryResponse
	(*UpdateExtraOrderRequest)(nil),                              // 25: moego.service.order.v1.UpdateExtraOrderRequest
	(*UpdateExtraOrderResponse)(nil),                             // 26: moego.service.order.v1.UpdateExtraOrderResponse
	(*GetGroomingDetailRelationRequest)(nil),                     // 27: moego.service.order.v1.GetGroomingDetailRelationRequest
	(*GetGroomingDetailRelationResponse)(nil),                    // 28: moego.service.order.v1.GetGroomingDetailRelationResponse
	(*EditStaffCommissionParams)(nil),                            // 29: moego.service.order.v1.EditStaffCommissionParams
	(*EditStaffCommissionResult)(nil),                            // 30: moego.service.order.v1.EditStaffCommissionResult
	(*UpgradeInvoiceReinventRequest)(nil),                        // 31: moego.service.order.v1.UpgradeInvoiceReinventRequest
	(*UpgradeInvoiceReinventResponse)(nil),                       // 32: moego.service.order.v1.UpgradeInvoiceReinventResponse
	(*CheckInvoiceReinventRequest)(nil),                          // 33: moego.service.order.v1.CheckInvoiceReinventRequest
	(*CheckInvoiceReinventResponse)(nil),                         // 34: moego.service.order.v1.CheckInvoiceReinventResponse
	(*PayOrderRequest)(nil),                                      // 35: moego.service.order.v1.PayOrderRequest
	(*PayOrderResponse)(nil),                                     // 36: moego.service.order.v1.PayOrderResponse
	(*UpdateOrderPaymentRequest)(nil),                            // 37: moego.service.order.v1.UpdateOrderPaymentRequest
	(*UpdateOrderPaymentResponse)(nil),                           // 38: moego.service.order.v1.UpdateOrderPaymentResponse
	(*SyncOrderPaymentAndOrderResponse)(nil),                     // 39: moego.service.order.v1.SyncOrderPaymentAndOrderResponse
	(*PreviewRefundOrderRequest)(nil),                            // 40: moego.service.order.v1.PreviewRefundOrderRequest
	(*PreviewRefundOrderResponse)(nil),                           // 41: moego.service.order.v1.PreviewRefundOrderResponse
	(*PreviewRefundOrderPaymentsRequest)(nil),                    // 42: moego.service.order.v1.PreviewRefundOrderPaymentsRequest
	(*PreviewRefundOrderPaymentsResponse)(nil),                   // 43: moego.service.order.v1.PreviewRefundOrderPaymentsResponse
	(*RefundOrderRequest)(nil),                                   // 44: moego.service.order.v1.RefundOrderRequest
	(*RefundOrderResponse)(nil),                                  // 45: moego.service.order.v1.RefundOrderResponse
	(*SyncRefundOrderPaymentRequest)(nil),                        // 46: moego.service.order.v1.SyncRefundOrderPaymentRequest
	(*SyncRefundOrderPaymentResponse)(nil),                       // 47: moego.service.order.v1.SyncRefundOrderPaymentResponse
	(*ListOrdersV1Request)(nil),                                  // 48: moego.service.order.v1.ListOrdersV1Request
	(*ListOrdersV1Response)(nil),                                 // 49: moego.service.order.v1.ListOrdersV1Response
	(*ListOrderDetailRequest)(nil),                               // 50: moego.service.order.v1.ListOrderDetailRequest
	(*ListOrderDetailResponse)(nil),                              // 51: moego.service.order.v1.ListOrderDetailResponse
	(*QueryOrderDetailRequest)(nil),                              // 52: moego.service.order.v1.QueryOrderDetailRequest
	(*QueryOrderDetailResponse)(nil),                             // 53: moego.service.order.v1.QueryOrderDetailResponse
	(*GetOrderDetailRequest)(nil),                                // 54: moego.service.order.v1.GetOrderDetailRequest
	(*GetOrderDetailResponse)(nil),                               // 55: moego.service.order.v1.GetOrderDetailResponse
	(*GetRefundOrderDetailRequest)(nil),                          // 56: moego.service.order.v1.GetRefundOrderDetailRequest
	(*GetRefundOrderDetailResponse)(nil),                         // 57: moego.service.order.v1.GetRefundOrderDetailResponse
	(*CreateNoShowOrderRequest)(nil),                             // 58: moego.service.order.v1.CreateNoShowOrderRequest
	(*CreateNoShowOrderResponse)(nil),                            // 59: moego.service.order.v1.CreateNoShowOrderResponse
	(*CreateTipOrderRequest)(nil),                                // 60: moego.service.order.v1.CreateTipOrderRequest
	(*CreateTipOrderResponse)(nil),                               // 61: moego.service.order.v1.CreateTipOrderResponse
	(*CreateDepositOrderRequest)(nil),                            // 62: moego.service.order.v1.CreateDepositOrderRequest
	(*CreateDepositOrderResponse)(nil),                           // 63: moego.service.order.v1.CreateDepositOrderResponse
	(*GetDepositDetailRequest)(nil),                              // 64: moego.service.order.v1.GetDepositDetailRequest
	(*GetDepositDetailResponse)(nil),                             // 65: moego.service.order.v1.GetDepositDetailResponse
	(*UpdateDepositOrderSourceRequest)(nil),                      // 66: moego.service.order.v1.UpdateDepositOrderSourceRequest
	(*UpdateDepositOrderSourceResponse)(nil),                     // 67: moego.service.order.v1.UpdateDepositOrderSourceResponse
	(*CancelOrderRequest)(nil),                                   // 68: moego.service.order.v1.CancelOrderRequest
	(*CancelOrderResponse)(nil),                                  // 69: moego.service.order.v1.CancelOrderResponse
	(*CreateInvoiceIDRequest)(nil),                               // 70: moego.service.order.v1.CreateInvoiceIDRequest
	(*CreateInvoiceIDResponse)(nil),                              // 71: moego.service.order.v1.CreateInvoiceIDResponse
	(*ListOrdersRequest_Filter)(nil),                             // 72: moego.service.order.v1.ListOrdersRequest.Filter
	(*PreviewRefundOrderResponse_RelatedRefundOrder)(nil),        // 73: moego.service.order.v1.PreviewRefundOrderResponse.RelatedRefundOrder
	(*PreviewRefundOrderResponse_RefundFlags)(nil),               // 74: moego.service.order.v1.PreviewRefundOrderResponse.RefundFlags
	(*PreviewRefundOrderResponse_RefundableItem)(nil),            // 75: moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem
	(*PreviewRefundOrderResponse_RefundableOrderPayment)(nil),    // 76: moego.service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment
	(*RefundOrderRequest_OrderPayment)(nil),                      // 77: moego.service.order.v1.RefundOrderRequest.OrderPayment
	(*RefundOrderRequest_RefundByItem)(nil),                      // 78: moego.service.order.v1.RefundOrderRequest.RefundByItem
	(*RefundOrderRequest_RefundByPayment)(nil),                   // 79: moego.service.order.v1.RefundOrderRequest.RefundByPayment
	(*RefundOrderRequest_RefundByItem_RefundItem)(nil),           // 80: moego.service.order.v1.RefundOrderRequest.RefundByItem.RefundItem
	(*RefundOrderRequest_RefundByPayment_RefundAmountFlags)(nil), // 81: moego.service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags
	(*RefundOrderResponse_RelatedRefundOrder)(nil),               // 82: moego.service.order.v1.RefundOrderResponse.RelatedRefundOrder
	(*v1.OrderModel)(nil),                                        // 83: moego.models.order.v1.OrderModel
	(*v1.OrderLineItemModel)(nil),                                // 84: moego.models.order.v1.OrderLineItemModel
	(*v1.OrderLineTaxModel)(nil),                                 // 85: moego.models.order.v1.OrderLineTaxModel
	(*v1.OrderLineDiscountModel)(nil),                            // 86: moego.models.order.v1.OrderLineDiscountModel
	(*v1.OrderLineExtraFeeModel)(nil),                            // 87: moego.models.order.v1.OrderLineExtraFeeModel
	(*v1.OrderDetailModel)(nil),                                  // 88: moego.models.order.v1.OrderDetailModel
	(*v1.RefundChannelResponse)(nil),                             // 89: moego.models.order.v1.RefundChannelResponse
	(*v2.PaginationRequest)(nil),                                 // 90: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                // 91: moego.utils.v2.PaginationResponse
	(*v1.GroomingDetailRelationModel)(nil),                       // 92: moego.models.order.v1.GroomingDetailRelationModel
	(*v1.EditStaffCommissionItem)(nil),                           // 93: moego.models.order.v1.EditStaffCommissionItem
	(*v11.PaymentMethodExtra)(nil),                               // 94: moego.models.payment.v1.PaymentMethodExtra
	(v1.OrderPaymentStatus)(0),                                   // 95: moego.models.order.v1.OrderPaymentStatus
	(*money.Money)(nil),                                          // 96: google.type.Money
	(*v1.OrderPaymentModel)(nil),                                 // 97: moego.models.order.v1.OrderPaymentModel
	(v1.RefundMode)(0),                                           // 98: moego.models.order.v1.RefundMode
	(*v1.OrderDetailModelV1)(nil),                                // 99: moego.models.order.v1.OrderDetailModelV1
	(*v1.RefundOrderDetailModel)(nil),                            // 100: moego.models.order.v1.RefundOrderDetailModel
	(*v1.RefundOrderPaymentModel)(nil),                           // 101: moego.models.order.v1.RefundOrderPaymentModel
	(*v1.RefundOrderModel)(nil),                                  // 102: moego.models.order.v1.RefundOrderModel
	(*v1.RefundOrderItemModel)(nil),                              // 103: moego.models.order.v1.RefundOrderItemModel
	(*v1.OrderModelV1)(nil),                                      // 104: moego.models.order.v1.OrderModelV1
	(v1.OrderSourceType)(0),                                      // 105: moego.models.order.v1.OrderSourceType
	(*v1.PriceDetailModel)(nil),                                  // 106: moego.models.order.v1.PriceDetailModel
	(*interval.Interval)(nil),                                    // 107: google.type.Interval
	(v1.RefundItemMode)(0),                                       // 108: moego.models.order.v1.RefundItemMode
	(*emptypb.Empty)(nil),                                        // 109: google.protobuf.Empty
}
var file_moego_service_order_v1_order_service_proto_depIdxs = []int32{
	83,  // 0: moego.service.order.v1.CreateOrderRequest.order:type_name -> moego.models.order.v1.OrderModel
	84,  // 1: moego.service.order.v1.CreateOrderRequest.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	83,  // 2: moego.service.order.v1.UpdateOrderRequest.order:type_name -> moego.models.order.v1.OrderModel
	84,  // 3: moego.service.order.v1.UpdateOrderRequest.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	85,  // 4: moego.service.order.v1.UpdateOrderRequest.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	86,  // 5: moego.service.order.v1.UpdateOrderRequest.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	87,  // 6: moego.service.order.v1.UpdateOrderRequest.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	88,  // 7: moego.service.order.v1.GetOrderListResponse.order_list:type_name -> moego.models.order.v1.OrderDetailModel
	83,  // 8: moego.service.order.v1.UpdateOrderIncrRequest.order:type_name -> moego.models.order.v1.OrderModel
	84,  // 9: moego.service.order.v1.UpdateOrderIncrRequest.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	85,  // 10: moego.service.order.v1.UpdateOrderIncrRequest.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	86,  // 11: moego.service.order.v1.UpdateOrderIncrRequest.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	87,  // 12: moego.service.order.v1.UpdateOrderIncrRequest.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	89,  // 13: moego.service.order.v1.UpdateOrderIncrResponse.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	88,  // 14: moego.service.order.v1.GetRetailInvoicesResponse.orders:type_name -> moego.models.order.v1.OrderDetailModel
	84,  // 15: moego.service.order.v1.GetOrderItemDetailResponse.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	85,  // 16: moego.service.order.v1.GetOrderItemDetailResponse.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	83,  // 17: moego.service.order.v1.GetTipsOrderListResponse.orders:type_name -> moego.models.order.v1.OrderModel
	89,  // 18: moego.service.order.v1.ModifyItemTaxOutput.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	89,  // 19: moego.service.order.v1.OperateOrderServiceChargeOutput.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	89,  // 20: moego.service.order.v1.SetTipsResult.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	90,  // 21: moego.service.order.v1.ListOrdersRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	72,  // 22: moego.service.order.v1.ListOrdersRequest.filter:type_name -> moego.service.order.v1.ListOrdersRequest.Filter
	91,  // 23: moego.service.order.v1.ListOrdersResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	83,  // 24: moego.service.order.v1.ListOrdersResponse.orders:type_name -> moego.models.order.v1.OrderModel
	83,  // 25: moego.service.order.v1.GetOrderHistoryResponse.order_models:type_name -> moego.models.order.v1.OrderModel
	83,  // 26: moego.service.order.v1.UpdateExtraOrderRequest.order:type_name -> moego.models.order.v1.OrderModel
	84,  // 27: moego.service.order.v1.UpdateExtraOrderRequest.line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	85,  // 28: moego.service.order.v1.UpdateExtraOrderRequest.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	86,  // 29: moego.service.order.v1.UpdateExtraOrderRequest.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	87,  // 30: moego.service.order.v1.UpdateExtraOrderRequest.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	92,  // 31: moego.service.order.v1.GetGroomingDetailRelationResponse.grooming_detail_relation_models:type_name -> moego.models.order.v1.GroomingDetailRelationModel
	93,  // 32: moego.service.order.v1.EditStaffCommissionParams.edit_staff_commission_items:type_name -> moego.models.order.v1.EditStaffCommissionItem
	94,  // 33: moego.service.order.v1.PayOrderRequest.payment_method_extra:type_name -> moego.models.payment.v1.PaymentMethodExtra
	95,  // 34: moego.service.order.v1.PayOrderRequest.payment_status:type_name -> moego.models.order.v1.OrderPaymentStatus
	96,  // 35: moego.service.order.v1.PayOrderRequest.total_amount:type_name -> google.type.Money
	96,  // 36: moego.service.order.v1.PayOrderRequest.amount:type_name -> google.type.Money
	96,  // 37: moego.service.order.v1.PayOrderRequest.payment_tips_before_create:type_name -> google.type.Money
	96,  // 38: moego.service.order.v1.PayOrderRequest.convenience_fee:type_name -> google.type.Money
	97,  // 39: moego.service.order.v1.PayOrderResponse.order_payment:type_name -> moego.models.order.v1.OrderPaymentModel
	95,  // 40: moego.service.order.v1.UpdateOrderPaymentRequest.payment_status:type_name -> moego.models.order.v1.OrderPaymentStatus
	94,  // 41: moego.service.order.v1.UpdateOrderPaymentRequest.payment_method_extra:type_name -> moego.models.payment.v1.PaymentMethodExtra
	96,  // 42: moego.service.order.v1.UpdateOrderPaymentRequest.total_amount:type_name -> google.type.Money
	96,  // 43: moego.service.order.v1.UpdateOrderPaymentRequest.payment_tips_after_create:type_name -> google.type.Money
	96,  // 44: moego.service.order.v1.UpdateOrderPaymentRequest.processing_fee:type_name -> google.type.Money
	96,  // 45: moego.service.order.v1.UpdateOrderPaymentRequest.convenience_fee:type_name -> google.type.Money
	96,  // 46: moego.service.order.v1.UpdateOrderPaymentRequest.payment_tips_before_create:type_name -> google.type.Money
	97,  // 47: moego.service.order.v1.UpdateOrderPaymentResponse.order_payment:type_name -> moego.models.order.v1.OrderPaymentModel
	98,  // 48: moego.service.order.v1.PreviewRefundOrderRequest.refund_mode:type_name -> moego.models.order.v1.RefundMode
	77,  // 49: moego.service.order.v1.PreviewRefundOrderRequest.source_order_payments:type_name -> moego.service.order.v1.RefundOrderRequest.OrderPayment
	78,  // 50: moego.service.order.v1.PreviewRefundOrderRequest.refund_by_item:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByItem
	79,  // 51: moego.service.order.v1.PreviewRefundOrderRequest.refund_by_payment:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment
	99,  // 52: moego.service.order.v1.PreviewRefundOrderResponse.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	100, // 53: moego.service.order.v1.PreviewRefundOrderResponse.preview_refund_order:type_name -> moego.models.order.v1.RefundOrderDetailModel
	73,  // 54: moego.service.order.v1.PreviewRefundOrderResponse.related_refund_orders:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RelatedRefundOrder
	75,  // 55: moego.service.order.v1.PreviewRefundOrderResponse.refundable_items:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem
	96,  // 56: moego.service.order.v1.PreviewRefundOrderResponse.refundable_tips:type_name -> google.type.Money
	74,  // 57: moego.service.order.v1.PreviewRefundOrderResponse.refund_flags:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundFlags
	76,  // 58: moego.service.order.v1.PreviewRefundOrderResponse.refundable_order_payments:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment
	96,  // 59: moego.service.order.v1.PreviewRefundOrderResponse.refundable_convenience_fee:type_name -> google.type.Money
	96,  // 60: moego.service.order.v1.PreviewRefundOrderPaymentsRequest.refund_amount:type_name -> google.type.Money
	81,  // 61: moego.service.order.v1.PreviewRefundOrderPaymentsRequest.refund_amount_flag:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags
	97,  // 62: moego.service.order.v1.PreviewRefundOrderPaymentsRequest.source_order_payments:type_name -> moego.models.order.v1.OrderPaymentModel
	96,  // 63: moego.service.order.v1.PreviewRefundOrderPaymentsResponse.refund_total_amount:type_name -> google.type.Money
	96,  // 64: moego.service.order.v1.PreviewRefundOrderPaymentsResponse.refund_convenience_fee:type_name -> google.type.Money
	101, // 65: moego.service.order.v1.PreviewRefundOrderPaymentsResponse.refund_order_payments:type_name -> moego.models.order.v1.RefundOrderPaymentModel
	98,  // 66: moego.service.order.v1.RefundOrderRequest.refund_mode:type_name -> moego.models.order.v1.RefundMode
	77,  // 67: moego.service.order.v1.RefundOrderRequest.source_order_payments:type_name -> moego.service.order.v1.RefundOrderRequest.OrderPayment
	78,  // 68: moego.service.order.v1.RefundOrderRequest.refund_by_item:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByItem
	79,  // 69: moego.service.order.v1.RefundOrderRequest.refund_by_payment:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment
	102, // 70: moego.service.order.v1.RefundOrderResponse.refund_order:type_name -> moego.models.order.v1.RefundOrderModel
	103, // 71: moego.service.order.v1.RefundOrderResponse.refund_order_items:type_name -> moego.models.order.v1.RefundOrderItemModel
	101, // 72: moego.service.order.v1.RefundOrderResponse.refund_order_payment:type_name -> moego.models.order.v1.RefundOrderPaymentModel
	82,  // 73: moego.service.order.v1.RefundOrderResponse.related_refund_orders:type_name -> moego.service.order.v1.RefundOrderResponse.RelatedRefundOrder
	104, // 74: moego.service.order.v1.ListOrdersV1Response.orders:type_name -> moego.models.order.v1.OrderModelV1
	102, // 75: moego.service.order.v1.ListOrdersV1Response.refund_orders:type_name -> moego.models.order.v1.RefundOrderModel
	99,  // 76: moego.service.order.v1.ListOrderDetailResponse.orders:type_name -> moego.models.order.v1.OrderDetailModelV1
	100, // 77: moego.service.order.v1.ListOrderDetailResponse.refund_orders:type_name -> moego.models.order.v1.RefundOrderDetailModel
	105, // 78: moego.service.order.v1.QueryOrderDetailRequest.source_type:type_name -> moego.models.order.v1.OrderSourceType
	99,  // 79: moego.service.order.v1.QueryOrderDetailResponse.orders:type_name -> moego.models.order.v1.OrderDetailModelV1
	100, // 80: moego.service.order.v1.QueryOrderDetailResponse.refund_orders:type_name -> moego.models.order.v1.RefundOrderDetailModel
	99,  // 81: moego.service.order.v1.GetOrderDetailResponse.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	100, // 82: moego.service.order.v1.GetRefundOrderDetailResponse.refund_order:type_name -> moego.models.order.v1.RefundOrderDetailModel
	105, // 83: moego.service.order.v1.CreateNoShowOrderRequest.source_type:type_name -> moego.models.order.v1.OrderSourceType
	96,  // 84: moego.service.order.v1.CreateNoShowOrderRequest.no_show_fee_amount:type_name -> google.type.Money
	99,  // 85: moego.service.order.v1.CreateNoShowOrderResponse.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	105, // 86: moego.service.order.v1.CreateTipOrderRequest.source_type:type_name -> moego.models.order.v1.OrderSourceType
	96,  // 87: moego.service.order.v1.CreateTipOrderRequest.tip_amount:type_name -> google.type.Money
	96,  // 88: moego.service.order.v1.CreateTipOrderRequest.tip_based_amount:type_name -> google.type.Money
	99,  // 89: moego.service.order.v1.CreateTipOrderResponse.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	105, // 90: moego.service.order.v1.CreateDepositOrderRequest.source_type:type_name -> moego.models.order.v1.OrderSourceType
	96,  // 91: moego.service.order.v1.CreateDepositOrderRequest.deposit_amount:type_name -> google.type.Money
	106, // 92: moego.service.order.v1.CreateDepositOrderRequest.deposit_price_detail:type_name -> moego.models.order.v1.PriceDetailModel
	99,  // 93: moego.service.order.v1.CreateDepositOrderResponse.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	96,  // 94: moego.service.order.v1.GetDepositDetailResponse.collected_amount:type_name -> google.type.Money
	96,  // 95: moego.service.order.v1.GetDepositDetailResponse.reversed_amount:type_name -> google.type.Money
	96,  // 96: moego.service.order.v1.GetDepositDetailResponse.deducted_amount:type_name -> google.type.Money
	96,  // 97: moego.service.order.v1.GetDepositDetailResponse.balance:type_name -> google.type.Money
	105, // 98: moego.service.order.v1.UpdateDepositOrderSourceRequest.old_source_type:type_name -> moego.models.order.v1.OrderSourceType
	105, // 99: moego.service.order.v1.UpdateDepositOrderSourceRequest.new_source_type:type_name -> moego.models.order.v1.OrderSourceType
	105, // 100: moego.service.order.v1.CreateInvoiceIDRequest.source_type:type_name -> moego.models.order.v1.OrderSourceType
	107, // 101: moego.service.order.v1.ListOrdersRequest.Filter.last_updated_time_range:type_name -> google.type.Interval
	99,  // 102: moego.service.order.v1.PreviewRefundOrderResponse.RelatedRefundOrder.order:type_name -> moego.models.order.v1.OrderDetailModelV1
	100, // 103: moego.service.order.v1.PreviewRefundOrderResponse.RelatedRefundOrder.preview_refund_order:type_name -> moego.models.order.v1.RefundOrderDetailModel
	108, // 104: moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem.refund_item_mode:type_name -> moego.models.order.v1.RefundItemMode
	96,  // 105: moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem.refundable_amount:type_name -> google.type.Money
	96,  // 106: moego.service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment.refundable_amount:type_name -> google.type.Money
	80,  // 107: moego.service.order.v1.RefundOrderRequest.RefundByItem.refund_items:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByItem.RefundItem
	96,  // 108: moego.service.order.v1.RefundOrderRequest.RefundByItem.refund_tips:type_name -> google.type.Money
	96,  // 109: moego.service.order.v1.RefundOrderRequest.RefundByPayment.refund_amount:type_name -> google.type.Money
	81,  // 110: moego.service.order.v1.RefundOrderRequest.RefundByPayment.refund_amount_flags:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags
	108, // 111: moego.service.order.v1.RefundOrderRequest.RefundByItem.RefundItem.refund_item_mode:type_name -> moego.models.order.v1.RefundItemMode
	96,  // 112: moego.service.order.v1.RefundOrderRequest.RefundByItem.RefundItem.refund_amount:type_name -> google.type.Money
	102, // 113: moego.service.order.v1.RefundOrderResponse.RelatedRefundOrder.refund_order:type_name -> moego.models.order.v1.RefundOrderModel
	103, // 114: moego.service.order.v1.RefundOrderResponse.RelatedRefundOrder.refund_order_items:type_name -> moego.models.order.v1.RefundOrderItemModel
	101, // 115: moego.service.order.v1.RefundOrderResponse.RelatedRefundOrder.refund_order_payment:type_name -> moego.models.order.v1.RefundOrderPaymentModel
	0,   // 116: moego.service.order.v1.OrderService.CreateOrder:input_type -> moego.service.order.v1.CreateOrderRequest
	1,   // 117: moego.service.order.v1.OrderService.UpdateOrder:input_type -> moego.service.order.v1.UpdateOrderRequest
	7,   // 118: moego.service.order.v1.OrderService.UpdateOrderIncremental:input_type -> moego.service.order.v1.UpdateOrderIncrRequest
	4,   // 119: moego.service.order.v1.OrderService.GetOrder:input_type -> moego.service.order.v1.GetOrderRequest
	4,   // 120: moego.service.order.v1.OrderService.GetOrderDetail:input_type -> moego.service.order.v1.GetOrderRequest
	5,   // 121: moego.service.order.v1.OrderService.GetOrderList:input_type -> moego.service.order.v1.GetOrderListRequest
	9,   // 122: moego.service.order.v1.OrderService.GetRetailOrderList:input_type -> moego.service.order.v1.GetRetailInvoicesRequest
	11,  // 123: moego.service.order.v1.OrderService.GetOrderItemDetail:input_type -> moego.service.order.v1.GetOrderItemDetailRequest
	13,  // 124: moego.service.order.v1.OrderService.GetTipsOrderList:input_type -> moego.service.order.v1.GetTipsOrderListRequest
	15,  // 125: moego.service.order.v1.OrderService.ModifyItemTax:input_type -> moego.service.order.v1.ModifyItemTaxInput
	17,  // 126: moego.service.order.v1.OrderService.AddServiceChargeToOrder:input_type -> moego.service.order.v1.OperateOrderServiceChargeInput
	17,  // 127: moego.service.order.v1.OrderService.RemoveServiceChargeFromOrder:input_type -> moego.service.order.v1.OperateOrderServiceChargeInput
	19,  // 128: moego.service.order.v1.OrderService.SetTips:input_type -> moego.service.order.v1.SetTipsRequest
	21,  // 129: moego.service.order.v1.OrderService.ListOrders:input_type -> moego.service.order.v1.ListOrdersRequest
	23,  // 130: moego.service.order.v1.OrderService.GetOrderHistory:input_type -> moego.service.order.v1.GetOrderHistoryRequest
	25,  // 131: moego.service.order.v1.OrderService.UpdateExtraOrder:input_type -> moego.service.order.v1.UpdateExtraOrderRequest
	29,  // 132: moego.service.order.v1.OrderService.EditStaffCommission:input_type -> moego.service.order.v1.EditStaffCommissionParams
	27,  // 133: moego.service.order.v1.OrderService.GetGroomingDetailRelation:input_type -> moego.service.order.v1.GetGroomingDetailRelationRequest
	31,  // 134: moego.service.order.v1.OrderService.UpgradeInvoiceReinvent:input_type -> moego.service.order.v1.UpgradeInvoiceReinventRequest
	33,  // 135: moego.service.order.v1.OrderService.CheckInvoiceReinvent:input_type -> moego.service.order.v1.CheckInvoiceReinventRequest
	35,  // 136: moego.service.order.v1.OrderService.PayOrder:input_type -> moego.service.order.v1.PayOrderRequest
	37,  // 137: moego.service.order.v1.OrderService.UpdateOrderPayment:input_type -> moego.service.order.v1.UpdateOrderPaymentRequest
	109, // 138: moego.service.order.v1.OrderService.SyncOrderPaymentAndOrder:input_type -> google.protobuf.Empty
	40,  // 139: moego.service.order.v1.OrderService.PreviewRefundOrder:input_type -> moego.service.order.v1.PreviewRefundOrderRequest
	42,  // 140: moego.service.order.v1.OrderService.PreviewRefundOrderPayments:input_type -> moego.service.order.v1.PreviewRefundOrderPaymentsRequest
	44,  // 141: moego.service.order.v1.OrderService.RefundOrder:input_type -> moego.service.order.v1.RefundOrderRequest
	46,  // 142: moego.service.order.v1.OrderService.SyncRefundOrderPayment:input_type -> moego.service.order.v1.SyncRefundOrderPaymentRequest
	48,  // 143: moego.service.order.v1.OrderService.ListOrdersV1:input_type -> moego.service.order.v1.ListOrdersV1Request
	50,  // 144: moego.service.order.v1.OrderService.ListOrderDetail:input_type -> moego.service.order.v1.ListOrderDetailRequest
	52,  // 145: moego.service.order.v1.OrderService.QueryOrderDetail:input_type -> moego.service.order.v1.QueryOrderDetailRequest
	54,  // 146: moego.service.order.v1.OrderService.GetOrderDetailV1:input_type -> moego.service.order.v1.GetOrderDetailRequest
	56,  // 147: moego.service.order.v1.OrderService.GetRefundOrderDetail:input_type -> moego.service.order.v1.GetRefundOrderDetailRequest
	58,  // 148: moego.service.order.v1.OrderService.CreateNoShowOrder:input_type -> moego.service.order.v1.CreateNoShowOrderRequest
	60,  // 149: moego.service.order.v1.OrderService.CreateTipOrder:input_type -> moego.service.order.v1.CreateTipOrderRequest
	62,  // 150: moego.service.order.v1.OrderService.CreateDepositOrder:input_type -> moego.service.order.v1.CreateDepositOrderRequest
	64,  // 151: moego.service.order.v1.OrderService.GetDepositDetail:input_type -> moego.service.order.v1.GetDepositDetailRequest
	66,  // 152: moego.service.order.v1.OrderService.UpdateDepositOrderSource:input_type -> moego.service.order.v1.UpdateDepositOrderSourceRequest
	68,  // 153: moego.service.order.v1.OrderService.CancelOrder:input_type -> moego.service.order.v1.CancelOrderRequest
	70,  // 154: moego.service.order.v1.OrderService.CreateInvoiceID:input_type -> moego.service.order.v1.CreateInvoiceIDRequest
	2,   // 155: moego.service.order.v1.OrderService.CreateOrder:output_type -> moego.service.order.v1.CreateOrderResponse
	3,   // 156: moego.service.order.v1.OrderService.UpdateOrder:output_type -> moego.service.order.v1.UpdateOrderResponse
	8,   // 157: moego.service.order.v1.OrderService.UpdateOrderIncremental:output_type -> moego.service.order.v1.UpdateOrderIncrResponse
	83,  // 158: moego.service.order.v1.OrderService.GetOrder:output_type -> moego.models.order.v1.OrderModel
	88,  // 159: moego.service.order.v1.OrderService.GetOrderDetail:output_type -> moego.models.order.v1.OrderDetailModel
	6,   // 160: moego.service.order.v1.OrderService.GetOrderList:output_type -> moego.service.order.v1.GetOrderListResponse
	10,  // 161: moego.service.order.v1.OrderService.GetRetailOrderList:output_type -> moego.service.order.v1.GetRetailInvoicesResponse
	12,  // 162: moego.service.order.v1.OrderService.GetOrderItemDetail:output_type -> moego.service.order.v1.GetOrderItemDetailResponse
	14,  // 163: moego.service.order.v1.OrderService.GetTipsOrderList:output_type -> moego.service.order.v1.GetTipsOrderListResponse
	16,  // 164: moego.service.order.v1.OrderService.ModifyItemTax:output_type -> moego.service.order.v1.ModifyItemTaxOutput
	18,  // 165: moego.service.order.v1.OrderService.AddServiceChargeToOrder:output_type -> moego.service.order.v1.OperateOrderServiceChargeOutput
	18,  // 166: moego.service.order.v1.OrderService.RemoveServiceChargeFromOrder:output_type -> moego.service.order.v1.OperateOrderServiceChargeOutput
	20,  // 167: moego.service.order.v1.OrderService.SetTips:output_type -> moego.service.order.v1.SetTipsResult
	22,  // 168: moego.service.order.v1.OrderService.ListOrders:output_type -> moego.service.order.v1.ListOrdersResponse
	24,  // 169: moego.service.order.v1.OrderService.GetOrderHistory:output_type -> moego.service.order.v1.GetOrderHistoryResponse
	26,  // 170: moego.service.order.v1.OrderService.UpdateExtraOrder:output_type -> moego.service.order.v1.UpdateExtraOrderResponse
	30,  // 171: moego.service.order.v1.OrderService.EditStaffCommission:output_type -> moego.service.order.v1.EditStaffCommissionResult
	28,  // 172: moego.service.order.v1.OrderService.GetGroomingDetailRelation:output_type -> moego.service.order.v1.GetGroomingDetailRelationResponse
	32,  // 173: moego.service.order.v1.OrderService.UpgradeInvoiceReinvent:output_type -> moego.service.order.v1.UpgradeInvoiceReinventResponse
	34,  // 174: moego.service.order.v1.OrderService.CheckInvoiceReinvent:output_type -> moego.service.order.v1.CheckInvoiceReinventResponse
	36,  // 175: moego.service.order.v1.OrderService.PayOrder:output_type -> moego.service.order.v1.PayOrderResponse
	38,  // 176: moego.service.order.v1.OrderService.UpdateOrderPayment:output_type -> moego.service.order.v1.UpdateOrderPaymentResponse
	39,  // 177: moego.service.order.v1.OrderService.SyncOrderPaymentAndOrder:output_type -> moego.service.order.v1.SyncOrderPaymentAndOrderResponse
	41,  // 178: moego.service.order.v1.OrderService.PreviewRefundOrder:output_type -> moego.service.order.v1.PreviewRefundOrderResponse
	43,  // 179: moego.service.order.v1.OrderService.PreviewRefundOrderPayments:output_type -> moego.service.order.v1.PreviewRefundOrderPaymentsResponse
	45,  // 180: moego.service.order.v1.OrderService.RefundOrder:output_type -> moego.service.order.v1.RefundOrderResponse
	47,  // 181: moego.service.order.v1.OrderService.SyncRefundOrderPayment:output_type -> moego.service.order.v1.SyncRefundOrderPaymentResponse
	49,  // 182: moego.service.order.v1.OrderService.ListOrdersV1:output_type -> moego.service.order.v1.ListOrdersV1Response
	51,  // 183: moego.service.order.v1.OrderService.ListOrderDetail:output_type -> moego.service.order.v1.ListOrderDetailResponse
	53,  // 184: moego.service.order.v1.OrderService.QueryOrderDetail:output_type -> moego.service.order.v1.QueryOrderDetailResponse
	55,  // 185: moego.service.order.v1.OrderService.GetOrderDetailV1:output_type -> moego.service.order.v1.GetOrderDetailResponse
	57,  // 186: moego.service.order.v1.OrderService.GetRefundOrderDetail:output_type -> moego.service.order.v1.GetRefundOrderDetailResponse
	59,  // 187: moego.service.order.v1.OrderService.CreateNoShowOrder:output_type -> moego.service.order.v1.CreateNoShowOrderResponse
	61,  // 188: moego.service.order.v1.OrderService.CreateTipOrder:output_type -> moego.service.order.v1.CreateTipOrderResponse
	63,  // 189: moego.service.order.v1.OrderService.CreateDepositOrder:output_type -> moego.service.order.v1.CreateDepositOrderResponse
	65,  // 190: moego.service.order.v1.OrderService.GetDepositDetail:output_type -> moego.service.order.v1.GetDepositDetailResponse
	67,  // 191: moego.service.order.v1.OrderService.UpdateDepositOrderSource:output_type -> moego.service.order.v1.UpdateDepositOrderSourceResponse
	69,  // 192: moego.service.order.v1.OrderService.CancelOrder:output_type -> moego.service.order.v1.CancelOrderResponse
	71,  // 193: moego.service.order.v1.OrderService.CreateInvoiceID:output_type -> moego.service.order.v1.CreateInvoiceIDResponse
	155, // [155:194] is the sub-list for method output_type
	116, // [116:155] is the sub-list for method input_type
	116, // [116:116] is the sub-list for extension type_name
	116, // [116:116] is the sub-list for extension extendee
	0,   // [0:116] is the sub-list for field type_name
}

func init() { file_moego_service_order_v1_order_service_proto_init() }
func file_moego_service_order_v1_order_service_proto_init() {
	if File_moego_service_order_v1_order_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v1_order_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderIncrRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderIncrResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRetailInvoicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRetailInvoicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderItemDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderItemDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsOrderListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsOrderListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyItemTaxInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyItemTaxOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateOrderServiceChargeInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateOrderServiceChargeOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTipsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExtraOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExtraOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingDetailRelationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingDetailRelationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeInvoiceReinventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeInvoiceReinventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInvoiceReinventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInvoiceReinventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOrderPaymentAndOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderPaymentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderPaymentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncRefundOrderPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncRefundOrderPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersV1Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersV1Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryOrderDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundOrderDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNoShowOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNoShowOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTipOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTipOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepositDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepositDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDepositOrderSourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDepositOrderSourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInvoiceIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInvoiceIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResponse_RelatedRefundOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResponse_RefundFlags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResponse_RefundableItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResponse_RefundableOrderPayment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest_OrderPayment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest_RefundByItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest_RefundByPayment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest_RefundByItem_RefundItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderRequest_RefundByPayment_RefundAmountFlags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderResponse_RelatedRefundOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_order_v1_order_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[35].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[37].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_order_service_proto_msgTypes[40].OneofWrappers = []interface{}{
		(*PreviewRefundOrderRequest_RefundByItem)(nil),
		(*PreviewRefundOrderRequest_RefundByPayment)(nil),
	}
	file_moego_service_order_v1_order_service_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*RefundOrderRequest_RefundByItem_)(nil),
		(*RefundOrderRequest_RefundByPayment_)(nil),
	}
	file_moego_service_order_v1_order_service_proto_msgTypes[75].OneofWrappers = []interface{}{
		(*PreviewRefundOrderResponse_RefundableItem_RefundableQuantity)(nil),
		(*PreviewRefundOrderResponse_RefundableItem_RefundableAmount)(nil),
	}
	file_moego_service_order_v1_order_service_proto_msgTypes[80].OneofWrappers = []interface{}{
		(*RefundOrderRequest_RefundByItem_RefundItem_RefundQuantity)(nil),
		(*RefundOrderRequest_RefundByItem_RefundItem_RefundAmount)(nil),
	}
	file_moego_service_order_v1_order_service_proto_msgTypes[81].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v1_order_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   83,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v1_order_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v1_order_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v1_order_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v1_order_service_proto = out.File
	file_moego_service_order_v1_order_service_proto_rawDesc = nil
	file_moego_service_order_v1_order_service_proto_goTypes = nil
	file_moego_service_order_v1_order_service_proto_depIdxs = nil
}
