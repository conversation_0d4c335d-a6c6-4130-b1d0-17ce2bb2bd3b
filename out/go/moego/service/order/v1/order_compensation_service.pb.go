// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v1/order_compensation_service.proto

package ordersvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get order id by business id and create time range request
type GetIDByBusinessIDAndTimeRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// time range
	TimeRange *interval.Interval `protobuf:"bytes,2,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetIDByBusinessIDAndTimeRangeRequest) Reset() {
	*x = GetIDByBusinessIDAndTimeRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_compensation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIDByBusinessIDAndTimeRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIDByBusinessIDAndTimeRangeRequest) ProtoMessage() {}

func (x *GetIDByBusinessIDAndTimeRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_compensation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIDByBusinessIDAndTimeRangeRequest.ProtoReflect.Descriptor instead.
func (*GetIDByBusinessIDAndTimeRangeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_compensation_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetIDByBusinessIDAndTimeRangeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetIDByBusinessIDAndTimeRangeRequest) GetTimeRange() *interval.Interval {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// order id response
type GetIDByBusinessIDAndTimeRangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id list
	OrderIds []int64 `protobuf:"varint,1,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
}

func (x *GetIDByBusinessIDAndTimeRangeResponse) Reset() {
	*x = GetIDByBusinessIDAndTimeRangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_compensation_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIDByBusinessIDAndTimeRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIDByBusinessIDAndTimeRangeResponse) ProtoMessage() {}

func (x *GetIDByBusinessIDAndTimeRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_compensation_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIDByBusinessIDAndTimeRangeResponse.ProtoReflect.Descriptor instead.
func (*GetIDByBusinessIDAndTimeRangeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_compensation_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetIDByBusinessIDAndTimeRangeResponse) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

var File_moego_service_order_v1_order_compensation_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v1_order_compensation_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x49, 0x44,
	0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0a, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22,
	0x44, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x49, 0x44, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x32, 0xb9, 0x01, 0x0a, 0x18, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x70, 0x65, 0x6e, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x44, 0x42, 0x79, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x49, 0x44, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x41,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49,
	0x44, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x41, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x7a, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_order_v1_order_compensation_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v1_order_compensation_service_proto_rawDescData = file_moego_service_order_v1_order_compensation_service_proto_rawDesc
)

func file_moego_service_order_v1_order_compensation_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v1_order_compensation_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v1_order_compensation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v1_order_compensation_service_proto_rawDescData)
	})
	return file_moego_service_order_v1_order_compensation_service_proto_rawDescData
}

var file_moego_service_order_v1_order_compensation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_order_v1_order_compensation_service_proto_goTypes = []interface{}{
	(*GetIDByBusinessIDAndTimeRangeRequest)(nil),  // 0: moego.service.order.v1.GetIDByBusinessIDAndTimeRangeRequest
	(*GetIDByBusinessIDAndTimeRangeResponse)(nil), // 1: moego.service.order.v1.GetIDByBusinessIDAndTimeRangeResponse
	(*interval.Interval)(nil),                     // 2: google.type.Interval
}
var file_moego_service_order_v1_order_compensation_service_proto_depIdxs = []int32{
	2, // 0: moego.service.order.v1.GetIDByBusinessIDAndTimeRangeRequest.time_range:type_name -> google.type.Interval
	0, // 1: moego.service.order.v1.OrderCompensationService.GetIDByBusinessIDAndTimeRange:input_type -> moego.service.order.v1.GetIDByBusinessIDAndTimeRangeRequest
	1, // 2: moego.service.order.v1.OrderCompensationService.GetIDByBusinessIDAndTimeRange:output_type -> moego.service.order.v1.GetIDByBusinessIDAndTimeRangeResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_order_v1_order_compensation_service_proto_init() }
func file_moego_service_order_v1_order_compensation_service_proto_init() {
	if File_moego_service_order_v1_order_compensation_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v1_order_compensation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIDByBusinessIDAndTimeRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_compensation_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIDByBusinessIDAndTimeRangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v1_order_compensation_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v1_order_compensation_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v1_order_compensation_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v1_order_compensation_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v1_order_compensation_service_proto = out.File
	file_moego_service_order_v1_order_compensation_service_proto_rawDesc = nil
	file_moego_service_order_v1_order_compensation_service_proto_goTypes = nil
	file_moego_service_order_v1_order_compensation_service_proto_depIdxs = nil
}
