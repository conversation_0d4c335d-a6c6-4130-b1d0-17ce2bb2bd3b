// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v1/order_merge_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderMergeServiceClient is the client API for OrderMergeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderMergeServiceClient interface {
	// Order merge customer data include : order
	MergeCustomerOrderData(ctx context.Context, in *MergeCustomerOrderDataRequest, opts ...grpc.CallOption) (*MergeCustomerOrderDataResponse, error)
}

type orderMergeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderMergeServiceClient(cc grpc.ClientConnInterface) OrderMergeServiceClient {
	return &orderMergeServiceClient{cc}
}

func (c *orderMergeServiceClient) MergeCustomerOrderData(ctx context.Context, in *MergeCustomerOrderDataRequest, opts ...grpc.CallOption) (*MergeCustomerOrderDataResponse, error) {
	out := new(MergeCustomerOrderDataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.OrderMergeService/MergeCustomerOrderData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderMergeServiceServer is the server API for OrderMergeService service.
// All implementations must embed UnimplementedOrderMergeServiceServer
// for forward compatibility
type OrderMergeServiceServer interface {
	// Order merge customer data include : order
	MergeCustomerOrderData(context.Context, *MergeCustomerOrderDataRequest) (*MergeCustomerOrderDataResponse, error)
	mustEmbedUnimplementedOrderMergeServiceServer()
}

// UnimplementedOrderMergeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderMergeServiceServer struct {
}

func (UnimplementedOrderMergeServiceServer) MergeCustomerOrderData(context.Context, *MergeCustomerOrderDataRequest) (*MergeCustomerOrderDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeCustomerOrderData not implemented")
}
func (UnimplementedOrderMergeServiceServer) mustEmbedUnimplementedOrderMergeServiceServer() {}

// UnsafeOrderMergeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderMergeServiceServer will
// result in compilation errors.
type UnsafeOrderMergeServiceServer interface {
	mustEmbedUnimplementedOrderMergeServiceServer()
}

func RegisterOrderMergeServiceServer(s grpc.ServiceRegistrar, srv OrderMergeServiceServer) {
	s.RegisterService(&OrderMergeService_ServiceDesc, srv)
}

func _OrderMergeService_MergeCustomerOrderData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeCustomerOrderDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderMergeServiceServer).MergeCustomerOrderData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.OrderMergeService/MergeCustomerOrderData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderMergeServiceServer).MergeCustomerOrderData(ctx, req.(*MergeCustomerOrderDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderMergeService_ServiceDesc is the grpc.ServiceDesc for OrderMergeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderMergeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v1.OrderMergeService",
	HandlerType: (*OrderMergeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MergeCustomerOrderData",
			Handler:    _OrderMergeService_MergeCustomerOrderData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v1/order_merge_service.proto",
}
