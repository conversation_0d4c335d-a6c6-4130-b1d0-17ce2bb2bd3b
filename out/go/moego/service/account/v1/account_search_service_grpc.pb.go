// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/account/v1/account_search_service.proto

package accountsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountSearchServiceClient is the client API for AccountSearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountSearchServiceClient interface {
	// Deprecated: Do not use.
	// search accounts for admin
	SearchAccountForAdmin(ctx context.Context, in *SearchAccountForAdminRequest, opts ...grpc.CallOption) (*SearchAccountForAdminResponse, error)
	// search accounts
	SearchAccount(ctx context.Context, in *SearchAccountRequest, opts ...grpc.CallOption) (*SearchAccountResponse, error)
}

type accountSearchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountSearchServiceClient(cc grpc.ClientConnInterface) AccountSearchServiceClient {
	return &accountSearchServiceClient{cc}
}

// Deprecated: Do not use.
func (c *accountSearchServiceClient) SearchAccountForAdmin(ctx context.Context, in *SearchAccountForAdminRequest, opts ...grpc.CallOption) (*SearchAccountForAdminResponse, error) {
	out := new(SearchAccountForAdminResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountSearchService/SearchAccountForAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountSearchServiceClient) SearchAccount(ctx context.Context, in *SearchAccountRequest, opts ...grpc.CallOption) (*SearchAccountResponse, error) {
	out := new(SearchAccountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountSearchService/SearchAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountSearchServiceServer is the server API for AccountSearchService service.
// All implementations must embed UnimplementedAccountSearchServiceServer
// for forward compatibility
type AccountSearchServiceServer interface {
	// Deprecated: Do not use.
	// search accounts for admin
	SearchAccountForAdmin(context.Context, *SearchAccountForAdminRequest) (*SearchAccountForAdminResponse, error)
	// search accounts
	SearchAccount(context.Context, *SearchAccountRequest) (*SearchAccountResponse, error)
	mustEmbedUnimplementedAccountSearchServiceServer()
}

// UnimplementedAccountSearchServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountSearchServiceServer struct {
}

func (UnimplementedAccountSearchServiceServer) SearchAccountForAdmin(context.Context, *SearchAccountForAdminRequest) (*SearchAccountForAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAccountForAdmin not implemented")
}
func (UnimplementedAccountSearchServiceServer) SearchAccount(context.Context, *SearchAccountRequest) (*SearchAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAccount not implemented")
}
func (UnimplementedAccountSearchServiceServer) mustEmbedUnimplementedAccountSearchServiceServer() {}

// UnsafeAccountSearchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountSearchServiceServer will
// result in compilation errors.
type UnsafeAccountSearchServiceServer interface {
	mustEmbedUnimplementedAccountSearchServiceServer()
}

func RegisterAccountSearchServiceServer(s grpc.ServiceRegistrar, srv AccountSearchServiceServer) {
	s.RegisterService(&AccountSearchService_ServiceDesc, srv)
}

func _AccountSearchService_SearchAccountForAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAccountForAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountSearchServiceServer).SearchAccountForAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountSearchService/SearchAccountForAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountSearchServiceServer).SearchAccountForAdmin(ctx, req.(*SearchAccountForAdminRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountSearchService_SearchAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountSearchServiceServer).SearchAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountSearchService/SearchAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountSearchServiceServer).SearchAccount(ctx, req.(*SearchAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountSearchService_ServiceDesc is the grpc.ServiceDesc for AccountSearchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountSearchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.account.v1.AccountSearchService",
	HandlerType: (*AccountSearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchAccountForAdmin",
			Handler:    _AccountSearchService_SearchAccountForAdmin_Handler,
		},
		{
			MethodName: "SearchAccount",
			Handler:    _AccountSearchService_SearchAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/account/v1/account_search_service.proto",
}
