// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/account/v1/session_service.proto

package accountsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get session token request
//
// Deprecated: Do not use.
type GetSessionTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session token
	SessionToken string `protobuf:"bytes,1,opt,name=session_token,json=sessionToken,proto3" json:"session_token,omitempty"`
}

func (x *GetSessionTokenRequest) Reset() {
	*x = GetSessionTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionTokenRequest) ProtoMessage() {}

func (x *GetSessionTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionTokenRequest.ProtoReflect.Descriptor instead.
func (*GetSessionTokenRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSessionTokenRequest) GetSessionToken() string {
	if x != nil {
		return x.SessionToken
	}
	return ""
}

// get session request
type GetSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetSessionRequest_Id
	//	*GetSessionRequest_SessionToken
	Identifier isGetSessionRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetSessionRequest) Reset() {
	*x = GetSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionRequest) ProtoMessage() {}

func (x *GetSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionRequest.ProtoReflect.Descriptor instead.
func (*GetSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{1}
}

func (m *GetSessionRequest) GetIdentifier() isGetSessionRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetSessionRequest) GetId() int64 {
	if x, ok := x.GetIdentifier().(*GetSessionRequest_Id); ok {
		return x.Id
	}
	return 0
}

func (x *GetSessionRequest) GetSessionToken() string {
	if x, ok := x.GetIdentifier().(*GetSessionRequest_SessionToken); ok {
		return x.SessionToken
	}
	return ""
}

type isGetSessionRequest_Identifier interface {
	isGetSessionRequest_Identifier()
}

type GetSessionRequest_Id struct {
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3,oneof"`
}

type GetSessionRequest_SessionToken struct {
	// session token
	SessionToken string `protobuf:"bytes,2,opt,name=session_token,json=sessionToken,proto3,oneof"`
}

func (*GetSessionRequest_Id) isGetSessionRequest_Identifier() {}

func (*GetSessionRequest_SessionToken) isGetSessionRequest_Identifier() {}

// create session request
type CreateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// ip
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// user agent
	UserAgent string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// referer link
	RefererLink string `protobuf:"bytes,4,opt,name=referer_link,json=refererLink,proto3" json:"referer_link,omitempty"`
	// referer session id
	RefererSessionId int64 `protobuf:"varint,5,opt,name=referer_session_id,json=refererSessionId,proto3" json:"referer_session_id,omitempty"`
	// device id
	DeviceId string `protobuf:"bytes,13,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// impersonator
	Impersonator string `protobuf:"bytes,6,opt,name=impersonator,proto3" json:"impersonator,omitempty"`
	// max age, second
	MaxAge *durationpb.Duration `protobuf:"bytes,7,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	// source
	Source string `protobuf:"bytes,8,opt,name=source,proto3" json:"source,omitempty"`
	// renewable, if not set, default is true
	Renewable *bool `protobuf:"varint,9,opt,name=renewable,proto3,oneof" json:"renewable,omitempty"`
	// allow frozen account to create session, if not set, default is false (not allow)
	AllowFrozenAccount *bool `protobuf:"varint,11,opt,name=allow_frozen_account,json=allowFrozenAccount,proto3,oneof" json:"allow_frozen_account,omitempty"`
	// allow deleted account to create session, if not set, default is false (not allow)
	AllowDeletedAccount *bool `protobuf:"varint,12,opt,name=allow_deleted_account,json=allowDeletedAccount,proto3,oneof" json:"allow_deleted_account,omitempty"`
	// session data
	SessionData *structpb.Struct `protobuf:"bytes,10,opt,name=session_data,json=sessionData,proto3" json:"session_data,omitempty"`
}

func (x *CreateSessionRequest) Reset() {
	*x = CreateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionRequest) ProtoMessage() {}

func (x *CreateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSessionRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateSessionRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateSessionRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *CreateSessionRequest) GetRefererLink() string {
	if x != nil {
		return x.RefererLink
	}
	return ""
}

func (x *CreateSessionRequest) GetRefererSessionId() int64 {
	if x != nil {
		return x.RefererSessionId
	}
	return 0
}

func (x *CreateSessionRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CreateSessionRequest) GetImpersonator() string {
	if x != nil {
		return x.Impersonator
	}
	return ""
}

func (x *CreateSessionRequest) GetMaxAge() *durationpb.Duration {
	if x != nil {
		return x.MaxAge
	}
	return nil
}

func (x *CreateSessionRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CreateSessionRequest) GetRenewable() bool {
	if x != nil && x.Renewable != nil {
		return *x.Renewable
	}
	return false
}

func (x *CreateSessionRequest) GetAllowFrozenAccount() bool {
	if x != nil && x.AllowFrozenAccount != nil {
		return *x.AllowFrozenAccount
	}
	return false
}

func (x *CreateSessionRequest) GetAllowDeletedAccount() bool {
	if x != nil && x.AllowDeletedAccount != nil {
		return *x.AllowDeletedAccount
	}
	return false
}

func (x *CreateSessionRequest) GetSessionData() *structpb.Struct {
	if x != nil {
		return x.SessionData
	}
	return nil
}

// update session request
type UpdateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ip
	Ip *string `protobuf:"bytes,2,opt,name=ip,proto3,oneof" json:"ip,omitempty"`
	// user agent
	UserAgent *string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3,oneof" json:"user_agent,omitempty"`
	// account_id
	AccountId *int64 `protobuf:"varint,4,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// referer session id
	RefererSessionId *int64 `protobuf:"varint,5,opt,name=referer_session_id,json=refererSessionId,proto3,oneof" json:"referer_session_id,omitempty"`
	// max age, second
	MaxAge *durationpb.Duration `protobuf:"bytes,7,opt,name=max_age,json=maxAge,proto3,oneof" json:"max_age,omitempty"`
	// renewable
	Renewable *bool `protobuf:"varint,9,opt,name=renewable,proto3,oneof" json:"renewable,omitempty"`
	// session data, existing values will be overwritten and null values will be deleted
	SessionData *structpb.Struct `protobuf:"bytes,10,opt,name=session_data,json=sessionData,proto3,oneof" json:"session_data,omitempty"`
}

func (x *UpdateSessionRequest) Reset() {
	*x = UpdateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSessionRequest) ProtoMessage() {}

func (x *UpdateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSessionRequest.ProtoReflect.Descriptor instead.
func (*UpdateSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSessionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSessionRequest) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *UpdateSessionRequest) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *UpdateSessionRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *UpdateSessionRequest) GetRefererSessionId() int64 {
	if x != nil && x.RefererSessionId != nil {
		return *x.RefererSessionId
	}
	return 0
}

func (x *UpdateSessionRequest) GetMaxAge() *durationpb.Duration {
	if x != nil {
		return x.MaxAge
	}
	return nil
}

func (x *UpdateSessionRequest) GetRenewable() bool {
	if x != nil && x.Renewable != nil {
		return *x.Renewable
	}
	return false
}

func (x *UpdateSessionRequest) GetSessionData() *structpb.Struct {
	if x != nil {
		return x.SessionData
	}
	return nil
}

// batch update session request
type BatchUpdateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session id list. No longer used. Use ByIds instead.
	//
	// Deprecated: Do not use.
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// account_id. No longer used. Use ByAccountAndSource instead.
	//
	// Deprecated: Do not use.
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// referer session id
	RefererSessionId *int64 `protobuf:"varint,3,opt,name=referer_session_id,json=refererSessionId,proto3,oneof" json:"referer_session_id,omitempty"`
	// max age, second
	MaxAge *durationpb.Duration `protobuf:"bytes,4,opt,name=max_age,json=maxAge,proto3,oneof" json:"max_age,omitempty"`
	// condition, at least one condition should be set
	//
	// Types that are assignable to Condition:
	//
	//	*BatchUpdateSessionRequest_ByIds_
	//	*BatchUpdateSessionRequest_ByAccountAndSource_
	Condition isBatchUpdateSessionRequest_Condition `protobuf_oneof:"condition"`
}

func (x *BatchUpdateSessionRequest) Reset() {
	*x = BatchUpdateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionRequest) ProtoMessage() {}

func (x *BatchUpdateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{4}
}

// Deprecated: Do not use.
func (x *BatchUpdateSessionRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// Deprecated: Do not use.
func (x *BatchUpdateSessionRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *BatchUpdateSessionRequest) GetRefererSessionId() int64 {
	if x != nil && x.RefererSessionId != nil {
		return *x.RefererSessionId
	}
	return 0
}

func (x *BatchUpdateSessionRequest) GetMaxAge() *durationpb.Duration {
	if x != nil {
		return x.MaxAge
	}
	return nil
}

func (m *BatchUpdateSessionRequest) GetCondition() isBatchUpdateSessionRequest_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (x *BatchUpdateSessionRequest) GetByIds() *BatchUpdateSessionRequest_ByIds {
	if x, ok := x.GetCondition().(*BatchUpdateSessionRequest_ByIds_); ok {
		return x.ByIds
	}
	return nil
}

func (x *BatchUpdateSessionRequest) GetByAccountAndSource() *BatchUpdateSessionRequest_ByAccountAndSource {
	if x, ok := x.GetCondition().(*BatchUpdateSessionRequest_ByAccountAndSource_); ok {
		return x.ByAccountAndSource
	}
	return nil
}

type isBatchUpdateSessionRequest_Condition interface {
	isBatchUpdateSessionRequest_Condition()
}

type BatchUpdateSessionRequest_ByIds_ struct {
	// update by session id list
	ByIds *BatchUpdateSessionRequest_ByIds `protobuf:"bytes,10,opt,name=by_ids,json=byIds,proto3,oneof"`
}

type BatchUpdateSessionRequest_ByAccountAndSource_ struct {
	// update by account id
	ByAccountAndSource *BatchUpdateSessionRequest_ByAccountAndSource `protobuf:"bytes,11,opt,name=by_account_and_source,json=byAccountAndSource,proto3,oneof"`
}

func (*BatchUpdateSessionRequest_ByIds_) isBatchUpdateSessionRequest_Condition() {}

func (*BatchUpdateSessionRequest_ByAccountAndSource_) isBatchUpdateSessionRequest_Condition() {}

// batch update session response
type BatchUpdateSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session list. Temporarily, this field should not be used because no data will be returned.
	//
	// Deprecated: Do not use.
	Sessions []*v1.SessionModel `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *BatchUpdateSessionResponse) Reset() {
	*x = BatchUpdateSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionResponse) ProtoMessage() {}

func (x *BatchUpdateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Do not use.
func (x *BatchUpdateSessionResponse) GetSessions() []*v1.SessionModel {
	if x != nil {
		return x.Sessions
	}
	return nil
}

// create session response
type CreateSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session token
	SessionToken string `protobuf:"bytes,1,opt,name=session_token,json=sessionToken,proto3" json:"session_token,omitempty"`
	// session id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// if this is the first session from given source
	FirstSession bool `protobuf:"varint,3,opt,name=first_session,json=firstSession,proto3" json:"first_session,omitempty"`
}

func (x *CreateSessionResponse) Reset() {
	*x = CreateSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionResponse) ProtoMessage() {}

func (x *CreateSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateSessionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateSessionResponse) GetSessionToken() string {
	if x != nil {
		return x.SessionToken
	}
	return ""
}

func (x *CreateSessionResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateSessionResponse) GetFirstSession() bool {
	if x != nil {
		return x.FirstSession
	}
	return false
}

// validate session request
type ValidateSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session token
	SessionToken string `protobuf:"bytes,1,opt,name=session_token,json=sessionToken,proto3" json:"session_token,omitempty"`
	// ip
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// user agent
	UserAgent string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *ValidateSessionRequest) Reset() {
	*x = ValidateSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionRequest) ProtoMessage() {}

func (x *ValidateSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionRequest.ProtoReflect.Descriptor instead.
func (*ValidateSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{7}
}

func (x *ValidateSessionRequest) GetSessionToken() string {
	if x != nil {
		return x.SessionToken
	}
	return ""
}

func (x *ValidateSessionRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ValidateSessionRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// get session list by account id request
type GetSessionListByAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// include impersonator
	IncludeImpersonator bool `protobuf:"varint,2,opt,name=include_impersonator,json=includeImpersonator,proto3" json:"include_impersonator,omitempty"`
}

func (x *GetSessionListByAccountIdRequest) Reset() {
	*x = GetSessionListByAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionListByAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionListByAccountIdRequest) ProtoMessage() {}

func (x *GetSessionListByAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionListByAccountIdRequest.ProtoReflect.Descriptor instead.
func (*GetSessionListByAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetSessionListByAccountIdRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *GetSessionListByAccountIdRequest) GetIncludeImpersonator() bool {
	if x != nil {
		return x.IncludeImpersonator
	}
	return false
}

// get session list by account id response
type GetSessionListByAccountIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session list
	Sessions []*v1.SessionModel `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *GetSessionListByAccountIdResponse) Reset() {
	*x = GetSessionListByAccountIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionListByAccountIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionListByAccountIdResponse) ProtoMessage() {}

func (x *GetSessionListByAccountIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionListByAccountIdResponse.ProtoReflect.Descriptor instead.
func (*GetSessionListByAccountIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetSessionListByAccountIdResponse) GetSessions() []*v1.SessionModel {
	if x != nil {
		return x.Sessions
	}
	return nil
}

// delete session by id request
type DeleteSessionByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteSessionByIdRequest) Reset() {
	*x = DeleteSessionByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSessionByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSessionByIdRequest) ProtoMessage() {}

func (x *DeleteSessionByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSessionByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteSessionByIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteSessionByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete all sessions by account id request
type DeleteAllSessionsByAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// ignored session ids
	IgnoredSessionIds []int64 `protobuf:"varint,2,rep,packed,name=ignored_session_ids,json=ignoredSessionIds,proto3" json:"ignored_session_ids,omitempty"`
}

func (x *DeleteAllSessionsByAccountIdRequest) Reset() {
	*x = DeleteAllSessionsByAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAllSessionsByAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAllSessionsByAccountIdRequest) ProtoMessage() {}

func (x *DeleteAllSessionsByAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAllSessionsByAccountIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteAllSessionsByAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteAllSessionsByAccountIdRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *DeleteAllSessionsByAccountIdRequest) GetIgnoredSessionIds() []int64 {
	if x != nil {
		return x.IgnoredSessionIds
	}
	return nil
}

// by ids
type BatchUpdateSessionRequest_ByIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session id list
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BatchUpdateSessionRequest_ByIds) Reset() {
	*x = BatchUpdateSessionRequest_ByIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionRequest_ByIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionRequest_ByIds) ProtoMessage() {}

func (x *BatchUpdateSessionRequest_ByIds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionRequest_ByIds.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionRequest_ByIds) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BatchUpdateSessionRequest_ByIds) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// by account and source
type BatchUpdateSessionRequest_ByAccountAndSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// source. If set, only sessions of this source will be updated, else all sessions of this account will be updated.
	Source *string `protobuf:"bytes,2,opt,name=source,proto3,oneof" json:"source,omitempty"`
}

func (x *BatchUpdateSessionRequest_ByAccountAndSource) Reset() {
	*x = BatchUpdateSessionRequest_ByAccountAndSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionRequest_ByAccountAndSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionRequest_ByAccountAndSource) ProtoMessage() {}

func (x *BatchUpdateSessionRequest_ByAccountAndSource) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionRequest_ByAccountAndSource.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionRequest_ByAccountAndSource) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_service_proto_rawDescGZIP(), []int{4, 1}
}

func (x *BatchUpdateSessionRequest_ByAccountAndSource) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *BatchUpdateSessionRequest_ByAccountAndSource) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

var File_moego_service_account_v1_session_service_proto protoreflect.FileDescriptor

var file_moego_service_account_v1_session_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4d, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x6b, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x9b, 0x05, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x70, 0x01, 0x52, 0x02, 0x69, 0x70, 0x12, 0x27, 0x0a, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x18,
	0x80, 0x02, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0xaa, 0x01, 0x04, 0x08, 0x01, 0x2a, 0x00, 0x52, 0x06, 0x6d, 0x61, 0x78,
	0x41, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x09, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x6e,
	0x65, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x5f, 0x66, 0x72, 0x6f, 0x7a, 0x65, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x46, 0x72, 0x6f, 0x7a, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x37, 0x0a, 0x15, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x02, 0x52, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x0c, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61,
	0x62, 0x6c, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x66, 0x72,
	0x6f, 0x7a, 0x65, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd7, 0x03, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x70, 0x01, 0x48, 0x00, 0x52, 0x02, 0x69, 0x70, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x01, 0x52, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x02, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x31, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x10, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x41, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x2a, 0x00, 0x48, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41,
	0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x09, 0x72, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x06, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x70,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67,
	0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xfc, 0x04, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x41, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x2a, 0x00, 0x48, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x52, 0x0a, 0x06, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x79, 0x49, 0x64, 0x73, 0x48, 0x00, 0x52,
	0x05, 0x62, 0x79, 0x49, 0x64, 0x73, 0x12, 0x7b, 0x0a, 0x15, 0x62, 0x79, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52,
	0x12, 0x62, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x1a, 0x2e, 0x0a, 0x05, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01,
	0x0d, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x1a, 0x66, 0x0a, 0x12, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x41, 0x6e, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x22,
	0x63, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a,
	0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x71, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0x80, 0x02, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x70, 0x01, 0x52, 0x02, 0x69, 0x70, 0x12, 0x27, 0x0a, 0x0a, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x22, 0x74, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x49, 0x6d, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x66, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41,
	0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x2a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x81, 0x01,
	0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x13, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0xf4, 0x03, 0x28, 0x01, 0x52, 0x11,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x32, 0xaf, 0x07, 0x0a, 0x0e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x60, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x94, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x70, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x66, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x7f, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x75, 0x0a, 0x1c,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c,
	0x6c, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_account_v1_session_service_proto_rawDescOnce sync.Once
	file_moego_service_account_v1_session_service_proto_rawDescData = file_moego_service_account_v1_session_service_proto_rawDesc
)

func file_moego_service_account_v1_session_service_proto_rawDescGZIP() []byte {
	file_moego_service_account_v1_session_service_proto_rawDescOnce.Do(func() {
		file_moego_service_account_v1_session_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_account_v1_session_service_proto_rawDescData)
	})
	return file_moego_service_account_v1_session_service_proto_rawDescData
}

var file_moego_service_account_v1_session_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_service_account_v1_session_service_proto_goTypes = []interface{}{
	(*GetSessionTokenRequest)(nil),                       // 0: moego.service.account.v1.GetSessionTokenRequest
	(*GetSessionRequest)(nil),                            // 1: moego.service.account.v1.GetSessionRequest
	(*CreateSessionRequest)(nil),                         // 2: moego.service.account.v1.CreateSessionRequest
	(*UpdateSessionRequest)(nil),                         // 3: moego.service.account.v1.UpdateSessionRequest
	(*BatchUpdateSessionRequest)(nil),                    // 4: moego.service.account.v1.BatchUpdateSessionRequest
	(*BatchUpdateSessionResponse)(nil),                   // 5: moego.service.account.v1.BatchUpdateSessionResponse
	(*CreateSessionResponse)(nil),                        // 6: moego.service.account.v1.CreateSessionResponse
	(*ValidateSessionRequest)(nil),                       // 7: moego.service.account.v1.ValidateSessionRequest
	(*GetSessionListByAccountIdRequest)(nil),             // 8: moego.service.account.v1.GetSessionListByAccountIdRequest
	(*GetSessionListByAccountIdResponse)(nil),            // 9: moego.service.account.v1.GetSessionListByAccountIdResponse
	(*DeleteSessionByIdRequest)(nil),                     // 10: moego.service.account.v1.DeleteSessionByIdRequest
	(*DeleteAllSessionsByAccountIdRequest)(nil),          // 11: moego.service.account.v1.DeleteAllSessionsByAccountIdRequest
	(*BatchUpdateSessionRequest_ByIds)(nil),              // 12: moego.service.account.v1.BatchUpdateSessionRequest.ByIds
	(*BatchUpdateSessionRequest_ByAccountAndSource)(nil), // 13: moego.service.account.v1.BatchUpdateSessionRequest.ByAccountAndSource
	(*durationpb.Duration)(nil),                          // 14: google.protobuf.Duration
	(*structpb.Struct)(nil),                              // 15: google.protobuf.Struct
	(*v1.SessionModel)(nil),                              // 16: moego.models.account.v1.SessionModel
	(*emptypb.Empty)(nil),                                // 17: google.protobuf.Empty
}
var file_moego_service_account_v1_session_service_proto_depIdxs = []int32{
	14, // 0: moego.service.account.v1.CreateSessionRequest.max_age:type_name -> google.protobuf.Duration
	15, // 1: moego.service.account.v1.CreateSessionRequest.session_data:type_name -> google.protobuf.Struct
	14, // 2: moego.service.account.v1.UpdateSessionRequest.max_age:type_name -> google.protobuf.Duration
	15, // 3: moego.service.account.v1.UpdateSessionRequest.session_data:type_name -> google.protobuf.Struct
	14, // 4: moego.service.account.v1.BatchUpdateSessionRequest.max_age:type_name -> google.protobuf.Duration
	12, // 5: moego.service.account.v1.BatchUpdateSessionRequest.by_ids:type_name -> moego.service.account.v1.BatchUpdateSessionRequest.ByIds
	13, // 6: moego.service.account.v1.BatchUpdateSessionRequest.by_account_and_source:type_name -> moego.service.account.v1.BatchUpdateSessionRequest.ByAccountAndSource
	16, // 7: moego.service.account.v1.BatchUpdateSessionResponse.sessions:type_name -> moego.models.account.v1.SessionModel
	16, // 8: moego.service.account.v1.GetSessionListByAccountIdResponse.sessions:type_name -> moego.models.account.v1.SessionModel
	0,  // 9: moego.service.account.v1.SessionService.GetSessionByToken:input_type -> moego.service.account.v1.GetSessionTokenRequest
	1,  // 10: moego.service.account.v1.SessionService.GetSession:input_type -> moego.service.account.v1.GetSessionRequest
	8,  // 11: moego.service.account.v1.SessionService.GetSessionListByAccountId:input_type -> moego.service.account.v1.GetSessionListByAccountIdRequest
	2,  // 12: moego.service.account.v1.SessionService.CreateSession:input_type -> moego.service.account.v1.CreateSessionRequest
	3,  // 13: moego.service.account.v1.SessionService.UpdateSession:input_type -> moego.service.account.v1.UpdateSessionRequest
	4,  // 14: moego.service.account.v1.SessionService.BatchUpdateSession:input_type -> moego.service.account.v1.BatchUpdateSessionRequest
	10, // 15: moego.service.account.v1.SessionService.DeleteSessionById:input_type -> moego.service.account.v1.DeleteSessionByIdRequest
	11, // 16: moego.service.account.v1.SessionService.DeleteAllSessionsByAccountId:input_type -> moego.service.account.v1.DeleteAllSessionsByAccountIdRequest
	16, // 17: moego.service.account.v1.SessionService.GetSessionByToken:output_type -> moego.models.account.v1.SessionModel
	16, // 18: moego.service.account.v1.SessionService.GetSession:output_type -> moego.models.account.v1.SessionModel
	9,  // 19: moego.service.account.v1.SessionService.GetSessionListByAccountId:output_type -> moego.service.account.v1.GetSessionListByAccountIdResponse
	6,  // 20: moego.service.account.v1.SessionService.CreateSession:output_type -> moego.service.account.v1.CreateSessionResponse
	16, // 21: moego.service.account.v1.SessionService.UpdateSession:output_type -> moego.models.account.v1.SessionModel
	5,  // 22: moego.service.account.v1.SessionService.BatchUpdateSession:output_type -> moego.service.account.v1.BatchUpdateSessionResponse
	17, // 23: moego.service.account.v1.SessionService.DeleteSessionById:output_type -> google.protobuf.Empty
	17, // 24: moego.service.account.v1.SessionService.DeleteAllSessionsByAccountId:output_type -> google.protobuf.Empty
	17, // [17:25] is the sub-list for method output_type
	9,  // [9:17] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_service_account_v1_session_service_proto_init() }
func file_moego_service_account_v1_session_service_proto_init() {
	if File_moego_service_account_v1_session_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_account_v1_session_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionListByAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionListByAccountIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSessionByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAllSessionsByAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionRequest_ByIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionRequest_ByAccountAndSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_account_v1_session_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*GetSessionRequest_Id)(nil),
		(*GetSessionRequest_SessionToken)(nil),
	}
	file_moego_service_account_v1_session_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_account_v1_session_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_account_v1_session_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*BatchUpdateSessionRequest_ByIds_)(nil),
		(*BatchUpdateSessionRequest_ByAccountAndSource_)(nil),
	}
	file_moego_service_account_v1_session_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_account_v1_session_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_account_v1_session_service_proto_goTypes,
		DependencyIndexes: file_moego_service_account_v1_session_service_proto_depIdxs,
		MessageInfos:      file_moego_service_account_v1_session_service_proto_msgTypes,
	}.Build()
	File_moego_service_account_v1_session_service_proto = out.File
	file_moego_service_account_v1_session_service_proto_rawDesc = nil
	file_moego_service_account_v1_session_service_proto_goTypes = nil
	file_moego_service_account_v1_session_service_proto_depIdxs = nil
}
