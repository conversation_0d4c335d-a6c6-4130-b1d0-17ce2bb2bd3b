// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/account/v1/session_service.proto

package accountsvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SessionServiceClient is the client API for SessionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SessionServiceClient interface {
	// Deprecated: Do not use.
	// Get session by session token.
	// Deprecated: use GetSession instead.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist or has been deleted.
	// - CODE_SESSION_EXPIRED: The session has expired.
	// - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
	// - CODE_PARAMS_ERROR: The format of session token is incorrect.
	GetSessionByToken(ctx context.Context, in *GetSessionTokenRequest, opts ...grpc.CallOption) (*v1.SessionModel, error)
	// Get session by id or session token.
	// If get by session token, only legal and not deleted and not expired session can be fetched.
	// If get by id, the session can be fetched without checking status, expiration time or token.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist, or is deleted but gotten by session token.
	// - CODE_SESSION_EXPIRED: The session has expired.
	// - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
	// - CODE_PARAMS_ERROR: The format of session token is incorrect.
	GetSession(ctx context.Context, in *GetSessionRequest, opts ...grpc.CallOption) (*v1.SessionModel, error)
	// Get session list by account id.
	// Expired sessions can be found while deleted sessions can not.
	// If impersonator's sessions are needed, set `include_impersonator` = true in the request.
	GetSessionListByAccountId(ctx context.Context, in *GetSessionListByAccountIdRequest, opts ...grpc.CallOption) (*GetSessionListByAccountIdResponse, error)
	// Create a new session.
	CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error)
	// Update an existing session, even if it has expired or been deleted.
	// If the inputted session data is empty, the session data in the database will not be updated or deleted.
	// If the values are NULL for certain keys, these keys will be deleted from database.
	// If the values are not NULL for certain keys, these keys will be created or updated into database.
	// If certain keys show in database but not show in the inputted session data, these key will remain in database.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist.
	UpdateSession(ctx context.Context, in *UpdateSessionRequest, opts ...grpc.CallOption) (*v1.SessionModel, error)
	// Batch update sessions. Two update conditions are supported.
	//   - By ids:
	//     Update sessions by given ids. Expired sessions and deleted sessions can be updated.
	//   - By account and source:
	//     Update sessions by given account id and source. Expired sessions can be updated, while deleted sessions cannot be updated.
	//     If source is set, only sessions of this source will be updated, else all sessions of this account will be updated.
	BatchUpdateSession(ctx context.Context, in *BatchUpdateSessionRequest, opts ...grpc.CallOption) (*BatchUpdateSessionResponse, error)
	// Delete a session.
	// Request is idempotent if the session has been deleted.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist.
	DeleteSessionById(ctx context.Context, in *DeleteSessionByIdRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete all sessions by account id.
	// This method won't throw an exception if no sessions need to be deleted.
	DeleteAllSessionsByAccountId(ctx context.Context, in *DeleteAllSessionsByAccountIdRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sessionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSessionServiceClient(cc grpc.ClientConnInterface) SessionServiceClient {
	return &sessionServiceClient{cc}
}

// Deprecated: Do not use.
func (c *sessionServiceClient) GetSessionByToken(ctx context.Context, in *GetSessionTokenRequest, opts ...grpc.CallOption) (*v1.SessionModel, error) {
	out := new(v1.SessionModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/GetSessionByToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) GetSession(ctx context.Context, in *GetSessionRequest, opts ...grpc.CallOption) (*v1.SessionModel, error) {
	out := new(v1.SessionModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/GetSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) GetSessionListByAccountId(ctx context.Context, in *GetSessionListByAccountIdRequest, opts ...grpc.CallOption) (*GetSessionListByAccountIdResponse, error) {
	out := new(GetSessionListByAccountIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/GetSessionListByAccountId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) CreateSession(ctx context.Context, in *CreateSessionRequest, opts ...grpc.CallOption) (*CreateSessionResponse, error) {
	out := new(CreateSessionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/CreateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) UpdateSession(ctx context.Context, in *UpdateSessionRequest, opts ...grpc.CallOption) (*v1.SessionModel, error) {
	out := new(v1.SessionModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/UpdateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) BatchUpdateSession(ctx context.Context, in *BatchUpdateSessionRequest, opts ...grpc.CallOption) (*BatchUpdateSessionResponse, error) {
	out := new(BatchUpdateSessionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/BatchUpdateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) DeleteSessionById(ctx context.Context, in *DeleteSessionByIdRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/DeleteSessionById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) DeleteAllSessionsByAccountId(ctx context.Context, in *DeleteAllSessionsByAccountIdRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.SessionService/DeleteAllSessionsByAccountId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SessionServiceServer is the server API for SessionService service.
// All implementations must embed UnimplementedSessionServiceServer
// for forward compatibility
type SessionServiceServer interface {
	// Deprecated: Do not use.
	// Get session by session token.
	// Deprecated: use GetSession instead.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist or has been deleted.
	// - CODE_SESSION_EXPIRED: The session has expired.
	// - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
	// - CODE_PARAMS_ERROR: The format of session token is incorrect.
	GetSessionByToken(context.Context, *GetSessionTokenRequest) (*v1.SessionModel, error)
	// Get session by id or session token.
	// If get by session token, only legal and not deleted and not expired session can be fetched.
	// If get by id, the session can be fetched without checking status, expiration time or token.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist, or is deleted but gotten by session token.
	// - CODE_SESSION_EXPIRED: The session has expired.
	// - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
	// - CODE_PARAMS_ERROR: The format of session token is incorrect.
	GetSession(context.Context, *GetSessionRequest) (*v1.SessionModel, error)
	// Get session list by account id.
	// Expired sessions can be found while deleted sessions can not.
	// If impersonator's sessions are needed, set `include_impersonator` = true in the request.
	GetSessionListByAccountId(context.Context, *GetSessionListByAccountIdRequest) (*GetSessionListByAccountIdResponse, error)
	// Create a new session.
	CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error)
	// Update an existing session, even if it has expired or been deleted.
	// If the inputted session data is empty, the session data in the database will not be updated or deleted.
	// If the values are NULL for certain keys, these keys will be deleted from database.
	// If the values are not NULL for certain keys, these keys will be created or updated into database.
	// If certain keys show in database but not show in the inputted session data, these key will remain in database.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist.
	UpdateSession(context.Context, *UpdateSessionRequest) (*v1.SessionModel, error)
	// Batch update sessions. Two update conditions are supported.
	//   - By ids:
	//     Update sessions by given ids. Expired sessions and deleted sessions can be updated.
	//   - By account and source:
	//     Update sessions by given account id and source. Expired sessions can be updated, while deleted sessions cannot be updated.
	//     If source is set, only sessions of this source will be updated, else all sessions of this account will be updated.
	BatchUpdateSession(context.Context, *BatchUpdateSessionRequest) (*BatchUpdateSessionResponse, error)
	// Delete a session.
	// Request is idempotent if the session has been deleted.
	//
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: The session does not exist.
	DeleteSessionById(context.Context, *DeleteSessionByIdRequest) (*emptypb.Empty, error)
	// Delete all sessions by account id.
	// This method won't throw an exception if no sessions need to be deleted.
	DeleteAllSessionsByAccountId(context.Context, *DeleteAllSessionsByAccountIdRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSessionServiceServer()
}

// UnimplementedSessionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSessionServiceServer struct {
}

func (UnimplementedSessionServiceServer) GetSessionByToken(context.Context, *GetSessionTokenRequest) (*v1.SessionModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessionByToken not implemented")
}
func (UnimplementedSessionServiceServer) GetSession(context.Context, *GetSessionRequest) (*v1.SessionModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSession not implemented")
}
func (UnimplementedSessionServiceServer) GetSessionListByAccountId(context.Context, *GetSessionListByAccountIdRequest) (*GetSessionListByAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSessionListByAccountId not implemented")
}
func (UnimplementedSessionServiceServer) CreateSession(context.Context, *CreateSessionRequest) (*CreateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSession not implemented")
}
func (UnimplementedSessionServiceServer) UpdateSession(context.Context, *UpdateSessionRequest) (*v1.SessionModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSession not implemented")
}
func (UnimplementedSessionServiceServer) BatchUpdateSession(context.Context, *BatchUpdateSessionRequest) (*BatchUpdateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateSession not implemented")
}
func (UnimplementedSessionServiceServer) DeleteSessionById(context.Context, *DeleteSessionByIdRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSessionById not implemented")
}
func (UnimplementedSessionServiceServer) DeleteAllSessionsByAccountId(context.Context, *DeleteAllSessionsByAccountIdRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAllSessionsByAccountId not implemented")
}
func (UnimplementedSessionServiceServer) mustEmbedUnimplementedSessionServiceServer() {}

// UnsafeSessionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SessionServiceServer will
// result in compilation errors.
type UnsafeSessionServiceServer interface {
	mustEmbedUnimplementedSessionServiceServer()
}

func RegisterSessionServiceServer(s grpc.ServiceRegistrar, srv SessionServiceServer) {
	s.RegisterService(&SessionService_ServiceDesc, srv)
}

func _SessionService_GetSessionByToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).GetSessionByToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/GetSessionByToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).GetSessionByToken(ctx, req.(*GetSessionTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_GetSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).GetSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/GetSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).GetSession(ctx, req.(*GetSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_GetSessionListByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSessionListByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).GetSessionListByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/GetSessionListByAccountId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).GetSessionListByAccountId(ctx, req.(*GetSessionListByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_CreateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).CreateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/CreateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).CreateSession(ctx, req.(*CreateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_UpdateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).UpdateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/UpdateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).UpdateSession(ctx, req.(*UpdateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_BatchUpdateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).BatchUpdateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/BatchUpdateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).BatchUpdateSession(ctx, req.(*BatchUpdateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_DeleteSessionById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSessionByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).DeleteSessionById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/DeleteSessionById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).DeleteSessionById(ctx, req.(*DeleteSessionByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_DeleteAllSessionsByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAllSessionsByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).DeleteAllSessionsByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.SessionService/DeleteAllSessionsByAccountId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).DeleteAllSessionsByAccountId(ctx, req.(*DeleteAllSessionsByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SessionService_ServiceDesc is the grpc.ServiceDesc for SessionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SessionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.account.v1.SessionService",
	HandlerType: (*SessionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSessionByToken",
			Handler:    _SessionService_GetSessionByToken_Handler,
		},
		{
			MethodName: "GetSession",
			Handler:    _SessionService_GetSession_Handler,
		},
		{
			MethodName: "GetSessionListByAccountId",
			Handler:    _SessionService_GetSessionListByAccountId_Handler,
		},
		{
			MethodName: "CreateSession",
			Handler:    _SessionService_CreateSession_Handler,
		},
		{
			MethodName: "UpdateSession",
			Handler:    _SessionService_UpdateSession_Handler,
		},
		{
			MethodName: "BatchUpdateSession",
			Handler:    _SessionService_BatchUpdateSession_Handler,
		},
		{
			MethodName: "DeleteSessionById",
			Handler:    _SessionService_DeleteSessionById_Handler,
		},
		{
			MethodName: "DeleteAllSessionsByAccountId",
			Handler:    _SessionService_DeleteAllSessionsByAccountId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/account/v1/session_service.proto",
}
