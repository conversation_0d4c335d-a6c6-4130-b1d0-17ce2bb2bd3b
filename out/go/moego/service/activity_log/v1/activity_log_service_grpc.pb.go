// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/activity_log/v1/activity_log_service.proto

package activitylogsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ActivityLogServiceClient is the client API for ActivityLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ActivityLogServiceClient interface {
	// Create an Activity Log
	CreateActivityLog(ctx context.Context, in *CreateActivityLogRequest, opts ...grpc.CallOption) (*CreateActivityLogResponse, error)
	// Search activity logs with pagination
	SearchActivityLogPage(ctx context.Context, in *SearchActivityLogPageInput, opts ...grpc.CallOption) (*SearchActivityLogPageOutput, error)
	// Get activity log details
	GetActivityLogDetails(ctx context.Context, in *GetActivityLogDetailsInput, opts ...grpc.CallOption) (*GetActivityLogDetailsOutput, error)
	// List activity logs details, not include affected activity logs
	ListActivityLogDetails(ctx context.Context, in *ListActivityLogDetailsRequest, opts ...grpc.CallOption) (*ListActivityLogDetailsResponse, error)
	// Search operators with pagination
	SearchOperatorPage(ctx context.Context, in *SearchOperatorPageInput, opts ...grpc.CallOption) (*SearchOperatorPageOutput, error)
	// Search resource types with pagination
	SearchResourceTypePage(ctx context.Context, in *SearchResourceTypePageInput, opts ...grpc.CallOption) (*SearchResourceTypePageOutput, error)
	// Search actions with pagination
	SearchActionPage(ctx context.Context, in *SearchActionPageInput, opts ...grpc.CallOption) (*SearchActionPageOutput, error)
	// Search owners with pagination
	SearchOwnerPage(ctx context.Context, in *SearchOwnerPageInput, opts ...grpc.CallOption) (*SearchOwnerPageOutput, error)
}

type activityLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewActivityLogServiceClient(cc grpc.ClientConnInterface) ActivityLogServiceClient {
	return &activityLogServiceClient{cc}
}

func (c *activityLogServiceClient) CreateActivityLog(ctx context.Context, in *CreateActivityLogRequest, opts ...grpc.CallOption) (*CreateActivityLogResponse, error) {
	out := new(CreateActivityLogResponse)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/CreateActivityLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) SearchActivityLogPage(ctx context.Context, in *SearchActivityLogPageInput, opts ...grpc.CallOption) (*SearchActivityLogPageOutput, error) {
	out := new(SearchActivityLogPageOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/SearchActivityLogPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) GetActivityLogDetails(ctx context.Context, in *GetActivityLogDetailsInput, opts ...grpc.CallOption) (*GetActivityLogDetailsOutput, error) {
	out := new(GetActivityLogDetailsOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/GetActivityLogDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) ListActivityLogDetails(ctx context.Context, in *ListActivityLogDetailsRequest, opts ...grpc.CallOption) (*ListActivityLogDetailsResponse, error) {
	out := new(ListActivityLogDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/ListActivityLogDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) SearchOperatorPage(ctx context.Context, in *SearchOperatorPageInput, opts ...grpc.CallOption) (*SearchOperatorPageOutput, error) {
	out := new(SearchOperatorPageOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/SearchOperatorPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) SearchResourceTypePage(ctx context.Context, in *SearchResourceTypePageInput, opts ...grpc.CallOption) (*SearchResourceTypePageOutput, error) {
	out := new(SearchResourceTypePageOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/SearchResourceTypePage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) SearchActionPage(ctx context.Context, in *SearchActionPageInput, opts ...grpc.CallOption) (*SearchActionPageOutput, error) {
	out := new(SearchActionPageOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/SearchActionPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityLogServiceClient) SearchOwnerPage(ctx context.Context, in *SearchOwnerPageInput, opts ...grpc.CallOption) (*SearchOwnerPageOutput, error) {
	out := new(SearchOwnerPageOutput)
	err := c.cc.Invoke(ctx, "/moego.service.activity_log.v1.ActivityLogService/SearchOwnerPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActivityLogServiceServer is the server API for ActivityLogService service.
// All implementations must embed UnimplementedActivityLogServiceServer
// for forward compatibility
type ActivityLogServiceServer interface {
	// Create an Activity Log
	CreateActivityLog(context.Context, *CreateActivityLogRequest) (*CreateActivityLogResponse, error)
	// Search activity logs with pagination
	SearchActivityLogPage(context.Context, *SearchActivityLogPageInput) (*SearchActivityLogPageOutput, error)
	// Get activity log details
	GetActivityLogDetails(context.Context, *GetActivityLogDetailsInput) (*GetActivityLogDetailsOutput, error)
	// List activity logs details, not include affected activity logs
	ListActivityLogDetails(context.Context, *ListActivityLogDetailsRequest) (*ListActivityLogDetailsResponse, error)
	// Search operators with pagination
	SearchOperatorPage(context.Context, *SearchOperatorPageInput) (*SearchOperatorPageOutput, error)
	// Search resource types with pagination
	SearchResourceTypePage(context.Context, *SearchResourceTypePageInput) (*SearchResourceTypePageOutput, error)
	// Search actions with pagination
	SearchActionPage(context.Context, *SearchActionPageInput) (*SearchActionPageOutput, error)
	// Search owners with pagination
	SearchOwnerPage(context.Context, *SearchOwnerPageInput) (*SearchOwnerPageOutput, error)
	mustEmbedUnimplementedActivityLogServiceServer()
}

// UnimplementedActivityLogServiceServer must be embedded to have forward compatible implementations.
type UnimplementedActivityLogServiceServer struct {
}

func (UnimplementedActivityLogServiceServer) CreateActivityLog(context.Context, *CreateActivityLogRequest) (*CreateActivityLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateActivityLog not implemented")
}
func (UnimplementedActivityLogServiceServer) SearchActivityLogPage(context.Context, *SearchActivityLogPageInput) (*SearchActivityLogPageOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchActivityLogPage not implemented")
}
func (UnimplementedActivityLogServiceServer) GetActivityLogDetails(context.Context, *GetActivityLogDetailsInput) (*GetActivityLogDetailsOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivityLogDetails not implemented")
}
func (UnimplementedActivityLogServiceServer) ListActivityLogDetails(context.Context, *ListActivityLogDetailsRequest) (*ListActivityLogDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListActivityLogDetails not implemented")
}
func (UnimplementedActivityLogServiceServer) SearchOperatorPage(context.Context, *SearchOperatorPageInput) (*SearchOperatorPageOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchOperatorPage not implemented")
}
func (UnimplementedActivityLogServiceServer) SearchResourceTypePage(context.Context, *SearchResourceTypePageInput) (*SearchResourceTypePageOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchResourceTypePage not implemented")
}
func (UnimplementedActivityLogServiceServer) SearchActionPage(context.Context, *SearchActionPageInput) (*SearchActionPageOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchActionPage not implemented")
}
func (UnimplementedActivityLogServiceServer) SearchOwnerPage(context.Context, *SearchOwnerPageInput) (*SearchOwnerPageOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchOwnerPage not implemented")
}
func (UnimplementedActivityLogServiceServer) mustEmbedUnimplementedActivityLogServiceServer() {}

// UnsafeActivityLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ActivityLogServiceServer will
// result in compilation errors.
type UnsafeActivityLogServiceServer interface {
	mustEmbedUnimplementedActivityLogServiceServer()
}

func RegisterActivityLogServiceServer(s grpc.ServiceRegistrar, srv ActivityLogServiceServer) {
	s.RegisterService(&ActivityLogService_ServiceDesc, srv)
}

func _ActivityLogService_CreateActivityLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateActivityLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).CreateActivityLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/CreateActivityLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).CreateActivityLog(ctx, req.(*CreateActivityLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_SearchActivityLogPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchActivityLogPageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).SearchActivityLogPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/SearchActivityLogPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).SearchActivityLogPage(ctx, req.(*SearchActivityLogPageInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_GetActivityLogDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityLogDetailsInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).GetActivityLogDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/GetActivityLogDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).GetActivityLogDetails(ctx, req.(*GetActivityLogDetailsInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_ListActivityLogDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListActivityLogDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).ListActivityLogDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/ListActivityLogDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).ListActivityLogDetails(ctx, req.(*ListActivityLogDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_SearchOperatorPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchOperatorPageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).SearchOperatorPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/SearchOperatorPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).SearchOperatorPage(ctx, req.(*SearchOperatorPageInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_SearchResourceTypePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchResourceTypePageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).SearchResourceTypePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/SearchResourceTypePage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).SearchResourceTypePage(ctx, req.(*SearchResourceTypePageInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_SearchActionPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchActionPageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).SearchActionPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/SearchActionPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).SearchActionPage(ctx, req.(*SearchActionPageInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActivityLogService_SearchOwnerPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchOwnerPageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityLogServiceServer).SearchOwnerPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.activity_log.v1.ActivityLogService/SearchOwnerPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityLogServiceServer).SearchOwnerPage(ctx, req.(*SearchOwnerPageInput))
	}
	return interceptor(ctx, in, info, handler)
}

// ActivityLogService_ServiceDesc is the grpc.ServiceDesc for ActivityLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ActivityLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.activity_log.v1.ActivityLogService",
	HandlerType: (*ActivityLogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateActivityLog",
			Handler:    _ActivityLogService_CreateActivityLog_Handler,
		},
		{
			MethodName: "SearchActivityLogPage",
			Handler:    _ActivityLogService_SearchActivityLogPage_Handler,
		},
		{
			MethodName: "GetActivityLogDetails",
			Handler:    _ActivityLogService_GetActivityLogDetails_Handler,
		},
		{
			MethodName: "ListActivityLogDetails",
			Handler:    _ActivityLogService_ListActivityLogDetails_Handler,
		},
		{
			MethodName: "SearchOperatorPage",
			Handler:    _ActivityLogService_SearchOperatorPage_Handler,
		},
		{
			MethodName: "SearchResourceTypePage",
			Handler:    _ActivityLogService_SearchResourceTypePage_Handler,
		},
		{
			MethodName: "SearchActionPage",
			Handler:    _ActivityLogService_SearchActionPage_Handler,
		},
		{
			MethodName: "SearchOwnerPage",
			Handler:    _ActivityLogService_SearchOwnerPage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/activity_log/v1/activity_log_service.proto",
}
