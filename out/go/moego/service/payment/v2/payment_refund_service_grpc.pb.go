// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/payment/v2/payment_refund_service.proto

package paymentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RefundServiceClient is the client API for RefundService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RefundServiceClient interface {
	// Refund 退款
	RefundPayment(ctx context.Context, in *RefundPaymentRequest, opts ...grpc.CallOption) (*RefundPaymentResponse, error)
	// GetRefund 获取退款单据
	GetRefund(ctx context.Context, in *GetRefundRequest, opts ...grpc.CallOption) (*GetRefundResponse, error)
	// ListRefund 获取退款列表
	ListRefunds(ctx context.Context, in *ListRefundsRequest, opts ...grpc.CallOption) (*ListRefundsResponse, error)
}

type refundServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRefundServiceClient(cc grpc.ClientConnInterface) RefundServiceClient {
	return &refundServiceClient{cc}
}

func (c *refundServiceClient) RefundPayment(ctx context.Context, in *RefundPaymentRequest, opts ...grpc.CallOption) (*RefundPaymentResponse, error) {
	out := new(RefundPaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.RefundService/RefundPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) GetRefund(ctx context.Context, in *GetRefundRequest, opts ...grpc.CallOption) (*GetRefundResponse, error) {
	out := new(GetRefundResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.RefundService/GetRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) ListRefunds(ctx context.Context, in *ListRefundsRequest, opts ...grpc.CallOption) (*ListRefundsResponse, error) {
	out := new(ListRefundsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.RefundService/ListRefunds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServiceServer is the server API for RefundService service.
// All implementations must embed UnimplementedRefundServiceServer
// for forward compatibility
type RefundServiceServer interface {
	// Refund 退款
	RefundPayment(context.Context, *RefundPaymentRequest) (*RefundPaymentResponse, error)
	// GetRefund 获取退款单据
	GetRefund(context.Context, *GetRefundRequest) (*GetRefundResponse, error)
	// ListRefund 获取退款列表
	ListRefunds(context.Context, *ListRefundsRequest) (*ListRefundsResponse, error)
	mustEmbedUnimplementedRefundServiceServer()
}

// UnimplementedRefundServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRefundServiceServer struct {
}

func (UnimplementedRefundServiceServer) RefundPayment(context.Context, *RefundPaymentRequest) (*RefundPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundPayment not implemented")
}
func (UnimplementedRefundServiceServer) GetRefund(context.Context, *GetRefundRequest) (*GetRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRefund not implemented")
}
func (UnimplementedRefundServiceServer) ListRefunds(context.Context, *ListRefundsRequest) (*ListRefundsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRefunds not implemented")
}
func (UnimplementedRefundServiceServer) mustEmbedUnimplementedRefundServiceServer() {}

// UnsafeRefundServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RefundServiceServer will
// result in compilation errors.
type UnsafeRefundServiceServer interface {
	mustEmbedUnimplementedRefundServiceServer()
}

func RegisterRefundServiceServer(s grpc.ServiceRegistrar, srv RefundServiceServer) {
	s.RegisterService(&RefundService_ServiceDesc, srv)
}

func _RefundService_RefundPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).RefundPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.RefundService/RefundPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).RefundPayment(ctx, req.(*RefundPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_GetRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).GetRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.RefundService/GetRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).GetRefund(ctx, req.(*GetRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_ListRefunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRefundsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).ListRefunds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.RefundService/ListRefunds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).ListRefunds(ctx, req.(*ListRefundsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RefundService_ServiceDesc is the grpc.ServiceDesc for RefundService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RefundService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.payment.v2.RefundService",
	HandlerType: (*RefundServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefundPayment",
			Handler:    _RefundService_RefundPayment_Handler,
		},
		{
			MethodName: "GetRefund",
			Handler:    _RefundService_GetRefund_Handler,
		},
		{
			MethodName: "ListRefunds",
			Handler:    _RefundService_ListRefunds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/payment/v2/payment_refund_service.proto",
}
