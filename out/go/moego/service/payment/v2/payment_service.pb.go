// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/payment/v2/payment_service.proto

package paymentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// export transaction list request
type ExportTransactionListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListTransactionRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序条件
	OrderBys []*ListTransactionRequest_OrderBy `protobuf:"bytes,2,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// company_id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 文件名，不传默认 uuid
	FileName *string `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3,oneof" json:"file_name,omitempty"`
	// 文件类型，不传默认 xlsx
	FileType *string `protobuf:"bytes,7,opt,name=file_type,json=fileType,proto3,oneof" json:"file_type,omitempty"`
}

func (x *ExportTransactionListRequest) Reset() {
	*x = ExportTransactionListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportTransactionListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportTransactionListRequest) ProtoMessage() {}

func (x *ExportTransactionListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportTransactionListRequest.ProtoReflect.Descriptor instead.
func (*ExportTransactionListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{0}
}

func (x *ExportTransactionListRequest) GetFilter() *ListTransactionRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ExportTransactionListRequest) GetOrderBys() []*ListTransactionRequest_OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ExportTransactionListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ExportTransactionListRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ExportTransactionListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ExportTransactionListRequest) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

func (x *ExportTransactionListRequest) GetFileType() string {
	if x != nil && x.FileType != nil {
		return *x.FileType
	}
	return ""
}

// export transaction list response
type ExportTransactionListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *ExportTransactionListResponse) Reset() {
	*x = ExportTransactionListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportTransactionListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportTransactionListResponse) ProtoMessage() {}

func (x *ExportTransactionListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportTransactionListResponse.ProtoReflect.Descriptor instead.
func (*ExportTransactionListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{1}
}

func (x *ExportTransactionListResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// get payment view request
type GetPaymentViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPaymentViewRequest) Reset() {
	*x = GetPaymentViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentViewRequest) ProtoMessage() {}

func (x *GetPaymentViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentViewRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentViewRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPaymentViewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get payment view response
type GetPaymentViewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment view
	Payment *v2.PaymentView `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *GetPaymentViewResponse) Reset() {
	*x = GetPaymentViewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentViewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentViewResponse) ProtoMessage() {}

func (x *GetPaymentViewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentViewResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentViewResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPaymentViewResponse) GetPayment() *v2.PaymentView {
	if x != nil {
		return x.Payment
	}
	return nil
}

// get transaction request
type GetTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTransactionRequest) Reset() {
	*x = GetTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionRequest) ProtoMessage() {}

func (x *GetTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetTransactionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get transaction response
type GetTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction view
	Transaction *v2.PaymentTransactionView `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *GetTransactionResponse) Reset() {
	*x = GetTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionResponse) ProtoMessage() {}

func (x *GetTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetTransactionResponse) GetTransaction() *v2.PaymentTransactionView {
	if x != nil {
		return x.Transaction
	}
	return nil
}

// get channel payment request
type GetChannelPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 收款方
	Payee *v2.User `protobuf:"bytes,1,opt,name=payee,proto3" json:"payee,omitempty"`
}

func (x *GetChannelPaymentRequest) Reset() {
	*x = GetChannelPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelPaymentRequest) ProtoMessage() {}

func (x *GetChannelPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetChannelPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetChannelPaymentRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

// get channel payment response
type GetChannelPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// channel payment
	//
	// Types that are assignable to ChannelPayment:
	//
	//	*GetChannelPaymentResponse_StripeChannelPayment
	ChannelPayment isGetChannelPaymentResponse_ChannelPayment `protobuf_oneof:"channel_payment"`
	// channel type
	ChannelType v2.ChannelType `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetChannelPaymentResponse) Reset() {
	*x = GetChannelPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelPaymentResponse) ProtoMessage() {}

func (x *GetChannelPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetChannelPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{7}
}

func (m *GetChannelPaymentResponse) GetChannelPayment() isGetChannelPaymentResponse_ChannelPayment {
	if m != nil {
		return m.ChannelPayment
	}
	return nil
}

func (x *GetChannelPaymentResponse) GetStripeChannelPayment() *v2.StripeChannelPayment {
	if x, ok := x.GetChannelPayment().(*GetChannelPaymentResponse_StripeChannelPayment); ok {
		return x.StripeChannelPayment
	}
	return nil
}

func (x *GetChannelPaymentResponse) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

type isGetChannelPaymentResponse_ChannelPayment interface {
	isGetChannelPaymentResponse_ChannelPayment()
}

type GetChannelPaymentResponse_StripeChannelPayment struct {
	// stripe channel payment
	StripeChannelPayment *v2.StripeChannelPayment `protobuf:"bytes,1,opt,name=stripe_channel_payment,json=stripeChannelPayment,proto3,oneof"`
}

func (*GetChannelPaymentResponse_StripeChannelPayment) isGetChannelPaymentResponse_ChannelPayment() {}

// Request for GetPaymentVersion
type GetPaymentVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 收款方
	Payee *v2.User `protobuf:"bytes,1,opt,name=payee,proto3" json:"payee,omitempty"`
}

func (x *GetPaymentVersionRequest) Reset() {
	*x = GetPaymentVersionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionRequest) ProtoMessage() {}

func (x *GetPaymentVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetPaymentVersionRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

// Response for GetPaymentVersion
type GetPaymentVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付版本
	PaymentVersion v2.PaymentVersion `protobuf:"varint,1,opt,name=payment_version,json=paymentVersion,proto3,enum=moego.models.payment.v2.PaymentVersion" json:"payment_version,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetPaymentVersionResponse) Reset() {
	*x = GetPaymentVersionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentVersionResponse) ProtoMessage() {}

func (x *GetPaymentVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentVersionResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentVersionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetPaymentVersionResponse) GetPaymentVersion() v2.PaymentVersion {
	if x != nil {
		return x.PaymentVersion
	}
	return v2.PaymentVersion(0)
}

func (x *GetPaymentVersionResponse) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Request for GetChannelBalanceAccountInfo
type GetChannelBalanceAccountInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v2.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
}

func (x *GetChannelBalanceAccountInfoRequest) Reset() {
	*x = GetChannelBalanceAccountInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelBalanceAccountInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelBalanceAccountInfoRequest) ProtoMessage() {}

func (x *GetChannelBalanceAccountInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelBalanceAccountInfoRequest.ProtoReflect.Descriptor instead.
func (*GetChannelBalanceAccountInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetChannelBalanceAccountInfoRequest) GetUser() *v2.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *GetChannelBalanceAccountInfoRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetChannelBalanceAccountInfo
type GetChannelBalanceAccountInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available balance
	AvailableBalance *money.Money `protobuf:"bytes,1,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// pending balance
	PendingBalance *money.Money `protobuf:"bytes,2,opt,name=pending_balance,json=pendingBalance,proto3" json:"pending_balance,omitempty"`
}

func (x *GetChannelBalanceAccountInfoResponse) Reset() {
	*x = GetChannelBalanceAccountInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelBalanceAccountInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelBalanceAccountInfoResponse) ProtoMessage() {}

func (x *GetChannelBalanceAccountInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelBalanceAccountInfoResponse.ProtoReflect.Descriptor instead.
func (*GetChannelBalanceAccountInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetChannelBalanceAccountInfoResponse) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetChannelBalanceAccountInfoResponse) GetPendingBalance() *money.Money {
	if x != nil {
		return x.PendingBalance
	}
	return nil
}

// Request for CreatePayment
type CreatePaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必填，上层业务系统类型
	ExternalType v2.ExternalType `protobuf:"varint,1,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 必填，上层业务系统内单据 ID
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 付款方，可选
	Payer *v2.User `protobuf:"bytes,3,opt,name=payer,proto3" json:"payer,omitempty"`
	// 收款方
	Payee *v2.User `protobuf:"bytes,4,opt,name=payee,proto3" json:"payee,omitempty"`
	// 金额
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 支付类型，决定是pre-pay、pre-auth还是常规支付
	PaymentType v2.PaymentModel_PaymentType `protobuf:"varint,6,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
}

func (x *CreatePaymentRequest) Reset() {
	*x = CreatePaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentRequest) ProtoMessage() {}

func (x *CreatePaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentRequest.ProtoReflect.Descriptor instead.
func (*CreatePaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreatePaymentRequest) GetExternalType() v2.ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return v2.ExternalType(0)
}

func (x *CreatePaymentRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *CreatePaymentRequest) GetPayer() *v2.User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *CreatePaymentRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *CreatePaymentRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreatePaymentRequest) GetPaymentType() v2.PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v2.PaymentModel_PaymentType(0)
}

// Response for CreatePayment
type CreatePaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 生成的支付单据
	Payment *v2.PaymentModel `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *CreatePaymentResponse) Reset() {
	*x = CreatePaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentResponse) ProtoMessage() {}

func (x *CreatePaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentResponse.ProtoReflect.Descriptor instead.
func (*CreatePaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreatePaymentResponse) GetPayment() *v2.PaymentModel {
	if x != nil {
		return x.Payment
	}
	return nil
}

// cancel payment request
type CancelPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CancelPaymentRequest) Reset() {
	*x = CancelPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentRequest) ProtoMessage() {}

func (x *CancelPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentRequest.ProtoReflect.Descriptor instead.
func (*CancelPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{14}
}

func (x *CancelPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// cancel payment response
type CancelPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CancelPaymentResponse) Reset() {
	*x = CancelPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentResponse) ProtoMessage() {}

func (x *CancelPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentResponse.ProtoReflect.Descriptor instead.
func (*CancelPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{15}
}

func (x *CancelPaymentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// Request for GetPayData
type GetPayDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
	ChannelType *v2.ChannelType `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
}

func (x *GetPayDataRequest) Reset() {
	*x = GetPayDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataRequest) ProtoMessage() {}

func (x *GetPayDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataRequest.ProtoReflect.Descriptor instead.
func (*GetPayDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetPayDataRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetPayDataRequest) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

// Response for GetPayData
type GetPayDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// 支付数据
	//
	// Types that are assignable to Data:
	//
	//	*GetPayDataResponse_AdyenData_
	Data isGetPayDataResponse_Data `protobuf_oneof:"data"`
}

func (x *GetPayDataResponse) Reset() {
	*x = GetPayDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResponse) ProtoMessage() {}

func (x *GetPayDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResponse.ProtoReflect.Descriptor instead.
func (*GetPayDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetPayDataResponse) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (m *GetPayDataResponse) GetData() isGetPayDataResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *GetPayDataResponse) GetAdyenData() *GetPayDataResponse_AdyenData {
	if x, ok := x.GetData().(*GetPayDataResponse_AdyenData_); ok {
		return x.AdyenData
	}
	return nil
}

type isGetPayDataResponse_Data interface {
	isGetPayDataResponse_Data()
}

type GetPayDataResponse_AdyenData_ struct {
	// adyen data
	AdyenData *GetPayDataResponse_AdyenData `protobuf:"bytes,2,opt,name=adyen_data,json=adyenData,proto3,oneof"`
}

func (*GetPayDataResponse_AdyenData_) isGetPayDataResponse_Data() {}

// Request for Pay
type PayPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据 id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 支付方式
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,2,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	Detail *v2.PaymentMethod_Detail `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail,omitempty"`
	// 是否添加cv fee,不传的时候后端判断
	AddConvenienceFee *bool `protobuf:"varint,4,opt,name=add_convenience_fee,json=addConvenienceFee,proto3,oneof" json:"add_convenience_fee,omitempty"`
	// payer, 一般是customer name
	Payer string `protobuf:"bytes,5,opt,name=payer,proto3" json:"payer,omitempty"`
	// payment description 支付描述 自定义
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *PayPaymentRequest) Reset() {
	*x = PayPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayPaymentRequest) ProtoMessage() {}

func (x *PayPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayPaymentRequest.ProtoReflect.Descriptor instead.
func (*PayPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{18}
}

func (x *PayPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PayPaymentRequest) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *PayPaymentRequest) GetDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *PayPaymentRequest) GetAddConvenienceFee() bool {
	if x != nil && x.AddConvenienceFee != nil {
		return *x.AddConvenienceFee
	}
	return false
}

func (x *PayPaymentRequest) GetPayer() string {
	if x != nil {
		return x.Payer
	}
	return ""
}

func (x *PayPaymentRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// Response for Pay
type PayPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// channel response，渠道返回的原始数据，用于前端加载第三方支付组件
	// e.g. adyen 3ds2:
	//
	//	`{
	//	  "resultCode": "IdentifyShopper",
	//	  "action": {
	//	    "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
	//	    "paymentMethodType": "scheme",
	//	    "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
	//	    "subtype": "fingerprint",
	//	    "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
	//	    "type": "threeDS2"
	//	  }
	//	}`
	ChannelResponse string `protobuf:"bytes,2,opt,name=channel_response,json=channelResponse,proto3" json:"channel_response,omitempty"`
	// channel payment
	ChannelPayment *v2.ChannelPayment `protobuf:"bytes,3,opt,name=channel_payment,json=channelPayment,proto3" json:"channel_payment,omitempty"`
}

func (x *PayPaymentResponse) Reset() {
	*x = PayPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayPaymentResponse) ProtoMessage() {}

func (x *PayPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayPaymentResponse.ProtoReflect.Descriptor instead.
func (*PayPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{19}
}

func (x *PayPaymentResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PayPaymentResponse) GetChannelResponse() string {
	if x != nil {
		return x.ChannelResponse
	}
	return ""
}

func (x *PayPaymentResponse) GetChannelPayment() *v2.ChannelPayment {
	if x != nil {
		return x.ChannelPayment
	}
	return nil
}

// Request for SubmitActionDetail
type SubmitActionDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道
	ChannelType v2.ChannelType `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_type,omitempty"`
	// Action Result，前端从组件拿到的原始数据，
	// e.g. ayden 3ds2:
	//
	//	`{
	//	  "details": {
	//	    "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
	//	  }
	//	}`
	ActionResult string `protobuf:"bytes,2,opt,name=action_result,json=actionResult,proto3" json:"action_result,omitempty"`
}

func (x *SubmitActionDetailRequest) Reset() {
	*x = SubmitActionDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailRequest) ProtoMessage() {}

func (x *SubmitActionDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailRequest.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{20}
}

func (x *SubmitActionDetailRequest) GetChannelType() v2.ChannelType {
	if x != nil {
		return x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *SubmitActionDetailRequest) GetActionResult() string {
	if x != nil {
		return x.ActionResult
	}
	return ""
}

// Response for SubmitPayDetail
type SubmitActionDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg 可以展示给用户的信息
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
	// e.g. adyen:
	//
	//	`{
	//	  "resultCode": "Authorised",
	//	  "pspReference": "V4HZ4RBFJGXXGN82"
	//	}`
	ChannelResponse string `protobuf:"bytes,2,opt,name=channel_response,json=channelResponse,proto3" json:"channel_response,omitempty"`
}

func (x *SubmitActionDetailResponse) Reset() {
	*x = SubmitActionDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitActionDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitActionDetailResponse) ProtoMessage() {}

func (x *SubmitActionDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitActionDetailResponse.ProtoReflect.Descriptor instead.
func (*SubmitActionDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{21}
}

func (x *SubmitActionDetailResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SubmitActionDetailResponse) GetChannelResponse() string {
	if x != nil {
		return x.ChannelResponse
	}
	return ""
}

// get payment request
type GetPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付单据id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPaymentRequest) Reset() {
	*x = GetPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentRequest) ProtoMessage() {}

func (x *GetPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetPaymentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get payment response
type GetPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment 实体
	Payment *v2.PaymentModel `protobuf:"bytes,1,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *GetPaymentResponse) Reset() {
	*x = GetPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentResponse) ProtoMessage() {}

func (x *GetPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetPaymentResponse) GetPayment() *v2.PaymentModel {
	if x != nil {
		return x.Payment
	}
	return nil
}

// list payment request
type ListPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页查询请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListPaymentRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPaymentRequest) Reset() {
	*x = ListPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentRequest) ProtoMessage() {}

func (x *ListPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentRequest.ProtoReflect.Descriptor instead.
func (*ListPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListPaymentRequest) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListPaymentRequest) GetFilter() *ListPaymentRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list payment response
type ListPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付列表
	PaymentViews []*v2.PaymentView `protobuf:"bytes,1,rep,name=payment_views,json=paymentViews,proto3" json:"payment_views,omitempty"`
	// 分页
	PaginationRequest *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
}

func (x *ListPaymentResponse) Reset() {
	*x = ListPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentResponse) ProtoMessage() {}

func (x *ListPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentResponse.ProtoReflect.Descriptor instead.
func (*ListPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListPaymentResponse) GetPaymentViews() []*v2.PaymentView {
	if x != nil {
		return x.PaymentViews
	}
	return nil
}

func (x *ListPaymentResponse) GetPaginationRequest() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

// add recurring payment method request
type AddRecurringPaymentMethodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// customer code
	EncryptedCustomerId *string `protobuf:"bytes,2,opt,name=encrypted_customer_id,json=encryptedCustomerId,proto3,oneof" json:"encrypted_customer_id,omitempty"`
	// payee
	Payee *v2.User `protobuf:"bytes,3,opt,name=payee,proto3" json:"payee,omitempty"`
	// channel type
	ChannelType *v2.ChannelType `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3,enum=moego.models.payment.v2.ChannelType,oneof" json:"channel_type,omitempty"`
	// payment method type
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,5,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	Detail *v2.PaymentMethod_Detail `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
	// 透传参数，一般是用户自定义的额外信息
	Extra *v2.RecurringPaymentMethodModel_Extra `protobuf:"bytes,7,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
}

func (x *AddRecurringPaymentMethodRequest) Reset() {
	*x = AddRecurringPaymentMethodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodRequest) ProtoMessage() {}

func (x *AddRecurringPaymentMethodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodRequest.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{26}
}

func (x *AddRecurringPaymentMethodRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *AddRecurringPaymentMethodRequest) GetEncryptedCustomerId() string {
	if x != nil && x.EncryptedCustomerId != nil {
		return *x.EncryptedCustomerId
	}
	return ""
}

func (x *AddRecurringPaymentMethodRequest) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *AddRecurringPaymentMethodRequest) GetChannelType() v2.ChannelType {
	if x != nil && x.ChannelType != nil {
		return *x.ChannelType
	}
	return v2.ChannelType(0)
}

func (x *AddRecurringPaymentMethodRequest) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *AddRecurringPaymentMethodRequest) GetDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *AddRecurringPaymentMethodRequest) GetExtra() *v2.RecurringPaymentMethodModel_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// add recurring payment method response
type AddRecurringPaymentMethodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已保存的支付方式
	RecurringPaymentMethod *v2.RecurringPaymentMethodModel `protobuf:"bytes,1,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3,oneof" json:"recurring_payment_method,omitempty"`
	// 渠道返回的原始数据
	ChannelResponse string `protobuf:"bytes,2,opt,name=channel_response,json=channelResponse,proto3" json:"channel_response,omitempty"`
}

func (x *AddRecurringPaymentMethodResponse) Reset() {
	*x = AddRecurringPaymentMethodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecurringPaymentMethodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecurringPaymentMethodResponse) ProtoMessage() {}

func (x *AddRecurringPaymentMethodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecurringPaymentMethodResponse.ProtoReflect.Descriptor instead.
func (*AddRecurringPaymentMethodResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{27}
}

func (x *AddRecurringPaymentMethodResponse) GetRecurringPaymentMethod() *v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.RecurringPaymentMethod
	}
	return nil
}

func (x *AddRecurringPaymentMethodResponse) GetChannelResponse() string {
	if x != nil {
		return x.ChannelResponse
	}
	return ""
}

// delete recurring payment method request
type DeleteRecurringPaymentMethodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 存储的 payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *DeleteRecurringPaymentMethodRequest) Reset() {
	*x = DeleteRecurringPaymentMethodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodRequest) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodRequest.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteRecurringPaymentMethodRequest) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// delete recurring payment method response
type DeleteRecurringPaymentMethodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRecurringPaymentMethodResponse) Reset() {
	*x = DeleteRecurringPaymentMethodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecurringPaymentMethodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecurringPaymentMethodResponse) ProtoMessage() {}

func (x *DeleteRecurringPaymentMethodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecurringPaymentMethodResponse.ProtoReflect.Descriptor instead.
func (*DeleteRecurringPaymentMethodResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{29}
}

// set recurring payment method primary request
type SetRecurringPaymentMethodPrimaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment method id
	PaymentMethodId int64 `protobuf:"varint,1,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryRequest) Reset() {
	*x = SetRecurringPaymentMethodPrimaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryRequest) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryRequest.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{30}
}

func (x *SetRecurringPaymentMethodPrimaryRequest) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

// set recurring payment method primary response
type SetRecurringPaymentMethodPrimaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已设置的 payment method
	RecurringPaymentMethod *v2.RecurringPaymentMethodModel `protobuf:"bytes,1,opt,name=recurring_payment_method,json=recurringPaymentMethod,proto3" json:"recurring_payment_method,omitempty"`
}

func (x *SetRecurringPaymentMethodPrimaryResponse) Reset() {
	*x = SetRecurringPaymentMethodPrimaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecurringPaymentMethodPrimaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecurringPaymentMethodPrimaryResponse) ProtoMessage() {}

func (x *SetRecurringPaymentMethodPrimaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecurringPaymentMethodPrimaryResponse.ProtoReflect.Descriptor instead.
func (*SetRecurringPaymentMethodPrimaryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{31}
}

func (x *SetRecurringPaymentMethodPrimaryResponse) GetRecurringPaymentMethod() *v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.RecurringPaymentMethod
	}
	return nil
}

// list recurring payment method request
type ListRecurringPaymentMethodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListRecurringPaymentMethodsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListRecurringPaymentMethodsRequest) Reset() {
	*x = ListRecurringPaymentMethodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsRequest) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsRequest.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{32}
}

func (x *ListRecurringPaymentMethodsRequest) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListRecurringPaymentMethodsRequest) GetFilter() *ListRecurringPaymentMethodsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list recurring payment method response
type ListRecurringPaymentMethodsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页
	PaginationResponse *v21.PaginationResponse `protobuf:"bytes,1,opt,name=pagination_response,json=paginationResponse,proto3" json:"pagination_response,omitempty"`
	// 已保存的支付方式
	PaymentMethods []*v2.RecurringPaymentMethodModel `protobuf:"bytes,2,rep,name=payment_methods,json=paymentMethods,proto3" json:"payment_methods,omitempty"`
}

func (x *ListRecurringPaymentMethodsResponse) Reset() {
	*x = ListRecurringPaymentMethodsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsResponse) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsResponse.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListRecurringPaymentMethodsResponse) GetPaginationResponse() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationResponse
	}
	return nil
}

func (x *ListRecurringPaymentMethodsResponse) GetPaymentMethods() []*v2.RecurringPaymentMethodModel {
	if x != nil {
		return x.PaymentMethods
	}
	return nil
}

// create combined payment request
type CreateCombinedPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必填，单个支付请求
	CombinedItems []*CreateCombinedPaymentRequest_CombinedItem `protobuf:"bytes,1,rep,name=combined_items,json=combinedItems,proto3" json:"combined_items,omitempty"`
	// 支付类型，合并支付的这里都是传standard的了
	PaymentType v2.PaymentModel_PaymentType `protobuf:"varint,2,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
}

func (x *CreateCombinedPaymentRequest) Reset() {
	*x = CreateCombinedPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCombinedPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCombinedPaymentRequest) ProtoMessage() {}

func (x *CreateCombinedPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCombinedPaymentRequest.ProtoReflect.Descriptor instead.
func (*CreateCombinedPaymentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{34}
}

func (x *CreateCombinedPaymentRequest) GetCombinedItems() []*CreateCombinedPaymentRequest_CombinedItem {
	if x != nil {
		return x.CombinedItems
	}
	return nil
}

func (x *CreateCombinedPaymentRequest) GetPaymentType() v2.PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v2.PaymentModel_PaymentType(0)
}

// create combined payment response
type CreateCombinedPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction id
	TransactionId int64 `protobuf:"varint,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// 生成的支付单据
	PaymentModels []*v2.PaymentModel `protobuf:"bytes,2,rep,name=payment_models,json=paymentModels,proto3" json:"payment_models,omitempty"`
}

func (x *CreateCombinedPaymentResponse) Reset() {
	*x = CreateCombinedPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCombinedPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCombinedPaymentResponse) ProtoMessage() {}

func (x *CreateCombinedPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCombinedPaymentResponse.ProtoReflect.Descriptor instead.
func (*CreateCombinedPaymentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{35}
}

func (x *CreateCombinedPaymentResponse) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

func (x *CreateCombinedPaymentResponse) GetPaymentModels() []*v2.PaymentModel {
	if x != nil {
		return x.PaymentModels
	}
	return nil
}

// list payment transaction request
type ListTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页查询请求
	PaginationRequest *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
	// filter
	Filter *ListTransactionRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序条件
	OrderBys []*ListTransactionRequest_OrderBy `protobuf:"bytes,3,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// company_id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListTransactionRequest) Reset() {
	*x = ListTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTransactionRequest) ProtoMessage() {}

func (x *ListTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTransactionRequest.ProtoReflect.Descriptor instead.
func (*ListTransactionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{36}
}

func (x *ListTransactionRequest) GetPaginationRequest() *v21.PaginationRequest {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

func (x *ListTransactionRequest) GetFilter() *ListTransactionRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListTransactionRequest) GetOrderBys() []*ListTransactionRequest_OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ListTransactionRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// list payment transaction response
type ListTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付列表
	Transactions []*v2.PaymentTransactionView `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
	// 分页
	PaginationRequest *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination_request,json=paginationRequest,proto3" json:"pagination_request,omitempty"`
}

func (x *ListTransactionResponse) Reset() {
	*x = ListTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTransactionResponse) ProtoMessage() {}

func (x *ListTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTransactionResponse.ProtoReflect.Descriptor instead.
func (*ListTransactionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{37}
}

func (x *ListTransactionResponse) GetTransactions() []*v2.PaymentTransactionView {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *ListTransactionResponse) GetPaginationRequest() *v21.PaginationResponse {
	if x != nil {
		return x.PaginationRequest
	}
	return nil
}

// list payment by transaction request
type ListPaymentByTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction id
	TransactionId int64 `protobuf:"varint,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
}

func (x *ListPaymentByTransactionRequest) Reset() {
	*x = ListPaymentByTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentByTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentByTransactionRequest) ProtoMessage() {}

func (x *ListPaymentByTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentByTransactionRequest.ProtoReflect.Descriptor instead.
func (*ListPaymentByTransactionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{38}
}

func (x *ListPaymentByTransactionRequest) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

// list payment by transaction response
type ListPaymentByTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 生成的支付单据
	Payments []*v2.PaymentView `protobuf:"bytes,1,rep,name=payments,proto3" json:"payments,omitempty"`
}

func (x *ListPaymentByTransactionResponse) Reset() {
	*x = ListPaymentByTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentByTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentByTransactionResponse) ProtoMessage() {}

func (x *ListPaymentByTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentByTransactionResponse.ProtoReflect.Descriptor instead.
func (*ListPaymentByTransactionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{39}
}

func (x *ListPaymentByTransactionResponse) GetPayments() []*v2.PaymentView {
	if x != nil {
		return x.Payments
	}
	return nil
}

// get payment setting request
type GetPaymentSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v2.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *GetPaymentSettingRequest) Reset() {
	*x = GetPaymentSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentSettingRequest) ProtoMessage() {}

func (x *GetPaymentSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentSettingRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetPaymentSettingRequest) GetUser() *v2.User {
	if x != nil {
		return x.User
	}
	return nil
}

// get payment setting response
type GetPaymentSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付设置
	PaymentSetting *v2.PaymentSetting `protobuf:"bytes,1,opt,name=payment_setting,json=paymentSetting,proto3" json:"payment_setting,omitempty"`
}

func (x *GetPaymentSettingResponse) Reset() {
	*x = GetPaymentSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentSettingResponse) ProtoMessage() {}

func (x *GetPaymentSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentSettingResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetPaymentSettingResponse) GetPaymentSetting() *v2.PaymentSetting {
	if x != nil {
		return x.PaymentSetting
	}
	return nil
}

// update payment setting request
type UpdatePaymentSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v2.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// convenience fee 配置
	ConvenienceFeeConfig *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation `protobuf:"bytes,2,opt,name=convenience_fee_config,json=convenienceFeeConfig,proto3,oneof" json:"convenience_fee_config,omitempty"`
	// tips 配置
	TipsConfig *UpdatePaymentSettingRequest_TipsConfigOperation `protobuf:"bytes,3,opt,name=tips_config,json=tipsConfig,proto3,oneof" json:"tips_config,omitempty"`
}

func (x *UpdatePaymentSettingRequest) Reset() {
	*x = UpdatePaymentSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePaymentSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePaymentSettingRequest) ProtoMessage() {}

func (x *UpdatePaymentSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePaymentSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdatePaymentSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{42}
}

func (x *UpdatePaymentSettingRequest) GetUser() *v2.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdatePaymentSettingRequest) GetConvenienceFeeConfig() *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation {
	if x != nil {
		return x.ConvenienceFeeConfig
	}
	return nil
}

func (x *UpdatePaymentSettingRequest) GetTipsConfig() *UpdatePaymentSettingRequest_TipsConfigOperation {
	if x != nil {
		return x.TipsConfig
	}
	return nil
}

// update payment setting response
type UpdatePaymentSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付设置
	PaymentSetting *v2.PaymentSetting `protobuf:"bytes,1,opt,name=payment_setting,json=paymentSetting,proto3" json:"payment_setting,omitempty"`
}

func (x *UpdatePaymentSettingResponse) Reset() {
	*x = UpdatePaymentSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePaymentSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePaymentSettingResponse) ProtoMessage() {}

func (x *UpdatePaymentSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePaymentSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdatePaymentSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{43}
}

func (x *UpdatePaymentSettingResponse) GetPaymentSetting() *v2.PaymentSetting {
	if x != nil {
		return x.PaymentSetting
	}
	return nil
}

// adyen data
type GetPayDataResponse_AdyenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// data
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetPayDataResponse_AdyenData) Reset() {
	*x = GetPayDataResponse_AdyenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayDataResponse_AdyenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayDataResponse_AdyenData) ProtoMessage() {}

func (x *GetPayDataResponse_AdyenData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayDataResponse_AdyenData.ProtoReflect.Descriptor instead.
func (*GetPayDataResponse_AdyenData) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetPayDataResponse_AdyenData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// filter
type ListPaymentRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 买家
	Payers []*v2.User `protobuf:"bytes,1,rep,name=payers,proto3" json:"payers,omitempty"`
	// 卖家
	Payees []*v2.User `protobuf:"bytes,2,rep,name=payees,proto3" json:"payees,omitempty"`
	// order id
	OrderIds []int64 `protobuf:"varint,3,rep,packed,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	// order payment id
	OrderPaymentIds []int64 `protobuf:"varint,4,rep,packed,name=order_payment_ids,json=orderPaymentIds,proto3" json:"order_payment_ids,omitempty"`
	// payment
	Ids []int64 `protobuf:"varint,5,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 查询时间范围
	TimeRange *interval.Interval `protobuf:"bytes,6,opt,name=time_range,json=timeRange,proto3,oneof" json:"time_range,omitempty"`
}

func (x *ListPaymentRequest_Filter) Reset() {
	*x = ListPaymentRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentRequest_Filter) ProtoMessage() {}

func (x *ListPaymentRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPaymentRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ListPaymentRequest_Filter) GetPayers() []*v2.User {
	if x != nil {
		return x.Payers
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetPayees() []*v2.User {
	if x != nil {
		return x.Payees
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetOrderIds() []int64 {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetOrderPaymentIds() []int64 {
	if x != nil {
		return x.OrderPaymentIds
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPaymentRequest_Filter) GetTimeRange() *interval.Interval {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// filter
type ListRecurringPaymentMethodsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户
	Users []*v2.User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// payment method ids
	RecurringPaymentMethodIds []int64 `protobuf:"varint,2,rep,packed,name=recurring_payment_method_ids,json=recurringPaymentMethodIds,proto3" json:"recurring_payment_method_ids,omitempty"` // users
}

func (x *ListRecurringPaymentMethodsRequest_Filter) Reset() {
	*x = ListRecurringPaymentMethodsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRecurringPaymentMethodsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecurringPaymentMethodsRequest_Filter) ProtoMessage() {}

func (x *ListRecurringPaymentMethodsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecurringPaymentMethodsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListRecurringPaymentMethodsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *ListRecurringPaymentMethodsRequest_Filter) GetUsers() []*v2.User {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListRecurringPaymentMethodsRequest_Filter) GetRecurringPaymentMethodIds() []int64 {
	if x != nil {
		return x.RecurringPaymentMethodIds
	}
	return nil
}

// combined item, 单个请求金额
type CreateCombinedPaymentRequest_CombinedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必填，上层业务系统类型
	ExternalType v2.ExternalType `protobuf:"varint,1,opt,name=external_type,json=externalType,proto3,enum=moego.models.payment.v2.ExternalType" json:"external_type,omitempty"`
	// 必填，上层业务系统内单据 ID
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 付款方，可选
	Payer *v2.User `protobuf:"bytes,3,opt,name=payer,proto3" json:"payer,omitempty"`
	// 收款方
	Payee *v2.User `protobuf:"bytes,4,opt,name=payee,proto3" json:"payee,omitempty"`
	// 金额
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// extra
	Extra *CreateCombinedPaymentRequest_CombinedItem_Extra `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *CreateCombinedPaymentRequest_CombinedItem) Reset() {
	*x = CreateCombinedPaymentRequest_CombinedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCombinedPaymentRequest_CombinedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCombinedPaymentRequest_CombinedItem) ProtoMessage() {}

func (x *CreateCombinedPaymentRequest_CombinedItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCombinedPaymentRequest_CombinedItem.ProtoReflect.Descriptor instead.
func (*CreateCombinedPaymentRequest_CombinedItem) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{34, 0}
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetExternalType() v2.ExternalType {
	if x != nil {
		return x.ExternalType
	}
	return v2.ExternalType(0)
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetPayer() *v2.User {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetPayee() *v2.User {
	if x != nil {
		return x.Payee
	}
	return nil
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreateCombinedPaymentRequest_CombinedItem) GetExtra() *CreateCombinedPaymentRequest_CombinedItem_Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// extra，非关键信息
type CreateCombinedPaymentRequest_CombinedItem_Extra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模块
	Module string `protobuf:"bytes,1,opt,name=module,proto3" json:"module,omitempty"`
	// 模块id
	ModuleId int64 `protobuf:"varint,2,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`
	// invoice id
	InvoiceId int64 `protobuf:"varint,3,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) Reset() {
	*x = CreateCombinedPaymentRequest_CombinedItem_Extra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCombinedPaymentRequest_CombinedItem_Extra) ProtoMessage() {}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCombinedPaymentRequest_CombinedItem_Extra.ProtoReflect.Descriptor instead.
func (*CreateCombinedPaymentRequest_CombinedItem_Extra) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{34, 0, 0}
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) GetModuleId() int64 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCombinedPaymentRequest_CombinedItem_Extra) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// filter
type ListTransactionRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道类型
	ChannelTypes []v2.ChannelType `protobuf:"varint,1,rep,packed,name=channel_types,json=channelTypes,proto3,enum=moego.models.payment.v2.ChannelType" json:"channel_types,omitempty"`
	// 查询时间范围
	Interval *interval.Interval `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// 支付方法类型
	MethodTypes []v2.PaymentMethod_MethodType `protobuf:"varint,3,rep,packed,name=method_types,json=methodTypes,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"method_types,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// transaction status
	Statuses []v2.PaymentTransactionModel_TransactionStatus `protobuf:"varint,6,rep,packed,name=statuses,proto3,enum=moego.models.payment.v2.PaymentTransactionModel_TransactionStatus" json:"statuses,omitempty"`
}

func (x *ListTransactionRequest_Filter) Reset() {
	*x = ListTransactionRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTransactionRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTransactionRequest_Filter) ProtoMessage() {}

func (x *ListTransactionRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTransactionRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTransactionRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *ListTransactionRequest_Filter) GetChannelTypes() []v2.ChannelType {
	if x != nil {
		return x.ChannelTypes
	}
	return nil
}

func (x *ListTransactionRequest_Filter) GetInterval() *interval.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *ListTransactionRequest_Filter) GetMethodTypes() []v2.PaymentMethod_MethodType {
	if x != nil {
		return x.MethodTypes
	}
	return nil
}

func (x *ListTransactionRequest_Filter) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ListTransactionRequest_Filter) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListTransactionRequest_Filter) GetStatuses() []v2.PaymentTransactionModel_TransactionStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 排序条件
type ListTransactionRequest_OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否降序
	Desc bool `protobuf:"varint,1,opt,name=desc,proto3" json:"desc,omitempty"`
	// 排序字段
	//
	// Types that are assignable to Field:
	//
	//	*ListTransactionRequest_OrderBy_CreatedAt
	//	*ListTransactionRequest_OrderBy_Id
	Field isListTransactionRequest_OrderBy_Field `protobuf_oneof:"field"`
}

func (x *ListTransactionRequest_OrderBy) Reset() {
	*x = ListTransactionRequest_OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTransactionRequest_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTransactionRequest_OrderBy) ProtoMessage() {}

func (x *ListTransactionRequest_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTransactionRequest_OrderBy.ProtoReflect.Descriptor instead.
func (*ListTransactionRequest_OrderBy) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{36, 1}
}

func (x *ListTransactionRequest_OrderBy) GetDesc() bool {
	if x != nil {
		return x.Desc
	}
	return false
}

func (m *ListTransactionRequest_OrderBy) GetField() isListTransactionRequest_OrderBy_Field {
	if m != nil {
		return m.Field
	}
	return nil
}

func (x *ListTransactionRequest_OrderBy) GetCreatedAt() bool {
	if x, ok := x.GetField().(*ListTransactionRequest_OrderBy_CreatedAt); ok {
		return x.CreatedAt
	}
	return false
}

func (x *ListTransactionRequest_OrderBy) GetId() bool {
	if x, ok := x.GetField().(*ListTransactionRequest_OrderBy_Id); ok {
		return x.Id
	}
	return false
}

type isListTransactionRequest_OrderBy_Field interface {
	isListTransactionRequest_OrderBy_Field()
}

type ListTransactionRequest_OrderBy_CreatedAt struct {
	// 创建时间
	CreatedAt bool `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3,oneof"`
}

type ListTransactionRequest_OrderBy_Id struct {
	// ID
	Id bool `protobuf:"varint,3,opt,name=id,proto3,oneof"`
}

func (*ListTransactionRequest_OrderBy_CreatedAt) isListTransactionRequest_OrderBy_Field() {}

func (*ListTransactionRequest_OrderBy_Id) isListTransactionRequest_OrderBy_Field() {}

// convenience fee 配置
type UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否启用 convenience fee
	Enable *bool `protobuf:"varint,1,opt,name=enable,proto3,oneof" json:"enable,omitempty"`
	// agreement signature url
	AgreementSignatureUrl *string `protobuf:"bytes,2,opt,name=agreement_signature_url,json=agreementSignatureUrl,proto3,oneof" json:"agreement_signature_url,omitempty"`
	// fee name
	FeeName *string `protobuf:"bytes,3,opt,name=fee_name,json=feeName,proto3,oneof" json:"fee_name,omitempty"`
	// fee rate for online
	OnlineFeeRate *v2.FeeRate `protobuf:"bytes,4,opt,name=online_fee_rate,json=onlineFeeRate,proto3,oneof" json:"online_fee_rate,omitempty"`
	// fee rate for in person
	TerminalFeeRate *v2.FeeRate `protobuf:"bytes,5,opt,name=terminal_fee_rate,json=terminalFeeRate,proto3,oneof" json:"terminal_fee_rate,omitempty"`
	// 计算方式
	CalculationMethod *v2.PaymentSetting_ConvenienceFeeConfig_CalculationMethod `protobuf:"varint,6,opt,name=calculation_method,json=calculationMethod,proto3,enum=moego.models.payment.v2.PaymentSetting_ConvenienceFeeConfig_CalculationMethod,oneof" json:"calculation_method,omitempty"`
	// exclude debit card
	ExcludeDebitCard *bool `protobuf:"varint,7,opt,name=exclude_debit_card,json=excludeDebitCard,proto3,oneof" json:"exclude_debit_card,omitempty"`
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) Reset() {
	*x = UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) ProtoMessage() {}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation.ProtoReflect.Descriptor instead.
func (*UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{42, 0}
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetEnable() bool {
	if x != nil && x.Enable != nil {
		return *x.Enable
	}
	return false
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetAgreementSignatureUrl() string {
	if x != nil && x.AgreementSignatureUrl != nil {
		return *x.AgreementSignatureUrl
	}
	return ""
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetFeeName() string {
	if x != nil && x.FeeName != nil {
		return *x.FeeName
	}
	return ""
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetOnlineFeeRate() *v2.FeeRate {
	if x != nil {
		return x.OnlineFeeRate
	}
	return nil
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetTerminalFeeRate() *v2.FeeRate {
	if x != nil {
		return x.TerminalFeeRate
	}
	return nil
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetCalculationMethod() v2.PaymentSetting_ConvenienceFeeConfig_CalculationMethod {
	if x != nil && x.CalculationMethod != nil {
		return *x.CalculationMethod
	}
	return v2.PaymentSetting_ConvenienceFeeConfig_CalculationMethod(0)
}

func (x *UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation) GetExcludeDebitCard() bool {
	if x != nil && x.ExcludeDebitCard != nil {
		return *x.ExcludeDebitCard
	}
	return false
}

// tips config
type UpdatePaymentSettingRequest_TipsConfigOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tips option for invoice page
	CashierTipsOption *v2.PaymentSetting_TipsConfig `protobuf:"bytes,1,opt,name=cashier_tips_option,json=cashierTipsOption,proto3,oneof" json:"cashier_tips_option,omitempty"`
	// tips option for terminal
	TerminalTipsOption *v2.PaymentSetting_TipsConfig `protobuf:"bytes,2,opt,name=terminal_tips_option,json=terminalTipsOption,proto3,oneof" json:"terminal_tips_option,omitempty"`
	// appearance
	TipsAppearance *string `protobuf:"bytes,3,opt,name=tips_appearance,json=tipsAppearance,proto3,oneof" json:"tips_appearance,omitempty"`
}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) Reset() {
	*x = UpdatePaymentSettingRequest_TipsConfigOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePaymentSettingRequest_TipsConfigOperation) ProtoMessage() {}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_payment_v2_payment_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePaymentSettingRequest_TipsConfigOperation.ProtoReflect.Descriptor instead.
func (*UpdatePaymentSettingRequest_TipsConfigOperation) Descriptor() ([]byte, []int) {
	return file_moego_service_payment_v2_payment_service_proto_rawDescGZIP(), []int{42, 1}
}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) GetCashierTipsOption() *v2.PaymentSetting_TipsConfig {
	if x != nil {
		return x.CashierTipsOption
	}
	return nil
}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) GetTerminalTipsOption() *v2.PaymentSetting_TipsConfig {
	if x != nil {
		return x.TerminalTipsOption
	}
	return nil
}

func (x *UpdatePaymentSettingRequest_TipsConfigOperation) GetTipsAppearance() string {
	if x != nil && x.TipsAppearance != nil {
		return *x.TipsAppearance
	}
	return ""
}

var File_moego_service_payment_v2_payment_service_proto protoreflect.FileDescriptor

var file_moego_service_payment_v2_payment_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa6, 0x03, 0x0a, 0x1c, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x26, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x38, 0x0a, 0x1d, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x58, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x6b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x51, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x22, 0xde, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x16, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x14, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x4f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xa1, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f,
	0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x70, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xef, 0x02, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x2a, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x58,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x26, 0x0a, 0x14, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x29, 0x0a, 0x15, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x93, 0x01, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x22, 0xdf, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x57, 0x0a, 0x0a, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x09, 0x61, 0x64, 0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x1f, 0x0a, 0x09, 0x41, 0x64,
	0x79, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xd2, 0x02, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x61, 0x0a, 0x13, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x11, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x79,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x89,
	0x01, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x59, 0x0a, 0x1a, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x55, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3f, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x22, 0xd1, 0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x9b, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x06, 0x70, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x70, 0x61, 0x79,
	0x65, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x70, 0x61, 0x79, 0x65, 0x65, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a,
	0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a,
	0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xca, 0x04, 0x0a, 0x20,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x13, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x12, 0x4c, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x02, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x61, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x55, 0x0a, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x48, 0x03, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65,
	0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0xe0, 0x01, 0x0a, 0x21, 0x41, 0x64, 0x64,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73,
	0x0a, 0x18, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x16, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x1b,
	0x0a, 0x19, 0x5f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x51, 0x0a, 0x23, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x26,
	0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55, 0x0a, 0x27, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x22, 0x9a, 0x01,
	0x0a, 0x28, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x18, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x16, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xd3, 0x02, 0x0a, 0x22, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x50, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x1a, 0x7e, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x3f, 0x0a, 0x1c, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x19, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x73,
	0x22, 0xd9, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x13, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a,
	0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x22, 0xed, 0x05, 0x0a,
	0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6a, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f,
	0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a,
	0x8a, 0x04, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x4a, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x1a, 0x95, 0x01, 0x0a, 0x05, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a,
	0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x22, 0x99, 0x07, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50,
	0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x11, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x59, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x1a, 0xf7, 0x03, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x3b, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x65,
	0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x6f, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01,
	0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x1a, 0x59, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x22,
	0xc1, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0c, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x51, 0x0a, 0x12, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x48, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x64, 0x0a,
	0x20, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0x4d, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x22, 0x6d, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x50, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x22, 0xde, 0x0a, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x12, 0x8e, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x14, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x6f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x69, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x01, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x1a, 0xff, 0x04, 0x0a, 0x1d, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x88,
	0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x66, 0x65, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x4d, 0x0a, 0x0f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x48, 0x03, 0x52,
	0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x51, 0x0a, 0x11, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x48, 0x04,
	0x52, 0x0f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x82, 0x01, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x48, 0x05, 0x52, 0x11, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x65,
	0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x1a, 0xdc, 0x02, 0x0a, 0x13, 0x54, 0x69, 0x70,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x67, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x68, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x70, 0x73,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x00, 0x52, 0x11, 0x63, 0x61, 0x73, 0x68, 0x69, 0x65, 0x72, 0x54, 0x69, 0x70, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x69, 0x0a, 0x14, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x69, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x12, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x69, 0x70, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x70, 0x70,
	0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x0e, 0x74, 0x69, 0x70, 0x73, 0x41, 0x70, 0x70, 0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x69, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x70, 0x70,
	0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x70, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x32, 0xca, 0x16, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a,
	0x0a, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6a, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a,
	0x19, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x20, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x9a, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x12,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0f,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x88, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_payment_v2_payment_service_proto_rawDescOnce sync.Once
	file_moego_service_payment_v2_payment_service_proto_rawDescData = file_moego_service_payment_v2_payment_service_proto_rawDesc
)

func file_moego_service_payment_v2_payment_service_proto_rawDescGZIP() []byte {
	file_moego_service_payment_v2_payment_service_proto_rawDescOnce.Do(func() {
		file_moego_service_payment_v2_payment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_payment_v2_payment_service_proto_rawDescData)
	})
	return file_moego_service_payment_v2_payment_service_proto_rawDescData
}

var file_moego_service_payment_v2_payment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_moego_service_payment_v2_payment_service_proto_goTypes = []interface{}{
	(*ExportTransactionListRequest)(nil),                              // 0: moego.service.payment.v2.ExportTransactionListRequest
	(*ExportTransactionListResponse)(nil),                             // 1: moego.service.payment.v2.ExportTransactionListResponse
	(*GetPaymentViewRequest)(nil),                                     // 2: moego.service.payment.v2.GetPaymentViewRequest
	(*GetPaymentViewResponse)(nil),                                    // 3: moego.service.payment.v2.GetPaymentViewResponse
	(*GetTransactionRequest)(nil),                                     // 4: moego.service.payment.v2.GetTransactionRequest
	(*GetTransactionResponse)(nil),                                    // 5: moego.service.payment.v2.GetTransactionResponse
	(*GetChannelPaymentRequest)(nil),                                  // 6: moego.service.payment.v2.GetChannelPaymentRequest
	(*GetChannelPaymentResponse)(nil),                                 // 7: moego.service.payment.v2.GetChannelPaymentResponse
	(*GetPaymentVersionRequest)(nil),                                  // 8: moego.service.payment.v2.GetPaymentVersionRequest
	(*GetPaymentVersionResponse)(nil),                                 // 9: moego.service.payment.v2.GetPaymentVersionResponse
	(*GetChannelBalanceAccountInfoRequest)(nil),                       // 10: moego.service.payment.v2.GetChannelBalanceAccountInfoRequest
	(*GetChannelBalanceAccountInfoResponse)(nil),                      // 11: moego.service.payment.v2.GetChannelBalanceAccountInfoResponse
	(*CreatePaymentRequest)(nil),                                      // 12: moego.service.payment.v2.CreatePaymentRequest
	(*CreatePaymentResponse)(nil),                                     // 13: moego.service.payment.v2.CreatePaymentResponse
	(*CancelPaymentRequest)(nil),                                      // 14: moego.service.payment.v2.CancelPaymentRequest
	(*CancelPaymentResponse)(nil),                                     // 15: moego.service.payment.v2.CancelPaymentResponse
	(*GetPayDataRequest)(nil),                                         // 16: moego.service.payment.v2.GetPayDataRequest
	(*GetPayDataResponse)(nil),                                        // 17: moego.service.payment.v2.GetPayDataResponse
	(*PayPaymentRequest)(nil),                                         // 18: moego.service.payment.v2.PayPaymentRequest
	(*PayPaymentResponse)(nil),                                        // 19: moego.service.payment.v2.PayPaymentResponse
	(*SubmitActionDetailRequest)(nil),                                 // 20: moego.service.payment.v2.SubmitActionDetailRequest
	(*SubmitActionDetailResponse)(nil),                                // 21: moego.service.payment.v2.SubmitActionDetailResponse
	(*GetPaymentRequest)(nil),                                         // 22: moego.service.payment.v2.GetPaymentRequest
	(*GetPaymentResponse)(nil),                                        // 23: moego.service.payment.v2.GetPaymentResponse
	(*ListPaymentRequest)(nil),                                        // 24: moego.service.payment.v2.ListPaymentRequest
	(*ListPaymentResponse)(nil),                                       // 25: moego.service.payment.v2.ListPaymentResponse
	(*AddRecurringPaymentMethodRequest)(nil),                          // 26: moego.service.payment.v2.AddRecurringPaymentMethodRequest
	(*AddRecurringPaymentMethodResponse)(nil),                         // 27: moego.service.payment.v2.AddRecurringPaymentMethodResponse
	(*DeleteRecurringPaymentMethodRequest)(nil),                       // 28: moego.service.payment.v2.DeleteRecurringPaymentMethodRequest
	(*DeleteRecurringPaymentMethodResponse)(nil),                      // 29: moego.service.payment.v2.DeleteRecurringPaymentMethodResponse
	(*SetRecurringPaymentMethodPrimaryRequest)(nil),                   // 30: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryRequest
	(*SetRecurringPaymentMethodPrimaryResponse)(nil),                  // 31: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse
	(*ListRecurringPaymentMethodsRequest)(nil),                        // 32: moego.service.payment.v2.ListRecurringPaymentMethodsRequest
	(*ListRecurringPaymentMethodsResponse)(nil),                       // 33: moego.service.payment.v2.ListRecurringPaymentMethodsResponse
	(*CreateCombinedPaymentRequest)(nil),                              // 34: moego.service.payment.v2.CreateCombinedPaymentRequest
	(*CreateCombinedPaymentResponse)(nil),                             // 35: moego.service.payment.v2.CreateCombinedPaymentResponse
	(*ListTransactionRequest)(nil),                                    // 36: moego.service.payment.v2.ListTransactionRequest
	(*ListTransactionResponse)(nil),                                   // 37: moego.service.payment.v2.ListTransactionResponse
	(*ListPaymentByTransactionRequest)(nil),                           // 38: moego.service.payment.v2.ListPaymentByTransactionRequest
	(*ListPaymentByTransactionResponse)(nil),                          // 39: moego.service.payment.v2.ListPaymentByTransactionResponse
	(*GetPaymentSettingRequest)(nil),                                  // 40: moego.service.payment.v2.GetPaymentSettingRequest
	(*GetPaymentSettingResponse)(nil),                                 // 41: moego.service.payment.v2.GetPaymentSettingResponse
	(*UpdatePaymentSettingRequest)(nil),                               // 42: moego.service.payment.v2.UpdatePaymentSettingRequest
	(*UpdatePaymentSettingResponse)(nil),                              // 43: moego.service.payment.v2.UpdatePaymentSettingResponse
	(*GetPayDataResponse_AdyenData)(nil),                              // 44: moego.service.payment.v2.GetPayDataResponse.AdyenData
	(*ListPaymentRequest_Filter)(nil),                                 // 45: moego.service.payment.v2.ListPaymentRequest.Filter
	(*ListRecurringPaymentMethodsRequest_Filter)(nil),                 // 46: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter
	(*CreateCombinedPaymentRequest_CombinedItem)(nil),                 // 47: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem
	(*CreateCombinedPaymentRequest_CombinedItem_Extra)(nil),           // 48: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.Extra
	(*ListTransactionRequest_Filter)(nil),                             // 49: moego.service.payment.v2.ListTransactionRequest.Filter
	(*ListTransactionRequest_OrderBy)(nil),                            // 50: moego.service.payment.v2.ListTransactionRequest.OrderBy
	(*UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation)(nil), // 51: moego.service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation
	(*UpdatePaymentSettingRequest_TipsConfigOperation)(nil),           // 52: moego.service.payment.v2.UpdatePaymentSettingRequest.TipsConfigOperation
	(*v2.PaymentView)(nil),                                            // 53: moego.models.payment.v2.PaymentView
	(*v2.PaymentTransactionView)(nil),                                 // 54: moego.models.payment.v2.PaymentTransactionView
	(*v2.User)(nil),                                                   // 55: moego.models.payment.v2.User
	(*v2.StripeChannelPayment)(nil),                                   // 56: moego.models.payment.v2.StripeChannelPayment
	(v2.ChannelType)(0),                                               // 57: moego.models.payment.v2.ChannelType
	(v2.PaymentVersion)(0),                                            // 58: moego.models.payment.v2.PaymentVersion
	(*money.Money)(nil),                                               // 59: google.type.Money
	(v2.ExternalType)(0),                                              // 60: moego.models.payment.v2.ExternalType
	(v2.PaymentModel_PaymentType)(0),                                  // 61: moego.models.payment.v2.PaymentModel.PaymentType
	(*v2.PaymentModel)(nil),                                           // 62: moego.models.payment.v2.PaymentModel
	(v2.PaymentMethod_MethodType)(0),                                  // 63: moego.models.payment.v2.PaymentMethod.MethodType
	(*v2.PaymentMethod_Detail)(nil),                                   // 64: moego.models.payment.v2.PaymentMethod.Detail
	(*v2.ChannelPayment)(nil),                                         // 65: moego.models.payment.v2.ChannelPayment
	(*v21.PaginationRequest)(nil),                                     // 66: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                                    // 67: moego.utils.v2.PaginationResponse
	(*v2.RecurringPaymentMethodModel_Extra)(nil),                      // 68: moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	(*v2.RecurringPaymentMethodModel)(nil),                            // 69: moego.models.payment.v2.RecurringPaymentMethodModel
	(*v2.PaymentSetting)(nil),                                         // 70: moego.models.payment.v2.PaymentSetting
	(*interval.Interval)(nil),                                         // 71: google.type.Interval
	(v2.PaymentTransactionModel_TransactionStatus)(0),                 // 72: moego.models.payment.v2.PaymentTransactionModel.TransactionStatus
	(*v2.FeeRate)(nil),                                                // 73: moego.models.payment.v2.FeeRate
	(v2.PaymentSetting_ConvenienceFeeConfig_CalculationMethod)(0),     // 74: moego.models.payment.v2.PaymentSetting.ConvenienceFeeConfig.CalculationMethod
	(*v2.PaymentSetting_TipsConfig)(nil),                              // 75: moego.models.payment.v2.PaymentSetting.TipsConfig
}
var file_moego_service_payment_v2_payment_service_proto_depIdxs = []int32{
	49, // 0: moego.service.payment.v2.ExportTransactionListRequest.filter:type_name -> moego.service.payment.v2.ListTransactionRequest.Filter
	50, // 1: moego.service.payment.v2.ExportTransactionListRequest.order_bys:type_name -> moego.service.payment.v2.ListTransactionRequest.OrderBy
	53, // 2: moego.service.payment.v2.GetPaymentViewResponse.payment:type_name -> moego.models.payment.v2.PaymentView
	54, // 3: moego.service.payment.v2.GetTransactionResponse.transaction:type_name -> moego.models.payment.v2.PaymentTransactionView
	55, // 4: moego.service.payment.v2.GetChannelPaymentRequest.payee:type_name -> moego.models.payment.v2.User
	56, // 5: moego.service.payment.v2.GetChannelPaymentResponse.stripe_channel_payment:type_name -> moego.models.payment.v2.StripeChannelPayment
	57, // 6: moego.service.payment.v2.GetChannelPaymentResponse.channel_type:type_name -> moego.models.payment.v2.ChannelType
	55, // 7: moego.service.payment.v2.GetPaymentVersionRequest.payee:type_name -> moego.models.payment.v2.User
	58, // 8: moego.service.payment.v2.GetPaymentVersionResponse.payment_version:type_name -> moego.models.payment.v2.PaymentVersion
	57, // 9: moego.service.payment.v2.GetPaymentVersionResponse.channel_type:type_name -> moego.models.payment.v2.ChannelType
	55, // 10: moego.service.payment.v2.GetChannelBalanceAccountInfoRequest.user:type_name -> moego.models.payment.v2.User
	57, // 11: moego.service.payment.v2.GetChannelBalanceAccountInfoRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	59, // 12: moego.service.payment.v2.GetChannelBalanceAccountInfoResponse.available_balance:type_name -> google.type.Money
	59, // 13: moego.service.payment.v2.GetChannelBalanceAccountInfoResponse.pending_balance:type_name -> google.type.Money
	60, // 14: moego.service.payment.v2.CreatePaymentRequest.external_type:type_name -> moego.models.payment.v2.ExternalType
	55, // 15: moego.service.payment.v2.CreatePaymentRequest.payer:type_name -> moego.models.payment.v2.User
	55, // 16: moego.service.payment.v2.CreatePaymentRequest.payee:type_name -> moego.models.payment.v2.User
	59, // 17: moego.service.payment.v2.CreatePaymentRequest.amount:type_name -> google.type.Money
	61, // 18: moego.service.payment.v2.CreatePaymentRequest.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	62, // 19: moego.service.payment.v2.CreatePaymentResponse.payment:type_name -> moego.models.payment.v2.PaymentModel
	57, // 20: moego.service.payment.v2.GetPayDataRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	57, // 21: moego.service.payment.v2.GetPayDataResponse.channel_type:type_name -> moego.models.payment.v2.ChannelType
	44, // 22: moego.service.payment.v2.GetPayDataResponse.adyen_data:type_name -> moego.service.payment.v2.GetPayDataResponse.AdyenData
	63, // 23: moego.service.payment.v2.PayPaymentRequest.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	64, // 24: moego.service.payment.v2.PayPaymentRequest.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	65, // 25: moego.service.payment.v2.PayPaymentResponse.channel_payment:type_name -> moego.models.payment.v2.ChannelPayment
	57, // 26: moego.service.payment.v2.SubmitActionDetailRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	62, // 27: moego.service.payment.v2.GetPaymentResponse.payment:type_name -> moego.models.payment.v2.PaymentModel
	66, // 28: moego.service.payment.v2.ListPaymentRequest.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	45, // 29: moego.service.payment.v2.ListPaymentRequest.filter:type_name -> moego.service.payment.v2.ListPaymentRequest.Filter
	53, // 30: moego.service.payment.v2.ListPaymentResponse.payment_views:type_name -> moego.models.payment.v2.PaymentView
	67, // 31: moego.service.payment.v2.ListPaymentResponse.pagination_request:type_name -> moego.utils.v2.PaginationResponse
	55, // 32: moego.service.payment.v2.AddRecurringPaymentMethodRequest.payee:type_name -> moego.models.payment.v2.User
	57, // 33: moego.service.payment.v2.AddRecurringPaymentMethodRequest.channel_type:type_name -> moego.models.payment.v2.ChannelType
	63, // 34: moego.service.payment.v2.AddRecurringPaymentMethodRequest.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	64, // 35: moego.service.payment.v2.AddRecurringPaymentMethodRequest.detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	68, // 36: moego.service.payment.v2.AddRecurringPaymentMethodRequest.extra:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel.Extra
	69, // 37: moego.service.payment.v2.AddRecurringPaymentMethodResponse.recurring_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	69, // 38: moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse.recurring_payment_method:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	66, // 39: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	46, // 40: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.filter:type_name -> moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter
	67, // 41: moego.service.payment.v2.ListRecurringPaymentMethodsResponse.pagination_response:type_name -> moego.utils.v2.PaginationResponse
	69, // 42: moego.service.payment.v2.ListRecurringPaymentMethodsResponse.payment_methods:type_name -> moego.models.payment.v2.RecurringPaymentMethodModel
	47, // 43: moego.service.payment.v2.CreateCombinedPaymentRequest.combined_items:type_name -> moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem
	61, // 44: moego.service.payment.v2.CreateCombinedPaymentRequest.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	62, // 45: moego.service.payment.v2.CreateCombinedPaymentResponse.payment_models:type_name -> moego.models.payment.v2.PaymentModel
	66, // 46: moego.service.payment.v2.ListTransactionRequest.pagination_request:type_name -> moego.utils.v2.PaginationRequest
	49, // 47: moego.service.payment.v2.ListTransactionRequest.filter:type_name -> moego.service.payment.v2.ListTransactionRequest.Filter
	50, // 48: moego.service.payment.v2.ListTransactionRequest.order_bys:type_name -> moego.service.payment.v2.ListTransactionRequest.OrderBy
	54, // 49: moego.service.payment.v2.ListTransactionResponse.transactions:type_name -> moego.models.payment.v2.PaymentTransactionView
	67, // 50: moego.service.payment.v2.ListTransactionResponse.pagination_request:type_name -> moego.utils.v2.PaginationResponse
	53, // 51: moego.service.payment.v2.ListPaymentByTransactionResponse.payments:type_name -> moego.models.payment.v2.PaymentView
	55, // 52: moego.service.payment.v2.GetPaymentSettingRequest.user:type_name -> moego.models.payment.v2.User
	70, // 53: moego.service.payment.v2.GetPaymentSettingResponse.payment_setting:type_name -> moego.models.payment.v2.PaymentSetting
	55, // 54: moego.service.payment.v2.UpdatePaymentSettingRequest.user:type_name -> moego.models.payment.v2.User
	51, // 55: moego.service.payment.v2.UpdatePaymentSettingRequest.convenience_fee_config:type_name -> moego.service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation
	52, // 56: moego.service.payment.v2.UpdatePaymentSettingRequest.tips_config:type_name -> moego.service.payment.v2.UpdatePaymentSettingRequest.TipsConfigOperation
	70, // 57: moego.service.payment.v2.UpdatePaymentSettingResponse.payment_setting:type_name -> moego.models.payment.v2.PaymentSetting
	55, // 58: moego.service.payment.v2.ListPaymentRequest.Filter.payers:type_name -> moego.models.payment.v2.User
	55, // 59: moego.service.payment.v2.ListPaymentRequest.Filter.payees:type_name -> moego.models.payment.v2.User
	71, // 60: moego.service.payment.v2.ListPaymentRequest.Filter.time_range:type_name -> google.type.Interval
	55, // 61: moego.service.payment.v2.ListRecurringPaymentMethodsRequest.Filter.users:type_name -> moego.models.payment.v2.User
	60, // 62: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.external_type:type_name -> moego.models.payment.v2.ExternalType
	55, // 63: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.payer:type_name -> moego.models.payment.v2.User
	55, // 64: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.payee:type_name -> moego.models.payment.v2.User
	59, // 65: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.amount:type_name -> google.type.Money
	48, // 66: moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.extra:type_name -> moego.service.payment.v2.CreateCombinedPaymentRequest.CombinedItem.Extra
	57, // 67: moego.service.payment.v2.ListTransactionRequest.Filter.channel_types:type_name -> moego.models.payment.v2.ChannelType
	71, // 68: moego.service.payment.v2.ListTransactionRequest.Filter.interval:type_name -> google.type.Interval
	63, // 69: moego.service.payment.v2.ListTransactionRequest.Filter.method_types:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	72, // 70: moego.service.payment.v2.ListTransactionRequest.Filter.statuses:type_name -> moego.models.payment.v2.PaymentTransactionModel.TransactionStatus
	73, // 71: moego.service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation.online_fee_rate:type_name -> moego.models.payment.v2.FeeRate
	73, // 72: moego.service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation.terminal_fee_rate:type_name -> moego.models.payment.v2.FeeRate
	74, // 73: moego.service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation.calculation_method:type_name -> moego.models.payment.v2.PaymentSetting.ConvenienceFeeConfig.CalculationMethod
	75, // 74: moego.service.payment.v2.UpdatePaymentSettingRequest.TipsConfigOperation.cashier_tips_option:type_name -> moego.models.payment.v2.PaymentSetting.TipsConfig
	75, // 75: moego.service.payment.v2.UpdatePaymentSettingRequest.TipsConfigOperation.terminal_tips_option:type_name -> moego.models.payment.v2.PaymentSetting.TipsConfig
	8,  // 76: moego.service.payment.v2.PaymentService.GetPaymentVersion:input_type -> moego.service.payment.v2.GetPaymentVersionRequest
	10, // 77: moego.service.payment.v2.PaymentService.GetChannelBalanceAccountInfo:input_type -> moego.service.payment.v2.GetChannelBalanceAccountInfoRequest
	12, // 78: moego.service.payment.v2.PaymentService.CreatePayment:input_type -> moego.service.payment.v2.CreatePaymentRequest
	34, // 79: moego.service.payment.v2.PaymentService.CreateCombinedPayment:input_type -> moego.service.payment.v2.CreateCombinedPaymentRequest
	14, // 80: moego.service.payment.v2.PaymentService.CancelPayment:input_type -> moego.service.payment.v2.CancelPaymentRequest
	16, // 81: moego.service.payment.v2.PaymentService.GetPayData:input_type -> moego.service.payment.v2.GetPayDataRequest
	18, // 82: moego.service.payment.v2.PaymentService.PayPayment:input_type -> moego.service.payment.v2.PayPaymentRequest
	20, // 83: moego.service.payment.v2.PaymentService.SubmitActionDetail:input_type -> moego.service.payment.v2.SubmitActionDetailRequest
	22, // 84: moego.service.payment.v2.PaymentService.GetPayment:input_type -> moego.service.payment.v2.GetPaymentRequest
	24, // 85: moego.service.payment.v2.PaymentService.ListPayment:input_type -> moego.service.payment.v2.ListPaymentRequest
	26, // 86: moego.service.payment.v2.PaymentService.AddRecurringPaymentMethod:input_type -> moego.service.payment.v2.AddRecurringPaymentMethodRequest
	28, // 87: moego.service.payment.v2.PaymentService.DeleteRecurringPaymentMethod:input_type -> moego.service.payment.v2.DeleteRecurringPaymentMethodRequest
	30, // 88: moego.service.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:input_type -> moego.service.payment.v2.SetRecurringPaymentMethodPrimaryRequest
	32, // 89: moego.service.payment.v2.PaymentService.ListRecurringPaymentMethods:input_type -> moego.service.payment.v2.ListRecurringPaymentMethodsRequest
	36, // 90: moego.service.payment.v2.PaymentService.ListTransaction:input_type -> moego.service.payment.v2.ListTransactionRequest
	6,  // 91: moego.service.payment.v2.PaymentService.GetChannelPayment:input_type -> moego.service.payment.v2.GetChannelPaymentRequest
	38, // 92: moego.service.payment.v2.PaymentService.ListPaymentByTransaction:input_type -> moego.service.payment.v2.ListPaymentByTransactionRequest
	4,  // 93: moego.service.payment.v2.PaymentService.GetTransaction:input_type -> moego.service.payment.v2.GetTransactionRequest
	2,  // 94: moego.service.payment.v2.PaymentService.GetPaymentView:input_type -> moego.service.payment.v2.GetPaymentViewRequest
	0,  // 95: moego.service.payment.v2.PaymentService.ExportTransactionList:input_type -> moego.service.payment.v2.ExportTransactionListRequest
	40, // 96: moego.service.payment.v2.PaymentService.GetPaymentSetting:input_type -> moego.service.payment.v2.GetPaymentSettingRequest
	42, // 97: moego.service.payment.v2.PaymentService.UpdatePaymentSetting:input_type -> moego.service.payment.v2.UpdatePaymentSettingRequest
	9,  // 98: moego.service.payment.v2.PaymentService.GetPaymentVersion:output_type -> moego.service.payment.v2.GetPaymentVersionResponse
	11, // 99: moego.service.payment.v2.PaymentService.GetChannelBalanceAccountInfo:output_type -> moego.service.payment.v2.GetChannelBalanceAccountInfoResponse
	13, // 100: moego.service.payment.v2.PaymentService.CreatePayment:output_type -> moego.service.payment.v2.CreatePaymentResponse
	35, // 101: moego.service.payment.v2.PaymentService.CreateCombinedPayment:output_type -> moego.service.payment.v2.CreateCombinedPaymentResponse
	15, // 102: moego.service.payment.v2.PaymentService.CancelPayment:output_type -> moego.service.payment.v2.CancelPaymentResponse
	17, // 103: moego.service.payment.v2.PaymentService.GetPayData:output_type -> moego.service.payment.v2.GetPayDataResponse
	19, // 104: moego.service.payment.v2.PaymentService.PayPayment:output_type -> moego.service.payment.v2.PayPaymentResponse
	21, // 105: moego.service.payment.v2.PaymentService.SubmitActionDetail:output_type -> moego.service.payment.v2.SubmitActionDetailResponse
	23, // 106: moego.service.payment.v2.PaymentService.GetPayment:output_type -> moego.service.payment.v2.GetPaymentResponse
	25, // 107: moego.service.payment.v2.PaymentService.ListPayment:output_type -> moego.service.payment.v2.ListPaymentResponse
	27, // 108: moego.service.payment.v2.PaymentService.AddRecurringPaymentMethod:output_type -> moego.service.payment.v2.AddRecurringPaymentMethodResponse
	29, // 109: moego.service.payment.v2.PaymentService.DeleteRecurringPaymentMethod:output_type -> moego.service.payment.v2.DeleteRecurringPaymentMethodResponse
	31, // 110: moego.service.payment.v2.PaymentService.SetRecurringPaymentMethodPrimary:output_type -> moego.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse
	33, // 111: moego.service.payment.v2.PaymentService.ListRecurringPaymentMethods:output_type -> moego.service.payment.v2.ListRecurringPaymentMethodsResponse
	37, // 112: moego.service.payment.v2.PaymentService.ListTransaction:output_type -> moego.service.payment.v2.ListTransactionResponse
	7,  // 113: moego.service.payment.v2.PaymentService.GetChannelPayment:output_type -> moego.service.payment.v2.GetChannelPaymentResponse
	39, // 114: moego.service.payment.v2.PaymentService.ListPaymentByTransaction:output_type -> moego.service.payment.v2.ListPaymentByTransactionResponse
	5,  // 115: moego.service.payment.v2.PaymentService.GetTransaction:output_type -> moego.service.payment.v2.GetTransactionResponse
	3,  // 116: moego.service.payment.v2.PaymentService.GetPaymentView:output_type -> moego.service.payment.v2.GetPaymentViewResponse
	1,  // 117: moego.service.payment.v2.PaymentService.ExportTransactionList:output_type -> moego.service.payment.v2.ExportTransactionListResponse
	41, // 118: moego.service.payment.v2.PaymentService.GetPaymentSetting:output_type -> moego.service.payment.v2.GetPaymentSettingResponse
	43, // 119: moego.service.payment.v2.PaymentService.UpdatePaymentSetting:output_type -> moego.service.payment.v2.UpdatePaymentSettingResponse
	98, // [98:120] is the sub-list for method output_type
	76, // [76:98] is the sub-list for method input_type
	76, // [76:76] is the sub-list for extension type_name
	76, // [76:76] is the sub-list for extension extendee
	0,  // [0:76] is the sub-list for field type_name
}

func init() { file_moego_service_payment_v2_payment_service_proto_init() }
func file_moego_service_payment_v2_payment_service_proto_init() {
	if File_moego_service_payment_v2_payment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_payment_v2_payment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportTransactionListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportTransactionListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentViewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentVersionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelBalanceAccountInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelBalanceAccountInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitActionDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecurringPaymentMethodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecurringPaymentMethodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecurringPaymentMethodPrimaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCombinedPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCombinedPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentByTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentByTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePaymentSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePaymentSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayDataResponse_AdyenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRecurringPaymentMethodsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCombinedPaymentRequest_CombinedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCombinedPaymentRequest_CombinedItem_Extra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTransactionRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTransactionRequest_OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePaymentSettingRequest_ConvenienceFeeConfigOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_payment_v2_payment_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePaymentSettingRequest_TipsConfigOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetChannelPaymentResponse_StripeChannelPayment)(nil),
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*GetPayDataResponse_AdyenData_)(nil),
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[27].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[42].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[45].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[49].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[50].OneofWrappers = []interface{}{
		(*ListTransactionRequest_OrderBy_CreatedAt)(nil),
		(*ListTransactionRequest_OrderBy_Id)(nil),
	}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[51].OneofWrappers = []interface{}{}
	file_moego_service_payment_v2_payment_service_proto_msgTypes[52].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_payment_v2_payment_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_payment_v2_payment_service_proto_goTypes,
		DependencyIndexes: file_moego_service_payment_v2_payment_service_proto_depIdxs,
		MessageInfos:      file_moego_service_payment_v2_payment_service_proto_msgTypes,
	}.Build()
	File_moego_service_payment_v2_payment_service_proto = out.File
	file_moego_service_payment_v2_payment_service_proto_rawDesc = nil
	file_moego_service_payment_v2_payment_service_proto_goTypes = nil
	file_moego_service_payment_v2_payment_service_proto_depIdxs = nil
}
