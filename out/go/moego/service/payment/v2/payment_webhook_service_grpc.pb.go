// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/payment/v2/payment_webhook_service.proto

package paymentsvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentWebhookServiceClient is the client API for PaymentWebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentWebhookServiceClient interface {
	// Handle webhook event.
	// (-- api-linter: core::0136::response-message-name=disabled --)
	HandleWebhookEvent(ctx context.Context, in *v1.HandleWebhookEventRequest, opts ...grpc.CallOption) (*v1.HandleWebhookEventResponse, error)
}

type paymentWebhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentWebhookServiceClient(cc grpc.ClientConnInterface) PaymentWebhookServiceClient {
	return &paymentWebhookServiceClient{cc}
}

func (c *paymentWebhookServiceClient) HandleWebhookEvent(ctx context.Context, in *v1.HandleWebhookEventRequest, opts ...grpc.CallOption) (*v1.HandleWebhookEventResponse, error) {
	out := new(v1.HandleWebhookEventResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentWebhookService/HandleWebhookEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentWebhookServiceServer is the server API for PaymentWebhookService service.
// All implementations must embed UnimplementedPaymentWebhookServiceServer
// for forward compatibility
type PaymentWebhookServiceServer interface {
	// Handle webhook event.
	// (-- api-linter: core::0136::response-message-name=disabled --)
	HandleWebhookEvent(context.Context, *v1.HandleWebhookEventRequest) (*v1.HandleWebhookEventResponse, error)
	mustEmbedUnimplementedPaymentWebhookServiceServer()
}

// UnimplementedPaymentWebhookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentWebhookServiceServer struct {
}

func (UnimplementedPaymentWebhookServiceServer) HandleWebhookEvent(context.Context, *v1.HandleWebhookEventRequest) (*v1.HandleWebhookEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleWebhookEvent not implemented")
}
func (UnimplementedPaymentWebhookServiceServer) mustEmbedUnimplementedPaymentWebhookServiceServer() {}

// UnsafePaymentWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentWebhookServiceServer will
// result in compilation errors.
type UnsafePaymentWebhookServiceServer interface {
	mustEmbedUnimplementedPaymentWebhookServiceServer()
}

func RegisterPaymentWebhookServiceServer(s grpc.ServiceRegistrar, srv PaymentWebhookServiceServer) {
	s.RegisterService(&PaymentWebhookService_ServiceDesc, srv)
}

func _PaymentWebhookService_HandleWebhookEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.HandleWebhookEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentWebhookServiceServer).HandleWebhookEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentWebhookService/HandleWebhookEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentWebhookServiceServer).HandleWebhookEvent(ctx, req.(*v1.HandleWebhookEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentWebhookService_ServiceDesc is the grpc.ServiceDesc for PaymentWebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentWebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.payment.v2.PaymentWebhookService",
	HandlerType: (*PaymentWebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleWebhookEvent",
			Handler:    _PaymentWebhookService_HandleWebhookEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/payment/v2/payment_webhook_service.proto",
}
