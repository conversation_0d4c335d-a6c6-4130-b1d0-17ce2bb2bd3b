// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/payment/v2/payment_terminal_service.proto

package paymentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentTerminalServiceClient is the client API for PaymentTerminalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentTerminalServiceClient interface {
	// GetReaderProcessingPayment 获取 reader
	GetTerminal(ctx context.Context, in *GetTerminalRequest, opts ...grpc.CallOption) (*GetTerminalResponse, error)
	// ListTerminal 获取支付终端列表
	ListTerminals(ctx context.Context, in *ListTerminalsRequest, opts ...grpc.CallOption) (*ListTerminalsResponse, error)
}

type paymentTerminalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentTerminalServiceClient(cc grpc.ClientConnInterface) PaymentTerminalServiceClient {
	return &paymentTerminalServiceClient{cc}
}

func (c *paymentTerminalServiceClient) GetTerminal(ctx context.Context, in *GetTerminalRequest, opts ...grpc.CallOption) (*GetTerminalResponse, error) {
	out := new(GetTerminalResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentTerminalService/GetTerminal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTerminalServiceClient) ListTerminals(ctx context.Context, in *ListTerminalsRequest, opts ...grpc.CallOption) (*ListTerminalsResponse, error) {
	out := new(ListTerminalsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentTerminalService/ListTerminals", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentTerminalServiceServer is the server API for PaymentTerminalService service.
// All implementations must embed UnimplementedPaymentTerminalServiceServer
// for forward compatibility
type PaymentTerminalServiceServer interface {
	// GetReaderProcessingPayment 获取 reader
	GetTerminal(context.Context, *GetTerminalRequest) (*GetTerminalResponse, error)
	// ListTerminal 获取支付终端列表
	ListTerminals(context.Context, *ListTerminalsRequest) (*ListTerminalsResponse, error)
	mustEmbedUnimplementedPaymentTerminalServiceServer()
}

// UnimplementedPaymentTerminalServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentTerminalServiceServer struct {
}

func (UnimplementedPaymentTerminalServiceServer) GetTerminal(context.Context, *GetTerminalRequest) (*GetTerminalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminal not implemented")
}
func (UnimplementedPaymentTerminalServiceServer) ListTerminals(context.Context, *ListTerminalsRequest) (*ListTerminalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTerminals not implemented")
}
func (UnimplementedPaymentTerminalServiceServer) mustEmbedUnimplementedPaymentTerminalServiceServer() {
}

// UnsafePaymentTerminalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentTerminalServiceServer will
// result in compilation errors.
type UnsafePaymentTerminalServiceServer interface {
	mustEmbedUnimplementedPaymentTerminalServiceServer()
}

func RegisterPaymentTerminalServiceServer(s grpc.ServiceRegistrar, srv PaymentTerminalServiceServer) {
	s.RegisterService(&PaymentTerminalService_ServiceDesc, srv)
}

func _PaymentTerminalService_GetTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTerminalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTerminalServiceServer).GetTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentTerminalService/GetTerminal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTerminalServiceServer).GetTerminal(ctx, req.(*GetTerminalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTerminalService_ListTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTerminalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTerminalServiceServer).ListTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentTerminalService/ListTerminals",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTerminalServiceServer).ListTerminals(ctx, req.(*ListTerminalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentTerminalService_ServiceDesc is the grpc.ServiceDesc for PaymentTerminalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentTerminalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.payment.v2.PaymentTerminalService",
	HandlerType: (*PaymentTerminalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTerminal",
			Handler:    _PaymentTerminalService_GetTerminal_Handler,
		},
		{
			MethodName: "ListTerminals",
			Handler:    _PaymentTerminalService_ListTerminals_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/payment/v2/payment_terminal_service.proto",
}
