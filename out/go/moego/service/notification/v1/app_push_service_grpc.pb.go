// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/notification/v1/app_push_service.proto

package notificationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppPushServiceClient is the client API for AppPushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppPushServiceClient interface {
	// Refresh device
	RefreshDevice(ctx context.Context, in *RefreshDeviceRequest, opts ...grpc.CallOption) (*RefreshDeviceResponse, error)
	// Create app push
	CreateAppPush(ctx context.Context, in *CreateAppPushRequest, opts ...grpc.CallOption) (*CreateAppPushResponse, error)
	// Batch create app push
	BatchCreateAppPush(ctx context.Context, in *BatchCreateAppPushRequest, opts ...grpc.CallOption) (*BatchCreateAppPushResponse, error)
	// refresh branded app notification config
	RefreshBrandedAppNotificationConfig(ctx context.Context, in *RefreshBrandedAppNotificationConfigRequest, opts ...grpc.CallOption) (*RefreshBrandedAppNotificationConfigResponse, error)
	// List push token
	ListPushToken(ctx context.Context, in *ListPushTokenRequest, opts ...grpc.CallOption) (*ListPushTokenResponse, error)
}

type appPushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppPushServiceClient(cc grpc.ClientConnInterface) AppPushServiceClient {
	return &appPushServiceClient{cc}
}

func (c *appPushServiceClient) RefreshDevice(ctx context.Context, in *RefreshDeviceRequest, opts ...grpc.CallOption) (*RefreshDeviceResponse, error) {
	out := new(RefreshDeviceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.AppPushService/RefreshDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appPushServiceClient) CreateAppPush(ctx context.Context, in *CreateAppPushRequest, opts ...grpc.CallOption) (*CreateAppPushResponse, error) {
	out := new(CreateAppPushResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.AppPushService/CreateAppPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appPushServiceClient) BatchCreateAppPush(ctx context.Context, in *BatchCreateAppPushRequest, opts ...grpc.CallOption) (*BatchCreateAppPushResponse, error) {
	out := new(BatchCreateAppPushResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.AppPushService/BatchCreateAppPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appPushServiceClient) RefreshBrandedAppNotificationConfig(ctx context.Context, in *RefreshBrandedAppNotificationConfigRequest, opts ...grpc.CallOption) (*RefreshBrandedAppNotificationConfigResponse, error) {
	out := new(RefreshBrandedAppNotificationConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.AppPushService/RefreshBrandedAppNotificationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appPushServiceClient) ListPushToken(ctx context.Context, in *ListPushTokenRequest, opts ...grpc.CallOption) (*ListPushTokenResponse, error) {
	out := new(ListPushTokenResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.AppPushService/ListPushToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppPushServiceServer is the server API for AppPushService service.
// All implementations must embed UnimplementedAppPushServiceServer
// for forward compatibility
type AppPushServiceServer interface {
	// Refresh device
	RefreshDevice(context.Context, *RefreshDeviceRequest) (*RefreshDeviceResponse, error)
	// Create app push
	CreateAppPush(context.Context, *CreateAppPushRequest) (*CreateAppPushResponse, error)
	// Batch create app push
	BatchCreateAppPush(context.Context, *BatchCreateAppPushRequest) (*BatchCreateAppPushResponse, error)
	// refresh branded app notification config
	RefreshBrandedAppNotificationConfig(context.Context, *RefreshBrandedAppNotificationConfigRequest) (*RefreshBrandedAppNotificationConfigResponse, error)
	// List push token
	ListPushToken(context.Context, *ListPushTokenRequest) (*ListPushTokenResponse, error)
	mustEmbedUnimplementedAppPushServiceServer()
}

// UnimplementedAppPushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppPushServiceServer struct {
}

func (UnimplementedAppPushServiceServer) RefreshDevice(context.Context, *RefreshDeviceRequest) (*RefreshDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDevice not implemented")
}
func (UnimplementedAppPushServiceServer) CreateAppPush(context.Context, *CreateAppPushRequest) (*CreateAppPushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppPush not implemented")
}
func (UnimplementedAppPushServiceServer) BatchCreateAppPush(context.Context, *BatchCreateAppPushRequest) (*BatchCreateAppPushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateAppPush not implemented")
}
func (UnimplementedAppPushServiceServer) RefreshBrandedAppNotificationConfig(context.Context, *RefreshBrandedAppNotificationConfigRequest) (*RefreshBrandedAppNotificationConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshBrandedAppNotificationConfig not implemented")
}
func (UnimplementedAppPushServiceServer) ListPushToken(context.Context, *ListPushTokenRequest) (*ListPushTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPushToken not implemented")
}
func (UnimplementedAppPushServiceServer) mustEmbedUnimplementedAppPushServiceServer() {}

// UnsafeAppPushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppPushServiceServer will
// result in compilation errors.
type UnsafeAppPushServiceServer interface {
	mustEmbedUnimplementedAppPushServiceServer()
}

func RegisterAppPushServiceServer(s grpc.ServiceRegistrar, srv AppPushServiceServer) {
	s.RegisterService(&AppPushService_ServiceDesc, srv)
}

func _AppPushService_RefreshDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).RefreshDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.AppPushService/RefreshDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).RefreshDevice(ctx, req.(*RefreshDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppPushService_CreateAppPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppPushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).CreateAppPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.AppPushService/CreateAppPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).CreateAppPush(ctx, req.(*CreateAppPushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppPushService_BatchCreateAppPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateAppPushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).BatchCreateAppPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.AppPushService/BatchCreateAppPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).BatchCreateAppPush(ctx, req.(*BatchCreateAppPushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppPushService_RefreshBrandedAppNotificationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshBrandedAppNotificationConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).RefreshBrandedAppNotificationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.AppPushService/RefreshBrandedAppNotificationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).RefreshBrandedAppNotificationConfig(ctx, req.(*RefreshBrandedAppNotificationConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppPushService_ListPushToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPushTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).ListPushToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.AppPushService/ListPushToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).ListPushToken(ctx, req.(*ListPushTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AppPushService_ServiceDesc is the grpc.ServiceDesc for AppPushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppPushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.notification.v1.AppPushService",
	HandlerType: (*AppPushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefreshDevice",
			Handler:    _AppPushService_RefreshDevice_Handler,
		},
		{
			MethodName: "CreateAppPush",
			Handler:    _AppPushService_CreateAppPush_Handler,
		},
		{
			MethodName: "BatchCreateAppPush",
			Handler:    _AppPushService_BatchCreateAppPush_Handler,
		},
		{
			MethodName: "RefreshBrandedAppNotificationConfig",
			Handler:    _AppPushService_RefreshBrandedAppNotificationConfig_Handler,
		},
		{
			MethodName: "ListPushToken",
			Handler:    _AppPushService_ListPushToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/notification/v1/app_push_service.proto",
}
