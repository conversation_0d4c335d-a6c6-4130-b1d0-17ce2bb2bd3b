// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/agreement/v1/agreement_service.proto

package agreementsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// check agreement input
type CheckAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CheckAgreementRequest) Reset() {
	*x = CheckAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAgreementRequest) ProtoMessage() {}

func (x *CheckAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAgreementRequest.ProtoReflect.Descriptor instead.
func (*CheckAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{0}
}

func (x *CheckAgreementRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CheckAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get agreement input
type GetAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id, if set, will check the company id instead of business id
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetAgreementRequest) Reset() {
	*x = GetAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementRequest) ProtoMessage() {}

func (x *GetAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementRequest.ProtoReflect.Descriptor instead.
func (*GetAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAgreementRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAgreementRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// delete agreement request
type DeleteAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *DeleteAgreementRequest) Reset() {
	*x = DeleteAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgreementRequest) ProtoMessage() {}

func (x *DeleteAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgreementRequest.ProtoReflect.Descriptor instead.
func (*DeleteAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteAgreementRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// DeleteAgreementResponse
type DeleteAgreementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of delete
	Number int32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *DeleteAgreementResponse) Reset() {
	*x = DeleteAgreementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgreementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgreementResponse) ProtoMessage() {}

func (x *DeleteAgreementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgreementResponse.ProtoReflect.Descriptor instead.
func (*DeleteAgreementResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteAgreementResponse) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

// CheckAgreementResponse
type CheckAgreementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result of check
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// message
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CheckAgreementResponse) Reset() {
	*x = CheckAgreementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAgreementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAgreementResponse) ProtoMessage() {}

func (x *CheckAgreementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAgreementResponse.ProtoReflect.Descriptor instead.
func (*CheckAgreementResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckAgreementResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *CheckAgreementResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// GetAgreementListRequest
type GetAgreementListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// agreement id list
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// status: normal, deleted
	Status *v1.Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.utils.v1.Status,oneof" json:"status,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
}

func (x *GetAgreementListRequest) Reset() {
	*x = GetAgreementListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListRequest) ProtoMessage() {}

func (x *GetAgreementListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListRequest.ProtoReflect.Descriptor instead.
func (*GetAgreementListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAgreementListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAgreementListRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetAgreementListRequest) GetStatus() v1.Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Status(0)
}

func (x *GetAgreementListRequest) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

// InitAgreementRequest
type InitAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business name
	BusinessName *string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3,oneof" json:"business_name,omitempty"`
	// creator id
	CreatorId *int64 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3,oneof" json:"creator_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *InitAgreementRequest) Reset() {
	*x = InitAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitAgreementRequest) ProtoMessage() {}

func (x *InitAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitAgreementRequest.ProtoReflect.Descriptor instead.
func (*InitAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{6}
}

func (x *InitAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *InitAgreementRequest) GetBusinessName() string {
	if x != nil && x.BusinessName != nil {
		return *x.BusinessName
	}
	return ""
}

func (x *InitAgreementRequest) GetCreatorId() int64 {
	if x != nil && x.CreatorId != nil {
		return *x.CreatorId
	}
	return 0
}

func (x *InitAgreementRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// AddAgreementRequest
type AddAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// creator id
	CreatorId int64 `protobuf:"varint,2,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// signed policy, see definition in SignedPolicy
	SignedPolicy v11.SignedPolicy `protobuf:"varint,3,opt,name=signed_policy,json=signedPolicy,proto3,enum=moego.models.agreement.v1.SignedPolicy" json:"signed_policy,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3" json:"service_types,omitempty"`
	// agreement title
	AgreementTitle *string `protobuf:"bytes,5,opt,name=agreement_title,json=agreementTitle,proto3,oneof" json:"agreement_title,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,6,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// template for send sms
	SmsTemplate *string `protobuf:"bytes,7,opt,name=sms_template,json=smsTemplate,proto3,oneof" json:"sms_template,omitempty"`
	// email template title
	EmailTemplateTitle *string `protobuf:"bytes,8,opt,name=email_template_title,json=emailTemplateTitle,proto3,oneof" json:"email_template_title,omitempty"`
	// email template body
	EmailTemplateBody *string `protobuf:"bytes,9,opt,name=email_template_body,json=emailTemplateBody,proto3,oneof" json:"email_template_body,omitempty"`
	// business name
	BusinessName *string `protobuf:"bytes,10,opt,name=business_name,json=businessName,proto3,oneof" json:"business_name,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,11,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *AddAgreementRequest) Reset() {
	*x = AddAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAgreementRequest) ProtoMessage() {}

func (x *AddAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAgreementRequest.ProtoReflect.Descriptor instead.
func (*AddAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{7}
}

func (x *AddAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AddAgreementRequest) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *AddAgreementRequest) GetSignedPolicy() v11.SignedPolicy {
	if x != nil {
		return x.SignedPolicy
	}
	return v11.SignedPolicy(0)
}

func (x *AddAgreementRequest) GetServiceTypes() int32 {
	if x != nil {
		return x.ServiceTypes
	}
	return 0
}

func (x *AddAgreementRequest) GetAgreementTitle() string {
	if x != nil && x.AgreementTitle != nil {
		return *x.AgreementTitle
	}
	return ""
}

func (x *AddAgreementRequest) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *AddAgreementRequest) GetSmsTemplate() string {
	if x != nil && x.SmsTemplate != nil {
		return *x.SmsTemplate
	}
	return ""
}

func (x *AddAgreementRequest) GetEmailTemplateTitle() string {
	if x != nil && x.EmailTemplateTitle != nil {
		return *x.EmailTemplateTitle
	}
	return ""
}

func (x *AddAgreementRequest) GetEmailTemplateBody() string {
	if x != nil && x.EmailTemplateBody != nil {
		return *x.EmailTemplateBody
	}
	return ""
}

func (x *AddAgreementRequest) GetBusinessName() string {
	if x != nil && x.BusinessName != nil {
		return *x.BusinessName
	}
	return ""
}

func (x *AddAgreementRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateAgreementRequest
type UpdateAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// signed policy, see definition in SignedPolicy
	SignedPolicy *v11.SignedPolicy `protobuf:"varint,3,opt,name=signed_policy,json=signedPolicy,proto3,enum=moego.models.agreement.v1.SignedPolicy,oneof" json:"signed_policy,omitempty"`
	// agreement title
	AgreementTitle *string `protobuf:"bytes,4,opt,name=agreement_title,json=agreementTitle,proto3,oneof" json:"agreement_title,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,5,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// template for send sms
	SmsTemplate *string `protobuf:"bytes,6,opt,name=sms_template,json=smsTemplate,proto3,oneof" json:"sms_template,omitempty"`
	// email template title
	EmailTemplateTitle *string `protobuf:"bytes,7,opt,name=email_template_title,json=emailTemplateTitle,proto3,oneof" json:"email_template_title,omitempty"`
	// email template body
	EmailTemplateBody *string `protobuf:"bytes,8,opt,name=email_template_body,json=emailTemplateBody,proto3,oneof" json:"email_template_body,omitempty"`
	// whether to update last_required_time
	UpdateLastRequiredTime *bool `protobuf:"varint,9,opt,name=update_last_required_time,json=updateLastRequiredTime,proto3,oneof" json:"update_last_required_time,omitempty"`
}

func (x *UpdateAgreementRequest) Reset() {
	*x = UpdateAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgreementRequest) ProtoMessage() {}

func (x *UpdateAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgreementRequest.ProtoReflect.Descriptor instead.
func (*UpdateAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateAgreementRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAgreementRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateAgreementRequest) GetSignedPolicy() v11.SignedPolicy {
	if x != nil && x.SignedPolicy != nil {
		return *x.SignedPolicy
	}
	return v11.SignedPolicy(0)
}

func (x *UpdateAgreementRequest) GetAgreementTitle() string {
	if x != nil && x.AgreementTitle != nil {
		return *x.AgreementTitle
	}
	return ""
}

func (x *UpdateAgreementRequest) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *UpdateAgreementRequest) GetSmsTemplate() string {
	if x != nil && x.SmsTemplate != nil {
		return *x.SmsTemplate
	}
	return ""
}

func (x *UpdateAgreementRequest) GetEmailTemplateTitle() string {
	if x != nil && x.EmailTemplateTitle != nil {
		return *x.EmailTemplateTitle
	}
	return ""
}

func (x *UpdateAgreementRequest) GetEmailTemplateBody() string {
	if x != nil && x.EmailTemplateBody != nil {
		return *x.EmailTemplateBody
	}
	return ""
}

func (x *UpdateAgreementRequest) GetUpdateLastRequiredTime() bool {
	if x != nil && x.UpdateLastRequiredTime != nil {
		return *x.UpdateLastRequiredTime
	}
	return false
}

// UpdateServiceTypeRequest
type UpdateServiceTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// services type, see the enum definition in ServiceType
	ServiceType v11.ServiceType `protobuf:"varint,3,opt,name=service_type,json=serviceType,proto3,enum=moego.models.agreement.v1.ServiceType" json:"service_type,omitempty"`
	// true is set the service_type, false is cancel the service_type
	SetOrCancel bool `protobuf:"varint,4,opt,name=set_or_cancel,json=setOrCancel,proto3" json:"set_or_cancel,omitempty"`
}

func (x *UpdateServiceTypeRequest) Reset() {
	*x = UpdateServiceTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceTypeRequest) ProtoMessage() {}

func (x *UpdateServiceTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateServiceTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateServiceTypeRequest) GetServiceType() v11.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *UpdateServiceTypeRequest) GetSetOrCancel() bool {
	if x != nil {
		return x.SetOrCancel
	}
	return false
}

// agreement model simple view list
type GetAgreementListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	AgreementSimpleView []*v11.AgreementModelSimpleView `protobuf:"bytes,1,rep,name=agreement_simple_view,json=agreementSimpleView,proto3" json:"agreement_simple_view,omitempty"`
}

func (x *GetAgreementListResponse) Reset() {
	*x = GetAgreementListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListResponse) ProtoMessage() {}

func (x *GetAgreementListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListResponse.ProtoReflect.Descriptor instead.
func (*GetAgreementListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetAgreementListResponse) GetAgreementSimpleView() []*v11.AgreementModelSimpleView {
	if x != nil {
		return x.AgreementSimpleView
	}
	return nil
}

// agreement model content view list
type GetAgreementContentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	AgreementContentView []*v11.AgreementModelContentView `protobuf:"bytes,1,rep,name=agreement_content_view,json=agreementContentView,proto3" json:"agreement_content_view,omitempty"`
}

func (x *GetAgreementContentListResponse) Reset() {
	*x = GetAgreementContentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementContentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementContentListResponse) ProtoMessage() {}

func (x *GetAgreementContentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementContentListResponse.ProtoReflect.Descriptor instead.
func (*GetAgreementContentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetAgreementContentListResponse) GetAgreementContentView() []*v11.AgreementModelContentView {
	if x != nil {
		return x.AgreementContentView
	}
	return nil
}

// GetAgreementSignStatusListRequest
type GetAgreementSignStatusListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// target id
	TargetId *int64 `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`
	// services type, see the enum definition in ServiceType
	ServiceType *v11.ServiceType `protobuf:"varint,4,opt,name=service_type,json=serviceType,proto3,enum=moego.models.agreement.v1.ServiceType,oneof" json:"service_type,omitempty"`
}

func (x *GetAgreementSignStatusListRequest) Reset() {
	*x = GetAgreementSignStatusListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementSignStatusListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementSignStatusListRequest) ProtoMessage() {}

func (x *GetAgreementSignStatusListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementSignStatusListRequest.ProtoReflect.Descriptor instead.
func (*GetAgreementSignStatusListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetAgreementSignStatusListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAgreementSignStatusListRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetAgreementSignStatusListRequest) GetTargetId() int64 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *GetAgreementSignStatusListRequest) GetServiceType() v11.ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return v11.ServiceType(0)
}

// GetAgreementSignStatusListResponse
type GetAgreementSignStatusListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement sign status view list
	AgreementStatusView []*v11.AgreementSignStatusView `protobuf:"bytes,1,rep,name=agreement_status_view,json=agreementStatusView,proto3" json:"agreement_status_view,omitempty"`
}

func (x *GetAgreementSignStatusListResponse) Reset() {
	*x = GetAgreementSignStatusListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementSignStatusListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementSignStatusListResponse) ProtoMessage() {}

func (x *GetAgreementSignStatusListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementSignStatusListResponse.ProtoReflect.Descriptor instead.
func (*GetAgreementSignStatusListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetAgreementSignStatusListResponse) GetAgreementStatusView() []*v11.AgreementSignStatusView {
	if x != nil {
		return x.AgreementStatusView
	}
	return nil
}

// BatchGetAgreementSignStatusRequest
type BatchGetAgreementUnsignedAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id with appointment id list
	CustomerWithAppointmentId []*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId `protobuf:"bytes,2,rep,name=customer_with_appointment_id,json=customerWithAppointmentId,proto3" json:"customer_with_appointment_id,omitempty"`
}

func (x *BatchGetAgreementUnsignedAppointmentRequest) Reset() {
	*x = BatchGetAgreementUnsignedAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAgreementUnsignedAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAgreementUnsignedAppointmentRequest) ProtoMessage() {}

func (x *BatchGetAgreementUnsignedAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAgreementUnsignedAppointmentRequest.ProtoReflect.Descriptor instead.
func (*BatchGetAgreementUnsignedAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{14}
}

func (x *BatchGetAgreementUnsignedAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchGetAgreementUnsignedAppointmentRequest) GetCustomerWithAppointmentId() []*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId {
	if x != nil {
		return x.CustomerWithAppointmentId
	}
	return nil
}

// BatchGetAgreementSignStatusResponse
type BatchGetAgreementUnsignedAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// need sign map, key - customer id, value - whether need to sign
	AppointmentId []int64 `protobuf:"varint,1,rep,packed,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *BatchGetAgreementUnsignedAppointmentResponse) Reset() {
	*x = BatchGetAgreementUnsignedAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAgreementUnsignedAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAgreementUnsignedAppointmentResponse) ProtoMessage() {}

func (x *BatchGetAgreementUnsignedAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAgreementUnsignedAppointmentResponse.ProtoReflect.Descriptor instead.
func (*BatchGetAgreementUnsignedAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{15}
}

func (x *BatchGetAgreementUnsignedAppointmentResponse) GetAppointmentId() []int64 {
	if x != nil {
		return x.AppointmentId
	}
	return nil
}

// Get agreement content list by company response
type GetAgreementContentListByCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	AgreementContentView []*v11.AgreementModelContentView `protobuf:"bytes,1,rep,name=agreement_content_view,json=agreementContentView,proto3" json:"agreement_content_view,omitempty"`
}

func (x *GetAgreementContentListByCompanyResponse) Reset() {
	*x = GetAgreementContentListByCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementContentListByCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementContentListByCompanyResponse) ProtoMessage() {}

func (x *GetAgreementContentListByCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementContentListByCompanyResponse.ProtoReflect.Descriptor instead.
func (*GetAgreementContentListByCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetAgreementContentListByCompanyResponse) GetAgreementContentView() []*v11.AgreementModelContentView {
	if x != nil {
		return x.AgreementContentView
	}
	return nil
}

// GetAgreementListByCompanyRequest
type GetAgreementListByCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// agreement id list
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// status: normal, deleted
	Status *v1.Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.utils.v1.Status,oneof" json:"status,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
	// business ids
	BusinessIds []int64 `protobuf:"varint,5,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetAgreementListByCompanyRequest) Reset() {
	*x = GetAgreementListByCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListByCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListByCompanyRequest) ProtoMessage() {}

func (x *GetAgreementListByCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListByCompanyRequest.ProtoReflect.Descriptor instead.
func (*GetAgreementListByCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetAgreementListByCompanyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAgreementListByCompanyRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetAgreementListByCompanyRequest) GetStatus() v1.Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Status(0)
}

func (x *GetAgreementListByCompanyRequest) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

func (x *GetAgreementListByCompanyRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// GetAgreementListByCompanyResponse
type GetAgreementListByCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	AgreementSimpleView []*v11.AgreementModelSimpleView `protobuf:"bytes,1,rep,name=agreement_simple_view,json=agreementSimpleView,proto3" json:"agreement_simple_view,omitempty"`
}

func (x *GetAgreementListByCompanyResponse) Reset() {
	*x = GetAgreementListByCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListByCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListByCompanyResponse) ProtoMessage() {}

func (x *GetAgreementListByCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListByCompanyResponse.ProtoReflect.Descriptor instead.
func (*GetAgreementListByCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetAgreementListByCompanyResponse) GetAgreementSimpleView() []*v11.AgreementModelSimpleView {
	if x != nil {
		return x.AgreementSimpleView
	}
	return nil
}

// ListAgreementsRequest
type ListAgreementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// agreement id list
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// status: normal, deleted
	Status *v1.Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.utils.v1.Status,oneof" json:"status,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,5,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
	// business ids
	BusinessIds []int64 `protobuf:"varint,6,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *ListAgreementsRequest) Reset() {
	*x = ListAgreementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgreementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgreementsRequest) ProtoMessage() {}

func (x *ListAgreementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgreementsRequest.ProtoReflect.Descriptor instead.
func (*ListAgreementsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{19}
}

func (x *ListAgreementsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAgreementsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAgreementsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListAgreementsRequest) GetStatus() v1.Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Status(0)
}

func (x *ListAgreementsRequest) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

func (x *ListAgreementsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// ListAgreementsResponse
type ListAgreementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// appointment detail
	Agreements []*v11.AgreementModel `protobuf:"bytes,2,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

func (x *ListAgreementsResponse) Reset() {
	*x = ListAgreementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgreementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgreementsResponse) ProtoMessage() {}

func (x *ListAgreementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgreementsResponse.ProtoReflect.Descriptor instead.
func (*ListAgreementsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListAgreementsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAgreementsResponse) GetAgreements() []*v11.AgreementModel {
	if x != nil {
		return x.Agreements
	}
	return nil
}

// ListUnsignedAgreement request
type ListUnsignedAgreementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business ids
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// customer id, 如果没有 customer id, 则返回所有 agreement
	CustomerId *int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
}

func (x *ListUnsignedAgreementRequest) Reset() {
	*x = ListUnsignedAgreementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnsignedAgreementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnsignedAgreementRequest) ProtoMessage() {}

func (x *ListUnsignedAgreementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnsignedAgreementRequest.ProtoReflect.Descriptor instead.
func (*ListUnsignedAgreementRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListUnsignedAgreementRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListUnsignedAgreementRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListUnsignedAgreementRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListUnsignedAgreementRequest) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

// ListUnsignedAgreement response
type ListUnsignedAgreementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	Agreements []*v11.AgreementModel `protobuf:"bytes,1,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

func (x *ListUnsignedAgreementResponse) Reset() {
	*x = ListUnsignedAgreementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnsignedAgreementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnsignedAgreementResponse) ProtoMessage() {}

func (x *ListUnsignedAgreementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnsignedAgreementResponse.ProtoReflect.Descriptor instead.
func (*ListUnsignedAgreementResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListUnsignedAgreementResponse) GetAgreements() []*v11.AgreementModel {
	if x != nil {
		return x.Agreements
	}
	return nil
}

// ListUnsignedAgreementByCustomersRequest
type ListUnsignedAgreementByCustomersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// list of business ids
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// list of customer ids
	CustomerIds []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes *int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3,oneof" json:"service_types,omitempty"`
}

func (x *ListUnsignedAgreementByCustomersRequest) Reset() {
	*x = ListUnsignedAgreementByCustomersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnsignedAgreementByCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnsignedAgreementByCustomersRequest) ProtoMessage() {}

func (x *ListUnsignedAgreementByCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnsignedAgreementByCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListUnsignedAgreementByCustomersRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListUnsignedAgreementByCustomersRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListUnsignedAgreementByCustomersRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListUnsignedAgreementByCustomersRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListUnsignedAgreementByCustomersRequest) GetServiceTypes() int32 {
	if x != nil && x.ServiceTypes != nil {
		return *x.ServiceTypes
	}
	return 0
}

// ListUnsignedAgreementByCustomersResponse
type ListUnsignedAgreementByCustomersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	CustomerAgreements []*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView `protobuf:"bytes,1,rep,name=customer_agreements,json=customerAgreements,proto3" json:"customer_agreements,omitempty"`
}

func (x *ListUnsignedAgreementByCustomersResponse) Reset() {
	*x = ListUnsignedAgreementByCustomersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnsignedAgreementByCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnsignedAgreementByCustomersResponse) ProtoMessage() {}

func (x *ListUnsignedAgreementByCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnsignedAgreementByCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListUnsignedAgreementByCustomersResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListUnsignedAgreementByCustomersResponse) GetCustomerAgreements() []*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView {
	if x != nil {
		return x.CustomerAgreements
	}
	return nil
}

// customer with appointment
type BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) Reset() {
	*x = BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) ProtoMessage() {}

func (x *BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId.ProtoReflect.Descriptor instead.
func (*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// CustomerAgreementView
type ListUnsignedAgreementByCustomersResponse_CustomerAgreementView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// list of agreements
	Agreements []*v11.AgreementModelSimpleView `protobuf:"bytes,2,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

func (x *ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) Reset() {
	*x = ListUnsignedAgreementByCustomersResponse_CustomerAgreementView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) ProtoMessage() {}

func (x *ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_agreement_v1_agreement_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnsignedAgreementByCustomersResponse_CustomerAgreementView.ProtoReflect.Descriptor instead.
func (*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) Descriptor() ([]byte, []int) {
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListUnsignedAgreementByCustomersResponse_CustomerAgreementView) GetAgreements() []*v11.AgreementModelSimpleView {
	if x != nil {
		return x.Agreements
	}
	return nil
}

var File_moego_service_agreement_v1_agreement_service_proto protoreflect.FileDescriptor

var file_moego_service_agreement_v1_agreement_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x48, 0x0a, 0x15,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x22, 0x49, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x17,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x42, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0xdd, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x00,
	0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xe1, 0x05, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x3d, 0x0a, 0x11, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72,
	0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x40, 0x48, 0x01, 0x52, 0x10, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x33,
	0x0a, 0x0c, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80,
	0x04, 0x48, 0x02, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x03, 0x52,
	0x12, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x08,
	0x48, 0x04, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x05, 0x52, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42,
	0x16, 0x0a, 0x14, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb7, 0x05, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x0f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x01, 0x52, 0x0e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3d,
	0x0a, 0x11, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06,
	0x10, 0x01, 0x18, 0x80, 0x80, 0x40, 0x48, 0x02, 0x52, 0x10, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a,
	0x0c, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x04,
	0x48, 0x03, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x41, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x04, 0x52, 0x12,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x08, 0x48,
	0x05, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x19, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x16, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x55, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x5f,
	0x6f, 0x72, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x73, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x22, 0x83, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x15, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69,
	0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x22, 0x8d, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x16, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x14, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x22, 0x82, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66,
	0x0a, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x56, 0x69, 0x65, 0x77, 0x22, 0xd8, 0x02, 0x0a, 0x2b, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0xa2, 0x01, 0x0a, 0x1c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x61,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x6e, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x52, 0x19, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x1a, 0x63, 0x0a, 0x19,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x55, 0x0a, 0x2c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x16, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x14, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x22, 0xa5, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92,
	0x01, 0x0d, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x67, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x22, 0x86, 0x03, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x28, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a,
	0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0xf4, 0x03, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x22, 0xbb, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xfe, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f,
	0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x22, 0x6a, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x49, 0x0a, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xfc, 0x01, 0x0a,
	0x27, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x10, 0x64,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01,
	0x08, 0x10, 0x64, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0xc8, 0x02, 0x0a, 0x28,
	0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x13, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x8d, 0x01, 0x0a, 0x15, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x53, 0x0a, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x32, 0xac, 0x11, 0x0a, 0x10, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x77, 0x0a, 0x0e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x7d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x8b, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x0d, 0x49,
	0x6e, 0x69, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x6a, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x70, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x7a, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb9, 0x01,
	0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x6e,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x20, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x55,
	0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x6e, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x86, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_agreement_v1_agreement_service_proto_rawDescOnce sync.Once
	file_moego_service_agreement_v1_agreement_service_proto_rawDescData = file_moego_service_agreement_v1_agreement_service_proto_rawDesc
)

func file_moego_service_agreement_v1_agreement_service_proto_rawDescGZIP() []byte {
	file_moego_service_agreement_v1_agreement_service_proto_rawDescOnce.Do(func() {
		file_moego_service_agreement_v1_agreement_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_agreement_v1_agreement_service_proto_rawDescData)
	})
	return file_moego_service_agreement_v1_agreement_service_proto_rawDescData
}

var file_moego_service_agreement_v1_agreement_service_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_moego_service_agreement_v1_agreement_service_proto_goTypes = []interface{}{
	(*CheckAgreementRequest)(nil),                                                 // 0: moego.service.agreement.v1.CheckAgreementRequest
	(*GetAgreementRequest)(nil),                                                   // 1: moego.service.agreement.v1.GetAgreementRequest
	(*DeleteAgreementRequest)(nil),                                                // 2: moego.service.agreement.v1.DeleteAgreementRequest
	(*DeleteAgreementResponse)(nil),                                               // 3: moego.service.agreement.v1.DeleteAgreementResponse
	(*CheckAgreementResponse)(nil),                                                // 4: moego.service.agreement.v1.CheckAgreementResponse
	(*GetAgreementListRequest)(nil),                                               // 5: moego.service.agreement.v1.GetAgreementListRequest
	(*InitAgreementRequest)(nil),                                                  // 6: moego.service.agreement.v1.InitAgreementRequest
	(*AddAgreementRequest)(nil),                                                   // 7: moego.service.agreement.v1.AddAgreementRequest
	(*UpdateAgreementRequest)(nil),                                                // 8: moego.service.agreement.v1.UpdateAgreementRequest
	(*UpdateServiceTypeRequest)(nil),                                              // 9: moego.service.agreement.v1.UpdateServiceTypeRequest
	(*GetAgreementListResponse)(nil),                                              // 10: moego.service.agreement.v1.GetAgreementListResponse
	(*GetAgreementContentListResponse)(nil),                                       // 11: moego.service.agreement.v1.GetAgreementContentListResponse
	(*GetAgreementSignStatusListRequest)(nil),                                     // 12: moego.service.agreement.v1.GetAgreementSignStatusListRequest
	(*GetAgreementSignStatusListResponse)(nil),                                    // 13: moego.service.agreement.v1.GetAgreementSignStatusListResponse
	(*BatchGetAgreementUnsignedAppointmentRequest)(nil),                           // 14: moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest
	(*BatchGetAgreementUnsignedAppointmentResponse)(nil),                          // 15: moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse
	(*GetAgreementContentListByCompanyResponse)(nil),                              // 16: moego.service.agreement.v1.GetAgreementContentListByCompanyResponse
	(*GetAgreementListByCompanyRequest)(nil),                                      // 17: moego.service.agreement.v1.GetAgreementListByCompanyRequest
	(*GetAgreementListByCompanyResponse)(nil),                                     // 18: moego.service.agreement.v1.GetAgreementListByCompanyResponse
	(*ListAgreementsRequest)(nil),                                                 // 19: moego.service.agreement.v1.ListAgreementsRequest
	(*ListAgreementsResponse)(nil),                                                // 20: moego.service.agreement.v1.ListAgreementsResponse
	(*ListUnsignedAgreementRequest)(nil),                                          // 21: moego.service.agreement.v1.ListUnsignedAgreementRequest
	(*ListUnsignedAgreementResponse)(nil),                                         // 22: moego.service.agreement.v1.ListUnsignedAgreementResponse
	(*ListUnsignedAgreementByCustomersRequest)(nil),                               // 23: moego.service.agreement.v1.ListUnsignedAgreementByCustomersRequest
	(*ListUnsignedAgreementByCustomersResponse)(nil),                              // 24: moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse
	(*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId)(nil), // 25: moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId
	(*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView)(nil),        // 26: moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse.CustomerAgreementView
	(v1.Status)(0),                        // 27: moego.utils.v1.Status
	(v11.SignedPolicy)(0),                 // 28: moego.models.agreement.v1.SignedPolicy
	(v11.ServiceType)(0),                  // 29: moego.models.agreement.v1.ServiceType
	(*v11.AgreementModelSimpleView)(nil),  // 30: moego.models.agreement.v1.AgreementModelSimpleView
	(*v11.AgreementModelContentView)(nil), // 31: moego.models.agreement.v1.AgreementModelContentView
	(*v11.AgreementSignStatusView)(nil),   // 32: moego.models.agreement.v1.AgreementSignStatusView
	(*v2.PaginationRequest)(nil),          // 33: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),         // 34: moego.utils.v2.PaginationResponse
	(*v11.AgreementModel)(nil),            // 35: moego.models.agreement.v1.AgreementModel
}
var file_moego_service_agreement_v1_agreement_service_proto_depIdxs = []int32{
	27, // 0: moego.service.agreement.v1.GetAgreementListRequest.status:type_name -> moego.utils.v1.Status
	28, // 1: moego.service.agreement.v1.AddAgreementRequest.signed_policy:type_name -> moego.models.agreement.v1.SignedPolicy
	28, // 2: moego.service.agreement.v1.UpdateAgreementRequest.signed_policy:type_name -> moego.models.agreement.v1.SignedPolicy
	29, // 3: moego.service.agreement.v1.UpdateServiceTypeRequest.service_type:type_name -> moego.models.agreement.v1.ServiceType
	30, // 4: moego.service.agreement.v1.GetAgreementListResponse.agreement_simple_view:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	31, // 5: moego.service.agreement.v1.GetAgreementContentListResponse.agreement_content_view:type_name -> moego.models.agreement.v1.AgreementModelContentView
	29, // 6: moego.service.agreement.v1.GetAgreementSignStatusListRequest.service_type:type_name -> moego.models.agreement.v1.ServiceType
	32, // 7: moego.service.agreement.v1.GetAgreementSignStatusListResponse.agreement_status_view:type_name -> moego.models.agreement.v1.AgreementSignStatusView
	25, // 8: moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest.customer_with_appointment_id:type_name -> moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId
	31, // 9: moego.service.agreement.v1.GetAgreementContentListByCompanyResponse.agreement_content_view:type_name -> moego.models.agreement.v1.AgreementModelContentView
	27, // 10: moego.service.agreement.v1.GetAgreementListByCompanyRequest.status:type_name -> moego.utils.v1.Status
	30, // 11: moego.service.agreement.v1.GetAgreementListByCompanyResponse.agreement_simple_view:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	33, // 12: moego.service.agreement.v1.ListAgreementsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	27, // 13: moego.service.agreement.v1.ListAgreementsRequest.status:type_name -> moego.utils.v1.Status
	34, // 14: moego.service.agreement.v1.ListAgreementsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	35, // 15: moego.service.agreement.v1.ListAgreementsResponse.agreements:type_name -> moego.models.agreement.v1.AgreementModel
	35, // 16: moego.service.agreement.v1.ListUnsignedAgreementResponse.agreements:type_name -> moego.models.agreement.v1.AgreementModel
	26, // 17: moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse.customer_agreements:type_name -> moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse.CustomerAgreementView
	30, // 18: moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse.CustomerAgreementView.agreements:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	0,  // 19: moego.service.agreement.v1.AgreementService.CheckAgreement:input_type -> moego.service.agreement.v1.CheckAgreementRequest
	1,  // 20: moego.service.agreement.v1.AgreementService.GetAgreement:input_type -> moego.service.agreement.v1.GetAgreementRequest
	5,  // 21: moego.service.agreement.v1.AgreementService.GetAgreementList:input_type -> moego.service.agreement.v1.GetAgreementListRequest
	5,  // 22: moego.service.agreement.v1.AgreementService.GetAgreementContentList:input_type -> moego.service.agreement.v1.GetAgreementListRequest
	12, // 23: moego.service.agreement.v1.AgreementService.GetAgreementSignStatusList:input_type -> moego.service.agreement.v1.GetAgreementSignStatusListRequest
	6,  // 24: moego.service.agreement.v1.AgreementService.InitAgreement:input_type -> moego.service.agreement.v1.InitAgreementRequest
	7,  // 25: moego.service.agreement.v1.AgreementService.AddAgreement:input_type -> moego.service.agreement.v1.AddAgreementRequest
	8,  // 26: moego.service.agreement.v1.AgreementService.UpdateAgreement:input_type -> moego.service.agreement.v1.UpdateAgreementRequest
	9,  // 27: moego.service.agreement.v1.AgreementService.UpdateAgreementServiceType:input_type -> moego.service.agreement.v1.UpdateServiceTypeRequest
	2,  // 28: moego.service.agreement.v1.AgreementService.DeleteAgreement:input_type -> moego.service.agreement.v1.DeleteAgreementRequest
	14, // 29: moego.service.agreement.v1.AgreementService.BatchGetAgreementUnsignedAppointment:input_type -> moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest
	17, // 30: moego.service.agreement.v1.AgreementService.GetAgreementContentListByCompany:input_type -> moego.service.agreement.v1.GetAgreementListByCompanyRequest
	17, // 31: moego.service.agreement.v1.AgreementService.GetAgreementListByCompany:input_type -> moego.service.agreement.v1.GetAgreementListByCompanyRequest
	19, // 32: moego.service.agreement.v1.AgreementService.ListAgreements:input_type -> moego.service.agreement.v1.ListAgreementsRequest
	21, // 33: moego.service.agreement.v1.AgreementService.ListUnsignedAgreement:input_type -> moego.service.agreement.v1.ListUnsignedAgreementRequest
	23, // 34: moego.service.agreement.v1.AgreementService.ListUnsignedAgreementByCustomers:input_type -> moego.service.agreement.v1.ListUnsignedAgreementByCustomersRequest
	4,  // 35: moego.service.agreement.v1.AgreementService.CheckAgreement:output_type -> moego.service.agreement.v1.CheckAgreementResponse
	35, // 36: moego.service.agreement.v1.AgreementService.GetAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	10, // 37: moego.service.agreement.v1.AgreementService.GetAgreementList:output_type -> moego.service.agreement.v1.GetAgreementListResponse
	11, // 38: moego.service.agreement.v1.AgreementService.GetAgreementContentList:output_type -> moego.service.agreement.v1.GetAgreementContentListResponse
	13, // 39: moego.service.agreement.v1.AgreementService.GetAgreementSignStatusList:output_type -> moego.service.agreement.v1.GetAgreementSignStatusListResponse
	35, // 40: moego.service.agreement.v1.AgreementService.InitAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	35, // 41: moego.service.agreement.v1.AgreementService.AddAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	35, // 42: moego.service.agreement.v1.AgreementService.UpdateAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	30, // 43: moego.service.agreement.v1.AgreementService.UpdateAgreementServiceType:output_type -> moego.models.agreement.v1.AgreementModelSimpleView
	3,  // 44: moego.service.agreement.v1.AgreementService.DeleteAgreement:output_type -> moego.service.agreement.v1.DeleteAgreementResponse
	15, // 45: moego.service.agreement.v1.AgreementService.BatchGetAgreementUnsignedAppointment:output_type -> moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse
	16, // 46: moego.service.agreement.v1.AgreementService.GetAgreementContentListByCompany:output_type -> moego.service.agreement.v1.GetAgreementContentListByCompanyResponse
	18, // 47: moego.service.agreement.v1.AgreementService.GetAgreementListByCompany:output_type -> moego.service.agreement.v1.GetAgreementListByCompanyResponse
	20, // 48: moego.service.agreement.v1.AgreementService.ListAgreements:output_type -> moego.service.agreement.v1.ListAgreementsResponse
	22, // 49: moego.service.agreement.v1.AgreementService.ListUnsignedAgreement:output_type -> moego.service.agreement.v1.ListUnsignedAgreementResponse
	24, // 50: moego.service.agreement.v1.AgreementService.ListUnsignedAgreementByCustomers:output_type -> moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse
	35, // [35:51] is the sub-list for method output_type
	19, // [19:35] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_service_agreement_v1_agreement_service_proto_init() }
func file_moego_service_agreement_v1_agreement_service_proto_init() {
	if File_moego_service_agreement_v1_agreement_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgreementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAgreementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementContentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementSignStatusListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementSignStatusListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAgreementUnsignedAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAgreementUnsignedAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementContentListByCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListByCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListByCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgreementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgreementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnsignedAgreementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnsignedAgreementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnsignedAgreementByCustomersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnsignedAgreementByCustomersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAgreementUnsignedAppointmentRequest_CustomerWithAppointmentId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_agreement_v1_agreement_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUnsignedAgreementByCustomersResponse_CustomerAgreementView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[19].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_moego_service_agreement_v1_agreement_service_proto_msgTypes[23].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_agreement_v1_agreement_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_agreement_v1_agreement_service_proto_goTypes,
		DependencyIndexes: file_moego_service_agreement_v1_agreement_service_proto_depIdxs,
		MessageInfos:      file_moego_service_agreement_v1_agreement_service_proto_msgTypes,
	}.Build()
	File_moego_service_agreement_v1_agreement_service_proto = out.File
	file_moego_service_agreement_v1_agreement_service_proto_rawDesc = nil
	file_moego_service_agreement_v1_agreement_service_proto_goTypes = nil
	file_moego_service_agreement_v1_agreement_service_proto_depIdxs = nil
}
