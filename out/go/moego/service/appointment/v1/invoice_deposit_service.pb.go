// @since 2024-02-22 11:28:28
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/invoice_deposit_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get invoice_deposit request
type GetInvoiceDepositListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the invoice id
	InvoiceIds []int64 `protobuf:"varint,2,rep,packed,name=invoice_ids,json=invoiceIds,proto3" json:"invoice_ids,omitempty"`
}

func (x *GetInvoiceDepositListRequest) Reset() {
	*x = GetInvoiceDepositListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceDepositListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceDepositListRequest) ProtoMessage() {}

func (x *GetInvoiceDepositListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceDepositListRequest.ProtoReflect.Descriptor instead.
func (*GetInvoiceDepositListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetInvoiceDepositListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetInvoiceDepositListRequest) GetInvoiceIds() []int64 {
	if x != nil {
		return x.InvoiceIds
	}
	return nil
}

// get invoice_deposit response
type GetInvoiceDepositListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deposit
	InvoiceDeposit map[int64]*v1.InvoiceDepositModel `protobuf:"bytes,1,rep,name=invoice_deposit,json=invoiceDeposit,proto3" json:"invoice_deposit,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetInvoiceDepositListResponse) Reset() {
	*x = GetInvoiceDepositListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceDepositListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceDepositListResponse) ProtoMessage() {}

func (x *GetInvoiceDepositListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceDepositListResponse.ProtoReflect.Descriptor instead.
func (*GetInvoiceDepositListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetInvoiceDepositListResponse) GetInvoiceDeposit() map[int64]*v1.InvoiceDepositModel {
	if x != nil {
		return x.InvoiceDeposit
	}
	return nil
}

var File_moego_service_appointment_v1_invoice_deposit_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x69,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x75, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92,
	0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x22, 0x8e, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0e, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x1a, 0x73, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xaa, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x90, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescData = file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDesc
)

func file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDescData
}

var file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_service_appointment_v1_invoice_deposit_service_proto_goTypes = []interface{}{
	(*GetInvoiceDepositListRequest)(nil),  // 0: moego.service.appointment.v1.GetInvoiceDepositListRequest
	(*GetInvoiceDepositListResponse)(nil), // 1: moego.service.appointment.v1.GetInvoiceDepositListResponse
	nil,                                   // 2: moego.service.appointment.v1.GetInvoiceDepositListResponse.InvoiceDepositEntry
	(*v1.InvoiceDepositModel)(nil),        // 3: moego.models.appointment.v1.InvoiceDepositModel
}
var file_moego_service_appointment_v1_invoice_deposit_service_proto_depIdxs = []int32{
	2, // 0: moego.service.appointment.v1.GetInvoiceDepositListResponse.invoice_deposit:type_name -> moego.service.appointment.v1.GetInvoiceDepositListResponse.InvoiceDepositEntry
	3, // 1: moego.service.appointment.v1.GetInvoiceDepositListResponse.InvoiceDepositEntry.value:type_name -> moego.models.appointment.v1.InvoiceDepositModel
	0, // 2: moego.service.appointment.v1.InvoiceDepositService.GetInvoiceDepositList:input_type -> moego.service.appointment.v1.GetInvoiceDepositListRequest
	1, // 3: moego.service.appointment.v1.InvoiceDepositService.GetInvoiceDepositList:output_type -> moego.service.appointment.v1.GetInvoiceDepositListResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_invoice_deposit_service_proto_init() }
func file_moego_service_appointment_v1_invoice_deposit_service_proto_init() {
	if File_moego_service_appointment_v1_invoice_deposit_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceDepositListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceDepositListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_invoice_deposit_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_invoice_deposit_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_invoice_deposit_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_invoice_deposit_service_proto = out.File
	file_moego_service_appointment_v1_invoice_deposit_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_invoice_deposit_service_proto_goTypes = nil
	file_moego_service_appointment_v1_invoice_deposit_service_proto_depIdxs = nil
}
