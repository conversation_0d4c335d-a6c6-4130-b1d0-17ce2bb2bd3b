// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/service_charge_detail_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServiceChargeDetailServiceClient is the client API for ServiceChargeDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceChargeDetailServiceClient interface {
	// add service_charge_detail
	AddServiceChargeDetail(ctx context.Context, in *AddServiceChargeDetailRequest, opts ...grpc.CallOption) (*AddServiceChargeDetailResponse, error)
	// delete service_charge_detail
	DeleteServiceChargeDetail(ctx context.Context, in *DeleteServiceChargeDetailRequest, opts ...grpc.CallOption) (*DeleteServiceChargeDetailResponse, error)
	// Update upcoming appointment service charge details
	UpdateUpcomingServiceChargeDetails(ctx context.Context, in *UpdateUpcomingServiceChargeDetailsRequest, opts ...grpc.CallOption) (*UpdateUpcomingServiceChargeDetailsResponse, error)
	// List service charge details
	ListServiceChargeDetails(ctx context.Context, in *ListServiceChargeDetailsRequest, opts ...grpc.CallOption) (*ListServiceChargeDetailsResponse, error)
	// List hit late pick-up rules
	// Use the current business time as the check out time to determine if there is a service charge hit.
	ListHitLatePickUpRules(ctx context.Context, in *ListHitLatePickUpRulesRequest, opts ...grpc.CallOption) (*ListHitLatePickUpRulesResponse, error)
	// Add late pick-up fee
	// Add service charge fee to the appointment if there is a late pick up rule hit.
	AddLatePickUpServiceChargeDetail(ctx context.Context, in *AddLatePickUpServiceChargeDetailRequest, opts ...grpc.CallOption) (*AddLatePickUpServiceChargeDetailResponse, error)
}

type serviceChargeDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceChargeDetailServiceClient(cc grpc.ClientConnInterface) ServiceChargeDetailServiceClient {
	return &serviceChargeDetailServiceClient{cc}
}

func (c *serviceChargeDetailServiceClient) AddServiceChargeDetail(ctx context.Context, in *AddServiceChargeDetailRequest, opts ...grpc.CallOption) (*AddServiceChargeDetailResponse, error) {
	out := new(AddServiceChargeDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/AddServiceChargeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeDetailServiceClient) DeleteServiceChargeDetail(ctx context.Context, in *DeleteServiceChargeDetailRequest, opts ...grpc.CallOption) (*DeleteServiceChargeDetailResponse, error) {
	out := new(DeleteServiceChargeDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/DeleteServiceChargeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeDetailServiceClient) UpdateUpcomingServiceChargeDetails(ctx context.Context, in *UpdateUpcomingServiceChargeDetailsRequest, opts ...grpc.CallOption) (*UpdateUpcomingServiceChargeDetailsResponse, error) {
	out := new(UpdateUpcomingServiceChargeDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/UpdateUpcomingServiceChargeDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeDetailServiceClient) ListServiceChargeDetails(ctx context.Context, in *ListServiceChargeDetailsRequest, opts ...grpc.CallOption) (*ListServiceChargeDetailsResponse, error) {
	out := new(ListServiceChargeDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/ListServiceChargeDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeDetailServiceClient) ListHitLatePickUpRules(ctx context.Context, in *ListHitLatePickUpRulesRequest, opts ...grpc.CallOption) (*ListHitLatePickUpRulesResponse, error) {
	out := new(ListHitLatePickUpRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/ListHitLatePickUpRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeDetailServiceClient) AddLatePickUpServiceChargeDetail(ctx context.Context, in *AddLatePickUpServiceChargeDetailRequest, opts ...grpc.CallOption) (*AddLatePickUpServiceChargeDetailResponse, error) {
	out := new(AddLatePickUpServiceChargeDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ServiceChargeDetailService/AddLatePickUpServiceChargeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceChargeDetailServiceServer is the server API for ServiceChargeDetailService service.
// All implementations must embed UnimplementedServiceChargeDetailServiceServer
// for forward compatibility
type ServiceChargeDetailServiceServer interface {
	// add service_charge_detail
	AddServiceChargeDetail(context.Context, *AddServiceChargeDetailRequest) (*AddServiceChargeDetailResponse, error)
	// delete service_charge_detail
	DeleteServiceChargeDetail(context.Context, *DeleteServiceChargeDetailRequest) (*DeleteServiceChargeDetailResponse, error)
	// Update upcoming appointment service charge details
	UpdateUpcomingServiceChargeDetails(context.Context, *UpdateUpcomingServiceChargeDetailsRequest) (*UpdateUpcomingServiceChargeDetailsResponse, error)
	// List service charge details
	ListServiceChargeDetails(context.Context, *ListServiceChargeDetailsRequest) (*ListServiceChargeDetailsResponse, error)
	// List hit late pick-up rules
	// Use the current business time as the check out time to determine if there is a service charge hit.
	ListHitLatePickUpRules(context.Context, *ListHitLatePickUpRulesRequest) (*ListHitLatePickUpRulesResponse, error)
	// Add late pick-up fee
	// Add service charge fee to the appointment if there is a late pick up rule hit.
	AddLatePickUpServiceChargeDetail(context.Context, *AddLatePickUpServiceChargeDetailRequest) (*AddLatePickUpServiceChargeDetailResponse, error)
	mustEmbedUnimplementedServiceChargeDetailServiceServer()
}

// UnimplementedServiceChargeDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceChargeDetailServiceServer struct {
}

func (UnimplementedServiceChargeDetailServiceServer) AddServiceChargeDetail(context.Context, *AddServiceChargeDetailRequest) (*AddServiceChargeDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddServiceChargeDetail not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) DeleteServiceChargeDetail(context.Context, *DeleteServiceChargeDetailRequest) (*DeleteServiceChargeDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceChargeDetail not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) UpdateUpcomingServiceChargeDetails(context.Context, *UpdateUpcomingServiceChargeDetailsRequest) (*UpdateUpcomingServiceChargeDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUpcomingServiceChargeDetails not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) ListServiceChargeDetails(context.Context, *ListServiceChargeDetailsRequest) (*ListServiceChargeDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServiceChargeDetails not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) ListHitLatePickUpRules(context.Context, *ListHitLatePickUpRulesRequest) (*ListHitLatePickUpRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHitLatePickUpRules not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) AddLatePickUpServiceChargeDetail(context.Context, *AddLatePickUpServiceChargeDetailRequest) (*AddLatePickUpServiceChargeDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLatePickUpServiceChargeDetail not implemented")
}
func (UnimplementedServiceChargeDetailServiceServer) mustEmbedUnimplementedServiceChargeDetailServiceServer() {
}

// UnsafeServiceChargeDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceChargeDetailServiceServer will
// result in compilation errors.
type UnsafeServiceChargeDetailServiceServer interface {
	mustEmbedUnimplementedServiceChargeDetailServiceServer()
}

func RegisterServiceChargeDetailServiceServer(s grpc.ServiceRegistrar, srv ServiceChargeDetailServiceServer) {
	s.RegisterService(&ServiceChargeDetailService_ServiceDesc, srv)
}

func _ServiceChargeDetailService_AddServiceChargeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddServiceChargeDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).AddServiceChargeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/AddServiceChargeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).AddServiceChargeDetail(ctx, req.(*AddServiceChargeDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeDetailService_DeleteServiceChargeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceChargeDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).DeleteServiceChargeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/DeleteServiceChargeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).DeleteServiceChargeDetail(ctx, req.(*DeleteServiceChargeDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeDetailService_UpdateUpcomingServiceChargeDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUpcomingServiceChargeDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).UpdateUpcomingServiceChargeDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/UpdateUpcomingServiceChargeDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).UpdateUpcomingServiceChargeDetails(ctx, req.(*UpdateUpcomingServiceChargeDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeDetailService_ListServiceChargeDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServiceChargeDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).ListServiceChargeDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/ListServiceChargeDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).ListServiceChargeDetails(ctx, req.(*ListServiceChargeDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeDetailService_ListHitLatePickUpRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHitLatePickUpRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).ListHitLatePickUpRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/ListHitLatePickUpRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).ListHitLatePickUpRules(ctx, req.(*ListHitLatePickUpRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeDetailService_AddLatePickUpServiceChargeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLatePickUpServiceChargeDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeDetailServiceServer).AddLatePickUpServiceChargeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ServiceChargeDetailService/AddLatePickUpServiceChargeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeDetailServiceServer).AddLatePickUpServiceChargeDetail(ctx, req.(*AddLatePickUpServiceChargeDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceChargeDetailService_ServiceDesc is the grpc.ServiceDesc for ServiceChargeDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceChargeDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.ServiceChargeDetailService",
	HandlerType: (*ServiceChargeDetailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddServiceChargeDetail",
			Handler:    _ServiceChargeDetailService_AddServiceChargeDetail_Handler,
		},
		{
			MethodName: "DeleteServiceChargeDetail",
			Handler:    _ServiceChargeDetailService_DeleteServiceChargeDetail_Handler,
		},
		{
			MethodName: "UpdateUpcomingServiceChargeDetails",
			Handler:    _ServiceChargeDetailService_UpdateUpcomingServiceChargeDetails_Handler,
		},
		{
			MethodName: "ListServiceChargeDetails",
			Handler:    _ServiceChargeDetailService_ListServiceChargeDetails_Handler,
		},
		{
			MethodName: "ListHitLatePickUpRules",
			Handler:    _ServiceChargeDetailService_ListHitLatePickUpRules_Handler,
		},
		{
			MethodName: "AddLatePickUpServiceChargeDetail",
			Handler:    _ServiceChargeDetailService_AddLatePickUpServiceChargeDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/service_charge_detail_service.proto",
}
