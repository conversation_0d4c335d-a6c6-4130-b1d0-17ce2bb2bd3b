// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/service_service.proto

package appointmentsvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListServiceColorCodeByAppointmentIds request
type ListServiceColorCodeByAppointmentIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment ids
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *ListServiceColorCodeByAppointmentIdsRequest) Reset() {
	*x = ListServiceColorCodeByAppointmentIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceColorCodeByAppointmentIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceColorCodeByAppointmentIdsRequest) ProtoMessage() {}

func (x *ListServiceColorCodeByAppointmentIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceColorCodeByAppointmentIdsRequest.ProtoReflect.Descriptor instead.
func (*ListServiceColorCodeByAppointmentIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListServiceColorCodeByAppointmentIdsRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// ListServiceColorCodeByAppointmentIds response
type ListServiceColorCodeByAppointmentIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of AppointmentIdAndServices
	ColorCodes []*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode `protobuf:"bytes,1,rep,name=color_codes,json=colorCodes,proto3" json:"color_codes,omitempty"`
}

func (x *ListServiceColorCodeByAppointmentIdsResponse) Reset() {
	*x = ListServiceColorCodeByAppointmentIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceColorCodeByAppointmentIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceColorCodeByAppointmentIdsResponse) ProtoMessage() {}

func (x *ListServiceColorCodeByAppointmentIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceColorCodeByAppointmentIdsResponse.ProtoReflect.Descriptor instead.
func (*ListServiceColorCodeByAppointmentIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_service_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListServiceColorCodeByAppointmentIdsResponse) GetColorCodes() []*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode {
	if x != nil {
		return x.ColorCodes
	}
	return nil
}

// Appointment id and services
type ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Color code
	ColorCode string `protobuf:"bytes,2,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
}

func (x *ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) Reset() {
	*x = ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) ProtoMessage() {}

func (x *ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_service_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode.ProtoReflect.Descriptor instead.
func (*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_service_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

var File_moego_service_appointment_v1_service_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_service_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x2b, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x22, 0x99, 0x02, 0x0a, 0x2c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x64, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x1a, 0x61, 0x0a, 0x19, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x32, 0xd7, 0x01,
	0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0xbf, 0x01, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x1a, 0x03, 0x88, 0x02, 0x01, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_service_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_service_service_proto_rawDescData = file_moego_service_appointment_v1_service_service_proto_rawDesc
)

func file_moego_service_appointment_v1_service_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_service_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_service_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_service_service_proto_rawDescData
}

var file_moego_service_appointment_v1_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_service_appointment_v1_service_service_proto_goTypes = []interface{}{
	(*ListServiceColorCodeByAppointmentIdsRequest)(nil),                            // 0: moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsRequest
	(*ListServiceColorCodeByAppointmentIdsResponse)(nil),                           // 1: moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsResponse
	(*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode)(nil), // 2: moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsResponse.AppointmentIdAndColorCode
}
var file_moego_service_appointment_v1_service_service_proto_depIdxs = []int32{
	2, // 0: moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsResponse.color_codes:type_name -> moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsResponse.AppointmentIdAndColorCode
	0, // 1: moego.service.appointment.v1.ServiceService.ListServiceColorCodeByAppointmentIds:input_type -> moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsRequest
	1, // 2: moego.service.appointment.v1.ServiceService.ListServiceColorCodeByAppointmentIds:output_type -> moego.service.appointment.v1.ListServiceColorCodeByAppointmentIdsResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_service_service_proto_init() }
func file_moego_service_appointment_v1_service_service_proto_init() {
	if File_moego_service_appointment_v1_service_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_service_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceColorCodeByAppointmentIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_service_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceColorCodeByAppointmentIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_service_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceColorCodeByAppointmentIdsResponse_AppointmentIdAndColorCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_service_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_service_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_service_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_service_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_service_service_proto = out.File
	file_moego_service_appointment_v1_service_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_service_service_proto_goTypes = nil
	file_moego_service_appointment_v1_service_service_proto_depIdxs = nil
}
