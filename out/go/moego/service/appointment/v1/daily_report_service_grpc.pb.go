// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/daily_report_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DailyReportServiceClient is the client API for DailyReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DailyReportServiceClient interface {
	// get daily report config
	GetDailyReportConfig(ctx context.Context, in *GetDailyReportConfigRequest, opts ...grpc.CallOption) (*GetDailyReportConfigResponse, error)
	// list daily report config
	ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigRequest, opts ...grpc.CallOption) (*ListDailyReportConfigResponse, error)
	// get daily report config sent result
	GetDailyReportSentResult(ctx context.Context, in *GetDailyReportSentResultRequest, opts ...grpc.CallOption) (*GetDailyReportSentResultResponse, error)
	// upsert daily report config
	UpsertDailyReportConfig(ctx context.Context, in *UpsertDailyReportConfigRequest, opts ...grpc.CallOption) (*UpsertDailyReportConfigResponse, error)
	// get daily report sent history
	GetDailyReportSentHistory(ctx context.Context, in *GetDailyReportSentHistoryRequest, opts ...grpc.CallOption) (*GetDailyReportSentHistoryResponse, error)
	// get daily report for customer
	GetDailyReportForCustomer(ctx context.Context, in *GetDailyReportForCustomerRequest, opts ...grpc.CallOption) (*GetDailyReportForCustomerResponse, error)
	// generate message content
	GenerateMessageContent(ctx context.Context, in *GenerateMessageContentRequest, opts ...grpc.CallOption) (*GenerateMessageContentResponse, error)
	// send message
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error)
	// list daily report config by filter
	ListDailyReportConfigByFilter(ctx context.Context, in *ListDailyReportConfigByFilterRequest, opts ...grpc.CallOption) (*ListDailyReportConfigByFilterResponse, error)
	// batch send daily draft report
	BatchSendDailyDraftReport(ctx context.Context, in *BatchSendDailyDraftReportRequest, opts ...grpc.CallOption) (*BatchSendDailyDraftReportResponse, error)
	// batch delete daily report config
	BatchDeleteDailyReportConfig(ctx context.Context, in *BatchDeleteDailyReportConfigRequest, opts ...grpc.CallOption) (*BatchDeleteDailyReportConfigResponse, error)
	// GenerateMessageContentById
	GenerateMessageContentById(ctx context.Context, in *GenerateMessageContentByIdRequest, opts ...grpc.CallOption) (*GenerateMessageContentByIdResponse, error)
}

type dailyReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDailyReportServiceClient(cc grpc.ClientConnInterface) DailyReportServiceClient {
	return &dailyReportServiceClient{cc}
}

func (c *dailyReportServiceClient) GetDailyReportConfig(ctx context.Context, in *GetDailyReportConfigRequest, opts ...grpc.CallOption) (*GetDailyReportConfigResponse, error) {
	out := new(GetDailyReportConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GetDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigRequest, opts ...grpc.CallOption) (*ListDailyReportConfigResponse, error) {
	out := new(ListDailyReportConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/ListDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportSentResult(ctx context.Context, in *GetDailyReportSentResultRequest, opts ...grpc.CallOption) (*GetDailyReportSentResultResponse, error) {
	out := new(GetDailyReportSentResultResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GetDailyReportSentResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) UpsertDailyReportConfig(ctx context.Context, in *UpsertDailyReportConfigRequest, opts ...grpc.CallOption) (*UpsertDailyReportConfigResponse, error) {
	out := new(UpsertDailyReportConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/UpsertDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportSentHistory(ctx context.Context, in *GetDailyReportSentHistoryRequest, opts ...grpc.CallOption) (*GetDailyReportSentHistoryResponse, error) {
	out := new(GetDailyReportSentHistoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GetDailyReportSentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportForCustomer(ctx context.Context, in *GetDailyReportForCustomerRequest, opts ...grpc.CallOption) (*GetDailyReportForCustomerResponse, error) {
	out := new(GetDailyReportForCustomerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GetDailyReportForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GenerateMessageContent(ctx context.Context, in *GenerateMessageContentRequest, opts ...grpc.CallOption) (*GenerateMessageContentResponse, error) {
	out := new(GenerateMessageContentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GenerateMessageContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error) {
	out := new(SendMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/SendMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) ListDailyReportConfigByFilter(ctx context.Context, in *ListDailyReportConfigByFilterRequest, opts ...grpc.CallOption) (*ListDailyReportConfigByFilterResponse, error) {
	out := new(ListDailyReportConfigByFilterResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/ListDailyReportConfigByFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) BatchSendDailyDraftReport(ctx context.Context, in *BatchSendDailyDraftReportRequest, opts ...grpc.CallOption) (*BatchSendDailyDraftReportResponse, error) {
	out := new(BatchSendDailyDraftReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/BatchSendDailyDraftReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) BatchDeleteDailyReportConfig(ctx context.Context, in *BatchDeleteDailyReportConfigRequest, opts ...grpc.CallOption) (*BatchDeleteDailyReportConfigResponse, error) {
	out := new(BatchDeleteDailyReportConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GenerateMessageContentById(ctx context.Context, in *GenerateMessageContentByIdRequest, opts ...grpc.CallOption) (*GenerateMessageContentByIdResponse, error) {
	out := new(GenerateMessageContentByIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DailyReportService/GenerateMessageContentById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DailyReportServiceServer is the server API for DailyReportService service.
// All implementations must embed UnimplementedDailyReportServiceServer
// for forward compatibility
type DailyReportServiceServer interface {
	// get daily report config
	GetDailyReportConfig(context.Context, *GetDailyReportConfigRequest) (*GetDailyReportConfigResponse, error)
	// list daily report config
	ListDailyReportConfig(context.Context, *ListDailyReportConfigRequest) (*ListDailyReportConfigResponse, error)
	// get daily report config sent result
	GetDailyReportSentResult(context.Context, *GetDailyReportSentResultRequest) (*GetDailyReportSentResultResponse, error)
	// upsert daily report config
	UpsertDailyReportConfig(context.Context, *UpsertDailyReportConfigRequest) (*UpsertDailyReportConfigResponse, error)
	// get daily report sent history
	GetDailyReportSentHistory(context.Context, *GetDailyReportSentHistoryRequest) (*GetDailyReportSentHistoryResponse, error)
	// get daily report for customer
	GetDailyReportForCustomer(context.Context, *GetDailyReportForCustomerRequest) (*GetDailyReportForCustomerResponse, error)
	// generate message content
	GenerateMessageContent(context.Context, *GenerateMessageContentRequest) (*GenerateMessageContentResponse, error)
	// send message
	SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error)
	// list daily report config by filter
	ListDailyReportConfigByFilter(context.Context, *ListDailyReportConfigByFilterRequest) (*ListDailyReportConfigByFilterResponse, error)
	// batch send daily draft report
	BatchSendDailyDraftReport(context.Context, *BatchSendDailyDraftReportRequest) (*BatchSendDailyDraftReportResponse, error)
	// batch delete daily report config
	BatchDeleteDailyReportConfig(context.Context, *BatchDeleteDailyReportConfigRequest) (*BatchDeleteDailyReportConfigResponse, error)
	// GenerateMessageContentById
	GenerateMessageContentById(context.Context, *GenerateMessageContentByIdRequest) (*GenerateMessageContentByIdResponse, error)
	mustEmbedUnimplementedDailyReportServiceServer()
}

// UnimplementedDailyReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDailyReportServiceServer struct {
}

func (UnimplementedDailyReportServiceServer) GetDailyReportConfig(context.Context, *GetDailyReportConfigRequest) (*GetDailyReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) ListDailyReportConfig(context.Context, *ListDailyReportConfigRequest) (*ListDailyReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportSentResult(context.Context, *GetDailyReportSentResultRequest) (*GetDailyReportSentResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportSentResult not implemented")
}
func (UnimplementedDailyReportServiceServer) UpsertDailyReportConfig(context.Context, *UpsertDailyReportConfigRequest) (*UpsertDailyReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportSentHistory(context.Context, *GetDailyReportSentHistoryRequest) (*GetDailyReportSentHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportSentHistory not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportForCustomer(context.Context, *GetDailyReportForCustomerRequest) (*GetDailyReportForCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportForCustomer not implemented")
}
func (UnimplementedDailyReportServiceServer) GenerateMessageContent(context.Context, *GenerateMessageContentRequest) (*GenerateMessageContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMessageContent not implemented")
}
func (UnimplementedDailyReportServiceServer) SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedDailyReportServiceServer) ListDailyReportConfigByFilter(context.Context, *ListDailyReportConfigByFilterRequest) (*ListDailyReportConfigByFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDailyReportConfigByFilter not implemented")
}
func (UnimplementedDailyReportServiceServer) BatchSendDailyDraftReport(context.Context, *BatchSendDailyDraftReportRequest) (*BatchSendDailyDraftReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSendDailyDraftReport not implemented")
}
func (UnimplementedDailyReportServiceServer) BatchDeleteDailyReportConfig(context.Context, *BatchDeleteDailyReportConfigRequest) (*BatchDeleteDailyReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) GenerateMessageContentById(context.Context, *GenerateMessageContentByIdRequest) (*GenerateMessageContentByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMessageContentById not implemented")
}
func (UnimplementedDailyReportServiceServer) mustEmbedUnimplementedDailyReportServiceServer() {}

// UnsafeDailyReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DailyReportServiceServer will
// result in compilation errors.
type UnsafeDailyReportServiceServer interface {
	mustEmbedUnimplementedDailyReportServiceServer()
}

func RegisterDailyReportServiceServer(s grpc.ServiceRegistrar, srv DailyReportServiceServer) {
	s.RegisterService(&DailyReportService_ServiceDesc, srv)
}

func _DailyReportService_GetDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GetDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportConfig(ctx, req.(*GetDailyReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_ListDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDailyReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/ListDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, req.(*ListDailyReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportSentResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportSentResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportSentResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GetDailyReportSentResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportSentResult(ctx, req.(*GetDailyReportSentResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_UpsertDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertDailyReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).UpsertDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/UpsertDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).UpsertDailyReportConfig(ctx, req.(*UpsertDailyReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportSentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportSentHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportSentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GetDailyReportSentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportSentHistory(ctx, req.(*GetDailyReportSentHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportForCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GetDailyReportForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportForCustomer(ctx, req.(*GetDailyReportForCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GenerateMessageContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMessageContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GenerateMessageContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GenerateMessageContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GenerateMessageContent(ctx, req.(*GenerateMessageContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/SendMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).SendMessage(ctx, req.(*SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_ListDailyReportConfigByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDailyReportConfigByFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).ListDailyReportConfigByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/ListDailyReportConfigByFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).ListDailyReportConfigByFilter(ctx, req.(*ListDailyReportConfigByFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_BatchSendDailyDraftReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendDailyDraftReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).BatchSendDailyDraftReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/BatchSendDailyDraftReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).BatchSendDailyDraftReport(ctx, req.(*BatchSendDailyDraftReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_BatchDeleteDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteDailyReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).BatchDeleteDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).BatchDeleteDailyReportConfig(ctx, req.(*BatchDeleteDailyReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GenerateMessageContentById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMessageContentByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GenerateMessageContentById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DailyReportService/GenerateMessageContentById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GenerateMessageContentById(ctx, req.(*GenerateMessageContentByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DailyReportService_ServiceDesc is the grpc.ServiceDesc for DailyReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DailyReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.DailyReportService",
	HandlerType: (*DailyReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDailyReportConfig",
			Handler:    _DailyReportService_GetDailyReportConfig_Handler,
		},
		{
			MethodName: "ListDailyReportConfig",
			Handler:    _DailyReportService_ListDailyReportConfig_Handler,
		},
		{
			MethodName: "GetDailyReportSentResult",
			Handler:    _DailyReportService_GetDailyReportSentResult_Handler,
		},
		{
			MethodName: "UpsertDailyReportConfig",
			Handler:    _DailyReportService_UpsertDailyReportConfig_Handler,
		},
		{
			MethodName: "GetDailyReportSentHistory",
			Handler:    _DailyReportService_GetDailyReportSentHistory_Handler,
		},
		{
			MethodName: "GetDailyReportForCustomer",
			Handler:    _DailyReportService_GetDailyReportForCustomer_Handler,
		},
		{
			MethodName: "GenerateMessageContent",
			Handler:    _DailyReportService_GenerateMessageContent_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _DailyReportService_SendMessage_Handler,
		},
		{
			MethodName: "ListDailyReportConfigByFilter",
			Handler:    _DailyReportService_ListDailyReportConfigByFilter_Handler,
		},
		{
			MethodName: "BatchSendDailyDraftReport",
			Handler:    _DailyReportService_BatchSendDailyDraftReport_Handler,
		},
		{
			MethodName: "BatchDeleteDailyReportConfig",
			Handler:    _DailyReportService_BatchDeleteDailyReportConfig_Handler,
		},
		{
			MethodName: "GenerateMessageContentById",
			Handler:    _DailyReportService_GenerateMessageContentById_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/daily_report_service.proto",
}
