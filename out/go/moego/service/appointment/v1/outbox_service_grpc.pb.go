// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/outbox_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OutboxServiceClient is the client API for OutboxService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OutboxServiceClient interface {
	// 推送指定时间范围内的消息记录
	PushEvent(ctx context.Context, in *PushEventRequest, opts ...grpc.CallOption) (*PushEventResponse, error)
}

type outboxServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOutboxServiceClient(cc grpc.ClientConnInterface) OutboxServiceClient {
	return &outboxServiceClient{cc}
}

func (c *outboxServiceClient) PushEvent(ctx context.Context, in *PushEventRequest, opts ...grpc.CallOption) (*PushEventResponse, error) {
	out := new(PushEventResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.OutboxService/PushEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OutboxServiceServer is the server API for OutboxService service.
// All implementations must embed UnimplementedOutboxServiceServer
// for forward compatibility
type OutboxServiceServer interface {
	// 推送指定时间范围内的消息记录
	PushEvent(context.Context, *PushEventRequest) (*PushEventResponse, error)
	mustEmbedUnimplementedOutboxServiceServer()
}

// UnimplementedOutboxServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOutboxServiceServer struct {
}

func (UnimplementedOutboxServiceServer) PushEvent(context.Context, *PushEventRequest) (*PushEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushEvent not implemented")
}
func (UnimplementedOutboxServiceServer) mustEmbedUnimplementedOutboxServiceServer() {}

// UnsafeOutboxServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OutboxServiceServer will
// result in compilation errors.
type UnsafeOutboxServiceServer interface {
	mustEmbedUnimplementedOutboxServiceServer()
}

func RegisterOutboxServiceServer(s grpc.ServiceRegistrar, srv OutboxServiceServer) {
	s.RegisterService(&OutboxService_ServiceDesc, srv)
}

func _OutboxService_PushEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OutboxServiceServer).PushEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.OutboxService/PushEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OutboxServiceServer).PushEvent(ctx, req.(*PushEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OutboxService_ServiceDesc is the grpc.ServiceDesc for OutboxService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OutboxService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.OutboxService",
	HandlerType: (*OutboxServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushEvent",
			Handler:    _OutboxService_PushEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/outbox_service.proto",
}
