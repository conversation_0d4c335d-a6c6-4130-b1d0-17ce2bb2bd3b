// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/pet_detail_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Save or update appointment pet detail request
type SaveOrUpdatePetDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// DO NOT use this field, use `pet_details` instead.
	//
	// Deprecated: Do not use.
	PetDetail *v1.PetDetailDef `protobuf:"bytes,2,opt,name=pet_detail,json=petDetail,proto3,oneof" json:"pet_detail,omitempty"`
	// Multi pets update details
	// It contains all the services and add-on information selected for a single pet.
	// If it already exists, it will be overwritten with the latest data.
	// If it does not exist, the latest data will be directly inserted.
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,7,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *SaveOrUpdatePetDetailsRequest) Reset() {
	*x = SaveOrUpdatePetDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveOrUpdatePetDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveOrUpdatePetDetailsRequest) ProtoMessage() {}

func (x *SaveOrUpdatePetDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveOrUpdatePetDetailsRequest.ProtoReflect.Descriptor instead.
func (*SaveOrUpdatePetDetailsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{0}
}

func (x *SaveOrUpdatePetDetailsRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Deprecated: Do not use.
func (x *SaveOrUpdatePetDetailsRequest) GetPetDetail() *v1.PetDetailDef {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

func (x *SaveOrUpdatePetDetailsRequest) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *SaveOrUpdatePetDetailsRequest) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

func (x *SaveOrUpdatePetDetailsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SaveOrUpdatePetDetailsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SaveOrUpdatePetDetailsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Save or update appointment pet detail response
type SaveOrUpdatePetDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveOrUpdatePetDetailsResponse) Reset() {
	*x = SaveOrUpdatePetDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveOrUpdatePetDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveOrUpdatePetDetailsResponse) ProtoMessage() {}

func (x *SaveOrUpdatePetDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveOrUpdatePetDetailsResponse.ProtoReflect.Descriptor instead.
func (*SaveOrUpdatePetDetailsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{1}
}

// create pet detail request
type CreatePetDetailsForExtraOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra order id
	ExtraOrderId int64 `protobuf:"varint,1,opt,name=extra_order_id,json=extraOrderId,proto3" json:"extra_order_id,omitempty"`
	// Appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Selected pet and services
	// If it already exists, it will report an error.
	// If it does not exist, the latest data will be directly inserted.
	PetDetail *v1.PetDetailDef `protobuf:"bytes,3,opt,name=pet_detail,json=petDetail,proto3" json:"pet_detail,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CreatePetDetailsForExtraOrderRequest) Reset() {
	*x = CreatePetDetailsForExtraOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetDetailsForExtraOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetDetailsForExtraOrderRequest) ProtoMessage() {}

func (x *CreatePetDetailsForExtraOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetDetailsForExtraOrderRequest.ProtoReflect.Descriptor instead.
func (*CreatePetDetailsForExtraOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePetDetailsForExtraOrderRequest) GetExtraOrderId() int64 {
	if x != nil {
		return x.ExtraOrderId
	}
	return 0
}

func (x *CreatePetDetailsForExtraOrderRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *CreatePetDetailsForExtraOrderRequest) GetPetDetail() *v1.PetDetailDef {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

func (x *CreatePetDetailsForExtraOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// create pet detail response
type CreatePetDetailsForExtraOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail ids
	PetDetailIds []int64 `protobuf:"varint,1,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
}

func (x *CreatePetDetailsForExtraOrderResponse) Reset() {
	*x = CreatePetDetailsForExtraOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetDetailsForExtraOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetDetailsForExtraOrderResponse) ProtoMessage() {}

func (x *CreatePetDetailsForExtraOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetDetailsForExtraOrderResponse.ProtoReflect.Descriptor instead.
func (*CreatePetDetailsForExtraOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePetDetailsForExtraOrderResponse) GetPetDetailIds() []int64 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

// Delete appointment selected pet request
type DeletePetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// selected pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *DeletePetRequest) Reset() {
	*x = DeletePetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetRequest) ProtoMessage() {}

func (x *DeletePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetRequest.ProtoReflect.Descriptor instead.
func (*DeletePetRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeletePetRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *DeletePetRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DeletePetRequest) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

func (x *DeletePetRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeletePetRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeletePetRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Delete appointment selected pet response
type DeletePetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetResponse) Reset() {
	*x = DeletePetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetResponse) ProtoMessage() {}

func (x *DeletePetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetResponse.ProtoReflect.Descriptor instead.
func (*DeletePetResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{5}
}

// Delete appointment selected pet evaluation request
type DeletePetEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// selected evaluation service detail id
	EvaluationServiceDetailId int64 `protobuf:"varint,3,opt,name=evaluation_service_detail_id,json=evaluationServiceDetailId,proto3" json:"evaluation_service_detail_id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,4,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
}

func (x *DeletePetEvaluationRequest) Reset() {
	*x = DeletePetEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetEvaluationRequest) ProtoMessage() {}

func (x *DeletePetEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetEvaluationRequest.ProtoReflect.Descriptor instead.
func (*DeletePetEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeletePetEvaluationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeletePetEvaluationRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *DeletePetEvaluationRequest) GetEvaluationServiceDetailId() int64 {
	if x != nil {
		return x.EvaluationServiceDetailId
	}
	return 0
}

func (x *DeletePetEvaluationRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

// Delete appointment selected pet result
type DeletePetEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetEvaluationResponse) Reset() {
	*x = DeletePetEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetEvaluationResponse) ProtoMessage() {}

func (x *DeletePetEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetEvaluationResponse.ProtoReflect.Descriptor instead.
func (*DeletePetEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{7}
}

// Update upcoming appt pet details request
type UpdateUpcomingPetDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Updated service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	// The staff id of the staff who updated the service
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// location override rule
	LocationOverrideRule []*v11.LocationOverrideRule `protobuf:"bytes,4,rep,name=location_override_rule,json=locationOverrideRule,proto3" json:"location_override_rule,omitempty"`
}

func (x *UpdateUpcomingPetDetailsRequest) Reset() {
	*x = UpdateUpcomingPetDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingPetDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingPetDetailsRequest) ProtoMessage() {}

func (x *UpdateUpcomingPetDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingPetDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingPetDetailsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateUpcomingPetDetailsRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateUpcomingPetDetailsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateUpcomingPetDetailsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateUpcomingPetDetailsRequest) GetLocationOverrideRule() []*v11.LocationOverrideRule {
	if x != nil {
		return x.LocationOverrideRule
	}
	return nil
}

// Update upcoming appt pet details response
type UpdateUpcomingPetDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected appt count
	AffectedApptCount int64 `protobuf:"varint,1,opt,name=affected_appt_count,json=affectedApptCount,proto3" json:"affected_appt_count,omitempty"`
}

func (x *UpdateUpcomingPetDetailsResponse) Reset() {
	*x = UpdateUpcomingPetDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingPetDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingPetDetailsResponse) ProtoMessage() {}

func (x *UpdateUpcomingPetDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingPetDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingPetDetailsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateUpcomingPetDetailsResponse) GetAffectedApptCount() int64 {
	if x != nil {
		return x.AffectedApptCount
	}
	return 0
}

// get pet detail request
type GetPetDetailListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the appointment id
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// 是否返回转换后的静态日期信息。当前预约支持动态日期配置，且层级复杂。提供一个参数支持解析后返回。短期内暂时只支持 service 类型的 petDetail 解析
	WithActualDates *bool `protobuf:"varint,3,opt,name=with_actual_dates,json=withActualDates,proto3,oneof" json:"with_actual_dates,omitempty"`
}

func (x *GetPetDetailListRequest) Reset() {
	*x = GetPetDetailListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetDetailListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetDetailListRequest) ProtoMessage() {}

func (x *GetPetDetailListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetDetailListRequest.ProtoReflect.Descriptor instead.
func (*GetPetDetailListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetPetDetailListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetPetDetailListRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *GetPetDetailListRequest) GetWithActualDates() bool {
	if x != nil && x.WithActualDates != nil {
		return *x.WithActualDates
	}
	return false
}

// get pet detail request
type GetPetDetailListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	PetDetails []*v1.PetDetailModel `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// pet evaluation detail
	PetEvaluations []*v1.EvaluationServiceModel `protobuf:"bytes,2,rep,name=pet_evaluations,json=petEvaluations,proto3" json:"pet_evaluations,omitempty"`
}

func (x *GetPetDetailListResponse) Reset() {
	*x = GetPetDetailListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetDetailListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetDetailListResponse) ProtoMessage() {}

func (x *GetPetDetailListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetDetailListResponse.ProtoReflect.Descriptor instead.
func (*GetPetDetailListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetPetDetailListResponse) GetPetDetails() []*v1.PetDetailModel {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *GetPetDetailListResponse) GetPetEvaluations() []*v1.EvaluationServiceModel {
	if x != nil {
		return x.PetEvaluations
	}
	return nil
}

// Update pet detail request
type UpdatePetDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// grooming id
	GroomingId *int64 `protobuf:"varint,2,opt,name=grooming_id,json=groomingId,proto3,oneof" json:"grooming_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// service id
	ServiceId *int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3,oneof" json:"service_id,omitempty"`
	// date type
	DateType *v11.DateType `protobuf:"varint,28,opt,name=date_type,json=dateType,proto3,enum=moego.models.offering.v1.DateType,oneof" json:"date_type,omitempty"`
	// service type
	ServiceType *v11.ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"service_type,omitempty"`
	// service time, in minutes
	ServiceTime *int32 `protobuf:"varint,7,opt,name=service_time,json=serviceTime,proto3,oneof" json:"service_time,omitempty"`
	// service price
	ServicePrice *float64 `protobuf:"fixed64,8,opt,name=service_price,json=servicePrice,proto3,oneof" json:"service_price,omitempty"`
	// start time, in minutes
	StartTime *int32 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime *int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// status
	Status *v1.PetDetailStatus `protobuf:"varint,11,opt,name=status,proto3,enum=moego.models.appointment.v1.PetDetailStatus,oneof" json:"status,omitempty"`
	// scope type price
	ScopeTypePrice *v11.ServiceScopeType `protobuf:"varint,12,opt,name=scope_type_price,json=scopeTypePrice,proto3,enum=moego.models.offering.v1.ServiceScopeType,oneof" json:"scope_type_price,omitempty"`
	// scope type time
	ScopeTypeTime *v11.ServiceScopeType `protobuf:"varint,13,opt,name=scope_type_time,json=scopeTypeTime,proto3,enum=moego.models.offering.v1.ServiceScopeType,oneof" json:"scope_type_time,omitempty"`
	// star staff id
	StarStaffId *int64 `protobuf:"varint,14,opt,name=star_staff_id,json=starStaffId,proto3,oneof" json:"star_staff_id,omitempty"`
	// package service id
	PackageServiceId *int64 `protobuf:"varint,15,opt,name=package_service_id,json=packageServiceId,proto3,oneof" json:"package_service_id,omitempty"`
	// enable operation
	EnableOperation *bool `protobuf:"varint,16,opt,name=enable_operation,json=enableOperation,proto3,oneof" json:"enable_operation,omitempty"`
	// work mode, 0-parallel, 1-sequence
	WorkMode *v1.WorkMode `protobuf:"varint,17,opt,name=work_mode,json=workMode,proto3,enum=moego.models.appointment.v1.WorkMode,oneof" json:"work_mode,omitempty"`
	// service color code
	ServiceColorCode *string `protobuf:"bytes,18,opt,name=service_color_code,json=serviceColorCode,proto3,oneof" json:"service_color_code,omitempty"`
	// service start date, in yyyy-MM-dd format, for boarding or daycare service
	StartDate *string `protobuf:"bytes,19,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format, for boarding or daycare service
	EndDate *string `protobuf:"bytes,20,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType *v11.ServiceItemType `protobuf:"varint,26,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// lodging id, only for boarding service item type
	LodgingId *int64 `protobuf:"varint,21,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	// price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
	PriceUnit *int32 `protobuf:"varint,22,opt,name=price_unit,json=priceUnit,proto3,oneof" json:"price_unit,omitempty"`
	// add-on specific dates, yyyy-MM-dd
	SpecificDates *string `protobuf:"bytes,23,opt,name=specific_dates,json=specificDates,proto3,oneof" json:"specific_dates,omitempty"`
	// add-on associated service id
	AssociatedServiceId *int64 `protobuf:"varint,24,opt,name=associated_service_id,json=associatedServiceId,proto3,oneof" json:"associated_service_id,omitempty"`
	// price override type
	PriceOverrideType *v11.ServiceOverrideType `protobuf:"varint,25,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType *v11.ServiceOverrideType `protobuf:"varint,27,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"duration_override_type,omitempty"`
}

func (x *UpdatePetDetailRequest) Reset() {
	*x = UpdatePetDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetDetailRequest) ProtoMessage() {}

func (x *UpdatePetDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetDetailRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePetDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetGroomingId() int64 {
	if x != nil && x.GroomingId != nil {
		return *x.GroomingId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetServiceId() int64 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetDateType() v11.DateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v11.DateType(0)
}

func (x *UpdatePetDetailRequest) GetServiceType() v11.ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *UpdatePetDetailRequest) GetServiceTime() int32 {
	if x != nil && x.ServiceTime != nil {
		return *x.ServiceTime
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetServicePrice() float64 {
	if x != nil && x.ServicePrice != nil {
		return *x.ServicePrice
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetStatus() v1.PetDetailStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.PetDetailStatus(0)
}

func (x *UpdatePetDetailRequest) GetScopeTypePrice() v11.ServiceScopeType {
	if x != nil && x.ScopeTypePrice != nil {
		return *x.ScopeTypePrice
	}
	return v11.ServiceScopeType(0)
}

func (x *UpdatePetDetailRequest) GetScopeTypeTime() v11.ServiceScopeType {
	if x != nil && x.ScopeTypeTime != nil {
		return *x.ScopeTypeTime
	}
	return v11.ServiceScopeType(0)
}

func (x *UpdatePetDetailRequest) GetStarStaffId() int64 {
	if x != nil && x.StarStaffId != nil {
		return *x.StarStaffId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetPackageServiceId() int64 {
	if x != nil && x.PackageServiceId != nil {
		return *x.PackageServiceId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetEnableOperation() bool {
	if x != nil && x.EnableOperation != nil {
		return *x.EnableOperation
	}
	return false
}

func (x *UpdatePetDetailRequest) GetWorkMode() v1.WorkMode {
	if x != nil && x.WorkMode != nil {
		return *x.WorkMode
	}
	return v1.WorkMode(0)
}

func (x *UpdatePetDetailRequest) GetServiceColorCode() string {
	if x != nil && x.ServiceColorCode != nil {
		return *x.ServiceColorCode
	}
	return ""
}

func (x *UpdatePetDetailRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *UpdatePetDetailRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *UpdatePetDetailRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *UpdatePetDetailRequest) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetPriceUnit() int32 {
	if x != nil && x.PriceUnit != nil {
		return *x.PriceUnit
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetSpecificDates() string {
	if x != nil && x.SpecificDates != nil {
		return *x.SpecificDates
	}
	return ""
}

func (x *UpdatePetDetailRequest) GetAssociatedServiceId() int64 {
	if x != nil && x.AssociatedServiceId != nil {
		return *x.AssociatedServiceId
	}
	return 0
}

func (x *UpdatePetDetailRequest) GetPriceOverrideType() v11.ServiceOverrideType {
	if x != nil && x.PriceOverrideType != nil {
		return *x.PriceOverrideType
	}
	return v11.ServiceOverrideType(0)
}

func (x *UpdatePetDetailRequest) GetDurationOverrideType() v11.ServiceOverrideType {
	if x != nil && x.DurationOverrideType != nil {
		return *x.DurationOverrideType
	}
	return v11.ServiceOverrideType(0)
}

// Create pet detail request
type CreatePetDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming id, aka. appointment id
	GroomingId int64 `protobuf:"varint,1,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// date type
	DateType v11.DateType `protobuf:"varint,4,opt,name=date_type,json=dateType,proto3,enum=moego.models.offering.v1.DateType" json:"date_type,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,5,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	// use service's item type if not specified
	ServiceItemType *v11.ServiceItemType `protobuf:"varint,6,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// service type, use service's type if not specified
	ServiceType *v11.ServiceType `protobuf:"varint,7,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"service_type,omitempty"`
	// price unit, use service's price unit if not specified
	PriceUnit *v11.ServicePriceUnit `protobuf:"varint,8,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit,oneof" json:"price_unit,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,9,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// service time, in minutes
	ServiceTime *int32 `protobuf:"varint,10,opt,name=service_time,json=serviceTime,proto3,oneof" json:"service_time,omitempty"`
	// service price
	ServicePrice *float64 `protobuf:"fixed64,11,opt,name=service_price,json=servicePrice,proto3,oneof" json:"service_price,omitempty"`
	// start time, in minutes
	StartTime *int32 `protobuf:"varint,12,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime *int32 `protobuf:"varint,13,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// service start date, in yyyy-MM-dd format
	StartDate *string `protobuf:"bytes,14,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format
	EndDate *string `protobuf:"bytes,15,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// lodging id, only for boarding/daycare service item type
	LodgingId *int64 `protobuf:"varint,16,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	// add-on specific dates, yyyy-MM-dd
	SpecificDates []string `protobuf:"bytes,17,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// add-on associated service id
	AssociatedServiceId *int64 `protobuf:"varint,18,opt,name=associated_service_id,json=associatedServiceId,proto3,oneof" json:"associated_service_id,omitempty"`
}

func (x *CreatePetDetailRequest) Reset() {
	*x = CreatePetDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetDetailRequest) ProtoMessage() {}

func (x *CreatePetDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetDetailRequest.ProtoReflect.Descriptor instead.
func (*CreatePetDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreatePetDetailRequest) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *CreatePetDetailRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CreatePetDetailRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *CreatePetDetailRequest) GetDateType() v11.DateType {
	if x != nil {
		return x.DateType
	}
	return v11.DateType(0)
}

func (x *CreatePetDetailRequest) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *CreatePetDetailRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *CreatePetDetailRequest) GetServiceType() v11.ServiceType {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *CreatePetDetailRequest) GetPriceUnit() v11.ServicePriceUnit {
	if x != nil && x.PriceUnit != nil {
		return *x.PriceUnit
	}
	return v11.ServicePriceUnit(0)
}

func (x *CreatePetDetailRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CreatePetDetailRequest) GetServiceTime() int32 {
	if x != nil && x.ServiceTime != nil {
		return *x.ServiceTime
	}
	return 0
}

func (x *CreatePetDetailRequest) GetServicePrice() float64 {
	if x != nil && x.ServicePrice != nil {
		return *x.ServicePrice
	}
	return 0
}

func (x *CreatePetDetailRequest) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *CreatePetDetailRequest) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *CreatePetDetailRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *CreatePetDetailRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *CreatePetDetailRequest) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

func (x *CreatePetDetailRequest) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *CreatePetDetailRequest) GetAssociatedServiceId() int64 {
	if x != nil && x.AssociatedServiceId != nil {
		return *x.AssociatedServiceId
	}
	return 0
}

// Update pet detail response
type UpdatePetDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected count
	AffectedCount int64 `protobuf:"varint,1,opt,name=affected_count,json=affectedCount,proto3" json:"affected_count,omitempty"`
}

func (x *UpdatePetDetailResponse) Reset() {
	*x = UpdatePetDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetDetailResponse) ProtoMessage() {}

func (x *UpdatePetDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetDetailResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdatePetDetailResponse) GetAffectedCount() int64 {
	if x != nil {
		return x.AffectedCount
	}
	return 0
}

// Get pet detail request
type GetPetDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Optional company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetPetDetailRequest) Reset() {
	*x = GetPetDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetDetailRequest) ProtoMessage() {}

func (x *GetPetDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetDetailRequest.ProtoReflect.Descriptor instead.
func (*GetPetDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetPetDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPetDetailRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// Get pet detail response
type GetPetDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet detail
	Record *v1.PetDetailModel `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
}

func (x *GetPetDetailResponse) Reset() {
	*x = GetPetDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetDetailResponse) ProtoMessage() {}

func (x *GetPetDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetDetailResponse.ProtoReflect.Descriptor instead.
func (*GetPetDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetPetDetailResponse) GetRecord() *v1.PetDetailModel {
	if x != nil {
		return x.Record
	}
	return nil
}

// delete pet detail request
type DeletePetDetailForExtraOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	PetDetailId int64 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
}

func (x *DeletePetDetailForExtraOrderRequest) Reset() {
	*x = DeletePetDetailForExtraOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetDetailForExtraOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetDetailForExtraOrderRequest) ProtoMessage() {}

func (x *DeletePetDetailForExtraOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetDetailForExtraOrderRequest.ProtoReflect.Descriptor instead.
func (*DeletePetDetailForExtraOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{17}
}

func (x *DeletePetDetailForExtraOrderRequest) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

// delete peta detail
type DeletePetDetailForExtraOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *DeletePetDetailForExtraOrderResponse) Reset() {
	*x = DeletePetDetailForExtraOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetDetailForExtraOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetDetailForExtraOrderResponse) ProtoMessage() {}

func (x *DeletePetDetailForExtraOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetDetailForExtraOrderResponse.ProtoReflect.Descriptor instead.
func (*DeletePetDetailForExtraOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{18}
}

func (x *DeletePetDetailForExtraOrderResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// Update upcoming appointments request
type UpdateUpcomingAppointmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids，需要 apply to upcoming 的 business id 列表，空列表不会更新任何数据
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// old service, 修改之前的 service
	// update coming appointments 操作需要根据 old/new service 做一些逻辑判断
	OldService *v11.ServiceModel `protobuf:"bytes,2,opt,name=old_service,json=oldService,proto3" json:"old_service,omitempty"`
}

func (x *UpdateUpcomingAppointmentsRequest) Reset() {
	*x = UpdateUpcomingAppointmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingAppointmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingAppointmentsRequest) ProtoMessage() {}

func (x *UpdateUpcomingAppointmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingAppointmentsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingAppointmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateUpcomingAppointmentsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *UpdateUpcomingAppointmentsRequest) GetOldService() *v11.ServiceModel {
	if x != nil {
		return x.OldService
	}
	return nil
}

// Update upcoming appointments response
type UpdateUpcomingAppointmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUpcomingAppointmentsResponse) Reset() {
	*x = UpdateUpcomingAppointmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingAppointmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingAppointmentsResponse) ProtoMessage() {}

func (x *UpdateUpcomingAppointmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingAppointmentsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingAppointmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{20}
}

// get last pet detail request
type GetLastPetDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId []int64 `protobuf:"varint,2,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pet id
	PetId []int64 `protobuf:"varint,3,rep,packed,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// filter
	Filter *GetLastPetDetailRequest_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *GetLastPetDetailRequest) Reset() {
	*x = GetLastPetDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastPetDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastPetDetailRequest) ProtoMessage() {}

func (x *GetLastPetDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastPetDetailRequest.ProtoReflect.Descriptor instead.
func (*GetLastPetDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetLastPetDetailRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetLastPetDetailRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *GetLastPetDetailRequest) GetPetId() []int64 {
	if x != nil {
		return x.PetId
	}
	return nil
}

func (x *GetLastPetDetailRequest) GetFilter() *GetLastPetDetailRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// get last pet detail response
type GetLastPetDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	PetDetails []*v1.PetDetailModel `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *GetLastPetDetailResponse) Reset() {
	*x = GetLastPetDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastPetDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastPetDetailResponse) ProtoMessage() {}

func (x *GetLastPetDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastPetDetailResponse.ProtoReflect.Descriptor instead.
func (*GetLastPetDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetLastPetDetailResponse) GetPetDetails() []*v1.PetDetailModel {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// get staff pet detail request
type GetStaffPetDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company_id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business_id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// start time range
	StartTimeRange *interval.Interval `protobuf:"bytes,4,opt,name=start_time_range,json=startTimeRange,proto3" json:"start_time_range,omitempty"`
	// end_time_range
	EndTimeRange *interval.Interval `protobuf:"bytes,5,opt,name=end_time_range,json=endTimeRange,proto3" json:"end_time_range,omitempty"`
}

func (x *GetStaffPetDetailsRequest) Reset() {
	*x = GetStaffPetDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffPetDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffPetDetailsRequest) ProtoMessage() {}

func (x *GetStaffPetDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffPetDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetStaffPetDetailsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetStaffPetDetailsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetStaffPetDetailsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffPetDetailsRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetStaffPetDetailsRequest) GetStartTimeRange() *interval.Interval {
	if x != nil {
		return x.StartTimeRange
	}
	return nil
}

func (x *GetStaffPetDetailsRequest) GetEndTimeRange() *interval.Interval {
	if x != nil {
		return x.EndTimeRange
	}
	return nil
}

// get staffs pet detail response
type GetStaffPetDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	StaffPetDetails []*v1.StaffPetDetail `protobuf:"bytes,1,rep,name=staff_pet_details,json=staffPetDetails,proto3" json:"staff_pet_details,omitempty"`
}

func (x *GetStaffPetDetailsResponse) Reset() {
	*x = GetStaffPetDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffPetDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffPetDetailsResponse) ProtoMessage() {}

func (x *GetStaffPetDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffPetDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetStaffPetDetailsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetStaffPetDetailsResponse) GetStaffPetDetails() []*v1.StaffPetDetail {
	if x != nil {
		return x.StaffPetDetails
	}
	return nil
}

// The request message for listing pet services and surcharge
type ListPetServicesAndSurchargesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *ListPetServicesAndSurchargesRequest) Reset() {
	*x = ListPetServicesAndSurchargesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetServicesAndSurchargesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetServicesAndSurchargesRequest) ProtoMessage() {}

func (x *ListPetServicesAndSurchargesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetServicesAndSurchargesRequest.ProtoReflect.Descriptor instead.
func (*ListPetServicesAndSurchargesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListPetServicesAndSurchargesRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// The response message for listing pet services and surcharge
type ListPetServicesAndSurchargesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet services
	PetServices []*v12.PetService `protobuf:"bytes,1,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// surcharges
	Surcharges []*v12.SurchargeItem `protobuf:"bytes,2,rep,name=surcharges,proto3" json:"surcharges,omitempty"`
}

func (x *ListPetServicesAndSurchargesResponse) Reset() {
	*x = ListPetServicesAndSurchargesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetServicesAndSurchargesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetServicesAndSurchargesResponse) ProtoMessage() {}

func (x *ListPetServicesAndSurchargesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetServicesAndSurchargesResponse.ProtoReflect.Descriptor instead.
func (*ListPetServicesAndSurchargesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListPetServicesAndSurchargesResponse) GetPetServices() []*v12.PetService {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *ListPetServicesAndSurchargesResponse) GetSurcharges() []*v12.SurchargeItem {
	if x != nil {
		return x.Surcharges
	}
	return nil
}

// filter
type GetLastPetDetailRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment status, if is empty then return not canceled appointment
	Status []v1.AppointmentStatus `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// if no start time or end time, default is before current time
	// start time range
	StartTimeRange *interval.Interval `protobuf:"bytes,3,opt,name=start_time_range,json=startTimeRange,proto3,oneof" json:"start_time_range,omitempty"`
	// end_time_range
	EndTimeRange *interval.Interval `protobuf:"bytes,4,opt,name=end_time_range,json=endTimeRange,proto3,oneof" json:"end_time_range,omitempty"`
	// service item type
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,5,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// filter no start time, default include
	FilterNoStartTime *bool `protobuf:"varint,6,opt,name=filter_no_start_time,json=filterNoStartTime,proto3,oneof" json:"filter_no_start_time,omitempty"`
	// filter booking request, default include
	FilterBookingRequest *bool `protobuf:"varint,7,opt,name=filter_booking_request,json=filterBookingRequest,proto3,oneof" json:"filter_booking_request,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,8,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *GetLastPetDetailRequest_Filter) Reset() {
	*x = GetLastPetDetailRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastPetDetailRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastPetDetailRequest_Filter) ProtoMessage() {}

func (x *GetLastPetDetailRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastPetDetailRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetLastPetDetailRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP(), []int{21, 0}
}

func (x *GetLastPetDetailRequest_Filter) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetLastPetDetailRequest_Filter) GetStatus() []v1.AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLastPetDetailRequest_Filter) GetStartTimeRange() *interval.Interval {
	if x != nil {
		return x.StartTimeRange
	}
	return nil
}

func (x *GetLastPetDetailRequest_Filter) GetEndTimeRange() *interval.Interval {
	if x != nil {
		return x.EndTimeRange
	}
	return nil
}

func (x *GetLastPetDetailRequest_Filter) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *GetLastPetDetailRequest_Filter) GetFilterNoStartTime() bool {
	if x != nil && x.FilterNoStartTime != nil {
		return *x.FilterNoStartTime
	}
	return false
}

func (x *GetLastPetDetailRequest_Filter) GetFilterBookingRequest() bool {
	if x != nil && x.FilterBookingRequest != nil {
		return *x.FilterBookingRequest
	}
	return false
}

func (x *GetLastPetDetailRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

var File_moego_service_appointment_v1_pet_detail_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_pet_detail_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x04, 0x0a, 0x1d, 0x53, 0x61, 0x76,
	0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x0a, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52,
	0x09, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a,
	0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x1c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x22, 0x0a, 0x20,
	0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x20, 0x0a, 0x1e, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x81, 0x02, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x0a, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x09, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x25, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x32, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x73, 0x22, 0x90, 0x03, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x1c, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x42, 0x22, 0x0a, 0x20, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x48, 0x0a, 0x1c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x19, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x1d, 0x0a, 0x1b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x1f, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x73, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x5b, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x13, 0x61,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x11, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc1, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40, 0x01, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x2f, 0x0a, 0x11, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0f,
	0x77, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0xc6, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x95, 0x12, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x50, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x04, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x59, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x05, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x48, 0x07, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x08, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05,
	0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0a, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0b, 0x52, 0x0e,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x63, 0x0a, 0x0f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x0c, 0x52, 0x0d, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x0d, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x0e, 0x52,
	0x10, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x48, 0x0f,
	0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x10, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x48, 0x11,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x12, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x48, 0x13, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x66, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x14, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x15, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x48, 0x16, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xe8, 0x07, 0x48, 0x17, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x15, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x18, 0x52, 0x13, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x13, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10,
	0x01, 0x48, 0x19, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x72, 0x0a, 0x16, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x48, 0x1a, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x19,
	0x0a, 0x17, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xad, 0x0a, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x66, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5a,
	0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x03, 0x52, 0x09, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x04, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x48, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x06, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x07,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a,
	0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x0a, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x0b, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1f, 0xfa, 0x42, 0x1c,
	0x92, 0x01, 0x19, 0x22, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x15, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x0c, 0x52, 0x13, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x79, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42,
	0x18, 0x0a, 0x16, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x40, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6a, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x5b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x22, 0x52, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0d, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46, 0x6f, 0x72, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12,
	0x51, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x6f, 0x6c, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0x24, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x81, 0x07, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x73, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a,
	0xff, 0x04, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01,
	0x09, 0x18, 0x01, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x48, 0x01, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x02, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x03, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x16, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x14, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01,
	0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x68, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9a, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x22, 0x75, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x57, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x55, 0x0a, 0x23, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x41, 0x6e, 0x64,
	0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xbe, 0x01, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x41, 0x6e, 0x64, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0a, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x32, 0xe5, 0x0e, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93,
	0x01, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa5, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x46,
	0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f,
	0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x81, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x41, 0x6e, 0x64, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x41, 0x6e, 0x64, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x41, 0x6e, 0x64, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_appointment_v1_pet_detail_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_pet_detail_service_proto_rawDescData = file_moego_service_appointment_v1_pet_detail_service_proto_rawDesc
)

func file_moego_service_appointment_v1_pet_detail_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_pet_detail_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_pet_detail_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_pet_detail_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_pet_detail_service_proto_rawDescData
}

var file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_moego_service_appointment_v1_pet_detail_service_proto_goTypes = []interface{}{
	(*SaveOrUpdatePetDetailsRequest)(nil),         // 0: moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest
	(*SaveOrUpdatePetDetailsResponse)(nil),        // 1: moego.service.appointment.v1.SaveOrUpdatePetDetailsResponse
	(*CreatePetDetailsForExtraOrderRequest)(nil),  // 2: moego.service.appointment.v1.CreatePetDetailsForExtraOrderRequest
	(*CreatePetDetailsForExtraOrderResponse)(nil), // 3: moego.service.appointment.v1.CreatePetDetailsForExtraOrderResponse
	(*DeletePetRequest)(nil),                      // 4: moego.service.appointment.v1.DeletePetRequest
	(*DeletePetResponse)(nil),                     // 5: moego.service.appointment.v1.DeletePetResponse
	(*DeletePetEvaluationRequest)(nil),            // 6: moego.service.appointment.v1.DeletePetEvaluationRequest
	(*DeletePetEvaluationResponse)(nil),           // 7: moego.service.appointment.v1.DeletePetEvaluationResponse
	(*UpdateUpcomingPetDetailsRequest)(nil),       // 8: moego.service.appointment.v1.UpdateUpcomingPetDetailsRequest
	(*UpdateUpcomingPetDetailsResponse)(nil),      // 9: moego.service.appointment.v1.UpdateUpcomingPetDetailsResponse
	(*GetPetDetailListRequest)(nil),               // 10: moego.service.appointment.v1.GetPetDetailListRequest
	(*GetPetDetailListResponse)(nil),              // 11: moego.service.appointment.v1.GetPetDetailListResponse
	(*UpdatePetDetailRequest)(nil),                // 12: moego.service.appointment.v1.UpdatePetDetailRequest
	(*CreatePetDetailRequest)(nil),                // 13: moego.service.appointment.v1.CreatePetDetailRequest
	(*UpdatePetDetailResponse)(nil),               // 14: moego.service.appointment.v1.UpdatePetDetailResponse
	(*GetPetDetailRequest)(nil),                   // 15: moego.service.appointment.v1.GetPetDetailRequest
	(*GetPetDetailResponse)(nil),                  // 16: moego.service.appointment.v1.GetPetDetailResponse
	(*DeletePetDetailForExtraOrderRequest)(nil),   // 17: moego.service.appointment.v1.DeletePetDetailForExtraOrderRequest
	(*DeletePetDetailForExtraOrderResponse)(nil),  // 18: moego.service.appointment.v1.DeletePetDetailForExtraOrderResponse
	(*UpdateUpcomingAppointmentsRequest)(nil),     // 19: moego.service.appointment.v1.UpdateUpcomingAppointmentsRequest
	(*UpdateUpcomingAppointmentsResponse)(nil),    // 20: moego.service.appointment.v1.UpdateUpcomingAppointmentsResponse
	(*GetLastPetDetailRequest)(nil),               // 21: moego.service.appointment.v1.GetLastPetDetailRequest
	(*GetLastPetDetailResponse)(nil),              // 22: moego.service.appointment.v1.GetLastPetDetailResponse
	(*GetStaffPetDetailsRequest)(nil),             // 23: moego.service.appointment.v1.GetStaffPetDetailsRequest
	(*GetStaffPetDetailsResponse)(nil),            // 24: moego.service.appointment.v1.GetStaffPetDetailsResponse
	(*ListPetServicesAndSurchargesRequest)(nil),   // 25: moego.service.appointment.v1.ListPetServicesAndSurchargesRequest
	(*ListPetServicesAndSurchargesResponse)(nil),  // 26: moego.service.appointment.v1.ListPetServicesAndSurchargesResponse
	(*GetLastPetDetailRequest_Filter)(nil),        // 27: moego.service.appointment.v1.GetLastPetDetailRequest.Filter
	(*v1.PetDetailDef)(nil),                       // 28: moego.models.appointment.v1.PetDetailDef
	(v1.RepeatAppointmentModifyScope)(0),          // 29: moego.models.appointment.v1.RepeatAppointmentModifyScope
	(*v11.LocationOverrideRule)(nil),              // 30: moego.models.offering.v1.LocationOverrideRule
	(*v1.PetDetailModel)(nil),                     // 31: moego.models.appointment.v1.PetDetailModel
	(*v1.EvaluationServiceModel)(nil),             // 32: moego.models.appointment.v1.EvaluationServiceModel
	(v11.DateType)(0),                             // 33: moego.models.offering.v1.DateType
	(v11.ServiceType)(0),                          // 34: moego.models.offering.v1.ServiceType
	(v1.PetDetailStatus)(0),                       // 35: moego.models.appointment.v1.PetDetailStatus
	(v11.ServiceScopeType)(0),                     // 36: moego.models.offering.v1.ServiceScopeType
	(v1.WorkMode)(0),                              // 37: moego.models.appointment.v1.WorkMode
	(v11.ServiceItemType)(0),                      // 38: moego.models.offering.v1.ServiceItemType
	(v11.ServiceOverrideType)(0),                  // 39: moego.models.offering.v1.ServiceOverrideType
	(v11.ServicePriceUnit)(0),                     // 40: moego.models.offering.v1.ServicePriceUnit
	(*v11.ServiceModel)(nil),                      // 41: moego.models.offering.v1.ServiceModel
	(*interval.Interval)(nil),                     // 42: google.type.Interval
	(*v1.StaffPetDetail)(nil),                     // 43: moego.models.appointment.v1.StaffPetDetail
	(*v12.PetService)(nil),                        // 44: moego.models.fulfillment.v1.PetService
	(*v12.SurchargeItem)(nil),                     // 45: moego.models.fulfillment.v1.SurchargeItem
	(v1.AppointmentStatus)(0),                     // 46: moego.models.appointment.v1.AppointmentStatus
}
var file_moego_service_appointment_v1_pet_detail_service_proto_depIdxs = []int32{
	28, // 0: moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest.pet_detail:type_name -> moego.models.appointment.v1.PetDetailDef
	28, // 1: moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	29, // 2: moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	28, // 3: moego.service.appointment.v1.CreatePetDetailsForExtraOrderRequest.pet_detail:type_name -> moego.models.appointment.v1.PetDetailDef
	29, // 4: moego.service.appointment.v1.DeletePetRequest.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	30, // 5: moego.service.appointment.v1.UpdateUpcomingPetDetailsRequest.location_override_rule:type_name -> moego.models.offering.v1.LocationOverrideRule
	31, // 6: moego.service.appointment.v1.GetPetDetailListResponse.pet_details:type_name -> moego.models.appointment.v1.PetDetailModel
	32, // 7: moego.service.appointment.v1.GetPetDetailListResponse.pet_evaluations:type_name -> moego.models.appointment.v1.EvaluationServiceModel
	33, // 8: moego.service.appointment.v1.UpdatePetDetailRequest.date_type:type_name -> moego.models.offering.v1.DateType
	34, // 9: moego.service.appointment.v1.UpdatePetDetailRequest.service_type:type_name -> moego.models.offering.v1.ServiceType
	35, // 10: moego.service.appointment.v1.UpdatePetDetailRequest.status:type_name -> moego.models.appointment.v1.PetDetailStatus
	36, // 11: moego.service.appointment.v1.UpdatePetDetailRequest.scope_type_price:type_name -> moego.models.offering.v1.ServiceScopeType
	36, // 12: moego.service.appointment.v1.UpdatePetDetailRequest.scope_type_time:type_name -> moego.models.offering.v1.ServiceScopeType
	37, // 13: moego.service.appointment.v1.UpdatePetDetailRequest.work_mode:type_name -> moego.models.appointment.v1.WorkMode
	38, // 14: moego.service.appointment.v1.UpdatePetDetailRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	39, // 15: moego.service.appointment.v1.UpdatePetDetailRequest.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	39, // 16: moego.service.appointment.v1.UpdatePetDetailRequest.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	33, // 17: moego.service.appointment.v1.CreatePetDetailRequest.date_type:type_name -> moego.models.offering.v1.DateType
	38, // 18: moego.service.appointment.v1.CreatePetDetailRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	34, // 19: moego.service.appointment.v1.CreatePetDetailRequest.service_type:type_name -> moego.models.offering.v1.ServiceType
	40, // 20: moego.service.appointment.v1.CreatePetDetailRequest.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	31, // 21: moego.service.appointment.v1.GetPetDetailResponse.record:type_name -> moego.models.appointment.v1.PetDetailModel
	41, // 22: moego.service.appointment.v1.UpdateUpcomingAppointmentsRequest.old_service:type_name -> moego.models.offering.v1.ServiceModel
	27, // 23: moego.service.appointment.v1.GetLastPetDetailRequest.filter:type_name -> moego.service.appointment.v1.GetLastPetDetailRequest.Filter
	31, // 24: moego.service.appointment.v1.GetLastPetDetailResponse.pet_details:type_name -> moego.models.appointment.v1.PetDetailModel
	42, // 25: moego.service.appointment.v1.GetStaffPetDetailsRequest.start_time_range:type_name -> google.type.Interval
	42, // 26: moego.service.appointment.v1.GetStaffPetDetailsRequest.end_time_range:type_name -> google.type.Interval
	43, // 27: moego.service.appointment.v1.GetStaffPetDetailsResponse.staff_pet_details:type_name -> moego.models.appointment.v1.StaffPetDetail
	44, // 28: moego.service.appointment.v1.ListPetServicesAndSurchargesResponse.pet_services:type_name -> moego.models.fulfillment.v1.PetService
	45, // 29: moego.service.appointment.v1.ListPetServicesAndSurchargesResponse.surcharges:type_name -> moego.models.fulfillment.v1.SurchargeItem
	46, // 30: moego.service.appointment.v1.GetLastPetDetailRequest.Filter.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	42, // 31: moego.service.appointment.v1.GetLastPetDetailRequest.Filter.start_time_range:type_name -> google.type.Interval
	42, // 32: moego.service.appointment.v1.GetLastPetDetailRequest.Filter.end_time_range:type_name -> google.type.Interval
	38, // 33: moego.service.appointment.v1.GetLastPetDetailRequest.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	15, // 34: moego.service.appointment.v1.PetDetailService.GetPetDetail:input_type -> moego.service.appointment.v1.GetPetDetailRequest
	0,  // 35: moego.service.appointment.v1.PetDetailService.SaveOrUpdatePetDetails:input_type -> moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest
	2,  // 36: moego.service.appointment.v1.PetDetailService.CreatePetDetailsForExtraOrder:input_type -> moego.service.appointment.v1.CreatePetDetailsForExtraOrderRequest
	17, // 37: moego.service.appointment.v1.PetDetailService.DeletePetDetailForExtraOrder:input_type -> moego.service.appointment.v1.DeletePetDetailForExtraOrderRequest
	4,  // 38: moego.service.appointment.v1.PetDetailService.DeletePet:input_type -> moego.service.appointment.v1.DeletePetRequest
	6,  // 39: moego.service.appointment.v1.PetDetailService.DeletePetEvaluation:input_type -> moego.service.appointment.v1.DeletePetEvaluationRequest
	8,  // 40: moego.service.appointment.v1.PetDetailService.UpdateUpcomingPetDetails:input_type -> moego.service.appointment.v1.UpdateUpcomingPetDetailsRequest
	19, // 41: moego.service.appointment.v1.PetDetailService.UpdateUpcomingAppointments:input_type -> moego.service.appointment.v1.UpdateUpcomingAppointmentsRequest
	10, // 42: moego.service.appointment.v1.PetDetailService.GetPetDetailList:input_type -> moego.service.appointment.v1.GetPetDetailListRequest
	12, // 43: moego.service.appointment.v1.PetDetailService.UpdatePetDetail:input_type -> moego.service.appointment.v1.UpdatePetDetailRequest
	21, // 44: moego.service.appointment.v1.PetDetailService.GetLastPetDetail:input_type -> moego.service.appointment.v1.GetLastPetDetailRequest
	23, // 45: moego.service.appointment.v1.PetDetailService.GetStaffPetDetails:input_type -> moego.service.appointment.v1.GetStaffPetDetailsRequest
	25, // 46: moego.service.appointment.v1.PetDetailService.ListPetServicesAndSurcharges:input_type -> moego.service.appointment.v1.ListPetServicesAndSurchargesRequest
	16, // 47: moego.service.appointment.v1.PetDetailService.GetPetDetail:output_type -> moego.service.appointment.v1.GetPetDetailResponse
	1,  // 48: moego.service.appointment.v1.PetDetailService.SaveOrUpdatePetDetails:output_type -> moego.service.appointment.v1.SaveOrUpdatePetDetailsResponse
	3,  // 49: moego.service.appointment.v1.PetDetailService.CreatePetDetailsForExtraOrder:output_type -> moego.service.appointment.v1.CreatePetDetailsForExtraOrderResponse
	18, // 50: moego.service.appointment.v1.PetDetailService.DeletePetDetailForExtraOrder:output_type -> moego.service.appointment.v1.DeletePetDetailForExtraOrderResponse
	5,  // 51: moego.service.appointment.v1.PetDetailService.DeletePet:output_type -> moego.service.appointment.v1.DeletePetResponse
	7,  // 52: moego.service.appointment.v1.PetDetailService.DeletePetEvaluation:output_type -> moego.service.appointment.v1.DeletePetEvaluationResponse
	9,  // 53: moego.service.appointment.v1.PetDetailService.UpdateUpcomingPetDetails:output_type -> moego.service.appointment.v1.UpdateUpcomingPetDetailsResponse
	20, // 54: moego.service.appointment.v1.PetDetailService.UpdateUpcomingAppointments:output_type -> moego.service.appointment.v1.UpdateUpcomingAppointmentsResponse
	11, // 55: moego.service.appointment.v1.PetDetailService.GetPetDetailList:output_type -> moego.service.appointment.v1.GetPetDetailListResponse
	14, // 56: moego.service.appointment.v1.PetDetailService.UpdatePetDetail:output_type -> moego.service.appointment.v1.UpdatePetDetailResponse
	22, // 57: moego.service.appointment.v1.PetDetailService.GetLastPetDetail:output_type -> moego.service.appointment.v1.GetLastPetDetailResponse
	24, // 58: moego.service.appointment.v1.PetDetailService.GetStaffPetDetails:output_type -> moego.service.appointment.v1.GetStaffPetDetailsResponse
	26, // 59: moego.service.appointment.v1.PetDetailService.ListPetServicesAndSurcharges:output_type -> moego.service.appointment.v1.ListPetServicesAndSurchargesResponse
	47, // [47:60] is the sub-list for method output_type
	34, // [34:47] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_pet_detail_service_proto_init() }
func file_moego_service_appointment_v1_pet_detail_service_proto_init() {
	if File_moego_service_appointment_v1_pet_detail_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveOrUpdatePetDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveOrUpdatePetDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetDetailsForExtraOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetDetailsForExtraOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingPetDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingPetDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetDetailListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetDetailListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetDetailForExtraOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetDetailForExtraOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingAppointmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingAppointmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastPetDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastPetDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffPetDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffPetDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetServicesAndSurchargesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetServicesAndSurchargesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastPetDetailRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[21].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes[27].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_pet_detail_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_pet_detail_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_pet_detail_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_pet_detail_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_pet_detail_service_proto = out.File
	file_moego_service_appointment_v1_pet_detail_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_pet_detail_service_proto_goTypes = nil
	file_moego_service_appointment_v1_pet_detail_service_proto_depIdxs = nil
}
