create table if not exists cosid_machine
(
  name            text         primary key,
  namespace       text         not null,
  machine_id      integer      not null default 0,
  last_timestamp  bigint       not null default 0,
  instance_id     text         not null default '',
  distribute_time bigint       not null default 0,
  revert_time     bigint       not null default 0
);

create index if not exists idx_namespace on cosid_machine (namespace);
create index if not exists idx_instance_id on cosid_machine (instance_id);

comment on table cosid_machine is 'cosid machine table';
comment on column cosid_machine.name is 'cosid machine name, format is {namespace}.{machine_id}';
