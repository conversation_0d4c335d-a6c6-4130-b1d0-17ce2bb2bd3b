package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import org.junit.jupiter.api.Test;

public class CollectionUtilsTest {

    @Test
    public void testFirstElement() {
        assertNull(CollectionUtils.firstElement(null));
        assertNull(CollectionUtils.firstElement(List.of()));
        assertNull(CollectionUtils.firstElement(Set.of()));
        assertEquals(1, CollectionUtils.firstElement(List.of(1, 2)));
        assertEquals(1, CollectionUtils.firstElement(new TreeSet<>(List.of(1, 2))));

        System.out.println("--> test get first element OK!");
    }

    @Test
    public void testLastElement() {
        assertNull(CollectionUtils.lastElement(null));
        assertNull(CollectionUtils.lastElement(List.of()));
        assertNull(CollectionUtils.lastElement(Set.of()));
        assertEquals(2, CollectionUtils.lastElement(List.of(1, 2)));
        assertEquals(2, CollectionUtils.lastElement(new TreeSet<>(List.of(1, 2))));

        System.out.println("--> test get last element OK!");
    }

    @Test
    public void testConsumeInBatch() {
        var items = List.of(1, 2, 3, 4, 5, 6, 7);

        SumConsumer consumer = new SumConsumer();
        CollectionUtils.consumeInBatch(items, 0, consumer::accept);
        assertEquals(List.of(1, 2, 3, 4, 5, 6, 7), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 1, consumer::accept);
        assertEquals(List.of(1, 2, 3, 4, 5, 6, 7), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 2, consumer::accept);
        assertEquals(List.of(3, 7, 11, 7), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 3, consumer::accept);
        assertEquals(List.of(6, 15, 7), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 4, consumer::accept);
        assertEquals(List.of(10, 18), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 5, consumer::accept);
        assertEquals(List.of(15, 13), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 6, consumer::accept);
        assertEquals(List.of(21, 7), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 7, consumer::accept);
        assertEquals(List.of(28), consumer.result());

        consumer.clear();
        CollectionUtils.consumeInBatch(items, 8, consumer::accept);
        assertEquals(List.of(28), consumer.result());

        System.out.println("--> test consumeInBatch OK!");
    }

    @Test
    public void testSplitList() {
        var items = List.of(1, 2, 3, 4, 5, 6, 7);

        assertEquals(List.of(items), CollectionUtils.splitList(items, 0));
        assertEquals(List.of(items), CollectionUtils.splitList(items, 1));
        assertEquals(List.of(List.of(1, 2, 3, 4), List.of(5, 6, 7)), CollectionUtils.splitList(items, 2));
        assertEquals(List.of(List.of(1, 2, 3), List.of(4, 5), List.of(6, 7)), CollectionUtils.splitList(items, 3));
        assertEquals(
                List.of(List.of(1, 2), List.of(3, 4), List.of(5, 6), List.of(7)), CollectionUtils.splitList(items, 4));
        assertEquals(
                List.of(List.of(1, 2), List.of(3, 4), List.of(5), List.of(6), List.of(7)),
                CollectionUtils.splitList(items, 5));
        assertEquals(
                List.of(List.of(1, 2), List.of(3), List.of(4), List.of(5), List.of(6), List.of(7)),
                CollectionUtils.splitList(items, 6));
        assertEquals(items.stream().map(List::of).toList(), CollectionUtils.splitList(items, 7));
        assertEquals(items.stream().map(List::of).toList(), CollectionUtils.splitList(items, 8));

        System.out.println("--> test splitList OK!");
    }

    static class SumConsumer {
        private final List<Integer> total = new ArrayList<>();

        public void accept(List<Integer> items) {
            this.total.add(items.stream().mapToInt(Integer::intValue).sum());
        }

        public List<Integer> result() {
            return this.total;
        }

        public void clear() {
            this.total.clear();
        }
    }
}
