package com.moego.lib.utils;

import lombok.AllArgsConstructor;
import lombok.Data;

public class GeoUtils {
    private static final double ONE_DEGREE_LAT_IN_METERS = 111000; // 每一度纬度对应的米数
    private static final double EARTH_RADIUS_M = 6371000; // 地球半径，单位：米

    /**
     * 判断地理点是否在指定的矩形范围内。
     * @param rectangle 矩形
     * @param point 地理点
     * @return 地理点是否在范围内
     */
    public static boolean isPointInRectangle(Rectangle rectangle, Point point) {
        // 检查点是否在边界范围内
        return point.lat >= rectangle.minLat
                && point.lat <= rectangle.maxLat
                && point.lng >= rectangle.minLng
                && point.lng <= rectangle.maxLng;
    }

    /**
     * 计算两个经纬度点之间的地理直线距离（大圆距离），单位：米
     */
    public static double haversineDistance(Point a, Point b) {
        double lat1Rad = Math.toRadians(a.getLat());
        double lat2Rad = Math.toRadians(b.getLat());
        double deltaLat = Math.toRadians(b.getLat() - a.getLat());
        double deltaLon = Math.toRadians(b.getLng() - a.getLng());

        double haversine = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2)
                + Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(haversine), Math.sqrt(1 - haversine));

        return EARTH_RADIUS_M * c; // 返回米
    }

    @Data
    @AllArgsConstructor
    public static class Rectangle {
        private final double minLat;
        private final double maxLat;
        private final double minLng;
        private final double maxLng;

        // width 和 height 单位为米
        public static Rectangle from(Point center, double width, double height) {
            // 纬度偏移量（单位：度）
            double latOffset = height / 2.0 / ONE_DEGREE_LAT_IN_METERS;
            // 经度偏移量（单位：度）
            double lngOffset = width / 2.0 / (ONE_DEGREE_LAT_IN_METERS * Math.cos(Math.toRadians(center.lat)));
            // 矩形边界
            var minLat = center.lat - latOffset;
            var maxLat = center.lat + latOffset;
            var minLng = center.lng - lngOffset;
            var maxLng = center.lng + lngOffset;
            return new Rectangle(minLat, maxLat, minLng, maxLng);
        }
    }

    @Data
    @AllArgsConstructor
    public static class Point {
        private double lat;
        private double lng;
    }
}
