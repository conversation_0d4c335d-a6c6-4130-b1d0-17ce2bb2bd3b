package com.moego.lib.utils;

import java.io.File;
import java.io.IOException;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.DirectoryStream.Filter;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

public final class PathUtils {

    // extract file extensions from string
    public static String extensions(String file) {
        if (!StringUtils.isBlank(file)) {
            if (!file.endsWith("/") && !file.endsWith("\\")) {
                var filename = Path.of(file).getFileName();
                if (filename != null) {
                    return parseExtensions(filename.toString());
                }
            }
        }

        return "";
    }

    // extract file extensions from real file path, NOTE: this file path must actually exist.
    public static String extensions(File file) {
        return extensions(file.toPath());
    }

    // extract file extensions from real file path, NOTE: this file path must actually exist.
    public static String extensions(Path path) {
        if (path != null && Files.isRegularFile(path)) {
            var filename = path.getFileName();
            if (filename != null) {
                return parseExtensions(filename.toString());
            }
        }

        return "";
    }

    // parse extensions from string
    private static String parseExtensions(String filename) {
        var index = filename.lastIndexOf('.');
        if (0 < index && index < filename.length() - 1) {
            return filename.substring(index + 1);
        }

        return "";
    }

    // read entire file, return the bytes read
    public static byte[] readBytes(String filename) throws IOException {
        return readBytes(Path.of(filename));
    }

    // read entire file, return the bytes read
    public static byte[] readBytes(Path path) throws IOException {
        return Files.readAllBytes(path);
    }

    // read entire file, return the text read
    public static String readText(String filename) throws IOException {
        return readText(Path.of(filename));
    }

    // read entire file, return the text read
    public static String readText(Path path) throws IOException {
        return Files.readString(path, StandardCharsets.UTF_8);
    }

    // Read the file line by line, return the lines read
    public static List<String> readLines(String filename) throws IOException {
        return readLines(Path.of(filename));
    }

    // Read the file line by line, return the lines read
    public static List<String> readLines(Path path) throws IOException {
        return Files.readAllLines(path, StandardCharsets.UTF_8);
    }

    // write bytes to file, return the path of file
    public static Path writeFile(String file, byte[] data) throws IOException {
        return writeFile(Path.of(file), data);
    }

    // write bytes to file, return the path of file
    public static Path writeFile(File file, byte[] data) throws IOException {
        return writeFile(file.toPath(), data);
    }

    // write bytes to file, return the path of file
    public static Path writeFile(Path file, byte[] data) throws IOException {
        createParentDirectories(file);
        return Files.write(file, data);
    }

    // write text to file, return the path of file
    public static Path writeFile(String file, String text) throws IOException {
        return writeFile(Path.of(file), text);
    }

    // write text to file, return the path of file
    public static Path writeFile(File file, String text) throws IOException {
        return writeFile(file.toPath(), text);
    }

    // write text to file, return the path of file
    public static Path writeFile(Path file, String text) throws IOException {
        createParentDirectories(file);
        return Files.writeString(file, text, StandardCharsets.UTF_8);
    }

    // write lines to file, return the number of rows written
    public static int writeLines(String file, List<String> lines) throws IOException {
        return writeLines(Path.of(file), lines);
    }

    // write lines to file, return the number of rows written
    public static int writeLines(File file, List<String> lines) throws IOException {
        return writeLines(file.toPath(), lines);
    }

    // write lines to file, return the number of rows written
    public static int writeLines(Path file, List<String> lines) throws IOException {
        int n = 0;
        if (lines != null && !lines.isEmpty()) {
            createParentDirectories(file);
            try (var ps = new PrintStream(file.toFile(), StandardCharsets.UTF_8)) {
                for (var line : lines) {
                    ps.println(line);
                    ++n;
                }
            }
        }

        return n;
    }

    private static Path createParentDirectories(Path path) throws IOException {
        var parent = path.getParent();
        if (parent != null) {
            return Files.createDirectories(parent);
        }

        return null;
    }

    /**
     * delete a path
     * if path is a file, delete the file
     * if path is a directory, delete a directory and its subdirectories recursively
     * return the number of deleted path
     */
    public static int deletePath(String path) {
        if (StringUtils.isBlank(path)) {
            return 0;
        }

        return deletePath(new File(path));
    }

    public static int deletePath(File path) {
        if (!path.exists()) {
            return 0;
        }

        if (path.isDirectory()) {
            return deleteDirectory(path);
        } else {
            return path.delete() ? 1 : 0;
        }
    }

    public static int deletePath(Path path) {
        return deletePath(path.toFile());
    }

    private static int deleteDirectory(File dir) {
        int n = 0;
        File[] fs = dir.listFiles();
        if (fs != null) {
            for (File f : fs) {
                if (f.isDirectory()) {
                    n += deleteDirectory(f);
                } else {
                    if (f.delete()) {
                        ++n;
                    }
                }
            }
        }

        if (dir.delete()) {
            ++n;
        }

        return n;
    }

    /**
     * Traverse all lines of the file using a method
     * Returns the number of lines in the file
     */
    public static long walkLines(String file, BiConsumer<Long, String> consumer) throws IOException {
        return walkLines(Path.of(file), consumer);
    }

    public static long walkLines(File file, BiConsumer<Long, String> consumer) throws IOException {
        return walkLines(file.toPath(), consumer);
    }

    public static long walkLines(Path file, BiConsumer<Long, String> consumer) throws IOException {
        try (var reader = Files.newBufferedReader(file, StandardCharsets.UTF_8)) {
            long n = 0;
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                }
                consumer.accept(n, line);
                ++n;
            }

            return n;
        }
    }

    /**
     * depth-first traverses the file tree with the given starting path as the root,
     * and performs the processor operation on each traversed path
     * return the number of traversed path
     */
    public static long walkFiles(String path, Consumer<Path> processor) throws IOException {
        return walkFiles(Path.of(path), processor);
    }

    public static long walkFiles(File path, Consumer<Path> processor) throws IOException {
        return walkFiles(path.toPath(), processor);
    }

    public static long walkFiles(Path path, Consumer<Path> processor) throws IOException {
        return walkFiles(path, Integer.MAX_VALUE, processor);
    }

    // same as 'walkFiles' function above, but use 'maxDepth' parameter to specify maximum traversal depth
    public static long walkFiles(String path, int maxDepth, Consumer<Path> processor) throws IOException {
        return walkFiles(Path.of(path), maxDepth, processor);
    }

    public static long walkFiles(File path, int maxDepth, Consumer<Path> processor) throws IOException {
        return walkFiles(path.toPath(), maxDepth, processor);
    }

    public static long walkFiles(Path path, int maxDepth, Consumer<Path> processor) throws IOException {
        if (maxDepth < 1) {
            return 0;
        }

        return walkFiles(path, maxDepth, 1, processor);
    }

    /**
     * depth-first traverses the file tree with the given starting path as the root,
     * filters each traversed path with filter, and then performs processor operation
     * return the number of traversed path
     */
    public static long walkFiles(String path, Filter<? super Path> filter, Consumer<Path> processor)
            throws IOException {
        return walkFiles(Path.of(path), filter, processor);
    }

    public static long walkFiles(File path, Filter<? super Path> filter, Consumer<Path> processor) throws IOException {
        return walkFiles(path.toPath(), filter, processor);
    }

    public static long walkFiles(Path path, Filter<? super Path> filter, Consumer<Path> processor) throws IOException {
        return walkFiles(path, Integer.MAX_VALUE, filter, processor);
    }

    // same as 'walkFiles' function above, but use 'maxDepth' parameter to specify maximum traversal depth
    public static long walkFiles(String path, int maxDepth, Filter<? super Path> filter, Consumer<Path> processor)
            throws IOException {
        return walkFiles(Path.of(path), maxDepth, filter, processor);
    }

    public static long walkFiles(File path, int maxDepth, Filter<? super Path> filter, Consumer<Path> processor)
            throws IOException {
        return walkFiles(path.toPath(), maxDepth, filter, processor);
    }

    public static long walkFiles(Path path, int maxDepth, Filter<? super Path> filter, Consumer<Path> processor)
            throws IOException {
        if (maxDepth < 1) {
            return 0;
        }

        return walkFiles(path, maxDepth, 1, filter, processor);
    }

    /**
     * depth-first traverses the file tree with the given starting path as the root,
     * filters each traversed path with glob expression, and then performs processor operation
     * return the number of traversed path
     */
    public static long walkFiles(String path, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        return walkFiles(Path.of(path), glob, includeEmpty, processor);
    }

    public static long walkFiles(File path, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        return walkFiles(path.toPath(), glob, includeEmpty, processor);
    }

    public static long walkFiles(Path path, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        return walkFiles(path, Integer.MAX_VALUE, glob, includeEmpty, processor);
    }

    // same as 'walkFiles' function above, but use 'maxDepth' parameter to specify maximum traversal depth
    public static long walkFiles(String path, int maxDepth, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        return walkFiles(Path.of(path), maxDepth, glob, includeEmpty, processor);
    }

    public static long walkFiles(File path, int maxDepth, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        return walkFiles(path.toPath(), maxDepth, glob, includeEmpty, processor);
    }

    public static long walkFiles(Path path, int maxDepth, String glob, boolean includeEmpty, Consumer<Path> processor)
            throws IOException {
        if (maxDepth < 1) {
            return 0;
        }

        var matcher = FileSystems.getDefault().getPathMatcher("glob:" + glob);
        var filter = new Filter<Path>() {
            @Override
            public boolean accept(Path entry) throws IOException {
                return (includeEmpty || 0 < Files.size(entry)) && matcher.matches(path.relativize(entry));
            }
        };

        return walkFiles(path, maxDepth, 1, filter, processor);
    }

    private static long walkFiles(
            Path path, int maxDepth, int currDepth, Filter<? super Path> filter, Consumer<Path> processor)
            throws IOException {
        long n = 0;
        if (Files.isDirectory(path)) {
            try (var stream = Files.newDirectoryStream(path)) {
                for (var p : stream) {
                    if (Files.isDirectory(p)) {
                        if (currDepth < maxDepth) {
                            n += walkFiles(p, maxDepth, currDepth + 1, filter, processor);
                        }
                    }
                    if (Files.isRegularFile(p)) {
                        if (filter.accept(p)) {
                            processor.accept(p);
                            ++n;
                        }
                    }
                }
            }

            return n;
        }

        if (Files.isRegularFile(path)) {
            if (filter.accept(path)) {
                processor.accept(path);
                ++n;
            }
        }

        return n;
    }

    private static long walkFiles(Path path, int maxDepth, int currDepth, Consumer<Path> processor) throws IOException {
        long n = 0;
        if (Files.isDirectory(path)) {
            try (var stream = Files.newDirectoryStream(path)) {
                for (var p : stream) {
                    if (Files.isDirectory(p)) {
                        if (currDepth < maxDepth) {
                            n += walkFiles(p, maxDepth, currDepth + 1, processor);
                        }
                    }
                    if (Files.isRegularFile(p)) {
                        processor.accept(p);
                        ++n;
                    }
                }
            }

            return n;
        }

        if (Files.isRegularFile(path)) {
            processor.accept(path);
            ++n;
        }

        return n;
    }
}
