package com.moego.lib.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public final class DateTimeUtils {

    // get the current timestamp
    public static long now() {
        return System.currentTimeMillis();
    }

    // get the current time in string
    public static String now(String pattern) {
        return now(pattern, ZoneId.systemDefault());
    }

    // get the current time in string
    public static String now(String pattern, String zoneId) {
        return now(pattern, ZoneId.of(zoneId));
    }

    // get the current time of the specified time zone in string
    public static String now(String pattern, ZoneId zoneId) {
        return toString(now(), pattern, zoneId);
    }

    // get the current time of the specified time zone in string
    public static String now(String pattern, int offset) {
        return now(pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // get the current time of the specified time zone in string
    public static String now(String pattern, ZoneOffset offset) {
        return toString(now(), pattern, offset);
    }

    // LocalDateTime to milliseconds
    public static long toMilliseconds(LocalDateTime datetime) {
        return toInstant(datetime).toEpochMilli();
    }

    // ZonedDateTime to milliseconds
    public static long toMilliseconds(ZonedDateTime datetime) {
        return datetime.toInstant().toEpochMilli();
    }

    // OffsetDateTime to milliseconds
    public static long toMilliseconds(OffsetDateTime datetime) {
        return datetime.toInstant().toEpochMilli();
    }

    // LocalDateTime to milliseconds with offset in seconds
    public static long toMilliseconds(LocalDateTime datetime, int offset) {
        return toMilliseconds(datetime, ZoneOffset.ofTotalSeconds(offset));
    }

    // LocalDateTime to milliseconds with ZoneOffset
    public static long toMilliseconds(LocalDateTime datetime, ZoneOffset offset) {
        return datetime.toInstant(offset).toEpochMilli();
    }

    // LocalDateTime to milliseconds with ZoneId name
    public static long toMilliseconds(LocalDateTime datetime, String zoneId) {
        return toMilliseconds(datetime, ZoneId.of(zoneId));
    }

    // LocalDateTime to milliseconds with ZoneId
    public static long toMilliseconds(LocalDateTime datetime, ZoneId zoneId) {
        return toMilliseconds(datetime.atZone(zoneId));
    }

    // DateTime string to milliseconds with default
    public static long toMilliseconds(String text, String pattern) {
        return toMilliseconds(text, pattern, ZoneId.systemDefault());
    }

    // DateTime string to milliseconds with ZoneId name
    public static long toMilliseconds(String text, String pattern, String zoneId) {
        return toMilliseconds(text, pattern, ZoneId.of(zoneId));
    }

    // DateTime string to milliseconds with ZoneId
    public static long toMilliseconds(String text, String pattern, ZoneId zoneId) {
        return toInstant(text, pattern, zoneId).toEpochMilli();
    }

    // DateTime string to milliseconds with offset in seconds
    public static long toMilliseconds(String text, String pattern, int offset) {
        return toMilliseconds(text, pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // DateTime string to milliseconds with ZoneOffset
    public static long toMilliseconds(String text, String pattern, ZoneOffset offset) {
        return toInstant(text, pattern, offset).toEpochMilli();
    }

    // LocalDateTime to Instant with default
    public static Instant toInstant(LocalDateTime datetime) {
        return toInstant(datetime, ZoneId.systemDefault());
    }

    // LocalDateTime to Instant with ZoneId name
    public static Instant toInstant(LocalDateTime datetime, String zoneId) {
        return toInstant(datetime, ZoneId.of(zoneId));
    }

    // LocalDateTime to Instant with ZoneId
    public static Instant toInstant(LocalDateTime datetime, ZoneId zoneId) {
        return datetime.atZone(zoneId).toInstant();
    }

    // LocalDateTime to Instant with offset in seconds
    public static Instant toInstant(LocalDateTime datetime, int offset) {
        return toInstant(datetime, ZoneOffset.ofTotalSeconds(offset));
    }

    // LocalDateTime to Instant with ZoneOffset
    public static Instant toInstant(LocalDateTime datetime, ZoneOffset offset) {
        return datetime.toInstant(offset);
    }

    // DateTime string to Instant with default
    public static Instant toInstant(String text, String pattern) {
        return toInstant(text, pattern, ZoneId.systemDefault());
    }

    // DateTime string to Instant with ZoneId name
    public static Instant toInstant(String text, String pattern, String zoneId) {
        return toInstant(text, pattern, ZoneId.of(zoneId));
    }

    // DateTime string to Instant with ZoneId
    public static Instant toInstant(String text, String pattern, ZoneId zoneId) {
        return toDateTime(text, pattern, zoneId).toInstant();
    }

    // DateTime string to Instant with offset in seconds
    public static Instant toInstant(String text, String pattern, int offset) {
        return toInstant(text, pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // DateTime string to Instant with ZoneOffset
    public static Instant toInstant(String text, String pattern, ZoneOffset offset) {
        return toDateTime(text, pattern, offset).toInstant();
    }

    // milliseconds to LocalDateTime
    public static LocalDateTime toDateTime(long t) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(t), ZoneId.systemDefault());
    }

    // milliseconds to ZonedDateTime
    public static ZonedDateTime toDateTime(long t, String zoneId) {
        return toDateTime(t, ZoneId.of(zoneId));
    }

    // milliseconds to ZonedDateTime
    public static ZonedDateTime toDateTime(long t, ZoneId zoneId) {
        return Instant.ofEpochMilli(t).atZone(zoneId);
    }

    // milliseconds to OffsetDateTime with offset in seconds
    public static OffsetDateTime toDateTime(long t, int offset) {
        return toDateTime(t, ZoneOffset.ofTotalSeconds(offset));
    }

    // milliseconds to OffsetDateTime
    public static OffsetDateTime toDateTime(long t, ZoneOffset offset) {
        return Instant.ofEpochMilli(t).atOffset(offset);
    }

    // DateTime string to LocalDateTime
    public static LocalDateTime toDateTime(String text, String pattern) {
        return LocalDateTime.parse(text, DateTimeFormatter.ofPattern(pattern));
    }

    // DateTime string to ZonedDateTime
    public static ZonedDateTime toDateTime(String text, String pattern, String zoneId) {
        return toDateTime(text, pattern, ZoneId.of(zoneId));
    }

    // DateTime string to ZonedDateTime
    public static ZonedDateTime toDateTime(String text, String pattern, ZoneId zoneId) {
        return toDateTime(text, pattern).atZone(zoneId);
    }

    // DateTime string to OffsetDateTime with offset in seconds
    public static OffsetDateTime toDateTime(String text, String pattern, int offset) {
        return toDateTime(text, pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // DateTime string to OffsetDateTime
    public static OffsetDateTime toDateTime(String text, String pattern, ZoneOffset offset) {
        return toDateTime(text, pattern).atOffset(offset);
    }

    // milliseconds to DateTime string with default zone
    public static String toString(long t, String pattern) {
        return toString(t, pattern, ZoneId.systemDefault());
    }

    // milliseconds to DateTime string with ZoneId name
    public static String toString(long t, String pattern, String zoneId) {
        return toString(t, pattern, ZoneId.of(zoneId));
    }

    // milliseconds to DateTime string with ZoneId
    public static String toString(long t, String pattern, ZoneId zoneId) {
        return toDateTime(t, zoneId).format(DateTimeFormatter.ofPattern(pattern));
    }

    // milliseconds to DateTime string with offset in seconds
    public static String toString(long t, String pattern, int offset) {
        return toString(t, pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // milliseconds to DateTime string with ZoneOffset
    public static String toString(long t, String pattern, ZoneOffset offset) {
        return toDateTime(t, offset).format(DateTimeFormatter.ofPattern(pattern));
    }

    // LocalDateTime to DateTime string with default zone
    public static String toString(LocalDateTime datetime, String pattern) {
        return toString(datetime, pattern, ZoneId.systemDefault());
    }

    // LocalDateTime to DateTime string with ZoneId name
    public static String toString(LocalDateTime datetime, String pattern, String zoneId) {
        return toString(datetime, pattern, ZoneId.of(zoneId));
    }

    // LocalDateTime to DateTime string with ZoneId
    public static String toString(LocalDateTime datetime, String pattern, ZoneId zoneId) {
        return ZonedDateTime.of(datetime, zoneId).format(DateTimeFormatter.ofPattern(pattern));
    }

    // LocalDateTime to DateTime string with offset in seconds
    public static String toString(LocalDateTime datetime, String pattern, int offset) {
        return toString(datetime, pattern, ZoneOffset.ofTotalSeconds(offset));
    }

    // LocalDateTime to DateTime string with ZoneOffset
    public static String toString(LocalDateTime datetime, String pattern, ZoneOffset offset) {
        return OffsetDateTime.of(datetime, offset).format(DateTimeFormatter.ofPattern(pattern));
    }

    // DateTime string to DateTime string
    public static String toString(String text, String fromPattern, String toPattern) {
        return toDateTime(text, fromPattern).format(DateTimeFormatter.ofPattern(toPattern));
    }

    // DateTime string to DateTime string
    public static String toString(String text, String fromPattern, ZoneId fromZone, String toPattern, ZoneId toZone) {
        return toDateTime(text, fromPattern, fromZone)
                .withZoneSameInstant(toZone)
                .format(DateTimeFormatter.ofPattern(toPattern));
    }

    // DateTime string to DateTime string
    public static String toString(String text, String fromPattern, String fromZone, String toPattern, String toZone) {
        return toString(text, fromPattern, ZoneId.of(fromZone), toPattern, ZoneId.of(toZone));
    }

    // DateTime string to DateTime string
    public static String toString(
            String text, String fromPattern, ZoneOffset fromOffset, String toPattern, ZoneOffset toOffset) {
        return toDateTime(text, fromPattern, fromOffset)
                .withOffsetSameInstant(toOffset)
                .format(DateTimeFormatter.ofPattern(toPattern));
    }

    // DateTime string to DateTime string with offset in seconds
    public static String toString(String text, String fromPattern, int fromOffset, String toPattern, int toOffset) {
        return toString(
                text,
                fromPattern,
                ZoneOffset.ofTotalSeconds(fromOffset),
                toPattern,
                ZoneOffset.ofTotalSeconds(toOffset));
    }
}
