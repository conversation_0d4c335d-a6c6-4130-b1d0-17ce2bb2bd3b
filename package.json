{"name": "@moego/api-definitions", "version": "1.109.0", "main": "index.js", "private": true, "repository": "**************:MoeGolibrary/moego-api-definitions.git", "publishConfig": {"registry": "https://nexus.devops.moego.pet/repository/npm-local/"}, "license": "UNLICENSED", "author": "junbao <<EMAIL>>", "files": [], "devDependencies": {"@bufbuild/protoc-gen-connect-es": "^0.11.0", "@bufbuild/protoc-gen-es": "^1.10.0", "@commitlint/cli": "^17.7.2", "@commitlint/config-angular": "^17.7.0", "dayjs": "^1.11.10", "git-branch-is": "^4.0.0", "glob": "^10.3.10", "husky": "^8.0.3", "inquirer-autocomplete-prompt": "^3.0.1", "js-yaml": "^4.1.0", "lint-staged": "^14.0.1", "npm-cli-login": "^1.0.0", "npm-run-all": "^4.1.5", "patch-package": "^8.0.0", "plop": "^4.0.0", "prettier": "^3.0.3", "protobufjs": "^7.4.0", "ts-morph": "^25.0.1", "ts-proto": "1.160.0"}, "scripts": {"prepare": "husky install", "check:format": "prettier --check .", "check:commit": "commitlint --from origin/main", "check": "run-p check:*", "postinstall": "patch-package", "sync-applications": "node ci/sync-node-bff-applications.mjs"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72", "dependencies": {"@bufbuild/protobuf": "^2.2.3", "@protobuf-ts/plugin": "^2.9.4", "@types/google-protobuf": "^3.15.12", "google-protobuf": "^3.21.4", "protoc-gen-grpc-js": "^0.4.0", "ts-protoc-gen": "^0.15.0"}}