name: validate

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches:
      - 'master'
      - 'main'
    tags:
      - 'v*'
  pull_request:

permissions:
  contents: read

jobs:
  linters:
    strategy:
      matrix:
        go-version: [1.23.x]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    timeout-minutes: 10
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go-version }}
      - uses: actions/checkout@v4
      - name: prepare generated code
        run: make prepare
      - name: lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.60.3
          args: --print-resources-usage --timeout=10m --verbose
