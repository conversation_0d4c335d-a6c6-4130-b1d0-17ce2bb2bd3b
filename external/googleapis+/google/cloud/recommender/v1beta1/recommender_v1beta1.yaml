type: google.api.Service
config_version: 3
name: recommender.googleapis.com
title: Recommender API

apis:
- name: google.cloud.recommender.v1beta1.Recommender

backend:
  rules:
  - selector: 'google.cloud.recommender.v1beta1.Recommender.*'
    deadline: 3.0

authentication:
  rules:
  - selector: 'google.cloud.recommender.v1beta1.Recommender.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
