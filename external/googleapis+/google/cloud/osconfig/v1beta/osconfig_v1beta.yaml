type: google.api.Service
config_version: 3
name: osconfig.googleapis.com
title: OS Config API

apis:
- name: google.cloud.osconfig.v1beta.OsConfigService

documentation:
  summary: |-
    OS management tools that can be used for patch management, patch
    compliance, and configuration management on VM instances.

backend:
  rules:
  - selector: 'google.cloud.osconfig.v1beta.OsConfigService.*'
    deadline: 30.0

authentication:
  rules:
  - selector: 'google.cloud.osconfig.v1beta.OsConfigService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.CancelOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
