type: google.api.Service
config_version: 3
name: composer.googleapis.com
title: Cloud Composer API

apis:
- name: google.cloud.orchestration.airflow.service.v1.Environments
- name: google.cloud.orchestration.airflow.service.v1.ImageVersions
- name: google.longrunning.Operations

types:
- name: google.cloud.orchestration.airflow.service.v1.CheckUpgradeResponse
- name: google.cloud.orchestration.airflow.service.v1.DatabaseFailoverResponse
- name: google.cloud.orchestration.airflow.service.v1.ExecuteAirflowCommandResponse
- name: google.cloud.orchestration.airflow.service.v1.FetchDatabasePropertiesResponse
- name: google.cloud.orchestration.airflow.service.v1.ListWorkloadsResponse
- name: google.cloud.orchestration.airflow.service.v1.LoadSnapshotResponse
- name: google.cloud.orchestration.airflow.service.v1.OperationMetadata
- name: google.cloud.orchestration.airflow.service.v1.PollAirflowCommandResponse
- name: google.cloud.orchestration.airflow.service.v1.SaveSnapshotResponse
- name: google.cloud.orchestration.airflow.service.v1.StopAirflowCommandResponse

documentation:
  summary: Manages Apache Airflow environments on Google Cloud Platform.

http:
  rules:
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.orchestration.airflow.service.v1.Environments.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.orchestration.airflow.service.v1.ImageVersions.ListImageVersions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
