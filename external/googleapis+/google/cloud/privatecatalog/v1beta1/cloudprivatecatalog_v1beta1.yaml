type: google.api.Service
config_version: 3
name: cloudprivatecatalog.googleapis.com
title: Cloud Private Catalog API

apis:
- name: google.cloud.privatecatalog.v1beta1.PrivateCatalog

documentation:
  summary: |-
    Enable cloud users to discover private catalogs and products in their
    organizations.
  overview: '# TODO(hongyes): unsuppress warnings before beta launch.'

authentication:
  rules:
  - selector: 'google.cloud.privatecatalog.v1beta1.PrivateCatalog.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
