type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
- name: google.cloud.config.v1.Config
  methods:
  - name: CreateDeployment
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
  - name: UpdateDeployment
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
  - name: DeleteDeployment
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
  - name: CreatePreview
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
  - name: DeletePreview
    long_running:
      initial_poll_delay_millis: 300000
      max_poll_delay_millis: 3600000
      poll_delay_multiplier: 1.25
      total_poll_timeout_millis: 43200000
