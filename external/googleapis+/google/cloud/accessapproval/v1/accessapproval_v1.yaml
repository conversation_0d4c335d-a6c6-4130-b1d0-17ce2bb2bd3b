type: google.api.Service
config_version: 3
name: accessapproval.googleapis.com
title: Access Approval API

apis:
- name: google.cloud.accessapproval.v1.AccessApproval

documentation:
  summary: An API for controlling access to data by Google personnel.

backend:
  rules:
  - selector: 'google.cloud.accessapproval.v1.AccessApproval.*'
    deadline: 10.0

authentication:
  rules:
  - selector: 'google.cloud.accessapproval.v1.AccessApproval.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  documentation_uri: https://cloud.google.com/access-approval/docs
  github_label: 'api: accessapproval'
  organization: CLOUD
  library_settings:
  - version: google.cloud.accessapproval.v1
    dotnet_settings:
      renamed_services:
        AccessApproval: AccessApprovalService
