type: google.api.Service
config_version: 3
name: admanager.googleapis.com
title: Google Ad Manager API

apis:
- name: google.ads.admanager.v1.AdUnitService
- name: google.ads.admanager.v1.CompanyService
- name: google.ads.admanager.v1.CustomFieldService
- name: google.ads.admanager.v1.CustomTargetingKeyService
- name: google.ads.admanager.v1.CustomTargetingValueService
- name: google.ads.admanager.v1.EntitySignalsMappingService
- name: google.ads.admanager.v1.NetworkService
- name: google.ads.admanager.v1.OrderService
- name: google.ads.admanager.v1.PlacementService
- name: google.ads.admanager.v1.ReportService
- name: google.ads.admanager.v1.RoleService
- name: google.ads.admanager.v1.TaxonomyCategoryService
- name: google.ads.admanager.v1.UserService
- name: google.longrunning.Operations

types:
- name: google.ads.admanager.v1.Contact
- name: google.ads.admanager.v1.Label
- name: google.ads.admanager.v1.Report
- name: google.ads.admanager.v1.RunReportMetadata
- name: google.ads.admanager.v1.RunReportResponse
- name: google.ads.admanager.v1.Team

documentation:
  summary: 'Manage your Ad Manager inventory, run reports and more.'
  overview: |-
    The Ad Manager API enables an app to integrate with Google Ad Manager. You
    can read Ad Manager data and run reports using the API.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=networks/*/operations/reports/runs/*}'
    additional_bindings:
    - get: '/v1/{name=networks/*/operations/reports/exports/*}'

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1265187&template=1787490
  documentation_uri: https://developers.google.com/ad-manager/api/beta
  api_short_name: admanager
  github_label: 'api: admanager'
  doc_tag_prefix: admanager
  organization: ADS
  library_settings:
  - version: google.ads.admanager.v1
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common: {}
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common: {}
