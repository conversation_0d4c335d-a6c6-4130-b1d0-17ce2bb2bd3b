type: google.api.Service
config_version: 3
name: aerialview.googleapis.com
title: Aerial View API

apis:
- name: google.maps.aerialview.v1.AerialView

documentation:
  summary: The Maps Aerial View API.
  overview: '<!--#include file="/geo/platform/aerial_view/g3doc/overview.md"-->'

authentication:
  rules:
  - selector: google.maps.aerialview.v1.AerialView.LookupVideo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.maps.aerialview.v1.AerialView.RenderVideo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1347175
  documentation_uri: https://developers.google.com/maps/documentation/aerial-view
  api_short_name: aerialview
  github_label: 'api: aerialview'
  doc_tag_prefix: aerialview
  organization: GEO
  library_settings:
  - version: google.maps.aerialview.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/maps/documentation/aerial-view/reference/rpc
