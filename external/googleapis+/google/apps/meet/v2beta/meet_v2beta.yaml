type: google.api.Service
config_version: 3
name: meet.googleapis.com
title: Google Meet API

apis:
- name: google.apps.meet.v2beta.ConferenceRecordsService
- name: google.apps.meet.v2beta.SpacesService

documentation:
  summary: Create and manage meetings in Google Meet.

authentication:
  rules:
  - selector: google.apps.meet.v2beta.*
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/meetings.space.created,
        https://www.googleapis.com/auth/meetings.space.readonly
  - selector: google.apps.meet.v2beta.SpacesService.CreateSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/meetings.space.created
  - selector: google.apps.meet.v2beta.SpacesService.UpdateSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/meetings.space.created
  - selector: google.apps.meet.v2beta.SpacesService.DeleteSpace
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/meetings.space.created
  - selector: google.apps.meet.v2beta.SpacesService.EndActiveConference
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/meetings.space.created

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1216362&template=1766418
  documentation_uri: https://developers.google.com/meet/api/guides/overview
  api_short_name: meet
  github_label: 'api: meet'
  doc_tag_prefix: meet
  organization: CLOUD
  library_settings:
  - version: google.apps.meet.v2beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/meet/api/reference/rest/
