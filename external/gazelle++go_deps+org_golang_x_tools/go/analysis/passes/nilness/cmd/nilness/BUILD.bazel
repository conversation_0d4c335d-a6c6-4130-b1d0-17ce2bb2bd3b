load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "nilness_lib",
    srcs = ["main.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/nilness/cmd/nilness",
    visibility = ["//visibility:private"],
    deps = [
        "//go/analysis/passes/nilness",
        "//go/analysis/singlechecker",
    ],
)

go_binary(
    name = "nilness",
    embed = [":nilness_lib"],
    visibility = ["//visibility:public"],
)
