package com.moego.server.grooming.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.agreement.v1.AgreementWithRecentRecordsViewList;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc;
import com.moego.idl.service.agreement.v1.BatchGetRecentSignedAgreementListResponse;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AgreementHelperTest {

    @Mock
    private AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub agreementRecordClient;

    @InjectMocks
    private AgreementHelper agreementHelper;

    @Test
    void batchGetCustomerRecentSignedAgreements_withValidCustomerIdsAndBusinessId_returnsExpectedResult() {
        List<Long> customerIds = List.of(1L, 2L, 3L);
        long businessId = 123L;
        when(agreementRecordClient.batchGetRecentSignedAgreementList(any()))
                .thenReturn(BatchGetRecentSignedAgreementListResponse.newBuilder()
                        .putAllCustomerRecentAgreement(Map.of(
                                1L,
                                AgreementWithRecentRecordsViewList.getDefaultInstance(),
                                2L,
                                AgreementWithRecentRecordsViewList.getDefaultInstance(),
                                3L,
                                AgreementWithRecentRecordsViewList.getDefaultInstance()))
                        .build());

        var result = agreementHelper.batchGetCustomerRecentSignedAgreements(customerIds, businessId);

        var expect = Map.of(1L, List.of(), 2L, List.of(), 3L, List.of());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void batchGetCustomerRecentSignedAgreements_withEmptyCustomerIds_returnsEmptyResult() {
        List<Long> customerIds = List.of();
        Long businessId = 123L;

        var result = agreementHelper.batchGetCustomerRecentSignedAgreements(customerIds, businessId);

        var expect = Map.of();
        assertThat(result).isEqualTo(expect);
    }
}
