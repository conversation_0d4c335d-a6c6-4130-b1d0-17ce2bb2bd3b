package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TimeSlotTest {

    @Test
    void updateAvailableTime_ShouldCalculateCorrectly() {
        // Arrange
        TimeSlot timeSlot = TimeSlot.builder()
                .start(540) // 9:00
                .end(600) // 10:00
                .build();

        // Act
        timeSlot.updateAvailableTime(30);

        // Assert
        assertThat(timeSlot.getAvailableTime()).isEqualTo(30); // 60 - 30 = 30 minutes available
    }

    @Test
    void isBeforeAddressValid_WhenBothLatLngPresent_ShouldReturnTrue() {
        // Arrange
        TimeSlot timeSlot =
                TimeSlot.builder().beforeLat("37.7749").beforeLng("-122.4194").build();

        // Act & Assert
        assertThat(timeSlot.isBeforeAddressValid()).isTrue();
    }

    @Test
    void isBeforeAddressValid_WhenLatLngEmpty_ShouldReturnFalse() {
        // Arrange
        TimeSlot timeSlot = TimeSlot.builder().beforeLat("").beforeLng("").build();

        // Act & Assert
        assertThat(timeSlot.isBeforeAddressValid()).isFalse();
    }

    @Test
    void isBeforeAddressValid_WhenLatLngNull_ShouldReturnFalse() {
        // Arrange
        TimeSlot timeSlot = TimeSlot.builder().build();

        // Act & Assert
        assertThat(timeSlot.isBeforeAddressValid()).isFalse();
    }

    @Test
    void isAfterAddressValid_WhenBothLatLngPresent_ShouldReturnTrue() {
        // Arrange
        TimeSlot timeSlot =
                TimeSlot.builder().afterLat("37.7749").afterLng("-122.4194").build();

        // Act & Assert
        assertThat(timeSlot.isAfterAddressValid()).isTrue();
    }

    @Test
    void isAfterAddressValid_WhenLatLngEmpty_ShouldReturnFalse() {
        // Arrange
        TimeSlot timeSlot = TimeSlot.builder().afterLat("").afterLng("").build();

        // Act & Assert
        assertThat(timeSlot.isAfterAddressValid()).isFalse();
    }

    @Test
    void isAfterAddressValid_WhenLatLngNull_ShouldReturnFalse() {
        // Arrange
        TimeSlot timeSlot = TimeSlot.builder().build();

        // Act & Assert
        assertThat(timeSlot.isAfterAddressValid()).isFalse();
    }
}
