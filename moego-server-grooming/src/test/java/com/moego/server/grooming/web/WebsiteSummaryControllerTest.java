package com.moego.server.grooming.web;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

/**
 * {@link WebsiteSummaryController} tester.
 */
class WebsiteSummaryControllerTest {

    /**
     * Test {@link WebsiteSummaryController#toMillion(int)} (int)}.
     */
    @Test
    void toMillion() {
        assertThat(WebsiteSummaryController.toMillion(12_345_678)).isEqualTo(12.35);
        assertThat(WebsiteSummaryController.toMillion(122_000_000)).isEqualTo(122.00);
        assertThat(WebsiteSummaryController.toMillion(10_000)).isEqualTo(0.01);
        assertThat(WebsiteSummaryController.toMillion(0)).isEqualTo(0.00);
    }
}
