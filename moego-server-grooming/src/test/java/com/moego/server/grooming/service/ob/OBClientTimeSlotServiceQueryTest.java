package com.moego.server.grooming.service.ob;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.PetBreedLimitation;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotFreeStaffServiceDef;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.organization.v1.ListSlotFreeServicesResponse;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.client.IBusinessWorkingHourClient;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.enums.StaffAvailableTypeEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.OfferingHelper;
import com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.StaffTimeSyncService;
import com.moego.server.grooming.service.dto.OBAvailableDateTimeDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeDetailDTO;
import com.moego.server.grooming.service.dto.PetToStaffDto;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.remote.ServiceManagementService;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OBClientTimeSlotServiceQueryTest {

    @InjectMocks
    private OBClientTimeSlotService obClientTimeSlotService;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private MoeBookOnlineAvailableStaffMapper moeBookOnlineAvailableStaffMapper;

    @Mock
    private GroomingServiceService groomingServiceService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private IBusinessWorkingHourClient iBusinessWorkingHourClient;

    @Mock
    private SmartScheduleV2Service smartScheduleV2Service;

    @Mock
    private OBBusinessService businessService;

    @Mock
    private OBClientService clientService;

    @Mock
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Mock
    private OBBusinessStaffService businessStaffService;

    @Mock
    MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Mock
    private ServiceManagementService serviceManagementService;

    @Mock
    private CompanyHelper companyHelper;

    @Mock
    private SmartSchedulerService smartSchedulerService;

    @Mock
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Mock
    private StaffTimeSyncService staffTimeSyncService;

    @Mock
    private FeatureFlagApi featureFlagApi;

    @Mock
    private IBusinessClosedDateClient iBusinessClosedDateClient;

    @Mock
    private OBPetLimitService obPetLimitService;

    @Mock
    private ExecutorService smartScheduleExecutorService;

    @Mock
    private OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    @Mock
    private com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    @Mock
    private IGroomingOnlineBookingService onlineBookingApi;

    @Mock
    private OfferingHelper offeringHelper;

    private static final Long COMPANY_ID = 100811L;
    private static final Integer BUSINESS_ID = 102786;

    private static final Integer STAFF_ID_1 = 119146;
    private static final Integer STAFF_ID_2 = 102498;

    private static final Integer CUSTOMER_ID = 110001;

    private static final Integer PET_ID_1 = 220001;
    private static final Integer PET_ID_2 = 220002;
    private static final Integer PET_ID_3 = 220003;

    private static final Integer SERVICE_ID_1 = 100;
    private static final Integer SERVICE_ID_2 = 200;
    private static final Integer SERVICE_ID_3 = 300;

    @BeforeAll
    static void setUpClass() {
        // 强制加载时区数据
        ZoneId.systemDefault();
        ZoneId.of("Asia/Shanghai");
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

    @Nested
    @DisplayName("Non Book by Family")
    class NonBookByFamilyTest {
        @Test
        @DisplayName("两只 pet，选择 anyone，无 slot")
        void testGetTimeSlotListV2_TwoPetsOneSlot_Anyone_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO200);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of()))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock iBusinessWorkingHourClient to prevent NullPointerException
                // Using lenient to avoid UnnecessaryStubbingException as this mock is not always used
                lenient()
                        .when(iBusinessWorkingHourClient.getBusinessWorkingHour(anyInt()))
                        .thenReturn(null);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }

        @Test
        @DisplayName("两只 pet 选择 anyone，无 slot")
        void testGetTimeSlotListV2_TwoPets_Anyone_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1); // staff 1 只能提供 service 2
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2); // staff 2 只能提供 service 1

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NOT_SELECTED);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(
                                new PetToStaffDto()
                                        .setPetIndex(0)
                                        .setStaffId(STAFF_ID_2)
                                        .setStartTime(540),
                                new PetToStaffDto()
                                        .setPetIndex(1)
                                        .setStaffId(STAFF_ID_1)
                                        .setStartTime(540))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("两只 pet 选择不同 staff，有 slot")
        void testGetTimeSlotListV2_TwoPets_DifferentStaff_HasSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petData2.setStaffId(STAFF_ID_2);
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1);
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.SELECTED_AVAILABLE);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(
                                new PetToStaffDto()
                                        .setPetIndex(0)
                                        .setStaffId(STAFF_ID_1)
                                        .setStartTime(540),
                                new PetToStaffDto()
                                        .setPetIndex(1)
                                        .setStaffId(STAFF_ID_2)
                                        .setStartTime(540))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("两只 pet 选择不同 staff，无 slot")
        void testGetTimeSlotListV2_TwoPets_DifferentStaff_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petData2.setStaffId(STAFF_ID_2);
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1); // staff 1 只能提供 service 2
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2); // staff 2 只能提供 service 1

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }

        @Test
        @DisplayName("一只 pet 选择 service 和 addon，有 slot")
        void testGetTimeSlotListV2_OnePetServiceAndAddOn_HasSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_3);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1, SERVICE_ID_3));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService.setStaffId(STAFF_ID_1); // staff 1 可以提供 service 1
                moeBookOnlineStaffService.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of(SERVICE_ID_1)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NOT_SELECTED);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(new PetToStaffDto()
                                .setPetIndex(0)
                                .setStaffId(STAFF_ID_1)
                                .setStartTime(540))));
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(600)
                        .setPetToStaffs(List.of(new PetToStaffDto()
                                .setPetIndex(0)
                                .setStaffId(STAFF_ID_1)
                                .setStartTime(600))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("一只 pet 选择 service 和 addon，无 slot")
        void testGetTimeSlotListV2_OnePetServiceAndAddOn_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_3);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1, SERVICE_ID_3));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService.setStaffId(STAFF_ID_2); // 只有 staff 2 才能提供 service 1
                moeBookOnlineStaffService.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of(SERVICE_ID_1)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }

        @Test
        @DisplayName("一只 pet 命中 daily setting capacity，无 slot")
        void testGetTimeSlotListV2_OnePet_DailySettingCapacity_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO100);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1(0, List.of())); // daily capacity = 0

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of()))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }

        @Test
        @DisplayName("一只 pet 命中 daily setting breed limitation，无 slot")
        void testGetTimeSlotListV2_OnePet_DailySettingBreedLimitation_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO100);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(
                        STAFF_ID_1,
                        getStaffAvailability1(
                                1,
                                List.of(LimitationGroup.newBuilder()
                                        .setOnlyAcceptSelected(false)
                                        .addPetBreedLimits(PetBreedLimitation.newBuilder()
                                                .setPetTypeId(1)
                                                .setIsAllBreed(false)
                                                .addBreedIds(990001)
                                                .setCapacity(0)
                                                .build())
                                        .build()))); // breed limitation

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of()))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                mockFinalPetLimitFilter.setCurrentBreedMap(Map.of("Golden Retriever", 1L));
                mockFinalPetLimitFilter.setPetBreedMap(Map.of(
                        990001,
                        MoePetBreedDTO.builder()
                                .id(990001)
                                .name("Golden Retriever")
                                .petTypeId(1)
                                .build()));
                mockFinalPetLimitFilter.setPetIndexMap(Map.of(0, petData1));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }
    }

    @Nested
    @DisplayName("Book by Family")
    class BookByFamilyTest {
        @Test
        @DisplayName("两只 pet 选择 anyone，daily capacity 为 1，没有 slot")
        void testGetTimeSlotListV2_TwoPetsOneSlot_Anyone_HasNoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO200);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of()))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock isSlotCapacityByReservation 方法
                when(featureFlagApi.isOn(any(), any())).thenReturn(true);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);

                // List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                // allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                //         .setDate(tomorrow.toString())
                //         .setStartTime(540)
                //         .setPetToStaffs(List.of(
                //                 new PetToStaffDto()
                //                         .setPetIndex(0)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(540),
                //                 new PetToStaffDto()
                //                         .setPetIndex(1)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(540))));
                // allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                //         .setDate(tomorrow.toString())
                //         .setStartTime(600)
                //         .setPetToStaffs(List.of(
                //                 new PetToStaffDto()
                //                         .setPetIndex(0)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(600),
                //                 new PetToStaffDto()
                //                         .setPetIndex(1)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(600))));
                // allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                //         .setDate(tomorrow.toString())
                //         .setStartTime(540)
                //         .setPetToStaffs(List.of(
                //                 new PetToStaffDto()
                //                         .setPetIndex(1)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(540),
                //                 new PetToStaffDto()
                //                         .setPetIndex(0)
                //                         .setStaffId(STAFF_ID_1)
                //                         .setStartTime(600))));
                // assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                // assertThat(result.getAllAvailableDateTimes())
                //         .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("两只 pet 选择 anyone，staff 和 service 不匹配，有 slot")
        void testGetTimeSlotListV2_TwoPets_Anyone_StaffAndServiceNotMatch_HasSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1); // staff 1 只能提供 service 2
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2); // staff 2 只能提供 service 1

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock isSlotCapacityByReservation 方法
                when(featureFlagApi.isOn(any(), any())).thenReturn(true);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NOT_SELECTED);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(
                                new PetToStaffDto()
                                        .setPetIndex(0)
                                        .setStaffId(STAFF_ID_2)
                                        .setStartTime(540),
                                new PetToStaffDto()
                                        .setPetIndex(1)
                                        .setStaffId(STAFF_ID_1)
                                        .setStartTime(540))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("两只 pet 选择不同 staff，有 slot")
        void testGetTimeSlotListV2_TwoPets_DifferentStaff_HasSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petData2.setStaffId(STAFF_ID_2);
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1);
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock iBusinessWorkingHourClient to prevent NullPointerException
                BusinessWorkingHourDetailDTO mockWorkingHour = new BusinessWorkingHourDetailDTO();
                BusinessWorkingHourDayDetailDTO timeData = new BusinessWorkingHourDayDetailDTO();
                // Set up working hours for all days (non-empty lists)
                timeData.setMonday(List.of(new TimeRangeDto()));
                timeData.setTuesday(List.of(new TimeRangeDto()));
                timeData.setWednesday(List.of(new TimeRangeDto()));
                timeData.setThursday(List.of(new TimeRangeDto()));
                timeData.setFriday(List.of(new TimeRangeDto()));
                timeData.setSaturday(List.of(new TimeRangeDto()));
                timeData.setSunday(List.of(new TimeRangeDto()));
                mockWorkingHour.setTimeData(timeData);
                // Using lenient to avoid UnnecessaryStubbingException as this mock is not always used
                lenient()
                        .when(iBusinessWorkingHourClient.getBusinessWorkingHour(anyInt()))
                        .thenReturn(mockWorkingHour);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.SELECTED_AVAILABLE);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(
                                new PetToStaffDto()
                                        .setPetIndex(0)
                                        .setStaffId(STAFF_ID_1)
                                        .setStartTime(540),
                                new PetToStaffDto()
                                        .setPetIndex(1)
                                        .setStaffId(STAFF_ID_2)
                                        .setStartTime(540))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("两只 pet 选择不同 staff，无 slot")
        void testGetTimeSlotListV2_TwoPets_DifferentStaff_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petData1.setStaffId(STAFF_ID_1);
                petParamList.add(petData1);

                OBPetDataDTO petData2 = new OBPetDataDTO();
                petData2.setPetId(PET_ID_2);
                petData2.setPetIndex(1);
                petData2.setWeight("10.0");
                petData2.setPetTypeId(1);
                petData2.setBreed("Golden Retriever");
                petData2.setStaffId(STAFF_ID_2);
                petParamList.add(petData2);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_2);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1));
                petServices.put(petData2.getPetId(), List.of(SERVICE_ID_2));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO200 = new MoeGroomingServiceDTO();
                serviceDTO200.setId(SERVICE_ID_2);
                serviceDTO200.setDuration(60);
                serviceDTO200.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO200.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO200);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService1 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService1.setStaffId(STAFF_ID_1);
                moeBookOnlineStaffService1.setServiceId(SERVICE_ID_2);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService1); // staff 1 只能提供 service 2
                MoeBookOnlineStaffService moeBookOnlineStaffService2 = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService2.setStaffId(STAFF_ID_2);
                moeBookOnlineStaffService2.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService2); // staff 2 只能提供 service 1

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto1 = new MoeStaffDto();
                staffDto1.setId(STAFF_ID_1);
                staffDto1.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto1);
                MoeStaffDto staffDto2 = new MoeStaffDto();
                staffDto2.setId(STAFF_ID_2);
                staffDto2.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto2);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());
                staffAvailabilityMap.put(STAFF_ID_2, getStaffAvailability2());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(
                                BUSINESS_ID, List.of(SERVICE_ID_1, SERVICE_ID_2)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1, STAFF_ID_2)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0), Set.of(1), Set.of(0, 1)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock isSlotCapacityByReservation 方法
                when(featureFlagApi.isOn(any(), any())).thenReturn(true);

                // Mock iBusinessWorkingHourClient to prevent NullPointerException
                // Using lenient to avoid UnnecessaryStubbingException as this mock is not always used
                lenient()
                        .when(iBusinessWorkingHourClient.getBusinessWorkingHour(anyInt()))
                        .thenReturn(null);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }

        @Test
        @DisplayName("一只 pet 选择 service 和 addon，有 slot")
        void testGetTimeSlotListV2_OnePetServiceAndAddOn_HasSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_3);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1, SERVICE_ID_3));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService.setStaffId(STAFF_ID_1); // staff 1 可以提供 service 1
                moeBookOnlineStaffService.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of(SERVICE_ID_1)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock isSlotCapacityByReservation 方法
                when(featureFlagApi.isOn(any(), any())).thenReturn(true);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NOT_SELECTED);

                List<OBAvailableTimeDetailDTO> allAvailableDateTimes = new ArrayList<>();
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(540)
                        .setPetToStaffs(List.of(new PetToStaffDto()
                                .setPetIndex(0)
                                .setStaffId(STAFF_ID_1)
                                .setStartTime(540))));
                allAvailableDateTimes.add(new OBAvailableTimeDetailDTO()
                        .setDate(tomorrow.toString())
                        .setStartTime(600)
                        .setPetToStaffs(List.of(new PetToStaffDto()
                                .setPetIndex(0)
                                .setStaffId(STAFF_ID_1)
                                .setStartTime(600))));
                assertThat(result.getAllAvailableDateTimes()).hasSameSizeAs(allAvailableDateTimes);
                assertThat(result.getAllAvailableDateTimes())
                        .containsExactlyInAnyOrderElementsOf(allAvailableDateTimes);
            }
        }

        @Test
        @DisplayName("一只 pet 选择 service 和 addon，无 slot")
        void testGetTimeSlotListV2_OnePetServiceAndAddOn_NoSlot() {
            final LocalDate today = LocalDate.of(2025, 7, 13);
            final LocalDate tomorrow = LocalDate.of(2025, 7, 14);
            try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
                mockedLocalDate.when(() -> LocalDate.now(any(ZoneId.class))).thenReturn(today);
                mockedLocalDate.when(() -> LocalDate.parse(anyString())).thenReturn(tomorrow);

                // 初始化宠物参数列表
                final List<OBPetDataDTO> petParamList = new ArrayList<>();
                OBPetDataDTO petData1 = new OBPetDataDTO();
                petData1.setPetId(PET_ID_1);
                petData1.setPetIndex(0);
                petData1.setWeight("10.0");
                petData1.setPetTypeId(1);
                petData1.setBreed("Golden Retriever");
                petParamList.add(petData1);

                Map<Integer, List<Integer>> petServices = new HashMap<>();
                var serviceIds = List.of(SERVICE_ID_1, SERVICE_ID_3);
                petServices.put(petData1.getPetId(), List.of(SERVICE_ID_1, SERVICE_ID_3));

                // 初始化基础测试数据
                final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();
                timeSlotDTO.setBusinessId(BUSINESS_ID);
                timeSlotDTO.setCustomerId(CUSTOMER_ID);
                timeSlotDTO.setDate(tomorrow.toString());
                timeSlotDTO.setPetServices(petServices);
                timeSlotDTO.setPetParamList(petParamList);
                timeSlotDTO.setServiceIds(serviceIds);

                // 初始化业务在线预订设置
                final MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
                businessBookOnline.setCompanyId(COMPANY_ID);
                businessBookOnline.setBusinessId(BUSINESS_ID);
                businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
                businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
                businessBookOnline.setBookingRangeStartOffset(0);
                businessBookOnline.setBookingRangeEndOffset(1);
                businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
                businessBookOnline.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);
                businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT);
                businessBookOnline.setBySlotShowOneAvailableTime(false);

                // 初始化服务列表
                final List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
                MoeGroomingServiceDTO serviceDTO100 = new MoeGroomingServiceDTO();
                serviceDTO100.setId(SERVICE_ID_1);
                serviceDTO100.setDuration(60);
                serviceDTO100.setType(ServiceEnum.TYPE_SERVICE);
                serviceDTO100.setIsAllStaff(CommonConstant.DISABLE);
                serviceDTOList.add(serviceDTO100);
                MoeGroomingServiceDTO serviceDTO300 = new MoeGroomingServiceDTO();
                serviceDTO300.setId(SERVICE_ID_3);
                serviceDTO300.setDuration(60);
                serviceDTO300.setType(ServiceEnum.TYPE_ADD_ONS);
                serviceDTO300.setIsAllStaff(CommonConstant.ENABLE);
                serviceDTOList.add(serviceDTO300);

                final BusinessPreferenceDto businessPreference = new BusinessPreferenceDto();
                businessPreference.setTimezoneName("Asia/Shanghai");

                final List<MoeBookOnlineStaffService> moeBookOnlineStaffServices = new ArrayList<>();
                MoeBookOnlineStaffService moeBookOnlineStaffService = new MoeBookOnlineStaffService();
                moeBookOnlineStaffService.setStaffId(STAFF_ID_2); // 只有 staff 2 才能提供 service 1
                moeBookOnlineStaffService.setServiceId(SERVICE_ID_1);
                moeBookOnlineStaffServices.add(moeBookOnlineStaffService);

                final List<MoeStaffDto> staffDtoList = new ArrayList<>();
                MoeStaffDto staffDto = new MoeStaffDto();
                staffDto.setId(STAFF_ID_1);
                staffDto.setStatus(StaffEnum.STATUS_NORMAL);
                staffDtoList.add(staffDto);

                final Map<Integer, StaffAvailability> staffAvailabilityMap = new HashMap<>();
                staffAvailabilityMap.put(STAFF_ID_1, getStaffAvailability1());

                var bookOnlineDTO = new BookOnlineDTO();
                bookOnlineDTO.setAvailableTimeSync(BooleanEnum.VALUE_TRUE);

                List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs = new ArrayList<>();

                // Mock 依赖服务的返回值
                when(companyHelper.mustGetCompanyId(BUSINESS_ID)).thenReturn(COMPANY_ID);
                when(clientService.validCustomerBlocked(timeSlotDTO.getObName(), COMPANY_ID, BUSINESS_ID, CUSTOMER_ID))
                        .thenReturn(false);
                when(businessService.getSettingInfoByBusinessId(BUSINESS_ID)).thenReturn(businessBookOnline);
                when(groomingServiceService.getServicesByServiceIds(BUSINESS_ID, timeSlotDTO.getServiceIds()))
                        .thenReturn(serviceDTOList);
                when(iBusinessBusinessClient.getBusinessPreference(BUSINESS_ID)).thenReturn(businessPreference);
                when(groomingServiceService.getServiceStaffByServiceIds(BUSINESS_ID, List.of(SERVICE_ID_1)))
                        .thenReturn(moeBookOnlineStaffServices);
                when(businessStaffService.getOBAvailableStaffList(BUSINESS_ID)).thenReturn(staffDtoList);
                when(staffTimeSyncService.queryShiftManagementStaffSlot(BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(staffAvailabilityMap);
                when(staffTimeSyncService.queryShiftManagementOverrideStaffSlot(
                                BUSINESS_ID, COMPANY_ID, Set.of(STAFF_ID_1)))
                        .thenReturn(new HashMap<>());
                when(onlineBookingApi.getOBSetting(BUSINESS_ID)).thenReturn(bookOnlineDTO);
                when(staffService.listSlotFreeServices(any()))
                        .thenReturn(ListSlotFreeServicesResponse.newBuilder()
                                .addAllDefs(slotFreeStaffServiceDefs)
                                .build());
                when(offeringHelper.fetchAllAddOn(anyLong(), anyLong())).thenReturn(List.of());

                doAnswer(invocation -> {
                            Runnable task = invocation.getArgument(0);
                            task.run(); // 在当前测试线程中同步执行任务
                            return null;
                        })
                        .when(smartScheduleExecutorService)
                        .execute(any(Runnable.class));

                // Mock queryStaffBlocksByDates 方法
                List<StaffBlockInfoDTO> mockBlocksList = new ArrayList<>();
                when(moeGroomingAppointmentService.queryStaffBlocksByDates(anyInt(), any()))
                        .thenReturn(mockBlocksList);

                // Mock queryStaffTimeslotPetCount 方法
                Collection<StaffTimeslotPetCountDTO> mockPetCountList = List.of();
                when(moeGroomingAppointmentService.queryStaffTimeslotPetCountByTime(
                                anyInt(), any(), any(), any(), any()))
                        .thenReturn(mockPetCountList);

                // Mock obPetLimitService.generateWithCurrentAppointment 方法
                OBPetLimitFilterDTO mockInitPetLimitFilter = new OBPetLimitFilterDTO();
                when(obPetLimitService.generateWithCurrentAppointment(anyLong(), anyInt(), any(), any()))
                        .thenReturn(mockInitPetLimitFilter);

                // Mock obPetLimitService.fillWithExistingAppointmentV2 方法
                OBPetLimitFilterDTO mockFinalPetLimitFilter = new OBPetLimitFilterDTO();
                mockFinalPetLimitFilter.setPetIndexSubList(List.of(Set.of(0)));
                when(obPetLimitService.fillWithExistingAppointmentV2(any(), any()))
                        .thenReturn(mockFinalPetLimitFilter);

                // Mock isSlotCapacityByReservation 方法
                when(featureFlagApi.isOn(any(), any())).thenReturn(true);

                // Act - 执行测试方法
                OBAvailableDateTimeDTO result = obClientTimeSlotService.getTimeSlotListV2(timeSlotDTO);

                // Assert - 验证结果
                assertThat(result).isNotNull();
                assertThat(result.getStaffAvailableType()).isEqualTo(StaffAvailableTypeEnum.NO_AVAILABLE);
            }
        }
    }

    @Nonnull
    private StaffAvailability getStaffAvailability1() {
        return StaffAvailability.newBuilder()
                .setStaffId(STAFF_ID_1)
                .setIsAvailable(true)
                .setStaffId(STAFF_ID_1)
                .setSlotStartSunday("2025-07-13")
                .setScheduleType(ScheduleType.ONE_WEEK)
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.MONDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.TUESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.WEDNESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.THURSDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.FRIDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.SATURDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.SUNDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .build();
    }

    @Nonnull
    private StaffAvailability getStaffAvailability2() {
        return StaffAvailability.newBuilder()
                .setStaffId(STAFF_ID_2)
                .setIsAvailable(true)
                .setStaffId(STAFF_ID_2)
                .setSlotStartSunday("2025-07-13")
                .setScheduleType(ScheduleType.ONE_WEEK)
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.MONDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.TUESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.WEDNESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.THURSDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.FRIDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.SATURDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_2)
                        .setDayOfWeek(DayOfWeek.SUNDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .build())
                .build();
    }

    @Nonnull
    private StaffAvailability getStaffAvailability1(int dailyCapacity, List<LimitationGroup> limitationGroups) {
        return StaffAvailability.newBuilder()
                .setStaffId(STAFF_ID_1)
                .setIsAvailable(true)
                .setStaffId(STAFF_ID_1)
                .setSlotStartSunday("2025-07-13")
                .setScheduleType(ScheduleType.ONE_WEEK)
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.MONDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.TUESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.WEDNESDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.THURSDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.FRIDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.SATURDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .addSlotAvailabilityDayList(SlotAvailabilityDay.newBuilder()
                        .setStaffId(STAFF_ID_1)
                        .setDayOfWeek(DayOfWeek.SUNDAY)
                        .setIsAvailable(true)
                        .setScheduleType(ScheduleType.ONE_WEEK)
                        .setSlotDailySetting(SlotDailySetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(1140)
                                .setCapacity(dailyCapacity)
                                .addAllLimitationGroups(limitationGroups)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .setCapacity(1)
                                .build())
                        .addSlotHourSettingList(SlotHourSetting.newBuilder()
                                .setStartTime(600)
                                .setEndTime(630)
                                .setCapacity(1)
                                .build())
                        .build())
                .build();
    }
}
