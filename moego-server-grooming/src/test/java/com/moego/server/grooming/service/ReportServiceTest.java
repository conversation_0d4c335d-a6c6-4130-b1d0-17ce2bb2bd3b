package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.ReportUtil.calculateCollectedAmount;
import static com.moego.server.grooming.service.utils.ReportUtil.calculateTax;
import static java.math.RoundingMode.HALF_UP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doCallRealMethod;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.CollectedAmountCollection;
import com.moego.server.grooming.service.dto.report.CollectedPriceDTO;
import com.moego.server.grooming.service.params.CalCollectedAmountParams;
import com.moego.server.grooming.service.params.CalCollectedPriceParams;
import com.moego.server.grooming.service.report.ReportCalculateService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/8/11
 */
@ExtendWith(MockitoExtension.class)
public class ReportServiceTest {

    @Mock
    private AppointmentServiceDetailService serviceDetailService;

    @InjectMocks
    private ReportCalculateService reportCalculateService;

    @Test
    public void calculateCollectedServicePriceNormal() {
        CalCollectedPriceParams params = new CalCollectedPriceParams()
                .setPaidAmount(BigDecimal.TEN.setScale(2, HALF_UP))
                .setRefundAmount(BigDecimal.ONE.setScale(2, HALF_UP))
                .setExpectedServiceTax(BigDecimal.ONE.setScale(2, HALF_UP))
                .setExpectedTips(BigDecimal.ONE.setScale(2, HALF_UP))
                .setServiceDiscountAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setProductDiscountAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setExpectedServiceTax(BigDecimal.ONE.setScale(2, HALF_UP))
                .setExpectedProductTax(BigDecimal.ONE.setScale(2, HALF_UP))
                .setTotalServiceSale(BigDecimal.ONE.setScale(2, HALF_UP))
                .setTotalProductSale(BigDecimal.ONE.setScale(2, HALF_UP));
        CollectedPriceDTO collectedPriceDTO = reportCalculateService.calculateCollectedPrice(params);
        // paid - refund
        Assertions.assertThat(collectedPriceDTO.getCollectedRevenue())
                .isEqualTo(BigDecimal.TEN.subtract(BigDecimal.ONE).setScale(2, HALF_UP));
        // paid - refund - tax - tips + discount
        Assertions.assertThat(collectedPriceDTO.getCollectedServicePrice())
                .isEqualTo(new BigDecimal("2.50").setScale(2, HALF_UP));
        Assertions.assertThat(collectedPriceDTO.getCollectedServiceTax())
                .isEqualTo(BigDecimal.ONE.setScale(2, HALF_UP));
        Assertions.assertThat(collectedPriceDTO.getCollectedTips()).isEqualTo(BigDecimal.ONE.setScale(2, HALF_UP));
        Assertions.assertThat(collectedPriceDTO.getServiceDiscountAmount())
                .isEqualTo(BigDecimal.ZERO.setScale(2, HALF_UP));
    }

    @Test
    public void calculateCollectedServicePriceZero() {
        CalCollectedPriceParams params = new CalCollectedPriceParams()
                .setPaidAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setRefundAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setExpectedServiceTax(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setExpectedTips(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setServiceDiscountAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setProductDiscountAmount(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setExpectedServiceTax(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setExpectedProductTax(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setTotalServiceSale(BigDecimal.ZERO.setScale(2, HALF_UP))
                .setTotalProductSale(BigDecimal.ZERO.setScale(2, HALF_UP));
        CollectedPriceDTO collectedPriceDTO = reportCalculateService.calculateCollectedPrice(params);
        // paid - refund
        Assertions.assertThat(collectedPriceDTO.getCollectedRevenue())
                .isEqualByComparingTo(BigDecimal.ZERO.subtract(BigDecimal.ZERO).setScale(2, HALF_UP));
        // paid - refund - tax - tips + discount
        Assertions.assertThat(collectedPriceDTO.getCollectedServicePrice())
                .isEqualByComparingTo(BigDecimal.ZERO.setScale(2, HALF_UP));
        Assertions.assertThat(collectedPriceDTO.getCollectedServiceTax())
                .isEqualByComparingTo(BigDecimal.ZERO.setScale(2, HALF_UP));
        Assertions.assertThat(collectedPriceDTO.getCollectedTips())
                .isEqualByComparingTo(BigDecimal.ZERO.setScale(2, HALF_UP));
    }

    @Test
    public void dealApptMoneyReport_NoRefund_ExcludeFeeTRUE() {
        doCallRealMethod().when(serviceDetailService).calculateAmount(any(), any(), any());
        GroomingReportWebAppointment appt = buildNoTipsNoTaxApptData();
        Map<Integer, ReportWebEmployee> idToStaff = buildReportMap();
        List<MoeGroomingInvoiceItem> invoiceItems = buildInvoiceItems();
        reportCalculateService.dealApptMoneyReportV2(appt, idToStaff, invoiceItems, Collections.emptyMap());

        // 校验staff1的CollectedServicePrice，不包含Convenience fee
        BigDecimal staff1Collected =
                idToStaff.get(1000).getCollectedServicePrice().setScale(2, RoundingMode.HALF_UP);

        Assertions.assertThat(staff1Collected).isEqualByComparingTo(BigDecimal.valueOf(100));
        // 校验staff1的CollectedAddonPrice，不包含Convenience fee
        BigDecimal staff2Collected =
                idToStaff.get(1001).getCollectedAddonPrice().setScale(2, RoundingMode.HALF_UP);
        Assertions.assertThat(staff2Collected).isEqualByComparingTo(BigDecimal.valueOf(50));
    }

    @Test
    public void dealApptMoneyReport_WithRefund_ExcludeFeeTRUE() {
        doCallRealMethod().when(serviceDetailService).calculateAmount(any(), any(), any());
        GroomingReportWebAppointment appt = buildNoTipsNoTaxApptData();
        Map<Integer, ReportWebEmployee> idToStaff = buildReportMap();
        List<MoeGroomingInvoiceItem> invoiceItems = buildInvoiceItems();
        // 退款60，剩余83.33，减去Convenience fee 3.33，按比例分配80
        appt.setRefundedAmount(BigDecimal.valueOf(60));

        reportCalculateService.dealApptMoneyReportV2(appt, idToStaff, invoiceItems, Collections.emptyMap());

        // 校验staff1的CollectedServicePrice，不包含Convenience fee
        BigDecimal staff1Collected =
                idToStaff.get(1000).getCollectedServicePrice().setScale(2, RoundingMode.HALF_UP);
        Assertions.assertThat(staff1Collected).isEqualByComparingTo(BigDecimal.valueOf(60));
        // 校验staff1的CollectedAddonPrice，不包含Convenience fee
        BigDecimal staff2Collected =
                idToStaff.get(1001).getCollectedAddonPrice().setScale(2, RoundingMode.HALF_UP);
        Assertions.assertThat(staff2Collected).isEqualByComparingTo(BigDecimal.valueOf(30));
    }

    @Test
    public void dealApptMoneyReport_WithRefundMoreThanFee_ExcludeFeeTRUE() {
        doCallRealMethod().when(serviceDetailService).calculateAmount(any(), any(), any());

        GroomingReportWebAppointment appt = buildNoTipsNoTaxApptData();
        Map<Integer, ReportWebEmployee> idToStaff = buildReportMap();
        List<MoeGroomingInvoiceItem> invoiceItems = buildInvoiceItems();
        // 退款151，剩余2.33，已比Convenience fee少，不再进行分配
        appt.setRefundedAmount(BigDecimal.valueOf(151));

        reportCalculateService.dealApptMoneyReportV2(appt, idToStaff, invoiceItems, Collections.emptyMap());

        // 校验staff1的CollectedServicePrice
        BigDecimal staff1Collected =
                idToStaff.get(1000).getCollectedServicePrice().setScale(2, RoundingMode.HALF_UP);
        Assertions.assertThat(staff1Collected).isNegative();
        // 校验staff1的CollectedAddonPrice
        BigDecimal staff2Collected =
                idToStaff.get(1001).getCollectedAddonPrice().setScale(2, RoundingMode.HALF_UP);
        Assertions.assertThat(staff2Collected).isNegative();
    }

    private GroomingReportWebAppointment buildNoTipsNoTaxApptData() {
        GroomingReportWebAppointment appt = new GroomingReportWebAppointment();
        List<ReportWebApptPetDetail> petDetails = new ArrayList<>();
        ReportWebApptPetDetail petDetail1 = new ReportWebApptPetDetail()
                .setId(1)
                .setPetId(1)
                .setServiceId(10)
                .setServicePrice(BigDecimal.valueOf(100))
                .setStaffId(1000)
                .setPriceUnit(1)
                .setQuantityPerDay(1)
                .setServiceType(ServiceEnum.TYPE_SERVICE.intValue());
        ReportWebApptPetDetail petDetail2 = new ReportWebApptPetDetail()
                .setId(2)
                .setPetId(2)
                .setServiceId(11)
                .setServicePrice(BigDecimal.valueOf(50))
                .setStaffId(1001)
                .setQuantityPerDay(1)
                .setServiceType(ServiceEnum.TYPE_ADD_ONS.intValue());
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);

        var apt = (GroomingReportWebAppointment) appt.setPetDetails(petDetails)
                .setIsPaid(GroomingAppointmentEnum.PAID)
                .setConvenienceFee(BigDecimal.valueOf(3.33))
                .setTipsAmount(BigDecimal.ZERO)
                .setTaxAmount(BigDecimal.ZERO)
                .setDiscountAmount(BigDecimal.ZERO)
                .setRemainAmount(BigDecimal.ZERO)
                .setTotalAmount(BigDecimal.valueOf(153.33))
                .setTotalServiceSale(BigDecimal.valueOf(150))
                .setTotalProductSale(BigDecimal.ZERO)
                .setTotalServiceChargeSale(BigDecimal.ZERO)
                .setPaidAmount(BigDecimal.valueOf(153.33));
        apt.setRefundedAmount(BigDecimal.ZERO);
        return apt;
    }

    private Map<Integer, ReportWebEmployee> buildReportMap() {
        Map<Integer, ReportWebEmployee> idToStaff = new HashMap<>();
        ReportWebEmployee employee1 = new ReportWebEmployee();
        employee1.setStaffId(1000);
        employee1.setCollectedServicePrice(BigDecimal.ZERO);
        ReportWebEmployee employee2 = new ReportWebEmployee();
        employee2.setStaffId(1001);
        employee2.setCollectedAddonPrice(BigDecimal.ZERO);
        idToStaff.put(1000, employee1);
        idToStaff.put(1001, employee2);
        return idToStaff;
    }

    private List<MoeGroomingInvoiceItem> buildInvoiceItems() {
        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        MoeGroomingInvoiceItem item1 = new MoeGroomingInvoiceItem();
        item1.setQuantity(1);
        item1.setServiceId(10);
        item1.setServiceUnitPrice(BigDecimal.valueOf(100));
        item1.setTaxAmount(BigDecimal.ZERO);
        MoeGroomingInvoiceItem item2 = new MoeGroomingInvoiceItem();
        item2.setQuantity(1);
        item2.setServiceId(11);
        item2.setServiceUnitPrice(BigDecimal.valueOf(50));
        item2.setTaxAmount(BigDecimal.ZERO);
        invoiceItems.add(item1);
        invoiceItems.add(item2);
        return invoiceItems;
    }

    private final Integer SERVICE_ID_1 = 1000;
    private final Integer SERVICE_ID_2 = 1001;
    private final Integer SERVICE_ID_3 = 1002;
    private final String SERVICE_PRICE_1 = "100.00";
    private final String SERVICE_PRICE_2 = "80.00";
    private final String SERVICE_PRICE_3 = "60.00";
    private final String TAX_AMOUNT_1 = "6.25";
    private final String TAX_AMOUNT_1_MULTIPLY_3 = "18.75";
    private final String TAX_AMOUNT_2 = "5.00";
    private final String TAX_AMOUNT_3 = "3.75";

    @Test
    public void calculateTax_SameServiceWithSamePrice_ReturnSameTax() {
        ReportWebApptPetDetail petDetail1 = getPetDetail(1, SERVICE_ID_1, SERVICE_PRICE_1);
        ReportWebApptPetDetail petDetail2 = getPetDetail(2, SERVICE_ID_1, SERVICE_PRICE_1);
        ReportWebApptPetDetail petDetail3 = getPetDetail(3, SERVICE_ID_1, SERVICE_PRICE_1);

        List<ReportWebApptPetDetail> petDetails = new ArrayList<>();
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);
        petDetails.add(petDetail3);

        // 相同的service（id、price都一样）会合并到同一个invoice item中
        MoeGroomingInvoiceItem invoiceItem1 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_1, TAX_AMOUNT_1_MULTIPLY_3, 3);

        List<MoeGroomingInvoiceItem> invoiceItems = Collections.singletonList(invoiceItem1);

        Map<Integer, BigDecimal> expectedResult = new HashMap<>();
        expectedResult.put(1, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(2, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(3, new BigDecimal(TAX_AMOUNT_1));

        assertThat(calculateTax(petDetails, invoiceItems)).isEqualTo(expectedResult);
    }

    @Test
    public void calculateTax_SameServiceWithDifferentPrice_ReturnDifferent() {
        ReportWebApptPetDetail petDetail1 = getPetDetail(1, SERVICE_ID_1, SERVICE_PRICE_1);
        ReportWebApptPetDetail petDetail2 = getPetDetail(2, SERVICE_ID_1, SERVICE_PRICE_2);
        ReportWebApptPetDetail petDetail3 = getPetDetail(3, SERVICE_ID_1, SERVICE_PRICE_3);

        List<ReportWebApptPetDetail> petDetails = new ArrayList<>();
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);
        petDetails.add(petDetail3);

        // 相同的service，价格不同时，会分成不同的invoice item
        MoeGroomingInvoiceItem invoiceItem1 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_1, TAX_AMOUNT_1, 1);
        MoeGroomingInvoiceItem invoiceItem2 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_2, TAX_AMOUNT_2, 1);
        MoeGroomingInvoiceItem invoiceItem3 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_3, TAX_AMOUNT_3, 1);

        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        invoiceItems.add(invoiceItem1);
        invoiceItems.add(invoiceItem2);
        invoiceItems.add(invoiceItem3);

        Map<Integer, BigDecimal> expectedResult = new HashMap<>();
        expectedResult.put(1, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(2, new BigDecimal(TAX_AMOUNT_2));
        expectedResult.put(3, new BigDecimal(TAX_AMOUNT_3));

        assertThat(calculateTax(petDetails, invoiceItems)).isEqualTo(expectedResult);
    }

    @Test
    public void calculateTax_DifferentServiceWithSameTax_ReturnSame() {
        ReportWebApptPetDetail petDetail1 = getPetDetail(1, SERVICE_ID_1, SERVICE_PRICE_1);
        ReportWebApptPetDetail petDetail2 = getPetDetail(2, SERVICE_ID_2, SERVICE_PRICE_2);
        ReportWebApptPetDetail petDetail3 = getPetDetail(3, SERVICE_ID_3, SERVICE_PRICE_3);

        List<ReportWebApptPetDetail> petDetails = new ArrayList<>();
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);
        petDetails.add(petDetail3);

        // 不同的service，会分成不同的invoice item
        MoeGroomingInvoiceItem invoiceItem1 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_1, TAX_AMOUNT_1, 1);
        MoeGroomingInvoiceItem invoiceItem2 = getInvoiceItem(SERVICE_ID_2, SERVICE_PRICE_2, TAX_AMOUNT_1, 1);
        MoeGroomingInvoiceItem invoiceItem3 = getInvoiceItem(SERVICE_ID_3, SERVICE_PRICE_3, TAX_AMOUNT_1, 1);

        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        invoiceItems.add(invoiceItem1);
        invoiceItems.add(invoiceItem2);
        invoiceItems.add(invoiceItem3);

        Map<Integer, BigDecimal> expectedResult = new HashMap<>();
        expectedResult.put(1, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(2, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(3, new BigDecimal(TAX_AMOUNT_1));

        assertThat(calculateTax(petDetails, invoiceItems)).isEqualTo(expectedResult);
    }

    @Test
    public void calculateTax_DifferentServiceWithDifferentTax_ReturnDifferent() {
        ReportWebApptPetDetail petDetail1 = getPetDetail(1, SERVICE_ID_1, SERVICE_PRICE_1);
        ReportWebApptPetDetail petDetail2 = getPetDetail(2, SERVICE_ID_2, SERVICE_PRICE_2);
        ReportWebApptPetDetail petDetail3 = getPetDetail(3, SERVICE_ID_3, SERVICE_PRICE_3);

        List<ReportWebApptPetDetail> petDetails = new ArrayList<>();
        petDetails.add(petDetail1);
        petDetails.add(petDetail2);
        petDetails.add(petDetail3);

        // 不同的service，会分成不同的invoice item
        MoeGroomingInvoiceItem invoiceItem1 = getInvoiceItem(SERVICE_ID_1, SERVICE_PRICE_1, TAX_AMOUNT_1, 1);
        MoeGroomingInvoiceItem invoiceItem2 = getInvoiceItem(SERVICE_ID_2, SERVICE_PRICE_2, TAX_AMOUNT_2, 1);
        MoeGroomingInvoiceItem invoiceItem3 = getInvoiceItem(SERVICE_ID_3, SERVICE_PRICE_3, TAX_AMOUNT_3, 1);

        List<MoeGroomingInvoiceItem> invoiceItems = new ArrayList<>();
        invoiceItems.add(invoiceItem1);
        invoiceItems.add(invoiceItem2);
        invoiceItems.add(invoiceItem3);

        Map<Integer, BigDecimal> expectedResult = new HashMap<>();
        expectedResult.put(1, new BigDecimal(TAX_AMOUNT_1));
        expectedResult.put(2, new BigDecimal(TAX_AMOUNT_2));
        expectedResult.put(3, new BigDecimal(TAX_AMOUNT_3));

        assertThat(calculateTax(petDetails, invoiceItems)).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateCollectedServicePriceV2_withoutProduct() {
        // case 1: collectedRevenue = 0, discount = 5
        CalCollectedPriceParams params = new CalCollectedPriceParams()
                .setPaidAmount(BigDecimal.valueOf(100))
                .setRefundAmount(BigDecimal.valueOf(100))
                .setExpectedServiceTax(BigDecimal.valueOf(1))
                .setExpectedTips(BigDecimal.valueOf(10))
                .setServiceDiscountAmount(BigDecimal.valueOf(5))
                .setTotalServiceSale(BigDecimal.valueOf(100))
                .setExpectedProductTax(null)
                .setProductDiscountAmount(null)
                .setTotalProductSale(null);
        CollectedPriceDTO result1 = reportCalculateService.calculateCollectedPrice(params);
        // result 1: collectedServiceTax = 0, tips = 0, discount = 5, servicePrice = 5
        assertThat(result1.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result1.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(5));

        // case 2: tax < collectedRevenue <= tax + tips
        params.setRefundAmount(BigDecimal.valueOf(90));
        CollectedPriceDTO result2 = reportCalculateService.calculateCollectedPrice(params);
        // result 2: collectedServiceTax = 1, tips = 9, discount = 5, servicePrice = 5
        assertThat(result2.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result2.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(9));
        assertThat(result2.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result2.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(5));

        // case 3: collectedRevenue > tax + tips
        params.setRefundAmount(BigDecimal.valueOf(50));
        CollectedPriceDTO result3 = reportCalculateService.calculateCollectedPrice(params);
        // result 3: collectedServiceTax = 1, tips = 10, discount = 5, servicePrice = 44
        assertThat(result3.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result3.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(10));
        assertThat(result3.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result3.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(44));
    }

    @Test
    public void testCalculateCollectedServicePriceV2_withProduct() {
        /*
         * collectedRevenue = paidAmount - refundAmount
         */
        // case 1: collectedRevenue = 0, serviceDiscount = 5, productDiscount = 0
        CalCollectedPriceParams params = new CalCollectedPriceParams()
                .setPaidAmount(BigDecimal.valueOf(120))
                .setRefundAmount(BigDecimal.valueOf(120))
                .setExpectedServiceTax(BigDecimal.valueOf(1))
                .setExpectedTips(BigDecimal.valueOf(10))
                .setServiceDiscountAmount(BigDecimal.valueOf(5))
                .setTotalServiceSale(BigDecimal.valueOf(100))
                .setTotalServiceChargeSale(BigDecimal.ZERO)
                .setExpectedProductTax(BigDecimal.valueOf(1))
                .setProductDiscountAmount(BigDecimal.valueOf(0))
                .setTotalProductSale(BigDecimal.valueOf(20));
        CollectedPriceDTO result1 = reportCalculateService.calculateCollectedPrice(params);
        // result 1: serviceTax, tips = 0, serviceDiscount = 5, collectedServicePrice = 5, productTax, discount,
        // collectedProductPrice = 0
        assertThat(result1.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result1.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result1.getCollectedProductTax()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getProductDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(0));
        assertThat(result1.getCollectedProductPrice()).isEqualByComparingTo(BigDecimal.valueOf(0));

        // case 2: totalTax < collectedRevenue <= totalTax + tips
        params.setRefundAmount(BigDecimal.valueOf(114));
        params.setProductDiscountAmount(BigDecimal.valueOf(10));
        CollectedPriceDTO result2 = reportCalculateService.calculateCollectedPrice(params);
        // result 2: serviceTax = 1, tips = 4, serviceDiscount = 5, collectedServicePrice = 5, productTax = 1, discount
        // = 10, collectedProductPrice = 10
        assertThat(result2.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result2.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(4));
        assertThat(result2.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result2.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result2.getCollectedProductTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result2.getProductDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(10));
        assertThat(result2.getCollectedProductPrice()).isEqualByComparingTo(BigDecimal.valueOf(10));

        // case 3: collectedRevenue > totalTax + tips
        params.setRefundAmount(BigDecimal.valueOf(60));
        CollectedPriceDTO result3 = reportCalculateService.calculateCollectedPrice(params);
        // result 3: serviceTax = 1, tips = 4, serviceDiscount = 5, collectedServicePrice = 5, productTax = 1, discount
        // = 10, collectedProductPrice = 10
        assertThat(result3.getCollectedServiceTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result3.getCollectedTips()).isEqualByComparingTo(BigDecimal.valueOf(10));
        assertThat(result3.getServiceDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(5));
        assertThat(result3.getCollectedServicePrice()).isEqualByComparingTo(BigDecimal.valueOf(44));
        assertThat(result3.getCollectedProductTax()).isEqualByComparingTo(BigDecimal.valueOf(1));
        assertThat(result3.getProductDiscountAmount()).isEqualByComparingTo(BigDecimal.valueOf(10));
        assertThat(result3.getCollectedProductPrice()).isEqualByComparingTo(BigDecimal.valueOf(19));
    }

    @Test
    public void testCalculateCollectedAmount() {
        /*
         * 前置 tax: 1, tips: 10, discount: 0/5
         * collectedItemPrice = collectedRevenue - tax - tips + discount
         */
        // case1: collectedRevenue = 0, discount = 0
        CalCollectedAmountParams params1 = new CalCollectedAmountParams(
                BigDecimal.ZERO, BigDecimal.valueOf(1), BigDecimal.valueOf(10), BigDecimal.valueOf(0));
        CollectedAmountCollection collectedAmount1 = calculateCollectedAmount(params1);
        // expect1: collectedTax = 0, collectedTips = 0, collectedDiscount = 0, collectedItemPrice = 0
        CollectedAmountCollection expectResult1 = new CollectedAmountCollection(
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        assertThat(collectedAmount1).isEqualTo(expectResult1);

        // case2: collectedRevenue = 0, discount = 5
        CalCollectedAmountParams params2 = new CalCollectedAmountParams(
                BigDecimal.ZERO, BigDecimal.valueOf(1), BigDecimal.valueOf(10), BigDecimal.valueOf(5));
        CollectedAmountCollection collectedAmount2 = calculateCollectedAmount(params2);
        // expect2: collectedTax = 0, collectedTips = 0, collectedDiscount = 5, collectedItemPrice = 5
        CollectedAmountCollection expectResult2 = new CollectedAmountCollection(
                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.valueOf(5), BigDecimal.valueOf(5), BigDecimal.ZERO);
        assertThat(collectedAmount2).isEqualTo(expectResult2);

        // case3: tax < collectedRevenue < tax + tips
        CalCollectedAmountParams params3 = new CalCollectedAmountParams(
                BigDecimal.valueOf(5), BigDecimal.valueOf(1), BigDecimal.valueOf(10), BigDecimal.valueOf(5));
        CollectedAmountCollection collectedAmount3 = calculateCollectedAmount(params3);
        // expect3: collectedTax = 1, collectedTips = 4, collectedDiscount = 5, collectedItemPrice = 5
        CollectedAmountCollection expectResult3 = new CollectedAmountCollection(
                BigDecimal.valueOf(1),
                BigDecimal.valueOf(4),
                BigDecimal.valueOf(5),
                BigDecimal.valueOf(5),
                BigDecimal.ZERO);
        assertThat(collectedAmount3).isEqualTo(expectResult3);

        // case4: collectedRevenue = tax + tips
        CalCollectedAmountParams params4 = new CalCollectedAmountParams(
                BigDecimal.valueOf(11), BigDecimal.valueOf(1), BigDecimal.valueOf(10), BigDecimal.valueOf(5));
        CollectedAmountCollection collectedAmount4 = calculateCollectedAmount(params4);
        // expect4: collectedTax = 1, collectedTips = 10, collectedDiscount = 5, collectedItemPrice = 5
        CollectedAmountCollection expectResult4 = new CollectedAmountCollection(
                BigDecimal.valueOf(1),
                BigDecimal.valueOf(10),
                BigDecimal.valueOf(5),
                BigDecimal.valueOf(5),
                BigDecimal.ZERO);
        assertThat(collectedAmount4).isEqualTo(expectResult4);

        // case4: collectedRevenue > tax + tips
        CalCollectedAmountParams params5 = new CalCollectedAmountParams(
                BigDecimal.valueOf(20), BigDecimal.valueOf(1), BigDecimal.valueOf(10), BigDecimal.valueOf(5));
        CollectedAmountCollection collectedAmount5 = calculateCollectedAmount(params5);
        // expect4: collectedTax = 1, collectedTips = 10, collectedDiscount = 5, collectedItemPrice = 14
        CollectedAmountCollection expectResult5 = new CollectedAmountCollection(
                BigDecimal.valueOf(1),
                BigDecimal.valueOf(10),
                BigDecimal.valueOf(5),
                BigDecimal.valueOf(14),
                BigDecimal.valueOf(9));
        assertThat(collectedAmount5).isEqualTo(expectResult5);
    }

    /***********************************************构建对象方法************************************************/
    private ReportWebApptPetDetail getPetDetail(Integer petDetailId, Integer serviceId, String servicePrice) {
        ReportWebApptPetDetail petDetail = new ReportWebApptPetDetail();
        petDetail.setId(petDetailId);
        petDetail.setServiceId(serviceId);
        petDetail.setServicePrice(new BigDecimal(servicePrice));
        petDetail.setPetId(100);

        return petDetail;
    }

    private MoeGroomingInvoiceItem getInvoiceItem(
            Integer serviceId, String servicePrice, String taxAmount, Integer quantity) {
        MoeGroomingInvoiceItem invoiceItem = new MoeGroomingInvoiceItem();
        invoiceItem.setServiceId(serviceId);
        invoiceItem.setServiceUnitPrice(new BigDecimal(servicePrice));
        invoiceItem.setTaxAmount(new BigDecimal(taxAmount));
        invoiceItem.setQuantity(quantity);
        invoiceItem.setPetId(100);
        invoiceItem.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        return invoiceItem;
    }
}
