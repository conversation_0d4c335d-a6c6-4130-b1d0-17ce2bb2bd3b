package com.moego.server.grooming.service.google;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CalendarCreateHelperTest {

    private List<GroomingPetInfoDetailDTO> buildStaff() {
        List<GroomingPetInfoDetailDTO> groomingPetInfoDetailDTOS = new ArrayList<>();
        GroomingPetInfoDetailDTO groomingPetInfoDetailDTO = new GroomingPetInfoDetailDTO();
        groomingPetInfoDetailDTO.setPetId(70001);
        groomingPetInfoDetailDTO.setPetName("PetName");
        groomingPetInfoDetailDTO.setPetTypeId(899991);
        groomingPetInfoDetailDTO.setTypeName("PetTypeName");
        GroomingPetServiceDTO groomingPetServiceDTO = new GroomingPetServiceDTO();
        groomingPetServiceDTO.setPetDetailId(200001);
        groomingPetServiceDTO.setStaffId(50001);
        GroomingPetServiceDTO groomingPetServiceDTO4 = new GroomingPetServiceDTO();
        groomingPetServiceDTO4.setPetDetailId(200002);
        groomingPetServiceDTO4.setStaffId(50001);
        List<GroomingPetServiceDTO> groomingPetServiceDTOS = new ArrayList<>();
        groomingPetServiceDTOS.add(groomingPetServiceDTO);
        groomingPetServiceDTOS.add(groomingPetServiceDTO4);
        groomingPetInfoDetailDTO.setGroomingPetServiceDTOS(groomingPetServiceDTOS);
        groomingPetInfoDetailDTOS.add(groomingPetInfoDetailDTO);

        GroomingPetInfoDetailDTO groomingPetInfoDetailDTO2 = new GroomingPetInfoDetailDTO();
        groomingPetInfoDetailDTO2.setPetId(70002);
        groomingPetInfoDetailDTO2.setPetName("PetName2");
        groomingPetInfoDetailDTO2.setPetTypeId(899992);
        groomingPetInfoDetailDTO2.setTypeName("PetTypeName2");
        GroomingPetServiceDTO groomingPetServiceDTO2 = new GroomingPetServiceDTO();
        groomingPetServiceDTO2.setPetDetailId(200002);
        groomingPetServiceDTO2.setStaffId(50001);
        groomingPetInfoDetailDTO2.setGroomingPetServiceDTOS(Collections.singletonList(groomingPetServiceDTO2));
        groomingPetInfoDetailDTOS.add(groomingPetInfoDetailDTO2);

        GroomingPetInfoDetailDTO groomingPetInfoDetailDTO3 = new GroomingPetInfoDetailDTO();
        groomingPetInfoDetailDTO3.setPetId(70003);
        groomingPetInfoDetailDTO3.setPetName("PetName3");
        groomingPetInfoDetailDTO3.setPetTypeId(899991);
        groomingPetInfoDetailDTO3.setTypeName("PetTypeName");
        GroomingPetServiceDTO groomingPetServiceDTO3 = new GroomingPetServiceDTO();
        groomingPetServiceDTO3.setPetDetailId(200002);
        groomingPetServiceDTO3.setStaffId(50001);
        groomingPetInfoDetailDTO3.setGroomingPetServiceDTOS(Collections.singletonList(groomingPetServiceDTO3));
        groomingPetInfoDetailDTOS.add(groomingPetInfoDetailDTO3);

        return groomingPetInfoDetailDTOS;
    }

    @Test
    void getCalenderEventTitle() {
        String petAmountString = CalendarCreateHelper.getCalenderEventTitle(
                "CustomerName", buildStaff(), Arrays.asList(200001, 200002), 50001, "StaffName");
        assertThat(petAmountString).isEqualTo("CustomerName (1PetTypeName2, 2PetTypeNames) by StaffName");
    }

    @Test
    void getPetAmount() {
        String petAmountString = CalendarCreateHelper.getPetAmount(buildStaff(), Arrays.asList(200001, 200002), 50001);
        assertThat(petAmountString).isEqualTo("1PetTypeName2, 2PetTypeNames");
    }
}
