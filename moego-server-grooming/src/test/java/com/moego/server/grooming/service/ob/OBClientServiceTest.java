package com.moego.server.grooming.service.ob;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerResponse;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.params.CustomerInfoIdParams;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OBClientServiceTest {

    @InjectMocks
    private OBClientService clientService;

    @Mock
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Mock
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Test
    public void validCustomerBlocked() {
        // params
        Long companyId = 100001L;
        Integer businessId = 100001;
        Integer customerId = 101001;

        // mock getCustomerWithDeletedHasBusinessId
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        customerInfoDto.setBusinessId(businessId);
        customerInfoDto.setCustomerId(customerId);
        customerInfoDto.setStatus(CommonConstant.NORMAL);
        customerInfoDto.setIsBlockOnlineBooking(CustomerContactEnum.CUSTOMER_IS_BLOCK);
        CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
        customerInfoIdParams.setBusinessId(businessId);
        customerInfoIdParams.setCustomerId(customerId);
        Mockito.doReturn(customerInfoDto)
                .when(iCustomerCustomerClient)
                .getCustomerWithDeletedHasBusinessId(customerInfoIdParams);

        Mockito.doReturn(ListBlockedCustomerResponse.newBuilder()
                        .addCustomerBlockInfos(ListBlockedCustomerResponse.CustomerBlockInfo.newBuilder()
                                .setCustomerId(customerId)
                                .addServiceItemTypes(ServiceItemType.GROOMING)
                                .build())
                        .build())
                .when(customerAvailabilityServiceBlockingStub)
                .listBlockedCustomer(Mockito.any());

        // invoke
        boolean isBlocked = clientService.validCustomerBlocked(null, companyId, businessId, customerId);
        Assertions.assertThat(isBlocked).isTrue();
    }
}
