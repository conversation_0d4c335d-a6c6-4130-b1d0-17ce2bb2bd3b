<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingNoteMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="note" jdbcType="LONGVARCHAR" property="note" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, customer_id, grooming_id, type, create_by, update_by, create_time,
    update_time, is_deleted, pet_id, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    note
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_note
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_note
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_note (business_id, customer_id, grooming_id,
      type, create_by, update_by,
      create_time, update_time, is_deleted,
      pet_id, company_id, note
      )
    values (#{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, #{groomingId,jdbcType=INTEGER},
      #{type,jdbcType=TINYINT}, #{createBy,jdbcType=INTEGER}, #{updateBy,jdbcType=INTEGER},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{isDeleted,jdbcType=BIT},
      #{petId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{note,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_note
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNoteExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_note
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=INTEGER},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.petId != null">
        pet_id = #{record.petId,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=INTEGER},
      update_by = #{record.updateBy,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      pet_id = #{record.petId,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      note = #{record.note,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=INTEGER},
      update_by = #{record.updateBy,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      pet_id = #{record.petId,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      pet_id = #{petId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      note = #{note,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingNote">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_note
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=INTEGER},
      update_by = #{updateBy,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      pet_id = #{petId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>


  <select id="selectByGroomingId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_note
    where grooming_id = #{groomingId}
    <if test="type != null">
      and type = #{type}
    </if>
    and is_deleted = false
  </select>

  <select id="queryCustomerHistoryComments" resultType="com.moego.server.grooming.dto.HistoryCommentsDTO">
    select mgn.note comments,
    mgn.create_time createTime,
    mgn.update_time updateTime,
    mgn.update_by editor,
    mgn.create_by creator,
    mgn.grooming_id groomingId,
    mgn.business_id businessId,
    mga.appointment_date appointmentDate,
    mga.appointment_end_date appointmentEndDate
    from moe_grooming_note mgn left join moe_grooming_appointment mga on mgn.grooming_id = mga.id
    where mgn.type = 2 and mgn.is_deleted = false
    and mga.is_deprecate = 0 and mga.business_id = #{businessId} and mga.customer_id = #{customerId}
    <if test="petId != null">
      and mgn.pet_id = #{petId}
    </if>
    order by mgn.update_time desc
  </select>
    <insert id="batchInsert">
    INSERT INTO `moe_grooming_note`
    (`business_id`, `company_id`, `customer_id`, `grooming_id`, `note`, `type`, `create_by`,`update_by`, `create_time`, `update_time`)
    VALUES
    <foreach collection="recordList" item="record" separator=",">
        (#{record.businessId}, #{record.companyId}, #{record.customerId}, #{record.groomingId}, #{record.note},
        #{record.type}, #{record.createBy}, #{record.createBy}
        , #{record.createTime}, #{record.updateTime})
    </foreach>
    </insert>

    <select id="selectByGroomingIds" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_note
    where grooming_id in
    <foreach close=")" collection="groomingIds" item="item" open="(" separator=",">
      #{item}
    </foreach>

    <if test="type != null">
      and type = #{type}
    </if>
    and is_deleted = false
  </select>
    <select id="selectLastNoteByCustomerId" resultMap="ResultMapWithBLOBs">
        SELECT <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
        FROM moe_grooming_note
        WHERE
        customer_id = #{customerId}
        AND type = #{type}
        AND is_deleted = false
        <if test="businessId != null">
          and business_id = #{businessId}
        </if>
        <if test="companyId != null">
          and company_id = #{companyId}
        </if>
        ORDER BY update_time DESC
        LIMIT 1
    </select>
</mapper>
