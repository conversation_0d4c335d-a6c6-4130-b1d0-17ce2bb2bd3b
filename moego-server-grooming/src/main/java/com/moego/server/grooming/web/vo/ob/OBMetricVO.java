package com.moego.server.grooming.web.vo.ob;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBMetricsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
@Builder
public record OBMetricVO(
        @Schema(description = "Metrics name") OBMetricsEnum name,
        @Schema(description = "Metrics, key: metrics type, value: metrics value")
                Map<OBMetricsTypeEnum, Object> metrics) {}
