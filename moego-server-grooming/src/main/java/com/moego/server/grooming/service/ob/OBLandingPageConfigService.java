package com.moego.server.grooming.service.ob;

import static com.moego.common.enums.groomingreport.GroomingReportConst.PRESET_TAGS;
import static com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO.LandingPageAmenitiesVO;
import static com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO.LandingPageConfigClientReviewRecordVO;
import static com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO.LandingPageConfigTeamVO;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.FeatureQuotaDto;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.PetTypeEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.service.membership.v1.ListMembershipsRequest;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineLandingPageConfigMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfigExample;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import com.moego.server.grooming.mapperbean.ObConfigClientReview;
import com.moego.server.grooming.mapperbean.ObConfigTeam;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.service.OBConfigClientReviewService;
import com.moego.server.grooming.service.OBConfigTeamService;
import com.moego.server.grooming.service.ob.component.ILandingPageComponentService;
import com.moego.server.grooming.service.utils.LandingPageConfigHelper;
import com.moego.server.grooming.web.params.OBLandingPageConfigParams;
import com.moego.server.grooming.web.result.ob.BriefOBProfileResult;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigClientVO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.message.api.IReviewRecordService;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.payment.client.IPaymentPlanClient;
import jakarta.annotation.Nullable;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBLandingPageConfigService {

    private final MoeBookOnlineProfileMapper bookOnlineProfileMapper;
    private final MoeBookOnlineLandingPageConfigMapper landingPageConfigMapper;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final Map<String, ILandingPageComponentService> landingPageComponentServiceMap;
    private final OBLandingPageGalleryService landingPageGalleryService;
    private final IPaymentPlanClient paymentPlanClient;
    private final IBusinessBusinessService businessApi;
    private final LandingPageConfigHelper landingPageConfigHelper;
    private final OBConfigClientReviewService obConfigClientReviewService;
    private final OBConfigTeamService obConfigTeamService;
    private final IBusinessStaffService staffApi;
    private final IReviewRecordService reviewRecordApi;
    private final ICustomerCustomerService customerApi;
    private final IPetService petApi;
    private final MoeGroomingReportMapper groomingReportMapper;
    private final PermissionHelper permissionHelper;
    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipServiceBlockingStub;

    public MoeBookOnlineLandingPageConfig selectByBusinessId(Integer businessId) {
        return landingPageConfigMapper.selectByBusinessId(businessId);
    }

    public List<MoeBookOnlineLandingPageConfig> listByBusinessId(List<Integer> businessIdList) {
        return landingPageConfigMapper.listByBusinessId(businessIdList);
    }

    public OBLandingPageConfigVO getLandingPageConfigVOByBusinessId(Integer businessId) {
        MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigMapper.selectByBusinessId(businessId);
        OBLandingPageConfigVO landingPageConfigVO = new OBLandingPageConfigVO();
        if (isNull(landingPageConfig)) {
            log.error("biz: [{}] does not have landing page config", businessId);
            return landingPageConfigVO;
        }
        BeanUtils.copyProperties(landingPageConfig, landingPageConfigVO);
        // Page components
        if (StringUtils.hasText(landingPageConfig.getPageComponents())) {
            landingPageConfigVO.setPageComponents(landingPageConfigHelper.getComponentEnabledMap(landingPageConfig));
        }
        // Amenity
        if (StringUtils.hasText(landingPageConfig.getAmenities())) {
            LandingPageAmenitiesVO amenitiesVO =
                    JsonUtil.toBean(landingPageConfig.getAmenities(), LandingPageAmenitiesVO.class);
            landingPageConfigVO.setAmenities(amenitiesVO);
        }
        landingPageConfigVO.setGalleryList(landingPageGalleryService.listAvailableLandingPageGallery(businessId));

        landingPageConfigVO.setClientReviews(new OBLandingPageConfigVO.LandingPageConfigClientReviewVO()
                .setIsDisplayClientReviewShowcasePhoto(landingPageConfig.getIsDisplayClientReviewShowcasePhoto())
                .setReviewRecords(toClientReviewVOs(businessId, obConfigClientReviewService.list(businessId))));

        landingPageConfigVO.setTeams(toTeamVOs(businessId, obConfigTeamService.listForAllStaff(businessId)));

        var membershipList = membershipServiceBlockingStub
                .listMemberships(ListMembershipsRequest.newBuilder()
                        .setCompanyId(landingPageConfig.getCompanyId())
                        .addStatusIn(MembershipModel.Status.ACTIVE)
                        .setOnlyOnlineBookingEnabled(true)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1)
                                .build())
                        .build())
                .getMembershipsList();
        if (!CollectionUtils.isEmpty(membershipList)) {
            landingPageConfigVO.setSupportMembership(true);
        }

        return landingPageConfigVO;
    }

    private List<LandingPageConfigTeamVO> toTeamVOs(Integer businessId, List<ObConfigTeam> teams) {
        if (ObjectUtils.isEmpty(teams)) {
            return List.of();
        }

        List<Integer> staffIds = teams.stream().map(ObConfigTeam::getStaffId).toList();

        Map<Integer, MoeStaffDto> idToStaff =
                staffApi.getStaffList(new StaffIdListParams(businessId, staffIds)).stream()
                        .collect(toMap(MoeStaffDto::getId, identity()));

        return teams.stream().map(team -> toTeamVO(team, idToStaff)).toList();
    }

    private static LandingPageConfigTeamVO toTeamVO(ObConfigTeam team, Map<Integer, MoeStaffDto> idToStaff) {
        LandingPageConfigTeamVO vo = new LandingPageConfigTeamVO();
        vo.setStaffId(team.getStaffId());
        vo.setInstagramLink(team.getInstagramLink());
        vo.setIntroduction(team.getIntroduction());
        vo.setTags(JsonUtil.toList(team.getTags(), String.class));
        vo.setIsEnabled(team.getIsEnabled());
        vo.setStaffName(Optional.ofNullable(idToStaff.get(team.getStaffId()))
                .map(OBLandingPageConfigService::getFormattedName)
                .orElse(""));
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLandingPageConfig(
            Integer businessId, Long companyId, OBLandingPageConfigParams landingPageConfigParams) {
        MoeBookOnlineLandingPageConfig landingPageConfig = new MoeBookOnlineLandingPageConfig();
        BeanUtils.copyProperties(landingPageConfigParams, landingPageConfig);
        if (nonNull(landingPageConfigParams.getIsPublished())) {
            var curConfig = landingPageConfigMapper.selectByBusinessId(businessId);
            if (!curConfig.getIsPublished().equals(landingPageConfigParams.getIsPublished())) {
                permissionHelper.checkPermission(
                        companyId, new HashSet<>(), AuthContext.get().staffId(), PermissionEnums.ONLINE_BOOKING_SWITCH);
            }
        }

        // Set biz id and json field
        landingPageConfig.setBusinessId(businessId);
        if (MapUtils.isNotEmpty(landingPageConfigParams.getPageComponents())) {
            // Check page component name
            landingPageConfigParams
                    .getPageComponents()
                    .forEach((component, enable) -> LandingPageComponentEnum.getEnumByComponent(component));
            landingPageConfig.setPageComponents(JsonUtil.toJson(landingPageConfigParams.getPageComponents()));
        }
        if (nonNull(landingPageConfigParams.getAmenities())) {
            landingPageConfig.setAmenities(JsonUtil.toJson(landingPageConfigParams.getAmenities()));
        }
        if (nonNull(landingPageConfigParams.getGalleryList())) {
            landingPageGalleryService.fullUpdateLandingPageGallery(
                    businessId, companyId, landingPageConfigParams.getGalleryList());
        }

        Optional.ofNullable(landingPageConfigParams.getClientReviews())
                .map(OBLandingPageConfigParams.OBConfigClientReviewParams::getReviewRecords)
                .ifPresent(
                        reviews -> obConfigClientReviewService.reset(businessId, toClientReviews(businessId, reviews)));

        Optional.ofNullable(landingPageConfigParams.getTeams())
                .ifPresent(teams -> obConfigTeamService.reset(businessId, toTeams(businessId, teams)));

        // Disable OB
        if (nonNull(landingPageConfigParams.getIsPublished())
                && BooleanUtils.isFalse(landingPageConfigParams.getIsPublished())) {
            MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
            businessBookOnline.setBusinessId(businessId);
            businessBookOnline.setIsEnable(CommonConstant.DISABLE);
            bookOnlineMapper.updateInfoByBusinessId(businessBookOnline);
        }

        Optional.ofNullable(landingPageConfigParams.getClientReviews())
                .map(OBLandingPageConfigParams.OBConfigClientReviewParams::getIsDisplayClientReviewShowcasePhoto)
                .ifPresent(landingPageConfig::setIsDisplayClientReviewShowcasePhoto);

        landingPageConfigMapper.updateByBusinessIdSelective(landingPageConfig);
    }

    private static List<ObConfigClientReview> toClientReviews(
            Integer businessId, List<OBLandingPageConfigParams.OBConfigClientReviewRecordParams> reviews) {
        return IntStream.range(0, reviews.size())
                .mapToObj(i -> {
                    ObConfigClientReview clientReview = toClientReview(businessId, reviews.get(i));
                    clientReview.setSort(i);
                    return clientReview;
                })
                .toList();
    }

    private static ObConfigClientReview toClientReview(
            Integer businessId, OBLandingPageConfigParams.OBConfigClientReviewRecordParams review) {
        ObConfigClientReview entity = new ObConfigClientReview();
        entity.setBusinessId(businessId);
        entity.setReviewId(review.getReviewId());
        entity.setCustomerId(review.getCustomerId());
        entity.setPetId(review.getPetId());
        return entity;
    }

    /**
     * Get business ID by anonymous params
     * TODO add validation
     *
     * @param anonymousParams bookOnlineName or domainName
     * @return business ID
     */
    public BusinessCompanyPO getBusinessIdAndCompanyIdByAnonymous(OBAnonymousParams anonymousParams) {
        if (StringUtils.hasText(anonymousParams.getDomain())) {
            MoeBookOnlineLandingPageConfig landingPageConfig =
                    landingPageConfigMapper.selectByDomainName(anonymousParams.getDomain());
            if (nonNull(landingPageConfig)) {
                return new BusinessCompanyPO(landingPageConfig.getBusinessId(), landingPageConfig.getCompanyId());
            }
        } else if (StringUtils.hasText(anonymousParams.getName())) {
            BusinessCompanyPO businessCompanyPO = bookOnlineMapper.selectByBookOnlineName(anonymousParams.getName());
            if (isNull(businessCompanyPO)) {
                throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
            }
            return businessCompanyPO;
        }
        return null;
    }

    /**
     * Get landing page config by business ID
     *
     * @param businessId business ID
     * @return landing page config
     */
    public MoeBookOnlineLandingPageConfig getLandingPageConfigByBusinessId(Integer businessId) {
        MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigMapper.selectByBusinessId(businessId);
        if (isNull(landingPageConfig)) {
            return null;
        }
        if (BooleanUtils.isFalse(landingPageConfig.getIsPublished())) {
            throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
        }
        return landingPageConfig;
    }

    /**
     * Build landing page config to client component view model
     *
     * @param businessId business ID
     * @return client component vo
     */
    public OBLandingPageConfigClientVO getLandingPageConfigClientVO(Integer businessId) {
        MoeBookOnlineLandingPageConfig landingPageConfig = this.getLandingPageConfigByBusinessId(businessId);
        if (isNull(landingPageConfig)) {
            return new OBLandingPageConfigClientVO();
        }
        // Get plan feature list
        Map<String, FeatureQuotaDto> planFeatureMap = paymentPlanClient.queryCompanyPlanFeatureByBid(businessId);
        OBLandingPageConfigClientVO result = new OBLandingPageConfigClientVO();
        // Set page components
        if (StringUtils.hasText(landingPageConfig.getPageComponents())) {
            Map<String, Boolean> componentToEnabled = landingPageConfigHelper.getComponentEnabledMap(landingPageConfig);
            List<BaseComponentVO> componentList = componentToEnabled.entrySet().parallelStream()
                    // Enable the module and have permissions
                    .filter(entry -> {
                        String component = entry.getKey();
                        Boolean enable = entry.getValue();
                        FeatureQuotaDto featureQuotaDto = planFeatureMap.get(component);
                        return (BooleanUtils.isTrue(enable)
                                && (isNull(featureQuotaDto) || BooleanUtils.isTrue(featureQuotaDto.getEnable())));
                    })
                    .map(entry -> landingPageComponentServiceMap.get(entry.getKey()))
                    .filter(Objects::nonNull)
                    .map(service -> service.getPageComponent(landingPageConfig))
                    .toList();
            result.setPageComponents(componentList);
        }
        // GA
        FeatureQuotaDto quotaDto = planFeatureMap.get(FeatureConst.FC_GOOGLE_ANALYTICS);
        if (nonNull(quotaDto) && BooleanUtils.isTrue(quotaDto.getEnable())) {
            result.setGaMeasurementId(landingPageConfig.getGaMeasurementId());
        }
        // Set other landing page config
        return result.setThemeColor(landingPageConfig.getThemeColor())
                .setThankYouPageUrl(landingPageConfig.getThankYouPageUrl());
    }

    /**
     * initialize landing page 3.0 config
     *
     * @param businessDto    biz info
     * @param bookOnlineName book online name
     */
    public void initializeLandingPageConfig(MoeBusinessDto businessDto, String bookOnlineName) {
        var moeBookOnlineLandingPageConfig = landingPageConfigMapper.selectByBusinessId(businessDto.getId());
        if (Objects.nonNull(moeBookOnlineLandingPageConfig)) {
            return;
        }
        Integer businessId = businessDto.getId();
        MoeBookOnlineLandingPageConfig landingPageConfig = new MoeBookOnlineLandingPageConfig();
        landingPageConfig.setBusinessId(businessId);
        landingPageConfig.setCompanyId(businessDto.getCompanyId().longValue());
        landingPageConfig.setAboutUs("");
        landingPageConfig.setWelcomePageMessage("Welcome to ".concat(businessDto.getBusinessName()));
        landingPageConfig.setIsPublished(Boolean.FALSE);
        landingPageConfig.setUrlDomainName(bookOnlineName);
        landingPageConfig.setPageComponents(JsonUtil.toJson(getComponentEnabledMapFromBusiness(businessDto)));
        log.info("biz: [{}] initialize landing page 3.0 business info: [{}]", businessId, businessDto);
        landingPageConfigMapper.insertSelective(landingPageConfig);
    }

    /**
     * inherited landing page 3.0 config
     *
     * @param businessId biz ID
     */
    public void inheritedLandingPageConfig(Integer businessId, Long companyId) {
        MoeBusinessBookOnline businessBookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        MoeBookOnlineProfile bookOnlineProfile = bookOnlineProfileMapper.selectByBusinessId(businessId);
        MoeBookOnlineLandingPageConfig landingPageConfig = new MoeBookOnlineLandingPageConfig();
        landingPageConfig.setBusinessId(businessId);
        landingPageConfig.setCompanyId(companyId);
        landingPageConfig.setAboutUs(bookOnlineProfile.getDescription());
        landingPageConfig.setWelcomePageMessage(businessBookOnline.getDescription());
        landingPageConfig.setThemeColor(bookOnlineProfile.getButtonColor());
        landingPageConfig.setIsPublished(Boolean.TRUE);
        landingPageConfig.setUrlDomainName(businessBookOnline.getBookOnlineName());
        landingPageConfig.setPageComponents(JsonUtil.toJson(
                getComponentEnabledMapFromBusiness(businessApi.getBusinessInfo(new InfoIdParams(businessId)))));
        log.info("biz: [{}] inherited landing page 3.0 config: [{}]", businessId, landingPageConfig);
        landingPageConfigMapper.insertSelective(landingPageConfig);
    }

    private static Map<String, Boolean> getComponentEnabledMapFromBusiness(MoeBusinessDto businessDto) {
        Map<String, Boolean> enabledMap = Arrays.stream(LandingPageComponentEnum.values())
                .collect(toMap(LandingPageComponentEnum::getComponent, LandingPageComponentEnum::isEnable));
        // Disable address and contact if not set
        // see https://moego.atlassian.net/browse/ERP-7447
        if (!StringUtils.hasText(businessDto.getAddress()) && !StringUtils.hasText(businessDto.getAddress1())) {
            enabledMap.put(LandingPageComponentEnum.ADDRESS.getComponent(), false);
        }
        if (!StringUtils.hasText(businessDto.getPhoneNumber())) {
            enabledMap.put(LandingPageComponentEnum.CONTACT.getComponent(), false);
        }
        return enabledMap;
    }

    private List<LandingPageConfigClientReviewRecordVO> toClientReviewVOs(
            Integer businessId, List<ObConfigClientReview> clientReviews) {
        List<Integer> reviewIds = clientReviews.stream()
                .map(ObConfigClientReview::getReviewId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, ReviewBoosterRecordDTO> reviewIdToReview =
                reviewRecordApi.listByIds(reviewIds).stream().collect(toMap(ReviewBoosterRecordDTO::getId, identity()));

        List<Integer> customerIds = clientReviews.stream()
                .map(ObConfigClientReview::getCustomerId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, MoeBusinessCustomerDTO> customerIdToCustomer =
                customerApi.queryCustomerListWithDeleted(new CustomerIdListParams(customerIds)).stream()
                        .collect(toMap(MoeBusinessCustomerDTO::getId, identity()));

        List<Integer> petIds = clientReviews.stream()
                .map(ObConfigClientReview::getPetId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, CustomerPetPetCodeDTO> petIdToPet = petApi.getCustomerPetInfoListByIdList(petIds).stream()
                .collect(toMap(CustomerPetDetailDTO::getPetId, identity()));

        List<Integer> appointmentIds = reviewIdToReview.values().stream()
                .map(ReviewBoosterRecordDTO::getAppointmentId)
                .filter(Objects::nonNull)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        List<MoeGroomingReport> groomingReports = getGroomingReports(businessId, appointmentIds);

        return clientReviews.stream()
                .map(review ->
                        toClientReviewVO(review, reviewIdToReview, customerIdToCustomer, petIdToPet, groomingReports))
                .toList();
    }

    private static LandingPageConfigClientReviewRecordVO toClientReviewVO(
            ObConfigClientReview clientReview,
            Map<Integer, ReviewBoosterRecordDTO> reviewIdToReview,
            Map<Integer, MoeBusinessCustomerDTO> customerIdToCustomer,
            Map<Integer, CustomerPetPetCodeDTO> petIdToPet,
            List<MoeGroomingReport> groomingReports) {
        return new LandingPageConfigClientReviewRecordVO()
                .setReviewId(clientReview.getReviewId())
                .setReviewContent(Optional.ofNullable(clientReview.getReviewId())
                        .map(reviewIdToReview::get)
                        .map(ReviewBoosterRecordDTO::getReviewContent)
                        .map(content -> getFilteredContent(content, PRESET_TAGS))
                        .orElse(""))
                .setCustomerId(clientReview.getCustomerId())
                .setCustomerName(Optional.ofNullable(clientReview.getCustomerId())
                        .map(customerIdToCustomer::get)
                        .map(MoeBusinessCustomerDTO::getFirstName)
                        .orElse(""))
                .setShowcasePhotoUrl(getShowcase(
                        groomingReports,
                        Optional.ofNullable(reviewIdToReview.get(clientReview.getReviewId()))
                                .map(ReviewBoosterRecordDTO::getAppointmentId)
                                .orElse(null),
                        clientReview.getPetId()))
                .setPetId(clientReview.getPetId())
                .setPetTypeId(Optional.ofNullable(clientReview.getPetId())
                        .map(petIdToPet::get)
                        .map(CustomerPetPetCodeDTO::getPetTypeId)
                        .orElse(PetTypeEnum.DOG.getType()))
                .setReviewTime(Optional.ofNullable(clientReview.getReviewId())
                        .map(reviewIdToReview::get)
                        .map(ReviewBoosterRecordDTO::getCreateTime)
                        .map(ts -> ts * 1000L)
                        .map(Date::new)
                        .orElse(null));
    }

    private List<MoeGroomingReport> getGroomingReports(Integer businessId, List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        MoeGroomingReportExample example = new MoeGroomingReportExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andGroomingIdIn(appointmentIds);
        return groomingReportMapper.selectByExample(example);
    }

    private static List<ObConfigTeam> toTeams(
            Integer businessId, List<OBLandingPageConfigParams.OBConfigTeamParams> teams) {
        return teams.stream().map(team -> toTeam(businessId, team)).toList();
    }

    private static ObConfigTeam toTeam(Integer businessId, OBLandingPageConfigParams.OBConfigTeamParams team) {
        ObConfigTeam entity = new ObConfigTeam();
        entity.setBusinessId(businessId);
        entity.setStaffId(team.getStaffId());
        entity.setInstagramLink(team.getInstagramLink());
        entity.setIntroduction(team.getIntroduction());
        entity.setTags(Optional.ofNullable(team.getTags()).map(JsonUtil::toJson).orElse(null));
        entity.setIsEnabled(team.getIsEnabled());
        return entity;
    }

    /**
     * 格式化 staff name
     *
     * <p> Freeman Liu => Freeman L.
     * <p> Freeman liu => Freeman L.
     * <p> Freeman -   => Freeman
     */
    public static String getFormattedName(MoeStaffDto staff) {
        String firstName = StringUtils.capitalize(staff.getFirstName());
        if (StringUtils.hasText(staff.getLastName()) && staff.getLastName().matches("^[a-zA-Z].*")) {
            return "%s %s."
                    .formatted(
                            firstName,
                            StringUtils.capitalize(staff.getLastName().substring(0, 1)));
        }
        return firstName;
    }

    /**
     * 过滤掉 exclude keywords，去除首尾所有的逗号和空格
     *
     * @param content         content
     * @param excludeKeywords exclude keywords
     * @return filtered content
     */
    public static String getFilteredContent(String content, List<String> excludeKeywords) {
        for (String keyword : excludeKeywords) {
            content = content.replace(keyword, "");
        }
        // 去除首尾所有的逗号和空格
        return content.replaceAll("(^[,\\s]+)|([,\\s]+$)", "");
    }

    /**
     * 获取指定 appointmentId 和 petId 的 grooming report 的最后一张 showcase 图片。
     *
     * @param groomingReports grooming reports
     * @param appointmentId  appointment id
     * @param petId         pet id
     * @return showcase url
     */
    public static String getShowcase(
            List<MoeGroomingReport> groomingReports, @Nullable Integer appointmentId, @Nullable Integer petId) {
        if (petId == null || appointmentId == null) {
            return "";
        }
        return groomingReports.stream()
                .filter(report -> Objects.equals(report.getGroomingId(), appointmentId)
                        && Objects.equals(report.getPetId(), petId))
                .findFirst()
                .map(MoeGroomingReport::getContentJson)
                .map(content -> JsonUtil.toBean(content, GroomingReportInfoDTO.GroomingReportContent.class))
                .map(GroomingReportInfoDTO.GroomingReportContent::getShowcase)
                .filter(showcases -> !ObjectUtils.isEmpty(showcases))
                .map(showcases -> showcases.get(showcases.size() - 1)) // 兼容之前 before/after 的逻辑，取最后一张
                .orElse("");
    }

    public List<BriefOBProfileResult.BriefOBProfileDTO> getBriefOBProfileList(Long companyId) {
        MoeBookOnlineLandingPageConfigExample example = new MoeBookOnlineLandingPageConfigExample();
        example.createCriteria().andCompanyIdEqualTo(companyId);
        List<MoeBookOnlineLandingPageConfig> landingPageConfigList = landingPageConfigMapper.selectByExample(example);
        return landingPageConfigList.stream()
                .map(landingPageConfig -> new BriefOBProfileResult.BriefOBProfileDTO(
                        landingPageConfig.getUrlDomainName(), landingPageConfig.getBusinessId()))
                .toList();
    }
}
