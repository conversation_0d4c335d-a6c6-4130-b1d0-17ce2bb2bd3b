package com.moego.server.grooming.service.report.migrate;

import com.github.pagehelper.PageHelper;
import com.google.type.Date;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateReportsRequest;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateReportsResponse;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigForMigrateRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.utils.StringUtils;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.convert.ReportCardMigrateConverter;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingReportService;
import com.moego.server.grooming.service.report.migrate.validation.ReportCardMigrateValidationService;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationError;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationResult;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
 * Report Card 报告迁移服务
 * 负责将 moe_grooming_report 和 daily_report_config 数据迁移到 fulfillment_report
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 通过调用fulfillment服务接口进行数据操作
 * 6. 可追溯性：完整记录迁移过程
 * 7. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardReportMigrateService {

    private final MoeGroomingReportMapper groomingReportMapper;
    private final MoeGroomingAppointmentService appointmentService;
    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;
    private final MoeGroomingPetDetailMapper petDetailMapper;
    private final MoeGroomingReportService groomingReportService;
    private final ReportCardMigrateValidationService validationService;

    /**
     * 迁移报告数据
     */
    public ReportCardMigrateResultDTO migrate(Integer businessId, boolean isValidation) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "report_" + System.currentTimeMillis();

        log.info("开始迁移报告数据，taskId: {}, businessId: {}", taskId, businessId);

        try {
            long totalMigrated = 0;
            long totalSkipped = 0;
            long totalFailed = 0;
            long totalSource = 0;

            // 1. 迁移 grooming report 数据
            log.info("开始迁移 grooming report 数据");
            MigrateResult groomingResult = migrateGroomingReports(businessId, isValidation);
            totalMigrated += groomingResult.migratedCount;
            totalSkipped += groomingResult.skippedCount;
            totalFailed += groomingResult.failedCount;
            totalSource += groomingResult.sourceCount;

            // 2. 迁移 daily report 数据
            log.info("开始迁移 daily report 数据");
            MigrateResult dailyResult = migrateDailyReports(businessId, isValidation);
            totalMigrated += dailyResult.migratedCount;
            totalSkipped += dailyResult.skippedCount;
            totalFailed += dailyResult.failedCount;
            totalSource += dailyResult.sourceCount;

            log.info(
                    "报告数据迁移完成，总源数据: {}, 成功: {}, 跳过: {}, 失败: {}", totalSource, totalMigrated, totalSkipped, totalFailed);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId,
                    startTime,
                    ReportCardMigrateConfig.TableNames.REPORT,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT + "+"
                            + ReportCardMigrateConfig.SourceTables.DAILY_REPORT_CONFIG,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT,
                    totalSource,
                    totalMigrated,
                    totalSkipped);
        } catch (Exception e) {
            log.error("报告数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 迁移 grooming report 数据
     */
    private MigrateResult migrateGroomingReports(Integer businessId, boolean isValidation) {
        try {
            // 1. 首先统计总记录数
            long totalCount = getTotalGroomingReportCount(businessId);
            log.info("grooming report 数据总数: {} 条，开始分页迁移", totalCount);

            if (totalCount == 0) {
                return new MigrateResult(0, 0, 0, 0);
            }

            // 2. 分页查询并处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                // 分页查询数据
                List<MoeGroomingReport> batch = getGroomingReportBatch(businessId, offset, batchSize);

                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }

                log.info(
                        "处理第 {} 批 grooming report 数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条",
                        batchNumber,
                        offset,
                        batchSize,
                        batch.size());

                // 选择模式
                if (isValidation) {
                    log.info("[validation] batch number: {}, size: {}", batchNumber, batch.size());
                    performGroomingReportBatchValidation(batch, batchNumber);
                } else {
                    // 处理当前批次的数据
                    BatchResult batchResult = processBatchGroomingReports(batch);
                    migratedCount += batchResult.migratedCount;
                    skippedCount += batchResult.skippedCount;
                    failedCount += batchResult.failedCount;

                    // 输出进度信息
                    long processedCount = migratedCount + skippedCount + failedCount;
                    int progressPercent = ReportCardMigrateUtils.calculateProgressPercent(processedCount, totalCount);
                    log.info(
                            "grooming report 批次 {} 处理完成，当前进度: {} ({}/{}), 成功: {}, 跳过: {}, 失败: {}",
                            batchNumber,
                            progressPercent + "%",
                            processedCount,
                            totalCount,
                            batchResult.migratedCount,
                            batchResult.skippedCount,
                            batchResult.failedCount);
                }

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 如果查询到的数据少于批次大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    break;
                }
            }

            return new MigrateResult(totalCount, migratedCount, skippedCount, failedCount);

        } catch (Exception e) {
            log.error("迁移 grooming report 数据失败", e);
            throw e;
        }
    }

    /**
     * 迁移 daily report 数据
     */
    private MigrateResult migrateDailyReports(Integer businessId, boolean isValidation) {
        try {
            // 分页查询并处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                int pageNum = (offset / batchSize) + 1;
                // 分页查询数据
                ListDailyReportConfigForMigrateRequest.Builder builder =
                        ListDailyReportConfigForMigrateRequest.newBuilder()
                                .setPagination(PaginationRequest.newBuilder()
                                        .setPageSize(batchSize)
                                        .setPageNum(pageNum)
                                        .build());
                if (businessId != null) {
                    builder.setBusinessId(businessId);
                }

                List<DailyReportConfigMigrateDef> batch = dailyReportService
                        .listDailyReportConfigForMigrate(builder.build())
                        .getReportConfigsList();
                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }
                log.info(
                        "处理第 {} 批 daily report 数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条",
                        batchNumber,
                        offset,
                        batchSize,
                        batch.size());

                Map<String, CareCategory> careTypeMap = getCareTypeMap(batch);
                // 选择模式
                if (isValidation) {
                    log.info("[validation] batch number: {}, size: {}", batchNumber, batch.size());
                    performDailyReportBatchValidation(batch, batchNumber, careTypeMap);
                } else {
                    // 处理当前批次的数据
                    BatchResult batchResult = processBatchDailyReports(batch, careTypeMap);
                    migratedCount += batchResult.migratedCount;
                    skippedCount += batchResult.skippedCount;
                    failedCount += batchResult.failedCount;
                }

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 如果查询到的数据少于批次大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    break;
                }
            }
            return new MigrateResult(migratedCount, migratedCount, skippedCount, failedCount);
        } catch (Exception e) {
            log.error("迁移 daily report 数据失败", e);
            throw e;
        }
    }

    /**
     * 获取 grooming report 总数量
     */
    private long getTotalGroomingReportCount(Integer businessId) {
        MoeGroomingReportExample countExample = new MoeGroomingReportExample();
        MoeGroomingReportExample.Criteria countCriteria = countExample.createCriteria();

        if (businessId != null) {
            countCriteria.andBusinessIdEqualTo(businessId);
        }

        return groomingReportMapper.countByExample(countExample);
    }

    /**
     * 分页查询 grooming report 数据
     */
    private List<MoeGroomingReport> getGroomingReportBatch(Integer businessId, int offset, int limit) {
        // 计算页码（PageHelper 使用 1 基础的页码）
        int pageNum = (offset / limit) + 1;

        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(pageNum, limit, "id ASC");

        try {
            MoeGroomingReportExample example = new MoeGroomingReportExample();
            MoeGroomingReportExample.Criteria criteria = example.createCriteria();

            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            // 设置排序以确保分页结果的一致性
            example.setOrderByClause("id ASC");

            // 执行查询
            return groomingReportMapper.selectByExample(example);
        } finally {
            // 清理 PageHelper 的线程本地变量
            PageHelper.clearPage();
        }
    }

    /**
     * 处理单个批次的 grooming report 数据
     */
    private BatchResult processBatchGroomingReports(List<MoeGroomingReport> batch) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        try {
            // 批量检查是否已存在
            List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();

            batch = batch.stream()
                    .filter(report -> report.getGroomingId() != null && report.getGroomingId() > 0)
                    .toList();

            List<Integer> groomingIds =
                    batch.stream().map(MoeGroomingReport::getGroomingId).toList();
            List<MoeGroomingAppointment> appointments = appointmentService.getAppointmentByIdsV2(groomingIds);
            Map<Integer, String> appointmentDates = appointments.stream()
                    .collect(Collectors.toMap(
                            MoeGroomingAppointment::getId, MoeGroomingAppointment::getAppointmentDate, (v1, v2) -> v1));

            for (MoeGroomingReport report : batch) {
                uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                        .setBusinessId(report.getBusinessId().longValue())
                        .setAppointmentId(report.getGroomingId().longValue())
                        .setCareType(CareCategory.GROOMING)
                        .setPetId(report.getPetId())
                        .setServiceDate("")
                        .build());
            }

            GetReportsByUniqueKeysResponse existingReports =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 过滤出需要迁移的数据
            List<MoeGroomingReport> reportsToMigrate = new ArrayList<>();
            for (MoeGroomingReport report : batch) {
                boolean exists = existingReports.getReportsList().stream()
                        .anyMatch(existing -> existing.getBusinessId() == report.getBusinessId()
                                && existing.getAppointmentId() == report.getGroomingId()
                                && existing.getPetId() == report.getPetId()
                                && existing.getCareType() == CareCategory.GROOMING);

                if (exists) {
                    log.info(
                            "Grooming report 已存在，跳过迁移: businessId={}, appointmentId={}, id={}",
                            report.getBusinessId(),
                            report.getGroomingId(),
                            report.getId());
                    skippedCount++;
                } else {
                    reportsToMigrate.add(report);
                }
            }

            if (!reportsToMigrate.isEmpty()) {
                reportsToMigrate.forEach(report -> {
                    if (StringUtils.isEmpty(report.getUuid())) {
                        report.setUuid(groomingReportService.buildUniqueIdForGroomingReport());
                    }
                });
                // 转换数据
                List<FulfillmentReportSync> convertedReports =
                        ReportCardMigrateConverter.INSTANCE.convertReports(reportsToMigrate, appointmentDates);

                if (!convertedReports.isEmpty()) {
                    // 批量迁移
                    BatchMigrateReportsRequest request = BatchMigrateReportsRequest.newBuilder()
                            .addAllReports(convertedReports)
                            .build();

                    BatchMigrateReportsResponse response = fulfillmentServiceClient.batchMigrateReports(request);

                    migratedCount += response.getSuccessCount();
                    skippedCount += response.getSkippedCount();
                    failedCount += response.getFailedCount();

                    log.info(
                            "Grooming report 单次批量迁移完成: 成功={}, 跳过={}, 失败={}",
                            response.getSuccessCount(),
                            response.getSkippedCount(),
                            response.getFailedCount());
                }
            }
        } catch (Exception e) {
            log.error("批量迁移报告失败", e);
            failedCount += batch.size();
        }
        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    /**
     * 处理单个批次的 daily report 数据
     */
    private BatchResult processBatchDailyReports(
            List<DailyReportConfigMigrateDef> batch, Map<String, CareCategory> careTypeMap) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        try {
            // 批量检查是否已存在
            List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();

            for (DailyReportConfigMigrateDef report : batch) {
                uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                        .setBusinessId(report.getBusinessId())
                        .setAppointmentId(report.getAppointmentId())
                        .setCareType(careTypeMap.get(report.getPetId() + "_" + report.getAppointmentId()))
                        .setPetId(report.getPetId())
                        .setServiceDate(dateToString(report.getServiceDate()))
                        .build());
            }
            GetReportsByUniqueKeysResponse existingReports =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            List<DailyReportConfigMigrateDef> reportsToMigrate = new ArrayList<>();
            for (DailyReportConfigMigrateDef report : batch) {
                boolean exists = existingReports.getReportsList().stream()
                        .anyMatch(existing -> existing.getBusinessId() == report.getBusinessId()
                                && existing.getAppointmentId() == report.getAppointmentId()
                                && existing.getPetId() == report.getPetId()
                                && existing.getServiceDate().equals(dateToString(report.getServiceDate()))
                                && existing.getCareType()
                                        == careTypeMap.get(report.getPetId() + "_" + report.getAppointmentId()));

                if (exists) {
                    log.info(
                            "Daily report 已存在，跳过迁移: businessId={}, appointmentId={}, id={}",
                            report.getBusinessId(),
                            report.getAppointmentId(),
                            report.getId());
                    skippedCount++;
                } else {
                    reportsToMigrate.add(report);
                }
            }
            if (!reportsToMigrate.isEmpty()) {
                // 转换数据
                List<FulfillmentReportSync> convertedReports =
                        ReportCardMigrateConverter.INSTANCE.convertDailyReports(reportsToMigrate, careTypeMap);

                if (!convertedReports.isEmpty()) {
                    // 批量迁移
                    BatchMigrateReportsRequest request = BatchMigrateReportsRequest.newBuilder()
                            .addAllReports(convertedReports)
                            .build();

                    BatchMigrateReportsResponse response = fulfillmentServiceClient.batchMigrateReports(request);

                    migratedCount += response.getSuccessCount();
                    skippedCount += response.getSkippedCount();
                    failedCount += response.getFailedCount();

                    log.info(
                            "Daily report 单次批量迁移完成: 成功={}, 跳过={}, 失败={}",
                            response.getSuccessCount(),
                            response.getSkippedCount(),
                            response.getFailedCount());

                    // 批量校验：每迁移一批数据后立即校验该批次
                    performDailyReportBatchValidation(reportsToMigrate, reportsToMigrate.size(), careTypeMap);
                }
            }
        } catch (Exception e) {
            log.error("批量迁移报告失败", e);
            failedCount += batch.size();
        }
        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    public Map<String, CareCategory> getCareTypeMap(List<DailyReportConfigMigrateDef> reportsToMigrate) {
        // Step 1: 查出所有 petDetail
        List<MoeGroomingPetDetail> allPetDetails = petDetailMapper.selectByAppointmentIdList(reportsToMigrate.stream()
                .map(DailyReportConfigMigrateDef::getAppointmentId)
                .map(Long::intValue)
                .toList());

        // Step 2: 用 Set 存储有 BOARDING 服务的 key
        Set<String> boardingKeys = new HashSet<>();
        for (MoeGroomingPetDetail detail : allPetDetails) {
            if (detail.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
                boardingKeys.add(detail.getPetId() + "_" + detail.getGroomingId());
            }
        }

        // Step 3: 生成最终结果
        Map<String, CareCategory> result = new HashMap<>(reportsToMigrate.size());
        for (DailyReportConfigMigrateDef report : reportsToMigrate) {
            String key = report.getPetId() + "_" + report.getAppointmentId();
            CareCategory careType = boardingKeys.contains(key) ? CareCategory.BOARDING : CareCategory.DAYCARE;
            result.put(key, careType);
        }

        return result;
    }

    @NotNull
    private static String dateToString(Date date) {
        return String.format("%04d-%02d-%02d", date.getYear(), date.getMonth(), date.getDay());
    }

    /**
     * 执行 Grooming Report 批量校验
     */
    private void performGroomingReportBatchValidation(List<MoeGroomingReport> batch, int batchNumber) {
        try {
            log.info("开始批量校验 Grooming Report 数据，批次: {}, 数量: {}", batchNumber, batch.size());

            ValidationResult validationResult = validationService.validateGroomingReportBatch(batch, batchNumber);

            if (validationResult.isSuccess()) {
                log.info("批量校验通过: {}", validationResult.getSummary());
            } else {
                log.warn("批量校验发现问题: {}", validationResult.getSummary());

                // 记录详细错误信息
                for (ValidationError error : validationResult.getErrors()) {
                    log.warn("校验错误: {}", error.getFullDescription());
                }

                handleValidationErrors(validationResult.getErrors(), batchNumber, "Grooming Report");
            }

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            // 校验异常不应中断迁移流程，但需要记录
        }
    }

    /**
     * 执行 Daily Report 批量校验
     */
    private void performDailyReportBatchValidation(
            List<DailyReportConfigMigrateDef> batch, int batchNumber, Map<String, CareCategory> careTypeMap) {
        try {
            log.info("开始批量校验 Daily Report 数据，批次: {}, 数量: {}", batchNumber, batch.size());

            ValidationResult validationResult =
                    validationService.validateDailyReportBatch(batch, batchNumber, careTypeMap);

            if (validationResult.isSuccess()) {
                log.info("批量校验通过: {}", validationResult.getSummary());
            } else {
                log.warn("批量校验发现问题: {}", validationResult.getSummary());

                // 记录详细错误信息
                for (ValidationError error : validationResult.getErrors()) {
                    log.warn("校验错误: {}", error.getFullDescription());
                }

                handleValidationErrors(validationResult.getErrors(), batchNumber, "Daily Report");
            }

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            // 校验异常不应中断迁移流程，但需要记录
        }
    }

    /**
     * 处理校验错误
     * 根据错误类型和严重程度决定处理策略
     */
    private void handleValidationErrors(List<ValidationError> errors, int batchNumber, String dataType) {
        int criticalErrors = 0;
        int warningErrors = 0;

        for (ValidationError error : errors) {
            switch (error.getErrorType()) {
                case DATA_MISSING:
                case SYSTEM_ERROR:
                    criticalErrors++;
                    break;
                case DATA_INTEGRITY:
                case BUSINESS_LOGIC:
                    criticalErrors++;
                    break;
                case DATA_CONSISTENCY:
                    warningErrors++;
                    break;
            }
        }

        if (criticalErrors > 0) {
            log.error("{} 批次 {} 发现 {} 个严重错误，{} 个警告错误", dataType, batchNumber, criticalErrors, warningErrors);

            // 严重错误超过阈值时可以考虑中断迁移
            double errorRate = (double) criticalErrors / errors.size();
            if (errorRate > 0.1) { // 10% 错误率阈值
                log.error("{} 批次 {} 错误率为: {}", dataType, batchNumber, errorRate * 100);
            }
        } else if (warningErrors > 0) {
            log.warn("{} 批次 {} 发现 {} 个警告错误，可继续迁移", dataType, batchNumber, warningErrors);
        }
    }

    private record BatchResult(long migratedCount, long skippedCount, long failedCount) {}

    private record MigrateResult(long sourceCount, long migratedCount, long skippedCount, long failedCount) {}
}
