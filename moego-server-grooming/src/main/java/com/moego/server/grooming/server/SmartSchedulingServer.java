/*
 * @since 2024-09-17 18:08:02
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.grooming.server;

import com.moego.server.grooming.api.ISmartSchedulingServiceBase;
import com.moego.server.grooming.convert.SmartScheduleRequestConverter;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.params.ss.SmartScheduleRequest;
import com.moego.server.grooming.service.SmartScheduleService;
import com.moego.server.grooming.service.ob.SmartScheduleV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class SmartSchedulingServer extends ISmartSchedulingServiceBase {
    private final SmartScheduleService smartScheduleService;
    private final SmartScheduleV2Service smartScheduleV2Service;

    @Override
    public SmartScheduleResultDto smartSchedule(Integer businessId, SmartScheduleRequest request) {
        log.error("SmartSchedulingServer#smartSchedule is deprecated.");
        return smartScheduleV2Service.smartScheduleV2(
                businessId, SmartScheduleRequestConverter.INSTANCE.toSmartScheduleV2Request(request));
    }
}
