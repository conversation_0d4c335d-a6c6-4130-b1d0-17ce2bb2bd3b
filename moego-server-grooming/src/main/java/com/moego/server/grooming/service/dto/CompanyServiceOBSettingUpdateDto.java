package com.moego.server.grooming.service.dto;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CompanyServiceOBSettingUpdateDto {

    @NotNull
    private Integer serviceId;

    @NotNull
    private Integer locationId;

    private Byte showBasePrice;
    private Byte bookOnlineAvailable;
    private Byte isAllStaff;
    private Boolean allowBookingWithOtherCareType;
    private List<Integer> staffIdList;
}
