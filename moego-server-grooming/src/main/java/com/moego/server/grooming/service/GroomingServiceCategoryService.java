package com.moego.server.grooming.service;

import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.exception.BizException;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.service.dto.ServiceCategoryListDto;
import com.moego.server.grooming.service.dto.ServiceCategorySaveDto;
import com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroomingServiceCategoryService {

    @Autowired
    private MoeGroomingServiceCategoryMapper groomingServiceCategoryMapper;

    @Autowired
    private MoeGroomingServiceMapper groomingServiceMapper;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    public List<ServiceCategoryListDto> getEditServiceCategory(
            Long companyId, Integer businessId, Byte type, ServiceItemType serviceItemType) {
        List<MoeGroomingServiceCategory> serviceCategoryList =
                companyGroomingServiceQueryService.groomingServiceCategorySelectByBusinessId(
                        companyId, businessId, type, serviceItemType);
        List<ServiceCategoryListDto> categoryList = new ArrayList<>();
        for (MoeGroomingServiceCategory serviceCategory : serviceCategoryList) {
            ServiceCategoryListDto categoryDto = new ServiceCategoryListDto();
            BeanUtils.copyProperties(serviceCategory, categoryDto);
            categoryDto.setCategoryId(serviceCategory.getId());
            categoryList.add(categoryDto);
        }
        return categoryList;
    }

    /**
     * setting service category保存
     *
     * @param categorySaveDto 更新用dto
     * @param businessId      商户id
     * @return 返回结果
     */
    public Integer createServiceCategory(Long companyId, Integer businessId, ServiceCategorySaveDto categorySaveDto) {
        // checking name length
        checkServiceCategory(categorySaveDto.getName());
        // 检测名字是否重复
        if (checkCategoryNameIsExist(
                companyId,
                businessId,
                categorySaveDto.getName(),
                categorySaveDto.getType(),
                PetDetailDTOUtil.mapServiceItemType(categorySaveDto.getServiceItemType()))) {
            throw new BizException(Code.CODE_SERVICE_CATEGORY_NAME_IS_EXIST_VALUE);
        }
        MoeGroomingServiceCategory serviceCategory = new MoeGroomingServiceCategory();
        serviceCategory.setBusinessId(businessId);
        serviceCategory.setCompanyId(companyId);
        serviceCategory.setName(categorySaveDto.getName());
        serviceCategory.setType(categorySaveDto.getType());
        serviceCategory.setCreateTime(CommonUtil.get10Timestamp());
        serviceCategory.setUpdateTime(CommonUtil.get10Timestamp());
        serviceCategory.setServiceItemType(categorySaveDto.getServiceItemType());
        boolean result = groomingServiceCategoryMapper.insertSelective(serviceCategory) > 0;
        Integer serviceCategoryId = serviceCategory.getId();
        if (result) {
            MoeGroomingServiceCategory serviceCategorySort = new MoeGroomingServiceCategory();
            serviceCategorySort.setId(serviceCategoryId);
            serviceCategorySort.setSort(serviceCategoryId);
            groomingServiceCategoryMapper.updateByPrimaryKeySelective(serviceCategorySort);
        }
        return serviceCategoryId;
    }

    public Boolean checkCategoryNameIsExist(
            Long companyId, Integer businessId, String name, Byte type, ServiceItemType serviceItemType) {
        return checkCategoryNameIsExist(companyId, businessId, name, type, null, serviceItemType);
    }

    public Boolean checkCategoryNameIsExist(
            Long companyId,
            Integer businessId,
            String name,
            Byte type,
            Integer updateCategoryId,
            ServiceItemType serviceItemType) {
        return companyGroomingServiceQueryService.groomingServiceCategoryCheckNameIsExist(
                companyId, businessId, name, type, updateCategoryId, serviceItemType);
    }

    private void checkServiceCategory(String svcName) {
        if (svcName.length() > ServiceEnum.MAX_LENGTH_OF_NAME) {
            log.error("name [{}] length is too long ", svcName);
            throw new CommonException(ResponseCodeEnum.SERVICE_CATEGORY_NAME_IS_TOO_LONG);
        }
    }

    /**
     * setting service category更新
     *
     * @param categoryUpdateDto 更新用dto
     * @param businessId        商户id
     * @return 返回结果
     */
    public Boolean updateServiceCategory(
            Long companyId, Integer businessId, ServiceCategoryUpdateDto categoryUpdateDto) {
        // checking name length
        checkServiceCategory(categoryUpdateDto.getName());
        // 检测名字是否重复
        if (checkCategoryNameIsExist(
                companyId,
                businessId,
                categoryUpdateDto.getName(),
                categoryUpdateDto.getType(),
                categoryUpdateDto.getCategoryId(),
                PetDetailDTOUtil.mapServiceItemType(categoryUpdateDto.getServiceItemType()))) {
            throw new CommonException(ResponseCodeEnum.SERVICE_CATEGORY_NAME_IS_EXIST);
        }
        MoeGroomingServiceCategory serviceCategory = new MoeGroomingServiceCategory();
        serviceCategory.setId(categoryUpdateDto.getCategoryId());
        serviceCategory.setCompanyId(companyId);
        serviceCategory.setName(categoryUpdateDto.getName());
        serviceCategory.setUpdateTime(CommonUtil.get10Timestamp());
        return groomingServiceCategoryMapper.updateByPrimaryKeySelectiveWithCid(serviceCategory) >= 0;
    }

    /**
     * setting service category更新
     *
     * @param categoryId categoryId
     * @return 返回结果
     */
    public Boolean deleteServiceCategory(Integer categoryId, Long companyId) {
        MoeGroomingServiceCategory serviceCategory = new MoeGroomingServiceCategory();
        serviceCategory.setId(categoryId);
        serviceCategory.setCompanyId(companyId);
        serviceCategory.setStatus(DeleteStatusEnum.STATUS_DELETE);
        serviceCategory.setUpdateTime(DateUtil.get10Timestamp());
        Boolean result = groomingServiceCategoryMapper.updateByPrimaryKeySelectiveWithCid(serviceCategory) >= 0;
        if (result) {
            // 删除category后，要将当前的绑定了当前category，移到默认组下(既category_id=0)
            groomingServiceMapper.setServiceCategoryDefaultForCid(categoryId, companyId);
        }
        return result;
    }

    /**
     * @param categoryIdList idList
     * @return 返回结果
     */
    public Boolean sortServiceCategory(Long companyId, List<Integer> categoryIdList) {
        List<MoeGroomingServiceCategory> categories =
                groomingServiceCategoryMapper.selectByIdSet(new HashSet<>(categoryIdList));
        Set<Integer> ids = new HashSet<>();
        categories.forEach(category -> {
            if (companyId.equals(category.getCompanyId())) {
                ids.add(category.getId());
            }
        });

        List<MoeGroomingServiceCategory> serviceCategoryList = new ArrayList<>();
        int sortIndex = 0;
        for (int i = categoryIdList.size(); 0 < i; --i) {
            Integer id = categoryIdList.get(i - 1);
            if (ids.contains(id)) {
                MoeGroomingServiceCategory serviceCategory = new MoeGroomingServiceCategory();
                serviceCategory.setId(id);
                serviceCategory.setSort(sortIndex);
                serviceCategoryList.add(serviceCategory);
                ++sortIndex;
            }
        }

        if (!serviceCategoryList.isEmpty()) {
            groomingServiceCategoryMapper.batchUpdateSort(serviceCategoryList);
        }
        return true;
    }
}
