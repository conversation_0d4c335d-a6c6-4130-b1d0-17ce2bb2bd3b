package com.moego.server.grooming.web.params;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/guides/integration/conversion-tracking#sending">Conversion Tracking</a>
 */
@Data
@Accessors(chain = true)
public class GoogleReserveConversionTrackingParam {

    @JsonProperty("conversion_partner_id")
    private String conversionPartnerId;

    @JsonProperty("rwg_token")
    private String rwgToken;
}
