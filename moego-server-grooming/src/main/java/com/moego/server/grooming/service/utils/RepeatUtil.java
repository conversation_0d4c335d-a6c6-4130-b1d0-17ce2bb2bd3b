package com.moego.server.grooming.service.utils;

import com.moego.common.enums.RepeatConst;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.mapstruct.RepeatMapper;
import com.moego.server.grooming.params.EditRepeatParams;
import com.moego.server.grooming.params.RepeatParams;
import com.moego.server.grooming.params.SaveRepeatParams;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Repeat 计算日期工具类，迁移自 RepeatService
 */
public class RepeatUtil {

    public static List<LocalDate> getDates(RepeatParams repeatParams) {
        return switch (repeatParams.getRepeatType()) {
            case RepeatConst.REPEAT_TYPE_DAY -> repeatByDay(repeatParams);
            case RepeatConst.REPEAT_TYPE_WEEK -> repeatByWeek(repeatParams);
            case RepeatConst.REPEAT_TYPE_MONTH -> repeatByMonth(repeatParams);
            default -> List.of();
        };
    }

    public static List<LocalDate> checkAndComputeDate(RepeatParams repeatParams) {
        // 计算日期前校验参数
        checkRepeatParams(repeatParams);
        List<LocalDate> dates = getDates(repeatParams);
        if (dates.size() > RepeatConst.MAX_REPEAT_TIMES) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "New repeat appointments cannot be more than 100.");
        }
        return dates;
    }

    public static void checkRepeatParams(RepeatParams repeatParams) {
        // 开始时间不得为空
        if (!StringUtils.hasText(repeatParams.getStartsOn())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat starts on could not be empty");
        }

        if (repeatParams.getRepeatType() == RepeatConst.REPEAT_TYPE_DAY) {
            // 类型为day时 every不得为空
            if (repeatParams.getRepeatEvery() == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat every could not be empty");
            }
        } else if (repeatParams.getRepeatType() == RepeatConst.REPEAT_TYPE_WEEK) {
            // 类型为week时 every和repeatBy|repeatByDays不得为空
            if (repeatParams.getRepeatEvery() == null
                    || (repeatParams.getRepeatBy() == null
                            && CollectionUtils.isEmpty(repeatParams.getRepeatByDays()))) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Repeat every or repeat on could not be empty");
            }
        } else if (repeatParams.getRepeatType() == RepeatConst.REPEAT_TYPE_MONTH) {
            // month repeatEveryType不得为空
            if (repeatParams.getRepeatEveryType() == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat every type could not be empty");
            }
            if (repeatParams.getRepeatEveryType() == RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY) {
                // 为1时，monthWeekTimes，monthWeekDay不得为空
                if (repeatParams.getMonthWeekTimes() == null || repeatParams.getMonthWeekDay() == null) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "Repeat month week times or month week day could not be empty");
                }
            } else if (repeatParams.getRepeatEveryType() == RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY) {
                // 为2时，monthDay不得为空
                if (repeatParams.getMonthDay() == null) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat month day could not be empty");
                }
            }
            // 兼容逻辑：旧版 by month 没有用到 repeatEvery 这个字段，如果前端没传，则默认设置为1
            if (repeatParams.getRepeatEvery() == null) {
                repeatParams.setRepeatEvery(1);
            }
        }

        if (Objects.equals(repeatParams.getType(), RepeatConst.REPEAT_END_TYPE_TIMES)) {
            if (repeatParams.getTimes() == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat times could not be empty");
            }
            if (repeatParams.getTimes() > RepeatConst.MAX_REPEAT_TIMES) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "New repeat appointments cannot be more than 100.");
            }
        } else if (Objects.equals(repeatParams.getType(), RepeatConst.REPEAT_END_TYPE_END_DATE)) {
            if (!StringUtils.hasText(repeatParams.getSetEndOn())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat end on could not be empty");
            }
        }
    }

    public static boolean checkParams(EditRepeatParams editRepeatParams) {
        if (editRepeatParams.getRepeatId() == null) {
            return false;
        }
        return switch (editRepeatParams.getType()) {
            case RepeatConst.REPEAT_END_TYPE_TIMES -> editRepeatParams.getTimes() != null;
            case RepeatConst.REPEAT_END_TYPE_END_DATE -> StringUtils.hasText(editRepeatParams.getEndsOn());
            default -> false;
        };
    }

    private static List<LocalDate> repeatByDay(RepeatParams repeatParams) {
        String startsOn = repeatParams.getStartsOn();
        String endsOn = repeatParams.getSetEndOn();
        Integer times = repeatParams.getTimes();
        String type = repeatParams.getType(); // endType
        Integer repeatEvery = repeatParams.getRepeatEvery(); // frequency

        List<LocalDate> repeatDates = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(startsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(type)) { // 按次数重复
            for (int i = 0; i < times; i++) {
                repeatDates.add(startDate);
                startDate = startDate.plusDays(repeatEvery);
            }
        } else if (RepeatConst.REPEAT_END_TYPE_END_DATE.equals(type)) { // 截止日期前重复
            LocalDate endDate = LocalDate.parse(endsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            while (!startDate.isAfter(endDate)) {
                repeatDates.add(startDate);
                startDate = startDate.plusDays(repeatEvery);
            }
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat end type not support");
        }
        return repeatDates;
    }

    private static List<LocalDate> repeatByWeek(RepeatParams repeatParams) {
        String startsOn = repeatParams.getStartsOn();
        String endsOn = repeatParams.getSetEndOn();
        List<Integer> repeatByDays = new ArrayList<>();
        if (!CollectionUtils.isEmpty(repeatParams.getRepeatByDays())) {
            repeatByDays = repeatParams.getRepeatByDays();
        } else if (repeatParams.getRepeatBy() != null) {
            repeatByDays.add(repeatParams.getRepeatBy());
        }
        if (CollectionUtils.isEmpty(repeatByDays)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat by days could not be empty");
        }
        repeatByDays.sort(Integer::compareTo);
        String type = repeatParams.getType(); // endType
        Integer times = repeatParams.getTimes();
        Integer repeatEvery = repeatParams.getRepeatEvery(); // frequency
        List<LocalDate> repeatDates = new ArrayList<>();
        // 指定的 startDate
        LocalDate startDate = LocalDate.parse(startsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(type)) { // 按次数重复
            // 当前偏移的日期
            LocalDate tempDate = startDate;
            for (int i = 0; i < times; ) {
                // 每 repeat weeks 内偏移的日期
                LocalDate repeatTempDate = tempDate;
                for (int dayOfWeek : repeatByDays) {
                    repeatTempDate = offsetDate(repeatTempDate, dayOfWeek);
                    repeatDates.add(repeatTempDate);
                    if (++i >= times) {
                        break;
                    }
                }
                // 移动到下一个周期
                tempDate = tempDate.plusWeeks(repeatEvery);
            }
        } else if (RepeatConst.REPEAT_END_TYPE_END_DATE.equals(type)) { // 截止日期前重复
            LocalDate tempDate = startDate;
            LocalDate endDate = LocalDate.parse(endsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            while (!tempDate.isAfter(endDate) && repeatDates.size() < 100) {
                LocalDate repeatTempDate = tempDate;
                for (int dayOfWeek : repeatByDays) {
                    repeatTempDate = offsetDate(repeatTempDate, dayOfWeek);
                    if (!repeatTempDate.isAfter(endDate)) {
                        repeatDates.add(repeatTempDate);
                    }
                }
                tempDate = tempDate.plusWeeks(repeatEvery); // 移动到下一个周期
            }
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat type not support");
        }

        return repeatDates;
    }

    /**
     * 计算repeat日期，startDate 不是指定的weekday时，+7 跳到下一周
     */
    private static LocalDate offsetDate(LocalDate tempDate, int dayOfWeek) {
        int offset = dayOfWeek - tempDate.getDayOfWeek().getValue();
        if (offset < 0) {
            offset = offset + 7;
        }
        return tempDate.plusDays(offset);
    }

    public static List<LocalDate> repeatByMonth(RepeatParams repeatParams) {
        List<LocalDate> repeatDates = new ArrayList<>();
        Integer repeatEveryType = repeatParams.getRepeatEveryType();
        if (repeatEveryType == RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY) {
            // 每 N 个月的第几周的周几
            repeatDates = repeatByMonthWeekday(repeatParams);
        } else if (repeatEveryType == RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY) {
            // 每 N 个月的第几天
            repeatDates = repeatByMonthDay(repeatParams);
        }
        return repeatDates;
    }

    private static List<LocalDate> repeatByMonthDay(RepeatParams repeatParams) {
        String startsOn = repeatParams.getStartsOn();
        String setEndOn = repeatParams.getSetEndOn();
        int repeatEvery = repeatParams.getRepeatEvery() == null || repeatParams.getRepeatEvery() == 0
                ? 1
                : repeatParams.getRepeatEvery();
        Integer times = repeatParams.getTimes();
        Integer monthDay = repeatParams.getMonthDay();
        String type = repeatParams.getType();

        LocalDate startDate = LocalDate.parse(startsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<LocalDate> repeatDates = new ArrayList<>();
        LocalDate tempDate = startDate; // 记录上一个循环的日期
        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(type)) { // 按次数重复
            while (repeatDates.size() < times) {
                LocalDate date = tempDate.withDayOfMonth(
                        monthDay > tempDate.lengthOfMonth() ? tempDate.lengthOfMonth() : monthDay);
                if (!date.isBefore(startDate)) {
                    repeatDates.add(date);
                }
                tempDate = tempDate.plusMonths(repeatEvery);
            }
        } else if (RepeatConst.REPEAT_END_TYPE_END_DATE.equals(type)) { // 截止日期前重复
            LocalDate endDate = LocalDate.parse(setEndOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            while (!tempDate.isAfter(endDate) && repeatDates.size() < RepeatConst.MAX_REPEAT_TIMES) {
                LocalDate date = tempDate.withDayOfMonth(
                        monthDay > tempDate.lengthOfMonth() ? tempDate.lengthOfMonth() : monthDay);
                if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                    // 日期在开始日期和结束日期之间
                    repeatDates.add(date);
                }
                tempDate = tempDate.plusMonths(repeatEvery);
            }
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat type not support");
        }
        return repeatDates;
    }

    private static List<LocalDate> repeatByMonthWeekday(RepeatParams repeatParams) {
        String startsOn = repeatParams.getStartsOn();
        String setEndOn = repeatParams.getSetEndOn();
        int repeatEvery = repeatParams.getRepeatEvery() == null || repeatParams.getRepeatEvery() == 0
                ? 1
                : repeatParams.getRepeatEvery();
        Integer times = repeatParams.getTimes();
        Integer monthWeekDay = repeatParams.getMonthWeekDay();
        Integer monthWeekTimes = repeatParams.getMonthWeekTimes();
        String type = repeatParams.getType();

        List<LocalDate> repeatDates = new ArrayList<>();
        LocalDate start = LocalDate.parse(startsOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(type)) { // 按次数重复
            if (times == 0) {
                return repeatDates;
            }
            // 计算第一个 repeat date
            LocalDate first = findNthWeekDay(monthWeekDay, monthWeekTimes, start);
            // 第一个 repeat date 不符合条件，跳到下个循环周期
            if (first.isBefore(start)) {
                start = start.plusMonths(repeatEvery);
            }
            for (int i = 0; i < times; i++) {
                LocalDate weekDays = findNthWeekDay(monthWeekDay, monthWeekTimes, start);
                repeatDates.add(weekDays);
                start = start.plusMonths(repeatEvery);
            }
        } else if (RepeatConst.REPEAT_END_TYPE_END_DATE.equals(type)) { // 截止日期前重复
            LocalDate endsOn = LocalDate.parse(setEndOn, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // endDate 之前、重复次数不超过 100 次时计算
            while (!start.isAfter(endsOn) && repeatDates.size() < RepeatConst.MAX_REPEAT_TIMES) {
                LocalDate nthWeekDay = findNthWeekDay(monthWeekDay, monthWeekTimes, start);
                // 如果 nthWeekDay 在 start 和 endsOn 之间，加入 repeatDates
                if (!nthWeekDay.isBefore(start) && !nthWeekDay.isAfter(endsOn)) {
                    repeatDates.add(nthWeekDay);
                }

                // 如果年份，月份和 endDate 相等，说明是最后一个月，直接跳出循环
                if (nthWeekDay.getYear() == endsOn.getYear() && nthWeekDay.getMonthValue() == endsOn.getMonthValue()) {
                    break;
                } else {
                    start = start.with(TemporalAdjusters.firstDayOfMonth()).plusMonths(repeatEvery);
                }
            }
        } else {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Repeat type not support");
        }

        return repeatDates;
    }

    /**
     * 计算第 N 个周 M 的日期
     * 当不存在第 N 个时，返回最后一个，如 2月第5个周三，不存在第5个周三，则返回最后一个周三
     *
     * @param monthWeekDay   月份中的第几周
     * @param monthWeekTimes 月份中的第几天
     * @param start          开始时间
     * @return LocalDate
     */
    private static LocalDate findNthWeekDay(Integer monthWeekDay, Integer monthWeekTimes, LocalDate start) {
        // 如果是0，代表周日，DayOfWeek 的周日是7
        if (monthWeekDay == 0) {
            monthWeekDay = 7;
        }
        DayOfWeek dayOfWeek = DayOfWeek.of(monthWeekDay);

        // 找到指定月份的第一个周三
        LocalDate firstWeekday = start.with(TemporalAdjusters.firstInMonth(dayOfWeek));
        LocalDate lastWeekday = start.with(TemporalAdjusters.lastInMonth(dayOfWeek));

        // 计算第N个周三的日期
        LocalDate nthWeekDay = firstWeekday.plusWeeks(monthWeekTimes - 1);
        if (nthWeekDay.isAfter(lastWeekday)) {
            nthWeekDay = lastWeekday;
        }

        return nthWeekDay;
    }

    public static MoeGroomingRepeat convertSaveRepeatParamsToBean(SaveRepeatParams params) {
        // 根据 repeat rule 计算出所有的日期
        List<LocalDate> dates = RepeatUtil.checkAndComputeDate(params);
        if (CollectionUtils.isEmpty(dates)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No matching date");
        }

        MoeGroomingRepeat record = RepeatMapper.INSTANCE.repeatParamsToBean(params);
        if (!CollectionUtils.isEmpty(params.getRepeatByDays())) {
            record.setRepeatBy(null);
        }
        record.setBusinessId(params.getBusinessId());
        record.setCompanyId(params.getCompanyId().longValue());
        record.setStaffId(params.getStaffId());
        if (Boolean.TRUE.equals(params.getSmartSchedule())) {
            record.setSsFlag(RepeatConst.REPEAT_SS_FLAG_TRUE);
        }
        // 根据 type 区分补齐 times 和 endOn
        LocalDate finalRepeatDate = dates.get(dates.size() - 1);
        Date endOn = DateUtil.convertLocalDateToDate(finalRepeatDate);
        if (RepeatConst.REPEAT_END_TYPE_TIMES.equals(params.getType())) { // 按次数重复
            // 如果 repeat 的结束类型是 times，设置 setEndOn 字段为最后一个 repeatDate 的日期
            record.setSetEndOn(endOn);
        } else {
            // 如果 repeat 的结束类型是 setEndDate，设置 times 字段为计算出来的 repeat date 数量
            record.setTimes(dates.size());
            // 当 repeat date 数量 = 100 时，把最后一个 repeatDate 设置到 setEndOn 字段（历史逻辑）
            if (dates.size() == RepeatConst.MAX_REPEAT_TIMES) {
                record.setSetEndOn(endOn);
            }
        }
        return record;
    }

    /**
     * 判断 repeat 是否只修改了 repeatTimes 或者 end date，用于判断是否需要重新 smart schedule
     */
    public static boolean isExtendRepeat(RepeatParams params, MoeGroomingRepeat repeat) {
        MoeGroomingRepeat updateRepeat = RepeatMapper.INSTANCE.repeatParamsToBean(params);
        if (updateRepeat.getRepeatType() != null
                && !Objects.equals(updateRepeat.getRepeatType(), repeat.getRepeatType())) {
            return false;
        }
        if (updateRepeat.getRepeatEvery() != null
                && !Objects.equals(updateRepeat.getRepeatEvery(), repeat.getRepeatEvery())) {
            return false;
        }
        if (updateRepeat.getRepeatBy() != null && !Objects.equals(updateRepeat.getRepeatBy(), repeat.getRepeatBy())) {
            return false;
        }
        if (updateRepeat.getRepeatByDays() != null
                && !Objects.equals(updateRepeat.getRepeatByDays(), repeat.getRepeatByDays())) {
            return false;
        }
        if (updateRepeat.getRepeatEveryType() != null
                && !Objects.equals(updateRepeat.getRepeatEveryType(), repeat.getRepeatEveryType())) {
            return false;
        }
        if (updateRepeat.getMonthDay() != null && !Objects.equals(updateRepeat.getMonthDay(), repeat.getMonthDay())) {
            return false;
        }
        if (updateRepeat.getMonthWeekTimes() != null
                && !Objects.equals(updateRepeat.getMonthWeekTimes(), repeat.getMonthWeekTimes())) {
            return false;
        }
        if (updateRepeat.getMonthWeekDay() != null
                && !Objects.equals(updateRepeat.getMonthWeekDay(), repeat.getMonthWeekDay())) {
            return false;
        }
        if (updateRepeat.getStartsOn() != null && !Objects.equals(updateRepeat.getStartsOn(), repeat.getStartsOn())) {
            return false;
        }
        if (updateRepeat.getType() != null && !Objects.equals(updateRepeat.getType(), repeat.getType())) {
            return false;
        }
        // 修改 times 或者 endOn 都是返回 true
        return true;
    }
}
