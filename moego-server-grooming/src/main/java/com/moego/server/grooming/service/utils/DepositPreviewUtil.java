package com.moego.server.grooming.service.utils;

import com.moego.idl.service.order.v2.PreviewDepositOrderRequest;
import com.moego.server.grooming.params.BookOnlinePetParams;
import java.util.List;

public class DepositPreviewUtil {
    // TODO(Jason): Help to implement this
    public static List<PreviewDepositOrderRequest.ServicePricingDetail> convertOBPetDataToPricingDetails(
            List<BookOnlinePetParams> petData) {
        throw new RuntimeException("Not yet implemented");
    }
}
