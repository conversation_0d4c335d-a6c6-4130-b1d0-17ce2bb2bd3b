package com.moego.server.grooming.service.ob;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ClientSourceEnum;
import com.moego.common.enums.QuestionConst;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.api.ICustomerContactService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.client.ICustomerComposeClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerNoteClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.AdditionalContactDTO;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.customer.dto.SaveCustomerPetResultDto;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.customer.params.CustomerNoteSaveVo;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionSaveMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import com.moego.server.grooming.mapstruct.CustomerMapper;
import com.moego.server.grooming.mapstruct.PetMapper;
import com.moego.server.grooming.mapstruct.ProfileRequestMapper;
import com.moego.server.grooming.mapstruct.QuestionMapper;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import com.moego.server.grooming.params.ob.CustomerPetsParams;
import com.moego.server.grooming.params.ob.CustomerPetsUpdateParams;
import com.moego.server.grooming.service.utils.ProfileConflictUtils;
import com.moego.server.grooming.util.CustomQuestionUtil;
import com.moego.server.grooming.web.vo.ob.OBClientDetailVO;
import com.moego.server.grooming.web.vo.ob.OBClientPetsDetailVO;
import com.moego.server.grooming.web.vo.ob.OBPetDetailVO;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/6
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBCustomerService {

    private final MoeBookOnlineQuestionSaveMapper moeBookOnlineQuestionSaveMapper;
    private final OBQuestionService questionService;
    private final ICustomerComposeClient iCustomerComposeClient;
    private final ICustomerNoteClient iCustomerNoteClient;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final OBAddressService obAddressService;
    private final IPetClient iPetClient;
    private final ICustomerProfileRequestService customerProfileRequestService;
    private final ICustomerContactService customerContactService;
    private final IProfileRequestAddressService profileRequestAddressApi;

    /**
     * 新增用户
     * 1. 插入用户记录
     * 2. 插入用户信息（customer question）
     *
     * @param obParams
     * @return
     */
    public Integer newCustomer(BookOnlineSubmitParams obParams) {
        SaveWithPetCustomerVo saveWithPetCustomerVo = new SaveWithPetCustomerVo();
        BeanUtils.copyProperties(obParams.getCustomerData(), saveWithPetCustomerVo);
        // CRM-3555 emergency contact
        if (Objects.nonNull(obParams.getCustomerData())) {
            var customerData = obParams.getCustomerData();

            var em = new AdditionalContactDTO();
            em.setFirstName(customerData.getEmergencyContactFirstName());
            em.setLastName(customerData.getEmergencyContactLastName());
            em.setPhone(customerData.getEmergencyContactPhone());
            saveWithPetCustomerVo.setEmergencyContact(em);

            var pc = new AdditionalContactDTO();
            pc.setFirstName(customerData.getPickupContactFirstName());
            pc.setLastName(customerData.getPickupContactLastName());
            pc.setPhone(customerData.getPickupContactPhone());
            saveWithPetCustomerVo.setPickupContact(pc);
        }

        // [CRM] add leads identification
        saveWithPetCustomerVo.setSource(ClientSourceEnum.SOURCE_ONLINE_BOOKING.getSource());

        // 处理 referralSourceId 和 referralSourceDesc
        var customerAdditionalParams = obParams.getBookOnlineCustomerAdditionalParams();
        if (customerAdditionalParams != null) {
            saveWithPetCustomerVo.setReferralSourceId(customerAdditionalParams.getReferralSourceId());
            saveWithPetCustomerVo.setReferralSourceDesc(customerAdditionalParams.getReferralSourceDesc());
            saveWithPetCustomerVo.setPreferredGroomerId(customerAdditionalParams.getPreferredGroomerId());
            saveWithPetCustomerVo.setPreferredFrequencyDay(customerAdditionalParams.getPreferredFrequencyDay());
            saveWithPetCustomerVo.setPreferredFrequencyType(customerAdditionalParams.getPreferredFrequencyType());
            saveWithPetCustomerVo.setPreferredDay(customerAdditionalParams.getPreferredDay());
            saveWithPetCustomerVo.setPreferredTime(customerAdditionalParams.getPreferredTime());
        }

        // 1. call customer api to save customer
        ResponseResult<SaveCustomerPetResultDto> createCustomerResp =
                iCustomerComposeClient.createCustomerWithPermissionCheck(
                        obParams.getCompanyId(), obParams.getBusinessId(), null, saveWithPetCustomerVo);

        // 2. insert custom question
        Integer customerId = createCustomerResp.getData().getId();
        Map<String, Object> answersMap = obParams.getCustomerData().getAnswersMap();
        MoeBookOnlineQuestionSave customQuestion = new MoeBookOnlineQuestionSave();
        customQuestion.setBusinessId(obParams.getBusinessId());
        customQuestion.setCompanyId(obParams.getCompanyId());
        customQuestion.setCustomerId(customerId);
        customQuestion.setType(QuestionConst.TYPE_PET_OWNER_QUESTION);
        customQuestion.setCreateTime(DateUtil.get10Timestamp());
        customQuestion.setUpdateTime(customQuestion.getCreateTime());
        List<GroomingQuestionDTO> petOwnerQuestions = questionService.getQuestionsByBusinessId(
                obParams.getBusinessId(), QuestionConst.TYPE_PET_OWNER_QUESTION.intValue());
        customQuestion.setFormJson(JsonUtil.toJson(petOwnerQuestions));
        customQuestion.setQuestionJson(JsonUtil.toJson(answersMap));
        if (!CollectionUtils.isEmpty(answersMap)) {
            CustomerNoteSaveVo saveVo = new CustomerNoteSaveVo();
            saveVo.setCustomerId(customerId);
            saveVo.setNote(CustomQuestionUtil.generateNote(petOwnerQuestions, answersMap));
            iCustomerNoteClient.createCustomerNote(obParams.getBusinessId(), 0, saveVo);
        }
        moeBookOnlineQuestionSaveMapper.insertSelective(customQuestion);

        return customerId;
    }

    /**
     * Save or update profile request
     *
     * @param obParams           book online submit params
     * @param autoAcceptConflict auto accept conflict field
     */
    public void updateCustomerInfo(BookOnlineSubmitParams obParams, boolean autoAcceptConflict) {
        var customerId = AuthContext.get().getCustomerId();
        if (Objects.isNull(customerId)) {
            return;
        }
        List<BookOnlinePetParams> existingPets = obParams.getPetData().stream()
                .filter(pet -> Objects.nonNull(pet.getPetId()))
                .toList();
        // remote call to merge profile request
        CustomerProfileRequestDTO.ClientProfileDTO client =
                ProfileRequestMapper.INSTANCE.params2ClientProfileDTO(obParams);
        var customerData = obParams.getCustomerData();

        var em = new AdditionalContactDTO();
        em.setFirstName(customerData.getEmergencyContactFirstName());
        em.setLastName(customerData.getEmergencyContactLastName());
        em.setPhone(customerData.getEmergencyContactPhone());
        client.setEmergencyContact(em);

        var pc = new AdditionalContactDTO();
        pc.setFirstName(customerData.getPickupContactFirstName());
        pc.setLastName(customerData.getPickupContactLastName());
        pc.setPhone(customerData.getPickupContactPhone());
        client.setPickupContact(pc);

        customerProfileRequestService.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(obParams.getBusinessId())
                        .companyId(obParams.getCompanyId())
                        .customerId(AuthContext.get().getCustomerId())
                        .client(client)
                        .pets(ProfileRequestMapper.INSTANCE.params2PetProfileDTO(existingPets))
                        .build(),
                autoAcceptConflict);
        // insert customer and pet note
        questionService.upsertCustomerLatestQuestionSave(
                new BookOnlineQuestionSaveDTO()
                        .setBusinessId(obParams.getBusinessId())
                        .setCustomerId(AuthContext.get().getCustomerId())
                        .setClientCustomQuestionMap(obParams.getCustomerData().getAnswersMap())
                        .setPetCustomQuestionMap(existingPets.stream()
                                .collect(Collectors.toMap(BookOnlinePetParams::getPetId, pet -> Optional.ofNullable(
                                                pet.getPetQuestionAnswers())
                                        .orElse(Map.of())))),
                autoAcceptConflict);
    }

    /**
     * Get business client pets detail info
     *
     * @param params customer id and pet ids
     * @return client pets detail info
     */
    public OBClientPetsDetailVO getClientPetsDetail(CustomerPetsParams params) {
        Integer businessId = AuthContext.get().getBusinessId();
        Integer customerId = params.customerId();

        CustomerInfoDto customer = getCustomer(businessId, customerId);
        if (Objects.isNull(customer) || Objects.equals(customer.getStatus(), CommonConstant.DELETED)) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        CustomerContactDto ownerPhone = customerContactService.getCustomerOwnerPhone(businessId, customerId);
        CustomerIdWithPetIdsParams customerIdWithPetIdsParams = new CustomerIdWithPetIdsParams()
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setPetIds(params.petIds());
        List<CustomerPetDetailDTO> petDetailDTOList = iPetClient.getPetListWithVaccine(customerIdWithPetIdsParams);

        CustomerAddressDto primaryAddressInBusiness = obAddressService.getPrimaryAddress(customerId);

        // profile request
        CustomerProfileRequestDTO profileRequestDTO =
                customerProfileRequestService.getCustomerProfileRequest(businessId, customerId);
        BookOnlineQuestionSaveDTO latestQuestionSave =
                questionService.getCustomerLatestQuestionSave(businessId, customerId);
        Map<String, MoeBookOnlineQuestion> questionMap = questionService.getCustomQuestionMap(businessId);

        List<ProfileRequestAddressDTO> profileRequestAddresses =
                profileRequestAddressApi.listByCustomerIds(Set.of(customerId)).getOrDefault(customerId, List.of());

        return OBClientPetsDetailVO.builder()
                .customer(getCustomer(customer, ownerPhone, latestQuestionSave, questionMap, primaryAddressInBusiness))
                .pets(getPets(petDetailDTOList, latestQuestionSave, questionMap))
                .requestCustomer(getRequestCustomer(
                        profileRequestDTO, questionMap, primaryAddressInBusiness, profileRequestAddresses))
                .requestPets(getRequestPets(profileRequestDTO, questionMap))
                .build();
    }

    private CustomerInfoDto getCustomer(Integer businessId, Integer customerId) {
        CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
        customerInfoIdParams.setBusinessId(businessId);
        customerInfoIdParams.setCustomerId(customerId);
        return iCustomerCustomerClient.getCustomerWithDeletedHasCompanyId(customerInfoIdParams);
    }

    public Map<Integer, CustomerProfileRequestDTO> listCustomerProfile(Integer businessId, List<Integer> customerIds) {
        CompletableFuture<List<MoeBusinessCustomerDTO>> customersFuture = CompletableFuture.supplyAsync(
                () -> {
                    CustomerIdListParams params = new CustomerIdListParams();
                    params.setIdList(customerIds);
                    return iCustomerCustomerClient.queryCustomerList(params);
                },
                ThreadPool.getSubmitExecutor());
        CompletableFuture<List<CustomerContactDto>> contactsFuture = CompletableFuture.supplyAsync(
                () -> customerContactService.listCustomerOwnerPhone(businessId, customerIds),
                ThreadPool.getSubmitExecutor());
        CompletableFuture<List<CustomerPetDetailDTO>> petsFuture = CompletableFuture.supplyAsync(
                () -> iPetClient.getAllPetListWithVaccine(businessId, customerIds), ThreadPool.getSubmitExecutor());
        CompletableFuture<List<BookOnlineQuestionSaveDTO>> questionsFuture = CompletableFuture.supplyAsync(
                () -> questionService.listCustomerLatestQuestionSave(businessId, customerIds),
                ThreadPool.getSubmitExecutor());
        CompletableFuture.allOf(customersFuture, contactsFuture, petsFuture, questionsFuture)
                .join();
        List<MoeBusinessCustomerDTO> customerDTOList = customersFuture.join();
        List<CustomerContactDto> contactDTOList = contactsFuture.join();
        List<CustomerPetDetailDTO> petDetailDTOList = petsFuture.join();
        List<BookOnlineQuestionSaveDTO> questionSaveDTOList = questionsFuture.join();
        Map<Integer, MoeBusinessCustomerDTO> customerMap = customerDTOList.stream()
                .collect(Collectors.toMap(MoeBusinessCustomerDTO::getCustomerId, Function.identity()));
        Map<Integer, CustomerContactDto> ownerPhoneMap = contactDTOList.stream()
                .collect(Collectors.toMap(CustomerContactDto::getCustomerId, Function.identity()));
        Map<Integer, List<CustomerPetDetailDTO>> customerPetsMap =
                petDetailDTOList.stream().collect(Collectors.groupingBy(CustomerPetDetailDTO::getCustomerId));
        Map<Integer, BookOnlineQuestionSaveDTO> customerQuestionMap = questionSaveDTOList.stream()
                .collect(Collectors.toMap(BookOnlineQuestionSaveDTO::getCustomerId, Function.identity()));
        return customerIds.stream().collect(Collectors.toMap(customerId -> customerId, customerId -> {
            CustomerProfileRequestDTO.CustomerProfileRequestDTOBuilder builder = CustomerProfileRequestDTO.builder()
                    .businessId(businessId)
                    .customerId(customerId)
                    .pets(List.of());
            MoeBusinessCustomerDTO customerDTO = customerMap.get(customerId);
            if (Objects.isNull(customerDTO)) {
                return builder.build();
            }
            BookOnlineQuestionSaveDTO questionSaveDTO = customerQuestionMap.get(customerId);
            CustomerProfileRequestDTO.ClientProfileDTO client =
                    CustomerMapper.INSTANCE.dto2ClientProfileDTO(customerDTO);
            CustomerContactDto ownerPhone = ownerPhoneMap.get(customerId);
            client.setPhoneNumber(Objects.nonNull(ownerPhone) ? ownerPhone.getPhoneNumber() : null);
            builder.client(client);
            if (Objects.nonNull(questionSaveDTO)) {
                client.setCustomQuestions(questionSaveDTO.getClientCustomQuestionMap());
            }
            List<CustomerPetDetailDTO> petList = customerPetsMap.get(customerId);
            if (CollectionUtils.isEmpty(petList)) {
                return builder.build();
            }
            builder.pets(petList.stream()
                            .map(petDTO -> {
                                if (Objects.isNull(questionSaveDTO)) {
                                    return PetMapper.INSTANCE.dto2PetProfileDTO(petDTO);
                                }
                                return PetMapper.INSTANCE
                                        .dto2PetProfileDTO(petDTO)
                                        .setCustomQuestions(questionSaveDTO
                                                .getPetCustomQuestionMap()
                                                .get(petDTO.getPetId()));
                            })
                            .toList())
                    .build();
            return builder.build();
        }));
    }

    public CustomerHasRequestDTO getCustomerHasRequestUpdate(Integer businessId, Integer customerId) {
        if (Objects.isNull(businessId) || Objects.isNull(customerId)) {
            return CustomerHasRequestDTO.builder().hasRequestUpdate(false).build();
        }
        Map<Integer, CustomerHasRequestDTO> hasRequestDTOMap =
                listCustomerHasRequestUpdate(businessId, List.of(customerId));
        if (CollectionUtils.isEmpty(hasRequestDTOMap)) {
            return CustomerHasRequestDTO.builder().hasRequestUpdate(false).build();
        }
        return hasRequestDTOMap.get(customerId);
    }

    /**
     * List customer has profile request review updates
     *
     * @param businessId  business id
     * @param customerIds customer id list
     * @return customer id - profile request and review update map
     */
    public Map<Integer, CustomerHasRequestDTO> listCustomerHasRequestUpdate(
            Integer businessId, List<Integer> customerIds) {
        Map<Integer, CustomerProfileRequestDTO> profileRequestMap =
                customerProfileRequestService.getCustomerProfileRequest(businessId, customerIds).stream()
                        .collect(Collectors.toMap(
                                CustomerProfileRequestDTO::getCustomerId, Function.identity(), (v1, v2) -> v1));

        Map<Integer, List<ProfileRequestAddressDTO>> customerIdToProfileRequestAddresses =
                profileRequestAddressApi.listByCustomerIds(customerIds);

        Map<Integer, CustomerProfileRequestDTO> existingProfileMap = listCustomerProfile(businessId, customerIds);
        return customerIds.stream().collect(Collectors.toMap(customerId -> customerId, customerId -> {
            CustomerProfileRequestDTO request = profileRequestMap.get(customerId);
            CustomerProfileRequestDTO existing = existingProfileMap.get(customerId);
            CustomerHasRequestDTO.CustomerHasRequestDTOBuilder builder = CustomerHasRequestDTO.builder()
                    .profileRequest(request)
                    .existingProfile(existing)
                    .hasRequestUpdate(false);
            boolean hasUpdate = hasDiff(customerIdToProfileRequestAddresses.get(customerId), customerId);

            if (Objects.isNull(request) || Objects.isNull(existing)) {
                return builder.mergedProfile(existing)
                        .hasRequestUpdate(hasUpdate)
                        .build();
            }

            builder.mergedProfile(ProfileConflictUtils.replaceConflictProfile(request, existing));

            hasUpdate = hasUpdate || ProfileConflictUtils.conflict(request.getClient(), existing.getClient());

            if (!CollectionUtils.isEmpty(request.getPets())) {
                Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> requestPetMap = request.getPets().stream()
                        .collect(Collectors.toMap(
                                CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
                boolean petConflict = existing.getPets().stream()
                        .anyMatch(existingPet ->
                                ProfileConflictUtils.conflict(requestPetMap.get(existingPet.getPetId()), existingPet));
                hasUpdate = hasUpdate || petConflict;
            }

            builder.hasRequestUpdate(hasUpdate);

            return builder.build();
        }));
    }

    private boolean hasDiff(@Nullable List<ProfileRequestAddressDTO> profileRequestAddresses, Integer customerId) {
        if (ObjectUtils.isEmpty(profileRequestAddresses)) {
            return false;
        }

        boolean hasNewAddress = profileRequestAddresses.stream().anyMatch(e -> e.getCustomerAddressId() == null);
        if (hasNewAddress) {
            return true;
        }

        List<CustomerAddressDto> existingAddresses = obAddressService.listCustomerAddress(customerId);
        if (ObjectUtils.isEmpty(existingAddresses)) {
            return true;
        }

        Map<Integer, ProfileRequestAddressDTO> addressIdToProfileRequestAddress = profileRequestAddresses.stream()
                .filter(e -> e.getCustomerAddressId() != null)
                .collect(Collectors.toMap(
                        ProfileRequestAddressDTO::getCustomerAddressId, Function.identity(), (o, n) -> o));

        return existingAddresses.stream().anyMatch(existingAddress -> {
            ProfileRequestAddressDTO updatedBean =
                    addressIdToProfileRequestAddress.get(existingAddress.getCustomerAddressId());
            return hasAddressUpdate(updatedBean, existingAddress);
        });
    }

    private static boolean hasAddressUpdate(ProfileRequestAddressDTO updatedBean, CustomerAddressDto existingAddress) {
        if (updatedBean == null) {
            return false;
        }
        return (StringUtils.hasText(updatedBean.getAddress1())
                        && !Objects.equals(updatedBean.getAddress1(), existingAddress.getAddress1()))
                || (StringUtils.hasText(updatedBean.getAddress2())
                        && !Objects.equals(updatedBean.getAddress2(), existingAddress.getAddress2()))
                || (StringUtils.hasText(updatedBean.getCity())
                        && !Objects.equals(updatedBean.getCity(), existingAddress.getCity()))
                || (StringUtils.hasText(updatedBean.getState())
                        && !Objects.equals(updatedBean.getState(), existingAddress.getState()))
                || (StringUtils.hasText(updatedBean.getZipcode())
                        && !Objects.equals(updatedBean.getZipcode(), existingAddress.getZipcode()))
                || (StringUtils.hasText(updatedBean.getCountry())
                        && !Objects.equals(updatedBean.getCountry(), existingAddress.getCountry()))
                || (StringUtils.hasText(updatedBean.getLat())
                        && !Objects.equals(updatedBean.getLat(), existingAddress.getLat()))
                || (StringUtils.hasText(updatedBean.getLng())
                        && !Objects.equals(updatedBean.getLng(), existingAddress.getLng()))
                || (updatedBean.getIsPrimary() != null
                        && !Objects.equals(
                                Boolean.TRUE.equals(updatedBean.getIsPrimary())
                                        ? CustomerAddressDto.IsPrimary.TRUE.getValue()
                                        : CustomerAddressDto.IsPrimary.FALSE.getValue(),
                                existingAddress.getIsPrimary()));
    }

    /**
     * 1.Update customer info <br>
     * 2.Update pet info and vaccine <br>
     * 3.Insert customer note <br>
     * 4.Insert pet note <br>
     * 5.Upsert customer question save <br>
     * 5.Delete profile request
     *
     * @param updateParams customer pets update params
     */
    public void updateClientPetsDetail(CustomerPetsUpdateParams updateParams) {
        Integer businessId = AuthContext.get().getBusinessId();
        Integer staffId = AuthContext.get().getStaffId();
        Integer customerId = updateParams.customerId();
        Long companyId = AuthContext.get().companyId();
        if (Objects.nonNull(updateParams.customer())) {
            updateParams.customer().setCustomerId(customerId);
            updateParams.customer().setPhoneNumber(null);
            iCustomerCustomerClient.updateCustomer(companyId, businessId, staffId, updateParams.customer());
        }
        if (!CollectionUtils.isEmpty(updateParams.pets())) {
            iPetClient.batchUpdatePetWithVaccine(businessId, updateParams.pets());
        }
        if (!CollectionUtils.isEmpty(updateParams.clientCustomQuestionMap())
                || !CollectionUtils.isEmpty(updateParams.petCustomQuestionMap())) {
            questionService.upsertCustomerLatestQuestionSave(
                    new BookOnlineQuestionSaveDTO()
                            .setBusinessId(businessId)
                            .setCustomerId(customerId)
                            .setClientCustomQuestionMap(updateParams.clientCustomQuestionMap())
                            .setPetCustomQuestionMap(updateParams.petCustomQuestionMap()),
                    Boolean.TRUE);
        }
        CustomerAddressDto primaryAddress = updateParams.primaryAddress();
        if (!CollectionUtils.isEmpty(updateParams.newAddresses())) {
            // 数据量很小，不需要并行和批量插入，primary address 的设置可能会出现并发问题
            // 过滤掉 updateParams.primaryAddress()，因为 primary address 会在下面单独处理
            List<CustomerAddressDto> insertAddresses = new ArrayList<>(updateParams.newAddresses());
            insertAddresses.removeIf(addr -> primaryAddress != null
                    && Boolean.TRUE.equals(primaryAddress.getIsProfileRequestAddress())
                    && Objects.equals(addr.getCustomerAddressId(), primaryAddress.getCustomerAddressId()));
            insertAddresses.forEach(obAddressService::createCustomerAddress);
        }
        if (primaryAddress != null) {
            if (Boolean.TRUE.equals(primaryAddress.getIsProfileRequestAddress())) {
                // C 端 address 需要新增
                primaryAddress.setCustomerAddressId(null);
                obAddressService.createCustomerAddress(primaryAddress);
            } else {
                obAddressService.updateCustomerAddress(primaryAddress);
            }
        }
        customerProfileRequestService.deleteCustomerProfileRequest(businessId, customerId);
        profileRequestAddressApi.deleteByCustomerIds(Set.of(customerId));
    }

    private static List<OBPetDetailVO> getPets(
            List<CustomerPetDetailDTO> petDetailDTOList,
            BookOnlineQuestionSaveDTO latestQuestionSave,
            Map<String, MoeBookOnlineQuestion> questionMap) {
        return PetMapper.INSTANCE.petDetailDTO2OBPetDetailVO(petDetailDTOList).stream()
                .map(petDetailVO -> petDetailVO.toBuilder()
                        .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                                latestQuestionSave.getPetCustomQuestionMap().get(petDetailVO.petId()), questionMap))
                        .build())
                .toList();
    }

    private static OBClientDetailVO getCustomer(
            CustomerInfoDto customer,
            CustomerContactDto ownerPhone,
            BookOnlineQuestionSaveDTO latestQuestionSave,
            Map<String, MoeBookOnlineQuestion> questionMap,
            CustomerAddressDto addressInBusiness) {
        return CustomerMapper.INSTANCE.customerInfoDto2ClientDetailVO(customer).toBuilder()
                .phoneNumber(Objects.nonNull(ownerPhone) ? ownerPhone.getPhoneNumber() : null)
                .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                        latestQuestionSave.getClientCustomQuestionMap(), questionMap))
                .primaryAddress(addressInBusiness)
                .newAddresses(List.of())
                .build();
    }

    private static List<OBPetDetailVO> getRequestPets(
            CustomerProfileRequestDTO profileRequestDTO, Map<String, MoeBookOnlineQuestion> questionMap) {
        return Objects.isNull(profileRequestDTO) || CollectionUtils.isEmpty(profileRequestDTO.getPets())
                ? null
                : profileRequestDTO.getPets().stream()
                        .map(petProfileDTO ->
                                ProfileRequestMapper.INSTANCE.petProfileDTO2PetDetailVO(petProfileDTO).toBuilder()
                                        .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                                                petProfileDTO.getCustomQuestions(), questionMap))
                                        .build())
                        .toList();
    }

    private static OBClientDetailVO getRequestCustomer(
            CustomerProfileRequestDTO profileRequestDTO,
            Map<String, MoeBookOnlineQuestion> questionMap,
            CustomerAddressDto businessPrimaryAddress,
            List<ProfileRequestAddressDTO> profileRequestAddresses) {
        if (profileRequestDTO == null && ObjectUtils.isEmpty(profileRequestAddresses)) {
            return null;
        }
        OBClientDetailVO.OBClientDetailVOBuilder builder = ProfileRequestMapper.INSTANCE
                .clientProfileDTO2ClientDetailVO(Optional.ofNullable(profileRequestDTO)
                        .map(CustomerProfileRequestDTO::getClient)
                        .orElseGet(CustomerProfileRequestDTO.ClientProfileDTO::new))
                .toBuilder();
        CustomerAddressDto primaryAddress = profileRequestAddresses.stream()
                .filter(ProfileRequestAddressDTO::getIsPrimary)
                .findFirst()
                .map(OBCustomerService::toAddressDTO)
                .orElse(businessPrimaryAddress);

        // 如果一个 address 即是 new address 又是 primary address，需要从 new address 中移除
        List<CustomerAddressDto> newAddresses = profileRequestAddresses.stream()
                .filter(address -> address.getCustomerAddressId() == null)
                .map(OBCustomerService::toAddressDTO)
                .filter(e -> primaryAddress == null
                        || !primaryAddress.getIsProfileRequestAddress()
                        || !Objects.equals(e.getCustomerAddressId(), primaryAddress.getCustomerAddressId()))
                .toList();
        return builder.primaryAddress(primaryAddress)
                .newAddresses(newAddresses)
                .questionAnswerList(QuestionMapper.INSTANCE.entity2QuestionAnswerVO(
                        Optional.ofNullable(profileRequestDTO)
                                .map(CustomerProfileRequestDTO::getClient)
                                .map(CustomerProfileRequestDTO.ClientProfileDTO::getCustomQuestions)
                                .orElseGet(Map::of),
                        questionMap))
                .build();
    }

    private static CustomerAddressDto toAddressDTO(ProfileRequestAddressDTO profileRequestAddress) {
        if (profileRequestAddress == null) {
            return null;
        }
        CustomerAddressDto dto = new CustomerAddressDto();
        dto.setCustomerId(profileRequestAddress.getCustomerId());
        dto.setAddress1(profileRequestAddress.getAddress1());
        dto.setAddress2(profileRequestAddress.getAddress2());
        dto.setCity(profileRequestAddress.getCity());
        dto.setState(profileRequestAddress.getState());
        dto.setZipcode(profileRequestAddress.getZipcode());
        dto.setCountry(profileRequestAddress.getCountry());
        dto.setLat(profileRequestAddress.getLat());
        dto.setLng(profileRequestAddress.getLng());
        dto.setIsPrimary(
                Boolean.TRUE.equals(profileRequestAddress.getIsPrimary())
                        ? CustomerAddressDto.IsPrimary.TRUE.getValue()
                        : CustomerAddressDto.IsPrimary.FALSE.getValue());

        if (profileRequestAddress.getCustomerAddressId() == null) {
            dto.setCustomerAddressId(profileRequestAddress.getId());
            dto.setIsProfileRequestAddress(true);
        } else {
            dto.setCustomerAddressId(profileRequestAddress.getCustomerAddressId());
            dto.setIsProfileRequestAddress(false);
        }
        return dto;
    }
}
