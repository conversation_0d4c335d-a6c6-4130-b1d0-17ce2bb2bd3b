<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineMetricsMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="book_online_name" jdbcType="VARCHAR" property="bookOnlineName" />
    <result column="metric_name" jdbcType="VARCHAR" property="metricName" />
    <result column="metric_value" jdbcType="VARCHAR" property="metricValue" />
    <result column="date_range_alias" jdbcType="VARCHAR" property="dateRangeAlias" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, book_online_name, metric_name, metric_value, date_range_alias, start_date,
    end_date, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_book_online_metrics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_metrics
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_metrics (business_id, book_online_name, metric_name,
      metric_value, date_range_alias, start_date,
      end_date, create_time, update_time,
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{bookOnlineName,jdbcType=VARCHAR}, #{metricName,jdbcType=VARCHAR},
      #{metricValue,jdbcType=VARCHAR}, #{dateRangeAlias,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR},
      #{endDate,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_metrics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="bookOnlineName != null">
        book_online_name,
      </if>
      <if test="metricName != null">
        metric_name,
      </if>
      <if test="metricValue != null">
        metric_value,
      </if>
      <if test="dateRangeAlias != null">
        date_range_alias,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookOnlineName != null">
        #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="metricName != null">
        #{metricName,jdbcType=VARCHAR},
      </if>
      <if test="metricValue != null">
        #{metricValue,jdbcType=VARCHAR},
      </if>
      <if test="dateRangeAlias != null">
        #{dateRangeAlias,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_metrics
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="bookOnlineName != null">
        book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="metricName != null">
        metric_name = #{metricName,jdbcType=VARCHAR},
      </if>
      <if test="metricValue != null">
        metric_value = #{metricValue,jdbcType=VARCHAR},
      </if>
      <if test="dateRangeAlias != null">
        date_range_alias = #{dateRangeAlias,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_metrics
    set business_id = #{businessId,jdbcType=INTEGER},
      book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      metric_name = #{metricName,jdbcType=VARCHAR},
      metric_value = #{metricValue,jdbcType=VARCHAR},
      date_range_alias = #{dateRangeAlias,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchUpsert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineMetrics">
    INSERT INTO
        moe_book_online_metrics
        (business_id, book_online_name, metric_name, metric_value,
         date_range_alias, start_date, end_date, company_id)
    VALUES
    <foreach collection="list" index="index" item="item" separator=",">
        (#{item.businessId}, #{item.bookOnlineName}, #{item.metricName}, #{item.metricValue},
         #{item.dateRangeAlias}, #{item.startDate}, #{item.endDate}, #{item.companyId})
    </foreach>
    ON DUPLICATE KEY UPDATE
        metric_value = VALUES(metric_value),
        start_date = VALUES(start_date),
        end_date = VALUES(end_date)
  </insert>

  <select id="selectByCondition" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM moe_book_online_metrics
    WHERE business_id = #{businessId}
      AND metric_name = #{metricName}
      AND start_date = #{startDate}
      AND end_date = #{endDate}
    ORDER BY update_time DESC
    LIMIT 1
  </select>
</mapper>
