package com.moego.server.grooming.service.report.migrate;

import com.google.type.Date;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateRecordsRequest;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateRecordsResponse;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetRecordsByReportKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetRecordsByReportKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse;
import com.moego.backend.proto.fulfillment.v1.SendMethod;
import com.moego.backend.proto.fulfillment.v1.SendRecordUniqueKey;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.idl.models.appointment.v1.DailyReportSendLogMigrateDef;
import com.moego.idl.service.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByIdsRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportSendLogForMigrateRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.convert.ReportCardMigrateConverter;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSendLogsByPageParams;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import com.moego.server.message.client.IGroomingReportSendClient;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Report Card 发送记录迁移服务
 * 负责将 moe_grooming_report_send_log 和 daily_report_send_log 数据迁移到 fulfillment_report_send_record
 *
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 可追溯性：完整记录迁移过程
 * 6. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardSendRecordMigrateService {

    private final IGroomingReportSendClient iGroomingReportSendClient;
    private final MoeGroomingAppointmentService appointmentService;
    private final DailyReportServiceGrpc.DailyReportServiceBlockingStub dailyReportService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;
    private final ReportCardReportMigrateService reportMigrateService;

    /**
     * 迁移发送记录数据
     */
    @Transactional
    public ReportCardMigrateResultDTO migrate(Integer businessId, boolean isValidation) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "send_record_" + System.currentTimeMillis();

        log.info("开始迁移发送记录数据,taskId: {}, businessId: {}", taskId, businessId);

        try {
            long totalMigrated = 0;
            long totalSkipped = 0;
            long totalFailed = 0;
            long totalSource = 0;

            // 1. 迁移 grooming report send log 数据
            log.info("开始迁移 grooming report send log 数据");
            MigrateResult groomingResult = migrateGroomingSendLogs(businessId, isValidation);
            totalMigrated += groomingResult.migratedCount;
            totalSkipped += groomingResult.skippedCount;
            totalFailed += groomingResult.failedCount;
            totalSource += groomingResult.sourceCount;

            // 2. 迁移 daily report send log 数据
            log.info("开始迁移 daily report send log 数据");
            MigrateResult dailyResult = migrateDailySendLogs(businessId, isValidation);
            totalMigrated += dailyResult.migratedCount;
            totalSkipped += dailyResult.skippedCount;
            totalFailed += dailyResult.failedCount;
            totalSource += dailyResult.sourceCount;

            log.info(
                    "发送记录数据迁移完成，总源数据: {}, 成功: {}, 跳过: {}, 失败: {}",
                    totalSource,
                    totalMigrated,
                    totalSkipped,
                    totalFailed);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId,
                    startTime,
                    ReportCardMigrateConfig.TableNames.SEND_RECORD,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_SEND_LOG + "+"
                            + ReportCardMigrateConfig.SourceTables.DAILY_REPORT_SEND_LOG,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_SEND_RECORD,
                    totalSource,
                    totalMigrated,
                    totalSkipped);

        } catch (Exception e) {
            log.error("发送记录数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 迁移 grooming report send log 数据
     */
    private MigrateResult migrateGroomingSendLogs(Integer businessId, boolean isValidation) {
        try {
            // 1. 首先统计总记录数
            Integer totalCount = getTotalGroomingSendLogCount(businessId);
            log.info("grooming report send log 数据总数: {} 条，开始分页迁移", totalCount);

            if (totalCount == 0) {
                return new MigrateResult(0, 0, 0, 0);
            }

            // 2. 分页查询并处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                // 分页查询数据
                List<GroomingReportSendLogDTO> batch = getGroomingSendLogBatch(businessId, offset, batchSize);

                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }

                log.info(
                        "处理第 {} 批 grooming report send log 数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条",
                        batchNumber,
                        offset,
                        batchSize,
                        batch.size());

                // 处理当前批次的数据
                BatchResult batchResult = processBatchGroomingSendLogs(batch);
                migratedCount += batchResult.migratedCount;
                skippedCount += batchResult.skippedCount;
                failedCount += batchResult.failedCount;

                // 输出进度信息
                long processedCount = migratedCount + skippedCount + failedCount;
                int progressPercent = ReportCardMigrateUtils.calculateProgressPercent(processedCount, totalCount);
                log.info(
                        "grooming report send log 批次 {} 处理完成，当前进度: {} ({}/{}), 成功: {}, 跳过: {}, 失败: {}",
                        batchNumber,
                        progressPercent + "%",
                        processedCount,
                        totalCount,
                        batchResult.migratedCount,
                        batchResult.skippedCount,
                        batchResult.failedCount);

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 如果查询到的数据少于批次大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    break;
                }
            }

            return new MigrateResult(totalCount, migratedCount, skippedCount, failedCount);

        } catch (Exception e) {
            log.error("迁移 grooming send log 数据失败", e);
            throw e;
        }
    }

    /**
     * 迁移 daily report send log 数据
     */
    private MigrateResult migrateDailySendLogs(Integer businessId, ) {
        try {
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                int pageNum = (offset / batchSize) + 1;
                // 分页查询数据
                List<DailyReportSendLogMigrateDef> batch = dailyReportService
                        .listDailyReportSendLogForMigrate(ListDailyReportSendLogForMigrateRequest.newBuilder()
                                .setPagination(PaginationRequest.newBuilder()
                                        .setPageSize(batchSize)
                                        .setPageNum(pageNum)
                                        .build())
                                .build())
                        .getReportSendLogsList();

                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }
                log.info(
                        "处理第 {} 批 daily report send log 数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条",
                        batchNumber,
                        offset,
                        batchSize,
                        batch.size());

                // 处理当前批次的数据
                BatchResult batchResult = processBatchDailySendLogs(batch);
                migratedCount += batchResult.migratedCount;
                skippedCount += batchResult.skippedCount;
                failedCount += batchResult.failedCount;

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 如果查询到的数据少于批次大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    break;
                }
            }
            return new MigrateResult(migratedCount, migratedCount, skippedCount, failedCount);
        } catch (Exception e) {
            log.error("迁移 daily send log 数据失败", e);
            throw e;
        }
    }

    /**
     * 获取 grooming send log 总数量
     * 通过内部客户端接口获取数据
     */
    private Integer getTotalGroomingSendLogCount(Integer businessId) {
        try {
            return iGroomingReportSendClient.countGroomingReportSendLogs(businessId);
        } catch (Exception e) {
            log.error("获取 grooming send log 总数失败", e);
            return 0;
        }
    }

    /**
     * 分页查询 grooming send log 数据
     * 通过内部客户端接口获取数据，建议在相关字段上添加索引以提高查询性能
     */
    private List<GroomingReportSendLogDTO> getGroomingSendLogBatch(Integer businessId, int offset, int limit) {
        try {
            // 计算页码（从 1 开始）
            int pageNum = (offset / limit) + 1;
            var params = new GetGroomingReportSendLogsByPageParams();
            params.setBusinessId(businessId);
            params.setPagination(
                    Pagination.builder().pageNum(pageNum).pageSize(limit).build());
            return iGroomingReportSendClient.getGroomingReportSendLogsByPage(params);
        } catch (Exception e) {
            log.error("分页查询 grooming send log 数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个批次的 grooming send log 数据
     */
    private BatchResult processBatchGroomingSendLogs(List<GroomingReportSendLogDTO> batch) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        try {
            List<Integer> groomingIds =
                    batch.stream().map(GroomingReportSendLogDTO::getGroomingId).toList();
            List<MoeGroomingAppointment> appointments = appointmentService.getAppointmentByIdsV2(groomingIds);
            Map<Integer, String> appointmentDates = appointments.stream()
                    .collect(Collectors.toMap(
                            MoeGroomingAppointment::getId, MoeGroomingAppointment::getAppointmentDate, (v1, v2) -> v1));

            // 1. 获取所有相关的 report 信息，建立 report_id 映射关系
            Map<Integer, Integer> reportIdMapping = getReportIdMapping(batch, appointmentDates);

            if (reportIdMapping.isEmpty()) {
                log.warn("未找到任何对应的 grooming report，跳过整个批次");
                return new BatchResult(0, batch.size(), 0);
            }

            // 2. 过滤出有对应 report_id 的发送记录
            List<GroomingReportSendLogDTO> recordsWithReport = new ArrayList<>();
            for (GroomingReportSendLogDTO sendLog : batch) {
                Integer reportKey = sendLog.getReportId();
                if (reportIdMapping.containsKey(reportKey)) {
                    recordsWithReport.add(sendLog);
                } else {
                    log.info(
                            "发送记录没有对应的 report，跳过迁移: businessId={}, reportId={}, sendLogId={}",
                            sendLog.getBusinessId(),
                            sendLog.getReportId(),
                            sendLog.getId());
                    skippedCount++;
                }
            }

            if (recordsWithReport.isEmpty()) {
                return new BatchResult(0, skippedCount, 0);
            }

            // 3. 批量检查发送记录是否已存在
            GetRecordsByReportKeysResponse existingRecords =
                    fulfillmentServiceClient.getRecordsByReportKeys(GetRecordsByReportKeysRequest.newBuilder()
                            .addAllRecordKeys(recordsWithReport.stream()
                                    .map(sendLog -> SendRecordUniqueKey.newBuilder()
                                            .setReportUniqueKey(FulfillmentReportUniqueKey.newBuilder()
                                                    .setBusinessId(sendLog.getBusinessId()
                                                            .longValue())
                                                    .setAppointmentId(sendLog.getGroomingId()
                                                            .longValue())
                                                    .setPetId(sendLog.getPetId())
                                                    .setCareType(CareCategory.GROOMING)
                                                    .setServiceDate("")
                                                    .build())
                                            .build())
                                    .toList())
                            .build());

            // 4. 过滤出需要迁移的数据
            List<GroomingReportSendLogDTO> recordsToMigrate = new ArrayList<>();
            for (GroomingReportSendLogDTO sendLog : recordsWithReport) {
                // 获取映射后的新 reportId
                Integer newReportId = reportIdMapping.get(sendLog.getReportId());
                boolean exists = existingRecords.getRecordsList().stream()
                        .anyMatch(existing -> existing.getReportId() == newReportId
                                && existing.getSendMethod().equals(SendMethod.forNumber(sendLog.getSendingMethod())));

                if (exists) {
                    log.info(
                            "发送记录已存在，跳过迁移: businessId={}, oldReportId={}, newReportId={}, sendTime={}, id={}",
                            sendLog.getBusinessId(),
                            sendLog.getReportId(),
                            newReportId,
                            sendLog.getSentTime(),
                            sendLog.getId());
                    skippedCount++;
                } else {
                    recordsToMigrate.add(sendLog);
                }
            }

            // 唯一键去重
            recordsToMigrate = recordsToMigrate.stream()
                    .collect(Collectors.groupingBy(sendLog -> sendLog.getReportId() + "_" + sendLog.getSendingMethod()))
                    .values()
                    .stream()
                    .map(list -> list.stream()
                            .filter(sendLog -> sendLog.getStatus() != null && sendLog.getStatus() == 0)
                            .findFirst()
                            .orElse(list.get(0)))
                    .toList();

            if (!recordsToMigrate.isEmpty()) {
                // 5. 转换数据
                List<FulfillmentReportSendRecordSync> convertedRecords =
                        ReportCardMigrateConverter.INSTANCE.convertSendRecords(recordsToMigrate, reportIdMapping);

                if (!convertedRecords.isEmpty()) {
                    // 6. 批量迁移
                    BatchMigrateRecordsRequest request = BatchMigrateRecordsRequest.newBuilder()
                            .addAllRecords(convertedRecords)
                            .build();

                    BatchMigrateRecordsResponse response = fulfillmentServiceClient.batchMigrateRecords(request);

                    migratedCount += response.getSuccessCount();
                    skippedCount += response.getSkippedCount();
                    failedCount += response.getFailedCount();

                    log.info(
                            "grooming report send log 单次批量迁移完成: 成功={}, 跳过={}, 失败={}",
                            response.getSuccessCount(),
                            response.getSkippedCount(),
                            response.getFailedCount());
                }
            }
        } catch (Exception e) {
            log.error("批量迁移发送记录失败", e);
            failedCount += batch.size();
        }
        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    /**
     * 获取 report_id
     * Map<ole_report_id, new_report_id>
     */
    private Map<Integer, Integer> getReportIdMapping(
            List<GroomingReportSendLogDTO> sendLogs, Map<Integer, String> appointmentDates) {
        Map<Integer, Integer> reportIdMapping = new HashMap<>();

        try {
            // 构建唯一的 report 查询键
            Map<String, Integer> uniqueKeyToOldReportId = new HashMap<>();
            List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
            for (GroomingReportSendLogDTO sendLog : sendLogs) {
                String uniqueKey = buildReportKey(
                        sendLog.getBusinessId(),
                        sendLog.getGroomingId(),
                        sendLog.getPetId(),
                        CareCategory.GROOMING,
                        "");

                if (!uniqueKeyToOldReportId.containsKey(uniqueKey)) {
                    uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                            .setBusinessId(sendLog.getBusinessId().longValue())
                            .setAppointmentId(sendLog.getGroomingId().longValue())
                            .setPetId(sendLog.getPetId())
                            .setCareType(CareCategory.GROOMING)
                            .setServiceDate("")
                            .build());

                    uniqueKeyToOldReportId.put(uniqueKey, sendLog.getReportId());
                }
            }

            if (!uniqueKeys.isEmpty()) {
                // 批量查询 report 信息
                GetReportsByUniqueKeysResponse reportsResponse =
                        fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                                .addAllUniqueKeys(uniqueKeys)
                                .build());

                // 建立 report_id 映射关系
                for (var report : reportsResponse.getReportsList()) {
                    String uniqueKey = buildReportKey(
                            (int) report.getBusinessId(),
                            (int) report.getAppointmentId(),
                            (int) report.getPetId(),
                            CareCategory.GROOMING,
                            "");

                    Integer oldReportId = uniqueKeyToOldReportId.get(uniqueKey);
                    if (oldReportId != null) {
                        reportIdMapping.put(oldReportId, (int) report.getId());
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取 grooming_report_id 映射关系失败", e);
            reportIdMapping.clear();
        }

        log.info("获取到 {} 个 grooming_report_id 映射关系", reportIdMapping.size());
        return reportIdMapping;
    }

    private BatchResult processBatchDailySendLogs(List<DailyReportSendLogMigrateDef> batch) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;
        try {
            // get daily report config list
            List<DailyReportConfigMigrateDef> reportConfigsList = dailyReportService
                    .listDailyReportConfigByIds(ListDailyReportConfigByIdsRequest.newBuilder()
                            .addAllDailyReportIds(batch.stream()
                                    .map(DailyReportSendLogMigrateDef::getDailyReportId)
                                    .toList())
                            .build())
                    .getReportConfigsList();
            Map<Long, DailyReportConfigMigrateDef> reportConfigsMap =
                    reportConfigsList.stream().collect(Collectors.toMap(DailyReportConfigMigrateDef::getId, v -> v));

            // 获取 care type
            Map<String, CareCategory> careTypeMap = reportMigrateService.getCareTypeMap(reportConfigsList);

            // 1. 获取所有相关的 report 信息，建立 report_id 映射关系
            Map<Integer, Integer> reportIdMapping = getDailyReportIdMapping(batch, reportConfigsList, careTypeMap);

            if (reportIdMapping.isEmpty()) {
                log.warn("未找到任何对应的 daily report，跳过整个批次");
                return new BatchResult(0, batch.size(), 0);
            }

            // 2. 过滤出有对应 report_id 的发送记录
            List<DailyReportSendLogMigrateDef> recordsWithReport = new ArrayList<>();
            for (DailyReportSendLogMigrateDef sendLog : batch) {
                Integer reportKey = Math.toIntExact(sendLog.getDailyReportId());
                if (reportIdMapping.containsKey(reportKey)) {
                    recordsWithReport.add(sendLog);
                } else {
                    log.info(
                            "发送记录没有对应的 report，跳过迁移: reportId={}, sendLogId={}",
                            sendLog.getDailyReportId(),
                            sendLog.getId());
                    skippedCount++;
                }
            }

            if (recordsWithReport.isEmpty()) {
                return new BatchResult(0, skippedCount, 0);
            }

            // 3. 批量检查发送记录是否已存在
            GetRecordsByReportKeysResponse existingRecords =
                    fulfillmentServiceClient.getRecordsByReportKeys(GetRecordsByReportKeysRequest.newBuilder()
                            .addAllRecordKeys(recordsWithReport.stream()
                                    .map(sendLog -> {
                                        DailyReportConfigMigrateDef reportConfig =
                                                reportConfigsMap.get(sendLog.getDailyReportId());
                                        return SendRecordUniqueKey.newBuilder()
                                                .setReportUniqueKey(FulfillmentReportUniqueKey.newBuilder()
                                                        .setBusinessId(reportConfig.getBusinessId())
                                                        .setAppointmentId(reportConfig.getAppointmentId())
                                                        .setPetId(reportConfig.getPetId())
                                                        .setCareType(careTypeMap.get(reportConfig.getPetId() + "_"
                                                                + reportConfig.getAppointmentId()))
                                                        .setServiceDate(dateToString(reportConfig.getServiceDate()))
                                                        .build())
                                                .build();
                                    })
                                    .toList())
                            .build());

            // 4. 过滤出需要迁移的数据
            List<DailyReportSendLogMigrateDef> recordsToMigrate = new ArrayList<>();
            for (DailyReportSendLogMigrateDef sendLog : recordsWithReport) {
                // 获取映射后的新 reportId
                Integer newReportId = reportIdMapping.get(Math.toIntExact(sendLog.getDailyReportId()));
                boolean exists = existingRecords.getRecordsList().stream()
                        .anyMatch(existing -> existing.getReportId() == newReportId
                                && existing.getSendMethod().getNumber()
                                        == sendLog.getSendMethod().getNumber());

                if (exists) {
                    log.debug(
                            "发送记录已存在，跳过迁移: dailyReportId={}, sendMethod={}",
                            sendLog.getDailyReportId(),
                            sendLog.getSendMethod());
                    skippedCount++;
                } else {
                    recordsToMigrate.add(sendLog);
                }
            }

            // 唯一键去重
            recordsToMigrate = recordsToMigrate.stream()
                    .collect(Collectors.groupingBy(sendLog -> sendLog.getDailyReportId() + "_"
                            + sendLog.getSendMethod().getNumber()))
                    .values()
                    .stream()
                    .map(list -> list.stream()
                            .filter(DailyReportSendLogMigrateDef::getSentSuccess)
                            .findFirst()
                            .orElse(list.get(0)))
                    .toList();

            // 5. 转换数据
            List<FulfillmentReportSendRecordSync> convertedRecords =
                    ReportCardMigrateConverter.INSTANCE.convertDailySendRecords(
                            recordsToMigrate, reportIdMapping, reportConfigsMap);
            if (!convertedRecords.isEmpty()) {
                // 6. 批量迁移
                BatchMigrateRecordsRequest request = BatchMigrateRecordsRequest.newBuilder()
                        .addAllRecords(convertedRecords)
                        .build();
                BatchMigrateRecordsResponse response = fulfillmentServiceClient.batchMigrateRecords(request);
                migratedCount += response.getSuccessCount();
                skippedCount += response.getSkippedCount();
                failedCount += response.getFailedCount();
                log.info(
                        "Daily report send log 单次批量迁移完成: 成功={}, 跳过={}, 失败={}",
                        response.getSuccessCount(),
                        response.getSkippedCount(),
                        response.getFailedCount());
            }
        } catch (Exception e) {
            log.error("批量迁移发送记录失败", e);
            failedCount += batch.size();
        }
        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    private Map<Integer, Integer> getDailyReportIdMapping(
            List<DailyReportSendLogMigrateDef> sendLogs,
            List<DailyReportConfigMigrateDef> reportConfigsList,
            Map<String, CareCategory> careTypeMap) {
        // Map<reportId, uniqueKey>
        Map<Long, String> uniqueKeyMap = reportConfigsList.stream()
                .collect(Collectors.toMap(
                        DailyReportConfigMigrateDef::getId,
                        config -> buildReportKey(
                                Math.toIntExact(config.getBusinessId()),
                                Math.toIntExact(config.getAppointmentId()),
                                Math.toIntExact(config.getPetId()),
                                careTypeMap.get(config.getPetId() + "_" + config.getAppointmentId()),
                                dateToString(config.getServiceDate()))));

        Map<Integer, Integer> reportIdMapping = new HashMap<>();

        try {
            // 构建唯一的 report 查询键
            Map<String, Integer> uniqueKeyToOldReportId = new HashMap<>();
            List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
            for (DailyReportConfigMigrateDef reportConfig : reportConfigsList) {
                String uniqueKey = uniqueKeyMap.get(reportConfig.getId());

                if (!uniqueKeyToOldReportId.containsKey(uniqueKey)) {
                    uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                            .setBusinessId(reportConfig.getBusinessId())
                            .setAppointmentId(reportConfig.getAppointmentId())
                            .setPetId(reportConfig.getPetId())
                            .setCareType(
                                    careTypeMap.get(reportConfig.getPetId() + "_" + reportConfig.getAppointmentId()))
                            .setServiceDate(dateToString(reportConfig.getServiceDate()))
                            .build());

                    uniqueKeyToOldReportId.put(uniqueKey, Math.toIntExact(reportConfig.getId()));
                }
            }

            if (!uniqueKeys.isEmpty()) {
                // 批量查询 report 信息
                GetReportsByUniqueKeysResponse reportsResponse =
                        fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                                .addAllUniqueKeys(uniqueKeys)
                                .build());

                // 建立 report_id 映射关系
                for (var report : reportsResponse.getReportsList()) {
                    String uniqueKey = buildReportKey(
                            Math.toIntExact(report.getBusinessId()),
                            Math.toIntExact(report.getAppointmentId()),
                            Math.toIntExact(report.getPetId()),
                            careTypeMap.get(report.getPetId() + "_" + report.getAppointmentId()),
                            report.getServiceDate());

                    Integer oldReportId = uniqueKeyToOldReportId.get(uniqueKey);
                    if (oldReportId != null) {
                        reportIdMapping.put(oldReportId, Math.toIntExact(report.getId()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取 daily_report_id 映射关系失败", e);
            reportIdMapping.clear();
        }
        log.info("获取到 {} 个 daily_report_id 映射关系", reportIdMapping.size());
        return reportIdMapping;
    }

    /**
     * 构建 report 查询键
     */
    private String buildReportKey(
            Integer businessId, Integer reportId, Integer petId, CareCategory careType, String serviceDate) {
        return businessId + "_" + reportId + "_" + petId + "_" + careType.getNumber() + "_" + serviceDate;
    }

    @NotNull
    private static String dateToString(Date date) {
        return String.format("%04d-%02d-%02d", date.getYear(), date.getMonth(), date.getDay());
    }

    private record BatchResult(long migratedCount, long skippedCount, long failedCount) {}

    private record MigrateResult(long sourceCount, long migratedCount, long skippedCount, long failedCount) {}
}
