<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeZipcodeMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeZipcode">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="place_name" jdbcType="VARCHAR" property="placeName" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="state_abbreviation" jdbcType="VARCHAR" property="stateAbbreviation" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="place_id" jdbcType="VARCHAR" property="placeId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, zip_code, place_name, state, state_abbreviation, county, country_name, lat, lng, 
    place_id, status, updated_at, created_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_zipcode
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="searchByZipcodePrefix" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from moe_zipcode
        where `zip_code` LIKE concat(#{prefix},'%')
        order by zip_code limit 10
    </select>

    <select id="selectByZipcodeList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from moe_zipcode
        where `zip_code` IN
        <foreach close=")" collection="zipcodeList" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
