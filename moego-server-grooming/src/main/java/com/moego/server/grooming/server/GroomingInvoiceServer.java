package com.moego.server.grooming.server;

import static com.moego.common.enums.PaymentMethodEnum.MODULE_FULFILLMENT;

import com.moego.common.dto.CustomerPaymentSummary;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.params.CustomerIdsParams;
import com.moego.common.response.ResponseResult;
import com.moego.idl.models.order.v1.SplitTipsMethod;
import com.moego.server.grooming.api.IGroomingInvoiceServiceBase;
import com.moego.server.grooming.dto.AmountPercentagePair;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.dto.OnlineFeeInvoiceDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.dto.TipSplitDetailDTO;
import com.moego.server.grooming.dto.TipSplitDetailsDTO;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.BatchSaveOnlineFeeInvoiceParam;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.params.SaveTipSplitParams;
import com.moego.server.grooming.params.SetPaymentParams;
import com.moego.server.grooming.service.DepositService;
import com.moego.server.grooming.service.InvoiceService;
import com.moego.server.grooming.service.MoeGroomingOnlineFeeInvoiceService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.SplitTipsService;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GroomingInvoiceServer extends IGroomingInvoiceServiceBase {

    @Autowired
    private DepositService depositService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private SplitTipsService splitTipsService;

    @Autowired
    private MoeGroomingInvoiceApplyPackageMapper applyPackageMapper;

    @Autowired
    private MoeGroomingOnlineFeeInvoiceService onlineFeeInvoiceService;

    @Override
    public Boolean setPaymentResult(@RequestBody SetPaymentParams params) {
        if (Objects.equals(params.getModule(), MODULE_FULFILLMENT)) {
            return invoiceService.setPaymentResultForFulfillment(params);
        }
        return invoiceService.setPaymentResult(params);
    }

    @Override
    public ResponseResult<InvoiceSummaryDTO> getWithPaymentById(@RequestParam("invoiceId") Integer invoiceId) {
        return ResponseResult.success(invoiceService.getWithPaymentById(invoiceId, null));
    }

    @Override
    public ResponseResult<List<CustomerPaymentSummary>> queryCustomerPaymentSummary(
            @RequestBody CustomerIdsParams customerIdsParams) {
        return ResponseResult.success(invoiceService.queryCustomerPaymentSummary(
                customerIdsParams.getCustomerIds(), customerIdsParams.getBusinessId()));
    }

    @Override
    public ResponseResult<InvoiceSummaryDTO> setTips(@RequestBody InvoiceAmountVo param) {
        return ResponseResult.success(invoiceService.setTips(param));
    }

    @Override
    public Boolean saveTipSplitRecord(@RequestBody SaveTipSplitParams params) {
        splitTipsService.saveTipSplitRecord(params.toSplitRecord());
        return Boolean.TRUE;
    }

    @Override
    public ResponseResult<List<InvoiceSummaryDTO>> selectListByIds(@RequestBody CommonIdsParams params) {
        return ResponseResult.success(invoiceService.selectListByIds(params.getIds()));
    }

    @Override
    public InvoiceSummaryDTO checkInvoiceGuid(@RequestParam("guid") String guid) {
        MoeGroomingInvoice invoice;
        if (guid.startsWith(DepositService.DEPOSIT_PREFIX)) {
            MoeInvoiceDeposit deposit = depositService.getDepositByGuid(guid);
            if (deposit == null) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid guid.");
            }
            invoice = orderService.getOrderWithItemsById(deposit.getInvoiceId());
        } else {
            try {
                invoice = orderService.getOrderWithItemsByGuid(guid);
            } catch (CommonException e) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid invoice guid " + guid);
            }
        }
        InvoiceSummaryDTO dto = new InvoiceSummaryDTO();
        BeanUtils.copyProperties(invoice, dto);
        dto.setId(invoice.getId());
        dto.setBusinessId(invoice.getBusinessId());
        return dto;
    }

    @Override
    public List<InvoiceSummaryDTO> selectInvoicesByIds(@RequestBody CommonIdsParams params) {
        return invoiceService.selectInvoicesByIds(params.getIds());
    }

    private String convertSplitMethodToStr(int currentOrderSplitMethod) {
        if (currentOrderSplitMethod == SplitTipsMethod.SPLIT_TIPS_METHOD_BY_SERVICE_VALUE) {
            return SplitTipsService.SPLIT_METHOD_BY_SERVICE;
        } else if (currentOrderSplitMethod == SplitTipsMethod.SPLIT_TIPS_METHOD_BY_EQUALLY_VALUE) {
            return SplitTipsService.SPLIT_METHOD_BY_EQUALLY;
        } else {
            return SplitTipsService.SPLIT_METHOD_CUSTOMIZED;
        }
    }

    @Override
    public TipSplitDetailsDTO getTipsSplitDetails(@RequestBody List<Long> orderIds) {
        TipSplitDetailsDTO result = new TipSplitDetailsDTO();
        if (CollectionUtils.isEmpty(orderIds)) {
            result.setStaffTipAmountMap(Collections.emptyMap());
        }
        // 分别查询并汇总
        Map<Long, BigDecimal> staffTipAmountMap = new HashMap<>();
        orderIds.forEach(orderId -> {
            TipSplitDetailDTO detailDTO = splitTipsService.getTipsSplitDetailForOrder(
                    orderService.getOrderModelById(null, Math.toIntExact(orderId)));
            detailDTO.getStaffTipAmountList().forEach(staffTipAmountDTO -> {
                AmountPercentagePair pair =
                        staffTipAmountDTO.getAmountMap().get(convertSplitMethodToStr(detailDTO.getSplitMethod()));
                staffTipAmountMap.merge(
                        Long.valueOf(staffTipAmountDTO.getStaffId()), pair.getAmount(), BigDecimal::add);
            });
        });
        result.setStaffTipAmountMap(staffTipAmountMap);
        return result;
    }

    @Override
    public String getInvoiceClientUrl(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("invoiceId") Integer invoiceId,
            @RequestParam(value = "requiredProcessingFee", required = false) Boolean requiredProcessingFee) {
        return invoiceService.getInvoiceGuid(businessId, invoiceId, requiredProcessingFee);
    }

    @Override
    public String getInvoiceClientUrlByOrderId(
            @RequestParam("businessId") Integer businessId, @RequestParam("orderId") Integer orderId) {
        return invoiceService.getInvoiceGuid(businessId, orderId, null);
    }

    @Override
    @Deprecated
    public Integer fixAmountDeviation() {
        return 0;
    }

    @Override
    public Boolean usePackageForGrooming(
            @RequestParam("businessId") Integer businessId, @RequestParam("orderId") Integer orderId) {
        return invoiceService.usePackageForGrooming(orderId);
    }

    @Override
    public List<PackageServiceDTO> listApplyPackages(List<Long> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return List.of();
        }
        return applyPackageMapper
                .selectByInvoiceIds(invoiceIds.stream().map(Long::intValue).toList())
                .stream()
                .map(model -> {
                    PackageServiceDTO dto = new PackageServiceDTO();
                    dto.setId(model.getId());
                    dto.setInvoiceId(model.getInvoiceId());
                    dto.setInvoiceItemId(model.getInvoiceItemId());
                    dto.setPackageId(model.getPackageId());
                    dto.setServiceId(model.getServiceId());
                    dto.setPackageServiceId(model.getPackageServiceId());
                    dto.setPackageName(model.getPackageName());
                    dto.setServiceName(model.getServiceName());
                    dto.setQuantity(model.getQuantity());
                    dto.setCreateTime(model.getCreateTime());
                    dto.setUpdateTime(model.getUpdateTime());
                    return dto;
                })
                .toList();
    }

    @Override
    public List<OnlineFeeInvoiceDTO> listOnlineFeeInvoiceByInvoiceIds(List<Long> invoiceIds) {
        List<MoeGroomingOnlineFeeInvoice> feeInvoices = onlineFeeInvoiceService.selectByInvoiceIdList(invoiceIds);
        return feeInvoices.stream()
                .map(item -> {
                    OnlineFeeInvoiceDTO feeInvoiceDTO = new OnlineFeeInvoiceDTO();
                    feeInvoiceDTO.setId(item.getId());
                    feeInvoiceDTO.setBusinessId(item.getBusinessId());
                    feeInvoiceDTO.setInvoiceId(item.getInvoiceId());
                    feeInvoiceDTO.setType(item.getType());
                    feeInvoiceDTO.setRequiredFee(item.getRequiredFee());
                    feeInvoiceDTO.setCreateTime(item.getCreateTime());
                    feeInvoiceDTO.setUpdateTime(item.getUpdateTime());
                    feeInvoiceDTO.setCompanyId(item.getCompanyId());
                    return feeInvoiceDTO;
                })
                .toList();
    }

    @Override
    public void batchSaveOnlineFeeInvoice(BatchSaveOnlineFeeInvoiceParam param) {
        onlineFeeInvoiceService.batchSaveOnlineFeeInvoice(
                param.getBusinessId(), param.getInvoiceIdList(), param.isRequiredCvf());
    }
}
