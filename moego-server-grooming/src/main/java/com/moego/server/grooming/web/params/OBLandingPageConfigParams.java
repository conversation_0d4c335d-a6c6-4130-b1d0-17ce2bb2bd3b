package com.moego.server.grooming.web.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.URL;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Data
public class OBLandingPageConfigParams {

    @Size(max = 65535)
    @Schema(description = "About us, Inherited from moe_book_online_profile.description")
    private String aboutUs;

    @Size(max = 65535)
    @Schema(description = "Welcome page message, Inherited from moe_business_book_online.description")
    private String welcomePageMessage;

    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")
    @Schema(description = "Theme color, Inherited from moe_book_online_profile.button_color")
    private String themeColor;

    @Schema(description = "Business site live status")
    private Boolean isPublished;

    @URL
    @Size(max = 255)
    @Schema(description = "Showcase before image")
    private String showcaseBeforeImage;

    @URL
    @Size(max = 255)
    @Schema(description = "Showcase after image")
    private String showcaseAfterImage;

    @Pattern(regexp = "^[0-9a-z][0-9a-z-]*$")
    @Size(min = 5, max = 63)
    @Schema(description = "Customized URL domain name")
    private String urlDomainName;

    @Size(max = 255)
    @Schema(description = "GA4 PROPERTY ID")
    private String gaMeasurementId;

    @Schema(description = "Page components")
    private Map<String, Boolean> pageComponents;

    @Schema(description = "Amenity, 8 features and 4 payment options")
    private LandingPageAmenitiesParams amenities;

    @Valid
    @Schema(description = "Landing page gallery")
    private List<@NotNull OBLandingPageGalleryParams> galleryList;

    @Valid
    private OBConfigClientReviewParams clientReviews;

    private List<@NotNull @Valid OBConfigTeamParams> teams;

    @URL
    @Schema(description = "thank you page url")
    private String thankYouPageUrl;

    @Data
    @Accessors(chain = true)
    public static class LandingPageAmenitiesParams {

        @Schema(description = "Features")
        private Map<String, Boolean> features;

        @Schema(description = "Payment")
        private Map<String, Boolean> payment;
    }

    @Data
    public static class OBLandingPageGalleryParams {

        @URL
        @Size(max = 1024)
        @Schema(description = "Gallery image path")
        private String imagePath;

        @NotNull
        @Schema(description = "Gallery sort number")
        private Integer sort;
    }

    @Data
    public static class OBConfigTeamParams {
        @NotNull
        @Positive
        private Integer staffId;

        @URL
        @Size(max = 1024)
        private String instagramLink;

        @Size(max = 100)
        private String introduction;

        @Size(max = 10)
        private List<@Size(max = 50) String> tags;

        private Boolean isEnabled;
    }

    @Data
    public static class OBConfigClientReviewParams {

        private List<@Valid OBConfigClientReviewRecordParams> reviewRecords;

        private Boolean isDisplayClientReviewShowcasePhoto;
    }

    @Data
    public static class OBConfigClientReviewRecordParams {

        @NotNull
        @Positive
        private Integer reviewId;

        @Positive
        private Integer customerId;

        @Positive
        private Integer petId;
    }
}
