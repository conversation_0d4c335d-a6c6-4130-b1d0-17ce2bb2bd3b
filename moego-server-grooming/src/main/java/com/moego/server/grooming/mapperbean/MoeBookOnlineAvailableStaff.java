package com.moego.server.grooming.mapperbean;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_available_staff
 */
public class MoeBookOnlineAvailableStaff {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   by working hour type是否available, 0-disable, 1-enable
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.by_working_hour_enable
     *
     * @mbg.generated
     */
    private Byte byWorkingHourEnable;

    /**
     * Database Column Remarks:
     *   by slot type是否available, 0-disable, 1-enable
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.by_slot_enable
     *
     * @mbg.generated
     */
    private Byte bySlotEnable;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   sync with working hour 0-disable, 1-enable
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.sync_with_working_hour
     *
     * @mbg.generated
     */
    private Byte syncWithWorkingHour;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_available_staff.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.id
     *
     * @return the value of moe_book_online_available_staff.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.id
     *
     * @param id the value for moe_book_online_available_staff.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.business_id
     *
     * @return the value of moe_book_online_available_staff.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.business_id
     *
     * @param businessId the value for moe_book_online_available_staff.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.staff_id
     *
     * @return the value of moe_book_online_available_staff.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.staff_id
     *
     * @param staffId the value for moe_book_online_available_staff.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.by_working_hour_enable
     *
     * @return the value of moe_book_online_available_staff.by_working_hour_enable
     *
     * @mbg.generated
     */
    public Byte getByWorkingHourEnable() {
        return byWorkingHourEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.by_working_hour_enable
     *
     * @param byWorkingHourEnable the value for moe_book_online_available_staff.by_working_hour_enable
     *
     * @mbg.generated
     */
    public void setByWorkingHourEnable(Byte byWorkingHourEnable) {
        this.byWorkingHourEnable = byWorkingHourEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.by_slot_enable
     *
     * @return the value of moe_book_online_available_staff.by_slot_enable
     *
     * @mbg.generated
     */
    public Byte getBySlotEnable() {
        return bySlotEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.by_slot_enable
     *
     * @param bySlotEnable the value for moe_book_online_available_staff.by_slot_enable
     *
     * @mbg.generated
     */
    public void setBySlotEnable(Byte bySlotEnable) {
        this.bySlotEnable = bySlotEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.create_time
     *
     * @return the value of moe_book_online_available_staff.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.create_time
     *
     * @param createTime the value for moe_book_online_available_staff.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.update_time
     *
     * @return the value of moe_book_online_available_staff.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.update_time
     *
     * @param updateTime the value for moe_book_online_available_staff.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.sync_with_working_hour
     *
     * @return the value of moe_book_online_available_staff.sync_with_working_hour
     *
     * @mbg.generated
     */
    public Byte getSyncWithWorkingHour() {
        return syncWithWorkingHour;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.sync_with_working_hour
     *
     * @param syncWithWorkingHour the value for moe_book_online_available_staff.sync_with_working_hour
     *
     * @mbg.generated
     */
    public void setSyncWithWorkingHour(Byte syncWithWorkingHour) {
        this.syncWithWorkingHour = syncWithWorkingHour;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_available_staff.company_id
     *
     * @return the value of moe_book_online_available_staff.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_available_staff.company_id
     *
     * @param companyId the value for moe_book_online_available_staff.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
