package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSetting;
import com.moego.server.grooming.mapperbean.MoeQbSettingExample;
import com.moego.server.payment.params.ListQuickBookSettingParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    long countByExample(MoeQbSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int insert(MoeQbSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    List<MoeQbSetting> selectByExample(MoeQbSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    MoeQbSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MoeQbSetting record, @Param("example") MoeQbSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbSetting record, @Param("example") MoeQbSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSetting record);

    MoeQbSetting selectByBusinessId(Integer businessId, Byte status);

    List<MoeQbSetting> selectByCompanyId(Integer companyId);

    List<MoeQbSetting> selectAllStatusNormal();

    int updateOldRecordByStatus(
            @Param("id") Integer id, @Param("businessId") Integer businessId, @Param("updateTime") Long updateTime);

    Byte selectTaxSyncTypeByBusinessId(Integer businessId);

    MoeQbSetting selectByBusinessIdAndConnectId(
            @Param("businessId") Integer businessId, @Param("connectId") Integer connectId);

    int batchUpdateByPrimaryKeySelective(List<MoeQbSetting> records);

    List<MoeQbSetting> listReceiptOpenBusiness();

    List<MoeQbSetting> listByParams(
            ListQuickBookSettingParams params, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Integer countByParams(ListQuickBookSettingParams params);
}
