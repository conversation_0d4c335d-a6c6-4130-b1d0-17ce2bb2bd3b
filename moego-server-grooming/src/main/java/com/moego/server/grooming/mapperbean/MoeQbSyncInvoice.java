package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_invoice
 */
public class MoeQbSyncInvoice {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家店铺id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   realmId
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   moe_grooming_appointment表id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   invoice_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.invoice_id
     *
     * @mbg.generated
     */
    private Integer invoiceId;

    /**
     * Database Column Remarks:
     *   quickbookds invoice iD
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.qb_invoice_id
     *
     * @mbg.generated
     */
    private String qbInvoiceId;

    /**
     * Database Column Remarks:
     *   qb invoice 状态 1 正常  2已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.qb_invoice_status
     *
     * @mbg.generated
     */
    private Byte qbInvoiceStatus;

    /**
     * Database Column Remarks:
     *   相当于moe_grooming_invoice的type字段  type(appointment or noshow)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.invoice_type
     *
     * @mbg.generated
     */
    private String invoiceType;

    /**
     * Database Column Remarks:
     *   预约总金额
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.total_amount
     *
     * @mbg.generated
     */
    private BigDecimal totalAmount;

    /**
     * Database Column Remarks:
     *   支付总金额
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.paid_amount
     *
     * @mbg.generated
     */
    private BigDecimal paidAmount;

    /**
     * Database Column Remarks:
     *   0-not pay, 1-pay, 2- 部分支付
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.pay_status
     *
     * @mbg.generated
     */
    private Byte payStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_invoice.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.id
     *
     * @return the value of moe_qb_sync_invoice.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.id
     *
     * @param id the value for moe_qb_sync_invoice.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.business_id
     *
     * @return the value of moe_qb_sync_invoice.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.business_id
     *
     * @param businessId the value for moe_qb_sync_invoice.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.connect_id
     *
     * @return the value of moe_qb_sync_invoice.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.connect_id
     *
     * @param connectId the value for moe_qb_sync_invoice.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.realm_id
     *
     * @return the value of moe_qb_sync_invoice.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.realm_id
     *
     * @param realmId the value for moe_qb_sync_invoice.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.grooming_id
     *
     * @return the value of moe_qb_sync_invoice.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.grooming_id
     *
     * @param groomingId the value for moe_qb_sync_invoice.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.invoice_id
     *
     * @return the value of moe_qb_sync_invoice.invoice_id
     *
     * @mbg.generated
     */
    public Integer getInvoiceId() {
        return invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.invoice_id
     *
     * @param invoiceId the value for moe_qb_sync_invoice.invoice_id
     *
     * @mbg.generated
     */
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.qb_invoice_id
     *
     * @return the value of moe_qb_sync_invoice.qb_invoice_id
     *
     * @mbg.generated
     */
    public String getQbInvoiceId() {
        return qbInvoiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.qb_invoice_id
     *
     * @param qbInvoiceId the value for moe_qb_sync_invoice.qb_invoice_id
     *
     * @mbg.generated
     */
    public void setQbInvoiceId(String qbInvoiceId) {
        this.qbInvoiceId = qbInvoiceId == null ? null : qbInvoiceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.qb_invoice_status
     *
     * @return the value of moe_qb_sync_invoice.qb_invoice_status
     *
     * @mbg.generated
     */
    public Byte getQbInvoiceStatus() {
        return qbInvoiceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.qb_invoice_status
     *
     * @param qbInvoiceStatus the value for moe_qb_sync_invoice.qb_invoice_status
     *
     * @mbg.generated
     */
    public void setQbInvoiceStatus(Byte qbInvoiceStatus) {
        this.qbInvoiceStatus = qbInvoiceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.invoice_type
     *
     * @return the value of moe_qb_sync_invoice.invoice_type
     *
     * @mbg.generated
     */
    public String getInvoiceType() {
        return invoiceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.invoice_type
     *
     * @param invoiceType the value for moe_qb_sync_invoice.invoice_type
     *
     * @mbg.generated
     */
    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType == null ? null : invoiceType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.total_amount
     *
     * @return the value of moe_qb_sync_invoice.total_amount
     *
     * @mbg.generated
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.total_amount
     *
     * @param totalAmount the value for moe_qb_sync_invoice.total_amount
     *
     * @mbg.generated
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.paid_amount
     *
     * @return the value of moe_qb_sync_invoice.paid_amount
     *
     * @mbg.generated
     */
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.paid_amount
     *
     * @param paidAmount the value for moe_qb_sync_invoice.paid_amount
     *
     * @mbg.generated
     */
    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.pay_status
     *
     * @return the value of moe_qb_sync_invoice.pay_status
     *
     * @mbg.generated
     */
    public Byte getPayStatus() {
        return payStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.pay_status
     *
     * @param payStatus the value for moe_qb_sync_invoice.pay_status
     *
     * @mbg.generated
     */
    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.update_time
     *
     * @return the value of moe_qb_sync_invoice.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.update_time
     *
     * @param updateTime the value for moe_qb_sync_invoice.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.create_time
     *
     * @return the value of moe_qb_sync_invoice.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.create_time
     *
     * @param createTime the value for moe_qb_sync_invoice.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_invoice.company_id
     *
     * @return the value of moe_qb_sync_invoice.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_invoice.company_id
     *
     * @param companyId the value for moe_qb_sync_invoice.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
