package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.mapperbean.MoeGroomingDataRule;
import com.moego.server.grooming.service.MoeDataRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MoeDateRuleController {

    @Autowired
    private MoeDataRuleService moeDataRuleService;

    @Deprecated
    @GetMapping("/grooming/data/rule/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeGroomingDataRule> queryDataRuleInfo(AuthContext context) {
        return ResponseResult.success(moeDataRuleService.getDefaultDataRule());
    }
}
