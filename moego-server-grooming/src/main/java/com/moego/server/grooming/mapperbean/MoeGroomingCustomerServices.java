package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_customer_services
 */
public class MoeGroomingCustomerServices {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   商家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   客户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   记录创建staff_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.create_by
     *
     * @mbg.generated
     */
    private Integer createBy;

    /**
     * Database Column Remarks:
     *   宠物id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   服务id（对应表mm_business_services）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   服务名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_name
     *
     * @mbg.generated
     */
    private String serviceName;

    /**
     * Database Column Remarks:
     *   数据类型：1-主服务；2-额外服务
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_type
     *
     * @mbg.generated
     */
    private Integer serviceType;

    /**
     * Database Column Remarks:
     *   服务分类id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.category_id
     *
     * @mbg.generated
     */
    private Integer categoryId;

    /**
     * Database Column Remarks:
     *   服务时长（分钟）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_time
     *
     * @mbg.generated
     */
    private Integer serviceTime;

    /**
     * Database Column Remarks:
     *   服务价格
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_fee
     *
     * @mbg.generated
     */
    private BigDecimal serviceFee;

    /**
     * Database Column Remarks:
     *   save price/time for client:1-price；2-time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.save_type
     *
     * @mbg.generated
     */
    private Byte saveType;

    /**
     * Database Column Remarks:
     *   数据状态：1-正常 2-已删除 3-数据冲突导致的删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   税费id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.tax_id
     *
     * @mbg.generated
     */
    private Integer taxId;

    /**
     * Database Column Remarks:
     *   税率
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.tax_rate
     *
     * @mbg.generated
     */
    private Double taxRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   服务描述（详情）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_customer_services.service_detail
     *
     * @mbg.generated
     */
    private String serviceDetail;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.id
     *
     * @return the value of moe_grooming_customer_services.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.id
     *
     * @param id the value for moe_grooming_customer_services.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.business_id
     *
     * @return the value of moe_grooming_customer_services.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.business_id
     *
     * @param businessId the value for moe_grooming_customer_services.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.customer_id
     *
     * @return the value of moe_grooming_customer_services.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.customer_id
     *
     * @param customerId the value for moe_grooming_customer_services.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.create_by
     *
     * @return the value of moe_grooming_customer_services.create_by
     *
     * @mbg.generated
     */
    public Integer getCreateBy() {
        return createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.create_by
     *
     * @param createBy the value for moe_grooming_customer_services.create_by
     *
     * @mbg.generated
     */
    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.pet_id
     *
     * @return the value of moe_grooming_customer_services.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.pet_id
     *
     * @param petId the value for moe_grooming_customer_services.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_id
     *
     * @return the value of moe_grooming_customer_services.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_id
     *
     * @param serviceId the value for moe_grooming_customer_services.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_name
     *
     * @return the value of moe_grooming_customer_services.service_name
     *
     * @mbg.generated
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_name
     *
     * @param serviceName the value for moe_grooming_customer_services.service_name
     *
     * @mbg.generated
     */
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName == null ? null : serviceName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_type
     *
     * @return the value of moe_grooming_customer_services.service_type
     *
     * @mbg.generated
     */
    public Integer getServiceType() {
        return serviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_type
     *
     * @param serviceType the value for moe_grooming_customer_services.service_type
     *
     * @mbg.generated
     */
    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.category_id
     *
     * @return the value of moe_grooming_customer_services.category_id
     *
     * @mbg.generated
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.category_id
     *
     * @param categoryId the value for moe_grooming_customer_services.category_id
     *
     * @mbg.generated
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_time
     *
     * @return the value of moe_grooming_customer_services.service_time
     *
     * @mbg.generated
     */
    public Integer getServiceTime() {
        return serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_time
     *
     * @param serviceTime the value for moe_grooming_customer_services.service_time
     *
     * @mbg.generated
     */
    public void setServiceTime(Integer serviceTime) {
        this.serviceTime = serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_fee
     *
     * @return the value of moe_grooming_customer_services.service_fee
     *
     * @mbg.generated
     */
    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_fee
     *
     * @param serviceFee the value for moe_grooming_customer_services.service_fee
     *
     * @mbg.generated
     */
    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.save_type
     *
     * @return the value of moe_grooming_customer_services.save_type
     *
     * @mbg.generated
     */
    public Byte getSaveType() {
        return saveType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.save_type
     *
     * @param saveType the value for moe_grooming_customer_services.save_type
     *
     * @mbg.generated
     */
    public void setSaveType(Byte saveType) {
        this.saveType = saveType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.status
     *
     * @return the value of moe_grooming_customer_services.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.status
     *
     * @param status the value for moe_grooming_customer_services.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.create_time
     *
     * @return the value of moe_grooming_customer_services.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.create_time
     *
     * @param createTime the value for moe_grooming_customer_services.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.update_time
     *
     * @return the value of moe_grooming_customer_services.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.update_time
     *
     * @param updateTime the value for moe_grooming_customer_services.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.tax_id
     *
     * @return the value of moe_grooming_customer_services.tax_id
     *
     * @mbg.generated
     */
    public Integer getTaxId() {
        return taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.tax_id
     *
     * @param taxId the value for moe_grooming_customer_services.tax_id
     *
     * @mbg.generated
     */
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.tax_rate
     *
     * @return the value of moe_grooming_customer_services.tax_rate
     *
     * @mbg.generated
     */
    public Double getTaxRate() {
        return taxRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.tax_rate
     *
     * @param taxRate the value for moe_grooming_customer_services.tax_rate
     *
     * @mbg.generated
     */
    public void setTaxRate(Double taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.company_id
     *
     * @return the value of moe_grooming_customer_services.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.company_id
     *
     * @param companyId the value for moe_grooming_customer_services.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_customer_services.service_detail
     *
     * @return the value of moe_grooming_customer_services.service_detail
     *
     * @mbg.generated
     */
    public String getServiceDetail() {
        return serviceDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_customer_services.service_detail
     *
     * @param serviceDetail the value for moe_grooming_customer_services.service_detail
     *
     * @mbg.generated
     */
    public void setServiceDetail(String serviceDetail) {
        this.serviceDetail = serviceDetail == null ? null : serviceDetail.trim();
    }
}
