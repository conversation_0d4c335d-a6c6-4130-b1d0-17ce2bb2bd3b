package com.moego.server.grooming.service.report.migrate;

import com.moego.server.grooming.web.dto.ReportCardDataFixResultDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateProgressDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Report Card 数据迁移服务
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardMigrateService {

    private final ReportCardTemplateMigrateService templateMigrateService;
    private final ReportCardReportMigrateService reportMigrateService;
    private final ReportCardQuestionMigrateService questionMigrateService;
    private final ReportCardSendRecordMigrateService sendRecordMigrateService;

    @Resource(name = "developScriptExecutorService")
    private ExecutorService executorService;

    // 存储任务进度的Map
    private final Map<String, ReportCardMigrateProgressDTO> taskProgressMap = new ConcurrentHashMap<>();
    private final Map<String, Future<?>> taskFutureMap = new ConcurrentHashMap<>();

    /**
     * 迁移顺序定义
     */
    private static final List<String> MIGRATION_ORDER =
            Arrays.asList("theme_config", "template", "report", "question", "send_record");

    /**
     * 启动完整的数据迁移
     */
    public ReportCardMigrateResultDTO startFullMigration(Integer businessId) {
        String taskId = generateTaskId();
        LocalDateTime startTime = LocalDateTime.now();

        log.info("启动完整数据迁移，taskId: {}, businessId: {}", taskId, businessId);

        // 初始化进度跟踪
        ReportCardMigrateProgressDTO progress = ReportCardMigrateProgressDTO.builder()
                .taskId(taskId)
                .status(ReportCardMigrateProgressDTO.TaskStatus.RUNNING)
                .startTime(startTime)
                .progressPercent(0)
                .completedTables(new ArrayList<>())
                .pendingTables(new ArrayList<>(MIGRATION_ORDER))
                .currentMessage("开始数据迁移...")
                .build();
        taskProgressMap.put(taskId, progress);

        // 异步执行迁移任务
        Future<?> future = executorService.submit(() -> {
            try {
                executeFullMigration(taskId, businessId);
            } catch (Exception e) {
                log.error("数据迁移执行失败，taskId: {}", taskId, e);
                updateProgressError(taskId, e.getMessage());
            }
        });
        taskFutureMap.put(taskId, future);

        return ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(true)
                .startTime(startTime)
                .businessIds(businessId != null ? List.of(businessId) : null)
                .build();
    }

    /**
     * 执行完整迁移流程
     */
    private void executeFullMigration(String taskId, Integer businessId) {
        Map<String, ReportCardMigrateResultDTO.TableMigrateDetail> tableDetails = new HashMap<>();
        LocalDateTime overallStartTime = LocalDateTime.now();
        boolean overallSuccess;
        String overallErrorMessage = null;

        try {
            // 1. 迁移主题配置: 执行sql迁移

            // 2. 迁移模板
            updateProgress(taskId, 20, "template", "正在迁移模板数据...");
            ReportCardMigrateResultDTO templateResult = templateMigrateService.migrate(businessId);
            tableDetails.put("template", convertToTableDetail(templateResult, "template"));
            updateProgress(taskId, 40, null, "模板迁移完成");

            // 3. 迁移报告
            updateProgress(taskId, 40, "report", "正在迁移报告数据...");
            ReportCardMigrateResultDTO reportResult = reportMigrateService.migrate(businessId);
            tableDetails.put("report", convertToTableDetail(reportResult, "report"));
            updateProgress(taskId, 60, null, "报告迁移完成");

            // 4. 迁移问题
            updateProgress(taskId, 60, "question", "正在迁移问题数据...");
            ReportCardMigrateResultDTO questionResult = questionMigrateService.migrate(businessId);
            tableDetails.put("question", convertToTableDetail(questionResult, "question"));
            updateProgress(taskId, 80, null, "问题迁移完成");

            // 5. 迁移发送记录
            updateProgress(taskId, 80, "send_record", "正在迁移发送记录数据...");
            ReportCardMigrateResultDTO sendRecordResult = sendRecordMigrateService.migrate(businessId);
            tableDetails.put("send_record", convertToTableDetail(sendRecordResult, "send_record"));
            updateProgress(taskId, 100, null, "所有数据迁移完成");

            // 检查是否有失败的表
            overallSuccess =
                    tableDetails.values().stream().allMatch(ReportCardMigrateResultDTO.TableMigrateDetail::getSuccess);
            if (!overallSuccess) {
                overallErrorMessage = "部分表迁移失败，请查看详细信息";
            }

        } catch (Exception e) {
            log.error("迁移过程中发生异常，taskId: {}", taskId, e);
            overallSuccess = false;
            overallErrorMessage = e.getMessage();
            updateProgressError(taskId, e.getMessage());
        }

        // 更新最终结果
        LocalDateTime endTime = LocalDateTime.now();
        ReportCardMigrateResultDTO finalResult = ReportCardMigrateResultDTO.builder()
                .taskId(taskId)
                .success(overallSuccess)
                .errorMessage(overallErrorMessage)
                .startTime(overallStartTime)
                .endTime(endTime)
                .durationMs(
                        java.time.Duration.between(overallStartTime, endTime).toMillis())
                .businessIds(businessId != null ? List.of(businessId) : null)
                .tableDetails(tableDetails)
                .summary(buildSummary(tableDetails))
                .build();

        // 更新进度为完成状态
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            progress.setStatus(
                    overallSuccess
                            ? ReportCardMigrateProgressDTO.TaskStatus.COMPLETED
                            : ReportCardMigrateProgressDTO.TaskStatus.FAILED);
            progress.setProgressPercent(100);
            progress.setCurrentMessage(overallSuccess ? "迁移完成" : "迁移失败: " + overallErrorMessage);
            progress.setErrorMessage(overallErrorMessage);
        }

        log.info(
                "完整数据迁移结束，taskId: {}, success: {}, duration: {}ms",
                taskId,
                overallSuccess,
                finalResult.getDurationMs());
    }

    /**
     * 迁移模板数据
     */
    public ReportCardMigrateResultDTO migrateTemplate(Integer businessId) {
        log.info("开始迁移模板数据，businessId: {}", businessId);
        return templateMigrateService.migrate(businessId);
    }

    /**
     * 迁移报告数据
     */
    public ReportCardMigrateResultDTO migrateReport(Integer businessId) {
        log.info("开始迁移报告数据，businessId: {}", businessId);
        return reportMigrateService.migrate(businessId);
    }

    /**
     * 迁移问题数据
     */
    public ReportCardMigrateResultDTO migrateQuestion(Integer businessId) {
        log.info("开始迁移问题数据，businessId: {}", businessId);
        return questionMigrateService.migrate(businessId);
    }

    /**
     * 迁移发送记录数据
     */
    public ReportCardMigrateResultDTO migrateSendRecord(Integer businessId) {
        log.info("开始迁移发送记录数据，businessId: {}}", businessId);
        return sendRecordMigrateService.migrate(businessId);
    }

    /**
     * 获取迁移进度
     */
    public ReportCardMigrateProgressDTO getProgress(String taskId) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }
        return progress;
    }

    /**
     * 停止迁移任务
     */
    public void stopMigration(String taskId) {
        Future<?> future = taskFutureMap.get(taskId);
        if (future != null && !future.isDone()) {
            future.cancel(true);
            updateProgress(taskId, null, null, "任务已取消");
            ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
            if (progress != null) {
                progress.setStatus(ReportCardMigrateProgressDTO.TaskStatus.CANCELLED);
            }
        }
        taskFutureMap.remove(taskId);
    }

    // ==================== 私有辅助方法 ====================

    private String generateTaskId() {
        return "migrate_" + System.currentTimeMillis() + "_"
                + UUID.randomUUID().toString().substring(0, 8);
    }

    private void updateProgress(String taskId, Integer progressPercent, String currentTable, String message) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            if (progressPercent != null) {
                progress.setProgressPercent(progressPercent);
            }
            if (currentTable != null) {
                progress.setCurrentTable(currentTable);
                progress.getPendingTables().remove(currentTable);
                if (!progress.getCompletedTables().contains(currentTable)) {
                    progress.getCompletedTables().add(currentTable);
                }
            }
            progress.setCurrentMessage(message);
        }
    }

    private void updateProgressError(String taskId, String errorMessage) {
        ReportCardMigrateProgressDTO progress = taskProgressMap.get(taskId);
        if (progress != null) {
            progress.setStatus(ReportCardMigrateProgressDTO.TaskStatus.FAILED);
            progress.setErrorMessage(errorMessage);
            progress.setCurrentMessage("迁移失败: " + errorMessage);
        }
    }

    private ReportCardMigrateResultDTO.TableMigrateDetail convertToTableDetail(
            ReportCardMigrateResultDTO result, String tableName) {
        // 从结果中提取表详情，这里简化处理
        return ReportCardMigrateResultDTO.TableMigrateDetail.builder()
                .tableName(tableName)
                .success(result.getSuccess())
                .errorMessage(result.getErrorMessage())
                .startTime(result.getStartTime())
                .endTime(result.getEndTime())
                .durationMs(result.getDurationMs())
                .build();
    }

    private ReportCardMigrateResultDTO.MigrateSummary buildSummary(
            Map<String, ReportCardMigrateResultDTO.TableMigrateDetail> tableDetails) {
        int totalTables = tableDetails.size();
        int successTables = (int) tableDetails.values().stream()
                .mapToLong(detail -> detail.getSuccess() ? 1 : 0)
                .sum();
        int failedTables = totalTables - successTables;

        return ReportCardMigrateResultDTO.MigrateSummary.builder()
                .totalTables(totalTables)
                .successTables(successTables)
                .failedTables(failedTables)
                .build();
    }
}
