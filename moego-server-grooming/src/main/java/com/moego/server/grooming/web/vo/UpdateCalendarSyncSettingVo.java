package com.moego.server.grooming.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class UpdateCalendarSyncSettingVo {

    @Schema(description = "被同步的staff id 数组")
    private List<Integer> syncedStaffIdList;

    @Schema(description = "是否关闭链接")
    private Boolean disconnect;

    @Schema(description = "日历名字模板")
    private String calendarName;

    @Schema(description = "日历事件标题模板")
    private String eventTitle;

    @Schema(description = "日历事件描述模板")
    private String eventDescription;
}
