package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Data
@Accessors(chain = true)
public class PaymentDetailVO {

    @Schema(description = "Prepayment / Deposit")
    private OBDepositVO detail;

    @Schema(description = "Stripe credit card info")
    private CreditCardDetailVO creditCard;

    @Schema(description = "1-card, 2-card on file, 3-bluetooth, 4-smart reader, 5-apple pay, 6-google pay")
    private Byte stripePaymentMethod;
}
