package com.moego.server.grooming.service;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 用于兼容旧接口，将 staff times 的数据同步到新的表中
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StaffAvailableSyncService {

    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final MoeBookOnlineAvailableStaffMapper bookOnlineAvailableStaffMapper;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    /**
     * 重定向 bookOnlineAvailableStaffMapper 的方法
     *
     * @param record
     */
    public void updateByStaffId(MoeBookOnlineAvailableStaff record) {
        // business id + staff id
        bookOnlineAvailableStaffMapper.updateByStaffId(record);
        ThreadPool.execute(() -> {
            asyncStaffAvailable(record.getBusinessId(), record.getStaffId());
        });
    }

    /**
     * 重定向 bookOnlineAvailableStaffMapper 的方法
     *
     * @param record
     */
    public void insert(MoeBookOnlineAvailableStaff record) {
        // exist check
        List<MoeBookOnlineAvailableStaff> existsRecords = bookOnlineAvailableStaffMapper.selectByBusinessIdAndStaffId(
                record.getBusinessId(), record.getStaffId());
        if (!CollectionUtils.isEmpty(existsRecords)) {
            return;
        }
        // business id + staff id
        bookOnlineAvailableStaffMapper.insert(record);
        ThreadPool.execute(() -> {
            asyncStaffAvailable(
                    record.getCompanyId(),
                    record.getBusinessId(),
                    record.getStaffId(),
                    record.getByWorkingHourEnable(),
                    record.getBySlotEnable());
        });
    }

    /**
     * 重定向 bookOnlineAvailableStaffMapper 方法
     *
     * @param businessId
     * @param syncWithWorkingHour
     */
    public void updateSyncByBusinessId(Integer businessId, Byte syncWithWorkingHour) {
        // business id + staff id
        bookOnlineAvailableStaffMapper.updateSyncByBusinessId(businessId, syncWithWorkingHour);
        ThreadPool.execute(() -> {
            asyncAvailableTypeSync(businessId);
        });
    }

    /**
     * 同步 staff available 状态
     *
     * @param businessId
     * @param staffId
     */
    public void asyncStaffAvailable(Integer businessId, Integer staffId) {
        var availableStaffList =
                bookOnlineAvailableStaffMapper.selectByBusinessIdAndStaffId(businessId, staffId); // exist check
        // 未初始化有可能空值，正常跳过即可
        if (CollectionUtils.isEmpty(availableStaffList)) {
            return;
        }
        var availableStaff = availableStaffList.get(0);
        var staffAvailable = queryStaffObAvailable(businessId, staffId);
        obStaffAvailabilityServiceBlockingStub.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(availableStaff.getCompanyId())
                .setBusinessId(availableStaff.getBusinessId())
                .addStaffAvailabilityList(StaffAvailabilityDef.newBuilder()
                        .setStaffId(availableStaff.getStaffId())
                        .setIsAvailable(staffAvailable)
                        .build())
                .build());
    }

    public void asyncStaffAvailable(
            Long companyId, Integer businessId, Integer staffId, Byte byWorkingHourEnable, Byte bySlotEnable) {
        var staffAvailable = false;
        if (byWorkingHourEnable == null && bySlotEnable == null) {
            staffAvailable = queryStaffObAvailable(businessId, staffId);
        } else {
            staffAvailable = Objects.equals(byWorkingHourEnable, (byte) 1) || Objects.equals(bySlotEnable, (byte) 1);
        }
        obStaffAvailabilityServiceBlockingStub.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addStaffAvailabilityList(StaffAvailabilityDef.newBuilder()
                        .setStaffId(staffId)
                        .setIsAvailable(staffAvailable)
                        .build())
                .build());
    }

    /**
     * 同步 available type sync 开关
     *
     * @param businessId
     */
    public void asyncAvailableTypeSync(Integer businessId) {
        // sync
        var businessOBConfig = bookOnlineMapper.selectByBusinessId(businessId);
        if (businessOBConfig == null) {
            return;
        }
        boolean syncWithWorkingHourStatus =
                bookOnlineAvailableStaffMapper.selectDisableSyncCountByBusinessId(businessId) == 0;

        // get business time type
        var availabilityType = staffService
                .getBusinessStaffAvailabilityType(
                        com.moego.idl.service.organization.v1.GetBusinessStaffAvailabilityTypeRequest.newBuilder()
                                .setCompanyId(businessOBConfig.getCompanyId())
                                .setBusinessId(businessId)
                                .build())
                .getAvailabilityType();
        Byte availableTimeType =
                switch (availabilityType) {
                    case BY_TIME -> OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR;
                    case BY_SLOT -> OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT;
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid availability type");
                };

        var obConfigUpdateBean = new MoeBusinessBookOnline();
        obConfigUpdateBean.setId(businessOBConfig.getId());
        var availableTimeSync = syncWithWorkingHourStatus ? CommonConstant.ENABLE : CommonConstant.DISABLE;
        obConfigUpdateBean.setAvailableTimeSync(availableTimeSync);
        obConfigUpdateBean.setUpdateTime(DateUtil.get10Timestamp());
        obConfigUpdateBean.setAvailableTimeType(availableTimeType);
        bookOnlineMapper.updateByPrimaryKeySelective(obConfigUpdateBean);
        log.info(
                "syncStatusChanged in asyncAvailableTypeSync, businessId:{}, syncStatus:{}",
                businessId,
                availableTimeSync);
    }

    /**
     * 转化过程会合并数据，优先根据当前 ob 的 available type 来控制是否保留 staff available
     *
     * @param businessId
     * @param staffId
     * @return
     */
    public boolean queryStaffObAvailable(Integer businessId, Integer staffId) {
        // query moe_book_online_available_staff
        var availableStaff =
                bookOnlineAvailableStaffMapper.selectByBusinessIdAndStaffId(businessId, staffId); // exist check
        if (CollectionUtils.isEmpty(availableStaff)) {
            return false;
        }

        // query moe_business_book_online
        var bookOnlineSetting = bookOnlineMapper.selectByBusinessId(businessId);
        if (Objects.isNull(bookOnlineSetting)) {
            return false;
        }
        if (OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT == bookOnlineSetting.getAvailableTimeType()) {
            return availableStaff.get(0).getBySlotEnable() == OnlineBookingConst.STAFF_AVAILABLE_ENABLE;
        } else {
            return availableStaff.get(0).getByWorkingHourEnable() == OnlineBookingConst.STAFF_AVAILABLE_ENABLE;
        }
    }

    /**
     * 手动执行，触发数据同步，将 staff available 数据同步到新表
     */
    public void taskSyncAllAvailableRecord() {
        bookOnlineMapper.listAll().forEach(obConfig -> {
            var bid = obConfig.getBusinessId();
            try {
                syncOneBidAvailable(bid);
            } catch (Exception e) {
                log.error("syncOneBidAvailable error, bid:{}", bid, e);
            }
        });
    }

    /**
     * 新接口 for available time query
     *
     * @param businessId
     * @return
     */
    public Boolean queryAvailableTimeSync(Integer businessId) {
        var bookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        if (bookOnline == null) {
            return false;
        }
        return Objects.equals(bookOnline.getAvailableTimeSync(), CommonConstant.ENABLE);
    }

    /**
     * 触发 bid 的 staff 同步
     *
     * @param bid
     */
    public void syncOneBidAvailable(Integer bid) {
        // sync available type sync
        asyncAvailableTypeSync(bid);
        // sync staff available
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(bid);
        var staffList = iBusinessStaffClient.getStaffList(staffIdListParams);
        staffList.forEach(staff -> {
            asyncStaffAvailable(bid, staff.getId());
        });
    }
}
