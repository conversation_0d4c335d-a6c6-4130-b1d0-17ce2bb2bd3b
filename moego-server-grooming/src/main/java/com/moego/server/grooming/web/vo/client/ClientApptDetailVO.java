package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/2
 */
@Data
@Accessors(chain = true)
public class ClientApptDetailVO {

    @Schema(description = "Appointment detail")
    private ApptDetailVO appt;

    @Schema(description = "Business client info")
    private ClientInfoVO clientInfo;

    @Schema(description = "Online booking profile")
    private OBProfileVO obProfile;

    @Schema(description = "Business info")
    private BusinessInfoVO businessInfo;

    @Schema(description = "Online booking config")
    private OBConfigVO obConfig;

    @Schema(description = "Business customer primary address info")
    private AddressVO serviceAddress;

    @Schema(description = "Appointment details")
    List<ClientApptItemVO> itemList;

    @Schema(description = "Payment info")
    private PaymentDetailVO prepayPayment;

    @Schema(description = "Business twilio info")
    private BusinessTwilioVO businessTwilio;

    @Schema(description = "Grooming report records")
    private List<GroomingReportRecordVO> groomingReportRecords;
}
