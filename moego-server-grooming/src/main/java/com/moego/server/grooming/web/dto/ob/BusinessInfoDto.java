package com.moego.server.grooming.web.dto.ob;

import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import lombok.Data;

@Data
public class BusinessInfoDto {

    Byte isEnable;
    MoeBusinessBookOnline setting;
    Boolean isAvailable;
    /**
     * Landing page 3.0 merge flag
     */
    Boolean isMerged;

    // whether the business is in package whitelist
    Boolean isInPackageWhitelist;
}
