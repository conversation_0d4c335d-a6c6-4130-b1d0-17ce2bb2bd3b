package com.moego.server.grooming.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.server.grooming.mapperbean.MoeGcCalendar;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class GoogleCalendarSettingDto {

    @Schema(description = "googleAuthId")
    private Integer googleAuthId;

    @Schema(description = "设置信息的数据")
    private Integer settingId;

    @JsonIgnore
    private List<MoeGcCalendar> gcCalendarList;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @Schema(description = "当前设置所属的staffId")
    private Integer staffId;

    @Schema(description = "同步方式 1 import and export   2export only   3import only")
    private Byte syncType;

    @Schema(description = "同步状态 1 正常连接中   2连接中断")
    private Byte status;

    @Schema(description = "连接时间")
    private Long createTime;

    private Long updateTime;

    @Schema(description = "google 授权的email")
    private String googleAuthEmail;

    @Schema(description = "google 授权 URL")
    private String redirectUri;

    @Schema(description = "当前有效的被同步staff")
    private List<Integer> syncedStaffIdList;

    @Schema(description = "日历名字模板")
    private String calendarName;

    @Schema(description = "日历事件标题模板")
    private String eventTitle;

    @Schema(description = "日历事件描述模板")
    private String eventDescription;
}
