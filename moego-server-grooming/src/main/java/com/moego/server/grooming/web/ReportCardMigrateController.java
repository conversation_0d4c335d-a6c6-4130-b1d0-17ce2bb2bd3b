package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.report.migrate.ReportCardMigrateService;
import com.moego.server.grooming.web.dto.ReportCardDataFixResultDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateProgressDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import java.util.List;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Report Card 数据迁移控制器
 * 负责将 grooming report 和 daily report 数据迁移到 fulfillment 服务
 *
 */
@RestController
@RequestMapping("/scripts/report-card-migrate")
@RequiredArgsConstructor
@Slf4j
@Hidden
@Tag(name = "Report Card Migration", description = "Report Card数据迁移相关接口")
public class ReportCardMigrateController {

    private final ReportCardMigrateService reportCardMigrateService;

    /**
     * 启动完整的Report Card数据迁移
     * 按照依赖顺序迁移所有表：theme_config → template → report → question → send_record
     */
    @PostMapping("/start")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "启动完整数据迁移", description = "按照依赖顺序迁移所有Report Card相关数据")
    public ReportCardMigrateResultDTO startFullMigration(
            @Parameter(description = "指定businessID，为空则迁移所有数据") @RequestParam(required = false) Integer businessId) {

        log.info("开始Report Card数据迁移，businessId: {}", businessId);

        try {
            return reportCardMigrateService.startFullMigration(businessId);
        } catch (Exception e) {
            log.error("Report Card数据迁移失败", e);
            throw e;
        }
    }

    /**
     * 迁移模板数据
     * moe_grooming_report_template → fulfillment_report_template
     */
    @PostMapping("/template")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移模板数据")
    public ReportCardMigrateResultDTO migrateTemplate(@RequestParam(required = false) Integer businessId) {

        log.info("开始迁移模板数据，businessId: {}", businessId);
        return reportCardMigrateService.migrateTemplate(businessId);
    }

    /**
     * 迁移报告数据
     * moe_grooming_report + daily_report_config → fulfillment_report
     */
    @PostMapping("/report")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移报告数据")
    public ReportCardMigrateResultDTO migrateReport(@RequestParam(required = false) Integer businessId) {

        log.info("开始迁移报告数据，businessId: {}", businessId);
        return reportCardMigrateService.migrateReport(businessId);
    }

    /**
     * 迁移问题数据
     * moe_grooming_report_question → fulfillment_report_question
     */
    @PostMapping("/question")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移问题数据")
    public ReportCardMigrateResultDTO migrateQuestion(@RequestParam(required = false) Integer businessId) {

        log.info("开始迁移问题数据，businessId: {}", businessId);
        return reportCardMigrateService.migrateQuestion(businessId);
    }

    /**
     * 迁移发送记录数据
     * moe_grooming_report_send_log + daily_report_send_log → fulfillment_report_send_record
     */
    @PostMapping("/send-record")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "迁移发送记录数据")
    public ReportCardMigrateResultDTO migrateSendRecord(@RequestParam(required = false) Integer businessId) {

        log.info("开始迁移发送记录数据，businessId: {}", businessId);
        return reportCardMigrateService.migrateSendRecord(businessId);
    }

    /**
     * 同步template表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/template")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixTemplateData(@RequestParam Long oldId) {
        return reportCardMigrateService.fixTemplateData(oldId);
    }

    /**
     * 同步question表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/question")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixQuestionData(@RequestParam Long oldId) {
        return reportCardMigrateService.fixQuestionData(oldId);
    }

    /**
     * 同步grooming report表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/grooming/report")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixGroomingReportData(@RequestParam Long oldId) {
        return reportCardMigrateService.fixGroomingReportData(oldId);
    }

    /**
     * 同步daily report表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/daily/report")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixDailyReportData(@RequestParam Long oldId) {
        return reportCardMigrateService.fixDailyReportData(id);
    }

    /**
     * 同步grooming send record表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/grooming/send-record")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixGroomingSendRecordData(@RequestParam Long id) {
        return reportCardMigrateService.fixGroomingSendRecordData(id);
    }

    /**
     * 同步daily send record表单条数据
     * @param
     * @return
     */
    @PostMapping("/fix/daily/send-record")
    @Auth(AuthType.ANONYMOUS)
    public boolean fixDailySendRecordData(@RequestParam Long id) {
        return reportCardMigrateService.fixDailySendRecordData(id);
    }

    /**
     * 获取迁移进度
     */
    @GetMapping("/progress")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "获取迁移进度")
    public ReportCardMigrateProgressDTO getProgress(@Parameter(description = "任务ID") @RequestParam String taskId) {

        return reportCardMigrateService.getProgress(taskId);
    }

    /**
     * 停止正在进行的迁移任务
     */
    @PostMapping("/stop")
    @Auth(AuthType.ANONYMOUS)
    @Operation(summary = "停止迁移任务")
    public void stopMigration(@Parameter(description = "任务ID") @RequestParam String taskId) {

        log.info("停止迁移任务，taskId: {}", taskId);
        reportCardMigrateService.stopMigration(taskId);
    }
}
