package com.moego.server.grooming.service.ob;

import com.moego.server.grooming.mapper.MoeBookOnlineLandingPageConfigMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.GroomingFeaturePricingCheckService;
import com.moego.server.grooming.web.vo.ob.OBBusinessLocationVO;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBLocationService {

    private final MoeBookOnlineProfileMapper bookOnlineProfileMapper;
    private final MoeBusinessBookOnlineMapper businessBookOnlineMapper;
    private final MoeBookOnlineLandingPageConfigMapper landingPageConfigMapper;

    private final GroomingFeaturePricingCheckService pricingCheckService;
    private final OBBusinessProfileService businessProfileService;

    public List<OBBusinessLocationVO> getBookOnlineLocationDTOList(List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return Collections.emptyList();
        }
        List<MoeBookOnlineProfile> businessProfileList = bookOnlineProfileMapper.getBusinessProfileList(businessIdList);
        Map<Integer, MoeBookOnlineProfile> businessOBProfileMap = businessProfileList.stream()
                .collect(Collectors.toMap(MoeBookOnlineProfile::getBusinessId, Function.identity()));
        List<MoeBookOnlineLandingPageConfig> landingPageConfigList =
                landingPageConfigMapper.listByBusinessId(businessIdList);
        Map<Integer, MoeBookOnlineLandingPageConfig> landingPageConfigMap = landingPageConfigList.stream()
                .collect(Collectors.toMap(
                        MoeBookOnlineLandingPageConfig::getBusinessId, landingPageConfig -> landingPageConfig));
        List<Integer> availableBusinessIdList = businessProfileList.stream()
                .map(MoeBookOnlineProfile::getBusinessId)
                .toList();
        List<MoeBusinessBookOnline> obInfoList = getSettingInfoList(availableBusinessIdList);
        return obInfoList.stream()
                .filter(obInfo -> {
                    // Filter unpublished sites
                    MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigMap.get(obInfo.getBusinessId());
                    if (Objects.isNull(landingPageConfig)) {
                        return true;
                    }
                    return landingPageConfig.getIsPublished();
                })
                .map(obInfo -> {
                    Integer businessId = obInfo.getBusinessId();
                    OBBusinessLocationVO currentLocation = new OBBusinessLocationVO();
                    MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigMap.get(businessId);
                    MoeBookOnlineProfile profile = businessProfileService.getProfile(
                            businessId, businessOBProfileMap.get(businessId), landingPageConfig);
                    if (Objects.nonNull(profile)) {
                        BeanUtils.copyProperties(profile, currentLocation);
                    }
                    Optional.ofNullable(landingPageConfig)
                            .ifPresent(config -> currentLocation.setUrlDomainName(config.getUrlDomainName()));
                    currentLocation.setBookOnlineName(obInfo.getBookOnlineName());
                    currentLocation.setIsEnable(obInfo.getIsEnable());
                    currentLocation.setIsAvailable(true);
                    return currentLocation;
                })
                .toList();
    }

    public List<MoeBusinessBookOnline> getSettingInfoList(List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return Collections.emptyList();
        }
        List<MoeBusinessBookOnline> obSettingList = businessBookOnlineMapper.getBusinessBookOnlineList(businessIdList);
        for (MoeBusinessBookOnline obSetting : obSettingList) {
            pricingCheckService.checkOnlineBooking(obSetting.getBusinessId(), obSetting);
        }
        return obSettingList;
    }
}
