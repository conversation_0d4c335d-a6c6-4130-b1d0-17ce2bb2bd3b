package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.dto.report.ReportWebEmployee;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class TipsApptsPageDTO {

    private Integer count;
    private List<GroomingReportWebAppointment> appointments;
    private List<ReportWebEmployee> reportBeans;
}
