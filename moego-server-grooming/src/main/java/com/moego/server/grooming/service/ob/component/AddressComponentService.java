package com.moego.server.grooming.service.ob.component;

import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.client.AddressVO;
import com.moego.server.grooming.web.vo.ob.component.AddressComponentVO;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = LandingPageComponentEnum.COMPONENT_ADDRESS)
@RequiredArgsConstructor
public class AddressComponentService implements ILandingPageComponentService {

    private final IBusinessBusinessService businessApi;

    @Override
    public AddressComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        AddressComponentVO vo = new AddressComponentVO();
        InfoIdParams param = new InfoIdParams(landingPageConfig.getBusinessId());
        Optional.ofNullable(businessApi.getBusinessInfoForOB(param)).ifPresent(info -> {
            vo.setAddress(info.getAddress());
            vo.setAddressDetails(new AddressVO()
                    .setAddress1(info.getAddress1())
                    .setAddress2(info.getAddress2())
                    .setCity(info.getAddressCity())
                    .setState(info.getAddressState())
                    .setZipcode(info.getAddressZipcode())
                    .setCountry(info.getAddressCountry())
                    .setLat(info.getAddressLat())
                    .setLng(info.getAddressLng()));
        });
        return vo;
    }
}
