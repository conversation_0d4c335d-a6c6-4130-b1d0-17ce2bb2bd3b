package com.moego.server.grooming.service;

import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.server.grooming.dto.PetMergeRelationDTO;
import com.moego.server.grooming.mapper.MoeGroomingPackageMapper;
import com.moego.server.grooming.mapper.ObConfigClientReviewMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageExample;
import com.moego.server.grooming.mapperbean.ObConfigClientReview;
import com.moego.server.grooming.mapperbean.ObConfigClientReviewExample;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentMergeService {

    private final ObConfigClientReviewMapper obConfigClientReviewMapper;
    private final MoeGroomingPackageMapper groomingPackageMapper;

    public void mergeCustomerObConfigData(CustomerPetMergeRelationDTO mergedCustomerPet) {
        // & ob config client review
        // 修改 ob_config_client_review 修改字段：customer_id、pet_id 索引：business_id
        List<Integer> reviewIdList;
        // customer update
        ObConfigClientReviewExample example = new ObConfigClientReviewExample();
        example.createCriteria()
                .andBusinessIdIn(mergedCustomerPet.getAllLocationIds().stream()
                        .map(Long::intValue)
                        .toList())
                .andCustomerIdIn(mergedCustomerPet.getSourceCustomerIds());

        reviewIdList = obConfigClientReviewMapper.selectByExample(example).stream()
                .map(ObConfigClientReview::getId)
                .toList();
        if (!CollectionUtils.isEmpty(reviewIdList)) {
            log.info("ObConfigClientReview customer update count: {}", reviewIdList.size());
            ObConfigClientReviewExample updateExample = new ObConfigClientReviewExample();
            updateExample
                    .createCriteria()
                    .andIdIn(reviewIdList)
                    .andCustomerIdIn(mergedCustomerPet.getSourceCustomerIds());
            ObConfigClientReview record = new ObConfigClientReview();
            record.setCustomerId(mergedCustomerPet.getTargetCustomerId());
            obConfigClientReviewMapper.updateByExampleSelective(record, updateExample);
        }
        if (CollectionUtils.isEmpty(mergedCustomerPet.getPetMergeRelations())) {
            return;
        }
        // pet update
        for (PetMergeRelationDTO petMergeRelation : mergedCustomerPet.getPetMergeRelations()) {
            // query pet id
            ObConfigClientReviewExample petExample = new ObConfigClientReviewExample();
            petExample
                    .createCriteria()
                    .andBusinessIdIn(mergedCustomerPet.getAllLocationIds().stream()
                            .map(Long::intValue)
                            .toList())
                    .andPetIdIn(petMergeRelation.getSourcePetIds());
            reviewIdList = obConfigClientReviewMapper.selectByExample(petExample).stream()
                    .map(ObConfigClientReview::getId)
                    .toList();
            if (!CollectionUtils.isEmpty(reviewIdList)) {
                log.info("ObConfigClientReview pet update count: {}", reviewIdList.size());
                ObConfigClientReviewExample updateExample = new ObConfigClientReviewExample();
                updateExample.createCriteria().andIdIn(reviewIdList).andPetIdIn(petMergeRelation.getSourcePetIds());
                ObConfigClientReview record = new ObConfigClientReview();
                record.setPetId(petMergeRelation.getTargetPetId());
                obConfigClientReviewMapper.updateByExampleSelective(record, updateExample);
            }
        }
    }

    public void mergeCustomerGroomingPackageData(CustomerPetMergeRelationDTO mergedCustomerPet) {
        // package
        // 修改 moe_grooming_package 修改字段：customer_id 索引：company_id
        // customer update
        List<Integer> packageIdList;
        MoeGroomingPackageExample example = new MoeGroomingPackageExample();
        example.createCriteria()
                .andCompanyIdEqualTo(mergedCustomerPet.getCompanyId())
                .andCustomerIdIn(mergedCustomerPet.getSourceCustomerIds());

        packageIdList = groomingPackageMapper.selectByExample(example).stream()
                .map(MoeGroomingPackage::getId)
                .toList();
        if (!CollectionUtils.isEmpty(packageIdList)) {
            log.info("groomingPackage customer update count: {}", packageIdList.size());
            MoeGroomingPackageExample updateExample = new MoeGroomingPackageExample();
            updateExample
                    .createCriteria()
                    .andIdIn(packageIdList)
                    .andCustomerIdIn(mergedCustomerPet.getSourceCustomerIds());
            MoeGroomingPackage record = new MoeGroomingPackage();
            record.setCustomerId(mergedCustomerPet.getTargetCustomerId());
            groomingPackageMapper.updateByExampleSelective(record, updateExample);
        }
    }
}
