package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_landing_page_gallery
 */
public class MoeBookOnlineLandingPageGallery {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   moe_business.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   Inherited from moe_book_online_gallery.image_path
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.image_path
     *
     * @mbg.generated
     */
    private String imagePath;

    /**
     * Database Column Remarks:
     *   Inherited from moe_book_online_gallery.sort
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     * Database Column Remarks:
     *   0-false, 1-true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.is_deleted
     *
     * @mbg.generated
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_landing_page_gallery.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.id
     *
     * @return the value of moe_book_online_landing_page_gallery.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.id
     *
     * @param id the value for moe_book_online_landing_page_gallery.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.business_id
     *
     * @return the value of moe_book_online_landing_page_gallery.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.business_id
     *
     * @param businessId the value for moe_book_online_landing_page_gallery.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.image_path
     *
     * @return the value of moe_book_online_landing_page_gallery.image_path
     *
     * @mbg.generated
     */
    public String getImagePath() {
        return imagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.image_path
     *
     * @param imagePath the value for moe_book_online_landing_page_gallery.image_path
     *
     * @mbg.generated
     */
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath == null ? null : imagePath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.sort
     *
     * @return the value of moe_book_online_landing_page_gallery.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.sort
     *
     * @param sort the value for moe_book_online_landing_page_gallery.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.is_deleted
     *
     * @return the value of moe_book_online_landing_page_gallery.is_deleted
     *
     * @mbg.generated
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.is_deleted
     *
     * @param isDeleted the value for moe_book_online_landing_page_gallery.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.create_time
     *
     * @return the value of moe_book_online_landing_page_gallery.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.create_time
     *
     * @param createTime the value for moe_book_online_landing_page_gallery.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.update_time
     *
     * @return the value of moe_book_online_landing_page_gallery.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.update_time
     *
     * @param updateTime the value for moe_book_online_landing_page_gallery.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_landing_page_gallery.company_id
     *
     * @return the value of moe_book_online_landing_page_gallery.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_landing_page_gallery.company_id
     *
     * @param companyId the value for moe_book_online_landing_page_gallery.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
