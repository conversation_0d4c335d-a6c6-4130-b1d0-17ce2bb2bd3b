package com.moego.server.grooming.web.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Report Card 数据修复结果 DTO
 */
@Data
public class ReportCardDataFixResultDTO {
    
    /**
     * 修复是否成功
     */
    private boolean success;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 修复开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 修复结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 请求修复的ID列表
     */
    private List<Integer> requestedIds;
    
    /**
     * 总请求数量
     */
    private int totalRequested;
    
    /**
     * 成功修复数量
     */
    private int successCount;
    
    /**
     * 跳过数量（已存在或无需修复）
     */
    private int skippedCount;
    
    /**
     * 失败数量
     */
    private int failedCount;
    
    /**
     * 错误信息列表
     */
    private List<String> errorMessages;
    
    /**
     * 修复耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 详细消息
     */
    private String message;
    
    public ReportCardDataFixResultDTO() {
        this.errorMessages = new ArrayList<>();
        this.startTime = LocalDateTime.now();
    }
    
    /**
     * 添加错误信息
     */
    public void addErrorMessage(String errorMessage) {
        this.errorMessages.add(errorMessage);
    }
    
    /**
     * 完成修复，设置结束时间和耗时
     */
    public void complete() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null && this.endTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalRequested == 0) {
            return 0.0;
        }
        return (double) successCount / totalRequested * 100;
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errorMessages.isEmpty() || failedCount > 0;
    }
    
    /**
     * 获取修复摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("数据修复结果: ").append(success ? "成功" : "失败");
        sb.append(", 类型: ").append(dataType);
        sb.append(", 总数: ").append(totalRequested);
        sb.append(", 成功: ").append(successCount);
        sb.append(", 跳过: ").append(skippedCount);
        sb.append(", 失败: ").append(failedCount);
        sb.append(", 成功率: ").append(String.format("%.2f%%", getSuccessRate()));
        
        if (durationMs != null) {
            sb.append(", 耗时: ").append(durationMs).append("ms");
        }
        
        return sb.toString();
    }
}
