package com.moego.server.grooming.service.statemachine.action;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import java.time.Instant;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CheckInAction implements IStateTransitionAction {

    private final AppointmentServiceBlockingStub appointmentStub;
    private final NewOrderHelper newOrderHelper;
    private final OrderHelper orderHelper;

    @Override
    public boolean suit(AppointmentStatusEnum newStatus) {
        return AppointmentStatusEnum.CHECK_IN.equals(newStatus);
    }

    @Override
    public int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext) {
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())
                || Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.FINISHED.getValue())
                || Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.READY.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not check-in",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        if (Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CHECKED_IN_VALUE)) {
            // already check-in
            return 0;
        }

        // 保留之前的逻辑：where id = xx and status in (unconfirmed, confirmed)
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.UNCONFIRMED_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CONFIRMED_VALUE)) {
            return 0;
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.CHECKED_IN);
        builder.setCheckInTime(Instant.now().getEpochSecond());
        builder.setStatusBeforeCheckin(AppointmentStatus.forNumber(moeGroomingAppointment.getStatus()));
        builder.setUpdateTime(Timestamps.fromDate(new Date()));
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);

        // DONE new order flow
        if (newOrderHelper.isNewOrder(moeGroomingAppointment.getId())) {
            var invoiceId = orderHelper.createInvoiceId(moeGroomingAppointment);
            builder.setOrderId(invoiceId);
        }

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    @Override
    public int revert(MoeGroomingAppointment moeGroomingAppointment) {
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CHECKED_IN_VALUE)
                && !isOldCheckinStatus(moeGroomingAppointment)) {
            return 0;
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());

        if (Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CHECKED_IN_VALUE)) {
            // 这里主要是为了兼容旧数据，可能会是 confirm/unconfirm + checkin 的状态，此时 revert 的话不需要更改 status 字段，只需要将 checkin time 置空即可
            builder.setStatus(AppointmentStatus.forNumber(
                    moeGroomingAppointment.getStatusBeforeCheckin().getValue()));
        }

        builder.setCheckInTime(0L);
        builder.setUpdateTime(Timestamps.fromDate(new Date()));
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setStatusBeforeCheckin(AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED);

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    // 判断是否为旧版本的 checkin 状态（unconfirmed/confirmed with checkin)
    private boolean isOldCheckinStatus(MoeGroomingAppointment moeGroomingAppointment) {
        return Objects.nonNull(moeGroomingAppointment.getCheckInTime())
                && moeGroomingAppointment.getCheckInTime() > 0
                && (AppointmentStatusEnum.CONFIRMED.getValue().equals(moeGroomingAppointment.getStatus())
                        || AppointmentStatusEnum.UNCONFIRMED.getValue().equals(moeGroomingAppointment.getStatus()));
    }
}
