package com.moego.server.grooming.mapper.typehandler;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @since 2024/8/27
 */
public class ServiceOverrideTypeTypeHandler extends BaseTypeHandler<ServiceOverrideType> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ServiceOverrideType parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public ServiceOverrideType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getServiceOverrideType(rs.getInt(columnName), rs.wasNull());
    }

    @Override
    public ServiceOverrideType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getServiceOverrideType(rs.getInt(columnIndex), rs.wasNull());
    }

    @Override
    public ServiceOverrideType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getServiceOverrideType(cs.getInt(columnIndex), cs.wasNull());
    }

    private ServiceOverrideType getServiceOverrideType(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED;
        }

        var type = ServiceOverrideType.forNumber(number);
        return type != null ? type : ServiceOverrideType.UNRECOGNIZED;
    }
}
