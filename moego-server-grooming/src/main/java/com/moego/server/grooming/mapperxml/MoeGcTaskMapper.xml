<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcTaskMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="gc_calendar_id" jdbcType="INTEGER" property="gcCalendarId" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="complete_time" jdbcType="BIGINT" property="completeTime" />
    <result column="complete_count" jdbcType="INTEGER" property="completeCount" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, gc_calendar_id, task_status, complete_time, complete_count, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_gc_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_task (gc_calendar_id, task_status, complete_time,
      complete_count, create_time, update_time
      )
    values (#{gcCalendarId,jdbcType=INTEGER}, #{taskStatus,jdbcType=TINYINT}, #{completeTime,jdbcType=BIGINT},
      #{completeCount,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gcCalendarId != null">
        gc_calendar_id,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="completeCount != null">
        complete_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gcCalendarId != null">
        #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=BIGINT},
      </if>
      <if test="completeCount != null">
        #{completeCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_task
    <set>
      <if test="gcCalendarId != null">
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=BIGINT},
      </if>
      <if test="completeCount != null">
        complete_count = #{completeCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_task
    set gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      task_status = #{taskStatus,jdbcType=TINYINT},
      complete_time = #{completeTime,jdbcType=BIGINT},
      complete_count = #{completeCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByGcCalendarId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_task
        where
        gc_calendar_id = #{gcCalendarId} and
        task_status = 1
        and complete_time = 0
        limit 1
    </select>

    <update id="updateFailTask">
        update moe_gc_task
        set
        task_status = 3,
        complete_time = #{nowTime}
        where task_status = 1
        and complete_time = 0
        and update_time &lt;= #{nowFailTime}
    </update>
</mapper>
