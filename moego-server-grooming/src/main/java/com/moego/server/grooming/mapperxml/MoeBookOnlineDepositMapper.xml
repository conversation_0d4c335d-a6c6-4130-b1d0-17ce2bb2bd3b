<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeBookOnlineDepositMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="payment_id" jdbcType="INTEGER" property="paymentId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="booking_fee" jdbcType="DECIMAL" property="bookingFee" />
    <result column="tips_amount" jdbcType="DECIMAL" property="tipsAmount" />
    <result column="convenience_fee" jdbcType="DECIMAL" property="convenienceFee" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="service_total" jdbcType="DECIMAL" property="serviceTotal" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="service_charge_amount" jdbcType="DECIMAL" property="serviceChargeAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="discount_code_id" jdbcType="BIGINT" property="discountCodeId" />
    <result column="deposit_type" jdbcType="TINYINT" property="depositType" />
    <result column="booking_request_id" jdbcType="BIGINT" property="bookingRequestId" />
    <result column="preauth_info" jdbcType="CHAR" property="preauthInfo" typeHandler="com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.preauthInfoCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.preauthInfoCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, company_id, grooming_id, guid, payment_id, amount, booking_fee, 
    tips_amount, convenience_fee, status, create_time, update_time, service_total, tax_amount, 
    service_charge_amount, discount_amount, discount_code_id, deposit_type, booking_request_id, 
    preauth_info
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDepositExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_book_online_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_book_online_deposit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_deposit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDepositExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_book_online_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_deposit (business_id, company_id, grooming_id, 
      guid, payment_id, amount, 
      booking_fee, tips_amount, convenience_fee, 
      status, create_time, update_time, 
      service_total, tax_amount, service_charge_amount, 
      discount_amount, discount_code_id, deposit_type, 
      booking_request_id, preauth_info
      )
    values (#{businessId,jdbcType=INTEGER}, #{companyId,jdbcType=BIGINT}, #{groomingId,jdbcType=INTEGER}, 
      #{guid,jdbcType=VARCHAR}, #{paymentId,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{bookingFee,jdbcType=DECIMAL}, #{tipsAmount,jdbcType=DECIMAL}, #{convenienceFee,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{serviceTotal,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{serviceChargeAmount,jdbcType=DECIMAL}, 
      #{discountAmount,jdbcType=DECIMAL}, #{discountCodeId,jdbcType=BIGINT}, #{depositType,jdbcType=TINYINT}, 
      #{bookingRequestId,jdbcType=BIGINT}, #{preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_book_online_deposit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="bookingFee != null">
        booking_fee,
      </if>
      <if test="tipsAmount != null">
        tips_amount,
      </if>
      <if test="convenienceFee != null">
        convenience_fee,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="serviceTotal != null">
        service_total,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="serviceChargeAmount != null">
        service_charge_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="discountCodeId != null">
        discount_code_id,
      </if>
      <if test="depositType != null">
        deposit_type,
      </if>
      <if test="bookingRequestId != null">
        booking_request_id,
      </if>
      <if test="preauthInfo != null">
        preauth_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="bookingFee != null">
        #{bookingFee,jdbcType=DECIMAL},
      </if>
      <if test="tipsAmount != null">
        #{tipsAmount,jdbcType=DECIMAL},
      </if>
      <if test="convenienceFee != null">
        #{convenienceFee,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceTotal != null">
        #{serviceTotal,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceChargeAmount != null">
        #{serviceChargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountCodeId != null">
        #{discountCodeId,jdbcType=BIGINT},
      </if>
      <if test="depositType != null">
        #{depositType,jdbcType=TINYINT},
      </if>
      <if test="bookingRequestId != null">
        #{bookingRequestId,jdbcType=BIGINT},
      </if>
      <if test="preauthInfo != null">
        #{preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDepositExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_book_online_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_deposit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.groomingId != null">
        grooming_id = #{record.groomingId,jdbcType=INTEGER},
      </if>
      <if test="record.guid != null">
        guid = #{record.guid,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.bookingFee != null">
        booking_fee = #{record.bookingFee,jdbcType=DECIMAL},
      </if>
      <if test="record.tipsAmount != null">
        tips_amount = #{record.tipsAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.convenienceFee != null">
        convenience_fee = #{record.convenienceFee,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.serviceTotal != null">
        service_total = #{record.serviceTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceChargeAmount != null">
        service_charge_amount = #{record.serviceChargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.discountAmount != null">
        discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.discountCodeId != null">
        discount_code_id = #{record.discountCodeId,jdbcType=BIGINT},
      </if>
      <if test="record.depositType != null">
        deposit_type = #{record.depositType,jdbcType=TINYINT},
      </if>
      <if test="record.bookingRequestId != null">
        booking_request_id = #{record.bookingRequestId,jdbcType=BIGINT},
      </if>
      <if test="record.preauthInfo != null">
        preauth_info = #{record.preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_deposit
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=BIGINT},
      grooming_id = #{record.groomingId,jdbcType=INTEGER},
      guid = #{record.guid,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=DECIMAL},
      booking_fee = #{record.bookingFee,jdbcType=DECIMAL},
      tips_amount = #{record.tipsAmount,jdbcType=DECIMAL},
      convenience_fee = #{record.convenienceFee,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      service_total = #{record.serviceTotal,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      service_charge_amount = #{record.serviceChargeAmount,jdbcType=DECIMAL},
      discount_amount = #{record.discountAmount,jdbcType=DECIMAL},
      discount_code_id = #{record.discountCodeId,jdbcType=BIGINT},
      deposit_type = #{record.depositType,jdbcType=TINYINT},
      booking_request_id = #{record.bookingRequestId,jdbcType=BIGINT},
      preauth_info = #{record.preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_deposit
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="bookingFee != null">
        booking_fee = #{bookingFee,jdbcType=DECIMAL},
      </if>
      <if test="tipsAmount != null">
        tips_amount = #{tipsAmount,jdbcType=DECIMAL},
      </if>
      <if test="convenienceFee != null">
        convenience_fee = #{convenienceFee,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceTotal != null">
        service_total = #{serviceTotal,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceChargeAmount != null">
        service_charge_amount = #{serviceChargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="discountCodeId != null">
        discount_code_id = #{discountCodeId,jdbcType=BIGINT},
      </if>
      <if test="depositType != null">
        deposit_type = #{depositType,jdbcType=TINYINT},
      </if>
      <if test="bookingRequestId != null">
        booking_request_id = #{bookingRequestId,jdbcType=BIGINT},
      </if>
      <if test="preauthInfo != null">
        preauth_info = #{preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_book_online_deposit
    set business_id = #{businessId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      guid = #{guid,jdbcType=VARCHAR},
      payment_id = #{paymentId,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      booking_fee = #{bookingFee,jdbcType=DECIMAL},
      tips_amount = #{tipsAmount,jdbcType=DECIMAL},
      convenience_fee = #{convenienceFee,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      service_total = #{serviceTotal,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      service_charge_amount = #{serviceChargeAmount,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      discount_code_id = #{discountCodeId,jdbcType=BIGINT},
      deposit_type = #{depositType,jdbcType=TINYINT},
      booking_request_id = #{bookingRequestId,jdbcType=BIGINT},
      preauth_info = #{preauthInfo,jdbcType=CHAR,typeHandler=com.moego.server.grooming.mapper.typehandler.BookOnlineDepositPreAuthTypeHandler}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where guid = #{guid,jdbcType=VARCHAR}
    limit 1
  </select>

  <select id="selectByPaymentId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where business_id = #{businessId,jdbcType=INTEGER}
    and payment_id = #{paymentId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="selectByPaymentIdsAndDepositType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where business_id = #{businessId,jdbcType=INTEGER}
      and deposit_type = #{depositType,jdbcType=TINYINT}
    and payment_id in
    <foreach close=")" collection="paymentIds" item="item" open="(" separator=",">
        #{item}
    </foreach>
  </select>
  <select id="selectByPaymentIdsV2" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where company_id = #{companyId,jdbcType=BIGINT}
    and payment_id in
    <foreach close=")" collection="paymentIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByGroomingId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where business_id = #{businessId,jdbcType=INTEGER}
    and grooming_id = #{groomingId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="selectByGroomingIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where business_id = #{businessId}
    and grooming_id in
    <if test="groomingIds != null">
      <foreach close=")" collection="groomingIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>
<!-- groomingIds must not be empty, otherwise it will return a sql error, check it before calling this method  -->
  <select id="selectByGroomingIdsV2" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_book_online_deposit
    where grooming_id in
      <foreach close=")" collection="groomingIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
  </select>
  <select id="countPrepaidRevenue" resultType="bigDecimal">
    SELECT
        SUM(amount)
    FROM moe_book_online_deposit
    WHERE business_id = #{businessId}
    AND create_time &gt;= FROM_UNIXTIME(#{startTime})
    AND create_time &lt;= FROM_UNIXTIME(#{endTime})
    AND status IN (4, 6)
  </select>

  <update id="updateDiscountCodeId" parameterType="map">
    <foreach collection="discountCodeIdMap.entrySet()" index="key" item="value" separator=";">
      update moe_book_online_deposit
      set discount_code_id = #{value}
      where discount_code_id = #{key}
    </foreach>
  </update>
</mapper>