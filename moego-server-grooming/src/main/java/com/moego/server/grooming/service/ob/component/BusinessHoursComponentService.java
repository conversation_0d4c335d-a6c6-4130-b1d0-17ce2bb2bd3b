package com.moego.server.grooming.service.ob.component;

import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessWorkingHourClient;
import com.moego.server.business.dto.BusinessClosedDateDTO;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import com.moego.server.grooming.web.vo.ob.component.BusinessHoursComponentVO;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Service(value = LandingPageComponentEnum.COMPONENT_BUSINESS_HOURS)
@AllArgsConstructor
public class BusinessHoursComponentService implements ILandingPageComponentService {

    private final IBusinessWorkingHourClient businessWorkingHourClient;

    private final IBusinessClosedDateClient businessClosedDateClient;

    @Override
    public BaseComponentVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        BusinessWorkingHourDetailDTO businessWorkingHourDetailDTO =
                businessWorkingHourClient.getBusinessWorkingHour(landingPageConfig.getBusinessId());
        List<BusinessClosedDateDTO> closedDateDTOList =
                businessClosedDateClient.getThisWeekClosedDate(landingPageConfig.getBusinessId());
        return new BusinessHoursComponentVO()
                .setWorkingHours(businessWorkingHourDetailDTO.getTimeData())
                .setClosedDateList(closedDateDTOList);
    }
}
