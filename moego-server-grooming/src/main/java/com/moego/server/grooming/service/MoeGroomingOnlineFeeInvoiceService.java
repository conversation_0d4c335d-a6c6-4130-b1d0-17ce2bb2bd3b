package com.moego.server.grooming.service;

import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.mapper.MoeGroomingOnlineFeeInvoiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class MoeGroomingOnlineFeeInvoiceService {

    @Autowired
    private MoeGroomingOnlineFeeInvoiceMapper moeGroomingOnlineFeeInvoiceMapper;

    @Autowired
    private IBusinessBusinessClient businessBusinessClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private TransactionOperations transactionOperations;

    public void addOrUpdateOnlineFeeRecord(
            Integer businessId, Integer invoiceId, Byte type, Boolean requiredProcessingFee) {
        MoeGroomingOnlineFeeInvoice onlineFeeInvoice = new MoeGroomingOnlineFeeInvoice();
        onlineFeeInvoice.setBusinessId(businessId);
        onlineFeeInvoice.setInvoiceId(invoiceId);
        onlineFeeInvoice.setType(type);
        onlineFeeInvoice.setRequiredFee(
                Boolean.TRUE.equals(requiredProcessingFee) ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        insertOrUpdateOnlineFeeRecord(onlineFeeInvoice);
    }

    private void insertOrUpdateOnlineFeeRecord(MoeGroomingOnlineFeeInvoice onlineFeeInvoice) {
        MoeGroomingOnlineFeeInvoice record = selectOnlineFeeRecord(
                onlineFeeInvoice.getBusinessId(), onlineFeeInvoice.getInvoiceId(), onlineFeeInvoice.getType());
        if (record != null) {
            record.setRequiredFee(onlineFeeInvoice.getRequiredFee());
            moeGroomingOnlineFeeInvoiceMapper.updateByPrimaryKeySelective(record);
        } else {
            record = new MoeGroomingOnlineFeeInvoice();
            BeanUtils.copyProperties(onlineFeeInvoice, record);
            record.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(onlineFeeInvoice.getBusinessId()));
            moeGroomingOnlineFeeInvoiceMapper.insertSelective(record);
        }
    }

    public MoeGroomingOnlineFeeInvoice selectOnlineFeeRecord(Integer businessId, Integer invoiceId, Byte type) {
        return moeGroomingOnlineFeeInvoiceMapper.selectByInvoiceIdAndType(businessId, invoiceId, type);
    }

    public List<MoeGroomingOnlineFeeInvoice> selectByInvoiceIdList(List<Long> invoiceIdList) {
        if (CollectionUtils.isEmpty(invoiceIdList)) {
            return List.of();
        }
        return moeGroomingOnlineFeeInvoiceMapper.selectByInvoiceIdList(
                invoiceIdList, InvoiceStatusEnum.TYPE_ONLINE_INVOICE);
    }

    public void batchSaveOnlineFeeInvoice(Long businessId, List<Long> invoiceIdList, boolean requiredCvf) {
        if (CollectionUtils.isEmpty(invoiceIdList)) {
            return;
        }
        List<MoeGroomingOnlineFeeInvoice> feeInvoices = selectByInvoiceIdList(invoiceIdList);

        Byte requiredFee = Boolean.TRUE.equals(requiredCvf) ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE;
        List<Long> needAddInvoiceIdList = new ArrayList<>(invoiceIdList);
        List<Long> needUpdateInvoiceIdList = feeInvoices.stream()
                .map(item -> Long.valueOf(item.getInvoiceId()))
                .toList();
        transactionOperations.executeWithoutResult(status -> {
            if (!CollectionUtils.isEmpty(needUpdateInvoiceIdList)) {
                moeGroomingOnlineFeeInvoiceMapper.batchUpdateRequiredFeeByInvoiceIdList(
                        needUpdateInvoiceIdList, requiredFee, InvoiceStatusEnum.TYPE_ONLINE_INVOICE);
                needAddInvoiceIdList.removeAll(needUpdateInvoiceIdList);
            }

            if (!CollectionUtils.isEmpty(needAddInvoiceIdList)) {
                Long companyId = businessInfoHelper.getCompanyIdByBusinessId(Math.toIntExact(businessId));
                List<MoeGroomingOnlineFeeInvoice> addList = needAddInvoiceIdList.stream()
                        .map(invoiceId -> {
                            MoeGroomingOnlineFeeInvoice onlineFeeInvoice = new MoeGroomingOnlineFeeInvoice();
                            onlineFeeInvoice.setBusinessId(Math.toIntExact(businessId));
                            onlineFeeInvoice.setInvoiceId(Math.toIntExact(invoiceId));
                            onlineFeeInvoice.setType(InvoiceStatusEnum.TYPE_ONLINE_INVOICE);
                            onlineFeeInvoice.setRequiredFee(requiredFee);
                            onlineFeeInvoice.setCompanyId(companyId);
                            return onlineFeeInvoice;
                        })
                        .toList();
                moeGroomingOnlineFeeInvoiceMapper.batchInsert(addList);
            }
        });
    }
}
