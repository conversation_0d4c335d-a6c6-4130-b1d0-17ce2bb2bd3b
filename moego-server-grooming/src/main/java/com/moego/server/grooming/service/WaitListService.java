package com.moego.server.grooming.service;

import static com.moego.common.utils.PermissionUtil.CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static com.moego.common.utils.WeekUtil.getDayOfWeek;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.convertTimeSlot;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.filterNonWorkingTime;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.timeRangeListIntersection;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.LocationParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.waitlist.Staff;
import com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO;
import com.moego.server.grooming.dto.waitlist.WaitlistAvailableDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.helper.BusinessCustomerServiceHelper;
import com.moego.server.grooming.mapper.MoeWaitListMapper;
import com.moego.server.grooming.mapper.po.DatePreferencePO;
import com.moego.server.grooming.mapper.po.StaffPreferencePO;
import com.moego.server.grooming.mapper.po.TimePreferencePO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.mapstruct.DatePreferenceMapper;
import com.moego.server.grooming.mapstruct.PrepayMapper;
import com.moego.server.grooming.mapstruct.StaffPreferenceMapper;
import com.moego.server.grooming.mapstruct.TimePreferenceMapper;
import com.moego.server.grooming.params.appointment.EditAppointmentPetDetailsParams;
import com.moego.server.grooming.params.appointment.PetParams;
import com.moego.server.grooming.params.appointment.ServiceAndOperationParams;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.grooming.service.params.BatchSSParams;
import com.moego.server.grooming.service.params.GetAvailableSlotParams;
import com.moego.server.grooming.service.params.GetFreeSlotParams;
import com.moego.server.grooming.service.storage.AppointmentStorageService;
import com.moego.server.grooming.web.params.waitlist.AddWaitListParam;
import com.moego.server.grooming.web.params.waitlist.CalendarViewParam;
import com.moego.server.grooming.web.params.waitlist.FilterParams;
import com.moego.server.grooming.web.params.waitlist.FromAppointmentParam;
import com.moego.server.grooming.web.params.waitlist.GetWaitListParam;
import com.moego.server.grooming.web.params.waitlist.OrderEnum;
import com.moego.server.grooming.web.params.waitlist.PropertyEnum;
import com.moego.server.grooming.web.params.waitlist.SmartScheduleParam;
import com.moego.server.grooming.web.params.waitlist.SortParams;
import com.moego.server.grooming.web.params.waitlist.UpdateWaitListParam;
import com.moego.server.grooming.web.params.waitlist.WaitListPetServiceParam;
import com.moego.server.grooming.web.params.waitlist.WaitListServiceAndOperationParams;
import com.moego.server.grooming.web.vo.waitlist.AvailableInfoVO;
import com.moego.server.grooming.web.vo.waitlist.CalendarViewVO;
import com.moego.server.grooming.web.vo.waitlist.CustomerVO;
import com.moego.server.grooming.web.vo.waitlist.PetServiceVO;
import com.moego.server.grooming.web.vo.waitlist.ServiceAndOperationVO;
import com.moego.server.grooming.web.vo.waitlist.SmartScheduleListVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListDetailVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListListVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListSmartScheduleVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListVO;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class WaitListService {
    @Autowired
    private MoeWaitListMapper moeWaitListMapper;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private AppointmentServiceV2 appointmentServiceV2;

    @Autowired
    private AppointmentStorageService appointmentStorageService;

    @Autowired
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private SmartScheduleV3Service smartScheduleService;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private GoogleMapService googleMapService;

    @Autowired
    private ICustomerCustomerService iCustomerCustomerService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private IBusinessStaffService businessStaffService;

    @Autowired
    private BusinessCustomerServiceHelper businessCustomerServiceHelper;

    @Autowired
    private ActiveMQService mqService;

    public static final int DEFAULT_VALID_TILL = 1; // 默认为创建时间后一年
    public static final double DEFAULT_DRIVER_TIME_SCALE_FACTOR = 1.8;
    public static final String REPORT_TEXT_FOR_ANYONE = "Anyone";
    public static final String REPORT_TEXT_FOR_ANYDATE = "ASAP";
    public static final String REPORT_TEXT_FOR_ANYTIME = "Anytime";

    @Autowired
    private ActiveMQService activeMQService;

    @Autowired
    private BrandedAppNotificationService brandedAppNotificationService;

    /**
     * waitList 支持仅有 pet, 没有 service 的情况; 不支持 service operation。
     * 创建 waitList 不会创建订单信息
     */
    public void addWaitList(AddWaitListParam param) {
        String tz = getBusinessTimeZone(param.getBusinessId());
        Long now = CommonUtil.get10Timestamp();

        // 构建 MoeGroomingAppointment
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setCompanyId(param.getCompanyId());
        moeGroomingAppointment.setBusinessId(param.getBusinessId().intValue());
        moeGroomingAppointment.setCustomerId(param.getCustomerId());
        moeGroomingAppointment.setOrderId(CommonUtil.getUuid());
        moeGroomingAppointment.setAppointmentDate("");
        moeGroomingAppointment.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        moeGroomingAppointment.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
        moeGroomingAppointment.setWaitListStatus(WaitListStatusEnum.WAITLISTONLY);
        moeGroomingAppointment.setMoveWaitingBy(param.getTokenStaffId().intValue());
        moeGroomingAppointment.setCreatedById(param.getTokenStaffId().intValue());
        moeGroomingAppointment.setCreateTime(now);
        moeGroomingAppointment.setUpdateTime(now);

        // 构建 MoeGroomingPetDetail
        List<PetParams> petParams = waitListPetParams2PetParams(param.getPetServices());
        List<MoeGroomingPetDetail> petDetails = moePetDetailService.buildPetDetailList(
                petParams, param.getBusinessId().intValue(), "", 0, param.getAllPetsStartAtSameTime());
        petDetails.addAll(moePetDetailService.buildPetDetailWithoutService(petParams));

        // 插入数据库
        appointmentStorageService.insertAppointment(moeGroomingAppointment, petDetails, Collections.emptyMap());

        // 插入 MoeGroomingNote
        if (!Strings.isEmpty(param.getTicketComment())) {
            MoeGroomingNote moeGroomingNote = moeGroomingNoteService.buildGroomingComment(
                    param.getCompanyId(),
                    param.getBusinessId().intValue(),
                    param.getTokenStaffId().intValue(),
                    moeGroomingAppointment.getId(),
                    param.getCustomerId(),
                    param.getTicketComment(),
                    now);
            moeGroomingNoteService.batchInsert(List.of(moeGroomingNote));
        }

        // 插入 MoeWaitList
        addMoeWaitList(
                param.getBusinessId(),
                param.getCompanyId(),
                tz,
                moeGroomingAppointment.getId().longValue(),
                DatePreferenceMapper.INSTANCE.toEntity(param.getDatePreference()),
                TimePreferenceMapper.INSTANCE.toEntity(param.getTimePreference()),
                StaffPreferenceMapper.INSTANCE.toEntity(param.getStaffPreference()),
                param.getValidFrom(),
                param.getValidTill(),
                param.getTokenStaffId());
    }

    /**
     * 适用于 ob request 或 appointment 转 waitList
     * 1. ob request 转 waitList 时，原 ob request 消失，且支持编辑 petService、ticketComment，不支持 service operation
     * 2. appointment 转 waitList 时，支持 appointment 保留。若原 appt 是 repeat appointment，添加 waitList 后，转为普通 appt
     */
    public void addFromAppointment(FromAppointmentParam param) {
        String tz = getBusinessTimeZone(param.getBusinessId());
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentService.getAppointmentById(
                param.getBusinessId().intValue(), param.getAppointmentId().intValue());
        if (moeGroomingAppointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }

        boolean isOb =
                moeGroomingAppointment.getBookOnlineStatus().equals(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB);
        // 兼容历史逻辑。ob request 默认删除原 ob request；appointment 默认保留原 appointment
        if (param.getDeleteOriginalAppointment() == null) {
            param.setDeleteOriginalAppointment(isOb);
        }
        AppointmentStatusEnum appointmentStatus = AppointmentStatusEnum.fromValue(moeGroomingAppointment.getStatus());
        if (!AppointmentStatusEnum.UNCONFIRMED.equals(appointmentStatus)
                && !AppointmentStatusEnum.CONFIRMED.equals(appointmentStatus)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "appointment status is not valid, status: " + appointmentStatus);
        }
        if (moeGroomingAppointment.getWaitListStatus().equals(WaitListStatusEnum.APPTANDWAITLIST)
                || moeGroomingAppointment.getWaitListStatus().equals(WaitListStatusEnum.WAITLISTONLY)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment has been added to wait list");
        }

        // 更新 petService
        if (param.getPetList() != null) {
            appointmentServiceV2.editAppointmentPetAndService(new EditAppointmentPetDetailsParams(
                    param.getBusinessId().intValue(),
                    param.getTokenStaffId().intValue(),
                    param.getAppointmentId(),
                    param.getPetList(),
                    param.getAllPetsStartAtSameTime(),
                    1));
        }

        // 更新 comment
        if (param.getTicketComment() != null) {
            MoeGroomingNote moeGroomingNote = moeGroomingNoteService.buildGroomingComment(
                    param.getCompanyId(),
                    param.getBusinessId().intValue(),
                    param.getTokenStaffId().intValue(),
                    moeGroomingAppointment.getId(),
                    moeGroomingAppointment.getCustomerId(),
                    param.getTicketComment(),
                    CommonUtil.get10Timestamp());
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }

        // 插入 MoeWaitList
        addMoeWaitList(
                param.getBusinessId(),
                param.getCompanyId(),
                tz,
                moeGroomingAppointment.getId().longValue(),
                DatePreferenceMapper.INSTANCE.toEntity(param.getDatePreference()),
                TimePreferenceMapper.INSTANCE.toEntity(param.getTimePreference()),
                StaffPreferenceMapper.INSTANCE.toEntity(param.getStaffPreference()),
                param.getValidFrom(),
                param.getValidTill(),
                param.getTokenStaffId());

        // 更新 appointment
        MoeGroomingAppointment apptUpdate = new MoeGroomingAppointment();
        apptUpdate.setId(moeGroomingAppointment.getId());
        apptUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        apptUpdate.setUpdatedById(param.getTokenStaffId());
        // 移除repeat
        apptUpdate.setRepeatId(0);

        apptUpdate.setWaitListStatus(WaitListStatusEnum.APPTANDWAITLIST);
        if (param.getDeleteOriginalAppointment()) {
            apptUpdate.setWaitListStatus(WaitListStatusEnum.WAITLISTONLY);
            apptUpdate.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
        }
        appointmentServiceV2.editAppointment(apptUpdate);
        if (param.getDeleteOriginalAppointment()) {
            appointmentServiceV2.onAppointmentCanceled(moeGroomingAppointment);
        }
        if (isOb) {
            mqService.publishBookingRequestEvent(new BookingRequestEventParams()
                    .setBusinessId(moeGroomingAppointment.getBusinessId())
                    .setAppointmentId(moeGroomingAppointment.getId())
                    .setEvent(BookingRequestEventParams.BookingRequestEvent.MOVED_TO_WAIT_LIST));
        }
    }

    /**
     * 支持编辑 waitList 的基本信息。
     * 仅在没有关联的 appointment(WaitListStatusEnum.WAITLISTONLY) 时，允许更新 ticketComment、petService，不支持 service operation
     */
    public void updateWaitList(UpdateWaitListParam param) {
        String tz = getBusinessTimeZone(param.getBusinessId());
        MoeWaitList existWaitList = moeWaitListMapper.selectById(param.getBusinessId(), param.getId());
        if (existWaitList == null) {
            return;
        }

        MoeWaitList waitingList = new MoeWaitList();
        waitingList.setId(param.getId());
        waitingList.setBusinessId(param.getBusinessId());
        waitingList.setDatePreference(DatePreferenceMapper.INSTANCE.toEntity(param.getDatePreference()));
        waitingList.setTimePreference(TimePreferenceMapper.INSTANCE.toEntity(param.getTimePreference()));
        waitingList.setStaffPreference(StaffPreferenceMapper.INSTANCE.toEntity(param.getStaffPreference()));
        waitingList.setUpdatedAt(LocalDateTime.now(ZoneId.of(tz)));
        waitingList.setUpdatedBy(param.getTokenStaffId());
        waitingList.setValidFrom(param.getValidFrom());
        waitingList.setValidTill(param.getValidTill());
        moeWaitListMapper.updateById(waitingList);
        // 仅有 waitList，没有关联的 appointment 时，允许更新 ticketComment、petService
        if (param.getTicketComment() != null || param.getPetServices() != null) {
            MoeGroomingAppointment moeGroomingAppointment = getAppointment(existWaitList);
            if (moeGroomingAppointment == null) {
                throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "appointment not exist");
            }
            if (!WaitListStatusEnum.WAITLISTONLY.equals(moeGroomingAppointment.getWaitListStatus())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "wait list status is not valid");
            }

            // 更新 petService
            if (param.getPetServices() != null) {
                moePetDetailService.deleteByAppointId(moeGroomingAppointment.getId());
                List<PetParams> petParams = waitListPetParams2PetParams(param.getPetServices());
                List<MoeGroomingPetDetail> petDetails = moePetDetailService.buildPetDetailList(
                        petParams,
                        param.getBusinessId().intValue(),
                        moeGroomingAppointment.getAppointmentDate(),
                        0,
                        param.getAllPetsStartAtSameTime());
                petDetails.addAll(moePetDetailService.buildPetDetailWithoutService(petParams));
                moePetDetailService.addPetDetails(moeGroomingAppointment, petDetails);
            }

            // 更新 comment
            if (param.getTicketComment() != null) {
                MoeGroomingNote moeGroomingNote = moeGroomingNoteService.buildGroomingComment(
                        param.getCompanyId(),
                        param.getBusinessId().intValue(),
                        param.getTokenStaffId().intValue(),
                        moeGroomingAppointment.getId(),
                        moeGroomingAppointment.getCustomerId(),
                        param.getTicketComment(),
                        CommonUtil.get10Timestamp());
                moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
            }
        }
    }

    /**
     * 业务删除 waitList。
     * 1. 在没有关联的 appointment(WaitListStatusEnum.WAITLISTONLY) 时，需要同时删除主表 MoeGroomingAppointment。
     *    由于 waitList 的来源可能是 ob request，所以需要判断是否需要退款
     * 2. 否则仅删除 MoeWaitList
     */
    public void deleteWaitList(Long businessId, Long id) {
        String tz = getBusinessTimeZone(businessId);
        MoeWaitList moeWaitList = moeWaitListMapper.selectById(businessId, id);
        if (moeWaitList == null) {
            return;
        }
        MoeGroomingAppointment appointment = getAppointment(moeWaitList);
        switch (appointment.getWaitListStatus()) {
            case WAITLISTONLY -> batchDeletePureWaitList(businessId, tz, List.of(appointment.getId()));
            case APPTANDWAITLIST -> {
                deleteMoeWaitList(businessId, id, tz);
                MoeGroomingAppointment apptUpdate = new MoeGroomingAppointment();
                apptUpdate.setId(moeWaitList.getAppointmentId().intValue());
                apptUpdate.setWaitListStatus(WaitListStatusEnum.APPTONLY);
                appointmentServiceV2.editAppointment(apptUpdate);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid wait list status");
        }
        if (Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
            mqService.publishBookingRequestEvent(new BookingRequestEventParams()
                    .setBusinessId(appointment.getBusinessId())
                    .setAppointmentId(appointment.getId())
                    .setEvent(BookingRequestEventParams.BookingRequestEvent.DELETED));
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST);
        }
    }

    public WaitListDetailVO waitListDetail(Long businessId, Integer tokenStaffId, Long id) {
        MoeWaitList moeWaitList = moeWaitListMapper.selectById(businessId, id);
        if (moeWaitList == null) {
            return null;
        }

        MoeGroomingNote moeGroomingNote = getTicketComment(moeWaitList);
        MoeGroomingAppointment appointment = moeGroomingAppointmentService.getAppointmentById(
                businessId.intValue(), moeWaitList.getAppointmentId().intValue());
        GroomingCalenderCustomerInfo customerInfo = appointmentServiceV2.getCustomerInfo(tokenStaffId, appointment);
        // get more customer info
        boolean hasUpdateInfo = false;
        if (customerInfo != null) {
            var customerHasUpdateInfo = businessCustomerServiceHelper.listCustomerHasRequestUpdate(
                    businessId.intValue(), List.of(customerInfo.getCustomerId().longValue()));
            var hasRequestDTO =
                    customerHasUpdateInfo.get(customerInfo.getCustomerId().longValue());
            hasUpdateInfo = hasRequestDTO != null && hasRequestDTO.hasRequestUpdate();
        }

        // service 信息
        Map<Integer, List<MoeGroomingPetDetail>> petMoeDetailMap = getPetService(appointment);

        // 查询宠物的基础信息
        Map<Integer, CustomerPetDetailDTO> petDetailMap = moePetDetailService.getCustomerPetDetail(
                petMoeDetailMap.values().stream().flatMap(Collection::stream).toList());
        Map<Integer, MoeStaffDto> staffInfo = getStaffInfo(businessId, List.of(moeWaitList));

        WaitListDetailVO waitListDetailDTO = new WaitListDetailVO();
        waitListDetailDTO.setId(moeWaitList.getId());
        waitListDetailDTO.setDatePreference(DatePreferenceMapper.INSTANCE.toDto(moeWaitList.getDatePreference()));
        waitListDetailDTO.setTimePreference(TimePreferenceMapper.INSTANCE.toDto(moeWaitList.getTimePreference()));
        waitListDetailDTO.setStaffPreference(staffPreferencePO2DTO(moeWaitList.getStaffPreference(), staffInfo));
        waitListDetailDTO.setValidTill(moeWaitList.getValidTill());
        waitListDetailDTO.setCreateTime(moeWaitList.getCreatedAt());
        waitListDetailDTO.setCreateBy(
                getStaffDto(staffInfo.get(moeWaitList.getCreatedBy().intValue())));
        waitListDetailDTO.setTicketComment(moeGroomingNote == null ? "" : moeGroomingNote.getNote());
        waitListDetailDTO.setCertainAreaList(getWaitListServiceAreas(businessId, customerInfo));
        CustomerVO customerVO = new CustomerVO();
        customerVO.setId(appointment.getCustomerId());
        if (customerInfo != null) {
            customerVO.setFirstName(customerInfo.getCustomerFirstName());
            customerVO.setLastName(customerInfo.getCustomerLastName());
            customerVO.setPhoneNumber(customerInfo.getClientPhoneNumber());
            customerVO.setClientColor(customerInfo.getClientColor());
            customerVO.setAvatarPath(customerInfo.getAvatarPath());
            customerVO.setIsDeleted(!DeleteStatusEnum.STATUS_NORMAL.equals(customerInfo.getStatus()));
        }
        waitListDetailDTO.setCustomerInfo(customerVO);
        // petService 信息
        if (isAllPetServiceValid(petMoeDetailMap)) {
            waitListDetailDTO.setDuration(moePetDetailService.calculateServiceDuration(petMoeDetailMap.values().stream()
                    .flatMap(Collection::stream)
                    .toList()));
        }
        waitListDetailDTO.setAllPetsStartAtSameTime(moePetDetailService.isAllPetsStartAtSameTimeV2(petMoeDetailMap));
        Map<Integer, List<GroomingPetServiceDTO>> petServiceMap =
                moePetDetailService.getPetServiceDTOs(businessId.intValue(), petMoeDetailMap);
        List<GroomingPetInfoDetailDTO> petList = petServiceMap.entrySet().stream()
                .map(k -> {
                    Integer petId = k.getKey();
                    GroomingPetInfoDetailDTO dto = new GroomingPetInfoDetailDTO();
                    dto.setPetId(petId);
                    dto.setGroomingPetServiceDTOS(k.getValue());
                    CustomerPetDetailDTO petDetailDTO = petDetailMap.get(petId);
                    if (petDetailDTO != null) {
                        dto.setPetName(petDetailDTO.getPetName());
                        dto.setPetTypeId(petDetailDTO.getPetTypeId());
                        dto.setStatus(petDetailDTO.getStatus());
                        dto.setLifeStatus(petDetailDTO.getLifeStatus());
                        dto.setAvatarPath(petDetailDTO.getAvatarPath());
                    }
                    return dto;
                })
                .toList();
        waitListDetailDTO.setPetList(petList);

        waitListDetailDTO.setIsPaid(appointment.getIsPaid());
        if (WaitListStatusEnum.APPTANDWAITLIST.equals(appointment.getWaitListStatus())) {
            waitListDetailDTO.setAppointmentId(moeWaitList.getAppointmentId());
        }
        // 增加查询支付相关金额
        MoeGroomingInvoice invoice =
                invoiceService.queryInvoiceByGroomingId(businessId.intValue(), appointment.getId());
        waitListDetailDTO.setPaidAmount(invoice.getPaidAmount());
        waitListDetailDTO.setRefundAmount(invoice.getRefundedAmount());
        OBPrepayDetailDTO prepayDetail = appointmentServiceV2.getPrepayDetail(appointment);
        waitListDetailDTO.setPrepay(PrepayMapper.INSTANCE.dto2vo(prepayDetail));
        waitListDetailDTO.setHasRequestUpdate(hasUpdateInfo);
        return waitListDetailDTO;
    }

    public WaitlistAvailableDTO queryGroomingWaitlistAvailable(
            long companyId, long businessId, int pageNum, int pageSize) {
        var ownerStaffId = iBusinessStaffClient.getOwnerStaffId((int) businessId);
        var listVO = getWaitList(GetWaitListParam.builder()
                .businessId(businessId)
                .tokenStaffId(ownerStaffId.longValue())
                .companyId(companyId)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .filters(FilterParams.builder()
                        .isOnGoing(true)
                        .availableStartDateTime(LocalDateTime.now())
                        .availableEndDateTime(LocalDateTime.now().plusMonths(1))
                        .build())
                .sort(new SortParams(PropertyEnum.create_time, OrderEnum.desc))
                .build());
        var budiler = WaitlistAvailableDTO.builder();
        if (CollectionUtils.isEmpty(listVO.list())) {
            return budiler.build();
        }
        budiler.waitlistAvailableMap(
                listVO.list().stream().collect(Collectors.toMap(WaitListVO::getId, WaitListVO::getIsAvailable)));
        budiler.pagination(listVO.page());
        return budiler.build();
    }

    public WaitListListVO getWaitList(GetWaitListParam param) {
        Long businessId = param.businessId();
        LocalDateTime now = LocalDateTime.now(ZoneId.of(getBusinessTimeZone(businessId)));
        LocalDate validTillGte = null, validTillLt = null;
        if (param.filters().isOnGoing() != null) {
            if (param.filters().isOnGoing()) {
                validTillGte = now.toLocalDate();
            } else {
                validTillLt = now.toLocalDate();
            }
        }
        List<MoeWaitList> records =
                moeWaitListMapper.query(param.companyId(), businessId, validTillGte, validTillLt, null, null, null);

        // 过滤该 staff 可见的 waitList
        List<Integer> allStaffIdList = getStaffShowOnCalendar(businessId, param.tokenStaffId());
        if (!CollectionUtils.isEmpty(param.filters().staffIdList())) {
            allStaffIdList.retainAll(param.filters().staffIdList());
        }
        List<Integer> staffIdListDeleted = getStaffDeleted(businessId);
        records = records.stream()
                .filter(k -> checkStaff(k.getStaffPreference(), allStaffIdList, staffIdListDeleted))
                .filter(k -> checkDate(
                        k.getDatePreference(),
                        param.filters().isAnyDate(),
                        param.filters().dateList()))
                .filter(k -> checkTime(
                        k.getTimePreference(),
                        param.filters().isAnyTime(),
                        param.filters().timeRangeList()))
                .toList();

        Map<Long, MoeGroomingAppointment> waitListAppointment = batchGetAppointment(businessId, records);
        Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo =
                batchGetCustomerInfo(param.tokenStaffId(), waitListAppointment);

        List<Long> allCustomerIds = waitListCustomerInfo.values().stream()
                .filter(Objects::nonNull)
                .map(GroomingCalenderCustomerInfo::getCustomerId)
                .map(Integer::longValue)
                .distinct()
                .toList();

        Map<Long, List<CertainAreaDTO>> waitListServiceAreas =
                batchGetWaitListServiceAreas(businessId, waitListCustomerInfo);
        // 按 staff 权限，对 ob waitList 过滤
        records = obWaitListFilter(records, param.tokenStaffId(), waitListAppointment);
        // customer 过滤
        records = customerFilter(
                businessId,
                waitListCustomerInfo,
                records,
                param.keyword(),
                param.filters().clientType());
        // service area 过滤
        records = serviceAreaFilter(
                param.filters().serviceAreaIdList(), param.filters().isOutOfArea(), waitListServiceAreas, records);

        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetServices =
                batchGetPetService(businessId, waitListAppointment);
        Map<Integer, MoeGroomingService> serviceMap = batchGetService(businessId, waitListPetServices);
        List<LocalDate> expectDateList = null;
        if (!CollectionUtils.isEmpty(param.filters().dateList())) {
            expectDateList =
                    param.filters().dateList().stream().map(LocalDate::parse).toList();
        }
        LocalDateTime availableStartDateTime = param.filters().availableStartDateTime();
        if (availableStartDateTime.isBefore(now)) {
            availableStartDateTime = now;
        }

        // 计算 available 信息
        Map<Long, Boolean> availableMap = waitListAvailableInfo(
                businessId,
                allStaffIdList,
                expectDateList,
                param.filters().timeRangeList(),
                availableStartDateTime,
                param.filters().availableEndDateTime(),
                waitListCustomerInfo,
                waitListPetServices,
                records);
        // available 过滤
        if (param.filters().isAvailable() != null) {
            records = records.stream()
                    .filter(k -> Objects.equals(
                            availableMap.get(k.getId()), param.filters().isAvailable()))
                    .toList();
        }

        Map<Long, MoeGroomingNote> waitListComment = batchGetTicketComment(records);
        Map<Long, OBPrepayDetailDTO> waitListPrePay = batchGetPrepayDetail(businessId, waitListAppointment);
        Map<Integer, MoeStaffDto> staffInfo = getStaffInfo(businessId, records);

        // 查询宠物的基础信息
        Map<Integer, CustomerPetDetailDTO> petDetailMap =
                moePetDetailService.getCustomerPetDetail(waitListPetServices.values().stream()
                        .filter(Objects::nonNull)
                        .flatMap(k -> k.values().stream())
                        .flatMap(Collection::stream)
                        .toList());

        var customerHasUpdateInfo =
                businessCustomerServiceHelper.listCustomerHasRequestUpdate(businessId.intValue(), allCustomerIds);

        int offset = (param.pageNum() - 1) * param.pageSize();
        List<WaitListVO> result = records.stream()
                .map(k -> makeWaitListVO(
                        k,
                        waitListAppointment.get(k.getId()),
                        waitListComment.get(k.getId()),
                        waitListCustomerInfo.get(k.getId()),
                        waitListPetServices.get(k.getId()),
                        waitListServiceAreas.get(k.getId()),
                        serviceMap,
                        petDetailMap,
                        waitListPrePay.get(k.getId()),
                        availableMap,
                        staffInfo,
                        customerHasUpdateInfo))
                .sorted(getComparator(param.sort()))
                .skip(offset)
                .limit(param.pageSize())
                .toList();
        return WaitListListVO.builder()
                .list(result)
                .page(new Pagination(param.pageNum(), param.pageSize(), records.size()))
                .build();
    }

    /**
     * 对某一个 waitList 推荐合适的 timeSlot。
     * 对推荐结果的影响因素有：waitList 的地址(如果 cacd 开启)、waitList 服务耗时、对当前操作职工可见的职工
     */
    public SmartScheduleListVO smartSchedule(SmartScheduleParam param) {
        Long businessId = param.businessId();
        MoeWaitList record = moeWaitListMapper.selectById(businessId, param.waitListId());
        if (record == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "waitList not exist");
        }
        MoeGroomingAppointment appointment = getAppointment(record);
        CustomerAddressDto address = appointmentServiceV2.getAppointmentAddress(appointment);
        LocationParams waitListLocation =
                address == null ? null : new LocationParams(address.getLat(), address.getLng(), address.getZipcode());

        Map<Integer, List<MoeGroomingPetDetail>> petServiceMap = getPetService(appointment);
        List<Integer> allStaffIdList = getStaffShowOnCalendar(businessId, param.tokenStaffId());
        Map<LocalDate, Map<Integer, List<TimeSlot>>> slotMap = getSlot(
                businessId,
                allStaffIdList,
                null,
                null,
                record,
                waitListLocation,
                petServiceMap,
                param.startDateTime(),
                param.endDateTime());

        List<WaitListSmartScheduleVO> slotList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(slotMap)) {
            slotMap.forEach((date, staffTimeSlotMap) ->
                    staffTimeSlotMap.forEach((staffId, timeSlots) -> slotList.addAll(timeSlots.stream()
                            .map(timeSlot -> WaitListSmartScheduleVO.builder()
                                    .date(date.toString())
                                    .staffId(staffId)
                                    .timeSlot(convertTimeSlot(timeSlot))
                                    .build())
                            .toList())));
        }
        int offset = (param.pageNum() - 1) * param.pageSize();
        List<WaitListSmartScheduleVO> result = slotList.stream()
                .sorted(Comparator.comparing(WaitListSmartScheduleVO::getDate)
                        .thenComparing(k -> k.getTimeSlot().getStartTime()))
                .skip(offset)
                .limit(param.pageSize())
                .toList();
        return SmartScheduleListVO.builder()
                .smartSchedules(result)
                .page(new Pagination(param.pageNum(), param.pageSize(), slotList.size()))
                .build();
    }

    /**
     * 取消一个预约时触发，提示当前空闲区间有若干合适的 waitList 可以 book.
     * 如果这个预约涉及多个职工，只推荐对于第一个职工合适的 waitList
     */
    public AvailableInfoVO getAvailableInfoOnApptCancel(Long companyId, Long businessId, Long appointmentId) {
        LocalDateTime now = LocalDateTime.now(ZoneId.of(getBusinessTimeZone(businessId)));
        MoeGroomingAppointment appointment =
                moeGroomingAppointmentService.getAppointmentById(businessId.intValue(), appointmentId.intValue());
        if (appointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }
        // 获取待推荐职工
        List<MoeGroomingPetDetail> petDetails =
                moePetDetailService.queryMoePetDetailByGroomingIds(List.of(appointmentId.intValue()));
        if (CollectionUtils.isEmpty(petDetails)) {
            return null;
        }
        Integer staffId = petDetails.stream()
                .map(MoeGroomingPetDetail::getStaffId)
                .filter(k -> k > 0)
                .findFirst()
                .orElse(0);
        if (staffId == 0) {
            return null;
        }
        String date = appointment.getAppointmentDate();
        // 历史预约取消时，不返回 available 信息
        LocalDateTime startTime = LocalDate.parse(date).atTime(LocalTime.MIN);
        LocalDateTime endTime = LocalDate.parse(date).atTime(LocalTime.MAX);
        if (startTime.isBefore(now)) {
            startTime = now;
        }
        if (!startTime.isBefore(endTime)) {
            return null;
        }

        // 获取取消预约后的空闲 slot
        List<TimeSlot> freeSlots = getStaffFreeSlots(businessId, startTime, endTime, staffId).values().stream()
                .flatMap(Collection::stream)
                .toList();
        TimeSlot targetSlot = null;
        for (TimeSlot timeSlot : freeSlots) {
            if (timeSlot.getStart() <= appointment.getAppointmentStartTime()
                    && timeSlot.getEnd() >= appointment.getAppointmentEndTime()) {
                targetSlot = timeSlot;
                break;
            }
        }
        if (targetSlot == null) {
            return null;
        }

        List<Integer> staffIdListDeleted = getStaffDeleted(businessId);
        List<MoeWaitList> records =
                moeWaitListMapper.query(companyId, businessId, LocalDate.parse(date), null, null, null, null);
        records = records.stream()
                .filter(k -> checkStaff(k.getStaffPreference(), List.of(staffId), staffIdListDeleted))
                .toList();

        Map<Long, MoeGroomingAppointment> waitListAppointment = batchGetAppointment(businessId, records);
        Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo = batchGetCustomerInfo(null, waitListAppointment);
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetServices =
                batchGetPetService(businessId, waitListAppointment);

        // 计算 available 信息
        Map<Long, Boolean> availableMap = waitListAvailableInfo(
                businessId,
                List.of(staffId),
                null,
                List.of(new TimeRangeDto(targetSlot.getStart(), targetSlot.getEnd())),
                startTime,
                endTime,
                waitListCustomerInfo,
                waitListPetServices,
                records);
        records = records.stream().filter(k -> availableMap.get(k.getId())).toList();
        return AvailableInfoVO.builder()
                .date(date)
                .staffId(staffId)
                .startTime(targetSlot.getStart())
                .endTime(targetSlot.getEnd())
                .availableCnt(records.size())
                .build();
    }

    /**
     * 在日历视图空闲 slot 展示 waitList
     * 1. 包含 available 和 unavailable 的 waitList。一个空闲 slot 最多摆放 4 个 waitList
     * 2. 先摆放 available waitList。waitList 按计算的 available 信息，放置在日历上第一个合适的 slot 中
     * 3. 不足的部分摆放 unavailable waitList。unavailable waitList 按 preference 放置在第一个合适的 slot 中。
     */
    public List<CalendarViewVO> calendarView(CalendarViewParam param) {
        LocalDateTime nowTime = LocalDateTime.now(ZoneId.of(getBusinessTimeZone(param.getBusinessId())));
        LocalDateTime startTime = param.getStartDate().atTime(LocalTime.MIN);
        LocalDateTime endTime = param.getEndDate().atTime(LocalTime.MAX);
        if (startTime.isBefore(nowTime)) {
            startTime = nowTime;
        }
        if (!startTime.isBefore(endTime)) {
            return Collections.emptyList();
        }
        List<Integer> staffIdList = getStaffShowOnCalendar(param.getBusinessId(), param.getTokenStaffId());
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Collections.emptyList();
        }
        Map<LocalDate, Map<Integer, List<TimeSlot>>> freeSlots =
                batchGetStaffFreeSlots(param.getBusinessId(), startTime, endTime, staffIdList);

        List<Integer> staffIdListDeleted = getStaffDeleted(param.getBusinessId());
        List<MoeWaitList> records = moeWaitListMapper.query(
                param.getCompanyId(), param.getBusinessId(), nowTime.toLocalDate(), null, null, null, null);
        records = records.stream()
                .filter(k -> checkStaff(k.getStaffPreference(), staffIdList, staffIdListDeleted))
                .toList();
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        Map<Long, MoeGroomingAppointment> waitListAppointment = batchGetAppointment(param.getBusinessId(), records);
        // 按 staff 权限，对 ob waitList 过滤
        records = obWaitListFilter(records, param.getTokenStaffId(), waitListAppointment);
        Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo = batchGetCustomerInfo(null, waitListAppointment);
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetServices =
                batchGetPetService(param.getBusinessId(), waitListAppointment);
        Map<Integer, MoeGroomingService> serviceMap = batchGetService(param.getBusinessId(), waitListPetServices);
        Map<Long, MoeGroomingNote> waitListComment = batchGetTicketComment(records);
        Map<Long, List<CertainAreaDTO>> waitListServiceAreas =
                batchGetWaitListServiceAreas(param.getBusinessId(), waitListCustomerInfo);
        // 查询宠物的基础信息
        Map<Integer, CustomerPetDetailDTO> petDetailMap =
                moePetDetailService.getCustomerPetDetail(waitListPetServices.values().stream()
                        .flatMap(k -> k.values().stream())
                        .flatMap(Collection::stream)
                        .toList());
        Map<Long, OBPrepayDetailDTO> waitListPrePay = batchGetPrepayDetail(param.getBusinessId(), waitListAppointment);
        Map<Integer, MoeStaffDto> staffInfo = getStaffInfo(param.getBusinessId(), records);

        Map<Long, LocationParams> waitListLocation = new HashMap<>();
        records.forEach(k -> {
            GroomingCalenderCustomerInfo address = waitListCustomerInfo.get(k.getId());
            if (address == null) {
                return;
            }
            waitListLocation.put(
                    k.getId(), new LocationParams(address.getLat(), address.getLng(), address.getZipcode()));
        });

        Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> availableResult = batchGetSlot(
                param.getBusinessId(),
                staffIdList,
                null,
                null,
                records,
                waitListLocation,
                waitListPetServices,
                startTime,
                endTime);
        Map<Long, Boolean> avaliableMap = new HashMap<>();
        for (MoeWaitList record : records) {
            avaliableMap.put(record.getId(), !CollectionUtils.isEmpty(availableResult.get(record.getId())));
        }

        Map<LocalDate, Map<Integer, Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>>>> resultMap =
                new HashMap<>(); // date -> staffId -> timeRange -> waitList
        mergeAvailableWaitList(startTime, endTime, staffIdList, freeSlots, records, availableResult, resultMap);
        calendarViewSortAndTruncate(resultMap);
        mergeUnavailableWaitList(startTime, endTime, staffIdList, freeSlots, records, availableResult, resultMap);
        List<CalendarViewVO> result = new ArrayList<>();
        resultMap.forEach(
                (date, staffTimeSlotMap) -> staffTimeSlotMap.forEach((staffId, waitListInfoMap) -> waitListInfoMap
                        .values()
                        .forEach(waitListInfoList -> waitListInfoList.forEach(waitListInfo -> {
                            MoeWaitList waitList = waitListInfo.getLeft();
                            TimeSlot timeSlot = waitListInfo.getRight();
                            CalendarViewVO vo = new CalendarViewVO();
                            vo.setDate(date.toString());
                            vo.setStaffId(staffId);
                            vo.setWaitList(makeWaitListVO(
                                    waitList,
                                    waitListAppointment.get(waitList.getId()),
                                    waitListComment.get(waitList.getId()),
                                    waitListCustomerInfo.get(waitList.getId()),
                                    waitListPetServices.get(waitList.getId()),
                                    waitListServiceAreas.get(waitList.getId()),
                                    serviceMap,
                                    petDetailMap,
                                    waitListPrePay.get(waitList.getId()),
                                    avaliableMap,
                                    staffInfo,
                                    Map.of()));
                            vo.setTimeSlot(convertTimeSlot(timeSlot));
                            result.add(vo);
                        }))));
        return result;
    }

    /**
     * 按计算的 available 信息，放置在日历上第一个合适的 slot 中
     */
    void mergeAvailableWaitList(
            LocalDateTime startTime,
            LocalDateTime endTime,
            List<Integer> staffIdList,
            Map<LocalDate, Map<Integer, List<TimeSlot>>> freeSlots,
            List<MoeWaitList> records,
            Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> waitListAvailableSlot,
            Map<LocalDate, Map<Integer, Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>>>> result) {
        out:
        for (MoeWaitList waitList : records) {
            Map<LocalDate, Map<Integer, List<TimeSlot>>> availableSlot = waitListAvailableSlot.get(waitList.getId());
            if (CollectionUtils.isEmpty(availableSlot)) {
                continue;
            }
            for (LocalDate curDate = startTime.toLocalDate();
                    !curDate.isAfter(endTime.toLocalDate());
                    curDate = curDate.plusDays(1)) {
                for (Integer staffId : staffIdList) {
                    List<TimeSlot> timeSlots =
                            freeSlots.getOrDefault(curDate, new HashMap<>()).get(staffId);
                    List<TimeSlot> availableTimeSlots =
                            availableSlot.getOrDefault(curDate, new HashMap<>()).get(staffId);
                    Pair<TimeSlot, TimeSlot> freeSlotAndAvailableSlot =
                            calculateAvailableSlotOnDayStaff(timeSlots, availableTimeSlots);
                    if (freeSlotAndAvailableSlot != null) {
                        TimeSlot freeSlot = freeSlotAndAvailableSlot.getLeft();
                        TimeSlot slot = freeSlotAndAvailableSlot.getRight();
                        result.computeIfAbsent(curDate, k -> new HashMap<>())
                                .computeIfAbsent(staffId, k -> new HashMap<>())
                                .computeIfAbsent(
                                        new TimeRangeDto(freeSlot.getStart(), freeSlot.getEnd()),
                                        k -> new ArrayList<>())
                                .add(Pair.of(waitList, slot));
                        continue out;
                    }
                }
            }
        }
    }

    /**
     * 计算第一个合适的空闲 slot，以及对应的 available slot(包含 driving time、前后预约等信息)
     * @return Pair<freeSlot, availableSlot>
     */
    Pair<TimeSlot, TimeSlot> calculateAvailableSlotOnDayStaff(List<TimeSlot> freeSlots, List<TimeSlot> availableSlots) {
        if (CollectionUtils.isEmpty(freeSlots) || CollectionUtils.isEmpty(availableSlots)) {
            return null;
        }
        for (TimeSlot timeSlot : freeSlots) {
            for (TimeSlot slot : availableSlots) {
                if (slot.getStart() >= timeSlot.getStart() && slot.getEnd() <= timeSlot.getEnd()) {
                    return Pair.of(timeSlot, slot);
                }
            }
        }
        return null;
    }

    /**
     * availableSlot 按 driving time 从小到大排序，取前四个 waitList
     */
    void calendarViewSortAndTruncate(
            Map<LocalDate, Map<Integer, Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>>>> data) {
        for (Map<Integer, Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>>> tmp1 : data.values()) {
            for (Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>> tmp2 : tmp1.values()) {
                tmp2.forEach((k, v) -> {
                    // 仅 mobile 商家需要据此排序；该排序对非 mobile 商家无影响
                    v.sort(Comparator.comparing(value -> value.getRight().getDriveInMinutes()));
                    if (v.size() > 4) {
                        tmp2.put(k, v.subList(0, 4));
                    }
                });
            }
        }
    }

    /**
     * unavailable waitList 按 preference 放置在第一个合适的 slot 中
     * 如果第一个合适的 slot 已经放满了 waitList，则跳过此 waitList，不继续往下找合适的 slot
     */
    void mergeUnavailableWaitList(
            LocalDateTime startTime,
            LocalDateTime endTime,
            List<Integer> staffIdList,
            Map<LocalDate, Map<Integer, List<TimeSlot>>> freeSlots,
            List<MoeWaitList> records,
            Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> waitListAvailableSlot,
            Map<LocalDate, Map<Integer, Map<TimeRangeDto, List<Pair<MoeWaitList, TimeSlot>>>>> result) {
        out:
        for (MoeWaitList waitList : records) {
            Map<LocalDate, Map<Integer, List<TimeSlot>>> availableSlot = waitListAvailableSlot.get(waitList.getId());
            if (!CollectionUtils.isEmpty(availableSlot)) {
                continue;
            }
            for (LocalDate curDate = startTime.toLocalDate();
                    !curDate.isAfter(endTime.toLocalDate());
                    curDate = curDate.plusDays(1)) {
                List<String> expectExactDate = waitList.getDatePreference().getExactDate();
                if (!CollectionUtils.isEmpty(expectExactDate) && !expectExactDate.contains(curDate.toString())) {
                    continue;
                }
                List<Integer> expectDayOfWeek = waitList.getDatePreference().getDayOfWeek();
                int dayOfWeekValue = getDayOfWeek(curDate);
                if (!CollectionUtils.isEmpty(expectDayOfWeek) && !expectDayOfWeek.contains(dayOfWeekValue)) {
                    continue;
                }
                for (Integer staffId : staffIdList) {
                    List<Integer> expectStaffIds = waitList.getStaffPreference().getStaffIds();
                    if (!CollectionUtils.isEmpty(expectStaffIds) && !expectStaffIds.contains(staffId)) {
                        continue;
                    }
                    List<TimeSlot> timeSlots =
                            freeSlots.getOrDefault(curDate, new HashMap<>()).get(staffId);
                    if (CollectionUtils.isEmpty(timeSlots)) {
                        continue;
                    }
                    List<Pair<MoeWaitList, TimeSlot>> existWaitLists = result.computeIfAbsent(
                                    curDate, k -> new HashMap<>())
                            .computeIfAbsent(staffId, k -> new HashMap<>())
                            .computeIfAbsent(
                                    new TimeRangeDto(
                                            timeSlots.get(0).getStart(),
                                            timeSlots.get(0).getEnd()),
                                    k -> new ArrayList<>());
                    if (existWaitLists.size() >= 4) {
                        continue out;
                    }
                    existWaitLists.add(Pair.of(waitList, timeSlots.get(0)));
                    continue out;
                }
            }
        }
    }

    public Integer transToNewWaitList(long companyId, List<Long> businessIdList) {
        List<MoeGroomingAppointment> oldWaitListToTrans = appointmentServiceV2.getOldWaitListToBeUpdated(
                companyId, businessIdList.stream().map(Long::intValue).toList());
        if (CollectionUtils.isEmpty(oldWaitListToTrans)) {
            return 0;
        }
        Map<Integer, String> businessTimeZoneMap = batchGetBusinessTimeZone(businessIdList);
        Map<Integer, MoeGroomingAppointment> oldWaitListMap = oldWaitListToTrans.stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));
        List<Integer> appointmentIds =
                oldWaitListToTrans.stream().map(MoeGroomingAppointment::getId).toList();
        Map<Integer, List<MoeGroomingPetDetail>> petDetails =
                moePetDetailService.queryMoePetDetailByGroomingIds(appointmentIds).stream()
                        .collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        List<MoeGroomingAppointment> appointmentsUpdate = new ArrayList<>();
        List<MoeWaitList> waitListsAdd = new ArrayList<>();
        oldWaitListMap.forEach((k, v) -> {
            List<MoeGroomingPetDetail> petDetailList = petDetails.getOrDefault(k, Collections.emptyList());
            Pair<MoeGroomingAppointment, MoeWaitList> pair =
                    transNewWaitList(v, petDetailList, businessTimeZoneMap.get(v.getBusinessId()), false);
            appointmentsUpdate.add(pair.getKey());
            waitListsAdd.add(pair.getValue());
        });
        moeWaitListMapper.batchInsertRecords(waitListsAdd);
        appointmentServiceV2.batchUpdateAppointment(appointmentsUpdate);
        return waitListsAdd.size();
    }

    /**
     * 目前有两种使用场景
     * 1. 老 waitList 接口，将老 waitList 同步到新 waitList
     * 2. ob 自动化触发，将 ob request 转为 waitList
     * @param isAutoMove 是否由 ob 自动化触发
     */
    public void addToWaitList(List<Integer> appointmentIds, boolean isAutoMove) {
        List<MoeGroomingAppointment> appointments = moeGroomingAppointmentService.getAppointmentByIdsV2(appointmentIds);
        appointments = appointments.stream()
                .filter(k -> k.getWaitListStatus() == WaitListStatusEnum.APPTONLY)
                .toList();
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        if (isAutoMove) {
            log.info("ob auto move, appointmentIds: {}", appointmentIds);
        }
        Map<Integer, String> businessTimeZoneMap = batchGetBusinessTimeZone(
                appointments.stream().map(k -> k.getBusinessId().longValue()).toList());
        Map<Integer, List<MoeGroomingPetDetail>> petDetails =
                moePetDetailService.queryMoePetDetailByGroomingIds(appointmentIds).stream()
                        .collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        List<MoeGroomingAppointment> appointmentsUpdate = new ArrayList<>();
        List<MoeWaitList> waitListsAdd = new ArrayList<>();
        appointments.forEach(k -> {
            List<MoeGroomingPetDetail> petDetailList = petDetails.getOrDefault(k.getId(), Collections.emptyList());
            Pair<MoeGroomingAppointment, MoeWaitList> pair =
                    transNewWaitList(k, petDetailList, businessTimeZoneMap.get(k.getBusinessId()), true);
            appointmentsUpdate.add(pair.getKey());
            waitListsAdd.add(pair.getValue());
        });
        appointmentServiceV2.batchUpdateAppointment(appointmentsUpdate);
        moeWaitListMapper.batchInsertRecords(waitListsAdd);
        appointments.forEach(appt -> activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                .setBusinessId(appt.getBusinessId())
                .setAppointmentId(appt.getId())
                .setEvent(BookingRequestEventParams.BookingRequestEvent.MOVED_TO_WAIT_LIST)));
        if (!isAutoMove) {
            return;
        }
        appointments.forEach(k -> {
            // OB异步通知
            ThreadPool.execute(() -> {
                OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams = new OnlineBookWaitingNotifyParams();
                onlineBookWaitingNotifyParams.setCompanyId(k.getCompanyId());
                onlineBookWaitingNotifyParams.setBusinessId(k.getBusinessId());
                onlineBookWaitingNotifyParams.setGroomingId(k.getId());
                onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_WAITING);
                iNotificationClient.bookOnlineNotify(onlineBookWaitingNotifyParams);
            });
            appointmentServiceV2.onAppointmentCanceled(k);
        });
    }

    public void addMoeWaitList(
            Long businessId,
            Long campanyId,
            String timeZone,
            Long appointmentId,
            DatePreferencePO datePreference,
            TimePreferencePO timePreference,
            StaffPreferencePO staffPreference,
            LocalDate validFrom,
            LocalDate validTill,
            Long tokenStaffId) {
        MoeWaitList waitingList = new MoeWaitList();
        waitingList.setBusinessId(businessId);
        waitingList.setCompanyId(campanyId);
        waitingList.setAppointmentId(appointmentId);
        waitingList.setDatePreference(datePreference);
        waitingList.setTimePreference(timePreference);
        waitingList.setStaffPreference(staffPreference);
        waitingList.setValidFrom(validFrom);
        waitingList.setValidTill(validTill);
        waitingList.setCreatedAt(LocalDateTime.now(ZoneId.of(timeZone)));
        waitingList.setCreatedBy(tokenStaffId);
        waitingList.setUpdatedAt(LocalDateTime.now(ZoneId.of(timeZone)));
        waitingList.setUpdatedBy(tokenStaffId);
        moeWaitListMapper.insertSelective(waitingList);
    }

    void deleteMoeWaitList(Long businessId, Long id, String timeZone) {
        moeWaitListMapper.deleteById(businessId, id, LocalDateTime.now(ZoneId.of(timeZone)));
    }

    /**
     * 仅用于删除 waitListStatus 为 WAITLISTONLY 的 waitList
     */
    void batchDeletePureWaitList(Long businessId, String timezone, List<Integer> groomingIds) {
        List<MoeBookOnlineDeposit> deposits = moeBookOnlineDepositService.getOBDepositByGroomingIds(
                businessId.intValue(), new HashSet<>(groomingIds));
        Map<Integer, MoeBookOnlineDeposit> depositMap = deposits.stream()
                .collect(Collectors.toMap(MoeBookOnlineDeposit::getGroomingId, i -> i, (d1, d2) -> d2));
        for (Integer groomingId : groomingIds) {
            ThreadPool.execute(() -> {
                MoeBookOnlineDeposit deposit = depositMap.get(groomingId);
                if (deposit != null && Objects.equals(deposit.getStatus(), BookOnlineDepositConst.REQUIRE_CAPTURE)) {
                    CreateRefundByPaymentIdParams params = new CreateRefundByPaymentIdParams();
                    params.setPaymentId(deposit.getPaymentId());
                    params.setReason("cancel for waitList delete");
                    params.setBookingFee(deposit.getBookingFee());
                    iPaymentRefundClient.createRefundByPaymentId(businessId.intValue(), params);
                    // 退款之后更新ob deposit状态
                    deposit.setStatus(BookOnlineDepositConst.CANCEL);
                    moeBookOnlineDepositService.update(deposit);
                }
            });
        }
        appointmentStorageService.deleteAppointmentList(businessId.intValue(), groomingIds, timezone);
    }

    void batchDeleteRelatedWaitList(Long companyId, Long businessId, String timezone, List<Integer> groomingIds) {
        deleteByAppointmentId(
                companyId, businessId, groomingIds.stream().map(Long::valueOf).toList(), timezone);
        List<MoeGroomingAppointment> appointmentsUpdate = new ArrayList<>();
        for (Integer groomingId : groomingIds) {
            MoeGroomingAppointment appointment = new MoeGroomingAppointment();
            appointment.setId(groomingId);
            appointment.setIsWaitingList(ServiceEnum.IS_WAITING_LIST_FALSE);
            appointment.setWaitListStatus(WaitListStatusEnum.APPTONLY);
            appointmentsUpdate.add(appointment);
        }
        appointmentServiceV2.batchUpdateAppointment(appointmentsUpdate);
    }

    public void deleteByAppointmentId(Long companyId, Long businessId, List<Long> appointmentIdList, String timeZone) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return;
        }
        moeWaitListMapper.deleteByAppointmentId(
                companyId, businessId, appointmentIdList, LocalDateTime.now(ZoneId.of(timeZone)));
    }

    public String getReportDatePreference(DatePreferencePO datePreference) {
        if (datePreference == null) {
            return "";
        }
        if (Boolean.TRUE.equals(datePreference.getIsAnyDate())) {
            return REPORT_TEXT_FOR_ANYDATE;
        }
        if (!CollectionUtils.isEmpty(datePreference.getDayOfWeek())) {
            List<String> dayOfWeek = datePreference.getDayOfWeek().stream()
                    .map(WeekUtil::getKeyForWeekDay)
                    .toList();
            return StringUtils.collectionToDelimitedString(dayOfWeek, ",");
        }
        if (!CollectionUtils.isEmpty(datePreference.getExactDate())) {
            return datePreference.getExactDate().get(0);
        }
        return "";
    }

    public String getReportTimePreference(TimePreferencePO timePreference) {
        if (timePreference == null) {
            return "";
        }
        if (Boolean.TRUE.equals(timePreference.getIsAnyTime())) {
            return REPORT_TEXT_FOR_ANYTIME;
        }
        if (!CollectionUtils.isEmpty(timePreference.getTimeRange())) {
            List<String> timeRange = new ArrayList<>();
            for (TimeRangeDto timeRangeDto : timePreference.getTimeRange()) {
                if (timeRangeDto.getEndTime() <= 720) {
                    timeRange.add("Morning");
                    continue;
                }
                if (timeRangeDto.getStartTime() >= 720 && timeRangeDto.getEndTime() <= 1020) {
                    timeRange.add("Afternoon");
                    continue;
                }
                if (timeRangeDto.getEndTime() >= 1020) {
                    timeRange.add("Evening");
                }
            }
            return StringUtils.collectionToDelimitedString(timeRange, ",");
        }
        if (!CollectionUtils.isEmpty(timePreference.getExactStartTime())) {
            return timePreference.getExactStartTime().get(0).toString();
        }
        return "";
    }

    public String getReportStaffPreference(StaffPreferencePO staffPreference, Map<Integer, MoeStaffDto> staffMap) {
        if (staffPreference == null) {
            return "";
        }
        if (Boolean.TRUE.equals(staffPreference.getIsAnyone())) {
            return REPORT_TEXT_FOR_ANYONE;
        }
        if (!CollectionUtils.isEmpty(staffPreference.getStaffIds())) {
            List<String> staffs = staffPreference.getStaffIds().stream()
                    .map(id -> {
                        MoeStaffDto staff = staffMap.get(id);
                        if (staff == null) {
                            return null;
                        }
                        return staff.getFirstName() + " " + staff.getLastName();
                    })
                    .filter(Objects::nonNull)
                    .toList();
            return String.join(",", staffs);
        }
        return "";
    }

    Pair<MoeGroomingAppointment, MoeWaitList> transNewWaitList(
            MoeGroomingAppointment oldWaitList,
            List<MoeGroomingPetDetail> petDetailList,
            String timeZone,
            boolean isNewCreateTime) {
        MoeGroomingAppointment apptUpdate = new MoeGroomingAppointment();
        apptUpdate.setId(oldWaitList.getId());
        apptUpdate.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
        apptUpdate.setWaitListStatus(WaitListStatusEnum.WAITLISTONLY);

        StaffPreferencePO staffPreference = new StaffPreferencePO();
        List<Integer> staffIds = petDetailList.stream()
                .map(MoeGroomingPetDetail::getStaffId)
                .filter(k -> k > 0)
                .distinct()
                .toList();
        if (!staffIds.isEmpty()) {
            staffPreference.setStaffIds(staffIds);
        } else {
            staffPreference.setIsAnyone(true);
        }
        DatePreferencePO datePreference = new DatePreferencePO();
        if (StringUtils.hasText(oldWaitList.getAppointmentDate())) {
            datePreference.setExactDate(List.of(oldWaitList.getAppointmentDate()));
        } else {
            datePreference.setIsAnyDate(true);
        }
        TimePreferencePO timePreference = new TimePreferencePO();
        if (oldWaitList.getAppointmentStartTime() > 0) {
            timePreference.setExactStartTime(List.of(oldWaitList.getAppointmentStartTime()));
        } else {
            timePreference.setIsAnyTime(true);
        }
        MoeWaitList waitingList = new MoeWaitList();
        waitingList.setBusinessId(oldWaitList.getBusinessId().longValue());
        waitingList.setCompanyId(oldWaitList.getCompanyId());
        waitingList.setAppointmentId(oldWaitList.getId().longValue());
        waitingList.setStaffPreference(staffPreference);
        waitingList.setDatePreference(datePreference);
        waitingList.setTimePreference(timePreference);
        LocalDateTime createTime = LocalDateTime.now(ZoneId.of(timeZone));
        waitingList.setValidTill(createTime.toLocalDate().plusYears(DEFAULT_VALID_TILL));
        if (!isNewCreateTime && oldWaitList.getCreateTime() != null && oldWaitList.getCreateTime() > 0) {
            Instant instant = Instant.ofEpochMilli(oldWaitList.getCreateTime() * 1000);
            createTime = LocalDateTime.ofInstant(instant, ZoneId.of(timeZone));
        }
        waitingList.setValidFrom(createTime.toLocalDate());
        if (oldWaitList.getMoveWaitingBy() != null) {
            waitingList.setCreatedBy(oldWaitList.getMoveWaitingBy().longValue());
            waitingList.setUpdatedBy(oldWaitList.getMoveWaitingBy().longValue());
        }
        waitingList.setCreatedAt(createTime);
        waitingList.setUpdatedAt(createTime);
        return Pair.of(apptUpdate, waitingList);
    }

    /**
     * 用于预约取消时，将关联的 waitList 单独摘出来。
     * 可看作后台自动创建了一个 waitList
     */
    void detachFromOldAppointment(MoeGroomingAppointment existMoeGroomingAppointment) {
        if (existMoeGroomingAppointment.getWaitListStatus() != WaitListStatusEnum.APPTANDWAITLIST) {
            return;
        }
        MoeWaitList existMoeWaitList = getWaitListByAppointment(
                existMoeGroomingAppointment.getBusinessId().longValue(),
                existMoeGroomingAppointment.getId().longValue());
        MoeGroomingNote existTicketComment = moeGroomingNoteService
                .getNoteListByGroomingIdListAndType(
                        List.of(existMoeGroomingAppointment.getId()), GroomingAppointmentEnum.NOTE_COMMENT.intValue())
                .stream()
                .findFirst()
                .orElse(null);

        List<MoeGroomingPetDetail> existPetDetails =
                moePetDetailService.queryPetDetailByOneGroomingId(existMoeGroomingAppointment.getId());

        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setCompanyId(existMoeGroomingAppointment.getCompanyId());
        moeGroomingAppointment.setBusinessId(existMoeGroomingAppointment.getBusinessId());
        moeGroomingAppointment.setCustomerId(existMoeGroomingAppointment.getCustomerId());
        moeGroomingAppointment.setOrderId(CommonUtil.getUuid());
        moeGroomingAppointment.setAppointmentDate("");
        moeGroomingAppointment.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        moeGroomingAppointment.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
        moeGroomingAppointment.setWaitListStatus(WaitListStatusEnum.WAITLISTONLY);
        moeGroomingAppointment.setMoveWaitingBy(existMoeGroomingAppointment.getMoveWaitingBy());
        moeGroomingAppointment.setCreatedById(existMoeGroomingAppointment.getCreatedById());
        moeGroomingAppointment.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        List<WaitListPetServiceParam> petParams = new ArrayList<>();
        Map<Integer, List<MoeGroomingPetDetail>> existPetDetailsMap =
                existPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        existPetDetailsMap.forEach((petId, value) -> {
            List<WaitListServiceAndOperationParams> serviceList = value.stream()
                    .map(g -> new WaitListServiceAndOperationParams(g.getServiceId()))
                    .toList();
            petParams.add(new WaitListPetServiceParam(petId, serviceList));
        });
        List<PetParams> petParams1 = waitListPetParams2PetParams(petParams);
        List<MoeGroomingPetDetail> petDetails = moePetDetailService.buildPetDetailList(
                petParams1,
                existMoeGroomingAppointment.getBusinessId(),
                existMoeGroomingAppointment.getAppointmentDate(),
                0,
                moePetDetailService.isAllPetsStartAtSameTimeV2(existPetDetailsMap));

        // 插入数据库
        appointmentStorageService.insertAppointment(moeGroomingAppointment, petDetails, new HashMap<>());
        if (existTicketComment != null) {
            existTicketComment.setId(null);
            existTicketComment.setGroomingId(moeGroomingAppointment.getId());
            moeGroomingNoteService.batchInsert(List.of(existTicketComment));
        }
        // 更新 MoeWaitList
        MoeWaitList waitListUpdate = new MoeWaitList();
        waitListUpdate.setId(existMoeWaitList.getId());
        waitListUpdate.setAppointmentId(moeGroomingAppointment.getId().longValue());
        moeWaitListMapper.updateByPrimaryKeySelective(waitListUpdate);
        // 更新 existMoeGroomingAppointment
        MoeGroomingAppointment appointmentUpdate = new MoeGroomingAppointment();
        appointmentUpdate.setId(existMoeGroomingAppointment.getId());
        appointmentUpdate.setIsWaitingList(ServiceEnum.IS_WAITING_LIST_FALSE);
        appointmentUpdate.setWaitListStatus(WaitListStatusEnum.APPTONLY);
        appointmentServiceV2.editAppointment(appointmentUpdate);
    }

    MoeGroomingAppointment getAppointment(MoeWaitList record) {
        return batchGetAppointment(record.getBusinessId(), List.of(record)).get(record.getId());
    }

    /**
     * @return <waitListId, MoeGroomingAppointment>
     */
    public Map<Long, MoeGroomingAppointment> batchGetAppointment(Long businessId, List<MoeWaitList> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new HashMap<>();
        }
        List<Integer> appointmentIds =
                records.stream().map(k -> k.getAppointmentId().intValue()).toList();
        Map<Integer, MoeGroomingAppointment> appointmentMap =
                moeGroomingAppointmentService.getAppointmentByIds(businessId.intValue(), appointmentIds).stream()
                        .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));
        return records.stream()
                .collect(Collectors.toMap(
                        MoeWaitList::getId,
                        k -> appointmentMap.get(k.getAppointmentId().intValue())));
    }

    /**
     * @return <waitListId, GroomingCalenderCustomerInfo>
     */
    Map<Long, GroomingCalenderCustomerInfo> batchGetCustomerInfo(
            Long tokenStaffId, Map<Long, MoeGroomingAppointment> waitListAppointment) {
        Map<Long, GroomingCalenderCustomerInfo> result = new HashMap<>();
        if (CollectionUtils.isEmpty(waitListAppointment)) {
            return result;
        }
        List<MoeGroomingAppointment> appointmentList =
                waitListAppointment.values().stream().toList();
        Map<Integer, GroomingCalenderCustomerInfo> appointmentCustomer = appointmentServiceV2.batchGetCustomerInfo(
                tokenStaffId == null ? null : tokenStaffId.intValue(), appointmentList);
        waitListAppointment.forEach((waitListId, appointment) -> {
            GroomingCalenderCustomerInfo customerInfo = appointmentCustomer.get(appointment.getId());
            if (customerInfo != null) {
                result.put(waitListId, customerInfo);
            }
        });
        return result;
    }

    /**
     * ignore business id, only filter by staff ids
     * @return <staffId, MoeStaffDto>
     */
    public Map<Integer, MoeStaffDto> getStaffInfo(@Deprecated Long businessId, List<MoeWaitList> records) {
        Set<Integer> staffIds = new HashSet<>();
        for (MoeWaitList waitList : records) {
            if (waitList.getCreatedBy() != null && waitList.getCreatedBy() > 0) {
                staffIds.add(waitList.getCreatedBy().intValue());
            }
            if (waitList.getUpdatedBy() != null && waitList.getUpdatedBy() > 0) {
                staffIds.add(waitList.getUpdatedBy().intValue());
            }
            if (!CollectionUtils.isEmpty(waitList.getStaffPreference().getStaffIds())) {
                staffIds.addAll(waitList.getStaffPreference().getStaffIds());
            }
        }
        if (CollectionUtils.isEmpty(staffIds)) {
            return new HashMap<>();
        }
        StaffIdListParams staffIdListParams = new StaffIdListParams(
                Objects.isNull(businessId) ? null : businessId.intValue(),
                staffIds.stream().toList());
        return iBusinessStaffClient.getStaffListV2(staffIdListParams).stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * @return Map<petId, List<MoeGroomingPetDetail>>
     */
    public Map<Integer, List<MoeGroomingPetDetail>> getPetService(MoeGroomingAppointment appointment) {
        return batchGetPetService(appointment.getBusinessId().longValue(), Map.of(0L, appointment))
                .getOrDefault(0L, new HashMap<>());
    }

    /**
     * @return <waitListId, Map<petId, List<MoeGroomingPetDetail>>>
     */
    public Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> batchGetPetService(
            Long businessId, Map<Long, MoeGroomingAppointment> waitListAppointment) {
        if (CollectionUtils.isEmpty(waitListAppointment)) {
            return new HashMap<>();
        }
        List<Integer> appointmentIds = waitListAppointment.values().stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
        Map<Integer, Map<Integer, List<MoeGroomingPetDetail>>> petServiceMap =
                moePetDetailService.queryMoePetDetailByGroomingIds(appointmentIds).stream()
                        .collect(Collectors.groupingBy(
                                MoeGroomingPetDetail::getGroomingId,
                                Collectors.groupingBy(MoeGroomingPetDetail::getPetId)));
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> result = new HashMap<>();
        Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> toSyncNewestTimeAndPrice = new HashMap<>();
        waitListAppointment.forEach((waitListId, appointment) -> {
            Map<Integer, List<MoeGroomingPetDetail>> petService = petServiceMap.get(appointment.getId());
            if (CollectionUtils.isEmpty(petService)) {
                return;
            }
            if (appointment.getWaitListStatus() != WaitListStatusEnum.WAITLISTONLY) {
                result.put(waitListId, petService);
            } else {
                toSyncNewestTimeAndPrice.put(waitListId, petService);
            }
        });
        moePetDetailService.batchSyncNewestTimeAndPrice(businessId.intValue(), toSyncNewestTimeAndPrice);
        result.putAll(toSyncNewestTimeAndPrice);
        return result;
    }

    public Map<Integer, MoeGroomingService> batchGetService(
            Long businessId, Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetServices) {
        Map<Integer, MoeGroomingService> serviceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(waitListPetServices)) {
            return serviceMap;
        }
        List<Integer> serviceIds = waitListPetServices.values().stream()
                .flatMap(petDetails -> petDetails.values().stream().flatMap(Collection::stream))
                .map(MoeGroomingPetDetail::getServiceId)
                .distinct()
                .filter(k -> k != null && k > 0)
                .toList();
        if (!CollectionUtils.isEmpty(serviceIds)) {
            serviceMap = groomingServiceService.getServiceMap(businessId.intValue(), serviceIds);
        }
        return serviceMap;
    }

    MoeGroomingNote getTicketComment(MoeWaitList record) {
        return batchGetTicketComment(List.of(record)).get(record.getId());
    }

    /**
     * @return <waitListId, MoeGroomingNote>
     */
    public Map<Long, MoeGroomingNote> batchGetTicketComment(List<MoeWaitList> records) {
        Map<Long, MoeGroomingNote> result = new HashMap<>();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<Integer> appointmentIds =
                records.stream().map(k -> k.getAppointmentId().intValue()).toList();
        Map<Integer, MoeGroomingNote> apptCommentMap = moeGroomingNoteService
                .getNoteListByGroomingIdListAndType(appointmentIds, GroomingAppointmentEnum.NOTE_COMMENT.intValue())
                .stream()
                .collect(Collectors.toMap(MoeGroomingNote::getGroomingId, Function.identity(), (k1, k2) -> k2));
        records.forEach(k -> {
            MoeGroomingNote note = apptCommentMap.get(k.getAppointmentId().intValue());
            if (note != null) {
                result.put(k.getId(), note);
            }
        });
        return result;
    }

    List<CertainAreaDTO> getWaitListServiceAreas(Long businessId, GroomingCalenderCustomerInfo customerInfo) {
        if (customerInfo == null) {
            return List.of();
        }
        return batchGetWaitListServiceAreas(businessId, Map.of(0L, customerInfo))
                .getOrDefault(0L, List.of());
    }

    /**
     * @param waitListCustomerInfo <waitListId, GroomingCalenderCustomerInfo>
     * @return <waitListId, List<CertainAreaDTO>>
     */
    Map<Long, List<CertainAreaDTO>> batchGetWaitListServiceAreas(
            Long businessId, Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo) {
        Map<Long, List<CertainAreaDTO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(waitListCustomerInfo)) {
            return result;
        }

        List<GetAreasByLocationParams> locations = waitListCustomerInfo.entrySet().stream()
                .map(k -> {
                    GroomingCalenderCustomerInfo customerInfo = k.getValue();
                    if (customerInfo == null
                            || !StringUtils.hasText(customerInfo.getLat())
                            || !StringUtils.hasText(customerInfo.getLng())) {
                        return null;
                    }
                    return new GetAreasByLocationParams(
                            k.getKey(), customerInfo.getLat(), customerInfo.getLng(), customerInfo.getZipcode());
                })
                .filter(Objects::nonNull)
                .toList();
        if (locations.isEmpty()) {
            return result;
        }
        return iBusinessServiceAreaClient.getAreasByLocation(
                new BatchGetAreasByLocationParams(businessId, null, locations));
    }

    public Map<Long, OBPrepayDetailDTO> batchGetPrepayDetail(
            Long businessId, Map<Long, MoeGroomingAppointment> waitListAppointment) {
        Map<Long, OBPrepayDetailDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(waitListAppointment)) {
            return result;
        }
        List<MoeGroomingAppointment> appointmentList =
                waitListAppointment.values().stream().toList();
        Map<Integer, OBPrepayDetailDTO> appointmentPrepayDetail =
                appointmentServiceV2.batchGetPrepayDetail(businessId.intValue(), appointmentList);
        waitListAppointment.forEach((waitListId, appointment) -> {
            OBPrepayDetailDTO detail = appointmentPrepayDetail.get(appointment.getId());
            if (detail != null) {
                result.put(waitListId, detail);
            }
        });
        return result;
    }

    Map<LocalDate, List<TimeSlot>> getStaffFreeSlots(
            Long businessId, LocalDateTime startTime, LocalDateTime entTime, Integer staffId) {
        Map<LocalDate, Map<Integer, List<TimeSlot>>> result =
                batchGetStaffFreeSlots(businessId, startTime, entTime, List.of(staffId));
        Map<LocalDate, List<TimeSlot>> finalResult = new HashMap<>();
        result.forEach((k, v) -> {
            List<TimeSlot> timeSlots = new ArrayList<>();
            v.forEach((k1, v1) -> timeSlots.addAll(v1));
            finalResult.put(k, timeSlots);
        });
        return finalResult;
    }

    Map<LocalDate, Map<Integer, List<TimeSlot>>> batchGetStaffFreeSlots(
            Long businessId, LocalDateTime startTime, LocalDateTime entTime, List<Integer> staffIds) {
        // 已有预约信息
        List<SmartScheduleGroomingDetailsDTO> existPetDetailInfoList = moePetDetailService.getServiceList(
                businessId.intValue(), startTime.toLocalDate(), entTime.toLocalDate(), staffIds);

        // 员工工作时间
        Map<Integer, Map<String, List<TimeRangeDto>>> workingRange = iBusinessStaffClient.queryStaffWorkingHourRange(
                businessId.intValue(),
                staffIds,
                startTime.toLocalDate().toString(),
                entTime.toLocalDate().toString());

        Map<LocalDate, Map<Integer, List<TimeSlot>>> result = new HashMap<>();
        for (LocalDate curDate = startTime.toLocalDate();
                !curDate.isAfter(entTime.toLocalDate());
                curDate = curDate.plusDays(1)) {
            String curDateStr = curDate.toString();
            for (Integer staffId : staffIds) {
                // 该职工当天工作时间
                List<TimeRangeDto> curWorkingTime =
                        workingRange.getOrDefault(staffId, new HashMap<>()).get(curDateStr);
                if (CollectionUtils.isEmpty(curWorkingTime)) {
                    continue;
                }
                // 该职工当天预约情况
                List<SmartScheduleGroomingDetailsDTO> petDetailInfoList = existPetDetailInfoList.stream()
                        .filter(srv -> srv.getStaffId().equals(staffId)
                                && (StringUtils.hasText(srv.getStartDate())
                                                ? srv.getStartDate()
                                                : srv.getAppointmentDate())
                                        .equals(curDateStr))
                        .sorted(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime))
                        .toList();
                // 在相邻 petService 之间，构建 TimeSlot
                List<TimeSlot> slotBetweenPetService = smartScheduleService.buildFreeTimeSlotsByStaffId(
                        curWorkingTime.get(0).getStartTime(),
                        curWorkingTime.get(curWorkingTime.size() - 1).getEndTime(),
                        petDetailInfoList);
                List<TimeSlot> freeSlot = filterNonWorkingTime(slotBetweenPetService, curWorkingTime);
                // 过滤边界时间
                freeSlot = smartScheduleService.boundaryTimeFilter(freeSlot, curDate, startTime, entTime);
                freeSlot =
                        freeSlot.stream().filter(k -> k.getEnd() > k.getStart()).toList();
                if (!CollectionUtils.isEmpty(freeSlot)) {
                    result.computeIfAbsent(curDate, k -> new HashMap<>())
                            .computeIfAbsent(staffId, k -> new ArrayList<>())
                            .addAll(freeSlot);
                }
            }
        }
        return result;
    }

    List<MoeWaitList> obWaitListFilter(
            List<MoeWaitList> records, Long tokenStaffId, Map<Long, MoeGroomingAppointment> waitListAppointment) {
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        StaffPermissions staffPermissions = businessStaffService.getBusinessRoleByStaffId(tokenStaffId.intValue());
        Boolean canAccessOBWaitList =
                checkStaffPermissionsInfo(staffPermissions, CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        if (canAccessOBWaitList) {
            return records;
        }
        List<MoeWaitList> result = new ArrayList<>();
        records.forEach(k -> {
            if (!waitListAppointment
                    .get(k.getId())
                    .getBookOnlineStatus()
                    .equals(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
                result.add(k);
            }
        });
        return result;
    }

    /**
     * @param wantDateList 为 null 或空列表，表示不过滤
     */
    boolean checkDate(DatePreferencePO datePreference, Boolean isAnyDate, List<String> wantDateList) {
        if (Boolean.TRUE.equals(isAnyDate)) {
            return Boolean.TRUE.equals(datePreference.getIsAnyDate());
        }
        if (CollectionUtils.isEmpty(wantDateList)) {
            return true;
        }
        for (String wantDate : wantDateList) {
            if (Boolean.TRUE.equals(datePreference.getIsAnyDate())) {
                return true;
            }
            if (datePreference.getDayOfWeek() != null
                    && datePreference.getDayOfWeek().contains(WeekUtil.getDayIndexOfWeekByDate(wantDate))) {
                return true;
            }
            if (datePreference.getExactDate() != null) {
                for (String date : datePreference.getExactDate()) {
                    if (wantDate.equals(date)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @param wantTimeRangeList 为 null 或空列表，表示不过滤
     */
    boolean checkTime(TimePreferencePO timePreference, Boolean isAnyTime, List<TimeRangeDto> wantTimeRangeList) {
        if (Boolean.TRUE.equals(isAnyTime)) {
            return Boolean.TRUE.equals(timePreference.getIsAnyTime());
        }
        if (CollectionUtils.isEmpty(wantTimeRangeList)) {
            return true;
        }
        for (TimeRangeDto wantTimeRange : wantTimeRangeList) {
            if (Boolean.TRUE.equals(timePreference.getIsAnyTime())) {
                return true;
            }
            if (timePreference.getTimeRange() != null) {
                if (timePreference.getTimeRange().stream()
                        .anyMatch(k -> k.getEndTime() > wantTimeRange.getStartTime()
                                && k.getStartTime() < wantTimeRange.getEndTime())) {
                    return true;
                }
            }
            if (timePreference.getExactStartTime() != null) {
                for (Integer exactTime : timePreference.getExactStartTime()) {
                    if (wantTimeRangeList.stream()
                            .anyMatch(k -> k.getStartTime() <= exactTime && k.getEndTime() >= exactTime)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 若 staffPreference 偏好的 staff 均已删除，则该 wait list 对所有职工可见
     * @param wantStaffIdList 为空列表，表示没有待匹配的 staff
     */
    boolean checkStaff(
            StaffPreferencePO staffPreference, List<Integer> wantStaffIdList, List<Integer> staffIdListDeleted) {
        if (wantStaffIdList.isEmpty()) {
            return false;
        }
        if (Boolean.TRUE.equals(staffPreference.getIsAnyone())) {
            return true;
        }
        if (staffPreference.getStaffIds() != null) {
            // 偏好的 staff 均已删除
            if (!CollectionUtils.isEmpty(staffIdListDeleted)
                    && new HashSet<>(staffIdListDeleted).containsAll(staffPreference.getStaffIds())) {
                return true;
            }
            return !staffPreference.getStaffIds().stream()
                    .filter(wantStaffIdList::contains)
                    .toList()
                    .isEmpty();
        }
        return false;
    }

    List<MoeWaitList> customerFilter(
            Long businessId,
            Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo,
            List<MoeWaitList> records,
            String keyword,
            List<String> clientTypeList) {
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        if (!StringUtils.hasText(keyword) && CollectionUtils.isEmpty(clientTypeList)) {
            return records;
        }
        SearchCustomerIdsParam searchCustomerIdsParam =
                new SearchCustomerIdsParam().setBusinessId(businessId.intValue());
        if (StringUtils.hasText(keyword)) {
            searchCustomerIdsParam.setKeyword(keyword);
        }
        searchCustomerIdsParam.setClientTypes(clientTypeList);

        Set<Integer> customerIdList = iCustomerCustomerService.searchCustomerIds(searchCustomerIdsParam);
        return records.stream()
                .filter(k -> {
                    GroomingCalenderCustomerInfo customerInfo = waitListCustomerInfo.get(k.getId());
                    if (customerInfo == null) {
                        return false;
                    }
                    return customerIdList.contains(customerInfo.getCustomerId());
                })
                .toList();
    }

    /**
     * @param wantServiceAreaIdList 为 null 或空列表，表示不过滤
     */
    List<MoeWaitList> serviceAreaFilter(
            List<Integer> wantServiceAreaIdList,
            Boolean isOutOfArea,
            Map<Long, List<CertainAreaDTO>> waitListServiceAreas,
            List<MoeWaitList> records) {
        List<MoeWaitList> result = new ArrayList<>();
        records.forEach(k -> {
            if (checkServiceArea(waitListServiceAreas.get(k.getId()), isOutOfArea, wantServiceAreaIdList)) {
                result.add(k);
            }
        });
        return result;
    }

    boolean checkServiceArea(
            List<CertainAreaDTO> certainAreaDTOS, Boolean isOutOfArea, List<Integer> wantServiceAreaIdList) {
        if (Boolean.TRUE.equals(isOutOfArea)) {
            return CollectionUtils.isEmpty(certainAreaDTOS);
        }
        if (CollectionUtils.isEmpty(wantServiceAreaIdList)) {
            return true;
        }
        if (CollectionUtils.isEmpty(certainAreaDTOS)) {
            return false;
        }
        for (CertainAreaDTO certainAreaDTO : certainAreaDTOS) {
            if (wantServiceAreaIdList.contains(certainAreaDTO.getAreaId().intValue())) {
                return true;
            }
        }
        return false;
    }

    /**
     * @return <waitListId, available>
     */
    Map<Long, Boolean> waitListAvailableInfo(
            Long businessId,
            List<Integer> allStaffIdList,
            List<LocalDate> dateList,
            List<TimeRangeDto> timeRangeList,
            LocalDateTime availableStartTime,
            LocalDateTime availableEndTime,
            Map<Long, GroomingCalenderCustomerInfo> waitListCustomerInfo,
            Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> waitListPetDetails,
            List<MoeWaitList> records) {
        Map<Long, Boolean> result = new HashMap<>();
        if (records.isEmpty()) {
            return result;
        }

        Map<Long, LocationParams> waitListLocation = new HashMap<>();
        records.forEach(k -> {
            GroomingCalenderCustomerInfo address = waitListCustomerInfo.get(k.getId());
            if (address == null) {
                return;
            }
            waitListLocation.put(
                    k.getId(), new LocationParams(address.getLat(), address.getLng(), address.getZipcode()));
        });

        Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> availableResult = batchGetSlot(
                businessId,
                allStaffIdList,
                dateList,
                timeRangeList,
                records,
                waitListLocation,
                waitListPetDetails,
                availableStartTime,
                availableEndTime);

        for (MoeWaitList record : records) {
            result.put(record.getId(), !CollectionUtils.isEmpty(availableResult.get(record.getId())));
        }
        return result;
    }

    Map<LocalDate, Map<Integer, List<TimeSlot>>> getSlot(
            Long businessId,
            List<Integer> allStaffIdList,
            List<LocalDate> dateList,
            List<TimeRangeDto> timeRangeList,
            MoeWaitList record,
            LocationParams waitListLocation,
            Map<Integer, List<MoeGroomingPetDetail>> petDetailMap,
            LocalDateTime availableStartTime,
            LocalDateTime availableEndTime) {
        Map<Long, LocationParams> waitListLocationMap = new HashMap<>();
        if (waitListLocation != null) {
            waitListLocationMap.put(record.getId(), waitListLocation);
        }
        return batchGetSlot(
                        businessId,
                        allStaffIdList,
                        dateList,
                        timeRangeList,
                        List.of(record),
                        waitListLocationMap,
                        Map.of(record.getId(), petDetailMap),
                        availableStartTime,
                        availableEndTime)
                .get(record.getId());
    }

    /**
     *
     * @param allStaffIdList 所有待查询的职工列表
     * @param expectDateList 期待的日期列表
     * @param expectTimeRangeList 期待的时间段列表
     * @param records 待查询的 waitList 列表
     * @param waitListLocation <waitListId, LocationParams> 用于 cacd 判断的地址信息
     * @param petDetailMap <waitListId, <petId, List<GroomingPetDetailDTO>>> 用于判断 slot 时间长度是否足够
     * @param availableStartTime slot 查找的起始时间
     * @param availableEndTime slot 查找的结束时间
     * @return <waitListId, <date, <staffId, List<timeSlot>>>>
     */
    Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> batchGetSlot(
            Long businessId,
            List<Integer> allStaffIdList,
            List<LocalDate> expectDateList,
            List<TimeRangeDto> expectTimeRangeList,
            List<MoeWaitList> records,
            Map<Long, LocationParams> waitListLocation,
            Map<Long, Map<Integer, List<MoeGroomingPetDetail>>> petDetailMap,
            LocalDateTime availableStartTime,
            LocalDateTime availableEndTime) {
        Map<Long, Map<LocalDate, Map<Integer, List<TimeSlot>>>> result = new HashMap<>();
        if (records.isEmpty() || CollectionUtils.isEmpty(allStaffIdList)) {
            return result;
        }

        // 初始化 BatchSSParams 相关参数
        BatchSSParams ssParams = smartScheduleService.initBatchSSParams(
                false,
                businessId.intValue(),
                allStaffIdList,
                availableStartTime.toLocalDate(),
                availableEndTime.toLocalDate(),
                waitListLocation);

        Map<Long, Future<Map<LocalDate, Map<Integer, List<TimeSlot>>>>> waitListFutureMap = new HashMap<>();
        for (MoeWaitList record : records) {
            Future<Map<LocalDate, Map<Integer, List<TimeSlot>>>> waitListSlotsFuture =
                    ThreadPool.submit(() -> getSlotPerWaitList(
                            record,
                            ssParams,
                            availableStartTime,
                            availableEndTime,
                            allStaffIdList,
                            expectDateList,
                            expectTimeRangeList,
                            petDetailMap.get(record.getId())));
            waitListFutureMap.put(record.getId(), waitListSlotsFuture);
        }
        for (Map.Entry<Long, Future<Map<LocalDate, Map<Integer, List<TimeSlot>>>>> entry :
                waitListFutureMap.entrySet()) {
            try {
                Map<LocalDate, Map<Integer, List<TimeSlot>>> waitListSlots =
                        entry.getValue().get();
                if (!CollectionUtils.isEmpty(waitListSlots)) {
                    result.put(entry.getKey(), waitListSlots);
                }
            } catch (Exception e) {
                log.error("batchGetSlot error, waitListId: {}", entry.getKey(), e);
            }
        }
        return result;
    }

    /**
     * 获取单个 waitList 的 available slot
     * @param availableStartTime slot 查找的起始时间
     * @param availableEndTime slot 查找的结束时间
     * @param allStaffIdList slot 查找的员工列表
     * @param dateList slot 查找的日期列表
     * @param timeRangeList slot 查找的时间段列表
     * @param waitListPetDetailMap slot 应满足的时长条件
     * @return <date, <staffId, List<timeSlot>>> 长度为 0 的 List<TimeSlot> 会从结果中过滤掉
     */
    Map<LocalDate, Map<Integer, List<TimeSlot>>> getSlotPerWaitList(
            MoeWaitList record,
            BatchSSParams ssParams,
            LocalDateTime availableStartTime,
            LocalDateTime availableEndTime,
            List<Integer> allStaffIdList,
            List<LocalDate> dateList,
            List<TimeRangeDto> timeRangeList,
            Map<Integer, List<MoeGroomingPetDetail>> waitListPetDetailMap) {
        List<Integer> staffIdList = new ArrayList<>(allStaffIdList);
        if (!Boolean.TRUE.equals(record.getStaffPreference().getIsAnyone())
                && !CollectionUtils.isEmpty(record.getStaffPreference().getStaffIds())) {
            staffIdList.retainAll(record.getStaffPreference().getStaffIds());
        }
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Collections.emptyMap();
        }
        if (availableStartTime.isBefore(record.getValidFrom().atStartOfDay())) {
            availableStartTime = record.getValidFrom().atStartOfDay();
        }
        if (availableEndTime.isAfter(record.getValidTill().atTime(LocalTime.MAX))) {
            availableEndTime = record.getValidTill().atTime(LocalTime.MAX);
        }

        // 包含没有 service 的 petService，不需要计算 slot
        if (!isAllPetServiceValid(waitListPetDetailMap)) {
            return Collections.emptyMap();
        }
        // 获取原预约开始结束时间，用于过滤推荐的 slot 恰好等于 预约时间的场景
        int startTime = waitListPetDetailMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingPetDetail::getStartTime)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .orElse(0L)
                .intValue();
        int endTime = waitListPetDetailMap.values().stream()
                .flatMap(Collection::stream)
                .map(MoeGroomingPetDetail::getEndTime)
                .filter(Objects::nonNull)
                .max(Long::compareTo)
                .orElse(0L)
                .intValue();
        // 获取 waitList 的服务时长
        Integer serviceDuration = moePetDetailService.calculateServiceDuration(waitListPetDetailMap.values().stream()
                .flatMap(Collection::stream)
                .toList());

        GetFreeSlotParams freeSlotParams =
                buildGetFreeSlotParams(record, availableStartTime, availableEndTime, dateList, timeRangeList);
        if (freeSlotParams == null) {
            return Collections.emptyMap();
        }
        Map<String, Map<Integer, List<TimeSlot>>> freeSlots =
                smartScheduleService.getStaffPerDayFreeTimeSlot(ssParams, freeSlotParams);

        Map<LocalDate, Map<Integer, List<TimeSlot>>> result = new HashMap<>();
        for (LocalDate curDate = availableStartTime.toLocalDate();
                !curDate.isAfter(availableEndTime.toLocalDate());
                curDate = curDate.plusDays(1)) {
            for (Integer staffId : staffIdList) {
                List<TimeSlot> curFreeSlots = freeSlots
                        .getOrDefault(curDate.toString(), new HashMap<>())
                        .get(staffId);
                List<TimeSlot> availableSlots = smartScheduleService.getAvailableTimeSlot(
                        ssParams,
                        buildGetAvailableSlotParams(curFreeSlots, staffId, serviceDuration, record, ssParams));
                // filtered the exact same slot with the original appt
                availableSlots = availableSlots.stream()
                        .filter(k -> k.getStart() < startTime || k.getEnd() > endTime)
                        .toList();
                if (!availableSlots.isEmpty()) {
                    result.computeIfAbsent(curDate, k -> new HashMap<>()).put(staffId, availableSlots);
                }
            }
        }
        return result;
    }

    GetFreeSlotParams buildGetFreeSlotParams(
            MoeWaitList record,
            LocalDateTime availableStartTime,
            LocalDateTime availableEndTime,
            List<LocalDate> dateList,
            List<TimeRangeDto> timeRangeList) {
        GetFreeSlotParams getFreeSlotParams = new GetFreeSlotParams();
        getFreeSlotParams.setLocationId(record.getId());
        getFreeSlotParams.setStartDateTime(availableStartTime);
        getFreeSlotParams.setEndDateTime(availableEndTime);
        getFreeSlotParams.setFilterAppointmentIdList(
                List.of(record.getAppointmentId().intValue()));
        if (!Boolean.TRUE.equals(record.getDatePreference().getIsAnyDate())
                && !CollectionUtils.isEmpty(record.getDatePreference().getExactDate())) {
            List<LocalDate> localDateList = new ArrayList<>();
            for (String date : record.getDatePreference().getExactDate()) {
                localDateList.add(LocalDate.parse(date));
            }
            getFreeSlotParams.setExpectDateList(localDateList);
        }
        if (!CollectionUtils.isEmpty(dateList)) {
            if (CollectionUtils.isEmpty(getFreeSlotParams.getExpectDateList())) {
                getFreeSlotParams.setExpectDateList(dateList);
            } else {
                getFreeSlotParams.getExpectDateList().retainAll(dateList);
                if (getFreeSlotParams.getExpectDateList().isEmpty()) {
                    return null;
                }
            }
        }
        if (!Boolean.TRUE.equals(record.getTimePreference().getIsAnyTime())
                && !CollectionUtils.isEmpty(record.getTimePreference().getTimeRange())) {
            getFreeSlotParams.setExpectTimeRangeList(record.getTimePreference().getTimeRange());
        }
        if (!CollectionUtils.isEmpty(timeRangeList)) {
            if (CollectionUtils.isEmpty(getFreeSlotParams.getExpectTimeRangeList())) {
                getFreeSlotParams.setExpectTimeRangeList(timeRangeList);
            } else {
                getFreeSlotParams.setExpectTimeRangeList(
                        timeRangeListIntersection(getFreeSlotParams.getExpectTimeRangeList(), timeRangeList));
                if (getFreeSlotParams.getExpectTimeRangeList().isEmpty()) {
                    return null;
                }
            }
        }
        getFreeSlotParams.setExpectDayOfWeekList(record.getDatePreference().getDayOfWeek());
        getFreeSlotParams.setExpectStartTimeList(record.getTimePreference().getExactStartTime());
        return getFreeSlotParams;
    }

    GetAvailableSlotParams buildGetAvailableSlotParams(
            List<TimeSlot> freeSlots,
            Integer staffId,
            Integer serviceDuration,
            MoeWaitList record,
            BatchSSParams ssParams) {
        GetAvailableSlotParams getAvailableSlotParams = new GetAvailableSlotParams();
        getAvailableSlotParams.setTimeSlots(freeSlots);
        getAvailableSlotParams.setServiceDuration(serviceDuration);
        getAvailableSlotParams.setNeedCount(smartScheduleService.COUNT_FOR_AVAILABLE_TIME_SLOT_NEED);
        getAvailableSlotParams.setLocationId(record.getId());
        if (ssParams.getIsSmartScheduleEnable()
                && ssParams.getStaffSsSettingMap().get(staffId) != null) {
            getAvailableSlotParams.setDrivingRule(
                    ssParams.getStaffSsSettingMap().get(staffId).getDrivingRule());
        }
        getAvailableSlotParams.setDriverTimeCalculator(googleMapService::calculateAvailableTimeStraightDistance);
        getAvailableSlotParams.setDistanceScaleFactor(DEFAULT_DRIVER_TIME_SCALE_FACTOR);
        return getAvailableSlotParams;
    }

    Comparator<WaitListVO> getComparator(SortParams sort) {
        Comparator<WaitListVO> comparator;
        if (sort.property() == PropertyEnum.ticket_price) {
            comparator = Comparator.comparing(k -> k.getPrice() == null ? new BigDecimal(0) : k.getPrice());
        } else {
            comparator = Comparator.comparing(WaitListVO::getCreateTime);
        }
        if (sort.order() != OrderEnum.asc) {
            comparator = comparator.reversed();
        }
        return comparator;
    }

    MoeWaitList getWaitListById(Long businessId, Long id) {
        return moeWaitListMapper.selectById(businessId, id);
    }

    public MoeWaitList getWaitListByAppointment(Long businessId, Long appointmentId) {
        return moeWaitListMapper.selectByAppointmentId(businessId, appointmentId);
    }

    public List<MoeWaitList> batchGetWaitListByAppointment(Long businessId, List<Long> appointmentIdList) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return List.of();
        }
        return moeWaitListMapper.selectByAppointmentIds(businessId, appointmentIdList);
    }

    public List<MoeWaitList> batchGetWaitListByAppointmentV2(List<Long> appointmentIdList) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return List.of();
        }
        return moeWaitListMapper.selectByAppointmentIdsV2(appointmentIdList);
    }

    public List<MoeWaitList> queryWaitList(
            Long companyId, Long businessId, LocalDateTime createStartTime, LocalDateTime createEndTime, String order) {
        return moeWaitListMapper.query(companyId, businessId, null, null, createStartTime, createEndTime, order);
    }

    String getBusinessTimeZone(Long businessId) {
        return batchGetBusinessTimeZone(List.of(businessId)).get(businessId.intValue());
    }

    Map<Integer, String> batchGetBusinessTimeZone(List<Long> businessIdList) {
        Map<Integer, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(businessIdList)) {
            return result;
        }
        Map<Integer, BusinessDateTimeDTO> businessInfoList = iBusinessBusinessClient.listBusinessDateTime(
                businessIdList.stream().map(Long::intValue).toList());
        return businessInfoList.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, k -> k.getValue().getTimezoneName()));
    }

    List<Integer> getStaffShowOnCalendar(Long businessId, Long tokenStaffId) {
        // 获取 staff
        List<MoeStaffDto> staffs =
                iBusinessStaffClient.getStaffListForCalendar(businessId.intValue(), tokenStaffId.intValue());
        List<Integer> result = new ArrayList<>();
        staffs.stream()
                .sorted(Comparator.comparing(MoeStaffDto::getSort).reversed())
                .forEach(k -> result.add(k.getId()));
        return result;
    }

    List<Integer> getStaffDeleted(Long businessId) {
        List<MoeStaffDto> staffs = iBusinessStaffClient.getStaffListByBusinessId(businessId.intValue(), true);
        List<Integer> result = new ArrayList<>();
        staffs.stream()
                .filter(k -> !DeleteStatusEnum.STATUS_NORMAL.equals(k.getStatus()))
                .forEach(k -> result.add(k.getId()));
        return result;
    }

    /**
     * 检查是否所有 petDetail 都有确定的 service
     * @param petMoeDetailMap <petId, List<MoeGroomingPetDetail>>
     */
    boolean isAllPetServiceValid(Map<Integer, List<MoeGroomingPetDetail>> petMoeDetailMap) {
        if (CollectionUtils.isEmpty(petMoeDetailMap)) {
            return false;
        }
        return petMoeDetailMap.values().stream()
                .flatMap(Collection::stream)
                .allMatch(k -> k.getServiceId() != null && k.getServiceId() > 0);
    }

    WaitListVO makeWaitListVO(
            MoeWaitList waitList,
            MoeGroomingAppointment appointment,
            MoeGroomingNote moeGroomingNote,
            GroomingCalenderCustomerInfo customerInfo,
            Map<Integer, List<MoeGroomingPetDetail>> petServiceMap,
            List<CertainAreaDTO> serviceAreas,
            Map<Integer, MoeGroomingService> serviceMap,
            Map<Integer, CustomerPetDetailDTO> petDetailMap,
            OBPrepayDetailDTO prepayDetail,
            Map<Long, Boolean> availableMap,
            Map<Integer, MoeStaffDto> staffInfo,
            Map<Long, BusinessCustomerServiceHelper.CustomerHasRequestDTO> customerHasRequestDTOMap) {

        WaitListVO waitListDTO = new WaitListVO();
        waitListDTO.setId(waitList.getId());
        if (WaitListStatusEnum.APPTANDWAITLIST.equals(appointment.getWaitListStatus())) {
            waitListDTO.setAppointmentId(waitList.getAppointmentId());
        }
        waitListDTO.setPrepay(PrepayMapper.INSTANCE.dto2vo(prepayDetail));
        waitListDTO.setIsAvailable(availableMap.get(waitList.getId()));
        waitListDTO.setDatePreference(DatePreferenceMapper.INSTANCE.toDto(waitList.getDatePreference()));
        waitListDTO.setTimePreference(TimePreferenceMapper.INSTANCE.toDto(waitList.getTimePreference()));
        waitListDTO.setStaffPreference(staffPreferencePO2DTO(waitList.getStaffPreference(), staffInfo));
        waitListDTO.setValidFrom(waitList.getValidFrom());
        waitListDTO.setValidTill(waitList.getValidTill());
        waitListDTO.setCreateTime(waitList.getCreatedAt());
        waitListDTO.setTicketComment(moeGroomingNote == null ? "" : moeGroomingNote.getNote());
        waitListDTO.setCertainAreaList(serviceAreas);

        if (customerInfo != null) {
            waitListDTO.setCustomerInfo(new CustomerVO() {
                {
                    setId(customerInfo.getCustomerId());
                    setFirstName(customerInfo.getCustomerFirstName());
                    setLastName(customerInfo.getCustomerLastName());
                    setClientColor(customerInfo.getClientColor());
                    setAvatarPath(customerInfo.getAvatarPath());
                    setPhoneNumber(customerInfo.getClientPhoneNumber());
                    setIsDeleted(!DeleteStatusEnum.STATUS_NORMAL.equals(customerInfo.getStatus()));
                }
            });

            var hasRequestDTO =
                    customerHasRequestDTOMap.get(customerInfo.getCustomerId().longValue());
            waitListDTO.setHasRequestUpdate(hasRequestDTO != null && hasRequestDTO.hasRequestUpdate());
        }
        if (CollectionUtils.isEmpty(petServiceMap)) {
            waitListDTO.setPetList(List.of());
            return waitListDTO;
        }
        if (isAllPetServiceValid(petServiceMap)) {
            waitListDTO.setPrice(petServiceMap.values().stream()
                    .flatMap(List::stream)
                    .map(MoeGroomingPetDetail::getServicePrice)
                    .reduce(new BigDecimal(0), BigDecimal::add));
            waitListDTO.setDuration(moePetDetailService.calculateServiceDuration(
                    petServiceMap.values().stream().flatMap(List::stream).toList()));
        }
        waitListDTO.setPetList(petServiceMap.entrySet().stream()
                .map(k -> makePetServiceVO(petDetailMap.get(k.getKey()), k.getValue(), serviceMap))
                .toList());
        return waitListDTO;
    }

    PetServiceVO makePetServiceVO(
            CustomerPetDetailDTO petDetail,
            List<MoeGroomingPetDetail> petServices,
            Map<Integer, MoeGroomingService> serviceMap) {
        PetServiceVO petServiceDTO = new PetServiceVO();
        // 脏数据兼容
        if (petDetail != null) {
            petServiceDTO.setPetId(petDetail.getPetId());
            petServiceDTO.setPetName(petDetail.getPetName() == null ? "" : petDetail.getPetName());
            petServiceDTO.setPetTypeId(petDetail.getPetTypeId());
            petServiceDTO.setBreed(petDetail.getBreed());
            petServiceDTO.setAvatarPath(petDetail.getAvatarPath());
        } else {
            petServiceDTO.setPetId(0);
            petServiceDTO.setPetName("");
            petServiceDTO.setPetTypeId(0);
            petServiceDTO.setBreed("");
        }
        petServices = petServices.stream().filter(k -> k.getServiceId() > 0).toList();
        if (CollectionUtils.isEmpty(petServices)) {
            petServiceDTO.setServiceList(List.of());
        } else {
            petServiceDTO.setServiceList(petServices.stream()
                    .map(k -> ServiceAndOperationVO.builder()
                            .serviceId(k.getServiceId())
                            .serviceName(
                                    serviceMap.get(k.getServiceId()) == null
                                            ? ""
                                            : serviceMap.get(k.getServiceId()).getName())
                            .servicePrice(k.getServicePrice())
                            .build())
                    .toList());
        }
        return petServiceDTO;
    }

    List<PetParams> waitListPetParams2PetParams(List<WaitListPetServiceParam> wPetParams) {
        List<PetParams> result = new ArrayList<>();
        wPetParams.forEach(k -> {
            List<ServiceAndOperationParams> serviceList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(k.serviceList())) {
                k.serviceList()
                        .forEach(j -> serviceList.add(ServiceAndOperationParams.builder()
                                .serviceId(j.serviceId())
                                .build()));
            }
            result.add(new PetParams(k.petId(), serviceList, null)); // TODO boarding: @bob 需要检查一下这里
        });
        return result;
    }

    Staff getStaffDto(MoeStaffDto staffDto) {
        if (staffDto == null) {
            return null;
        }
        return Staff.builder()
                .id(staffDto.getId())
                .firstName(staffDto.getFirstName())
                .lastName(staffDto.getLastName())
                .avatarPath(staffDto.getAvatarPath())
                .build();
    }

    public StaffPreferenceDTO staffPreferencePO2DTO(
            StaffPreferencePO staffPreferencePO, Map<Integer, MoeStaffDto> staffInfo) {
        if (staffPreferencePO == null) {
            return null;
        }
        StaffPreferenceDTO staffPreference = new StaffPreferenceDTO();
        staffPreference.setIsAnyone(staffPreferencePO.getIsAnyone());
        if (!CollectionUtils.isEmpty(staffPreferencePO.getStaffIds())) {
            staffPreference.setStaffList(staffPreferencePO.getStaffIds().stream()
                    .map(staffInfo::get)
                    .map(this::getStaffDto)
                    .toList());
        }
        return staffPreference;
    }
}
