package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IBookOnlineBusinessServiceBase;
import com.moego.server.grooming.dto.BookOnlineLandingPageDTO;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class BookOnlineBusinessServer extends IBookOnlineBusinessServiceBase {

    private final OBLandingPageConfigService landingPageConfigService;

    @Override
    public BookOnlineLandingPageDTO getLandingPageConfig(Long businessId) {
        var config = landingPageConfigService.selectByBusinessId(businessId.intValue());
        return new BookOnlineLandingPageDTO(
                config.getBusinessId().longValue(), config.getCompanyId(), config.getUrlDomainName());
    }
}
