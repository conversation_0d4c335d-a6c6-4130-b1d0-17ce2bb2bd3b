package com.moego.server.grooming.mapperbean;

import com.moego.server.retail.dto.PackageInfoDto.Service;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MoeGroomingPackageServiceExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public MoeGroomingPackageServiceExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> servicesCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            servicesCriteria = new ArrayList<>();
        }

        public List<Criterion> getServicesCriteria() {
            return servicesCriteria;
        }

        protected void addServicesCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            servicesCriteria.add(new Criterion(
                    condition, value, "com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler"));
            allCriteria = null;
        }

        protected void addServicesCriterion(
                String condition, List<Service> value1, List<Service> value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            servicesCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.PackageServiceTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0 || servicesCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(servicesCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPackageIdIsNull() {
            addCriterion("package_id is null");
            return (Criteria) this;
        }

        public Criteria andPackageIdIsNotNull() {
            addCriterion("package_id is not null");
            return (Criteria) this;
        }

        public Criteria andPackageIdEqualTo(Integer value) {
            addCriterion("package_id =", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdNotEqualTo(Integer value) {
            addCriterion("package_id <>", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdGreaterThan(Integer value) {
            addCriterion("package_id >", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_id >=", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdLessThan(Integer value) {
            addCriterion("package_id <", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdLessThanOrEqualTo(Integer value) {
            addCriterion("package_id <=", value, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdIn(List<Integer> values) {
            addCriterion("package_id in", values, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdNotIn(List<Integer> values) {
            addCriterion("package_id not in", values, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdBetween(Integer value1, Integer value2) {
            addCriterion("package_id between", value1, value2, "packageId");
            return (Criteria) this;
        }

        public Criteria andPackageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("package_id not between", value1, value2, "packageId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNull() {
            addCriterion("service_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNotNull() {
            addCriterion("service_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdEqualTo(Integer value) {
            addCriterion("service_id =", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotEqualTo(Integer value) {
            addCriterion("service_id <>", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThan(Integer value) {
            addCriterion("service_id >", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_id >=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThan(Integer value) {
            addCriterion("service_id <", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_id <=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIn(List<Integer> values) {
            addCriterion("service_id in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotIn(List<Integer> values) {
            addCriterion("service_id not in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdBetween(Integer value1, Integer value2) {
            addCriterion("service_id between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_id not between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityIsNull() {
            addCriterion("total_quantity is null");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityIsNotNull() {
            addCriterion("total_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityEqualTo(Integer value) {
            addCriterion("total_quantity =", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityNotEqualTo(Integer value) {
            addCriterion("total_quantity <>", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityGreaterThan(Integer value) {
            addCriterion("total_quantity >", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_quantity >=", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityLessThan(Integer value) {
            addCriterion("total_quantity <", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("total_quantity <=", value, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityIn(List<Integer> values) {
            addCriterion("total_quantity in", values, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityNotIn(List<Integer> values) {
            addCriterion("total_quantity not in", values, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityBetween(Integer value1, Integer value2) {
            addCriterion("total_quantity between", value1, value2, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andTotalQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("total_quantity not between", value1, value2, "totalQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityIsNull() {
            addCriterion("remaining_quantity is null");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityIsNotNull() {
            addCriterion("remaining_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityEqualTo(Integer value) {
            addCriterion("remaining_quantity =", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityNotEqualTo(Integer value) {
            addCriterion("remaining_quantity <>", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityGreaterThan(Integer value) {
            addCriterion("remaining_quantity >", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("remaining_quantity >=", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityLessThan(Integer value) {
            addCriterion("remaining_quantity <", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("remaining_quantity <=", value, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityIn(List<Integer> values) {
            addCriterion("remaining_quantity in", values, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityNotIn(List<Integer> values) {
            addCriterion("remaining_quantity not in", values, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityBetween(Integer value1, Integer value2) {
            addCriterion("remaining_quantity between", value1, value2, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andRemainingQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("remaining_quantity not between", value1, value2, "remainingQuantity");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceIsNull() {
            addCriterion("service_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceIsNotNull() {
            addCriterion("service_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceEqualTo(BigDecimal value) {
            addCriterion("service_unit_price =", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("service_unit_price <>", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("service_unit_price >", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_unit_price >=", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceLessThan(BigDecimal value) {
            addCriterion("service_unit_price <", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_unit_price <=", value, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceIn(List<BigDecimal> values) {
            addCriterion("service_unit_price in", values, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("service_unit_price not in", values, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_unit_price between", value1, value2, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServiceUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_unit_price not between", value1, value2, "serviceUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServicesIsNull() {
            addCriterion("services is null");
            return (Criteria) this;
        }

        public Criteria andServicesIsNotNull() {
            addCriterion("services is not null");
            return (Criteria) this;
        }

        public Criteria andServicesEqualTo(List<Service> value) {
            addServicesCriterion("services =", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesNotEqualTo(List<Service> value) {
            addServicesCriterion("services <>", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesGreaterThan(List<Service> value) {
            addServicesCriterion("services >", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesGreaterThanOrEqualTo(List<Service> value) {
            addServicesCriterion("services >=", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesLessThan(List<Service> value) {
            addServicesCriterion("services <", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesLessThanOrEqualTo(List<Service> value) {
            addServicesCriterion("services <=", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesLike(List<Service> value) {
            addServicesCriterion("services like", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesNotLike(List<Service> value) {
            addServicesCriterion("services not like", value, "services");
            return (Criteria) this;
        }

        public Criteria andServicesIn(List<List<Service>> values) {
            addServicesCriterion("services in", values, "services");
            return (Criteria) this;
        }

        public Criteria andServicesNotIn(List<List<Service>> values) {
            addServicesCriterion("services not in", values, "services");
            return (Criteria) this;
        }

        public Criteria andServicesBetween(List<Service> value1, List<Service> value2) {
            addServicesCriterion("services between", value1, value2, "services");
            return (Criteria) this;
        }

        public Criteria andServicesNotBetween(List<Service> value1, List<Service> value2) {
            addServicesCriterion("services not between", value1, value2, "services");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_package_service
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
