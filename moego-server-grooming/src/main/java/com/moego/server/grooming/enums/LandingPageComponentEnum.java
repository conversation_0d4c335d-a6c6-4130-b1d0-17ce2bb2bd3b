package com.moego.server.grooming.enums;

import com.moego.common.enums.FeatureConst;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Getter
@AllArgsConstructor
public enum LandingPageComponentEnum {
    GALLERY(LandingPageComponentEnum.COMPONENT_GALLERY, true),
    ABOUT_US(LandingPageComponentEnum.COMPONENT_ABOUT_US, true),
    BUSINESS_HOURS(LandingPageComponentEnum.COMPONENT_BUSINESS_HOURS, true),
    SHOWCASE(LandingPageComponentEnum.COMPONENT_SHOWCASE, true),
    AMENITIES(LandingPageComponentEnum.COMPONENT_AMENITIES, true),
    WELCOME_PAGE_MESSAGE(LandingPageComponentEnum.COMPONENT_WELCOME_PAGE_MESSAGE, true),
    // see https://moego.atlassian.net/browse/ERP-3731
    CONTACT(LandingPageComponentEnum.COMPONENT_CONTACT, true),
    // see https://moego.atlassian.net/browse/ERP-3853
    ADDRESS(LandingPageComponentEnum.COMPONENT_ADDRESS, true),
    // see https://moego.atlassian.net/browse/ERP-8222
    // 这个配置只给 mobile 和 hybrid 使用，salon 不下发这个值
    SERVICE_AREA(LandingPageComponentEnum.COMPONENT_SERVICE_AREA, false),
    // see https://moego.atlassian.net/browse/ERP-8136
    OB_CONFIG_TEAMS(LandingPageComponentEnum.COMPONENT_OB_CONFIG_TEAMS, false),
    OB_CONFIG_CLIENT_REVIEWS(LandingPageComponentEnum.COMPONENT_OB_CONFIG_CLIENT_REVIEWS, false);

    public static final String COMPONENT_GALLERY = "gallery";
    public static final String COMPONENT_ABOUT_US = "aboutUs";
    public static final String COMPONENT_BUSINESS_HOURS = "businessHours";
    public static final String COMPONENT_SHOWCASE = "showcase";
    public static final String COMPONENT_AMENITIES = FeatureConst.FC_AMENITIES;
    public static final String COMPONENT_WELCOME_PAGE_MESSAGE = "welcomePageMessage";
    public static final String COMPONENT_CONTACT = "contact";
    public static final String COMPONENT_ADDRESS = "address";
    public static final String COMPONENT_SERVICE_AREA = FeatureConst.FC_SERVICE_AREA;
    public static final String COMPONENT_OB_CONFIG_TEAMS = FeatureConst.FC_OB_CONFIG_TEAMS;
    public static final String COMPONENT_OB_CONFIG_CLIENT_REVIEWS = FeatureConst.FC_OB_CONFIG_CLIENT_REVIEWS;

    /**
     * Page component name, contains storefront and booking experience
     */
    private final String component;

    /**
     * Default value
     */
    private final boolean enable;

    public static LandingPageComponentEnum getEnumByComponent(String component) {
        for (LandingPageComponentEnum value : values()) {
            if (Objects.equals(value.getComponent(), component)) {
                return value;
            }
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Landing page component not found");
    }
}
