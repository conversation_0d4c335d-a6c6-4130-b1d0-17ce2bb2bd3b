package com.moego.server.grooming.mapstruct;

import static com.moego.server.grooming.web.AbandonedScheduleMessageSettingController.UpdateAbandonedScheduleMessageSettingParam;

import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.AbandonedScheduleMessageSettingDTO;
import com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting;
import com.moego.server.grooming.web.vo.AbandonedScheduleMessageSettingVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(imports = {JsonUtil.class, TypeRef.class})
public interface AbandonedScheduleMessageSettingConverter {

    AbandonedScheduleMessageSettingConverter INSTANCE =
            Mappers.getMapper(AbandonedScheduleMessageSettingConverter.class);

    @Mapping(target = "clientTypes", expression = "java(JsonUtil.toBean(entity.getClientTypes(), new TypeRef<>(){}))")
    @Mapping(
            target = "abandonedSteps",
            expression = "java(JsonUtil.toBean(entity.getAbandonedSteps(), new TypeRef<>(){}))")
    @Mapping(target = "onTypeDays", expression = "java(JsonUtil.toBean(entity.getOnTypeDays(), new TypeRef<>(){}))")
    AbandonedScheduleMessageSettingDTO entityToDTO(AbandonedScheduleMessageSetting entity);

    @Mapping(target = "clientTypes", expression = "java(JsonUtil.toBean(entity.getClientTypes(), new TypeRef<>(){}))")
    @Mapping(
            target = "abandonedSteps",
            expression = "java(JsonUtil.toBean(entity.getAbandonedSteps(), new TypeRef<>(){}))")
    @Mapping(target = "onTypeDays", expression = "java(JsonUtil.toBean(entity.getOnTypeDays(), new TypeRef<>(){}))")
    @Mapping(target = "waitForTypeHour", expression = "java(entity.getWaitForTypeMinute() / 60)")
    AbandonedScheduleMessageSettingVO entityToVO(AbandonedScheduleMessageSetting entity);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "businessId", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "enabledAt", ignore = true)
    @Mapping(
            target = "clientTypes",
            expression = "java(param.clientTypes() != null ? JsonUtil.toJson(param.clientTypes()) : null)")
    @Mapping(
            target = "abandonedSteps",
            expression = "java(param.abandonedSteps() != null ? JsonUtil.toJson(param.abandonedSteps()) : null)")
    @Mapping(target = "waitForTypeMinute", expression = "java(waitForTypeMinute(param))")
    @Mapping(
            target = "onTypeDays",
            expression = "java(param.onTypeDays() != null ? JsonUtil.toJson(param.onTypeDays()) : null)")
    AbandonedScheduleMessageSetting updateParamToEntity(UpdateAbandonedScheduleMessageSettingParam param);

    default Integer waitForTypeMinute(UpdateAbandonedScheduleMessageSettingParam param) {
        if (param.waitForTypeMinute() != null) {
            return param.waitForTypeMinute();
        } else if (param.waitForTypeHour() != null) {
            return param.waitForTypeHour() * 60;
        }
        return null;
    }
}
