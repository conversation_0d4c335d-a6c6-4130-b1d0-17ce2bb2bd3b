package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.dto.calendarcard.CalendarCardDTO;
import com.moego.server.grooming.dto.calendarcard.MonthlyViewDTO;
import com.moego.server.grooming.enums.AppointmentPermissionEnums;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import com.moego.server.grooming.service.CalendarCardService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.web.params.calendar.CardRescheduleParams;
import com.moego.server.grooming.web.params.calendar.QueryCardListParams;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/grooming/calendar/card")
@RequiredArgsConstructor
public class CalendarCardController {

    private final CalendarCardService calendarCardService;
    private final MigrateHelper migrateHelper;
    private final MoeGroomingAppointmentService appointmentService;

    @PutMapping("/reschedule")
    @Auth(AuthType.BUSINESS)
    public Boolean reschedule(AuthContext authContext, @RequestBody CardRescheduleParams params) {
        Integer businessId = authContext.getBusinessId();
        if (migrateHelper.isMigrate(authContext.companyId())) {
            if (params.cardType().equals(CardTypeEnum.BLOCK)) {
                businessId = appointmentService.checkPermissionToAppointment(
                        authContext.companyId(),
                        authContext.staffId(),
                        params.appointmentId().longValue(),
                        AppointmentPermissionEnums.CREATE_AND_EDIT_BLOCK);
            } else {
                businessId = appointmentService.checkPermissionToAppointment(
                        authContext.companyId(),
                        authContext.staffId(),
                        params.appointmentId().longValue(),
                        AppointmentPermissionEnums.UPDATE);
            }
        }
        return calendarCardService.reschedule(params.toBuilder()
                .businessId(businessId)
                .tokenStaffId(authContext.getStaffId())
                .build());
    }

    @GetMapping("/day/list")
    @Auth(AuthType.BUSINESS)
    public List<CalendarCardDTO> getDayList(
            AuthContext authContext,
            @RequestParam String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Boolean isWaitingList,
            @RequestParam(required = false, defaultValue = "true") boolean filterNoStartTime,
            @RequestParam(required = false, defaultValue = "true") boolean filterNoStaff) {
        if (Objects.isNull(endDate)) endDate = startDate;
        QueryCardListParams QueryCardDayListParams = new QueryCardListParams(
                authContext.getBusinessId(),
                authContext.getStaffId(),
                startDate,
                endDate,
                isWaitingList,
                filterNoStartTime,
                filterNoStaff);
        return calendarCardService.queryCalendarCardByDate(QueryCardDayListParams);
    }

    @GetMapping("/monthly/list")
    @Auth(AuthType.BUSINESS)
    public List<MonthlyViewDTO> getMonthlyList(
            AuthContext authContext,
            @RequestParam String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Boolean isWaitingList,
            @RequestParam(required = false, defaultValue = "true") boolean filterNoStartTime,
            @RequestParam(required = false, defaultValue = "true") boolean filterNoStaff) {
        if (Objects.isNull(endDate)) endDate = startDate;
        QueryCardListParams QueryCardDayListParams = new QueryCardListParams(
                authContext.getBusinessId(),
                authContext.getStaffId(),
                startDate,
                endDate,
                isWaitingList,
                filterNoStartTime,
                filterNoStaff);

        return calendarCardService.queryAppointmentForMonthlyView(QueryCardDayListParams);
    }
}
