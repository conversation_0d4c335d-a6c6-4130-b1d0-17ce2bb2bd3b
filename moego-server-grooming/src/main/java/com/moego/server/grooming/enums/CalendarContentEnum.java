package com.moego.server.grooming.enums;

public enum CalendarContentEnum {
    CALENDAR_NAME("MoeGo - {staffName}"),
    EVENT_TITLE("{customerName} {petAmount} by {staffName}"),
    EVENT_DESCRIPTION(
            "{petName}({petBreed}){service}\n\n** Please make changes to this appointment in the MoeGo calendar. Any changes made here will be overwritten during the next sync.");

    private String content;

    CalendarContentEnum(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }
}
