package com.moego.server.grooming.service.client;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.params.PageQuery;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;
import com.moego.server.grooming.service.dto.client.ListCommonApptDTO;
import com.moego.server.grooming.web.vo.client.ClientApptDetailVO;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Service
@AllArgsConstructor
public class ClientHistoryApptService implements IBaseClientApptService {

    private final AppointmentMapperProxy appointmentMapper;

    private final ClientApptService clientApptService;

    private final IBusinessBusinessClient businessClient;

    @Override
    public Page<MoeGroomingAppointment> getApptList(ListClientApptDTO listClientApptDTO, PageQuery pageQuery) {
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap = listClientApptDTO.getBusinessDateTimeDTOMap();
        List<ListCommonApptDTO> commonApptDTOList = listClientApptDTO.getCustomerIdDTOList().stream()
                .filter(customerIdDTO -> businessDateTimeDTOMap.containsKey(customerIdDTO.getBusinessId()))
                .map(customerIdDTO -> {
                    BusinessDateTimeDTO dateTimeDTO = businessDateTimeDTOMap.get(customerIdDTO.getBusinessId());
                    ListCommonApptDTO commonApptDTO = new ListCommonApptDTO()
                            .setIsHistory(Boolean.TRUE)
                            .setIsDeprecate(GroomingAppointmentEnum.IS_DEPRECATE_FALSE.byteValue())
                            .setIsBlock(ClientApptConst.IS_BLOCK_FALSE)
                            .setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB)
                            .setIsWaitingList(GroomingAppointmentEnum.NOT_WAITING_LIST)
                            .setCurrentDate(dateTimeDTO.getCurrentDate())
                            .setCurrentMinutes(dateTimeDTO.getCurrentMinutes());
                    if (Objects.nonNull(listClientApptDTO.getStatus())) {
                        commonApptDTO.setStatus(listClientApptDTO.getStatus());
                    } else {
                        commonApptDTO.setStatusList(AppointmentStatusSet.ACTIVE_STATUS_SET);
                    }
                    commonApptDTO.setBusinessId(customerIdDTO.getBusinessId());
                    commonApptDTO.setCustomerId(customerIdDTO.getCustomerId());
                    commonApptDTO.setCompanyId(customerIdDTO.getCompanyId());
                    return commonApptDTO;
                })
                .collect(Collectors.toList());
        return PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())
                .doSelectPage(() -> appointmentMapper.listClientAppt(commonApptDTOList, pageQuery));
    }

    @Override
    public Byte getApptType() {
        return ClientApptConst.APPT_TYPE_HISTORY;
    }

    public ClientApptDetailVO getLastFinishedAppt(ListClientApptDTO listClientApptDTO, PageQuery pageQuery) {
        listClientApptDTO.setStatus(AppointmentStatusEnum.FINISHED.getValue());
        List<MoeGroomingAppointment> apptList = getApptList(listClientApptDTO, pageQuery);
        if (CollectionUtils.isEmpty(apptList)) {
            return new ClientApptDetailVO();
        }
        String bookingId = String.valueOf(apptList.get(0).getId());
        return clientApptService.getApptDetail(bookingId, listClientApptDTO.getCustomerIdDTOList());
    }

    public Integer getLastFinishedApptId(List<BaseBusinessCustomerIdDTO> linkCustomers) {
        ListClientApptDTO listClientApptDTO = new ListClientApptDTO();
        listClientApptDTO.setCustomerIdDTOList(linkCustomers);
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap =
                businessClient.listBusinessDateTime(linkCustomers.stream()
                        .map(BaseBusinessCustomerIdDTO::getBusinessId)
                        .toList());
        listClientApptDTO.setBusinessDateTimeDTOMap(businessDateTimeDTOMap);
        PageQuery pageQuery = new PageQuery()
                .setPageNum(1)
                .setPageSize(1)
                .setSortList(Arrays.asList(
                        new PageQuery.SortQuery().setSortBy("appointment_date").setOrder(PageQuery.OrderEnum.desc),
                        new PageQuery.SortQuery()
                                .setSortBy("appointment_start_time")
                                .setOrder(PageQuery.OrderEnum.desc)));
        listClientApptDTO.setStatus(AppointmentStatusEnum.FINISHED.getValue());
        List<MoeGroomingAppointment> apptList = getApptList(listClientApptDTO, pageQuery);
        return Optional.ofNullable(apptList)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .map(list -> list.get(0).getId())
                .orElse(null);
    }
}
