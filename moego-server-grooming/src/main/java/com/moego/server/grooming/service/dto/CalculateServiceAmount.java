package com.moego.server.grooming.service.dto;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import lombok.Builder;

@Builder(toBuilder = true)
public record CalculateServiceAmount(
        int serviceId,
        BigDecimal serviceAmount,
        BigDecimal taxRate,
        int petId,
        ServiceOverrideType priceOverrideType,
        @Nullable String date) {}
