package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncAccount;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncAccountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    MoeQbSyncAccount selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncAccount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncAccount record);

    MoeQbSyncAccount selectByBusinessIdAndRealmId(
            @Param("businessId") Integer businessId, @Param("realmId") String realmId);

    MoeQbSyncAccount selectByBusinessRealmIdAndAccountType(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("accType") Integer accType);
}
