package com.moego.server.grooming.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.agreement.v1.AgreementWithRecentRecordsView;
import com.moego.idl.models.agreement.v1.ServiceType;
import com.moego.idl.models.agreement.v1.SignedType;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc;
import com.moego.idl.service.agreement.v1.BatchGetRecentSignedAgreementListRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AgreementHelper {
    private final AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub agreementRecordClient;

    public Map<Long, List<AgreementWithRecentRecordsView>> batchGetCustomerRecentSignedAgreements(
            List<Long> customerIds, Long businessId) {
        customerIds =
                customerIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        List<Set<Long>> sets = CommonUtil.splitListByItemNum(customerIds, 100);

        Map<Long, List<AgreementWithRecentRecordsView>> result = new HashMap<>();
        sets.forEach(set -> {
            var req = BatchGetRecentSignedAgreementListRequest.newBuilder()
                    .setBusinessId(businessId)
                    .addAllCustomerIds(set)
                    .addSignedType(SignedType.SIGNED_TYPE_BY_CUSTOMER_SIGNED)
                    .setServiceTypes(ServiceType.SERVICE_TYPE_GROOMING_VALUE)
                    .build();
            var resp =
                    agreementRecordClient.batchGetRecentSignedAgreementList(req).getCustomerRecentAgreementMap();

            resp.forEach((k, v) -> result.put(k, v.getValuesList()));
        });

        return result;
    }
}
