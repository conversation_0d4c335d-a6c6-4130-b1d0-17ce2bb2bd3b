package com.moego.server.grooming.convert;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.protobuf.Timestamp;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestionSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.NextAppointmentDateFormatType;
import com.moego.backend.proto.fulfillment.v1.QuestionCategory;
import com.moego.backend.proto.fulfillment.v1.QuestionType;
import com.moego.backend.proto.fulfillment.v1.SendMethod;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.backend.proto.pet.v1.Pet;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.idl.models.appointment.v1.DailyReportSendLogMigrateDef;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class, StructConverter.class})
public interface ReportCardMigrateConverter {
    ReportCardMigrateConverter INSTANCE = Mappers.getMapper(ReportCardMigrateConverter.class);

    default FulfillmentReportTemplateSync convertTemplate(MoeGroomingReportTemplate source) {
        if (source == null) {
            return null;
        }

        FulfillmentReportTemplateSync.Builder builder =
                FulfillmentReportTemplateSync.newBuilder().setCareType(CareCategory.GROOMING);

        if (source.getCompanyId() != null) {
            builder.setCompanyId(source.getCompanyId());
        }
        if (source.getBusinessId() != null) {
            builder.setBusinessId(source.getBusinessId());
        }
        if (source.getThankYouMessage() != null) {
            builder.setThankYouMessage(source.getThankYouMessage());
        }
        if (source.getThemeColor() != null) {
            builder.setThemeColor(source.getThemeColor());
        }
        if (source.getLightThemeColor() != null) {
            builder.setLightThemeColor(source.getLightThemeColor());
        }
        if (source.getShowShowcase() != null) {
            builder.setShowShowcase(source.getShowShowcase());
        }
        if (source.getShowOverallFeedback() != null) {
            builder.setShowOverallFeedback(source.getShowOverallFeedback());
        }
        if (source.getShowPetCondition() != null) {
            builder.setShowPetCondition(source.getShowPetCondition());
        }
        if (source.getShowServiceStaffName() != null) {
            builder.setShowServiceStaffName(source.getShowServiceStaffName());
        }
        if (source.getShowNextAppointment() != null) {
            builder.setShowNextAppointment(source.getShowNextAppointment());
        }
        if (source.getShowReviewBooster() != null) {
            builder.setShowReviewBooster(source.getShowReviewBooster());
        }
        if (source.getShowYelpReview() != null) {
            builder.setShowYelpReview(source.getShowYelpReview());
        }
        if (source.getShowGoogleReview() != null) {
            builder.setShowGoogleReview(source.getShowGoogleReview());
        }
        if (source.getShowFacebookReview() != null) {
            builder.setShowFacebookReview(source.getShowFacebookReview());
        }
        if (source.getTitle() != null) {
            builder.setTitle(source.getTitle());
        }
        if (source.getUpdateBy() != null) {
            builder.setUpdateBy(source.getUpdateBy());
        }

        if (source.getNextAppointmentDateFormatType() != null) {
            builder.setNextAppointmentDateFormatType(NextAppointmentDateFormatType.forNumber(
                    source.getNextAppointmentDateFormatType().intValue()));
        }

        if (source.getThemeCode() != null) {
            builder.setThemeCode(source.getThemeCode());
        }

        if (source.getLastPublishTime() != null) {
            builder.setLastPublishTime(dateToTimestamp(source.getLastPublishTime()));
        }

        if (source.getCreateTime() != null) {
            builder.setCreateTime(dateToTimestamp(source.getCreateTime()));
        }

        if (source.getUpdateTime() != null) {
            builder.setUpdateTime(dateToTimestamp(source.getUpdateTime()));
        }

        return builder.build();
    }

    default List<FulfillmentReportTemplateSync> convertTemplates(List<MoeGroomingReportTemplate> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportTemplateSync> result = new ArrayList<>(sourceList.size());
        for (MoeGroomingReportTemplate source : sourceList) {
            FulfillmentReportTemplateSync converted = convertTemplate(source);
            if (converted != null) {
                result.add(converted);
            }
        }
        return result;
    }

    default FulfillmentReportSync.Builder convertReport(MoeGroomingReport source) {
        if (source == null) {
            return null;
        }

        FulfillmentReportSync.Builder builder =
                FulfillmentReportSync.newBuilder().setCareType(CareCategory.GROOMING);

        // 基本字段映射
        if (source.getBusinessId() != null) {
            builder.setBusinessId(source.getBusinessId());
        }
        if (source.getGroomingId() != null) {
            builder.setAppointmentId(source.getGroomingId());
        }
        if (source.getCustomerId() != null) {
            builder.setCustomerId(source.getCustomerId());
        }
        if (source.getPetId() != null) {
            builder.setPetId(source.getPetId());
        }
        if (source.getCompanyId() != null) {
            builder.setCompanyId(source.getCompanyId());
        }
        if (source.getPetTypeId() != null) {
            builder.setPetTypeId(source.getPetTypeId());
        }
        if (source.getThemeCode() != null) {
            builder.setThemeCode(source.getThemeCode());
        }
        if (source.getUuid() != null) {
            builder.setUuid(source.getUuid());
        }

        // JSON 字段
        if (source.getTemplateJson() != null) {
            builder.setTemplateJson(source.getTemplateJson());
        } else {
            builder.setTemplateJson("{}");
        }
        if (source.getContentJson() != null) {
            builder.setContentJson(transformContentJson(source.getContentJson()));
        } else {
            builder.setContentJson("{}");
        }

        // 状态字段
        if (source.getStatus() != null) {
            builder.setStatus(source.getStatus());
        }

        // 其他字段
        if (source.getLinkOpenedCount() != null) {
            builder.setLinkOpenedCount(source.getLinkOpenedCount());
        }
        if (source.getUpdateBy() != null) {
            builder.setUpdateBy(source.getUpdateBy());
        }

        // 时间字段
        if (source.getTemplatePublishTime() != null) {
            builder.setTemplateVersion(dateToTimestamp(source.getTemplatePublishTime()));
        }
        if (source.getCreateTime() != null) {
            builder.setCreateTime(dateToTimestamp(source.getCreateTime()));
        }
        if (source.getUpdateTime() != null) {
            builder.setUpdateTime(dateToTimestamp(source.getUpdateTime()));
        }

        return builder;
    }

    default List<FulfillmentReportSync> convertReports(
            List<MoeGroomingReport> sourceReports, Map<Integer, String> appointmentDates) {
        if (sourceReports == null || sourceReports.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportSync> result = new ArrayList<>(sourceReports.size());
        for (MoeGroomingReport source : sourceReports) {
            FulfillmentReportSync.Builder converted = convertReport(source);
            String serviceDate = appointmentDates.get(source.getGroomingId());
            converted.setServiceDate(serviceDate == null ? "" : serviceDate);
            result.add(converted.build());
        }
        return result;
    }

    default FulfillmentReportSync.Builder convertDailyReport(
            DailyReportConfigMigrateDef source, Map<String, CareCategory> careTypeMap) {
        if (source == null) {
            return null;
        }

        FulfillmentReportSync.Builder builder = FulfillmentReportSync.newBuilder()
                .setCareType(careTypeMap.get(source.getPetId() + "_" + source.getAppointmentId()))
                .setPetTypeId(Pet.PetType.DOG_VALUE)
                .setThemeCode("Default");

        // 基本字段映射
        if (source.getBusinessId() != 0) {
            builder.setBusinessId((int) source.getBusinessId());
        }
        if (source.getAppointmentId() != 0) {
            builder.setAppointmentId((int) source.getAppointmentId()); // appointmentId 直接映射
        }
        if (source.getCustomerId() != 0) {
            builder.setCustomerId((int) source.getCustomerId());
        }
        if (source.getPetId() != 0) {
            builder.setPetId((int) source.getPetId());
        }
        if (source.getCompanyId() != 0) {
            builder.setCompanyId((int) source.getCompanyId());
        }
        if (!source.getUuid().isEmpty()) {
            builder.setUuid(source.getUuid());
        }

        // JSON 字段
        if (!source.getTemplateJson().isEmpty()) {
            builder.setContentJson(convertTypeNumberToString(source.getTemplateJson()));
        } else {
            builder.setContentJson("{}");
        }

        // 状态字段
        if (!source.getStatus().isEmpty()) {
            builder.setStatus(source.getStatus());
        }

        // 其他字段
        if (source.getUpdateBy() != 0) {
            builder.setUpdateBy((int) source.getUpdateBy());
        }

        // 时间字段
        if (source.hasCreateTime()) {
            builder.setCreateTime(source.getCreateTime());
        }
        if (source.hasUpdateTime()) {
            builder.setUpdateTime(source.getUpdateTime());
        }

        // 服务日期
        if (source.hasServiceDate()) {
            builder.setServiceDate(String.format(
                    "%04d-%02d-%02d",
                    source.getServiceDate().getYear(),
                    source.getServiceDate().getMonth(),
                    source.getServiceDate().getDay()));
        }
        return builder;
    }

    default List<FulfillmentReportSync> convertDailyReports(
            List<DailyReportConfigMigrateDef> sourceDailyReports, Map<String, CareCategory> careTypeMap) {
        if (sourceDailyReports == null || sourceDailyReports.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportSync> result = new ArrayList<>(sourceDailyReports.size());
        for (DailyReportConfigMigrateDef source : sourceDailyReports) {
            FulfillmentReportSync.Builder converted = convertDailyReport(source, careTypeMap);
            result.add(converted.build());
        }
        return result;
    }

    default FulfillmentReportQuestionSync convertQuestion(MoeGroomingReportQuestion source) {
        if (source == null) {
            return null;
        }

        FulfillmentReportQuestionSync.Builder builder =
                FulfillmentReportQuestionSync.newBuilder().setCareType(CareCategory.GROOMING);

        // 基本字段映射
        if (source.getBusinessId() != null) {
            builder.setBusinessId(source.getBusinessId());
        }
        if (source.getCompanyId() != null) {
            builder.setCompanyId(source.getCompanyId());
        }
        if (source.getKey() != null) {
            if ("comment".equals(source.getKey())) {
                builder.setKey("additional_note");
            } else if ("overall_feedback".equals(source.getKey())) {
                builder.setKey("how_did_it_go");
            } else {
                builder.setKey(source.getKey());
            }
        }
        if (source.getCategory() != null) {
            builder.setCategory(QuestionCategory.forNumber((int) source.getCategory()));
        }
        if (source.getType() != null) {
            builder.setType(QuestionType.valueOf(source.getType().trim().toUpperCase()));
        }
        if (source.getTitle() != null) {
            builder.setTitle(source.getTitle());
        }
        if (source.getSort() != null) {
            builder.setSort(source.getSort());
        }

        // JSON 字段
        if (source.getExtraJson() != null) {
            builder.setExtraJson(source.getExtraJson());
        } else {
            builder.setExtraJson("{}");
        }

        // 状态字段
        if (source.getRequired() != null) {
            builder.setIsRequired(source.getRequired());
        }
        if (source.getIsDefault() != null) {
            builder.setIsDefault(source.getIsDefault());
        }
        if (source.getTypeEditable() != null) {
            builder.setIsTypeEditable(source.getTypeEditable());
        }
        if (source.getTitleEditable() != null) {
            builder.setIsTitleEditable(source.getTitleEditable());
        }
        if (source.getOptionsEditable() != null) {
            builder.setIsOptionsEditable(source.getOptionsEditable());
        }

        // 时间字段
        if (source.getCreateTime() != null) {
            builder.setCreateTime(dateToTimestamp(source.getCreateTime()));
        }
        if (source.getUpdateTime() != null) {
            builder.setUpdateTime(dateToTimestamp(source.getUpdateTime()));
        }

        return builder.build();
    }

    default List<FulfillmentReportQuestionSync> convertQuestions(List<MoeGroomingReportQuestion> sourceQuestions) {
        if (sourceQuestions == null || sourceQuestions.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportQuestionSync> result = new ArrayList<>(sourceQuestions.size());
        for (MoeGroomingReportQuestion source : sourceQuestions) {
            FulfillmentReportQuestionSync converted = convertQuestion(source);
            if (converted != null) {
                result.add(converted);
            }
        }
        return result;
    }

    default FulfillmentReportSendRecordSync convertSendRecord(
            GroomingReportSendLogDTO source, Map<Integer, Integer> reportIdMapping) {
        if (source == null) {
            return null;
        }

        // 获取对应的 report_id
        Integer reportKey = source.getReportId();
        Integer newReportId = reportIdMapping.get(reportKey);
        if (newReportId == null) {
            return null; // 没有对应的 report_id，跳过
        }

        FulfillmentReportSendRecordSync.Builder builder =
                FulfillmentReportSendRecordSync.newBuilder().setReportId(newReportId); // 设置新的 report_id

        if (source.getBusinessId() != null) {
            builder.setBusinessId(source.getBusinessId());
        }
        if (source.getCompanyId() != null) {
            builder.setCompanyId(source.getCompanyId());
        }
        if (source.getGroomingId() != null) {
            builder.setAppointmentId(source.getGroomingId());
        }
        if (source.getPetId() != null) {
            builder.setPetId(source.getPetId());
        }
        if (source.getReportId() != null) {
            builder.setReportId(reportIdMapping.get(source.getReportId()));
        }
        if (source.getSendingMethod() != null) {
            builder.setSendMethod(SendMethod.forNumber(source.getSendingMethod()));
        }
        if (source.getSentTime() != null) {
            builder.setSentTime(dateToTimestamp(source.getSentTime()));
        }
        if (source.getSentBy() != null) {
            builder.setSentBy(source.getSentBy());
        }
        if (source.getErrorMsg() != null) {
            builder.setErrorMessage(source.getErrorMsg());
        }

        if (source.getStatus() != null) {
            builder.setIsSentSuccess(source.getStatus() == 0);
        }
        if (source.getCreateTime() != null) {
            builder.setCreateTime(dateToTimestamp(source.getCreateTime()));
        }
        if (source.getUpdateTime() != null) {
            builder.setUpdateTime(dateToTimestamp(source.getUpdateTime()));
        }

        return builder.build();
    }

    default List<FulfillmentReportSendRecordSync> convertSendRecords(
            List<GroomingReportSendLogDTO> sourceSendRecords, Map<Integer, Integer> reportIdMapping) {
        if (sourceSendRecords == null || sourceSendRecords.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportSendRecordSync> result = new ArrayList<>(sourceSendRecords.size());
        for (GroomingReportSendLogDTO source : sourceSendRecords) {
            FulfillmentReportSendRecordSync converted = convertSendRecord(source, reportIdMapping);
            if (converted != null) {
                result.add(converted);
            }
        }
        return result;
    }

    default FulfillmentReportSendRecordSync convertDailySendRecord(
            DailyReportSendLogMigrateDef source,
            Map<Integer, Integer> reportIdMapping,
            Map<Long, DailyReportConfigMigrateDef> reportConfigMapping) {
        if (source == null) {
            return null;
        }

        // 获取对应的 report_id
        Integer reportKey = (int) source.getDailyReportId();
        Integer newReportId = reportIdMapping.get(reportKey);
        if (newReportId == null) {
            return null; // 没有对应的 report_id，跳过
        }

        DailyReportConfigMigrateDef dailyReport = reportConfigMapping.get(source.getDailyReportId());
        if (dailyReport == null) {
            return null; // 没有对应的 report_config，跳过
        }

        FulfillmentReportSendRecordSync.Builder builder =
                FulfillmentReportSendRecordSync.newBuilder().setReportId(newReportId); // 设置新的 report_id

        // 基本字段映射
        if (dailyReport.getBusinessId() != 0) {
            builder.setBusinessId(dailyReport.getBusinessId());
        }
        if (dailyReport.getCompanyId() != 0) {
            builder.setCompanyId(dailyReport.getCompanyId());
        }
        if (dailyReport.getAppointmentId() != 0) {
            builder.setAppointmentId(dailyReport.getAppointmentId());
        }
        if (dailyReport.getPetId() != 0) {
            builder.setPetId(dailyReport.getPetId());
        }

        // 发送相关字段
        builder.setSendMethod(SendMethod.forNumber(source.getSendMethod().getNumber()));

        if (source.hasSendTime()) {
            builder.setSentTime(source.getSendTime());
        }
        if (source.getUpdateBy() != 0) {
            builder.setSentBy((int) source.getUpdateBy());
        }

        // 状态和错误信息
        builder.setIsSentSuccess(source.getSentSuccess());
        if (!source.getErrorMessage().isEmpty()) {
            builder.setErrorMessage(source.getErrorMessage());
        }

        // JSON 字段
        if (!source.getContentJson().isEmpty()) {
            builder.setContentJson(source.getContentJson());
        } else {
            builder.setContentJson("{}");
        }

        // 时间字段
        if (source.hasCreateTime()) {
            builder.setCreateTime(source.getCreateTime());
        }
        if (source.hasUpdateTime()) {
            builder.setUpdateTime(source.getUpdateTime());
        }

        return builder.build();
    }

    default List<FulfillmentReportSendRecordSync> convertDailySendRecords(
            List<DailyReportSendLogMigrateDef> sourceDailySendRecords,
            Map<Integer, Integer> reportIdMapping,
            Map<Long, DailyReportConfigMigrateDef> reportConfigMapping) {
        if (sourceDailySendRecords == null || sourceDailySendRecords.isEmpty()) {
            return new ArrayList<>();
        }

        List<FulfillmentReportSendRecordSync> result = new ArrayList<>(sourceDailySendRecords.size());
        for (DailyReportSendLogMigrateDef source : sourceDailySendRecords) {
            FulfillmentReportSendRecordSync converted =
                    convertDailySendRecord(source, reportIdMapping, reportConfigMapping);
            if (converted != null) {
                result.add(converted);
            }
        }
        return result;
    }

    private Timestamp dateToTimestamp(Date date) {
        if (date == null) {
            return Timestamp.getDefaultInstance();
        }

        long seconds = date.getTime() / 1000;

        return Timestamp.newBuilder().setSeconds(seconds).build();
    }

    /**
     * Transform contentJson field names for compatibility
     * 1. "text": -> "inputText":
     * 2. "showcase": -> "photos":
     * 3. "comment" -> "additional_note"
     */
    default String transformContentJson(String contentJson) {
        if (contentJson == null || contentJson.trim().isEmpty()) {
            return contentJson;
        }

        return contentJson
                .replace("\"text\":", "\"inputText\":")
                .replace("\"showcase\":", "\"photos\":")
                .replace("\"comment\"", "\"additional_note\"")
                .replace("\"overall_feedback\"", "\"how_did_it_go\"");
    }

    default String convertTypeNumberToString(String contentJson) {
        if (contentJson == null || contentJson.trim().isEmpty()) {
            return contentJson;
        }
        ObjectMapper mapper = new ObjectMapper();
        Map<Integer, String> typeMap = new HashMap<>();
        typeMap.put(1, "single_choice");
        typeMap.put(2, "multi_choice");
        typeMap.put(3, "text_input");
        typeMap.put(4, "body_view");
        typeMap.put(5, "short_text_input");
        typeMap.put(6, "tag_choice");

        try {
            JsonNode root = mapper.readTree(contentJson);
            JsonNode feedbacks = root.get("feedbacks");
            if (feedbacks != null && feedbacks.isArray()) {
                for (JsonNode node : feedbacks) {
                    if (node.has("type") && node.get("type").isInt()) {
                        int typeNum = node.get("type").asInt();
                        String typeStr = typeMap.getOrDefault(typeNum, "QUESTION_TYPE_UNSPECIFIED");
                        ((ObjectNode) node).put("type", typeStr);
                    }
                }
            }
            return mapper.writeValueAsString(root);
        } catch (Exception e) {
            return contentJson;
        }
    }
}
