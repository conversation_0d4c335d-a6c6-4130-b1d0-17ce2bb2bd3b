package com.moego.server.grooming.mapper.typehandler;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.INTEGER)
@MappedTypes(AppointmentStatusEnum.class)
public class AppointmentStatusEnumHandler extends BaseTypeHandler<AppointmentStatusEnum> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, AppointmentStatusEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getValue());
    }

    @Override
    public AppointmentStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int status = rs.getInt(columnName);
        return AppointmentStatusEnum.values()[status];
    }

    @Override
    public AppointmentStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int status = rs.getInt(columnIndex);
        return AppointmentStatusEnum.values()[status];
    }

    @Override
    public AppointmentStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int status = cs.getInt(columnIndex);
        return AppointmentStatusEnum.values()[status];
    }
}
