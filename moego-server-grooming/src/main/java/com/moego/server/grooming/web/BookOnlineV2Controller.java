package com.moego.server.grooming.web;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessPetTypeModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessPetTypeServiceGrpc.BusinessPetTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.ListPetTypeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerOnlineBookingClient;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.customer.params.CreateOBLoginTokenParams;
import com.moego.server.grooming.dto.ob.OBServiceDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.enums.AppointmentSourcePlatform;
import com.moego.server.grooming.helper.MetadataHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import com.moego.server.grooming.properties.GoogleReserveProperties;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.dto.OBAvailableDateTimeDTO;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBSubmitResult;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import com.moego.server.grooming.service.dto.ob.OBLastApptVO;
import com.moego.server.grooming.service.ob.OBAbandonService;
import com.moego.server.grooming.service.ob.OBBusinessProfileService;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.ob.OBBusinessStaffTimeService;
import com.moego.server.grooming.service.ob.OBClientApptService;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.service.ob.OBLocationService;
import com.moego.server.grooming.service.ob.OBQuestionService;
import com.moego.server.grooming.service.ob.OBServiceService;
import com.moego.server.grooming.service.ob.OBWhitelistService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.web.dto.ob.BusinessInfoDto;
import com.moego.server.grooming.web.dto.ob.InfoDto;
import com.moego.server.grooming.web.dto.ob.IsAvailableDto;
import com.moego.server.grooming.web.dto.ob.OBBusinessStaffDTO;
import com.moego.server.grooming.web.dto.ob.OBSubmitResultDTO;
import com.moego.server.grooming.web.dto.ob.ProfileDto;
import com.moego.server.grooming.web.dto.ob.QuestionListDto;
import com.moego.server.grooming.web.params.GoogleReserveConversionTrackingParam;
import com.moego.server.grooming.web.params.OBAbandonParams;
import com.moego.server.grooming.web.params.OBBusinessStaffParams;
import com.moego.server.grooming.web.params.OBClientInfoParams;
import com.moego.server.grooming.web.params.OBServiceParams;
import com.moego.server.grooming.web.params.OBTimeSlotParams;
import com.moego.server.grooming.web.params.SubmitBookingRequestParams;
import com.moego.server.grooming.web.vo.ob.OBBusinessLocationVO;
import com.moego.server.grooming.web.vo.ob.OBBusinessStaffVO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageConfigClientVO;
import com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO;
import com.moego.server.message.api.IMessageService;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.PaymentSettingForClientDTO;
import com.moego.server.payment.dto.SmartTipConfigDTO;
import com.moego.server.payment.dto.SmartTipConfigForClientDTO;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@RestController
@RequestMapping("/grooming/ob/v2/client")
@Slf4j
@Validated
@RequiredArgsConstructor
public class BookOnlineV2Controller {

    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    private final GroomingServiceService groomingServiceService;

    private final IBusinessBusinessClient iBusinessBusinessClient;

    private final BusinessInfoHelper businessInfoHelper;

    private final IPaymentCreditCardClient iPaymentService;

    private final LockManager moegoLockManager;

    private final OBClientTimeSlotService clientTimeSlotService;

    private final OBBusinessStaffTimeService staffTimeService;

    private final IBusinessBusinessClient businessClient;

    private final OBClientApptService clientApptService;

    private final OBGroomingService groomingService;

    private final OBQuestionService questionService;

    private final OBLocationService locationService;

    private final OBBusinessService businessService;

    private final IPaymentSettingClient iPaymentSettingClient;

    private final OBServiceService serviceService;

    private final ICustomerOnlineBookingClient iCustomerOnlineBookingClient;

    private final OBLandingPageConfigService landingPageConfigService;

    private final OBBusinessProfileService businessProfileService;

    private final IMessageService messageService;

    private final OBAbandonService abandonService;

    private final RestTemplate restTemplate;

    private final GoogleReserveProperties googleReserveProperties;

    private final AppointmentMapperProxy appointmentMapper;

    private final OBWhitelistService whitelistService;

    private final MigrateHelper migrateHelper;

    private final MetadataHelper metadataHelper;

    private final BusinessPetTypeServiceBlockingStub businessPetTypeServiceBlockingStub;

    @PostMapping("/abandon")
    @Auth(AuthType.OB)
    public void collectAbandonInfo(
            OBAnonymousParams anonymousParams, @RequestBody @Valid OBAbandonParams abandonParams) {
        if (StringUtils.hasText(AuthContext.get().impersonator())) {
            abandonParams = abandonParams.toBuilder().skipAbandon(Boolean.TRUE).build();
        }
        BusinessCompanyPO businessCompanyPO =
                landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
            throw bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }
        String resourceCacheKey =
                moegoLockManager.getResourceKey(LockManager.OB_ABANDON, abandonParams.bookingFlowId());
        String randomValue = CommonUtil.getUuid();
        try {
            if (!moegoLockManager.lock(resourceCacheKey, randomValue)) {
                return;
            }
            var params = abandonParams.toBuilder()
                    .businessId(businessCompanyPO.getBusinessId())
                    .companyId(businessCompanyPO.getCompanyId())
                    .obName(getOBName(anonymousParams))
                    .build();
            abandonService.collectAbandonInfo(params);
        } finally {
            moegoLockManager.unlock(resourceCacheKey, randomValue);
        }
    }

    @GetMapping("/business/config")
    @Auth(AuthType.ANONYMOUS)
    public OBLandingPageConfigClientVO getBusinessOBProfileAndConfig(OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO =
                landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
            return new OBLandingPageConfigClientVO();
        }
        return landingPageConfigService.getLandingPageConfigClientVO(businessCompanyPO.getBusinessId());
    }

    @GetMapping("/business/services")
    @Auth(AuthType.ANONYMOUS)
    public List<OBLandingPageServiceCategoryVO> getBusinessOBServices(
            OBAnonymousParams anonymousParams, @RequestParam("type") @NotNull @Range(min = 1, max = 2) Byte type) {
        BusinessCompanyPO businessCompanyPO =
                landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
            return Collections.emptyList();
        }
        return serviceService.listLandingPageServiceCategoryVO(businessCompanyPO.getBusinessId(), type);
    }

    @PostMapping("/business/staff")
    @Auth(AuthType.OB)
    public List<OBBusinessStaffVO> getOBAvailableStaffListByService(
            OBAnonymousParams anonymousParams, @Valid @RequestBody OBBusinessStaffParams params) {
        Integer businessId = toBusinessId(params.getBusinessId(), params.getBusinessName(), anonymousParams);

        OBBusinessStaffDTO obBusinessStaffDTO = new OBBusinessStaffDTO();
        obBusinessStaffDTO.setBusinessId(businessId);
        obBusinessStaffDTO.setObName(getOBName(anonymousParams));
        obBusinessStaffDTO.setCustomerId(params.getCustomerId());
        obBusinessStaffDTO.setPetServiceList(params.getPetServiceList());
        obBusinessStaffDTO.setTimeSlotParam(params.getTimeSlotParam());

        return staffTimeService.getOBAvailableStaffListByService(obBusinessStaffDTO);
    }

    @PostMapping("/service")
    @Auth(AuthType.OB)
    public OBServiceListDto queryBookOnlinePetServicesForClient(
            OBAnonymousParams anonymousParams, @Valid @RequestBody OBServiceParams params) {
        Integer businessId = toBusinessId(params.getBusinessId(), params.getBusinessName(), anonymousParams);
        params.getPetDataList().stream()
                .filter(pet -> !StringUtils.hasText(pet.getCoat()))
                .forEach(pet -> pet.setCoat(pet.getHairLength()));

        OBServiceDTO obServiceDTO = new OBServiceDTO();
        obServiceDTO.setBusinessId(businessId);
        obServiceDTO.setCustomerId(params.getCustomerId());
        obServiceDTO.setPetDataList(params.getPetDataList());

        return serviceService.getPetServiceList(obServiceDTO);
    }

    @GetMapping("/distance")
    @Auth(AuthType.OB)
    public IsAvailableDto checkAvailableDist(
            Integer businessId, // todo: remove businessId
            String businessName,
            OBAnonymousParams anonymousParams,
            @RequestParam String lat,
            @RequestParam String lng,
            @RequestParam String zipcode) {
        businessId = toBusinessId(businessId, businessName, anonymousParams);

        ServiceAreaParams.ClientAddressParams clientAddressParams = new ServiceAreaParams.ClientAddressParams();
        clientAddressParams.setLat(lat);
        clientAddressParams.setLng(lng);
        clientAddressParams.setZipcode(zipcode);
        clientAddressParams.setAddressId(0);
        ServiceAreaParams serviceAreaParams = new ServiceAreaParams();
        serviceAreaParams.setBusinessId(businessId);
        serviceAreaParams.setAddressParamsList(Collections.singletonList(clientAddressParams));
        List<ServiceAreaResultDTO> resultList = businessService.getServiceAreaResultList(serviceAreaParams);
        // result
        IsAvailableDto result = new IsAvailableDto();
        result.setIsAvailable(BooleanUtils.isNotTrue(resultList.stream()
                .findFirst()
                .orElse(new ServiceAreaResultDTO())
                .getOutOfArea()));
        return result;
    }

    @GetMapping("/question")
    @Auth(AuthType.ANONYMOUS)
    public QuestionListDto getQuestionForClient(
            Integer businessId, // todo: remove businessId
            String businessName,
            OBAnonymousParams anonymousParams,
            @RequestParam Integer type) {
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();

        QuestionListDto result = new QuestionListDto();
        result.setQuestions(questionService.getShowQuestionsByBusinessId(migrated, companyId, businessId, type));
        return result;
    }

    @GetMapping("/business/location")
    @Auth(AuthType.ANONYMOUS)
    public List<OBBusinessLocationVO> getRelevantBiz(
            Integer businessId, String businessName, OBAnonymousParams anonymousParams) { // todo: remove businessId
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        List<Integer> relevantBusinessIdList = iBusinessBusinessClient.getAllRelevantBusinessIds(businessId);
        return locationService.getBookOnlineLocationDTOList(relevantBusinessIdList);
    }

    @GetMapping("/business/preference")
    @Auth(AuthType.ANONYMOUS)
    public OBBusinessInfoDTO getBusinessPreference(
            Integer businessId, String businessName, OBAnonymousParams anonymousParams) { // todo: remove businessId
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        return businessService.getBusinessInfo(businessId);
    }

    @GetMapping("/business/profile")
    @Auth(AuthType.ANONYMOUS)
    public ProfileDto getProfile(
            Integer businessId, String businessName, OBAnonymousParams anonymousParams) { // todo: remove businessId
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        ProfileDto result = new ProfileDto();
        result.setProfile(businessProfileService.getProfileByBusinessId(businessId));
        result.setCompany(new ProfileDto.OBBusinessProfileCompanyVO(
                mustGetCompany(businessId).getEnterpriseId()));
        return result;
    }

    @GetMapping("/business/info")
    @Auth(AuthType.ANONYMOUS)
    public InfoDto getInfo(String businessName, OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO = toBusinessAndCompanyPO(null, businessName, anonymousParams);
        Integer businessId = businessCompanyPO.getBusinessId();
        Long companyId = businessCompanyPO.getCompanyId();
        // 结果放到business_info下面
        BusinessInfoDto infoObject = new BusinessInfoDto();
        MoeBusinessBookOnline currentBusiness = moeGroomingBookOnlineService.getSettingInfoByBusinessId(businessId);
        // 当商家降级后，需要在C端接口上返回only show applicable service开关的默认值 关闭，否则C端页面显示数据会有问题
        if (!groomingServiceService.isServiceFilterAllowed(businessId)) {
            currentBusiness.setServiceFilter(OnlineBookingConst.SERVICE_FILTER_DISABLE);
        }
        infoObject.setIsEnable(currentBusiness.getIsEnable());
        infoObject.setSetting(currentBusiness);
        infoObject.setIsMerged(Objects.nonNull(landingPageConfigService.selectByBusinessId(businessId)));
        // 商家会员是否到期 todo check level
        infoObject.setIsAvailable(true);
        infoObject.setIsInPackageWhitelist(metadataHelper.isInPackageWhitelist(businessId));

        // 查询payment setting
        PaymentSettingDTO paymentSetting = iPaymentSettingClient.getPaymentSetting(businessId);
        PaymentSettingForClientDTO paymentSettingForClient = new PaymentSettingForClientDTO();
        BeanUtils.copyProperties(paymentSetting, paymentSettingForClient);

        // 查询tip配置
        SmartTipConfigDTO tipConfig = iPaymentSettingClient.getSmartTipConfig(businessId);
        SmartTipConfigForClientDTO tipConfigForClient = new SmartTipConfigForClientDTO();
        BeanUtils.copyProperties(tipConfig, tipConfigForClient);

        // 查询 Pet Type 配置
        List<PetType> petTypeList = businessPetTypeServiceBlockingStub
                .listPetType(ListPetTypeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build())
                .getTypesList()
                .stream()
                .map(BusinessPetTypeModel::getPetTypeId)
                .toList();

        // 构造最终返回结果
        InfoDto result = new InfoDto();
        result.setBusinessInfo(infoObject);
        result.setSquareInfo(iPaymentService.getToken(businessId));
        result.setPaymentSetting(paymentSettingForClient);
        result.setTipConfig(tipConfigForClient);
        result.setTwilioInfo(messageService.getBusinessTwilioNumber(businessId));
        result.setAvailablePetTypes(petTypeList);

        if (whitelistService.isWhitelistFranchise(businessId)) {
            MoeBusinessBookOnline setting = result.getBusinessInfo().getSetting();
            setting.setAcceptClient(OBWhitelistService.CLIENT_TYPE_FORCE_MAP.get(
                    setting.getAcceptClient())); // setting.acceptClient has been replaced via
            // obAvailabilitySettingServiceBlockingStub.getGroomingServiceAvailability
        }

        return result;
    }

    /**
     * @deprecated by Freeman, use {@link BookOnlineV3Controller#submitBookingRequest(SubmitBookingRequestParams, OBAnonymousParams, String)} instead
     */
    @PostMapping("/submit")
    @Auth(AuthType.OB)
    @Deprecated(forRemoval = true, since = "2024/12/24")
    public OBSubmitResultDTO bookOnlineSubmit(
            @Valid @RequestBody OBClientInfoParams clientDTO,
            OBAnonymousParams anonymousParams,
            // see
            // https://developers.google.com/maps-booking/verticals/appointment-booking/guides/integration/conversion-tracking
            @Parameter(hidden = true) @CookieValue(value = "_rwg_token", required = false) String rwgToken)
            throws Exception {
        var obSession = checkOBSession(anonymousParams);
        // check service, init add on list
        for (BookOnlinePetParams petDataDTO : clientDTO.getPetData()) {
            if (petDataDTO.getServiceId() == null) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Invalid param. Please refresh the page and try again");
            }
            if (petDataDTO.getAddOnIds() == null) {
                petDataDTO.setAddOnIds(Collections.emptyList());
            }
        }
        // 用 session 里的 customer id 覆盖前端入参传的 customer id
        var sessionCustomerId = AuthContext.get().getCustomerId();
        clientDTO.getCustomerData().setCustomerId(sessionCustomerId);

        // 如果是 new client，那么 sessionCustomerId 为 null， submit 后，会返回一个新创建的 customer id
        OBSubmitResult obSubmitResult = submit(clientDTO, anonymousParams);
        Integer submitCustomerId = obSubmitResult.getCustomerId();

        // Reserve with Google, send conversion event after submit
        if (StringUtils.hasText(rwgToken)) {
            // Don't block the main process
            ThreadPool.execute(() -> sendConversionEvent(rwgToken, obSubmitResult.getAppointmentId()));
        }

        // submit 返回的 customer id 与会话中的 customer id 一致，说明是 existing client，不需要重新创建子会话
        if (submitCustomerId.equals(sessionCustomerId)) {
            return new OBSubmitResultDTO(obSubmitResult.getAutoAcceptRequest());
        }

        // submit 返回的 customer id 与会话中的 customer id 不一致，说明是 new client，需要为其创建子会话
        CreateOBLoginTokenParams params = CreateOBLoginTokenParams.builder()
                .customerId(submitCustomerId)
                .ip(RequestUtils.getIP())
                .userAgent(RequestUtils.getUserAgent())
                .refererLink(RequestUtils.getReferer())
                .mainSession(obSession)
                .build();

        iCustomerOnlineBookingClient.genLoginToken(params);

        return new OBSubmitResultDTO(obSubmitResult.getAutoAcceptRequest());
    }

    private OBSubmitResult submit(OBClientInfoParams clientDTO, OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO =
                toBusinessAndCompanyPO(clientDTO.getBusinessId(), clientDTO.getBusinessName(), anonymousParams);

        // 填充 business id, 查询 ob 配置
        BookOnlineSubmitParams obParams = new BookOnlineSubmitParams();
        BeanUtils.copyProperties(clientDTO, obParams);
        obParams.setBusinessId(businessCompanyPO.getBusinessId());
        obParams.setCompanyId(businessCompanyPO.getCompanyId());

        MoeBusinessBookOnline obBusiness =
                moeGroomingBookOnlineService.getSettingInfoByBusinessId(businessCompanyPO.getBusinessId());

        // check ob config、apptTime
        moeGroomingBookOnlineService.submitParamsCheck(obBusiness, obParams);

        String prepayGuid = obParams.getPrepayGuid();

        // disable select time submit
        if (Objects.isNull(obParams.getStaffId()) || !StringUtils.hasText(obParams.getAppointmentDate())) {
            try {
                return groomingService.bookOnlineSubmit(obParams, obBusiness);
            } catch (Exception e) {
                refundIfNecessary(prepayGuid);
                throw e;
            }
        }
        // by working hour / by slot submit
        String lockKey = moegoLockManager.getResourceKey(
                LockManager.OB_SUBMIT, obParams.getStaffId() + obParams.getAppointmentDate());
        String randomValue = CommonUtil.getUuid();
        try {
            // 释放check时加的锁，否则下方的加锁会失败
            if (StringUtils.hasText(prepayGuid)) {
                moegoLockManager.unlock(lockKey, prepayGuid);
            }

            if (moegoLockManager.lockWithRetry(lockKey, randomValue)) {
                return groomingService.bookOnlineSubmit(obParams, obBusiness);
            } else {
                throw new CommonException(ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + lockKey);
            }
        } catch (Exception e) {
            // 如果定金id不为空，则发起退款流程
            refundIfNecessary(prepayGuid);
            throw e;
        } finally {
            moegoLockManager.unlock(lockKey, randomValue);
        }
    }

    @PostMapping("/timeslot")
    @Auth(AuthType.OB)
    public Map<String, OBAvailableTimeDto> getStaffAvailableTimeV2(
            @Valid @RequestBody OBTimeSlotParams params, OBAnonymousParams anonymousParams) {
        Integer businessId = toBusinessId(params.getBusinessId(), params.getBusinessName(), anonymousParams);

        OBTimeSlotDTO obTimeSlotDTO = new OBTimeSlotDTO();
        BeanUtils.copyProperties(params, obTimeSlotDTO);
        obTimeSlotDTO.setBusinessId(businessId);
        obTimeSlotDTO.setObName(getOBName(anonymousParams));

        return clientTimeSlotService.getTimeSlotListV2(obTimeSlotDTO).getAvailableDateTimes();
    }

    @PostMapping("/timeslots")
    @Auth(AuthType.OB)
    public OBAvailableDateTimeDTO getStaffAvailableDateTime(
            @Valid @RequestBody OBTimeSlotParams params, OBAnonymousParams anonymousParams) {
        Integer businessId = toBusinessId(params.getBusinessId(), params.getBusinessName(), anonymousParams);

        OBTimeSlotDTO obTimeSlotDTO = new OBTimeSlotDTO();
        BeanUtils.copyProperties(params, obTimeSlotDTO);
        obTimeSlotDTO.setBusinessId(businessId);
        obTimeSlotDTO.setObName(getOBName(anonymousParams));

        return clientTimeSlotService.getTimeSlotListV2(obTimeSlotDTO);
    }

    @GetMapping("/appt/last")
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public OBLastApptVO getCustomerLastAppointmentForBookOnline(
            AuthContext context,
            Integer businessId, // todo: remove businessId
            String businessName,
            OBAnonymousParams anonymousParams) {
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        OBLastApptVO lastApptVO = new OBLastApptVO();
        OBClientApptDTO apptDTO = clientApptService.getLastAppt(context.getCustomerId(), businessInfo);
        lastApptVO.setLastAppt(apptDTO);
        lastApptVO.setIsExists(Objects.nonNull(apptDTO));
        return lastApptVO;
    }

    @GetMapping("/appt")
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public OBClientApptDTO getCustomerAppointmentForBookOnline(
            AuthContext context,
            @NotNull @RequestParam String bookingId,
            OBAnonymousParams params // 这里的代码没用到，但是 authz 鉴权需要
            ) {
        Integer apptId = Integer.valueOf(bookingId);
        return clientApptService.getOBClientAppt(context.getCustomerId(), apptId);
    }

    @GetMapping("/grooming-report/appt")
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public OBClientApptDTO getCustomerAppointmentForBookOnline(
            AuthContext context, @RequestParam Integer reportId, OBAnonymousParams params // 这里的代码没用到，但是 authz 鉴权需要
            ) {
        return clientApptService.getOBClientApptFromGroomingReport(context.getCustomerId(), reportId);
    }

    private Integer toBusinessId(Integer businessId, String businessName, OBAnonymousParams anonymousParams) {
        if (businessId != null) {
            return businessId;
        }
        if (Objects.nonNull(anonymousParams.getName()) || Objects.nonNull(anonymousParams.getDomain())) {
            BusinessCompanyPO businessCompanyPO =
                    landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
            if (Objects.nonNull(businessCompanyPO)) {
                businessId = businessCompanyPO.getBusinessId();
            }
        } else if (StringUtils.hasText(businessName)) {
            businessId = moeGroomingBookOnlineService.getBusinessIdByBookOnlineName(businessName);
        }
        if (Objects.nonNull(businessId)) {
            return businessId;
        }
        throw bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
    }

    private BusinessCompanyPO toBusinessAndCompanyPO(
            Integer businessId, String businessName, OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO = null;
        if (businessId != null) {
            businessCompanyPO =
                    new BusinessCompanyPO(businessId, businessInfoHelper.getCompanyIdByBusinessId(businessId));
        }
        if (Objects.nonNull(anonymousParams.getName()) || Objects.nonNull(anonymousParams.getDomain())) {
            businessCompanyPO = landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        } else if (StringUtils.hasText(businessName)) {
            businessCompanyPO = moeGroomingBookOnlineService.getBusinessCompanyByBookOnlineName(businessName);
        }
        if (Objects.isNull(businessCompanyPO) || Objects.isNull(businessCompanyPO.getBusinessId())) {
            throw bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
        }
        return businessCompanyPO;
    }

    /**
     * 检查 OB 主会话参数，需要有 session id，account id (<-1) 和 OB name
     *
     * @param anonymousParams ob name
     * @return OBMainSessionDTO
     */
    private OBMainSessionDTO checkOBSession(OBAnonymousParams anonymousParams) {
        var context = AuthContext.get();
        // main session id is required
        var sessionId = context.sessionId();
        if (sessionId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        // main account id should be < -1
        var accountId = context.accountId();
        if (accountId == null || accountId >= -1) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }

        // obName is required, 优先解析 domain（自定义域名的 OB）, 然后解析 name （统一域名的 ob）
        var obName = getOBName(anonymousParams);
        if (!StringUtils.hasText(obName)) {
            throw bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }

        return new OBMainSessionDTO(sessionId, accountId, obName);
    }

    private String getOBName(OBAnonymousParams anonymousParams) {
        var obName = anonymousParams.getDomain();
        if (StringUtils.hasText(obName)) {
            return obName;
        }
        return anonymousParams.getName();
    }

    private void sendConversionEvent(String rwgToken, Integer appointmentId) {
        // send conversion event
        GoogleReserveConversionTrackingParam param = new GoogleReserveConversionTrackingParam()
                .setRwgToken(rwgToken)
                .setConversionPartnerId(googleReserveProperties.getPartnerId());
        try {
            restTemplate.postForEntity(googleReserveProperties.getConversionTrackingUrl(), param, String.class);
        } catch (RestClientException e) {
            log.error(
                    "Failed to send conversion event, appointment: {}, param: {}",
                    appointmentId,
                    JsonUtil.toJson(param),
                    e);
        }
        // set source platform to RESERVE_WITH_GOOGLE
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(appointmentId);
        appointment.setSourcePlatform(AppointmentSourcePlatform.RESERVE_WITH_GOOGLE.name());
        appointmentMapper.updateByPrimaryKeySelective(appointment);
    }

    private void refundIfNecessary(String prepayGuid) {
        if (StringUtils.hasLength(prepayGuid)) {
            moeGroomingBookOnlineService.createRefundForOBDeposit(prepayGuid);
        }
    }

    private CompanyDto mustGetCompany(Integer businessId) {
        var company = businessClient.getCompanyByBusinessId(businessId);
        if (company == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "company not found for businessId: " + businessId);
        }
        return company;
    }
}
