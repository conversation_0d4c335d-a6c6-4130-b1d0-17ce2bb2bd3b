package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingOnlineFeeInvoiceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MoeGroomingOnlineFeeInvoice record);

    int insertSelective(MoeGroomingOnlineFeeInvoice record);

    MoeGroomingOnlineFeeInvoice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MoeGroomingOnlineFeeInvoice record);

    int updateByPrimaryKey(MoeGroomingOnlineFeeInvoice record);

    MoeGroomingOnlineFeeInvoice selectByInvoiceIdAndType(
            @Param("businessId") Integer businessId, @Param("invoiceId") Integer invoiceId, @Param("type") Byte type);

    List<MoeGroomingOnlineFeeInvoice> selectByInvoiceIdList(
            @Param("invoiceIdList") List<Long> invoiceIdList, @Param("type") Byte type);

    int batchUpdateRequiredFeeByInvoiceIdList(
            @Param("invoiceIdList") List<Long> invoiceIdList,
            @Param("requiredFee") Byte requiredFee,
            @Param("type") Byte type);

    int batchInsert(List<MoeGroomingOnlineFeeInvoice> records);
}
