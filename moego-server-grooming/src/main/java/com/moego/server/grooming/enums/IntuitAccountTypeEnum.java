package com.moego.server.grooming.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/2/28
 */
@Getter
public enum IntuitAccountTypeEnum {
    /**
     * code:
     * [1 byte]: 对齐QB定义的帐号类型
     * [2 byte]: 1 byte 的子类型, 为MoeGo定义的帐号类型
     * [3 byte]: 2 byte 的子类型
     */
    // 截止2024/02/28 仅QB sync version 1.0 使用
    ACCOUNT_TYPE_MOEGO_INVOICE(1, "MoeGo Invoice", false),
    // qb sync version 2.0 把moego invoice 拆成了两个帐号, 分别是11 和 12, 其中分别有2/3个子帐号
    ACCOUNT_TYPE_MOEGO_INVOICE_INCOME(11, "MoeGo Invoice Income", false),
    // parent: ACCOUNT_TYPE_MOEGO_SALES_INCOME
    ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE(111, "MoeGo Invoice Sales Revenue", true),
    // parent: ACCOUNT_TYPE_MOEGO_SALES_INCOME
    ACCOUNT_TYPE_MOEGO_INVOICE_TIPS(112, "MoeGo Invoice Tips", false),
    // parent: ACCOUNT_TYPE_MOEGO_SALES_INCOME
    ACCOUNT_TYPE_MOEGO_INCOME_FEE_BY_CLIENT_REVENUE(113, "MoeGo Invoice Fee By Clients Revenue", true),

    ACCOUNT_TYPE_MOEGO_REFUND_SERVICE_INCOME(114, "MoeGo Refund Income", false),
    ACCOUNT_TYPE_MOEGO_REFUND_FROM_CREDIT_MEMO(1141, "Refund from Credit Memo", true),
    ACCOUNT_TYPE_MOEGO_REFUND_FROM_MOEGO_PAYMENT(1142, "Refund from MoeGo Payment", true),

    ACCOUNT_TYPE_MOEGO_RECEIVED_PAYMENT(12, "MoeGo Pay Received Payment Income", false),
    ACCOUNT_TYPE_MOEGO_RECEIVED_GROSS_SALE(121, "MoeGo Pay Received Gross Sale", true),
    ACCOUNT_TYPE_MOEGO_RECEIVED_NET_SALE(122, "MoeGo Pay Received Net Sale", true),
    ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS(123, "MoeGo Pay Received Tips", false),

    ACCOUNT_TYPE_MOEGO_BANK(2, "MoeGo Bank", false),
    ACCOUNT_TYPE_MOEGO_ACCOUNT_RECEIVABLE(3, "MoeGo Account Receivable", false),
    ACCOUNT_TYPE_MOEGO_EXPENSE(4, "MoeGo Pay processing fee", false),
    ACCOUNT_TYPE_MOEGO_OTHER_CURRENT_LIABILITY(6, "MoeGo Liability Account", false),
    /*
     * 这两个类型虽然是属于OTHER_CURRENT_LIABILITY, 但不作为子帐号, 与ACCOUNT_TYPE_MOEGO_SALES_INCOME类似
     */
    ACCOUNT_TYPE_MOEGO_SALES_TAX_PAYABLE(61, "MoeGo Sales Tax Payable", false),
    ACCOUNT_TYPE_MOEGO_RECEIVED_TAX_PAYABLE(62, "MoeGo Pay Received Tax Payable", false),

    // OTHER_CURRENT_LIABILITY
    ACCOUNT_TYPE_MOEGO_OTHER_CURRENT_ASSET(7, "MoeGo Other Current Asset", false),
    // 这个账号作为ACCOUNT_TYPE_MOEGO_RECEIVED_PAYMENT_GROSS_SALE的DepositTo账号,
    ACCOUNT_TYPE_MOEGO_PAY_SALES_RECEIPT(71, "MoeGo Pay Sales Receipt", false),
    //    // 这个账号是用来处理同步 purchase(expense) 时必须指定账号的问题, 无实际应用
    //    @Deprecated
    //    ACCOUNT_TYPE_MOEGO_PURCHASE_ACCOUNT(72, "MoeGo Purchase Record Account", false),
    // 这个账号用来避免 bank 同步造成的 double record, 无实际应用
    ACCOUNT_TYPE_MOEGO_PAYMENTS(73, "MoeGo Payments", false);
    ;

    private final Integer code;
    private final String name;
    private final Boolean isSubAccount;

    IntuitAccountTypeEnum(Integer code, String name, Boolean isSubAccount) {
        this.code = code;
        this.name = name;
        this.isSubAccount = isSubAccount;
    }

    public static IntuitAccountTypeEnum getParentAccountType(IntuitAccountTypeEnum e) {
        return switch (e) {
            case ACCOUNT_TYPE_MOEGO_INVOICE_SALES_REVENUE,
                    ACCOUNT_TYPE_MOEGO_INVOICE_TIPS,
                    ACCOUNT_TYPE_MOEGO_INCOME_FEE_BY_CLIENT_REVENUE -> ACCOUNT_TYPE_MOEGO_INVOICE_INCOME;
            case ACCOUNT_TYPE_MOEGO_RECEIVED_GROSS_SALE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_NET_SALE,
                    ACCOUNT_TYPE_MOEGO_RECEIVED_TIPS -> ACCOUNT_TYPE_MOEGO_RECEIVED_PAYMENT;
            case ACCOUNT_TYPE_MOEGO_REFUND_FROM_CREDIT_MEMO,
                    ACCOUNT_TYPE_MOEGO_REFUND_FROM_MOEGO_PAYMENT -> ACCOUNT_TYPE_MOEGO_REFUND_SERVICE_INCOME;
            default -> null;
        };
    }
}
