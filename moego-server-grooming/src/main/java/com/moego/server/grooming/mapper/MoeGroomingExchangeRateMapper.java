package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingExchangeRate;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingExchangeRateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_exchange_rate
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingExchangeRate record);

    /**
     * 获取最新的汇率
     * @return
     */
    MoeGroomingExchangeRate selectLatestRate(@Param("base") String base);

    /**
     * 根据base更新对应的汇率
     * @param record
     * @return
     */
    int updateById(MoeGroomingExchangeRate record);
}
