package com.moego.server.grooming.service;

import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.mapper.MoeGroomingExchangeRateMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingExchangeRate;
import com.moego.server.grooming.service.dto.ExchangeRateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @description
 * @date 4/27/21 2:38 PM
 */
@Service
@Slf4j
public class MoeGroomingExchangeRateService {

    @Autowired
    private MoeGroomingExchangeRateMapper exchangeRateMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${exchange.rate.newUrl}")
    private String exchangeRateUrl;

    @Value("${exchange.rate.param.symbols}")
    private String symbols;

    @Value("${exchange.rate.apiKey}")
    private String apiKey;

    @Value("${exchange.rate.base}")
    private String rateBase;

    /**
     * 获取最新汇率
     *
     * @return
     */
    public ExchangeRateDto getLatestExchangeRate() {
        ExchangeRateDto rateDto = new ExchangeRateDto();
        MoeGroomingExchangeRate latestRate = exchangeRateMapper.selectLatestRate(rateBase);
        if (latestRate != null) {
            BeanUtils.copyProperties(latestRate, rateDto);
            rateDto.setRates(JsonUtil.toBean(latestRate.getRates(), new TypeRef<>() {}));
        } else {
            // 如果数据库中没有记录，重新获取
            rateDto = requestExchangeRate();
        }
        return rateDto;
    }

    /**
     * 请求第三方汇率
     *
     * @return
     */
    @TimerMetrics(group = TimerGroup.TASK)
    public ExchangeRateDto requestExchangeRate() {
        // 根据请求参数构建请求地址
        String reqUrl = String.format(exchangeRateUrl, symbols, rateBase);
        // 请求第三方获取汇率
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("apiKey", apiKey);
        HttpEntity<String> httpEntity = new HttpEntity<>(httpHeaders);

        ResponseEntity<ExchangeRateDto> response =
                restTemplate.exchange(reqUrl, HttpMethod.GET, httpEntity, ExchangeRateDto.class);
        if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
            log.error("请求第三方获取汇率返回结果为空, response：{}", response);
            return null;
        }
        ExchangeRateDto result = response.getBody();
        if (result == null || result.getRates() == null || result.getRates().isEmpty()) {
            // 汇率为空，不更新到数据库
            log.info("请求第三方获取汇率返回结果为空，response:{}", response.getBody());
            return null;
        }

        // 并发下可能会创建多个，虽然在新增时进行了判断，但是加锁最保险
        synchronized (this) {
            // 查询数据库，如果有就更新，没有就新增
            MoeGroomingExchangeRate latestRate = exchangeRateMapper.selectLatestRate(rateBase);
            MoeGroomingExchangeRate saveRecord = new MoeGroomingExchangeRate();
            BeanUtils.copyProperties(result, saveRecord);
            saveRecord.setRates(JsonUtil.toJson(result.getRates()));
            if (latestRate == null) {
                // 将结果存入数据库中供下次查询
                exchangeRateMapper.insertSelective(saveRecord);
            } else {
                // 更新
                saveRecord.setId(latestRate.getId());
                exchangeRateMapper.updateById(saveRecord);
            }
        }
        return result;
    }
}
