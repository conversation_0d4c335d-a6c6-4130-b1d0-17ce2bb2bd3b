package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/12
 */
@Mapper
public interface PrepayMapper {
    PrepayMapper INSTANCE = Mappers.getMapper(PrepayMapper.class);

    OBRequestDetailVO.OBPrepayDetailVO dto2vo(OBPrepayDetailDTO dto);
}
