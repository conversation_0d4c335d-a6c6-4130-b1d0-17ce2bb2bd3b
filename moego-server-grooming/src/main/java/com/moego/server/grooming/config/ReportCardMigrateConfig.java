package com.moego.server.grooming.config;

/**
 * Report Card 数据迁移配置类
 * 定义迁移过程中使用的常量和配置信息
 */
public class ReportCardMigrateConfig {

    /**
     * 迁移表名常量
     */
    public static class TableNames {
        public static final String THEME_CONFIG = "theme_config";
        public static final String TEMPLATE = "template";
        public static final String REPORT = "report";
        public static final String QUESTION = "question";
        public static final String SEND_RECORD = "send_record";
    }

    /**
     * 源表名常量
     */
    public static class SourceTables {
        // MySQL 源表
        public static final String MOE_GROOMING_REPORT_THEME_CONFIG = "moe_grooming_report_theme_config";
        public static final String MOE_GROOMING_REPORT_TEMPLATE = "moe_grooming_report_template";
        public static final String MOE_GROOMING_REPORT = "moe_grooming_report";
        public static final String MOE_GROOMING_REPORT_QUESTION = "moe_grooming_report_question";
        public static final String MOE_GROOMING_REPORT_SEND_LOG = "moe_grooming_report_send_log";

        // PostgreSQL 源表
        public static final String DAILY_REPORT_CONFIG = "daily_report_config";
        public static final String DAILY_REPORT_SEND_LOG = "daily_report_send_log";
    }

    /**
     * 目标表名常量
     */
    public static class TargetTables {
        public static final String FULFILLMENT_REPORT_THEME_CONFIG = "fulfillment_report_theme_config";
        public static final String FULFILLMENT_REPORT_TEMPLATE = "fulfillment_report_template";
        public static final String FULFILLMENT_REPORT = "fulfillment_report";
        public static final String FULFILLMENT_REPORT_QUESTION = "fulfillment_report_question";
        public static final String FULFILLMENT_REPORT_SEND_RECORD = "fulfillment_report_send_record";
    }

    /**
     * 数据源类型
     */
    public static class SourceType {
        public static final String GROOMING = "grooming";
        public static final String DAILY = "daily";
    }

    /**
     * 迁移配置参数
     */
    public static class MigrationConfig {
        // 批处理大小
        public static final int BATCH_SIZE = 300;

        // 最大重试次数
        public static final int MAX_RETRY_COUNT = 3;

        // 超时时间（毫秒）
        public static final long TIMEOUT_MS = 300000; // 5分钟

        // 进度更新间隔（记录数）
        public static final int PROGRESS_UPDATE_INTERVAL = 100;
    }

    /**
     * 数据校验配置参数
     */
    public static class ValidationConfig {
        // 是否启用批量校验
        public static final boolean ENABLE_BATCH_VALIDATION = true;

        // 是否启用全量校验
        public static final boolean ENABLE_FULL_VALIDATION = true;

        // 严重错误率阈值（超过此阈值将发出警告）
        public static final double CRITICAL_ERROR_RATE_THRESHOLD = 0.1; // 10%

        // 抽样校验比例
        public static final double SAMPLING_VALIDATION_RATIO = 0.05; // 5%

        // 抽样校验最小样本数
        public static final int MIN_SAMPLING_SIZE = 10;

        // 抽样校验最大样本数
        public static final int MAX_SAMPLING_SIZE = 1000;

        // 校验超时时间（毫秒）
        public static final long VALIDATION_TIMEOUT_MS = 60000; // 1分钟

        // 是否在校验失败时中断迁移
        public static final boolean STOP_ON_VALIDATION_FAILURE = false;
    }
}
