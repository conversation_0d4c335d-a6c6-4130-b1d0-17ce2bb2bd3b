package com.moego.server.grooming.service.dto;

import com.google.type.LatLng;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import java.time.LocalDate;
import java.util.List;

public record RouteOptimizationDTO(
        LatLng origin,
        LatLng destination,
        List<SmartScheduleGroomingDetailsDTO> serviceList,
        Integer businessId,
        Integer staffId,
        LocalDate date) {}
