package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.service.dto.report.ReportBaseAmountDTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @date 5/17/21 10:23 AM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GroomingReportApptInvoice extends ReportBaseAmountDTO {

    private String type;

    private BigDecimal discountRate;

    private String discountType;

    private BigDecimal discountedSubTotalAmount;

    private BigDecimal tipsRate;

    private String tipsType;

    private Integer invoiceStatus;

    // 卖 product 的 staff id 列表
    private List<Integer> sellProductStaffIds;
    // invoice service charge 列表
    private List<String> serviceChargeList;
}
