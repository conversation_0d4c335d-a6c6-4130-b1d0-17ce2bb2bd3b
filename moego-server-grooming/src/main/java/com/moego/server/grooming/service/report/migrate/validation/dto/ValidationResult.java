package com.moego.server.grooming.service.report.migrate.validation.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * 批量校验结果
 */
@Data
public class ValidationResult {

    /**
     * 校验是否成功
     */
    private boolean success;

    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 校验开始时间
     */
    private LocalDateTime startTime;

    /**
     * 校验结束时间
     */
    private LocalDateTime endTime;

    /**
     * 总记录数
     */
    private int totalCount;

    /**
     * 有效记录数
     */
    private int validCount;

    /**
     * 错误列表
     */
    private List<ValidationError> errors;

    /**
     * 校验耗时（毫秒）
     */
    private Long durationMs;

    public ValidationResult() {
        this.errors = new ArrayList<>();
        this.startTime = LocalDateTime.now();
        this.validCount = 0;
    }

    /**
     * 添加错误
     */
    public void addError(ValidationError error) {
        this.errors.add(error);
    }

    /**
     * 增加有效记录数
     */
    public void incrementValidCount() {
        this.validCount++;
    }

    /**
     * 完成校验，设置结束时间和耗时
     */
    public void complete() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null && this.endTime != null) {
            this.durationMs =
                    java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors.size();
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) validCount / totalCount * 100;
    }

    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    /**
     * 获取校验摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("校验结果: ").append(success ? "成功" : "失败");

        if (batchNumber != null) {
            sb.append(", 批次: ").append(batchNumber);
        }

        sb.append(", 总数: ")
                .append(totalCount)
                .append(", 有效: ")
                .append(validCount)
                .append(", 错误: ")
                .append(getErrorCount())
                .append(", 成功率: ")
                .append(String.format("%.2f%%", getSuccessRate()));

        if (durationMs != null) {
            sb.append(", 耗时: ").append(durationMs).append("ms");
        }

        return sb.toString();
    }
}
