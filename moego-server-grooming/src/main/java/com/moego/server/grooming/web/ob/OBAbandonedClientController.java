package com.moego.server.grooming.web.ob;

import static com.moego.common.utils.PermissionUtil.CAN_EXPORT_CLIENTS;
import static com.moego.common.utils.PermissionUtil.CREATE_NEW_CLIENT;
import static com.moego.common.utils.PermissionUtil.DELETE_CLIENT;
import static com.moego.common.utils.PermissionUtil.VIEW_CLIENT_PHONE;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static com.moego.common.utils.PermissionUtil.phoneNumberConfusion;
import static com.moego.common.utils.PermissionUtil.verifyStaffPermissions;
import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.AbandonStatus;
import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.LeadType;
import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.Order;
import static com.moego.server.grooming.web.params.SearchAbandonedClientParam.Property;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.PageDTO;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.enums.ClientSourceEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.client.ICustomerComposeClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.SaveCustomerPetResultDto;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import com.moego.server.grooming.config.S3Client;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.AbandonedBookingAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper;
import com.moego.server.grooming.mapper.param.SearchAbandonedRecordParam;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample;
import com.moego.server.grooming.mapstruct.CustomerMapper;
import com.moego.server.grooming.mapstruct.PetMapper;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import com.moego.server.grooming.service.ob.OBAbandonRecordService;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.service.ob.OBCustomerService;
import com.moego.server.grooming.service.ob.metrics.RecoverableRecordsMetricService;
import com.moego.server.grooming.utils.ExcelUtil;
import com.moego.server.grooming.utils.OBAbandonedUtil;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import com.moego.server.grooming.web.vo.AbandonedClientPageDTO;
import com.moego.server.grooming.web.vo.ExportVO;
import com.moego.server.grooming.web.vo.ob.AbandonedClientVO;
import com.moego.server.grooming.web.vo.ob.BulkAddClientVO;
import com.moego.server.grooming.web.vo.ob.BulkDeleteAbandonedVO;
import com.moego.server.grooming.web.vo.ob.ExportAbandonedClientVO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/grooming/ob/v2/abandoned-client")
public class OBAbandonedClientController {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final RecoverableRecordsMetricService recoverableRecordsMetricService;
    private final MoeBookOnlineAbandonRecordPetMapper abandonRecordPetMapper;
    private final ICustomerCustomerService customerApi;
    private final IBusinessStaffService staffApi;
    private final OBAddressService obAddressService;
    private final AppointmentMapperProxy appointmentMapper;
    private final S3Client s3Client;
    private final IBusinessBusinessService businessApi;
    private final GroomingServiceService groomingServiceService;
    private final OBCustomerService customerService;
    private final ICustomerComposeClient customerComposeClient;
    private final OBAbandonRecordService abandonRecordService;
    private final PermissionHelper permissionHelper;
    private final MigrateHelper migrateHelper;

    @PostMapping("/search")
    @Auth(AuthType.BUSINESS)
    public AbandonedClientPageDTO<AbandonedClientVO> search(@RequestBody @Valid SearchAbandonedClientParam param) {
        if (migrateHelper.isMigrate(AuthContext.get())) {
            permissionHelper.checkPermission(
                    AuthContext.get().companyId(),
                    Set.of(AuthContext.get().businessId()),
                    AuthContext.get().staffId(),
                    PermissionEnums.ACCESS_ABANDON_BOOKING_REQUEST);
        }
        AuthContext ac = AuthContext.get();
        checkStaffPermission(ac.getStaffId());

        if (param.getPageNo() != null && param.getPageSize() != null) {
            param.setPage(new SearchAbandonedClientParam.Page(param.getPageNo(), param.getPageSize()));
        }
        SearchAbandonedClientParam.TimeRange timeRange = param.getFilter().timeRange();
        long startTimeSec =
                LocalDateTime.now().minusDays(30).atZone(ZoneId.systemDefault()).toEpochSecond();
        long endTimeSec = LocalDateTime.now()
                .minusMinutes(60)
                .atZone(ZoneId.systemDefault())
                .toEpochSecond();
        if (Objects.nonNull(timeRange)
                && Objects.nonNull(timeRange.startTimeSec())
                && Objects.nonNull(timeRange.endTimeSec())) {
            startTimeSec = timeRange.startTimeSec();
            endTimeSec = timeRange.endTimeSec();
        }

        Long companyId = ac.companyId();
        Integer businessId = ac.getBusinessId();

        List<MoeBookOnlineAbandonRecord> latestRecords = abandonRecordService.listBySearchParam(businessId, param);

        List<MoeBookOnlineAbandonRecord> records = sortRecords(param, latestRecords);
        Set<Integer> filterCustomerIds = records.stream()
                .map(MoeBookOnlineAbandonRecord::getCustomerId)
                .filter(Objects::nonNull)
                .collect(toSet());
        Map<Integer, CustomerHasRequestDTO> customerHasRequestDTOMap;
        if (!CollectionUtils.isEmpty(filterCustomerIds)) {
            customerHasRequestDTOMap =
                    customerService.listCustomerHasRequestUpdate(businessId, new ArrayList<>(filterCustomerIds));
        } else {
            customerHasRequestDTOMap = Map.of();
        }
        List<AbandonedClientVO> data = records.parallelStream()
                .map(record -> {
                    AbandonedClientVO abandonedClientVO = this.toAbandonedClientVo(record);
                    CustomerHasRequestDTO requestDTO;
                    if (Objects.isNull(record.getCustomerId())
                            || Objects.isNull(requestDTO = customerHasRequestDTOMap.get(record.getCustomerId()))) {
                        return abandonedClientVO;
                    }
                    Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> requestPetMap;
                    if (Objects.nonNull(requestDTO.profileRequest())
                            && !CollectionUtils.isEmpty(
                                    requestDTO.profileRequest().getPets())) {
                        requestPetMap = requestDTO.profileRequest().getPets().stream()
                                .collect(toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
                    } else {
                        requestPetMap = Map.of();
                    }
                    abandonedClientVO.setHasRequestUpdate(requestDTO.hasRequestUpdate());
                    // Only the pet name is allowed to be modified here
                    Optional.ofNullable(abandonedClientVO.getPets())
                            .filter(pets -> !CollectionUtils.isEmpty(pets))
                            .ifPresent(pets -> pets.forEach(pet -> {
                                if (Objects.isNull(pet.getPetId())) {
                                    return;
                                }
                                CustomerProfileRequestDTO.PetProfileDTO requestPet = requestPetMap.get(pet.getPetId());
                                if (Objects.nonNull(requestPet) && StringUtils.hasText(requestPet.getPetName())) {
                                    pet.setPetName(requestPet.getPetName());
                                }
                            }));
                    return abandonedClientVO;
                })
                .toList();

        maskDataIfNecessary(ac.getStaffId(), data);

        PageDTO<AbandonedClientVO> page = PageDTO.create(
                data,
                latestRecords.size(),
                param.getPage().pageNumber(),
                param.getPage().pageSize());

        AbandonedClientPageDTO<AbandonedClientVO> result = AbandonedClientPageDTO.of(page);

        int recoverableRecordCount = recoverableRecordsMetricService.sumMetrics(
                new OBMetricTimeRangeDTO(companyId, businessId, startTimeSec, endTimeSec));
        int abandonedRecordCount = abandonRecordMapper.countOnlyAbandonedClients(
                businessId, startTimeSec, endTimeSec, OBStepEnum.listRecoverableSteps());

        result.setAbandonedClientTotalCount(recoverableRecordCount);
        result.setAbandonedClientAbandonedCount(abandonedRecordCount);

        return result;
    }

    @GetMapping("/export")
    @Auth(AuthType.BUSINESS)
    public ExportVO export(
            @RequestParam(value = "sheetName", required = false, defaultValue = "sheet1") String sheetName,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "startTimeSec", required = false) Long startTimeSec,
            @RequestParam(value = "endTimeSec", required = false) Long endTimeSec)
            throws IOException {
        AuthContext ac = AuthContext.get();
        StaffPermissions sp = staffApi.getBusinessRoleByStaffId(ac.getStaffId());
        verifyStaffPermissions(sp, CAN_EXPORT_CLIENTS);

        Integer businessId = ac.getBusinessId();
        SearchAbandonedRecordParam p = new SearchAbandonedRecordParam();
        p.setBusinessId(businessId);
        p.setStartTimeSec(startTimeSec);
        p.setEndTimeSec(endTimeSec);
        p.setAbandonStep(new HashSet<>(toNames(OBStepEnum.listRecoverableSteps())));
        p.setLeadType(Arrays.stream(LeadType.values()).map(LeadType::getValue).collect(Collectors.toSet()));

        List<MoeBookOnlineAbandonRecord> allRecords = abandonRecordMapper.searchByFilterPram(p);

        List<MoeBookOnlineAbandonRecord> filteredRecords = OBAbandonedUtil.filterLatestRecords(allRecords);

        BusinessDateTimeDTO businessDateTime = businessApi.getBusinessDateTime(businessId);
        if (businessDateTime == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Business not found: " + businessId);
        }

        List<ExportAbandonedClientVO> data = filteredRecords.parallelStream()
                .map(it -> toExportAbandonedClientVo(businessId, it, businessDateTime.getTimezoneName()))
                .toList();

        String defaultFileName = "abandoned-client-"
                + DateUtil.convertDateBySeconds(
                        DateUtil.get10Timestamp(), businessDateTime.getTimezoneName(), "yyyy-MM-dd-HH-mm-ss")
                + ".xlsx";
        String fileNameToUse = Optional.ofNullable(fileName).orElse(defaultFileName);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ExcelUtil.writeExcel(outputStream, sheetName, ExportAbandonedClientVO.class, data);
            String url = s3Client.uploadExcelFile(outputStream.toByteArray(), fileNameToUse);
            return new ExportVO().setUrl(url);
        }
    }

    @PostMapping("/bulk-add-client")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AbandonedBookingAction.BATCH_ADD_CLIENTS,
            resourceType = ResourceType.ABANDONED_BOOKING,
            details = "#param")
    public BulkAddClientVO bulkAddClient(@RequestBody @Valid SearchAbandonedClientParam param) {
        StaffPermissions sp =
                staffApi.getBusinessRoleByStaffId(AuthContext.get().getStaffId());
        verifyStaffPermissions(sp, CREATE_NEW_CLIENT);
        // only filter not associated with client's abandoned record
        param.setFilter(param.getFilter().toBuilder()
                .leadType(Stream.of(LeadType.NON_CLIENT).collect(Collectors.toSet()))
                .build());
        List<MoeBookOnlineAbandonRecord> abandonRecords =
                abandonRecordService.listBySearchParam(AuthContext.get().getBusinessId(), param).stream()
                        .filter(record -> Objects.isNull(record.getCustomerId()))
                        .toList();
        if (CollectionUtils.isEmpty(abandonRecords)) {
            return BulkAddClientVO.builder().build();
        }
        List<String> bookingFlowIds = abandonRecords.stream()
                .map(MoeBookOnlineAbandonRecord::getBookingFlowId)
                .toList();
        MoeBookOnlineAbandonRecordPetExample example = new MoeBookOnlineAbandonRecordPetExample();
        example.createCriteria()
                .andBusinessIdEqualTo(AuthContext.get().getBusinessId())
                .andBookingFlowIdIn(bookingFlowIds);
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList = abandonRecordPetMapper.selectByExample(example);
        Map<String, List<MoeBookOnlineAbandonRecordPet>> abandonPetMap = abandonRecordPetList.stream()
                .collect(Collectors.groupingBy(MoeBookOnlineAbandonRecordPet::getBookingFlowId));
        int total = abandonRecords.size();
        AtomicInteger success = new AtomicInteger(0);
        AtomicInteger fail = new AtomicInteger(0);
        List<String> failIds = new ArrayList<>();
        Set<Integer> includeCustomerIds = new HashSet<>();
        Set<Integer> excludeCustomerIds = new HashSet<>();
        abandonRecords.forEach(abandonRecord -> {
            SaveWithPetCustomerVo customerVo = CustomerMapper.INSTANCE.abandonEntity2SaveVO(abandonRecord);
            List<MoeBookOnlineAbandonRecordPet> abandonRecordPets = abandonPetMap.get(abandonRecord.getBookingFlowId());
            customerVo.setPetList(PetMapper.INSTANCE.abandonEntity2SaveVO(abandonRecordPets));
            customerVo.setSource(ClientSourceEnum.SOURCE_ONLINE_BOOKING.getSource());
            try {
                SaveCustomerPetResultDto result = customerComposeClient.createCustomerAndPets(
                        AuthContext.get().companyId(),
                        AuthContext.get().getBusinessId(),
                        AuthContext.get().getStaffId(),
                        customerVo);
                if (BooleanUtils.isTrue(result.getResult())) {
                    success.incrementAndGet();
                    includeCustomerIds.add(result.getId());
                } else {
                    fail.incrementAndGet();
                    failIds.add(abandonRecord.getBookingFlowId());
                }
            } catch (Exception e) {
                fail.incrementAndGet();
                failIds.add(abandonRecord.getBookingFlowId());
            }
        });
        if (!CollectionUtils.isEmpty(param.getFilter().excludeBookingFlowIds())) {
            MoeBookOnlineAbandonRecordExample recordExample = new MoeBookOnlineAbandonRecordExample();
            recordExample
                    .createCriteria()
                    .andBookingFlowIdIn(new ArrayList<>(param.getFilter().excludeBookingFlowIds()));
            List<MoeBookOnlineAbandonRecord> recordList = abandonRecordMapper.selectByExample(recordExample);
            excludeCustomerIds = recordList.stream()
                    .map(MoeBookOnlineAbandonRecord::getCustomerId)
                    .filter(Objects::nonNull)
                    .collect(toSet());
        }
        return BulkAddClientVO.builder()
                .total(total)
                .success(success.get())
                .fail(fail.get())
                .failIds(failIds)
                .includeCustomerIds(
                        !CollectionUtils.isEmpty(param.getFilter().includeBookingFlowIds())
                                ? includeCustomerIds
                                : Set.of())
                .excludeCustomerIds(excludeCustomerIds)
                .build();
    }

    @PostMapping("/bulk-delete-record")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AbandonedBookingAction.BATCH_DELETE,
            resourceType = ResourceType.ABANDONED_BOOKING,
            details = "#param")
    public BulkDeleteAbandonedVO bulkDeleteAbandonedRecords(@RequestBody @Valid SearchAbandonedClientParam param) {
        StaffPermissions sp =
                staffApi.getBusinessRoleByStaffId(AuthContext.get().getStaffId());
        verifyStaffPermissions(sp, DELETE_CLIENT);
        List<MoeBookOnlineAbandonRecord> abandonRecords =
                abandonRecordService.listBySearchParam(AuthContext.get().getBusinessId(), param);
        if (CollectionUtils.isEmpty(abandonRecords)) {
            return BulkDeleteAbandonedVO.builder().build();
        }
        // existing client
        Set<Integer> existingClientCustomerIds = abandonRecords.stream()
                .filter(record -> AbandonStatus.NOT_RECOVERED_STATUSES.contains(record.getAbandonStatus()))
                .filter(record -> Objects.equals(record.getLeadType(), LeadType.EXISTING_CLIENT.getValue())
                        && Objects.nonNull(record.getCustomerId()))
                .map(MoeBookOnlineAbandonRecord::getCustomerId)
                .collect(toSet());
        // new visitor
        Set<String> newVisitorPhoneNumbers = abandonRecords.stream()
                .filter(record -> AbandonStatus.NOT_RECOVERED_STATUSES.contains(record.getAbandonStatus()))
                .filter(record -> Objects.equals(record.getLeadType(), LeadType.NEW_VISITOR.getValue())
                        && Objects.nonNull(record.getPhoneNumber()))
                .map(MoeBookOnlineAbandonRecord::getPhoneNumber)
                .collect(toSet());
        abandonRecordService.deleteExistingClientAbandonRecords(
                AuthContext.get().getBusinessId(), existingClientCustomerIds);
        abandonRecordService.deleteNewVisitorAbandonRecords(AuthContext.get().getBusinessId(), newVisitorPhoneNumbers);
        // recovered record
        Set<Integer> recoveredIds = abandonRecords.stream()
                .filter(record -> Objects.equals(record.getAbandonStatus(), AbandonStatus.RECOVERED.getValue()))
                .map(MoeBookOnlineAbandonRecord::getId)
                .collect(toSet());
        if (!CollectionUtils.isEmpty(recoveredIds)) {
            MoeBookOnlineAbandonRecord record = new MoeBookOnlineAbandonRecord();
            record.setIsDeleted(Boolean.TRUE);
            record.setDeleteType(AbandonDeleteTypeEnum.MANUAL.getType());
            MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
            example.createCriteria().andIdIn(new ArrayList<>(recoveredIds));
            abandonRecordMapper.updateByExampleSelective(record, example);
        }
        return BulkDeleteAbandonedVO.builder().success(abandonRecords.size()).build();
    }

    private static List<MoeBookOnlineAbandonRecord> sortRecords(
            SearchAbandonedClientParam param, List<MoeBookOnlineAbandonRecord> records) {
        // 过滤所有 new client abandoned 记录，只保留最新的一条
        Comparator<MoeBookOnlineAbandonRecord> comparator = getComparator(param);
        return records.stream()
                .sorted(param.getSort().order() == Order.ASC ? comparator : comparator.reversed())
                .skip((long) (param.getPage().pageNumber() - 1)
                        * param.getPage().pageSize())
                .limit(param.getPage().pageSize())
                .toList();
    }

    @SuppressWarnings("rawtypes")
    private static Comparator<MoeBookOnlineAbandonRecord> getComparator(SearchAbandonedClientParam param) {
        return Comparator.<MoeBookOnlineAbandonRecord, Comparable>comparing(
                r -> {
                    Property property = param.getSort().property();
                    return switch (property) {
                        case ID -> r.getId();
                        case ABANDON_TIME -> r.getAbandonTime();
                    };
                },
                nullsFirst(naturalOrder()));
    }

    private void maskDataIfNecessary(Integer staffId, List<AbandonedClientVO> data) {
        StaffPermissions sp = staffApi.getBusinessRoleByStaffId(staffId);
        boolean showPhone = checkStaffPermissionsInfo(sp, VIEW_CLIENT_PHONE);
        if (!showPhone) {
            data.forEach(vo -> vo.setPhoneNumber(phoneNumberConfusion(vo.getPhoneNumber())));
        }
    }

    static int getLeadTypeOrder(String leadType) {
        if (Objects.equals(LeadType.NEW_VISITOR.getValue(), leadType)) {
            return 0;
        }
        if (Objects.equals(LeadType.EXISTING_CLIENT.getValue(), leadType)) {
            return 1;
        }
        return 2;
    }

    private ExportAbandonedClientVO toExportAbandonedClientVo(
            Integer businessId, MoeBookOnlineAbandonRecord abandonRecord, String timeZone) {
        ExportAbandonedClientVO vo = new ExportAbandonedClientVO();
        vo.setAbandonDateTime(toDateTimeString(abandonRecord.getAbandonTime(), timeZone));
        vo.setType(toReadableString(abandonRecord.getLeadType()));
        vo.setAbandonedStep(toReadableString(abandonRecord.getAbandonStep()));

        vo.setFirstName(abandonRecord.getFirstName());
        vo.setLastName(abandonRecord.getLastName());
        vo.setEmail(abandonRecord.getEmail());
        vo.setContact(abandonRecord.getPhoneNumber());

        vo.setAddress(getAddress(abandonRecord));

        if (abandonRecord.getAppointmentDate() != null) {
            String serviceDateTime = abandonRecord.getAppointmentDate();
            if (abandonRecord.getAppointmentStartTime() != null) {
                serviceDateTime += " " + minute2Hour(abandonRecord.getAppointmentStartTime());
            }
            vo.setServiceDateTime(serviceDateTime);
        }
        vo.setStaff(getStaffName(abandonRecord.getBusinessId(), abandonRecord.getStaffId()));
        vo.setStatus(toReadableString(abandonRecord.getAbandonStatus()));

        // existing client 优先使用 customer 表数据
        Integer customerId = abandonRecord.getCustomerId();
        if (customerId != null) {
            MoeBusinessCustomerDTO c = customerApi.getCustomerWithDeleted(customerId);
            if (c != null) {
                vo.setFirstName(c.getFirstName());
                vo.setLastName(c.getLastName());
                vo.setEmail(c.getEmail());
            }
        }

        // 优先使用 customer address 表数据
        if (abandonRecord.getAddressId() != null) {
            CustomerAddressDto ca = obAddressService.getCustomerAddress(abandonRecord.getAddressId());
            if (ca != null) {
                vo.setAddress(getAddress(ca));
            }
        }

        List<MoeBookOnlineAbandonRecordPet> recordPets = abandonRecordPetMapper.listRecordPetByBookingFlowId(
                abandonRecord.getBusinessId(), abandonRecord.getBookingFlowId());
        List<AbandonedClientVO.Pet> pets =
                recordPets.stream().map(PetMapper.INSTANCE::abandonedPet2VO).toList();
        if (!ObjectUtils.isEmpty(pets)) {
            vo.setPetName(pets.stream()
                    .map(p -> "%s(%s)".formatted(p.getPetName(), p.getBreed()))
                    .collect(joining(", ")));
        }

        Map<String, List<Integer>> petNameToServiceIds = recordPets.stream()
                .filter(pet -> StringUtils.hasText(pet.getPetName()))
                .map(pet -> Pair.of(
                        pet.getPetName(),
                        Stream.concat(
                                        Optional.ofNullable(pet.getServiceId()).stream(),
                                        Optional.ofNullable(pet.getAddonIds())
                                                .map(addons -> JsonUtil.toList(addons, Integer.class))
                                                .orElseGet(List::of)
                                                .stream())
                                .toList()))
                .collect(toMap(Pair::getFirst, Pair::getSecond, (oldV, newV) -> {
                    List<Integer> old = new ArrayList<>(oldV);
                    old.addAll(newV);
                    return old;
                }));

        if (!ObjectUtils.isEmpty(petNameToServiceIds)) {
            // format pet service
            // see
            // https://docs.google.com/spreadsheets/d/1ePa9vVlyrr3o7KsCfoflcfjuCfC19b5Od_jknt5fZ64/edit#gid=1601095103
            StringBuilder s = new StringBuilder();

            Set<Integer> serviceIds = petNameToServiceIds.values().stream()
                    .flatMap(Collection::stream)
                    .collect(toSet());
            Map<Integer, MoeGroomingServiceDTO> serviceIdToService =
                    groomingServiceService.getServicesByServiceIds(businessId, List.copyOf(serviceIds)).stream()
                            .collect(toMap(MoeGroomingServiceDTO::getId, Function.identity()));

            for (var en : petNameToServiceIds.entrySet()) {
                String petName = en.getKey();
                List<MoeGroomingServiceDTO> services = petNameToServiceIds.getOrDefault(petName, List.of()).stream()
                        .map(serviceIdToService::get)
                        .filter(Objects::nonNull)
                        .toList();
                if (CollectionUtils.isEmpty(services)) {
                    continue;
                }

                StringBuilder sb = new StringBuilder();
                sb.append(petName).append(": ");
                services.forEach(service -> {
                    if (Objects.equals((byte) 1 /*main service*/, service.getType())) {
                        sb.append(service.getName()).append(", ");
                    } else {
                        sb.append(service.getName()).append("(add-on), ");
                    }
                });
                s.append(sb);
            }

            if (s.length() > 2) {
                s.delete(s.length() - 2, s.length());
            }
            vo.setSelectedService(s.toString());
        }

        return vo;
    }

    private static String getAddress(CustomerAddressDto ca) {
        String addr = "";
        if (ca.getAddress1() != null) {
            addr = ca.getAddress1();
        }
        if (ca.getAddress2() != null) {
            addr += " " + ca.getAddress2();
        }
        if (ca.getCity() != null) {
            addr += ", " + ca.getCity();
        }
        if (ca.getState() != null) {
            addr += ", " + ca.getState();
        }
        if (ca.getCountry() != null) {
            addr += ", " + ca.getCountry();
        }
        if (ca.getZipcode() != null) {
            addr += ", " + ca.getZipcode();
        }
        return addr;
    }

    private static String getAddress(MoeBookOnlineAbandonRecord abandonRecord) {
        String addr = "";
        if (abandonRecord.getAddress1() != null) {
            addr = abandonRecord.getAddress1();
        }
        if (abandonRecord.getAddress2() != null) {
            addr += " " + abandonRecord.getAddress2();
        }
        if (abandonRecord.getCity() != null) {
            addr += ", " + abandonRecord.getCity();
        }
        if (abandonRecord.getState() != null) {
            addr += ", " + abandonRecord.getState();
        }
        if (abandonRecord.getCountry() != null) {
            addr += ", " + abandonRecord.getCountry();
        }
        if (abandonRecord.getZipcode() != null) {
            addr += ", " + abandonRecord.getZipcode();
        }
        return addr;
    }

    private String getStaffName(Integer businessId, Integer staffId) {
        StaffIdParams params = new StaffIdParams();
        params.setBusinessId(businessId);
        params.setStaffId(staffId);
        MoeStaffDto staff = staffApi.getStaff(params);
        if (staff != null) {
            return staff.getFirstName() + " " + staff.getLastName();
        }
        return null;
    }

    private void checkStaffPermission(Integer staffId) {
        if (staffId == null) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
        }
        StaffPermissions permission = staffApi.getBusinessRoleByStaffId(staffId);
        boolean canAccessAbandonedBookings =
                PermissionUtil.checkStaffPermissionsInfo(permission, PermissionUtil.CAN_ACCESS_ABANDONED_BOOKINGS);
        if (!canAccessAbandonedBookings) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "You don't have permission to access abandoned bookings");
        }
    }

    private static String toDateTimeString(Long timestampSec, String timeZone) {
        return DateUtil.convertDateBySeconds(timestampSec, timeZone, DateUtil.STANDARD_DATE_TIME);
    }

    private static String minute2Hour(Integer minute) {
        int minuteToUse = Optional.ofNullable(minute).orElse(0);
        return String.format("%d:%02d", minuteToUse / 60, minuteToUse % 60);
    }

    private AbandonedClientVO toAbandonedClientVo(MoeBookOnlineAbandonRecord abandonRecord) {
        AbandonedClientVO vo = new AbandonedClientVO();
        vo.setId(abandonRecord.getId());
        vo.setBookingFlowId(abandonRecord.getBookingFlowId());
        vo.setAbandonStatus(abandonRecord.getAbandonStatus());
        vo.setFirstName(abandonRecord.getFirstName());
        vo.setLastName(abandonRecord.getLastName());
        vo.setPhoneNumber(abandonRecord.getPhoneNumber());
        vo.setCareType(abandonRecord.getCareType());
        vo.setLastContactTime(Math.max(abandonRecord.getLastTextedTime(), abandonRecord.getLastEmailedTime()));
        Integer customerId = abandonRecord.getCustomerId();
        if (customerId != null) {
            MoeBusinessCustomerDTO c = customerApi.getCustomerWithDeleted(customerId);
            if (c != null) {
                vo.setAvatarPath(c.getAvatarPath());
                vo.setFirstName(c.getFirstName());
                vo.setLastName(c.getLastName());
            }
        }

        vo.setPets(
                abandonRecordPetMapper
                        .listRecordPetByBookingFlowId(abandonRecord.getBusinessId(), abandonRecord.getBookingFlowId())
                        .stream()
                        .map(PetMapper.INSTANCE::abandonedPet2VO)
                        .toList());

        vo.setAbandonStep(abandonRecord.getAbandonStep());
        vo.setAbandonTime(abandonRecord.getAbandonTime());
        Optional.ofNullable(abandonRecord.getAppointmentId())
                .map(appointmentMapper::selectByPrimaryKey)
                .ifPresent(appt -> {
                    AbandonedClientVO.Appointment a = new AbandonedClientVO.Appointment();
                    a.setId(appt.getId());
                    // 当 book_online_status = 1(ob request) 且 status == 1(unconfirmed) 时，说明该预约还在 OB request list
                    if (Objects.equals(appt.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                            && Objects.equals(appt.getStatus(), AppointmentStatusEnum.UNCONFIRMED.getValue())) {
                        a.setStatus((byte) 1); // OB request
                    } else {
                        a.setStatus((byte) 0); // non-OB request
                    }
                    vo.setAppointment(a);
                });
        vo.setCustomerId(abandonRecord.getCustomerId());
        return vo;
    }

    /**
     * CONTACTED -> Contacted
     * <p> NEW_VISITOR -> New Visitor
     * <p> new_visitor -> New Visitor
     */
    private static String toReadableString(String str) {
        return Optional.ofNullable(str)
                .map(String::toLowerCase)
                .map(s -> s.replace("_", " "))
                .map(StringUtils::capitalize)
                .orElse(null);
    }

    private static List<String> toNames(Collection<? extends Enum<?>> enums) {
        return enums.stream().map(Enum::name).toList();
    }
}
