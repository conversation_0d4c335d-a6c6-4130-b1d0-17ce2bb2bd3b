<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.AbandonedScheduleMessageSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="client_types" jdbcType="CHAR" property="clientTypes" />
    <result column="abandoned_steps" jdbcType="CHAR" property="abandonedSteps" />
    <result column="send_out_type" jdbcType="VARCHAR" property="sendOutType" />
    <result column="on_type_days" jdbcType="CHAR" property="onTypeDays" />
    <result column="on_type_minute" jdbcType="INTEGER" property="onTypeMinute" />
    <result column="wait_for_type_minute" jdbcType="INTEGER" property="waitForTypeMinute" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="is_enabled" jdbcType="BIT" property="isEnabled" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="enabled_at" jdbcType="TIMESTAMP" property="enabledAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, client_types, abandoned_steps, send_out_type, on_type_days, on_type_minute, 
    wait_for_type_minute, message, is_enabled, created_at, updated_at, enabled_at
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from abandoned_schedule_message_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from abandoned_schedule_message_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from abandoned_schedule_message_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from abandoned_schedule_message_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into abandoned_schedule_message_setting (business_id, client_types, abandoned_steps, 
      send_out_type, on_type_days, on_type_minute, 
      wait_for_type_minute, message, is_enabled, 
      created_at, updated_at, enabled_at
      )
    values (#{businessId,jdbcType=INTEGER}, #{clientTypes,jdbcType=CHAR}, #{abandonedSteps,jdbcType=CHAR}, 
      #{sendOutType,jdbcType=VARCHAR}, #{onTypeDays,jdbcType=CHAR}, #{onTypeMinute,jdbcType=INTEGER}, 
      #{waitForTypeMinute,jdbcType=INTEGER}, #{message,jdbcType=VARCHAR}, #{isEnabled,jdbcType=BIT}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{enabledAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into abandoned_schedule_message_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="clientTypes != null">
        client_types,
      </if>
      <if test="abandonedSteps != null">
        abandoned_steps,
      </if>
      <if test="sendOutType != null">
        send_out_type,
      </if>
      <if test="onTypeDays != null">
        on_type_days,
      </if>
      <if test="onTypeMinute != null">
        on_type_minute,
      </if>
      <if test="waitForTypeMinute != null">
        wait_for_type_minute,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="isEnabled != null">
        is_enabled,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="enabledAt != null">
        enabled_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="clientTypes != null">
        #{clientTypes,jdbcType=CHAR},
      </if>
      <if test="abandonedSteps != null">
        #{abandonedSteps,jdbcType=CHAR},
      </if>
      <if test="sendOutType != null">
        #{sendOutType,jdbcType=VARCHAR},
      </if>
      <if test="onTypeDays != null">
        #{onTypeDays,jdbcType=CHAR},
      </if>
      <if test="onTypeMinute != null">
        #{onTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="waitForTypeMinute != null">
        #{waitForTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        #{isEnabled,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enabledAt != null">
        #{enabledAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from abandoned_schedule_message_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update abandoned_schedule_message_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.clientTypes != null">
        client_types = #{record.clientTypes,jdbcType=CHAR},
      </if>
      <if test="record.abandonedSteps != null">
        abandoned_steps = #{record.abandonedSteps,jdbcType=CHAR},
      </if>
      <if test="record.sendOutType != null">
        send_out_type = #{record.sendOutType,jdbcType=VARCHAR},
      </if>
      <if test="record.onTypeDays != null">
        on_type_days = #{record.onTypeDays,jdbcType=CHAR},
      </if>
      <if test="record.onTypeMinute != null">
        on_type_minute = #{record.onTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="record.waitForTypeMinute != null">
        wait_for_type_minute = #{record.waitForTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.isEnabled != null">
        is_enabled = #{record.isEnabled,jdbcType=BIT},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enabledAt != null">
        enabled_at = #{record.enabledAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update abandoned_schedule_message_setting
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      client_types = #{record.clientTypes,jdbcType=CHAR},
      abandoned_steps = #{record.abandonedSteps,jdbcType=CHAR},
      send_out_type = #{record.sendOutType,jdbcType=VARCHAR},
      on_type_days = #{record.onTypeDays,jdbcType=CHAR},
      on_type_minute = #{record.onTypeMinute,jdbcType=INTEGER},
      wait_for_type_minute = #{record.waitForTypeMinute,jdbcType=INTEGER},
      message = #{record.message,jdbcType=VARCHAR},
      is_enabled = #{record.isEnabled,jdbcType=BIT},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      enabled_at = #{record.enabledAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update abandoned_schedule_message_setting
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="clientTypes != null">
        client_types = #{clientTypes,jdbcType=CHAR},
      </if>
      <if test="abandonedSteps != null">
        abandoned_steps = #{abandonedSteps,jdbcType=CHAR},
      </if>
      <if test="sendOutType != null">
        send_out_type = #{sendOutType,jdbcType=VARCHAR},
      </if>
      <if test="onTypeDays != null">
        on_type_days = #{onTypeDays,jdbcType=CHAR},
      </if>
      <if test="onTypeMinute != null">
        on_type_minute = #{onTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="waitForTypeMinute != null">
        wait_for_type_minute = #{waitForTypeMinute,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        is_enabled = #{isEnabled,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enabledAt != null">
        enabled_at = #{enabledAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.AbandonedScheduleMessageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update abandoned_schedule_message_setting
    set business_id = #{businessId,jdbcType=INTEGER},
      client_types = #{clientTypes,jdbcType=CHAR},
      abandoned_steps = #{abandonedSteps,jdbcType=CHAR},
      send_out_type = #{sendOutType,jdbcType=VARCHAR},
      on_type_days = #{onTypeDays,jdbcType=CHAR},
      on_type_minute = #{onTypeMinute,jdbcType=INTEGER},
      wait_for_type_minute = #{waitForTypeMinute,jdbcType=INTEGER},
      message = #{message,jdbcType=VARCHAR},
      is_enabled = #{isEnabled,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      enabled_at = #{enabledAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>