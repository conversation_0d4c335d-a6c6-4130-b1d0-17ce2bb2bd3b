package com.moego.server.grooming.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ParamAuthService {

    @Autowired
    private MoeGroomingServiceCategoryMapper moeGroomingServiceCategoryMapper;

    public void authServiceCategoryWithCid(Integer categoryId, Long companyId) throws BizException {
        if (categoryId == 0) {
            return;
        }
        var serviceCategory = moeGroomingServiceCategoryMapper.selectByPrimaryKey(categoryId);
        if (serviceCategory == null || !serviceCategory.getCompanyId().equals(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_SERVICE_CATEGORY_NOT_FOUND);
        }
    }
}
