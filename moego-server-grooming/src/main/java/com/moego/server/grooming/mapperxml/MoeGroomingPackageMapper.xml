<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingPackageMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cart_package_id" jdbcType="INTEGER" property="cartPackageId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="retail_invoice_item_id" jdbcType="INTEGER" property="retailInvoiceItemId" />
    <result column="confirmation_id" jdbcType="VARCHAR" property="confirmationId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="package_desc" jdbcType="VARCHAR" property="packageDesc" />
    <result column="package_price" jdbcType="DECIMAL" property="packagePrice" />
    <result column="purchase_time" jdbcType="BIGINT" property="purchaseTime" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="expiration_date" jdbcType="VARCHAR" property="expirationDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, cart_package_id, business_id, company_id, customer_id, staff_id, retail_invoice_item_id,
    confirmation_id, package_name, package_desc, package_price, purchase_time, start_time,
    end_time, create_time, update_time, status, expiration_date
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_package
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_package
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package (cart_package_id, business_id, company_id,
      customer_id, staff_id, retail_invoice_item_id,
      confirmation_id, package_name, package_desc,
      package_price, purchase_time, start_time,
      end_time, create_time, update_time,
      status, expiration_date)
    values (#{cartPackageId,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, #{companyId,jdbcType=BIGINT},
      #{customerId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{retailInvoiceItemId,jdbcType=INTEGER},
      #{confirmationId,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, #{packageDesc,jdbcType=VARCHAR},
      #{packagePrice,jdbcType=DECIMAL}, #{purchaseTime,jdbcType=BIGINT}, #{startTime,jdbcType=BIGINT},
      #{endTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{status,jdbcType=TINYINT}, #{expirationDate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cartPackageId != null">
        cart_package_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="retailInvoiceItemId != null">
        retail_invoice_item_id,
      </if>
      <if test="confirmationId != null">
        confirmation_id,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="packageDesc != null">
        package_desc,
      </if>
      <if test="packagePrice != null">
        package_price,
      </if>
      <if test="purchaseTime != null">
        purchase_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cartPackageId != null">
        #{cartPackageId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="retailInvoiceItemId != null">
        #{retailInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="confirmationId != null">
        #{confirmationId,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageDesc != null">
        #{packageDesc,jdbcType=VARCHAR},
      </if>
      <if test="packagePrice != null">
        #{packagePrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.cartPackageId != null">
        cart_package_id = #{record.cartPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.staffId != null">
        staff_id = #{record.staffId,jdbcType=INTEGER},
      </if>
      <if test="record.retailInvoiceItemId != null">
        retail_invoice_item_id = #{record.retailInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="record.confirmationId != null">
        confirmation_id = #{record.confirmationId,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDesc != null">
        package_desc = #{record.packageDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.packagePrice != null">
        package_price = #{record.packagePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseTime != null">
        purchase_time = #{record.purchaseTime,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.expirationDate != null">
        expiration_date = #{record.expirationDate,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package
    set id = #{record.id,jdbcType=INTEGER},
      cart_package_id = #{record.cartPackageId,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      staff_id = #{record.staffId,jdbcType=INTEGER},
      retail_invoice_item_id = #{record.retailInvoiceItemId,jdbcType=INTEGER},
      confirmation_id = #{record.confirmationId,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      package_desc = #{record.packageDesc,jdbcType=VARCHAR},
      package_price = #{record.packagePrice,jdbcType=DECIMAL},
      purchase_time = #{record.purchaseTime,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=BIGINT},
      end_time = #{record.endTime,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      expiration_date = #{record.expirationDate,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package
    <set>
      <if test="cartPackageId != null">
        cart_package_id = #{cartPackageId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="retailInvoiceItemId != null">
        retail_invoice_item_id = #{retailInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="confirmationId != null">
        confirmation_id = #{confirmationId,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageDesc != null">
        package_desc = #{packageDesc,jdbcType=VARCHAR},
      </if>
      <if test="packagePrice != null">
        package_price = #{packagePrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTime != null">
        purchase_time = #{purchaseTime,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingPackage">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_package
    set cart_package_id = #{cartPackageId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=BIGINT},
      customer_id = #{customerId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      retail_invoice_item_id = #{retailInvoiceItemId,jdbcType=INTEGER},
      confirmation_id = #{confirmationId,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      package_desc = #{packageDesc,jdbcType=VARCHAR},
      package_price = #{packagePrice,jdbcType=DECIMAL},
      purchase_time = #{purchaseTime,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      expiration_date = #{expirationDate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <resultMap id="GroomingPackageDTOResultMap" type="com.moego.server.grooming.dto.GroomingPackageDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="cart_package_id" jdbcType="INTEGER" property="cartPackageId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="retail_invoice_item_id" jdbcType="INTEGER" property="retailInvoiceItemId" />
    <result column="confirmation_id" jdbcType="VARCHAR" property="confirmationId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="package_desc" jdbcType="VARCHAR" property="packageDesc" />
    <result column="package_price" jdbcType="DECIMAL" property="packagePrice" />
    <result column="purchase_time" jdbcType="BIGINT" property="purchaseTime" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="expiration_date" jdbcType="VARCHAR" property="expirationDate" />
  </resultMap>

  <resultMap id="GroomingPackageServiceInfoDTOResultMap" type="com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO">
    <result column="package_name" property="packageName" />
    <result column="package_id" property="packageId" />
    <result column="service_id" property="serviceId" />
    <result column="service_name" property="serviceName" />
    <result column="packageServiceId" property="packageServiceId" />
    <result column="remaining_quantity" property="remainingQuantity" />
  </resultMap>

  <select id="queryCustomerPackageList" resultMap="GroomingPackageDTOResultMap">
        select
        id, cart_package_id, business_id, customer_id, staff_id, retail_invoice_item_id,
    confirmation_id, package_name, package_desc, package_price, purchase_time, start_time,
    end_time, create_time, update_time, status, expiration_date
    from moe_grooming_package
    where business_id = #{businessId}
    and customer_id = #{customerId}
    and status = 1
  </select>

  <select id="queryCustomerPackageListByBusinessIds" resultMap="GroomingPackageDTOResultMap">
    select
      id, cart_package_id, business_id, customer_id, staff_id, retail_invoice_item_id,
      confirmation_id, package_name, package_desc, package_price, purchase_time, start_time,
      end_time, create_time, update_time, status, expiration_date
    from moe_grooming_package
    where business_id in
          <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
            #{businessId}
          </foreach>
      and customer_id = #{customerId}
      and status = 1
  </select>

  <select id="queryCustomerPackageListByCompany" resultMap="GroomingPackageDTOResultMap">
    select
      id, cart_package_id, customer_id, staff_id, retail_invoice_item_id,
      confirmation_id, package_name, package_desc, package_price, purchase_time, start_time,
      end_time, create_time, update_time, status, business_id, expiration_date
    from moe_grooming_package
    where company_id = #{companyId}
      and customer_id = #{customerId}
      and status = 1
  </select>

  <select id="queryCustomerPackageByServiceIds" resultMap="GroomingPackageServiceInfoDTOResultMap">
    SELECT
    p.package_name ,
    s.remaining_quantity,s.package_id,s.service_id,s.id packageServiceId,
    (SELECT name from moe_grooming_service where id = s.service_id) service_name
    from moe_grooming_package p
    left join moe_grooming_package_service s on p.id = s.package_id
    where p.customer_id = #{customerId}
    and p.business_id = #{businessId}
    and p.status = 1
    and s.service_id IN
    <foreach close=")" collection="serviceIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and p.expiration_date &gt;= #{date}
  </select>

  <select id="queryCustomerPackageListByFilter" resultMap="GroomingPackageDTOResultMap">
    select
    id, cart_package_id, business_id, customer_id, staff_id, retail_invoice_item_id,
    confirmation_id, package_name, package_desc, package_price, purchase_time, start_time,
    end_time, create_time, update_time, status, expiration_date
    from moe_grooming_package
    where company_id = #{companyId}
    and customer_id in
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach>
    <if test="businessId != null">
      and business_id = #{businessId}
    </if>
    <if test="date != null">
      and expiration_date &gt;= #{date}
    </if>
    and status = 1
  </select>
</mapper>
