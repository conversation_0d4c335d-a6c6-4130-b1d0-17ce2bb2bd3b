package com.moego.server.grooming.web.vo;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class Business2023SummaryVo {

    private Staff2023Summary staff2023Summary;
    private Business2023Summary businessSummary;
    private Owner2023Summary ownerSummary;

    @Data
    public static class Staff2023Summary {
        private Integer totalServiceTimeInMin;
        private LongestWorkingDay2023 longestWorkingDay;

        private Integer takeCaredPetCnt;
        private String MostPopularPetName;

        private List<MostPetBreed> mostPetBreeds;
        private List<MostPetService> mostPetServices;

        private Review2023Summary reviewSummary;
    }

    @Data
    public static class Business2023Summary {
        private Integer totalServiceTimeInMin;
        private LongestWorkingDay2023 longestWorkingDay;

        private Integer takeCaredPetCnt;
        private String MostPopularPetName;

        private List<MostPetBreed> mostPetBreeds;
        private List<MostPetService> mostPetServices;

        private Review2023Summary reviewSummary;
    }

    @Data
    public static class Owner2023Summary {
        private MoegoPay2023Summary moegoPaySummary;
        private OnlineBooking2023Summary onlineBookingSummary;
        private GroomingReport2023Summary groomingReportSummary;
        private MarketingCampaigns2023Summary marketingCampaignsSummary;

        @Data
        public static class MoegoPay2023Summary {
            private String transactionAmount;
            private String feeSaved;
            private String tipsBoosted;
        }

        @Data
        public static class OnlineBooking2023Summary {
            private Integer obRequestCnt;
            private Integer abandonedBookingsRecovered;
            private BigDecimal recoveredRevenue;
        }

        @Data
        public static class GroomingReport2023Summary {
            private Integer reportSendCnt;
            private String mostFrequentMood;
        }

        @Data
        public static class MarketingCampaigns2023Summary {
            private Integer marketingEmailsSentCnt;
            private Integer marketingEmailsOpenedCnt;
            private Integer contributedBookingsCnt;
        }
    }

    @Data
    public static class LongestWorkingDay2023 {
        private String date;
        private Integer workingTimeInMin;
    }

    @Data
    @AllArgsConstructor
    public static class MostPetBreed {
        private String breed;
        private Integer count;
    }

    @Data
    @AllArgsConstructor
    public static class MostPetService {
        private String serviceName;
        private Integer count;
    }

    @Data
    public static class Review2023Summary {
        private Integer reviewCnt;
        private String avgScore;
    }
}
