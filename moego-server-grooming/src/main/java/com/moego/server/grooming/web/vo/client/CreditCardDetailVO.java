package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Data
@Accessors(chain = true)
public class CreditCardDetailVO {

    @Schema(description = "Card type")
    private String cardType;

    @Schema(description = "Card number")
    private String cardNumber;

    @Schema(description = "Expire month")
    private String expMonth;

    @Schema(description = "Expire year")
    private String expYear;
}
