package com.moego.server.grooming.web.vo.ob;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbandonedClientFilterVO {

    /**
     * filter name, eg: status, lead_type, abandoned_status
     */
    private String filter;

    private List<Option> options;

    @Data
    @Accessors(chain = true)
    public static class Option {

        /**
         * option value, could be String, int etc.
         */
        private String value;

        private int count;
    }
}
