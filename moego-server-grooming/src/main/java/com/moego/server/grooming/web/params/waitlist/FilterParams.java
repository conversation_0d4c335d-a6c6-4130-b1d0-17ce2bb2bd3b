package com.moego.server.grooming.web.params.waitlist;

import com.moego.server.business.dto.TimeRangeDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;

@Builder
public record FilterParams(
        Boolean isOnGoing,
        @Schema(description = "availability query range start, yyyy-MM-ddTHH:mm:ss")
                LocalDateTime availableStartDateTime,
        @Schema(description = "availability query range end, yyyy-MM-ddTHH:mm:ss") LocalDateTime availableEndDateTime,
        List<@NotBlank String> dateList,
        Boolean isAnyDate,
        List<TimeRangeDto> timeRangeList,
        Boolean isAnyTime,
        List<Integer> staffIdList,
        @Schema(description = "search list that not in service area") Boolean isOutOfArea,
        List<Integer> serviceAreaIdList,
        @Schema(description = "new, recurring, prospect") List<String> clientType,
        @AssertTrue Boolean isAvailable) {}
