package com.moego.server.grooming.web.dto.ob;

import com.moego.idl.models.customer.v1.PetType;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.PaymentSettingForClientDTO;
import com.moego.server.payment.dto.SmartTipConfigForClientDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class InfoDto {

    GetSquareTokenResponse squareInfo;

    BusinessInfoDto businessInfo;

    @Schema(description = "payment setting")
    PaymentSettingForClientDTO paymentSetting;

    @Schema(description = "business tip config")
    SmartTipConfigForClientDTO tipConfig;

    @Schema(description = "Business twilio number")
    BusinessTwilioNumberDTO twilioInfo;

    // Available pet types
    List<PetType> availablePetTypes;
}
