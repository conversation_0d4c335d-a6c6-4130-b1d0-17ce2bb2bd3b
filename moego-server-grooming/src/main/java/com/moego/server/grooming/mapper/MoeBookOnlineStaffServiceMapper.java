package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineStaffServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineStaffService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineStaffService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    MoeBookOnlineStaffService selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineStaffService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_staff_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineStaffService record);

    /**
     * get all relations in current business or target service
     * @param businessId
     * @param serviceId   this can be null
     * @return
     */
    List<MoeBookOnlineStaffService> selectByBusinessId(
            @Param("businessId") Integer businessId, @Param("serviceId") Integer serviceId);

    List<MoeBookOnlineStaffService> selectByCompanyId(
            @Param("companyId") Long companyId, @Param("serviceId") Integer serviceId);

    /**
     * delete all serviceId in relations
     * @param serviceId
     * @return
     */
    int deleteByServiceId(@Param("businessId") Integer businessId, @Param("serviceId") Integer serviceId);

    /**
     * 批量插入
     * @param records
     * @return
     */
    int batchInsertSelective(@Param("records") List<MoeBookOnlineStaffService> records);

    List<MoeBookOnlineStaffService> getServiceListByStaffIdList(
            @Param("businessId") Integer businessId, @Param("staffIdList") List<Integer> staffIdList);

    List<MoeBookOnlineStaffService> getServiceListByServiceIdList(
            @Param("businessId") Integer businessId, @Param("serviceIdList") List<Integer> serviceIdList);
}
