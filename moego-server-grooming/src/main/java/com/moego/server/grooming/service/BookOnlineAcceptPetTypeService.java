package com.moego.server.grooming.service;

import com.moego.server.grooming.mapper.MoeBookOnlineAcceptPetTypeMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAcceptPetType;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAcceptPetTypeExample;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class BookOnlineAcceptPetTypeService {

    private final MoeBookOnlineAcceptPetTypeMapper moeBookOnlineAcceptPetTypeMapper;

    public List<MoeBookOnlineAcceptPetType> getAcceptPetTypeList(long companyId, long businessId) {

        var example = new MoeBookOnlineAcceptPetTypeExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andBusinessIdEqualTo(businessId);

        return moeBookOnlineAcceptPetTypeMapper.selectByExample(example);
    }

    public void updateAcceptPetTypeList(long companyId, long businessId, Map<Integer, Boolean> petTypeAcceptMap) {

        var originalPetTypeAcceptList = getAcceptPetTypeList(companyId, businessId);
        // key is `petTypeId`, value is `accepted` (0 or 1)
        var originalPetTypeAcceptMap = originalPetTypeAcceptList == null
                ? new HashMap<Integer, Integer>()
                : originalPetTypeAcceptList.stream()
                        .collect(Collectors.toMap(
                                MoeBookOnlineAcceptPetType::getPetTypeId, MoeBookOnlineAcceptPetType::getAccepted));

        for (var entry : petTypeAcceptMap.entrySet()) {
            var petTypeId = entry.getKey();
            var accepted = entry.getValue() ? 1 : 0;

            // update if exists, insert if not exists
            if (originalPetTypeAcceptMap.containsKey(petTypeId)) {
                var originalAccepted = originalPetTypeAcceptMap.get(petTypeId);
                if (originalAccepted == accepted) {
                    continue;
                }

                var record = new MoeBookOnlineAcceptPetType();
                record.setAccepted(accepted);
                record.setUpdatedAt(LocalDateTime.now());

                var example = new MoeBookOnlineAcceptPetTypeExample();
                example.createCriteria()
                        .andCompanyIdEqualTo(companyId)
                        .andBusinessIdEqualTo(businessId)
                        .andPetTypeIdEqualTo(petTypeId);

                moeBookOnlineAcceptPetTypeMapper.updateByExampleSelective(record, example);

            } else {
                // 所有 pet type 应当都初始化过了，理论上不会走到这里，如果走到这里需要排查一下原因
                log.warn(
                        "accept pet type not initialized. pet type id {}, company id {}, business id {}",
                        petTypeId,
                        companyId,
                        businessId);
                var now = LocalDateTime.now();
                var record = new MoeBookOnlineAcceptPetType();
                record.setCompanyId(companyId);
                record.setBusinessId(businessId);
                record.setPetTypeId(petTypeId);
                record.setAccepted(accepted);
                record.setCreatedAt(now);
                record.setUpdatedAt(now);
                moeBookOnlineAcceptPetTypeMapper.insertSelective(record);
            }
        }
    }

    /**
     * 支持重复调用
     * @param companyId
     * @param businessId
     */
    public void initAcceptPetTypeList(long companyId, long businessId) {
        var example = new MoeBookOnlineAcceptPetTypeExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andBusinessIdEqualTo(businessId);
        var moeBookOnlineAcceptPetTypes = moeBookOnlineAcceptPetTypeMapper.selectByExample(example);
        if (!moeBookOnlineAcceptPetTypes.isEmpty()) {
            return;
        }
        // ob accept pet type 初始化，默认所有 pet 都接受
        var now = LocalDateTime.now();
        // FIXME: petTypeId 应当取自枚举类型
        for (int petTypeId = 1; petTypeId <= 11; petTypeId++) {
            var record = new MoeBookOnlineAcceptPetType();
            record.setCompanyId(companyId);
            record.setBusinessId(businessId);
            record.setPetTypeId(petTypeId);
            record.setAccepted(1);
            record.setCreatedAt(now);
            record.setUpdatedAt(now);
            moeBookOnlineAcceptPetTypeMapper.insertSelective(record);
        }
    }
}
