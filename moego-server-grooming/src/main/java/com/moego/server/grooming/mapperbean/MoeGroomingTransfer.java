package com.moego.server.grooming.mapperbean;

public class MoeGroomingTransfer {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.order_id
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.from_by
     *
     * @mbg.generated
     */
    private Integer fromBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.to_by
     *
     * @mbg.generated
     */
    private Integer toBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_transfer.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    private Integer petDetailId;

    public Integer getPetDetailId() {
        return petDetailId;
    }

    public void setPetDetailId(Integer petDetailId) {
        this.petDetailId = petDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.id
     *
     * @return the value of moe_grooming_transfer.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.id
     *
     * @param id the value for moe_grooming_transfer.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.order_id
     *
     * @return the value of moe_grooming_transfer.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.order_id
     *
     * @param orderId the value for moe_grooming_transfer.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.grooming_id
     *
     * @return the value of moe_grooming_transfer.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.grooming_id
     *
     * @param groomingId the value for moe_grooming_transfer.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.from_by
     *
     * @return the value of moe_grooming_transfer.from_by
     *
     * @mbg.generated
     */
    public Integer getFromBy() {
        return fromBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.from_by
     *
     * @param fromBy the value for moe_grooming_transfer.from_by
     *
     * @mbg.generated
     */
    public void setFromBy(Integer fromBy) {
        this.fromBy = fromBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.to_by
     *
     * @return the value of moe_grooming_transfer.to_by
     *
     * @mbg.generated
     */
    public Integer getToBy() {
        return toBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.to_by
     *
     * @param toBy the value for moe_grooming_transfer.to_by
     *
     * @mbg.generated
     */
    public void setToBy(Integer toBy) {
        this.toBy = toBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_transfer.create_time
     *
     * @return the value of moe_grooming_transfer.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_transfer.create_time
     *
     * @param createTime the value for moe_grooming_transfer.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
}
