package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_pet_detail
 */
public class MoeGroomingPetDetail {
    /**
     * Database Column Remarks:
     *   预约订单详情id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   预约订单主表id(mm_appointment_table)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     * Database Column Remarks:
     *   宠物id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   负责服务的员工id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   服务id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     * Database Column Remarks:
     *   1-主服务 2-额外服务
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_type
     *
     * @mbg.generated
     */
    private Integer serviceType;

    /**
     * Database Column Remarks:
     *   服务预估时间(考虑save time)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_time
     *
     * @mbg.generated
     */
    private Integer serviceTime;

    /**
     * Database Column Remarks:
     *   服务预估费用(考虑save price)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_price
     *
     * @mbg.generated
     */
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   服务开始时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.start_time
     *
     * @mbg.generated
     */
    private Long startTime;

    /**
     * Database Column Remarks:
     *   服务结束时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.end_time
     *
     * @mbg.generated
     */
    private Long endTime;

    /**
     * Database Column Remarks:
     *   状态 1-正常 2-已删除  3因为修改的删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   服务价格的生效范围类型  1-this appt 2 this and future(this appt和未来创建的预约会生效)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.scope_type_price
     *
     * @mbg.generated
     */
    private Integer scopeTypePrice;

    /**
     * Database Column Remarks:
     *   服务时间的生效范围类型  1-this appt 2 this and future(this appt和未来创建的预约会生效)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.scope_type_time
     *
     * @mbg.generated
     */
    private Integer scopeTypeTime;

    /**
     * Database Column Remarks:
     *   标星的staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.star_staff_id
     *
     * @mbg.generated
     */
    private Integer starStaffId;

    /**
     * Database Column Remarks:
     *   使用package的packageServiceid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.package_service_id
     *
     * @mbg.generated
     */
    private Integer packageServiceId;

    /**
     * Database Column Remarks:
     *   enable operation
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.enable_operation
     *
     * @mbg.generated
     */
    private Boolean enableOperation;

    /**
     * Database Column Remarks:
     *   work mode, 0-parallel, 1-sequence
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.work_mode
     *
     * @mbg.generated
     */
    private Integer workMode;

    /**
     * Database Column Remarks:
     *   color code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_color_code
     *
     * @mbg.generated
     */
    private String serviceColorCode;

    /**
     * Database Column Remarks:
     *   开始日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.start_date
     *
     * @mbg.generated
     */
    private String startDate;

    /**
     * Database Column Remarks:
     *   结束日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.end_date
     *
     * @mbg.generated
     */
    private String endDate;

    /**
     * Database Column Remarks:
     *   service item, 1-grooming, 2-boarding, 3-daycare
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.service_item_type
     *
     * @mbg.generated
     */
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   lodging id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.lodging_id
     *
     * @mbg.generated
     */
    private Long lodgingId;

    /**
     * Database Column Remarks:
     *   1-per session, 2-per night, 3-per hour, 4-per day
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.price_unit
     *
     * @mbg.generated
     */
    private Integer priceUnit;

    /**
     * Database Column Remarks:
     *   add-on specific dates, yyyy-MM-dd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.specific_dates
     *
     * @mbg.generated
     */
    private String specificDates;

    /**
     * Database Column Remarks:
     *   add-on associated service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.associated_service_id
     *
     * @mbg.generated
     */
    private Long associatedServiceId;

    /**
     * Database Column Remarks:
     *   0 - no override, 1 - override by location (business), 2 - override by pet (client), 3 - override by staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.price_override_type
     *
     * @mbg.generated
     */
    private ServiceOverrideType priceOverrideType;

    /**
     * Database Column Remarks:
     *   0 - no override, 1 - override by location (business), 2 - override by pet (client), 3 - override by staff
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.duration_override_type
     *
     * @mbg.generated
     */
    private ServiceOverrideType durationOverrideType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.updated_at
     *
     * @mbg.generated
     */
    private Date updatedAt;

    /**
     * Database Column Remarks:
     *   Number of times per day this service is performed
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.quantity_per_day
     *
     * @mbg.generated
     */
    private Integer quantityPerDay;

    /**
     * Database Column Remarks:
     *   date type, 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.date_type
     *
     * @mbg.generated
     */
    private Integer dateType;

    /**
     * Database Column Remarks:
     *   The total price of this service, affected by the inclusion of the pricing rule
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.total_price
     *
     * @mbg.generated
     */
    private BigDecimal totalPrice;

    /**
     * Database Column Remarks:
     *   The quantity of this selected service
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.quantity
     *
     * @mbg.generated
     */
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   Order line item id, 0 if not created from order
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_pet_detail.order_line_item_id
     *
     * @mbg.generated
     */
    private Long orderLineItemId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.id
     *
     * @return the value of moe_grooming_pet_detail.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.id
     *
     * @param id the value for moe_grooming_pet_detail.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.grooming_id
     *
     * @return the value of moe_grooming_pet_detail.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.grooming_id
     *
     * @param groomingId the value for moe_grooming_pet_detail.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.pet_id
     *
     * @return the value of moe_grooming_pet_detail.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.pet_id
     *
     * @param petId the value for moe_grooming_pet_detail.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.staff_id
     *
     * @return the value of moe_grooming_pet_detail.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.staff_id
     *
     * @param staffId the value for moe_grooming_pet_detail.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_id
     *
     * @return the value of moe_grooming_pet_detail.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_id
     *
     * @param serviceId the value for moe_grooming_pet_detail.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_type
     *
     * @return the value of moe_grooming_pet_detail.service_type
     *
     * @mbg.generated
     */
    public Integer getServiceType() {
        return serviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_type
     *
     * @param serviceType the value for moe_grooming_pet_detail.service_type
     *
     * @mbg.generated
     */
    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_time
     *
     * @return the value of moe_grooming_pet_detail.service_time
     *
     * @mbg.generated
     */
    public Integer getServiceTime() {
        return serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_time
     *
     * @param serviceTime the value for moe_grooming_pet_detail.service_time
     *
     * @mbg.generated
     */
    public void setServiceTime(Integer serviceTime) {
        this.serviceTime = serviceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_price
     *
     * @return the value of moe_grooming_pet_detail.service_price
     *
     * @mbg.generated
     */
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_price
     *
     * @param servicePrice the value for moe_grooming_pet_detail.service_price
     *
     * @mbg.generated
     */
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.start_time
     *
     * @return the value of moe_grooming_pet_detail.start_time
     *
     * @mbg.generated
     */
    public Long getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.start_time
     *
     * @param startTime the value for moe_grooming_pet_detail.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.end_time
     *
     * @return the value of moe_grooming_pet_detail.end_time
     *
     * @mbg.generated
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.end_time
     *
     * @param endTime the value for moe_grooming_pet_detail.end_time
     *
     * @mbg.generated
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.status
     *
     * @return the value of moe_grooming_pet_detail.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.status
     *
     * @param status the value for moe_grooming_pet_detail.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.update_time
     *
     * @return the value of moe_grooming_pet_detail.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.update_time
     *
     * @param updateTime the value for moe_grooming_pet_detail.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.scope_type_price
     *
     * @return the value of moe_grooming_pet_detail.scope_type_price
     *
     * @mbg.generated
     */
    public Integer getScopeTypePrice() {
        return scopeTypePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.scope_type_price
     *
     * @param scopeTypePrice the value for moe_grooming_pet_detail.scope_type_price
     *
     * @mbg.generated
     */
    public void setScopeTypePrice(Integer scopeTypePrice) {
        this.scopeTypePrice = scopeTypePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.scope_type_time
     *
     * @return the value of moe_grooming_pet_detail.scope_type_time
     *
     * @mbg.generated
     */
    public Integer getScopeTypeTime() {
        return scopeTypeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.scope_type_time
     *
     * @param scopeTypeTime the value for moe_grooming_pet_detail.scope_type_time
     *
     * @mbg.generated
     */
    public void setScopeTypeTime(Integer scopeTypeTime) {
        this.scopeTypeTime = scopeTypeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.star_staff_id
     *
     * @return the value of moe_grooming_pet_detail.star_staff_id
     *
     * @mbg.generated
     */
    public Integer getStarStaffId() {
        return starStaffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.star_staff_id
     *
     * @param starStaffId the value for moe_grooming_pet_detail.star_staff_id
     *
     * @mbg.generated
     */
    public void setStarStaffId(Integer starStaffId) {
        this.starStaffId = starStaffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.package_service_id
     *
     * @return the value of moe_grooming_pet_detail.package_service_id
     *
     * @mbg.generated
     */
    public Integer getPackageServiceId() {
        return packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.package_service_id
     *
     * @param packageServiceId the value for moe_grooming_pet_detail.package_service_id
     *
     * @mbg.generated
     */
    public void setPackageServiceId(Integer packageServiceId) {
        this.packageServiceId = packageServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.enable_operation
     *
     * @return the value of moe_grooming_pet_detail.enable_operation
     *
     * @mbg.generated
     */
    public Boolean getEnableOperation() {
        return enableOperation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.enable_operation
     *
     * @param enableOperation the value for moe_grooming_pet_detail.enable_operation
     *
     * @mbg.generated
     */
    public void setEnableOperation(Boolean enableOperation) {
        this.enableOperation = enableOperation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.work_mode
     *
     * @return the value of moe_grooming_pet_detail.work_mode
     *
     * @mbg.generated
     */
    public Integer getWorkMode() {
        return workMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.work_mode
     *
     * @param workMode the value for moe_grooming_pet_detail.work_mode
     *
     * @mbg.generated
     */
    public void setWorkMode(Integer workMode) {
        this.workMode = workMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_color_code
     *
     * @return the value of moe_grooming_pet_detail.service_color_code
     *
     * @mbg.generated
     */
    public String getServiceColorCode() {
        return serviceColorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_color_code
     *
     * @param serviceColorCode the value for moe_grooming_pet_detail.service_color_code
     *
     * @mbg.generated
     */
    public void setServiceColorCode(String serviceColorCode) {
        this.serviceColorCode = serviceColorCode == null ? null : serviceColorCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.start_date
     *
     * @return the value of moe_grooming_pet_detail.start_date
     *
     * @mbg.generated
     */
    public String getStartDate() {
        return startDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.start_date
     *
     * @param startDate the value for moe_grooming_pet_detail.start_date
     *
     * @mbg.generated
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate == null ? null : startDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.end_date
     *
     * @return the value of moe_grooming_pet_detail.end_date
     *
     * @mbg.generated
     */
    public String getEndDate() {
        return endDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.end_date
     *
     * @param endDate the value for moe_grooming_pet_detail.end_date
     *
     * @mbg.generated
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate == null ? null : endDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.service_item_type
     *
     * @return the value of moe_grooming_pet_detail.service_item_type
     *
     * @mbg.generated
     */
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.service_item_type
     *
     * @param serviceItemType the value for moe_grooming_pet_detail.service_item_type
     *
     * @mbg.generated
     */
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.lodging_id
     *
     * @return the value of moe_grooming_pet_detail.lodging_id
     *
     * @mbg.generated
     */
    public Long getLodgingId() {
        return lodgingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.lodging_id
     *
     * @param lodgingId the value for moe_grooming_pet_detail.lodging_id
     *
     * @mbg.generated
     */
    public void setLodgingId(Long lodgingId) {
        this.lodgingId = lodgingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.price_unit
     *
     * @return the value of moe_grooming_pet_detail.price_unit
     *
     * @mbg.generated
     */
    public Integer getPriceUnit() {
        return priceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.price_unit
     *
     * @param priceUnit the value for moe_grooming_pet_detail.price_unit
     *
     * @mbg.generated
     */
    public void setPriceUnit(Integer priceUnit) {
        this.priceUnit = priceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.specific_dates
     *
     * @return the value of moe_grooming_pet_detail.specific_dates
     *
     * @mbg.generated
     */
    public String getSpecificDates() {
        return specificDates;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.specific_dates
     *
     * @param specificDates the value for moe_grooming_pet_detail.specific_dates
     *
     * @mbg.generated
     */
    public void setSpecificDates(String specificDates) {
        this.specificDates = specificDates == null ? null : specificDates.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.associated_service_id
     *
     * @return the value of moe_grooming_pet_detail.associated_service_id
     *
     * @mbg.generated
     */
    public Long getAssociatedServiceId() {
        return associatedServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.associated_service_id
     *
     * @param associatedServiceId the value for moe_grooming_pet_detail.associated_service_id
     *
     * @mbg.generated
     */
    public void setAssociatedServiceId(Long associatedServiceId) {
        this.associatedServiceId = associatedServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.price_override_type
     *
     * @return the value of moe_grooming_pet_detail.price_override_type
     *
     * @mbg.generated
     */
    public ServiceOverrideType getPriceOverrideType() {
        return priceOverrideType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.price_override_type
     *
     * @param priceOverrideType the value for moe_grooming_pet_detail.price_override_type
     *
     * @mbg.generated
     */
    public void setPriceOverrideType(ServiceOverrideType priceOverrideType) {
        this.priceOverrideType = priceOverrideType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.duration_override_type
     *
     * @return the value of moe_grooming_pet_detail.duration_override_type
     *
     * @mbg.generated
     */
    public ServiceOverrideType getDurationOverrideType() {
        return durationOverrideType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.duration_override_type
     *
     * @param durationOverrideType the value for moe_grooming_pet_detail.duration_override_type
     *
     * @mbg.generated
     */
    public void setDurationOverrideType(ServiceOverrideType durationOverrideType) {
        this.durationOverrideType = durationOverrideType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.created_at
     *
     * @return the value of moe_grooming_pet_detail.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.created_at
     *
     * @param createdAt the value for moe_grooming_pet_detail.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.updated_at
     *
     * @return the value of moe_grooming_pet_detail.updated_at
     *
     * @mbg.generated
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.updated_at
     *
     * @param updatedAt the value for moe_grooming_pet_detail.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.quantity_per_day
     *
     * @return the value of moe_grooming_pet_detail.quantity_per_day
     *
     * @mbg.generated
     */
    public Integer getQuantityPerDay() {
        return quantityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.quantity_per_day
     *
     * @param quantityPerDay the value for moe_grooming_pet_detail.quantity_per_day
     *
     * @mbg.generated
     */
    public void setQuantityPerDay(Integer quantityPerDay) {
        this.quantityPerDay = quantityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.date_type
     *
     * @return the value of moe_grooming_pet_detail.date_type
     *
     * @mbg.generated
     */
    public Integer getDateType() {
        return dateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.date_type
     *
     * @param dateType the value for moe_grooming_pet_detail.date_type
     *
     * @mbg.generated
     */
    public void setDateType(Integer dateType) {
        this.dateType = dateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.total_price
     *
     * @return the value of moe_grooming_pet_detail.total_price
     *
     * @mbg.generated
     */
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.total_price
     *
     * @param totalPrice the value for moe_grooming_pet_detail.total_price
     *
     * @mbg.generated
     */
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.quantity
     *
     * @return the value of moe_grooming_pet_detail.quantity
     *
     * @mbg.generated
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.quantity
     *
     * @param quantity the value for moe_grooming_pet_detail.quantity
     *
     * @mbg.generated
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_pet_detail.order_line_item_id
     *
     * @return the value of moe_grooming_pet_detail.order_line_item_id
     *
     * @mbg.generated
     */
    public Long getOrderLineItemId() {
        return orderLineItemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_pet_detail.order_line_item_id
     *
     * @param orderLineItemId the value for moe_grooming_pet_detail.order_line_item_id
     *
     * @mbg.generated
     */
    public void setOrderLineItemId(Long orderLineItemId) {
        this.orderLineItemId = orderLineItemId;
    }
}
