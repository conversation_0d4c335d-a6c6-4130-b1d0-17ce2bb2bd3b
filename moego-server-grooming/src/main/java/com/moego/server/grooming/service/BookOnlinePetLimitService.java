package com.moego.server.grooming.service;

import com.google.gson.reflect.TypeToken;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.utils.GsonUtil;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.customer.client.IPetBreedClient;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.dto.BookOnlinePetLimitDTO;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.helper.OfferingHelper;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeBookOnlinePetLimitMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffTimeMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimit;
import com.moego.server.grooming.mapperbean.MoeBookOnlinePetLimitBreedBinding;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.dto.CapacityTimeslotDTO;
import com.moego.server.grooming.service.dto.OneDayTimeslotsDTO;
import com.moego.server.grooming.web.vo.BookOnlinePetLimitRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BookOnlinePetLimitService {

    @Autowired
    private MoeBookOnlinePetLimitMapper moeBookOnlinePetLimitMapper;

    @Autowired
    private IPetBreedClient iPetBreedClient;

    @Autowired
    private MoeBookOnlineStaffTimeMapper moeBookOnlineStaffTimeMapper;

    @Autowired
    private MoeBookOnlinePetLimitBreedBindingMapper moeBookOnlinePetLimitBreedBindingMapper;

    @Autowired
    private MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Autowired
    private BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceBlockingStub;

    @Autowired
    private IBusinessBusinessService iBusinessBusinessService;

    @Autowired
    private MigrateHelper migrateHelper;

    public static final byte LIMIT_BY_SIZE = 1;

    public static final byte LIMIT_BY_BREED = 2;

    public static final byte ALL_BREED = 1;

    public static final byte CUSTOMIZE_BREED = 2;

    @Autowired
    private StaffTimeSyncService staffTimeSyncService;

    @Autowired
    private OfferingHelper offeringHelper;

    public List<BookOnlinePetLimitDTO> getPetLimit(long companyId, Integer businessId) {
        return getPetLimit(companyId, businessId, List.of());
    }

    /**
     * for 前端查询兼容使用
     * @param companyId
     * @param businessId
     * @param limitIdList
     * @return
     */
    public List<BookOnlinePetLimitDTO> getPetLimit(long companyId, Integer businessId, List<Long> limitIdList) {
        if (CollectionUtils.isEmpty(limitIdList)) {
            var petLimitIdSet = getPetLimitIdList(businessId);
            if (CollectionUtils.isEmpty(petLimitIdSet)) {
                return List.of();
            }
            limitIdList = new ArrayList<>(petLimitIdSet);
        }

        Map<Long, PetSizeDTO> petSizeMap = new HashMap<>();
        Map<Integer, MoePetBreedDTO> petBreedMap = new HashMap<>();
        Map<Long, MoeBookOnlinePetLimitBreedBinding> breedBindingMap = new HashMap<>();

        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        // query pet size
        completableFutureList.add(CompletableFuture.runAsync(
                () -> petSizeMap.putAll(getPetSizeList(companyId, businessId)), ThreadPool.getExecutor()));
        // query pet breed
        completableFutureList.add(CompletableFuture.runAsync(
                () -> petBreedMap.putAll(iPetBreedClient.getPetBreed(businessId)), ThreadPool.getExecutor()));
        // query breed binding
        completableFutureList.add(CompletableFuture.runAsync(
                () -> breedBindingMap.putAll(
                        moeBookOnlinePetLimitBreedBindingMapper.selectByBusinessId(businessId).stream()
                                .collect(Collectors.toMap(
                                        MoeBookOnlinePetLimitBreedBinding::getId, Function.identity()))),
                ThreadPool.getExecutor()));
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();

        return moeBookOnlinePetLimitMapper.selectByIdList(businessId, limitIdList).stream()
                .map(petLimit -> getPetLimitDTO(petSizeMap, petBreedMap, breedBindingMap, petLimit))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static BookOnlinePetLimitDTO getPetLimitDTO(
            Map<Long, PetSizeDTO> petSizeMap,
            Map<Integer, MoePetBreedDTO> petBreedMap,
            Map<Long, MoeBookOnlinePetLimitBreedBinding> breedBindingMap,
            MoeBookOnlinePetLimit petLimit) {
        BookOnlinePetLimitDTO petLimitDTO = new BookOnlinePetLimitDTO();
        BeanUtils.copyProperties(petLimit, petLimitDTO);

        Long findId = petLimit.getFindId();
        if (LIMIT_BY_SIZE == petLimit.getType()) {
            PetSizeDTO petSizeDTO = petSizeMap.get(findId);
            if (Objects.isNull(petSizeDTO)) {
                return null;
            }
            petLimitDTO.setPetSizeDTO(petSizeDTO);
        } else if (LIMIT_BY_BREED == petLimit.getType()) {
            MoeBookOnlinePetLimitBreedBinding breedBinding = breedBindingMap.get(findId);
            if (Objects.isNull(breedBinding)) {
                return null;
            }

            petLimitDTO.setPetTypeId(breedBinding.getPetTypeId());
            petLimitDTO.setAllBreed(ALL_BREED == breedBinding.getAllBreed());
            if (ALL_BREED == breedBinding.getAllBreed()) {
                petLimitDTO.setPetBreedDTOList(new ArrayList<>());
                return petLimitDTO;
            }

            List<Integer> breedIdList =
                    GsonUtil.fromJson(breedBinding.getBreedIdList(), new TypeToken<List<Integer>>() {});
            if (CollectionUtils.isEmpty(breedIdList)) {
                return null;
            }

            List<MoePetBreedDTO> petBreedDTOList = breedIdList.stream()
                    .map(petBreedMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            petLimitDTO.setPetBreedDTOList(petBreedDTOList);
        }
        return petLimitDTO;
    }

    private Set<Long> getPetLimitIdList(Integer businessId) {
        Set<Long> limitIdList = new HashSet<>();

        MoeBusinessBookOnline businessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (Objects.isNull(businessBookOnline)) {
            return limitIdList;
        }
        Byte availableTimeType = businessBookOnline.getAvailableTimeType();
        if (Objects.equals(availableTimeType, OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE)) {
            return limitIdList;
        }

        moeBookOnlineStaffTimeMapper
                .selectByBusinessId(businessId) // 给前端查询用，可以保留
                .forEach(
                        staffTime -> { // getPetLimitIdList
                            // extract working hour setting
                            if (Objects.equals(availableTimeType, OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR)
                                    && StringUtils.hasText(staffTime.getStaffTimes())) {
                                GsonUtil.fromJsonToMap(staffTime.getStaffTimes())
                                        .values()
                                        .forEach(workingHour -> {
                                            List<Long> limitIds = GsonUtil.fromJson(
                                                            workingHour.toString(), StaffTime.class)
                                                    .getLimitIds();
                                            if (!CollectionUtils.isEmpty(limitIds)) {
                                                limitIdList.addAll(limitIds);
                                            }
                                        });
                            }
                            // extract time slot setting
                            if (Objects.equals(availableTimeType, OnlineBookingConst.AVAILABLE_TIME_TYPE_BY_SLOT)
                                    && StringUtils.hasText(staffTime.getStaffSlots())) {
                                GsonUtil.fromJsonToMap(staffTime.getStaffSlots())
                                        .values()
                                        .forEach(timeSlot -> {
                                            limitIdList.addAll(
                                                    GsonUtil.fromJson(timeSlot.toString(), OneDayTimeslotsDTO.class)
                                                            .getTimeSlot()
                                                            .stream()
                                                            .map(CapacityTimeslotDTO::getLimitIds)
                                                            .filter(Objects::nonNull)
                                                            .flatMap(Collection::stream)
                                                            .collect(Collectors.toList()));
                                        });
                            }
                        });
        return limitIdList;
    }

    public Long savePetLimit(Integer businessId, Long companyId, BookOnlinePetLimitRequest request) {
        MoeBookOnlinePetLimit petLimit = new MoeBookOnlinePetLimit();
        petLimit.setBusinessId(businessId);
        petLimit.setCompanyId(companyId);
        petLimit.setType(request.getType());
        petLimit.setMaxNumber(request.getMaxNumber());

        if (LIMIT_BY_SIZE == request.getType()) {
            petLimit.setFindId(request.getFindId());
        } else {
            MoeBookOnlinePetLimitBreedBinding breedBinding = new MoeBookOnlinePetLimitBreedBinding();
            breedBinding.setBusinessId(businessId);
            breedBinding.setCompanyId(companyId);
            breedBinding.setPetTypeId(request.getPetTypeId());
            breedBinding.setAllBreed(Boolean.TRUE.equals(request.getAllBreed()) ? ALL_BREED : CUSTOMIZE_BREED);
            if (Boolean.TRUE.equals(request.getAllBreed())) {
                breedBinding.setBreedIdList(GsonUtil.toJson(new ArrayList<>()));
            } else {
                breedBinding.setBreedIdList(GsonUtil.toJson(request.getBreedIdList()));
            }
            moeBookOnlinePetLimitBreedBindingMapper.insertSelective(breedBinding);
            petLimit.setFindId(breedBinding.getId());
        }

        moeBookOnlinePetLimitMapper.insertSelective(petLimit);
        return petLimit.getId();
    }

    public Map<Long, BookOnlinePetLimitDTO> getPetLimitMap(Integer businessId) {
        var companyId =
                iBusinessBusinessService.getCompanyIdByBusinessId(businessId).companyId();
        return getPetLimit(companyId, businessId).stream()
                .collect(Collectors.toMap(BookOnlinePetLimitDTO::getId, Function.identity()));
    }

    public List<Long> getInUsedPetSizeIdList(Integer businessId) {
        var allStaffTimeMap = staffTimeSyncService.queryStaffTime(businessId, null);
        List<Long> allPetSizeId = new ArrayList<>();
        allStaffTimeMap.forEach((staffId, staffSlotTime) -> {
            staffSlotTime.getStaffSlots().values().stream()
                    .flatMap(daySlot ->
                            Stream.concat(daySlot.getTimeSlot().stream(), Stream.of(daySlot.getDailyTimeSlot())))
                    .map(BookOnlineStaffTimeDTO.StaffSlot.CapacityTimeslot::getLimitGroups)
                    .flatMap(Collection::stream)
                    .filter(Predicate.not(LimitGroupDTO::isEmpty))
                    .forEach(limitGroupDto ->
                            Optional.ofNullable(limitGroupDto.getPetSizeLimitList())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .filter(petSizeLimitDto -> !Boolean.TRUE.equals(petSizeLimitDto.getIsAllPetSize()))
                                    .forEach(petSizeLimitDto -> {
                                        Optional.ofNullable(petSizeLimitDto.getPetSizeIds())
                                                .ifPresent(allPetSizeId::addAll);
                                    }));
            staffSlotTime.getStaffTimes().values().stream()
                    .map(StaffTime::getLimitGroups)
                    .flatMap(Collection::stream)
                    .filter(Predicate.not(LimitGroupDTO::isEmpty))
                    .forEach(limitGroupDto ->
                            Optional.ofNullable(limitGroupDto.getPetSizeLimitList())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .filter(petSizeLimitDto -> !Boolean.TRUE.equals(petSizeLimitDto.getIsAllPetSize()))
                                    .forEach(petSizeLimitDto -> {
                                        Optional.ofNullable(petSizeLimitDto.getPetSizeIds())
                                                .ifPresent(allPetSizeId::addAll);
                                    }));
        });
        return allPetSizeId;
    }

    public Map<Long, PetSizeDTO> getPetSizeList(long companyId, Integer businessId) {
        var request = ListPetSizeRequest.newBuilder().setCompanyId(companyId);

        var response = businessPetSizeServiceBlockingStub.listPetSize(request.build());
        return response.getSizesList().stream()
                .collect(Collectors.toMap(BusinessPetSizeModel::getId, size -> PetSizeDTO.builder()
                        .id(size.getId())
                        .name(size.getName())
                        .weightLow(size.getWeightLow())
                        .weightHigh(size.getWeightHigh())
                        .build()));
    }

    public Map<Long, ServiceModel> getServiceMap(Long companyId, Integer businessId) {
        return offeringHelper.fetchAllGroomingService(companyId, businessId).stream()
                .collect(Collectors.toMap(ServiceModel::getServiceId, Function.identity()));
    }
}
