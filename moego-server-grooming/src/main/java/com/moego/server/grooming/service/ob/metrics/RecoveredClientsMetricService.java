package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class RecoveredClientsMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Override
    public Object sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return abandonRecordMapper.countRecoveredClients(
                timeRangeDTO.businessId(), timeRangeDTO.startTime(), timeRangeDTO.endTime());
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        // recovered clients / recoverable clients
        int recoveredClients = abandonRecordMapper.countRecoveredClients(
                timeRangeDTO.businessId(), timeRangeDTO.startTime(), timeRangeDTO.endTime());
        int recoverableClients = abandonRecordMapper.countRecoverableClients(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        if (recoverableClients == 0) {
            return "";
        }
        return BigDecimal.valueOf(recoveredClients)
                .divide(BigDecimal.valueOf(recoverableClients), 2, RoundingMode.HALF_UP);
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.recovered_clients;
    }
}
