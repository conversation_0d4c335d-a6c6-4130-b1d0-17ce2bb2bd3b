package com.moego.server.grooming.web.dto;

import lombok.Data;

@Data
public class SummaryDto {

    private StaffSummary staffSummary;
    private BusinessSummary businessSummary;

    @Data
    public static class StaffSummary {

        private Service service;
        private LongestWorkingDay longestWorkingDay;
        private TakeCaredPet takeCaredPet;
        private MostPetBreed mostPetBreed;

        @Data
        public static class Service {

            private int totalServiceTimeInMin;
        }

        @Data
        public static class TakeCaredPet {

            private int count;
        }

        @Data
        public static class LongestWorkingDay {

            private String date;
            private int minutes;
        }

        @Data
        public static class MostPetBreed {

            private String breed;
            private int count;
        }
    }

    @Data
    public static class BusinessSummary {

        private OnlineBookingSummary onlineBookingSummary;
        private MoegoPaySummary moeGoPaySummary;
        private StaffSummary staffSummary;

        @Data
        public static class OnlineBookingSummary {

            private boolean enabled;
            private Integer count;
        }

        @Data
        public static class MoegoPaySummary {

            private boolean enabled;
            private Integer count;
            private Double totalAmount;
        }
    }
}
