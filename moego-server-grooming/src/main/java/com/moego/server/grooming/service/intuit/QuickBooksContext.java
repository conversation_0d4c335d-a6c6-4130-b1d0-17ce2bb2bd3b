package com.moego.server.grooming.service.intuit;

/**
 * <AUTHOR>
 * @since 2024/8/26
 */
public class QuickBooksContext {
    private static final ThreadLocal<Integer> businessId = new ThreadLocal<>();

    public static void setBusinessId(Integer id) {
        businessId.set(id);
    }

    public static Integer getBusinessId() {
        return businessId.get();
    }

    public static void clear() {
        businessId.remove();
    }
}
