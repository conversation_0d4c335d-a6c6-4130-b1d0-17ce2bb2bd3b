package com.moego.server.grooming.service.report.migrate.validation.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * 全量校验摘要
 */
@Data
public class ValidationSummary {
    
    /**
     * 校验是否成功
     */
    private boolean success;
    
    /**
     * 业务ID
     */
    private Integer businessId;
    
    /**
     * 校验开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 校验结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 预期总数
     */
    private long expectedTotalCount;
    
    /**
     * 实际总数
     */
    private long actualTotalCount;
    
    /**
     * 错误列表
     */
    private List<ValidationError> errors;
    
    /**
     * 抽样校验结果
     */
    private ValidationResult samplingValidation;
    
    /**
     * 校验耗时（毫秒）
     */
    private Long durationMs;
    
    /**
     * 额外的校验统计信息
     */
    private ValidationStatistics statistics;
    
    public ValidationSummary() {
        this.errors = new ArrayList<>();
        this.startTime = LocalDateTime.now();
        this.statistics = new ValidationStatistics();
    }
    
    /**
     * 添加错误
     */
    public void addError(ValidationError error) {
        this.errors.add(error);
    }
    
    /**
     * 完成校验，设置结束时间和耗时
     */
    public void complete() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null && this.endTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors.size();
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 获取数据完整性
     */
    public double getDataIntegrityRate() {
        if (expectedTotalCount == 0) {
            return 0.0;
        }
        return (double) actualTotalCount / expectedTotalCount * 100;
    }
    
    /**
     * 获取校验摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("全量校验结果: ").append(success ? "成功" : "失败");
        sb.append(", businessId: ").append(businessId);
        sb.append(", 预期总数: ").append(expectedTotalCount);
        sb.append(", 实际总数: ").append(actualTotalCount);
        sb.append(", 完整性: ").append(String.format("%.2f%%", getDataIntegrityRate()));
        sb.append(", 错误数: ").append(getErrorCount());
        
        if (samplingValidation != null) {
            sb.append(", 抽样校验: ").append(samplingValidation.getSummary());
        }
        
        if (durationMs != null) {
            sb.append(", 耗时: ").append(durationMs).append("ms");
        }
        
        return sb.toString();
    }
    
    /**
     * 校验统计信息内部类
     */
    @Data
    public static class ValidationStatistics {
        /**
         * 数据完整性错误数
         */
        private int dataIntegrityErrors = 0;
        
        /**
         * 数据一致性错误数
         */
        private int dataConsistencyErrors = 0;
        
        /**
         * 业务逻辑错误数
         */
        private int businessLogicErrors = 0;
        
        /**
         * 数据缺失错误数
         */
        private int dataMissingErrors = 0;
        
        /**
         * 系统错误数
         */
        private int systemErrors = 0;
        
        /**
         * 更新错误统计
         */
        public void updateStatistics(List<ValidationError> errors) {
            for (ValidationError error : errors) {
                switch (error.getErrorType()) {
                    case DATA_INTEGRITY:
                        dataIntegrityErrors++;
                        break;
                    case DATA_CONSISTENCY:
                        dataConsistencyErrors++;
                        break;
                    case BUSINESS_LOGIC:
                        businessLogicErrors++;
                        break;
                    case DATA_MISSING:
                        dataMissingErrors++;
                        break;
                    case SYSTEM_ERROR:
                        systemErrors++;
                        break;
                }
            }
        }
        
        /**
         * 获取总错误数
         */
        public int getTotalErrors() {
            return dataIntegrityErrors + dataConsistencyErrors + businessLogicErrors + 
                   dataMissingErrors + systemErrors;
        }
    }
}
