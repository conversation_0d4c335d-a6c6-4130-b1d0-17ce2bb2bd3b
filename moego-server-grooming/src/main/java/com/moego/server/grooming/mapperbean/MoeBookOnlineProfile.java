package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.web.vo.client.AddressVO;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_profile
 */
public class MoeBookOnlineProfile {
    /**
     * Database Column Remarks:
     *   商家ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   商家店名
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.business_name
     *
     * @mbg.generated
     */
    private String businessName;

    /**
     * Database Column Remarks:
     *   商家电话
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     * Database Column Remarks:
     *   商家网站
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.website
     *
     * @mbg.generated
     */
    private String website;

    /**
     * Database Column Remarks:
     *   详细地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.address
     *
     * @mbg.generated
     */
    private String address;

    /**
     * Database Column Remarks:
     *   商家email
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.business_email
     *
     * @mbg.generated
     */
    private String businessEmail;

    /**
     * Database Column Remarks:
     *   image path(url)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.avatar_path
     *
     * @mbg.generated
     */
    private String avatarPath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.facebook
     *
     * @mbg.generated
     */
    private String facebook;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.instagram
     *
     * @mbg.generated
     */
    private String instagram;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.google
     *
     * @mbg.generated
     */
    private String google;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.yelp
     *
     * @mbg.generated
     */
    private String yelp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.other
     *
     * @mbg.generated
     */
    private String other;

    /**
     * Database Column Remarks:
     *   book online 语言
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.language
     *
     * @mbg.generated
     */
    private String language;

    /**
     * Database Column Remarks:
     *   button颜色
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.button_color
     *
     * @mbg.generated
     */
    private String buttonColor;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.tiktok
     *
     * @mbg.generated
     */
    private String tiktok;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_profile.business_hours_json
     *
     * @mbg.generated
     */
    private String businessHoursJson;

    /**
     * <a href="https://moego.atlassian.net/browse/ERP-4757">remove duplicate city name</a>
     * 为了兼容该需求，增加该字段。如果抽离 dto 改动地方较多，后续旧 ob 下线该实体会废弃
     */
    private AddressVO addressDetails;

    public AddressVO getAddressDetails() {
        return addressDetails;
    }

    public void setAddressDetails(AddressVO addressDetails) {
        this.addressDetails = addressDetails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.id
     *
     * @return the value of moe_book_online_profile.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.id
     *
     * @param id the value for moe_book_online_profile.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.business_id
     *
     * @return the value of moe_book_online_profile.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.business_id
     *
     * @param businessId the value for moe_book_online_profile.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.business_name
     *
     * @return the value of moe_book_online_profile.business_name
     *
     * @mbg.generated
     */
    public String getBusinessName() {
        return businessName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.business_name
     *
     * @param businessName the value for moe_book_online_profile.business_name
     *
     * @mbg.generated
     */
    public void setBusinessName(String businessName) {
        this.businessName = businessName == null ? null : businessName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.phone_number
     *
     * @return the value of moe_book_online_profile.phone_number
     *
     * @mbg.generated
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.phone_number
     *
     * @param phoneNumber the value for moe_book_online_profile.phone_number
     *
     * @mbg.generated
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.website
     *
     * @return the value of moe_book_online_profile.website
     *
     * @mbg.generated
     */
    public String getWebsite() {
        return website;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.website
     *
     * @param website the value for moe_book_online_profile.website
     *
     * @mbg.generated
     */
    public void setWebsite(String website) {
        this.website = website == null ? null : website.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.address
     *
     * @return the value of moe_book_online_profile.address
     *
     * @mbg.generated
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.address
     *
     * @param address the value for moe_book_online_profile.address
     *
     * @mbg.generated
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.business_email
     *
     * @return the value of moe_book_online_profile.business_email
     *
     * @mbg.generated
     */
    public String getBusinessEmail() {
        return businessEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.business_email
     *
     * @param businessEmail the value for moe_book_online_profile.business_email
     *
     * @mbg.generated
     */
    public void setBusinessEmail(String businessEmail) {
        this.businessEmail = businessEmail == null ? null : businessEmail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.avatar_path
     *
     * @return the value of moe_book_online_profile.avatar_path
     *
     * @mbg.generated
     */
    public String getAvatarPath() {
        return avatarPath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.avatar_path
     *
     * @param avatarPath the value for moe_book_online_profile.avatar_path
     *
     * @mbg.generated
     */
    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath == null ? null : avatarPath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.facebook
     *
     * @return the value of moe_book_online_profile.facebook
     *
     * @mbg.generated
     */
    public String getFacebook() {
        return facebook;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.facebook
     *
     * @param facebook the value for moe_book_online_profile.facebook
     *
     * @mbg.generated
     */
    public void setFacebook(String facebook) {
        this.facebook = facebook == null ? null : facebook.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.instagram
     *
     * @return the value of moe_book_online_profile.instagram
     *
     * @mbg.generated
     */
    public String getInstagram() {
        return instagram;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.instagram
     *
     * @param instagram the value for moe_book_online_profile.instagram
     *
     * @mbg.generated
     */
    public void setInstagram(String instagram) {
        this.instagram = instagram == null ? null : instagram.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.google
     *
     * @return the value of moe_book_online_profile.google
     *
     * @mbg.generated
     */
    public String getGoogle() {
        return google;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.google
     *
     * @param google the value for moe_book_online_profile.google
     *
     * @mbg.generated
     */
    public void setGoogle(String google) {
        this.google = google == null ? null : google.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.yelp
     *
     * @return the value of moe_book_online_profile.yelp
     *
     * @mbg.generated
     */
    public String getYelp() {
        return yelp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.yelp
     *
     * @param yelp the value for moe_book_online_profile.yelp
     *
     * @mbg.generated
     */
    public void setYelp(String yelp) {
        this.yelp = yelp == null ? null : yelp.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.other
     *
     * @return the value of moe_book_online_profile.other
     *
     * @mbg.generated
     */
    public String getOther() {
        return other;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.other
     *
     * @param other the value for moe_book_online_profile.other
     *
     * @mbg.generated
     */
    public void setOther(String other) {
        this.other = other == null ? null : other.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.language
     *
     * @return the value of moe_book_online_profile.language
     *
     * @mbg.generated
     */
    public String getLanguage() {
        return language;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.language
     *
     * @param language the value for moe_book_online_profile.language
     *
     * @mbg.generated
     */
    public void setLanguage(String language) {
        this.language = language == null ? null : language.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.button_color
     *
     * @return the value of moe_book_online_profile.button_color
     *
     * @mbg.generated
     */
    public String getButtonColor() {
        return buttonColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.button_color
     *
     * @param buttonColor the value for moe_book_online_profile.button_color
     *
     * @mbg.generated
     */
    public void setButtonColor(String buttonColor) {
        this.buttonColor = buttonColor == null ? null : buttonColor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.create_time
     *
     * @return the value of moe_book_online_profile.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.create_time
     *
     * @param createTime the value for moe_book_online_profile.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.update_time
     *
     * @return the value of moe_book_online_profile.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.update_time
     *
     * @param updateTime the value for moe_book_online_profile.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.company_id
     *
     * @return the value of moe_book_online_profile.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.company_id
     *
     * @param companyId the value for moe_book_online_profile.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.tiktok
     *
     * @return the value of moe_book_online_profile.tiktok
     *
     * @mbg.generated
     */
    public String getTiktok() {
        return tiktok;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.tiktok
     *
     * @param tiktok the value for moe_book_online_profile.tiktok
     *
     * @mbg.generated
     */
    public void setTiktok(String tiktok) {
        this.tiktok = tiktok == null ? null : tiktok.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.description
     *
     * @return the value of moe_book_online_profile.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.description
     *
     * @param description the value for moe_book_online_profile.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_profile.business_hours_json
     *
     * @return the value of moe_book_online_profile.business_hours_json
     *
     * @mbg.generated
     */
    public String getBusinessHoursJson() {
        return businessHoursJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_profile.business_hours_json
     *
     * @param businessHoursJson the value for moe_book_online_profile.business_hours_json
     *
     * @mbg.generated
     */
    public void setBusinessHoursJson(String businessHoursJson) {
        this.businessHoursJson = businessHoursJson == null ? null : businessHoursJson.trim();
    }
}
