package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.BookOnlineAvailableStaffDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface BookOnlineAvailableStaffMapper {
    BookOnlineAvailableStaffMapper INSTANCE = Mappers.getMapper(BookOnlineAvailableStaffMapper.class);

    BookOnlineAvailableStaffDTO entity2DTO(MoeBookOnlineAvailableStaff entity);
}
