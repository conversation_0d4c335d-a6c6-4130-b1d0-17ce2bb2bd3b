package com.moego.server.grooming.service.ob.component;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.moego.common.enums.StaffEnum;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.ObConfigTeam;
import com.moego.server.grooming.service.OBConfigTeamService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.web.vo.ob.component.BaseComponentVO;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = LandingPageComponentEnum.COMPONENT_OB_CONFIG_TEAMS)
@RequiredArgsConstructor
public class TeamsComponentService implements ILandingPageComponentService {

    private final OBConfigTeamService obConfigTeamService;
    private final IBusinessStaffService staffApi;

    @Override
    public LandingPageTeamListVO getPageComponent(MoeBookOnlineLandingPageConfig landingPageConfig) {
        Integer businessId = landingPageConfig.getBusinessId();

        Map<Integer, MoeStaffDto> staffIdToStaff = getActiveStaffMap(businessId);

        Set<Integer> activeStaffIds = staffIdToStaff.keySet();

        List<ObConfigTeam> teams = obConfigTeamService.list(businessId).stream()
                .filter(ObConfigTeam::getIsEnabled)
                .filter(team -> activeStaffIds.contains(team.getStaffId()))
                .toList();

        return new LandingPageTeamListVO().setTeams(toTeamVOs(teams, staffIdToStaff));
    }

    private Map<Integer, MoeStaffDto> getActiveStaffMap(Integer businessId) {
        return staffApi.getStaffList(new StaffIdListParams(businessId, null)).stream()
                .filter(staff -> Objects.equals(staff.getStatus(), StaffEnum.STATUS_NORMAL))
                .collect(toMap(MoeStaffDto::getId, identity()));
    }

    private static List<LandingPageTeamVO> toTeamVOs(List<ObConfigTeam> teams, Map<Integer, MoeStaffDto> idToStaff) {
        return teams.stream()
                .sorted(Comparator.<ObConfigTeam>comparingInt(
                                team -> Optional.ofNullable(idToStaff.get(team.getStaffId()))
                                        .map(MoeStaffDto::getSort)
                                        .orElse(Integer.MIN_VALUE))
                        .reversed())
                .map(team -> toTeamVO(team, idToStaff))
                .toList();
    }

    private static LandingPageTeamVO toTeamVO(ObConfigTeam team, Map<Integer, MoeStaffDto> idToStaff) {
        LandingPageTeamVO vo = new LandingPageTeamVO();
        vo.setStaffId(team.getStaffId());
        vo.setIntroduction(team.getIntroduction());
        vo.setInstagramLink(team.getInstagramLink());
        vo.setTags(JsonUtil.toList(team.getTags(), String.class));

        MoeStaffDto staff = idToStaff.get(team.getStaffId());
        vo.setStaffName(Optional.ofNullable(staff)
                .map(OBLandingPageConfigService::getFormattedName)
                .orElse(""));
        vo.setStaffAvatar(
                Optional.ofNullable(staff).map(MoeStaffDto::getAvatarPath).orElse(""));

        return vo;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    public static class LandingPageTeamListVO extends BaseComponentVO {

        private List<LandingPageTeamVO> teams;

        @Override
        public String getComponent() {
            return LandingPageComponentEnum.OB_CONFIG_TEAMS.getComponent();
        }
    }

    /**
     * @see <a href="https://www.figma.com/file/qGCiknaG6wZh7XrzOuzwsq/OB---master-file?type=design&node-id=6321-3526&mode=design&t=qpOjKnGlcqlR1h7O-0">Figma</a>
     */
    @Data
    public static class LandingPageTeamVO {
        private Integer staffId;
        private String staffName;
        private String staffAvatar;
        private String introduction;
        private String instagramLink;
        private List<String> tags;
    }
}
