package com.moego.server.grooming.service;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.server.grooming.mapper.MoeInvoiceDepositMapper;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.DepositVo;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/10/21 11:26 AM
 */
@Slf4j
@Service
public class DepositService {

    public static final String DEPOSIT_PREFIX = "de_";
    public static final String PREAUTH_PREFIX = "preauth_";

    @Autowired
    private MoeInvoiceDepositMapper invoiceDepositMapper;

    @Autowired
    private MoeGroomingOnlineFeeInvoiceService onlineFeeInvoiceService;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    /**
     * check existing before creating new record for same invoiceId
     * @param depositVo deposit信息
     * @param newGuid 是否生成新的guid
     * @return
     * TODO(Ritchie, P2): 验证 @Transactional 是否生效, 如果生效可以应用到其它这种限定唯一的场景
     */
    @Transactional
    public String createDepositId(DepositVo depositVo, Boolean newGuid) {
        log.info("create or update deposit link for : {} ", depositVo.getInvoiceId());

        var order = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(depositVo.getInvoiceId()).build());
        if (OrderModel.OrderType.DEPOSIT.equals(order.getOrderType())) {
            // 存在 deposit order 表示之前已经创建好了，直接返回 guid
            return order.getGuid();
        }

        MoeInvoiceDeposit existing = invoiceDepositMapper.selectByInvoiceId(depositVo.getInvoiceId());
        MoeInvoiceDeposit record = new MoeInvoiceDeposit();
        if (existing == null) {
            // 第一次生成guid，只在需要添加processing fee时新增记录
            if (Boolean.TRUE.equals(depositVo.getRequiredProcessingFee())) {
                onlineFeeInvoiceService.addOrUpdateOnlineFeeRecord(
                        depositVo.getBusinessId(),
                        depositVo.getInvoiceId(),
                        InvoiceStatusEnum.TYPE_ONLINE_DEPOSIT,
                        depositVo.getRequiredProcessingFee());
            }
            BeanUtils.copyProperties(depositVo, record);
            record.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(depositVo.getBusinessId()));
            if (BooleanUtils.isTrue(newGuid)) {
                String deGuid = DEPOSIT_PREFIX + CommonUtil.getUuid();
                record.setDeGuid(deGuid);
            }
            Integer id = invoiceDepositMapper.insertSelective(record);
            log.info("create new record {}", id);
            return record.getDeGuid();
        } else {
            if (!existing.getBusinessId().equals(depositVo.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
            }
            // 在已有记录的情况下，需要刷新requiredProcessingFee开关
            onlineFeeInvoiceService.addOrUpdateOnlineFeeRecord(
                    depositVo.getBusinessId(),
                    depositVo.getInvoiceId(),
                    InvoiceStatusEnum.TYPE_ONLINE_DEPOSIT,
                    depositVo.getRequiredProcessingFee());

            record.setId(existing.getId());
            record.setAmount(depositVo.getAmount());
            Integer count = invoiceDepositMapper.updateByPrimaryKeySelective(record);
            log.info("update {} deposit record from {} to {} ", count, existing.getAmount(), depositVo.getAmount());
            return existing.getDeGuid();
        }
    }

    public MoeInvoiceDeposit getDepositByGuid(String deGuid) {
        return invoiceDepositMapper.selectByDeGuid(deGuid);
    }

    public MoeInvoiceDeposit getDepositByInvoiceId(Integer invoiceId) {
        return invoiceDepositMapper.selectByInvoiceId(invoiceId);
    }
}
