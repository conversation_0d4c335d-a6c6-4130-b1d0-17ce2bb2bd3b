package com.moego.server.grooming.service;

import com.google.gson.Gson;
import com.intuit.ipp.core.IEntity;
import com.intuit.ipp.data.Account;
import com.intuit.ipp.data.AccountTypeEnum;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.services.QueryResult;
import com.intuit.oauth2.client.OAuth2PlatformClient;
import com.intuit.oauth2.config.Scope;
import com.intuit.oauth2.data.BearerTokenResponse;
import com.intuit.oauth2.data.UserInfoResponse;
import com.intuit.oauth2.exception.InvalidRequestException;
import com.intuit.oauth2.exception.OAuthException;
import com.intuit.oauth2.exception.OpenIdException;
import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.QuickBooksConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.ratelimit.v1.Dimension;
import com.moego.idl.models.ratelimit.v1.Rule;
import com.moego.idl.models.ratelimit.v1.TimeSlot;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.metadata.v1.UpdateValueRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.ratelimit.v1.RateLimitServiceGrpc;
import com.moego.idl.service.ratelimit.v1.RegisterRulesRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeQbConnectMapper;
import com.moego.server.grooming.mapper.MoeQbSettingMapper;
import com.moego.server.grooming.mapper.MoeQbTaskMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeQbConnect;
import com.moego.server.grooming.mapperbean.MoeQbSetting;
import com.moego.server.grooming.mapperbean.MoeQbTask;
import com.moego.server.grooming.mapstruct.QuickBookMapper;
import com.moego.server.grooming.service.dto.QBAccountDto;
import com.moego.server.grooming.service.dto.QBAccountReturnDto;
import com.moego.server.grooming.service.dto.QBBusinessSettingDto;
import com.moego.server.grooming.service.dto.QBConnectReturnDto;
import com.moego.server.grooming.service.dto.QbQueryGroomingResultDto;
import com.moego.server.grooming.service.intuit.InvoiceSyncJob;
import com.moego.server.grooming.service.intuit.QuickBooksContext;
import com.moego.server.grooming.service.intuit.RedisGroomingListElementObj;
import com.moego.server.grooming.service.intuit.helper.InvoiceHelper;
import com.moego.server.grooming.service.intuit.qbo.BusinessDataServiceFactory;
import com.moego.server.grooming.service.intuit.qbo.OAuth2PlatformClientFactory;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.web.vo.QBCreateConnectVo;
import com.moego.server.grooming.web.vo.QBSettingUpdateVo;
import com.moego.server.grooming.web.vo.QbSetUpVo;
import com.moego.server.payment.dto.ListQuickBookSettingDTO;
import com.moego.server.payment.params.ListQuickBookSettingParams;
import com.moego.server.payment.params.UpdateQuickBookSettingParams;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class QuickBooksService {

    @Autowired
    private MoeQbConnectMapper qbConnectMapper;

    @Autowired
    private BusinessDataServiceFactory dataServiceFactory;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeQbSettingMapper qbSettingMapper;

    @Autowired
    private MoeQbTaskMapper qbTaskMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    OAuth2PlatformClientFactory factory;

    @Autowired
    private QuickBooksSyncService quickBooksSyncService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataClient;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderServiceBlockingStub;

    @Autowired
    private RateLimitServiceGrpc.RateLimitServiceBlockingStub rateLimitServiceBlockingStub;

    private static final String QB_METADATA_GRAY_SCALES_TEST_KEY = "quickbook_v2_enabled";
    private static final String QB_METADATA_USER_OPTIONS_KEY = "quickbook_v2_pre_onboarding";
    private static final Integer QB_RATE_LIMITER_THRESHOLD = 20;

    public QBBusinessSettingDto getDefaultSettingDto() {
        QBBusinessSettingDto settingDto = new QBBusinessSettingDto();
        settingDto.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);
        return settingDto;
    }

    /**
     * 在查询qb数据前，通过这个校验
     *
     * @param businessId
     * @return
     */
    public QBBusinessSettingDto getQbConnectSettingMustAvailable(Integer businessId) {
        QBBusinessSettingDto settingDto = getQbConnectSetting(businessId);
        if (settingDto == null
                || !QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())
                || !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(settingDto.getConnectStatus())) {
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_SETTING_ERROR);
        }
        return settingDto;
    }

    public String getQbRedisListKey(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_FORMAT_STR, businessId);
    }

    public String getQbRedisListKeyLegacy(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_FORMAT_STR_LEGACY, businessId);
    }

    public String getQbRedisListKeyInProgress(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_FORMAT_STR_SYNCING, businessId);
    }

    public String getQbRedisListKeyInProgressLegacy(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_FORMAT_STR_SYNCING_LEGACY, businessId);
    }

    public String getQbRedisErrorListKey(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_FORMAT_STR_ERROR, businessId);
    }

    public String getQbRedisSyncResultKey(Integer businessId) {
        return String.format(QuickBooksConst.REDIS_KEY_QUICK_BOOK_ACCOUNT_SYNC_RESULT, businessId);
    }

    /**
     * 将需要同步的任务存储在缓存
     *
     * @param businessId
     * @param groomingId
     * @param appointmentDate
     */
    public void addRedisSyncGroomingData(Integer businessId, Integer groomingId, String appointmentDate) {
        // 获取business配置
        QBBusinessSettingDto settingDto = getQbConnectSetting(businessId);
        if (settingDto == null
                || settingDto.getSetttingId() == null
                || !QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())) {
            // 没有打开qb设置
            return;
        }
        if (appointmentDate == null) {
            MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
            appointmentDate = moeGroomingAppointment.getAppointmentDate();
        }
        Long syncCreateTime = DateUtil.get10Timestamp();
        // 同步的日期需要大于等于设置内的时间，才允许增加到队列内
        if (DateUtil.twoStrDateCompare(appointmentDate, settingDto.getSyncBeginDate(), null) >= 0) {
            String qbRedisListKey = getQbRedisListKey(businessId);
            MoeGroomingInvoice groomingInvoice = orderService.getOrderByGroomingId(businessId, groomingId);
            if (Objects.isNull(groomingInvoice) || !CommonUtil.isNormal(groomingInvoice.getId())) {
                return;
            }
            InvoiceSyncJob elementObj = new InvoiceSyncJob(businessId, groomingInvoice.getId(), syncCreateTime);
            // 增加在最前面
            redisUtil.lLeftPush(qbRedisListKey, new Gson().toJson(elementObj));
        }
    }

    /**
     * 将需要同步的任务存储在缓存
     *
     * @param businessId
     * @param invoiceId
     */
    public void addSyncJobByInvoiceId(Integer businessId, Integer invoiceId) {
        // payment 调用时，可能没有 businessId
        MoeGroomingInvoice groomingInvoice = orderService.getOrderById(businessId, invoiceId);
        if (Objects.isNull(businessId) || businessId == 0) {
            businessId = groomingInvoice.getBusinessId();
        }

        // 获取business配置
        QBBusinessSettingDto settingDto = getQbConnectSetting(businessId);
        if (Objects.isNull(settingDto)
                || Objects.isNull(settingDto.getSetttingId())
                || !QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())) {
            // 没有打开qb设置
            return;
        }

        // 同步的日期需要大于等于设置的日期，才增加到同步任务队列
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        LocalDate invoiceDate = LocalDate.ofInstant(
                Instant.ofEpochSecond(groomingInvoice.getUpdateTime()), ZoneId.of(businessInfo.getTimezoneName()));
        LocalDate syncBeginDate = LocalDate.parse(settingDto.getSyncBeginDate());

        if (invoiceDate.isAfter(syncBeginDate) || invoiceDate.isEqual(syncBeginDate)) {
            String qbRedisListKey = getQbRedisListKey(businessId);
            InvoiceSyncJob elementObj =
                    new InvoiceSyncJob(businessId, invoiceId, Instant.now().getEpochSecond());
            // 增加在最前面
            redisUtil.lLeftPush(qbRedisListKey, JsonUtil.toJson(elementObj));
        }
    }

    /**
     * 转换yyyy-MM-dd格式的String时间
     * @param date 需要转换的时间戳, 如果为空返回now+1day
     * @return long类型的时间戳, 粒度为秒级
     */
    private Long convertTimeStringToLongYYYYMMDD(String date) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.now().plusDays(1);
        if (!Strings.isEmpty(date)) localDate = LocalDate.parse(date, dtf);
        return localDate
                .atStartOfDay(ZoneId.systemDefault()) // 使用默认时区，并假设时间为午夜
                .toInstant()
                .getEpochSecond();
    }

    /**
     * 按时间范围查找出同步的任务存储在缓存
     * @param businessId 商家id
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    public void addSyncJobByDateRange(Integer businessId, String startDate, String endDate) {
        List<QbQueryGroomingResultDto> groomingResultDtos =
                moeGroomingAppointmentMapper.queryAppointmentDateByDateRange(businessId, startDate, endDate);
        if (CollectionUtils.isEmpty(groomingResultDtos)) {
            return;
        }

        List<Integer> groomingIdList = groomingResultDtos.stream()
                .map(QbQueryGroomingResultDto::getGroomingId)
                .toList();
        List<MoeGroomingInvoice> listByGroomingIds =
                orderService.getListByGroomingIds(businessId, groomingIdList, null);
        var setting = getQbConnectSetting(businessId);
        // 这里尽量不影响旧逻辑，避免出现商家觉得多出一堆订单的情况
        if (QuickBooksConst.USER_VERSION_NEW.equals(setting.getUserVersion())) {
            // 新增查找package和retail部分订单
            var lStartTime = convertTimeStringToLongYYYYMMDD(startDate);
            var eStartTime = convertTimeStringToLongYYYYMMDD(endDate);
            var packageOrders = orderService.getSaleOrderByDateRange(
                    businessId, lStartTime, eStartTime, InvoiceHelper.TYPE_PACKAGE);
            var retailOrders = orderService.getSaleOrderByDateRange(
                    businessId, lStartTime, eStartTime, InvoiceHelper.TYPE_PRODUCT);
            listByGroomingIds.addAll(packageOrders);
            listByGroomingIds.addAll(retailOrders);
        }

        String qbRedisListKey = getQbRedisListKey(businessId);
        Long syncCreateTime = DateUtil.get10Timestamp();
        listByGroomingIds.stream()
                .map(MoeGroomingInvoice::getId)
                // 去重是因为有可能出现retail记录是依赖appt产生的, 这时候会有两个一样订单id
                .distinct()
                .map(id -> new InvoiceSyncJob(businessId, id, syncCreateTime))
                .forEach(elementObj -> redisUtil.lRightPush(qbRedisListKey, JsonUtil.toJson(elementObj)));
    }

    /**
     * 根据时间戳补偿任务, 目前仅用在mis上的qb补偿机制
     *
     * @param businessId business id
     * @param startTime  order update time start time
     * @param endTime    order update time end time
     * @return true or false
     */
    public int compensationByDateRange(Integer businessId, Long startTime, Long endTime) {
        var setting = getQbConnectSetting(businessId);
        if (setting.getEnableSync().equals(QuickBooksConst.ENABLE_SYNC_CLOSE)) {
            log.error("this business setting enable sync close, businessId:{}, startTime:{}", businessId, startTime);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "this business setting enable sync close");
        }
        var orders = orderService.listOrderByTimeRange(businessId, startTime, endTime);
        if (CollectionUtils.isEmpty(orders)) {
            log.error("business id:{} not found groomingOrders with this time", businessId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not found order with this time");
        }
        String qbRedisListKey = getQbRedisListKey(businessId);
        Long syncCreateTime = DateUtil.get10Timestamp();
        var jobs = orders.stream()
                .map(MoeGroomingInvoice::getId)
                .distinct()
                .map(id -> new InvoiceSyncJob(businessId, id, syncCreateTime))
                .map(JsonUtil::toJson)
                .toList();
        redisUtil.lRightPushAll(qbRedisListKey, jobs);
        return 1;
    }

    /**
     * 将需要同步的支付记录存储在缓存
     * @param businessId 商家id
     * @param paymentId 退款记录id
     */
    public Long addRedisNeedSyncPaymentToListRight(Integer businessId, Integer paymentId) {
        String qbRedisListKey = quickBooksSyncService.getQBSyncPaymentKey(businessId);
        return redisUtil.lRightPush(qbRedisListKey, String.valueOf(paymentId));
    }

    /**
     * 将同步失败的对象放在list最后，等待下次重新同步
     *
     * @param businessId
     * @param elementObj
     */
    public void addRedisErrorSyncToListRight(Integer businessId, InvoiceSyncJob elementObj) {
        String qbRedisListKey = getQbRedisListKey(businessId);
        elementObj.errorCountPlus();
        redisUtil.lRightPush(qbRedisListKey, JsonUtil.toJson(elementObj));
    }

    /**
     * 将同步失败的对象放在list最后，等待下次重新同步
     *
     * @param businessId
     * @param objJson
     */
    public void addRedisErrorSyncToListRight(Integer businessId, String objJson) {
        String qbRedisListKey = getQbRedisErrorListKey(businessId);
        redisUtil.lRightPush(qbRedisListKey, objJson);
    }

    /**
     * 将qb redis 队列删除，用户重新关联的用户
     *
     * @param businessId
     */
    public void deleteRedisQbListByBusinessId(Integer businessId) {
        String qbRedisListKey = getQbRedisListKey(businessId);
        redisUtil.delete(qbRedisListKey);
    }

    public String getQbInProgress(Integer businessId) {
        String qbRedisStringKey = getQbRedisListKeyInProgress(businessId);
        return redisUtil.get(qbRedisStringKey);
    }

    public String getQbInProgressLegacy(Integer businessId) {
        String qbRedisStringKey = getQbRedisListKeyInProgressLegacy(businessId);
        return redisUtil.get(qbRedisStringKey);
    }

    public void setQbInProgress(Integer businessId, String objJson) {
        String qbRedisStringKey = getQbRedisListKeyInProgress(businessId);
        redisUtil.setEx(qbRedisStringKey, objJson, CommonConstant.DEFAULT_CACHE_TTL, TimeUnit.SECONDS);
    }

    public void delQbInProgress(Integer businessId) {
        String qbRedisStringKey = getQbRedisListKeyInProgress(businessId);
        redisUtil.delete(qbRedisStringKey);
    }

    public InvoiceSyncJob getRedisSyncGroomingLegacy(Integer businessId) {
        String qbRedisListKey = getQbRedisListKeyLegacy(businessId);
        String objJson = getQbInProgressLegacy(businessId);
        if (StringUtils.isEmpty(objJson)) {
            objJson = redisUtil.lLeftPop(qbRedisListKey);
            if (StringUtils.isEmpty(objJson)) {
                return null;
            }
        }

        RedisGroomingListElementObj elementObj = JsonUtil.toBean(objJson, RedisGroomingListElementObj.class);
        MoeGroomingInvoice orderInvoice = orderService.getOrderByGroomingId(businessId, elementObj.getGroomingId());
        if (Objects.isNull(orderInvoice)) {
            return null;
        }
        InvoiceSyncJob invoiceSyncJob = new InvoiceSyncJob(
                businessId, orderInvoice.getId(), elementObj.getSyncTime(), elementObj.getErrorCount());
        objJson = JsonUtil.toJson(invoiceSyncJob);

        // 将当前值保存在in progress key内
        setQbInProgress(businessId, objJson);

        // 错误多次的转移到另一个队列内
        if (elementObj.getErrorCount() == null || elementObj.getErrorCount() >= 3) {
            addRedisErrorSyncToListRight(businessId, objJson);
            // 当前值已转移，从in progress移除
            delQbInProgress(businessId);
            return getRedisSyncGroomingLegacy(businessId);
        }
        return invoiceSyncJob;
    }

    public InvoiceSyncJob getRedisSyncGrooming(Integer businessId) {
        String qbRedisListKey = getQbRedisListKey(businessId);
        String objJson = getQbInProgress(businessId);
        if (StringUtils.isEmpty(objJson)) {
            objJson = redisUtil.lLeftPop(qbRedisListKey);
            if (StringUtils.isEmpty(objJson)) {
                return null;
            }
        }
        // 将当前值保存在in progress key内
        setQbInProgress(businessId, objJson);

        if (!StringUtils.isEmpty(objJson)) {
            InvoiceSyncJob elementObj = JsonUtil.toBean(objJson, InvoiceSyncJob.class);
            // 错误多次的转移到另一个队列内
            if (elementObj.getErrorCount() == null || elementObj.getErrorCount() >= 3) {
                addRedisErrorSyncToListRight(businessId, objJson);
                // 当前值已转移，从in progress移除
                delQbInProgress(businessId);
                return getRedisSyncGrooming(businessId);
            }
            return elementObj;
        }
        return null;
    }

    private void registerRules() {
        var req = RegisterRulesRequest.newBuilder()
                .setDomain("QuickBooksService")
                .addRules(Rule.newBuilder()
                        .setName("the quick books rate limiter")
                        .addAllDimensions(List.of(
                                Dimension.newBuilder()
                                        .setLabel("BusinessID")
                                        .setExist(true)
                                        .build(),
                                Dimension.newBuilder()
                                        .setLabel("API")
                                        .setExact("QuickBooksService")
                                        .build()))
                        .setType(Rule.Type.NORMAL)
                        .setThreshold(QB_RATE_LIMITER_THRESHOLD)
                        .setTimeSlot(TimeSlot.newBuilder()
                                .setSize(1)
                                .setUnit(TimeSlot.Unit.MINUTE)
                                .build())
                        .build())
                .build();
        var resp = rateLimitServiceBlockingStub.registerRules(req);
        // 这里可以忽略resp 的成功与否, 因为每次都会注册, 只要有一次注册成功即可, 这里打个日志
        if (!resp.getSucceed()) {
            log.error("register rate limiter failed");
        }
    }

    private List<MoeQbSetting> preCheck() {
        // 注册限流, business 维度 40/m
        registerRules();
        List<MoeQbSetting> needSyncData = new ArrayList<>();
        List<MoeQbSetting> settingBatchUpdateList = new ArrayList<>();

        // 拿到所有需要同步的数据, 即同步状态正常开启的数据
        qbSettingMapper.selectAllStatusNormal().stream()
                .filter(setting -> QuickBooksConst.ENABLE_SYNC_OPEN.equals(setting.getEnableSync()))
                .forEach(setting -> {
                    if (QuickBooksConst.USER_VERSION_NEW.equals(setting.getUserVersion())) {
                        // 已经是新版本的用户, 直接同步
                        needSyncData.add(setting);
                        return;
                    }

                    var businessId = setting.getBusinessId();
                    var companyId = setting.getCompanyId();
                    // metadata 中确定是否开启灰度
                    var grayScalesValue = getQBMetadataValue(QB_METADATA_GRAY_SCALES_TEST_KEY, companyId);
                    if (!Boolean.parseBoolean(grayScalesValue)) {
                        // 没有灰度, 直接同步
                        needSyncData.add(setting);
                        return;
                    }

                    // 开启了灰度, 需要先洗一遍数据, 在 metadata 中确定该用户选择的同步模式
                    // keep: 保持旧逻辑同步, 但是从业务逻辑的角度, 灰度开关一定是false, 所以不需要在这一步处理
                    // auto: 自动使用新QB同步, 并迁移setting 时间内的数据到新账号, 代码表现是把这个time range 的数据用新逻辑跑一次sync task
                    // manual: 手动同步, 需要给他断开链接并标记为迁移, 下一次重连会走迁移逻辑, 迁移逻辑会把迁移标记删掉
                    // manual_informed: 手动同步已通知, 与manual一样
                    var option = getQBMetadataValue(QB_METADATA_USER_OPTIONS_KEY, companyId);
                    if (StringUtils.hasText(option)) option = option.replace("\"", "");
                    if (QuickBooksConst.QB_OPTION_AUTO.equalsIgnoreCase(option)) {
                        log.info("auto sync for businessId:{} companyId:{}", businessId, companyId);
                        // 选择自动重连, 把该用户的 user version 设置成 new , 然后再走同步逻辑
                        setting.setUserVersion(QuickBooksConst.USER_VERSION_NEW);
                        needSyncData.add(setting);
                    } else if (QuickBooksConst.QB_OPTION_MANUAL.equalsIgnoreCase(option)
                            || QuickBooksConst.QB_OPTION_MANUAL_INFORMED.equalsIgnoreCase(option)) {
                        log.info("close connect for businessId:{} companyId:{}", businessId, companyId);
                        // 关闭该用户的同步
                        setting.setStatus(QuickBooksConst.STATUS_EXPIRE);
                        setting.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);
                    } else {
                        // 这种情况是 开启了灰度, 但是没有选择同步模式的数据, 能走到这里应该是user version = 0 且option = ""或者其他, 属于异常情况, 打个日志标记一下
                        // 不影响该用户的同步
                        log.warn(
                                "businessId:{} companyId:{} has gray but metadata non set option or error, option:{}",
                                businessId,
                                companyId,
                                option);
                        needSyncData.add(setting);
                        return;
                    }
                    settingBatchUpdateList.add(setting);
                });
        if (!settingBatchUpdateList.isEmpty()) qbSettingMapper.batchUpdateByPrimaryKeySelective(settingBatchUpdateList);
        return needSyncData;
    }

    private String getQBMetadataValue(String key, Long companyId) {
        var response = metadataClient.extractValues(ExtractValuesRequest.newBuilder()
                .setKeyName(key)
                .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, companyId)
                .build());
        var values = response.getValues();
        if (!values.containsKey(key)) {
            return metadataClient
                    .getKey(GetKeyRequest.newBuilder().setName(key).build())
                    .getKey()
                    .getDefaultValue();
        }
        return values.get(key);
    }

    @TimerMetrics(group = TimerGroup.TASK)
    public List<String> beginTask() {
        List<MoeQbSetting> qbSettingList = preCheck();
        // 将已有的超时task标记为失败
        Long nowTime = DateUtil.get10Timestamp();
        qbTaskMapper.updateFailTask(nowTime, nowTime - QuickBooksConst.TASK_FAIL_TIME_SECOND);
        List<String> taskStatusRecord = new ArrayList<>();
        String settingError = "businessId:%s skip   reason:setting status error";
        String existTask = "businessId:%s skip   reason:already have task count:%s";
        String noRedisTask = "businessId:%s skip   reason:no redis list need sync";
        String startTask = "businessId:%s start redis data count:%s";

        List<Future> futureList = new LinkedList<>();

        for (MoeQbSetting qbSetting : qbSettingList) {
            Integer businessId = qbSetting.getBusinessId();
            Long companyId = qbSetting.getCompanyId();
            String qbRedisListKey = getQbRedisListKey(businessId);
            Long needSyncLen = redisUtil.lLen(qbRedisListKey);
            if (needSyncLen == null || needSyncLen == 0) {
                taskStatusRecord.add(String.format(noRedisTask, businessId));
                continue;
            }
            QBBusinessSettingDto settingDto = getQbConnectSetting(businessId);
            if (settingDto == null
                    || !QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())
                    || !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(settingDto.getConnectStatus())) {
                taskStatusRecord.add(String.format(settingError, businessId));
                continue;
            }
            MoeQbTask qbTask = qbTaskMapper.selectBusinessIdTask(qbSetting.getBusinessId());

            if (qbTask != null) {
                taskStatusRecord.add(String.format(existTask, businessId, needSyncLen));
                // 已有task在执行就不重复执行了
                continue;
            }
            // 创建新的task执行记录
            MoeQbTask qbNewTask = new MoeQbTask();
            qbNewTask.setBusinessId(businessId);
            qbNewTask.setCompanyId(companyId);
            qbNewTask.setTaskStatus(QuickBooksConst.TASK_STATUS_START);
            Long startTime = DateUtil.get10Timestamp();
            qbNewTask.setCreateTime(startTime);
            qbNewTask.setUpdateTime(startTime);
            // businessId taskStatus completeTime有唯一索引，重复创建会报错
            qbTaskMapper.insertSelective(qbNewTask);
            taskStatusRecord.add(String.format(startTask, businessId, needSyncLen));
            // 没有报错就执行下去
            Future future = ThreadPool.submit(() -> {
                QuickBooksContext.setBusinessId(businessId);
                try {
                    log.info(String.format("businessId:%s start new background task", businessId));
                    beginOneBusinessQbSyncTask(
                            businessId, qbTaskMapper.selectByPrimaryKey(qbNewTask.getId()), settingDto);
                } finally {
                    QuickBooksContext.clear();
                }
            });
            futureList.add(future);
        }

        // 阻塞等待所有子线程运行完毕，以便 TimerMetrics 记录真实的运行时间
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("QuickBooksService beginTask future task error", e);
            }
        });

        return taskStatusRecord;
    }

    public void beginOneBusinessQbSyncTask(Integer businessId, MoeQbTask qbTask, QBBusinessSettingDto settingDto) {
        InvoiceSyncJob invoiceSyncJob = getRedisSyncGroomingLegacy(businessId);
        if (Objects.isNull(invoiceSyncJob)) {
            invoiceSyncJob = getRedisSyncGrooming(businessId);
        }

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        while (invoiceSyncJob != null) {
            MoeQbTask updateQbTask = new MoeQbTask();
            updateQbTask.setId(qbTask.getId());
            updateQbTask.setUpdateTime(DateUtil.get10Timestamp());

            try {
                // 这里判断order的类型, 并走对应的逻辑
                syncByOrderType(businessInfo, invoiceSyncJob, settingDto);
                updateQbTask.setCompleteCount(qbTask.getCompleteCount() + 1);
            } catch (Exception e) {
                // 后台任务的异常不会中断执行，会记录错误信息，并执行下一个
                log.error("qbSyncError businessId:{}, groomingDataJson: {}, exception:", businessId, invoiceSyncJob, e);
                addRedisErrorSyncToListRight(businessId, invoiceSyncJob);
            }
            qbTaskMapper.updateByPrimaryKeySelective(updateQbTask);
            // 更新task数据
            qbTask = qbTaskMapper.selectByPrimaryKey(qbTask.getId());
            delQbInProgress(businessId);
            invoiceSyncJob = getRedisSyncGroomingLegacy(businessId);
            if (Objects.isNull(invoiceSyncJob)) {
                invoiceSyncJob = getRedisSyncGrooming(businessId);
            }
        }
        MoeQbTask closeTask = new MoeQbTask();
        closeTask.setId(qbTask.getId());
        closeTask.setTaskStatus(QuickBooksConst.TASK_STATUS_COMPLETE);
        closeTask.setCompleteTime(DateUtil.get10Timestamp());
        closeTask.setUpdateTime(DateUtil.get10Timestamp());
        qbTaskMapper.updateByPrimaryKeySelective(closeTask);
    }

    private void syncByOrderType(
            MoeBusinessDto businessInfo, InvoiceSyncJob invoiceSyncJob, QBBusinessSettingDto settingDto) {
        String minSyncDate;
        var businessId = businessInfo.getId();
        OrderDetailModel orderDetail = orderService.getOrderDetailByOrderId(businessId, invoiceSyncJob.getInvoiceId());
        if (isSyncBaseOnInvoice(orderDetail)) {
            quickBooksSyncService.syncInvoiceByInvoiceId(businessId, orderDetail, invoiceSyncJob.getSyncTime());
            minSyncDate = DateUtil.getStringDate(invoiceSyncJob.getSyncTime());
        } else {
            MoeGroomingAppointment groomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                    Math.toIntExact(orderDetail.getOrder().getSourceId()));

            if (groomingAppointment == null) {
                throw new CommonException(ResponseCodeEnum.GROOMING_NOT_FOUND);
            }

            LocalDate invoiceDate = LocalDate.ofInstant(
                    Instant.ofEpochSecond(orderDetail.getOrder().getUpdateTime()),
                    ZoneId.of(businessInfo.getTimezoneName()));
            LocalDate syncBeginDate = LocalDate.parse(settingDto.getSyncBeginDate());
            if (invoiceDate.isAfter(syncBeginDate) || invoiceDate.isEqual(syncBeginDate)) {
                quickBooksSyncService.syncInvoiceByGroomingId(
                        businessId, groomingAppointment, invoiceSyncJob.getSyncTime());
            }
            minSyncDate = groomingAppointment.getAppointmentDate();
        }

        // 如果当前同步的预约日期比设置内的最小同步日期小，则更新设置数据
        if (StringUtils.hasText(settingDto.getMinSyncDate())
                || DateUtil.twoStrDateCompare(settingDto.getMinSyncDate(), minSyncDate, null) > 0) {
            MoeQbSetting updateSetting = new MoeQbSetting();
            updateSetting.setMinSyncDate(minSyncDate);
            updateSetting.setId(settingDto.getSetttingId());
            qbSettingMapper.updateByPrimaryKeySelective(updateSetting);
        }
    }

    /**
     * 判断这个order是否需要先查grooming再查invoice, true-直接查invoice
     * @param orderDetail order细节
     * @return true - 直接查invoice, false-走grooming的逻辑
     */
    private boolean isSyncBaseOnInvoice(OrderDetailModel orderDetail) {
        var sourceType = orderDetail.getOrder().getSourceType();
        return Objects.equals(InvoiceHelper.TYPE_PRODUCT, sourceType)
                || Objects.equals(InvoiceHelper.TYPE_PACKAGE, sourceType);
    }

    public List<QBBusinessSettingDto> getQbConnectSettingByCompanyId(Integer companyId) {
        List<MoeQbSetting> qbSettingList = qbSettingMapper.selectByCompanyId(companyId);
        List<QBBusinessSettingDto> settingDtoList = new ArrayList<>();
        for (MoeQbSetting qbSetting : qbSettingList) {
            settingDtoList.add(buildQuickBookSettingDto(qbSetting));
        }
        return settingDtoList;
    }

    public QBBusinessSettingDto getQbConnectSetting(Integer businessId) {
        MoeQbSetting qbExistSetting = qbSettingMapper.selectByBusinessId(businessId, QuickBooksConst.STATUS_NORMAL);
        QBBusinessSettingDto settingDto = buildQuickBookSettingDto(qbExistSetting);
        // 补充oauth url
        settingDto.setOAuthUrl(getOauthUrl(QuickBooksConst.USER_VERSION_OLD, businessId.toString()));
        return settingDto;
    }

    private QBBusinessSettingDto buildQuickBookSettingDto(MoeQbSetting qbExistSetting) {
        QBBusinessSettingDto settingDto;
        if (qbExistSetting == null) {
            settingDto = getDefaultSettingDto();
        } else {
            settingDto = new QBBusinessSettingDto();
            BeanUtils.copyProperties(qbExistSetting, settingDto);
            settingDto.setSetttingId(qbExistSetting.getId());
            if (!QuickBooksConst.STATUS_NORMAL.equals(qbExistSetting.getStatus())) {
                settingDto.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);
            }
            MoeQbConnect connect = qbConnectMapper.selectByPrimaryKey(qbExistSetting.getConnectId());
            if (connect != null) {
                settingDto.setConnectId(connect.getId());
                settingDto.setConnectEmail(connect.getConnectEmail());
                settingDto.setConnectStatus(connect.getConnectStatus());
                settingDto.setConnectCompanyName(connect.getConnectCompanyName());
                settingDto.setQbConnect(connect);
                // 兼容旧版 qb
                settingDto.setAccountName(connect.getAccountName());
                settingDto.setAccountId(connect.getAccountId());
            }
            // 检查setting有效性
            validateQbConnect(qbExistSetting.getBusinessId(), settingDto);
        }
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(settingDto.getConnectId())
                || !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(settingDto.getConnectStatus())) {
            settingDto.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);
        }
        return settingDto;
    }

    private void validateQbConnect(Integer businessId, QBBusinessSettingDto settingDto) {
        if (QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())
                && QuickBooksConst.CONNECT_STATUS_NORMAL.equals(settingDto.getConnectStatus())) {
            try {
                String qbRedisSyncResultKey = getQbRedisSyncResultKey(businessId);
                // 缓存检查信息， 1天内不重复检查
                String value = redisUtil.get(qbRedisSyncResultKey);
                if (!StringUtils.hasLength(value)) {
                    // 如果 token 失效， 捕获异常后更新connect状态
                    dataServiceFactory.executeQuery(
                            settingDto.getQbConnect(),
                            String.format(
                                    "select * from account where AccountType in ('%s','%s')",
                                    AccountTypeEnum.BANK.value(), AccountTypeEnum.OTHER_CURRENT_ASSET.value()));
                    redisUtil.setEx(qbRedisSyncResultKey, businessId.toString(), 1, TimeUnit.DAYS);
                }
            } catch (Exception e) {
                settingDto.setConnectStatus(QuickBooksConst.CONNECT_STATUS_AUTH_ERROR);
            }
        }
    }

    public QBAccountReturnDto getBusinessConnectAccount(Integer businessId) {
        QBAccountReturnDto returnDto = new QBAccountReturnDto();
        QBBusinessSettingDto settingDto = getQbConnectSettingMustAvailable(businessId);
        QueryResult queryResult = dataServiceFactory.executeQuery(
                settingDto.getQbConnect(),
                String.format(
                        "select * from account where AccountType in ('%s','%s')",
                        AccountTypeEnum.BANK.value(), AccountTypeEnum.OTHER_CURRENT_ASSET.value()));
        List<QBAccountDto> accountList = new ArrayList<>();
        for (IEntity entity : queryResult.getEntities()) {
            Account accounts = (Account) entity;
            QBAccountDto accountDto = new QBAccountDto();
            accountDto.setAccountId(accounts.getId());
            accountDto.setAccountName(accounts.getName());
            accountList.add(accountDto);
        }
        returnDto.setAccountList(accountList);
        return returnDto;
    }

    public String getConnectOAuthUrl(List<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "businessIds is empty");
        }
        return getOauthUrl(
                QuickBooksConst.USER_VERSION_NEW,
                businessIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(OAuth2PlatformClientFactory.STATE_DELIMITER)));
    }

    public String getOauthUrl(Integer userVersion, String selectedBizIds) {
        // prepare scopes
        List<Scope> scopes = new ArrayList<Scope>();
        scopes.add(Scope.Accounting);
        scopes.add(Scope.Email);
        scopes.add(Scope.OpenId);
        try {
            return factory.getOAuth2Config().prepareUrl(scopes, factory.getRedirectUri(userVersion), selectedBizIds);
        } catch (InvalidRequestException exception) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_DEV_ERROR);
        }
    }

    @Deprecated
    public Boolean qbCreateSetting(Integer tokenBusinessId, Long tokenCompanyId, Integer connectId) {
        getConnectWithChecking(connectId);

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(tokenBusinessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);

        long createTime = DateUtil.get10Timestamp();
        MoeQbSetting qbSetting = new MoeQbSetting();
        qbSetting.setBusinessId(tokenBusinessId);
        qbSetting.setCompanyId(tokenCompanyId);
        qbSetting.setConnectId(connectId);
        qbSetting.setEnableSync(QuickBooksConst.ENABLE_SYNC_OPEN);
        qbSetting.setStatus(QuickBooksConst.STATUS_NORMAL);
        qbSetting.setSyncBeginDate(DateUtil.convertTimeZoneBySeconds(createTime, businessInfo.getTimezoneName()));
        qbSetting.setMinSyncDate(qbSetting.getSyncBeginDate());
        qbSetting.setCreateTime(createTime);
        qbSetting.setUpdateTime(createTime);
        // 从connect中获取taxSyncType是为了保证 这个 tax sync 的白名单商家继续使用
        Byte taxSyncType = qbSettingMapper.selectTaxSyncTypeByBusinessId(tokenBusinessId);
        if (Objects.nonNull(taxSyncType)) {
            qbSetting.setTaxSyncType(taxSyncType);
        }

        int userVersion = QuickBooksConst.USER_VERSION_OLD;
        try {
            var response = metadataClient.extractValues(ExtractValuesRequest.newBuilder()
                    .setKeyName(QB_METADATA_GRAY_SCALES_TEST_KEY)
                    .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, tokenCompanyId)
                    .build());
            if (Boolean.parseBoolean(
                    response.getValues().getOrDefault(QB_METADATA_GRAY_SCALES_TEST_KEY, Boolean.FALSE.toString()))) {
                userVersion = QuickBooksConst.USER_VERSION_NEW;
            }
        } catch (Exception e) {
            log.warn("set user version error!, msg:{}", e.getMessage());
        }
        qbSetting.setUserVersion(userVersion);
        // ERP-12503 新增字段是否同步sales receipt, 对于增量用户全部设置为关闭
        qbSetting.setSalesReceiptEnable(QuickBooksConst.QB_SYNC_SALES_RECEIPT_CLOSE);
        qbSettingMapper.insertSelective(qbSetting);
        // 将已有数据设置为过期
        qbSettingMapper.updateOldRecordByStatus(qbSetting.getId(), tokenBusinessId, createTime);
        // 将connect设置为已连接
        updateQbConnectStatusToNormal(connectId, createTime);
        ThreadPool.execute(() -> {
            deleteRedisQbListByBusinessId(tokenBusinessId);
            addSyncJobByDateRange(tokenBusinessId, qbSetting.getSyncBeginDate(), null);
        });
        return true;
    }

    private void updateQbConnectStatusToNormal(Integer connectId, long createTime) {
        MoeQbConnect updateConnect = new MoeQbConnect();
        updateConnect.setId(connectId);
        updateConnect.setConnectStatus(QuickBooksConst.CONNECT_STATUS_NORMAL);
        updateConnect.setUpdateTime(createTime);
        qbConnectMapper.updateByPrimaryKeySelective(updateConnect);
    }

    private MoeQbConnect getConnectWithChecking(Integer connectId) {
        MoeQbConnect connect = qbConnectMapper.selectByPrimaryKey(connectId);
        // 找不到connect 或者businessId对不上，或者connect状态异常，都无法create setting
        if (connect == null || !QuickBooksConst.CONNECT_STATUS_DEFAULT.equals(connect.getConnectStatus())) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_OAUTH_ERROR);
        }
        return connect;
    }

    @Transactional
    public String qbSetUp(QbSetUpVo vo) {
        // create setting in batch
        // 1. parse state to businessIds and minus dismissedBusinessIds
        // 2. setting enable begin date and min date
        // 3. set last disconnected timed
        // 4. update/create setting
        // 6. update qb connect status and redis job
        StringBuilder resultSb = new StringBuilder("connectId:").append(vo.getConnectId());
        MoeQbConnect connect = getConnectWithChecking(vo.getConnectId());
        Set<Integer> selectedBizIds = getSelectedBizIds(vo, connect);
        Map<Integer, BusinessDateTimeDTO> bizTimezoneInfo = iBusinessBusinessClient.listBusinessDateTime(
                selectedBizIds.stream().toList());
        long createTime = DateUtil.get10Timestamp();
        // 获取该business 对应的company 所设置的metadata
        var response = metadataClient.extractValues(ExtractValuesRequest.newBuilder()
                .setKeyName(QB_METADATA_GRAY_SCALES_TEST_KEY)
                .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, vo.getCompanyId())
                .build());
        // 曾经链接过的更新，否则添加(兼容旧的 taxSyncType 白名单）
        for (Integer businessId : selectedBizIds) {
            MoeQbSetting qbSetting = qbSettingMapper.selectByBusinessId(businessId, null);
            String syncBeginDate;
            if (StringUtils.hasText(vo.getSyncBeginDate())) {
                syncBeginDate = vo.getSyncBeginDate();
            } else {
                syncBeginDate = DateUtil.convertTimeZoneBySeconds(
                        createTime, bizTimezoneInfo.get(businessId).getTimezoneName());
            }
            int userVersion = QuickBooksConst.USER_VERSION_OLD;
            try {
                if (Boolean.parseBoolean(response.getValues()
                        .getOrDefault(QB_METADATA_GRAY_SCALES_TEST_KEY, Boolean.FALSE.toString()))) {
                    userVersion = QuickBooksConst.USER_VERSION_NEW;
                }
            } catch (Exception e) {
                log.warn("set user version error!, business id:{},  msg:{}", businessId, e.getMessage());
            }

            if (qbSetting != null) {
                MoeQbSetting updateSetting = new MoeQbSetting();
                updateSetting.setId(qbSetting.getId());
                updateSetting.setConnectId(vo.getConnectId());
                updateSetting.setEnableSync(QuickBooksConst.ENABLE_SYNC_OPEN);
                updateSetting.setStatus(QuickBooksConst.STATUS_NORMAL);
                updateSetting.setSyncBeginDate(syncBeginDate);
                updateSetting.setMinSyncDate(updateSetting.getSyncBeginDate());
                updateSetting.setUpdateTime(createTime);
                updateSetting.setLastDisconnectedTime(DateUtil.convertTimeZoneBySeconds(
                        qbSetting.getUpdateTime(),
                        bizTimezoneInfo.get(businessId).getTimezoneName()));
                updateSetting.setUserVersion(userVersion);
                qbSettingMapper.updateByPrimaryKeySelective(updateSetting);
                resultSb.append(System.lineSeparator())
                        .append(" update setting:")
                        .append(businessId);
            } else {
                MoeQbSetting newSetting = new MoeQbSetting();
                newSetting.setBusinessId(businessId);
                newSetting.setCompanyId(connect.getCompanyId());
                newSetting.setConnectId(vo.getConnectId());
                newSetting.setEnableSync(QuickBooksConst.ENABLE_SYNC_OPEN);
                newSetting.setStatus(QuickBooksConst.STATUS_NORMAL);
                newSetting.setSyncBeginDate(syncBeginDate);
                newSetting.setMinSyncDate(newSetting.getSyncBeginDate());
                newSetting.setUserVersion(userVersion); // 与数据库默认值相同
                newSetting.setCreateTime(createTime);
                newSetting.setUpdateTime(createTime);
                // ERP-12503 新增字段是否同步sales receipt, 对于增量用户全部设置为关闭
                newSetting.setSalesReceiptEnable(QuickBooksConst.QB_SYNC_SALES_RECEIPT_CLOSE);
                qbSettingMapper.insertSelective(newSetting);
                resultSb.append(System.lineSeparator())
                        .append(" create setting:")
                        .append(businessId);
            }
            ThreadPool.execute(() -> {
                deleteRedisQbListByBusinessId(businessId);
                addSyncJobByDateRange(
                        businessId, bizTimezoneInfo.get(businessId).getTimezoneName(), null);
            });
        }
        updateQbConnectStatusToNormal(vo.getConnectId(), createTime);
        return resultSb.toString();
    }

    private static Set<Integer> getSelectedBizIds(QbSetUpVo vo, MoeQbConnect connect) {
        if (!StringUtils.hasText(connect.getState())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "qb connect state has error.");
        }
        Set<Integer> selectedBizIds = Arrays.stream(
                        connect.getState().split(OAuth2PlatformClientFactory.STATE_DELIMITER))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        // 减去本次链接取消的 business id
        if (!CollectionUtils.isEmpty(vo.getDismissedBusinessIds())) {
            vo.getDismissedBusinessIds().forEach(selectedBizIds::remove);
        }
        if (CollectionUtils.isEmpty(selectedBizIds)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no business is selected");
        }
        return selectedBizIds;
    }

    public QBConnectReturnDto qbCreateConnect(QBCreateConnectVo createConnectVo) {
        try {
            OAuth2PlatformClient client = factory.getOAuth2PlatformClient();
            BearerTokenResponse bearerTokenResponse = client.retrieveBearerTokens(
                    createConnectVo.getAuthCode(), factory.getRedirectUri(createConnectVo.getUserVersion()));
            log.info("bearerTokenResponse:" + new Gson().toJson(bearerTokenResponse));
            Long nowTime = DateUtil.get10Timestamp();
            MoeQbConnect connect = new MoeQbConnect();
            connect.setBusinessId(createConnectVo.getBusinessId());
            connect.setState(createConnectVo.getState());
            connect.setCompanyId(Long.valueOf(createConnectVo.getCompanyId()));
            UserInfoResponse userInfo = getOauthUserInfo(bearerTokenResponse.getAccessToken());
            connect.setConnectEmail(userInfo.getEmail());
            connect.setConnectStatus(QuickBooksConst.CONNECT_STATUS_DEFAULT);
            connect.setConnectSub(userInfo.getSub());
            connect.setRealmId(createConnectVo.getRealmId());
            connect.setAccessToken(bearerTokenResponse.getAccessToken());
            connect.setRefreshToken(bearerTokenResponse.getRefreshToken());
            connect.setTokenExpiredTime(nowTime + bearerTokenResponse.getExpiresIn());
            connect.setCreateTime(nowTime);
            connect.setUpdateTime(nowTime);
            qbConnectMapper.insertSelective(connect);
            String connectCompanyName = updateQbConnectCompanyName(qbConnectMapper.selectByPrimaryKey(connect.getId()));
            QBConnectReturnDto connectReturnDto = new QBConnectReturnDto();
            connectReturnDto.setConnectId(connect.getId());
            connectReturnDto.setConnectEmail(connect.getConnectEmail());
            connectReturnDto.setConnectCompanyName(connectCompanyName);
            // parse state to business id list for frontend
            if (StringUtils.hasText(connect.getState())) {
                connectReturnDto.setSelectedBusinessIds(
                        Arrays.stream(connect.getState().split(OAuth2PlatformClientFactory.STATE_DELIMITER))
                                .map(Integer::parseInt)
                                .toList());
            }
            return connectReturnDto;
        } catch (OAuthException e) {
            throw ExceptionUtil.bizException(Code.CODE_QUICKBOOKS_OAUTH_ERROR);
        }
    }

    private String updateQbConnectCompanyName(MoeQbConnect connect) {
        QueryResult queryResult = dataServiceFactory.executeQuery(connect, "select * from companyinfo");
        if (!queryResult.getEntities().isEmpty()) {
            CompanyInfo companyInfo = (CompanyInfo) queryResult.getEntities().get(0);
            MoeQbConnect updateConnect = new MoeQbConnect();
            updateConnect.setId(connect.getId());
            updateConnect.setConnectCompanyName(companyInfo.getCompanyName());
            qbConnectMapper.updateByPrimaryKeySelective(updateConnect);
            return companyInfo.getCompanyName();
        }
        return "";
    }

    public UserInfoResponse getOauthUserInfo(String accessToken) {
        try {
            return factory.getOAuth2PlatformClient().getUserInfo(accessToken);
        } catch (OpenIdException e) {
            throw new CommonException(ResponseCodeEnum.QUICKBOOKS_OAUTH_ERROR);
        }
    }

    public Boolean updateBusinessSetting(QBSettingUpdateVo updateVo) {
        Integer businessId = updateVo.getBusinessId();
        QBBusinessSettingDto settingDto = getQbConnectSetting(businessId);
        MoeQbConnect connect = qbConnectMapper.selectByPrimaryKey(settingDto.getConnectId());

        MoeQbSetting updateSetting = new MoeQbSetting();
        MoeQbConnect updateConnect = new MoeQbConnect();
        Long updateTime = DateUtil.get10Timestamp();
        // 关闭同步
        if (updateVo.getEnableSync() != null
                && QuickBooksConst.ENABLE_SYNC_OPEN.equals(settingDto.getEnableSync())
                && QuickBooksConst.ENABLE_SYNC_CLOSE.equals(updateVo.getEnableSync())) {
            // 修改setting
            updateSetting.setEnableSync(QuickBooksConst.ENABLE_SYNC_CLOSE);
            updateSetting.setUpdateTime(updateTime);
        }
        // 修改syncBeginDate
        if (StringUtils.hasText(updateVo.getSyncBeginDate())) {
            updateSetting.setSyncBeginDate(updateVo.getSyncBeginDate());
            updateSetting.setUpdateTime(updateTime);
            if (StringUtils.hasText(settingDto.getSyncBeginDate())
                    && !settingDto.getSyncBeginDate().equals(updateVo.getSyncBeginDate())) {
                // 旧的有值，且和新传入的不同
                // 比qb更新的sync_date
                if (StringUtils.isEmpty(settingDto.getMinSyncDate())
                        || DateUtil.twoStrDateCompare(updateVo.getSyncBeginDate(), settingDto.getMinSyncDate(), null)
                                <= 0) {
                    // 将数据库内有的预约，批量增加redis队列内
                    ThreadPool.execute(() -> {
                        addSyncJobByDateRange(businessId, updateVo.getSyncBeginDate(), settingDto.getMinSyncDate());
                    });
                }
            }
        }
        // 修改账本信息
        if (StringUtils.hasText(updateVo.getAccountName()) && updateVo.getAccountId() != null) {
            updateConnect.setAccountId(updateVo.getAccountId());
            updateConnect.setAccountName(updateVo.getAccountName());
            updateConnect.setUpdateTime(updateTime);
        }
        if (updateConnect.getUpdateTime() != null) {
            updateConnect.setId(connect.getId());
            qbConnectMapper.updateByPrimaryKeySelective(updateConnect);
        }
        if (updateSetting.getUpdateTime() != null) {
            updateSetting.setId(settingDto.getSetttingId());
            qbSettingMapper.updateByPrimaryKeySelective(updateSetting);
        }
        return true;
    }

    public String refreshMetadata(Integer businessId, Byte mode) {
        log.info("<refresh metadata> refresh metadata, operator businessId:{}", businessId);
        // 这里拿到所有 status == 1, 即qb状态正常的数据
        var allSetting = qbSettingMapper.selectAllStatusNormal();
        var keyModel = metadataClient.getKey(GetKeyRequest.newBuilder()
                .setName(QB_METADATA_GRAY_SCALES_TEST_KEY)
                .build());
        var optionKeyModel = metadataClient.getKey(
                GetKeyRequest.newBuilder().setName(QB_METADATA_USER_OPTIONS_KEY).build());
        var optionKeyId = optionKeyModel.getKey().getId();
        var grayScalesKeyId = keyModel.getKey().getId();
        List<MoeQbSetting> settingBatchUpdateList = new ArrayList<>();
        // 记录刷新的用户数量
        AtomicInteger count = new AtomicInteger();
        allSetting.forEach(setting -> {
            var grayMetadata = BooleanUtils.FALSE;
            var settingBusinessId = setting.getBusinessId();
            var connectSetting = getQbConnectSetting(settingBusinessId);
            // qb sync开启, 链接状态正常才会进行刷数据的操作
            if (connectSetting == null
                    || !QuickBooksConst.ENABLE_SYNC_OPEN.equals(connectSetting.getEnableSync())
                    || !QuickBooksConst.CONNECT_STATUS_NORMAL.equals(connectSetting.getConnectStatus())) {
                if (Objects.isNull(connectSetting)) {
                    log.info(
                            "<refresh metadata> businessId:{} connect setting not found, no need to refresh metadata",
                            settingBusinessId);
                    return;
                }
                log.info(
                        "<refresh metadata> businessId:{} connect status error, no need to refresh metadata, enable status = {}, conncet status = {}",
                        settingBusinessId,
                        connectSetting.getEnableSync(),
                        connectSetting.getConnectStatus());
                return;
            }
            var companyId = setting.getCompanyId();
            if (companyId == 0) {
                log.warn("<refresh metadata> company id = 0 in qb setting table, business id:{}", businessId);
                companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
            }

            if (QuickBooksConst.QB_REFRESH_METADATA_MODE_NORMAL.equals(mode)) {
                if (QuickBooksConst.USER_VERSION_NEW.equals(setting.getUserVersion())) {
                    log.info(
                            "<refresh metadata> businessId:{} companyId:{} user version is new, no need to refresh metadata",
                            settingBusinessId,
                            companyId);
                    return;
                }
                var optionMetadata = getQBMetadataValue(QB_METADATA_USER_OPTIONS_KEY, companyId);
                if (StringUtils.hasText(optionMetadata)) optionMetadata = optionMetadata.replace("\"", "");
                // refresh metadata rules:
                // 如果选择了手动自动, 证明希望开启新模式, 则灰度为true
                // refused属于保持现状, 不需要打开灰度开关
                // 其余情况保持灰度为false
                if (optionMetadata.equalsIgnoreCase(QuickBooksConst.QB_OPTION_MANUAL)
                        || optionMetadata.equalsIgnoreCase(QuickBooksConst.QB_OPTION_MANUAL_INFORMED)
                        || optionMetadata.equalsIgnoreCase(QuickBooksConst.QB_OPTION_AUTO)) {
                    log.info(
                            "<refresh metadata> businessId:{} companyId:{}, option:{} open gray scales",
                            settingBusinessId,
                            companyId,
                            optionMetadata);
                    grayMetadata = BooleanUtils.TRUE;
                } else if (optionMetadata.equalsIgnoreCase(QuickBooksConst.QB_OPTION_REFUSED)) {
                    // refused属于保持现状, 灰度开关关闭, 但是记录一个日志以便追踪
                    log.info(
                            "<refresh metadata> businessId:{} companyId:{}, option refused, no need to refresh metadata",
                            settingBusinessId,
                            companyId);
                } else {
                    // 如果出现空值那就记录一下, option状态记录成refused
                    log.warn(
                            "<refresh metadata> businessId:{} companyId:{}, option metadata empty, option:{}",
                            settingBusinessId,
                            companyId,
                            optionMetadata);
                    var refreshMetadataResp = metadataClient.updateValue(UpdateValueRequest.newBuilder()
                            .setKeyId(optionKeyId)
                            .setValue(String.format("\"%s\"", QuickBooksConst.QB_OPTION_REFUSED))
                            .setOwnerId(companyId)
                            .setInternalOperatorId("system")
                            .build());
                }
            } else if (QuickBooksConst.QB_REFRESH_METADATA_MODE_INIT.equals(mode)) {
                // 初始化模式下, 检查用户是否有灰度值, 如果有灰度值取用户的灰度值
                var grayScalesValue = getQBMetadataValue(QB_METADATA_GRAY_SCALES_TEST_KEY, companyId);
                if (StringUtils.hasText(grayScalesValue) && Boolean.parseBoolean(grayScalesValue)) {
                    grayMetadata = grayScalesValue;
                }
                log.info("<refresh metadata> companyId:{} init user version:{}", companyId, grayMetadata);
            } else if (QuickBooksConst.QB_REFRESH_METADATA_MODE_ROLLBACK.equals(mode)) {
                // 如果是回滚的话, 直接关闭灰度开关, 在迁移逻辑中未开启灰度开关会让所有用户走旧逻辑
                var grayScalesValue = getQBMetadataValue(QB_METADATA_GRAY_SCALES_TEST_KEY, companyId);
                // 灰度开关为true 才需要回滚
                if (Boolean.parseBoolean(grayScalesValue)) {
                    log.info(
                            "<refresh metadata> businessId:{} companyId:{} rollback user version",
                            businessId,
                            companyId);
                    // 回滚所有用户的 user version = 0
                    setting.setUserVersion(QuickBooksConst.USER_VERSION_OLD);
                    settingBatchUpdateList.add(setting);
                }
            }
            try {
                // refresh metadata
                var refreshMetadataResp = metadataClient.updateValue(UpdateValueRequest.newBuilder()
                        .setKeyId(grayScalesKeyId)
                        .setValue(grayMetadata)
                        .setOwnerId(companyId)
                        .setInternalOperatorId("system")
                        .build());
                count.getAndIncrement();
            } catch (Exception e) {
                log.error(
                        "<refresh metadata> refresh metadata error, businessId:{}, companyId:{}, msg:{}",
                        settingBusinessId,
                        companyId,
                        e.getMessage());
            }
        });
        log.info(
                "<refresh metadata> refresh metadata success, count:{}, operator businessId:{}",
                count.get(),
                businessId);
        if (!settingBatchUpdateList.isEmpty()) qbSettingMapper.batchUpdateByPrimaryKeySelective(settingBatchUpdateList);
        return "refresh success!, count: " + count.get();
    }

    /**
     * 方法作用于使用 qb sync task 1.0的用户在使用qb task 2.0后希望迁移数据的情况
     * e.g. 某用户设置同步时间为4.1开始, 5.1 转到qb 2.0后希望迁移4.1-5.1的数据, 此时可使用本方法
     * 为了避免一些问题, 仅限已使用qb 2.0即setting中user version == 1 的用户使用
     * @param businessId 待迁移的商家id
     * @param endDate 结束日期参数，用于确定数据迁移的截止日期，格式为"yyyy-MM-dd"。
     *                这个日期与从QuickBooks同步设置中获取的起始日期（qbSettingDate）一起，定义了需要迁移的数据的时间范围。
     *                例如，如果qbSettingDate为'2023-01-02'，且endDate传参为'2023-03-02'
     *                则本方法将迁移这段时间内即['2023-01-02', '2023-03-02']的所有数据。
     *                如果endDate为null，则迁移qbSettingDate到当前时间的所有数据['2023-01-02', now()]
     * @return 迁移操作的结果，通常为操作成功与否的状态消息。
     */
    public String migrationQBDataByBusinessId(Integer businessId, String endDate) {
        var setting = getQbConnectSetting(businessId);
        // 旧版本不支持直接迁移
        if (QuickBooksConst.USER_VERSION_OLD.equals(setting.getUserVersion())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format("business id %s error, only support user version: 1", businessId));
        }
        // 一个business 只能执行一次迁移, 这里和下面两个if 可以保证商家之前没有过迁移任务与正在执行中的任务
        var migrationTasks =
                qbTaskMapper.selectTaskByBusinessIdAndType(businessId, QuickBooksConst.QB_SYNC_TASK_TYPE_MIGRATION);
        if (Objects.nonNull(migrationTasks) && !migrationTasks.isEmpty()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "business id %s has already executed the migration task and cannot be executed again.",
                            businessId));
        }
        // 这里检查是否有执行中的非迁移任务(即正常的同步任务, 业务上保证同一时间只有一个任务状态是执行中), 如果有的话等待任务完成
        // 其实可以直接结束现有任务, 但是考虑后还是觉得等待比较好, 避免数据丢失
        // 这里表现为抛出异常, 需要等待一段时间后重新调用
        MoeQbTask qbTask = qbTaskMapper.selectBusinessIdTask(businessId);
        if (Objects.nonNull(qbTask)) {
            // 已有task在执行就不重复执行了
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "business id %s has already task, task id: %s, plz wait the sync task to finish",
                            businessId, qbTask.getId()));
        }
        log.info("<migration> businessId:{} start migration...", businessId);
        var businessInfo = getBusinessInfo(businessId);
        // 创建新的task执行记录
        MoeQbTask qbNewTask = new MoeQbTask();
        qbNewTask.setBusinessId(businessId);
        qbNewTask.setCompanyId(Long.valueOf(businessInfo.getCompanyId()));
        qbNewTask.setTaskStatus(QuickBooksConst.TASK_STATUS_START);
        Long startTime = DateUtil.get10Timestamp();
        qbNewTask.setCreateTime(startTime);
        qbNewTask.setUpdateTime(startTime);
        // businessId taskStatus completeTime有唯一索引，重复创建会报错
        qbTaskMapper.insertSelective(qbNewTask);

        // 标记用户状态为迁移
        quickBooksSyncService.setMigrationByBusinessId(businessId);
        // 把setting设置的区间到endDate的数据都放到redis里, 后续执行migration
        // 这里不能异步，异步速度没有主线程执行快，会捞不到数据, 只能牺牲一点时间了 Orz
        deleteRedisQbListByBusinessId(businessId);
        addSyncJobByDateRange(businessId, setting.getSyncBeginDate(), endDate);
        // 异步执行迁移任务
        ThreadPool.execute(() -> {
            log.info(String.format(" <migration> businessId:%s start new migration background task", businessId));
            // 复用sync task, 但在主逻辑中对migration 做一些独立的处理, 在迁移标记==true时, 不影响正常的同步逻辑
            beginOneBusinessQbSyncTask(businessId, qbTaskMapper.selectByPrimaryKey(qbNewTask.getId()), setting);
            // 迁移结束，删除key
            quickBooksSyncService.delRedisQBMigrationKey(businessId);
            log.info("<migration> businessId:{} migration success", businessId);
        });

        return "migration success";
    }

    private MoeBusinessDto getBusinessInfo(Integer businessId) {
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        return iBusinessBusinessClient.getBusinessInfo(businessIdParams);
    }

    public ListQuickBookSettingDTO listQuickBookSetting(ListQuickBookSettingParams params) {
        var pagination = Optional.ofNullable(params.getPagination()).orElse(Pagination.DEFAULT);
        return ListQuickBookSettingDTO.builder()
                .views(QuickBookMapper.INSTANCE.listBoToDto(qbSettingMapper.listByParams(
                        params,
                        CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()),
                        pagination.pageSize())))
                .total(qbSettingMapper.countByParams(params))
                .build();
    }

    public Integer updateQuickBookSetting(UpdateQuickBookSettingParams params) {
        var setting = qbSettingMapper.selectByBusinessId(params.getBusinessId(), QuickBooksConst.STATUS_NORMAL);
        Optional.ofNullable(params.getUserVersion()).ifPresent(setting::setUserVersion);
        Optional.ofNullable(params.getTaxSyncType()).ifPresent(setting::setTaxSyncType);
        Optional.ofNullable(params.getReceiptStatus()).ifPresent(setting::setSalesReceiptEnable);
        return qbSettingMapper.updateByPrimaryKeySelective(setting);
    }
}
