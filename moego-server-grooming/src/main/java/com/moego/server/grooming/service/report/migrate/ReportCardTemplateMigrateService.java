package com.moego.server.grooming.service.report.migrate;

import com.github.pagehelper.PageHelper;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateTemplatesRequest;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateTemplatesResponse;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.convert.ReportCardMigrateConverter;
import com.moego.server.grooming.mapper.MoeGroomingReportTemplateMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample;
import com.moego.server.grooming.service.report.migrate.validation.ReportCardMigrateValidationService;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationError;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationResult;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationSummary;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardDataFixResultDTO;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Report Card 模板迁移服务
 * 负责将 moe_grooming_report_template 数据迁移到 fulfillment_report_template
 * 核心原则：
 * 1. 零模式修改：不修改新表结构
 * 2. 引用完整性：保持数据间关联关系
 * 3. 注意表与表之间的id关联，需要直接使用新表的id，在迁移过程中进行新旧表id的替换
 * 4. 数据量较大的表迁移需考虑性能，例如分片处理
 * 5. 通过调用fulfillment服务接口进行数据操作
 * 6. 可追溯性：完整记录迁移过程
 * 7. 可回滚性：确保问题发生时可快速恢复
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardTemplateMigrateService {

    private final MoeGroomingReportTemplateMapper sourceMapper;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;
    private final ReportCardMigrateValidationService validationService;

    /**
     * 迁移模板数据
     */
    public ReportCardMigrateResultDTO migrate(Integer businessId) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "template_" + System.currentTimeMillis();

        log.info("开始迁移模板数据，taskId: {}, businessId: {}", taskId, businessId);

        try {
            // 1. 统计总记录数
            long totalCount = getTotalTemplateCount(businessId);
            log.info("模板数据总数: {} 条，开始分页迁移", totalCount);

            if (totalCount == 0) {
                return ReportCardMigrateUtils.buildSuccessResult(
                        taskId,
                        startTime,
                        ReportCardMigrateConfig.TableNames.TEMPLATE,
                        ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                        ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                        0,
                        0,
                        0);
            }

            // 2. 分页查询并处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                // 分页查询数据，避免一次性加载所有数据到内存
                List<MoeGroomingReportTemplate> batch = getTemplateBatch(businessId, offset, batchSize);

                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }

                log.info("处理第 {} 批数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条", batchNumber, offset, batchSize, batch.size());

                // 处理当前批次的数据
                BatchResult batchResult = processBatchTemplates(batch);
                migratedCount += batchResult.migratedCount;
                skippedCount += batchResult.skippedCount;
                failedCount += batchResult.failedCount;

                // 批量校验：每迁移一批数据后立即校验该批次
                if (batchResult.migratedCount > 0) {
                    performBatchValidation(batch, batchNumber);
                }

                // 输出进度信息
                long processedCount = migratedCount + skippedCount + failedCount;
                int progressPercent = ReportCardMigrateUtils.calculateProgressPercent(processedCount, totalCount);
                log.info(
                        "批次 {} 处理完成，当前进度: {} ({}/{}), 成功: {}, 跳过: {}, 失败: {}",
                        batchNumber,
                        progressPercent + "%",
                        processedCount,
                        totalCount,
                        batchResult.migratedCount,
                        batchResult.skippedCount,
                        batchResult.failedCount);

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 最后一批，退出循环
                if (batch.size() < batchSize) {
                    break;
                }
            }

            log.info("模板数据迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}", totalCount, migratedCount, skippedCount, failedCount);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId,
                    startTime,
                    ReportCardMigrateConfig.TableNames.TEMPLATE,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_TEMPLATE,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_TEMPLATE,
                    totalCount,
                    migratedCount,
                    skippedCount);
        } catch (Exception e) {
            log.error("模板数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 获取模板总数量
     * 建议在 business_id 字段上添加索引以提高查询性能
     */
    private long getTotalTemplateCount(Integer businessId) {
        MoeGroomingReportTemplateExample countExample = new MoeGroomingReportTemplateExample();
        MoeGroomingReportTemplateExample.Criteria countCriteria = countExample.createCriteria();

        if (businessId != null) {
            countCriteria.andBusinessIdEqualTo(businessId);
        }

        return sourceMapper.countByExample(countExample);
    }

    /**
     * 分页查询模板数据
     */
    private List<MoeGroomingReportTemplate> getTemplateBatch(Integer businessId, int offset, int limit) {
        // 计算页码（PageHelper 使用 1 基础的页码）
        int pageNum = (offset / limit) + 1;

        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(pageNum, limit, "id ASC");

        try {
            MoeGroomingReportTemplateExample example = new MoeGroomingReportTemplateExample();
            MoeGroomingReportTemplateExample.Criteria criteria = example.createCriteria();

            if (businessId != null) {
                criteria.andBusinessIdEqualTo(businessId);
            }

            // 设置排序以确保分页结果的一致性
            example.setOrderByClause("id ASC");

            // 执行分页查询，PageHelper 会自动将结果包装为 Page 对象
            return sourceMapper.selectByExample(example);
        } finally {
            // 清理 PageHelper 的线程本地变量
            PageHelper.clearPage();
        }
    }

    /**
     * 处理单个批次的模板数据
     */
    private BatchResult processBatchTemplates(List<MoeGroomingReportTemplate> batch) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        try {
            // 批量检查是否已存在
            List<FulfillmentReportTemplateUniqueKey> uniqueKeys = new ArrayList<>();
            for (MoeGroomingReportTemplate template : batch) {
                uniqueKeys.add(FulfillmentReportTemplateUniqueKey.newBuilder()
                        .setBusinessId(template.getBusinessId().longValue())
                        .setCompanyId(template.getCompanyId())
                        .setCareType(CareCategory.GROOMING)
                        .build());
            }

            GetTemplatesByUniqueKeysResponse existingTemplates =
                    fulfillmentServiceClient.getTemplatesByUniqueKeys(GetTemplatesByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 过滤出需要迁移的数据
            List<MoeGroomingReportTemplate> templatesToMigrate = new ArrayList<>();
            for (MoeGroomingReportTemplate template : batch) {
                boolean exists = existingTemplates.getTemplatesList().stream()
                        .anyMatch(existing -> existing.getBusinessId() == template.getBusinessId()
                                && existing.getCompanyId() == template.getCompanyId());

                if (exists) {
                    log.info(
                            "模板已存在，跳过迁移: businessId={}, companyId={}, id={}",
                            template.getBusinessId(),
                            template.getCompanyId(),
                            template.getId());
                    skippedCount++;
                } else {
                    templatesToMigrate.add(template);
                }
            }

            if (!templatesToMigrate.isEmpty()) {
                // 转换数据
                List<FulfillmentReportTemplateSync> convertedTemplates =
                        ReportCardMigrateConverter.INSTANCE.convertTemplates(templatesToMigrate);

                if (!convertedTemplates.isEmpty()) {
                    // 批量迁移
                    BatchMigrateTemplatesRequest request = BatchMigrateTemplatesRequest.newBuilder()
                            .addAllTemplates(convertedTemplates)
                            .build();

                    BatchMigrateTemplatesResponse response = fulfillmentServiceClient.batchMigrateTemplates(request);

                    migratedCount += response.getSuccessCount();
                    skippedCount += response.getSkippedCount();
                    failedCount += response.getFailedCount();

                    log.info(
                            "批量迁移完成: 成功={}, 跳过={}, 失败={}",
                            response.getSuccessCount(),
                            response.getSkippedCount(),
                            response.getFailedCount());
                }
            }

        } catch (Exception e) {
            log.error("批量迁移模板失败", e);
            failedCount += batch.size();
        }

        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    /**
     * 执行批量校验
     * 在每个批次迁移完成后立即调用，快速发现问题
     */
    private void performBatchValidation(List<MoeGroomingReportTemplate> batch, int batchNumber) {
        try {
            log.info("开始批量校验，批次: {}, 数量: {}", batchNumber, batch.size());

            ValidationResult validationResult = validationService.validateTemplateBatch(batch, batchNumber);

            if (validationResult.isSuccess()) {
                log.info("批量校验通过: {}", validationResult.getSummary());
            } else {
                log.warn("批量校验发现问题: {}", validationResult.getSummary());

                // 记录详细错误信息
                for (ValidationError error : validationResult.getErrors()) {
                    log.warn("校验错误: {}", error.getFullDescription());
                }

                // 判断错误严重程度
                handleValidationErrors(validationResult.getErrors(), batchNumber);
            }

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
        }
    }

    /**
     * 处理校验错误
     */
    private void handleValidationErrors(List<ValidationError> errors, int batchNumber) {
        int criticalErrors = 0;
        int warningErrors = 0;

        for (ValidationError error : errors) {
            switch (error.getErrorType()) {
                case DATA_MISSING:
                case SYSTEM_ERROR:
                    criticalErrors++;
                    break;
                case DATA_INTEGRITY:
                case BUSINESS_LOGIC:
                    criticalErrors++;
                    break;
                case DATA_CONSISTENCY:
                    warningErrors++;
                    break;
            }
        }

        if (criticalErrors > 0) {
            log.error("批次 {} 发现 {} 个严重错误，{} 个警告错误", batchNumber, criticalErrors, warningErrors);

            double errorRate = (double) criticalErrors / errors.size();
            if (errorRate > 0.1) { // 10% 错误率阈值
                log.error("批次 {} 错误率为: {}，大于10%", batchNumber, errorRate * 100);
            }
        } else if (warningErrors > 0) {
            log.warn("批次 {} 发现 {} 个警告错误", batchNumber, warningErrors);
        }
    }

    /**
     * 将校验信息添加到迁移结果中
     */
    private void addValidationInfoToResult(ReportCardMigrateResultDTO result, ValidationSummary validationSummary) {
        if (validationSummary == null) {
            return;
        }

        // 构建校验信息
        StringBuilder validationInfo = new StringBuilder();
        validationInfo.append("\n\n=== 模板数据校验结果 ===\n");
        validationInfo.append(validationSummary.getSummary());

        if (validationSummary.hasErrors()) {
            validationInfo.append("\n\n=== 校验错误详情 ===\n");
            for (ValidationError error : validationSummary.getErrors()) {
                validationInfo.append("- ").append(error.getFullDescription()).append("\n");
            }
        }

        if (validationSummary.getSamplingValidation() != null
                && validationSummary.getSamplingValidation().hasErrors()) {
            validationInfo.append("\n=== 抽样校验错误 ===\n");
            for (ValidationError error :
                    validationSummary.getSamplingValidation().getErrors()) {
                validationInfo.append("- ").append(error.getFullDescription()).append("\n");
            }
        }

        // 如果校验失败，更新结果状态和错误信息
        if (!validationSummary.isSuccess()) {
            result.setSuccess(false);
            String originalError = result.getErrorMessage();
            String enhancedError =
                    (originalError != null ? originalError : "模板数据迁移完成但校验发现问题") + validationInfo.toString();
            result.setErrorMessage(enhancedError);
        } else {
            // 校验成功时，也可以在错误信息中添加校验摘要（作为信息性内容）
            String originalError = result.getErrorMessage();
            if (originalError == null || originalError.isEmpty()) {
                result.setErrorMessage("迁移成功" + validationInfo.toString());
            } else {
                result.setErrorMessage(originalError + validationInfo.toString());
            }
        }
    }

    /**
     * 批次处理结果内部类
     */
    private record BatchResult(long migratedCount, long skippedCount, long failedCount) {}
}
