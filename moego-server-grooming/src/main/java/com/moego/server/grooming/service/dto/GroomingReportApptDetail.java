package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 5/17/21 10:20 AM
 */
@Data
public class GroomingReportApptDetail {

    private List<ReportWebApptPetDetail> petDetails;
    private List<EvaluationServiceDetailDTO> evaluationDetails;
    private List<AppointmentNote> appointmentNotes;
    private List<MoeGroomingInvoiceItem> invoiceItems;

    // invoice list:grooming 存在对应多个invocie 的情况（如在取消预约中，可能还存在noshowfee的invoice）
    private List<GroomingReportApptInvoice> invoices;

    /*
    grooming 基本信息
     */
    private Integer id;

    private String orderId;

    private Integer businessId;

    private Integer customerId;

    private String appointmentDate;

    private Integer appointmentStartTime;

    private Integer appointmentEndTime;

    private Byte isWaitingList;

    private Integer moveWaitingBy;

    private Long confirmedTime;

    private Long checkInTime;

    private Long checkOutTime;

    private Long canceledTime;

    private Byte status;

    private Byte bookOnlineStatus;

    private Integer repeatId;

    private Byte isPaid;

    private String colorCode;

    private Byte noShow;

    private Byte isPustNotification;

    private Byte cancelByType;

    private Integer cancelBy;

    private Byte confirmByType;

    private Integer confirmBy;

    private Integer createdById;

    private Byte outOfArea;

    private Long createTime;

    private Long updateTime;

    private Integer source;

    private Integer isBlock;

    private Integer isDeprecate;

    private BigDecimal noShowFee;

    private String oldAppointmentDate;

    private Integer oldAppointmentStartTime;

    private Integer oldAppointmentEndTime;

    // 填充 appointment service 时，完成计算填充
    private BigDecimal totalServicePrice;
    private BigDecimal totalAddOnsPrice;
}
