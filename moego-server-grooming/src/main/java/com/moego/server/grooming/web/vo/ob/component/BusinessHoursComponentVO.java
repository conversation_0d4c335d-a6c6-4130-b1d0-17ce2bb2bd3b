package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.business.dto.BusinessClosedDateDTO;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Storefront business working hours component
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BusinessHoursComponentVO extends BaseComponentVO {

    @Schema(description = "Business set working hours")
    private BusinessWorkingHourDayDetailDTO workingHours;

    @Schema(description = "Business set closed date")
    private List<BusinessClosedDateDTO> closedDateList;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.BUSINESS_HOURS.getComponent();
    }
}
