package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface AbandonedUserMapper {
    AbandonedUserMapper INSTANCE = Mappers.getMapper(AbandonedUserMapper.class);

    AppointmentDTO entity2DTO(MoeGroomingAppointment entity);
}
