package com.moego.server.grooming.service.ob;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerOnlineBookingClient;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.customer.params.OBLogoutParams;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/6
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBClientService {

    private final ICustomerCustomerClient iCustomerCustomerClient;

    private final ICustomerOnlineBookingClient iCustomerOnlineBookingClient;

    private final CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    public boolean validCustomerBlocked(String obName, Long companyId, Integer businessId, Integer customerId) {
        if (Objects.isNull(businessId) || Objects.isNull(customerId)) {
            return false;
        }
        CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
        customerInfoIdParams.setBusinessId(businessId);
        customerInfoIdParams.setCustomerId(customerId);
        CustomerInfoDto customerInfoDto =
                iCustomerCustomerClient.getCustomerWithDeletedHasBusinessId(customerInfoIdParams);
        if (Objects.isNull(customerInfoDto) || Objects.equals(customerInfoDto.getStatus(), CommonConstant.DELETED)) {
            // logout
            var context = AuthContext.get();
            if (Objects.nonNull(context.sessionId())) {
                var logoutParams = new OBLogoutParams(context.sessionId(), context.accountId(), obName);
                iCustomerOnlineBookingClient.logoutV2(logoutParams);
            }
            throw new CommonException(ResponseCodeEnum.CUSTOMER_NOT_FOUND_FOR_OB);
        }

        boolean isBlock = checkCustomerIsBlockedByGrooming(companyId, customerId);

        if (isBlock) {
            log.info("OB 3.0 the customerId: [{}] is blocked", customerId);
        }
        return isBlock;
    }

    public boolean checkCustomerIsBlockedByGrooming(Number companyId, Number customerId) {
        var listBlockCustomerResponse =
                customerAvailabilityServiceBlockingStub.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                        .setCompanyId(companyId.longValue())
                        .addServiceItemTypes(ServiceItemType.GROOMING)
                        .addCustomerIds(customerId.longValue())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(10)
                                .build())
                        .build());
        return listBlockCustomerResponse.getCustomerBlockInfosList().stream()
                .anyMatch(
                        customerBlockInfo -> Objects.equals(customerBlockInfo.getCustomerId(), customerId.longValue()));
    }

    public boolean checkCustomerIsBlocked(Number companyId, Number customerId) {
        var listBlockCustomerResponse =
                customerAvailabilityServiceBlockingStub.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                        .setCompanyId(companyId.longValue())
                        .addAllServiceItemTypes(
                                List.of(ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                        .addCustomerIds(customerId.longValue())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(10)
                                .build())
                        .build());
        return listBlockCustomerResponse.getCustomerBlockInfosList().stream()
                .anyMatch(
                        customerBlockInfo -> Objects.equals(customerBlockInfo.getCustomerId(), customerId.longValue()));
    }
}
