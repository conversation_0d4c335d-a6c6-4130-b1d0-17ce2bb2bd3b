package com.moego.server.grooming.service.client;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.DepositService;
import com.moego.server.grooming.utils.OBSettingUtil;
import com.moego.server.grooming.web.vo.client.AddressVO;
import com.moego.server.grooming.web.vo.client.ApptDetailVO;
import com.moego.server.grooming.web.vo.client.BusinessInfoVO;
import com.moego.server.grooming.web.vo.client.BusinessTwilioVO;
import com.moego.server.grooming.web.vo.client.ClientApptVO;
import com.moego.server.grooming.web.vo.client.ClientInfoVO;
import com.moego.server.grooming.web.vo.client.CreditCardDetailVO;
import com.moego.server.grooming.web.vo.client.OBConfigVO;
import com.moego.server.grooming.web.vo.client.OBDepositVO;
import com.moego.server.grooming.web.vo.client.OBProfileVO;
import com.moego.server.grooming.web.vo.client.PetDetailVO;
import com.moego.server.grooming.web.vo.client.StaffDetailVO;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.server.payment.dto.PaymentDTO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/12/12
 */
public class ClientApptUtils {

    public static BusinessDateTimeDTO getBusinessLocalDateTime(OBBusinessInfoDTO businessInfoDTO) {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(businessInfoDTO.getTimezoneName()));
        return new BusinessDateTimeDTO()
                .setBusinessId(businessInfoDTO.getId())
                .setCurrentDate(localDateTime.toLocalDate().toString())
                .setCurrentMinutes((int) localDateTime.getLong(ChronoField.MINUTE_OF_DAY))
                .setLocalDateTime(localDateTime)
                .setTimezoneName(businessInfoDTO.getTimezoneName());
    }

    public static Byte getApptType(MoeGroomingAppointment appointment, OBBusinessInfoDTO businessInfoDTO) {
        BusinessDateTimeDTO dateTimeDTO = getBusinessLocalDateTime(businessInfoDTO);
        if (isRequests(appointment)) {
            return ClientApptConst.APPT_TYPE_REQUESTS;
        } else if (isUpcoming(appointment, dateTimeDTO)) {
            return ClientApptConst.APPT_TYPE_UPCOMING;
        } else if (isHistory(appointment, dateTimeDTO)) {
            return ClientApptConst.APPT_TYPE_HISTORY;
        }
        throw new CommonException(ResponseCodeEnum.APPOINTMENT_INVALID_STATUS);
    }

    public static boolean isRequests(MoeGroomingAppointment appointment) {
        boolean obCondition = Objects.equals(appointment.getSource(), GroomingAppointmentEnum.SOURCE_OB)
                && Objects.equals(appointment.getCreatedById(), ClientApptConst.CREATED_BY_OB);
        boolean requestsCondition = Objects.equals(
                        appointment.getStatus(), AppointmentStatusEnum.UNCONFIRMED.getValue())
                && Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                && Objects.equals(appointment.getIsDeprecate(), GroomingAppointmentEnum.IS_DEPRECATE_FALSE);
        return obCondition && requestsCondition;
    }

    public static boolean isUpcoming(MoeGroomingAppointment appointment, BusinessDateTimeDTO dateTimeDTO) {
        boolean upcomingCondition =
                Objects.equals(appointment.getIsWaitingList(), GroomingAppointmentEnum.NOT_WAITING_LIST)
                        && Objects.equals(appointment.getIsDeprecate(), GroomingAppointmentEnum.IS_DEPRECATE_FALSE)
                        && Objects.equals(appointment.getIsBlock().byteValue(), ClientApptConst.IS_BLOCK_FALSE);
        LocalDate apptDate =
                LocalDate.parse(appointment.getAppointmentDate(), DateTimeFormatter.ofPattern(DateUtil.STANDARD_DATE));
        boolean afterCurrent = apptDate.isAfter(dateTimeDTO.getLocalDateTime().toLocalDate())
                || (apptDate.isEqual(dateTimeDTO.getLocalDateTime().toLocalDate())
                        && appointment.getAppointmentStartTime() > dateTimeDTO.getCurrentMinutes());
        return upcomingCondition && afterCurrent;
    }

    public static boolean isHistory(MoeGroomingAppointment appointment, BusinessDateTimeDTO dateTimeDTO) {
        boolean historyCondition = Objects.equals(
                        appointment.getIsDeprecate(), GroomingAppointmentEnum.IS_DEPRECATE_FALSE)
                && Objects.equals(appointment.getIsBlock().byteValue(), ClientApptConst.IS_BLOCK_FALSE)
                && Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB)
                && Objects.equals(appointment.getIsWaitingList(), GroomingAppointmentEnum.NOT_WAITING_LIST)
                && AppointmentStatusSet.ACTIVE_STATUS_SET.contains(
                        AppointmentStatusEnum.values()[appointment.getStatus()]);
        LocalDate apptDate =
                LocalDate.parse(appointment.getAppointmentDate(), DateTimeFormatter.ofPattern(DateUtil.STANDARD_DATE));
        boolean beforeCurrent = apptDate.isBefore(dateTimeDTO.getLocalDateTime().toLocalDate())
                || (apptDate.isEqual(dateTimeDTO.getLocalDateTime().toLocalDate())
                        && appointment.getAppointmentStartTime() < dateTimeDTO.getCurrentMinutes());
        return historyCondition && beforeCurrent;
    }

    public static Map<Integer, BusinessDateTimeDTO> buildBusinessDateTime(Map<Integer, MoeBusinessDto> businessDtoMap) {
        LocalDateTime localDateTime = LocalDateTime.now();
        return businessDtoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            MoeBusinessDto businessDto = entry.getValue();
            LocalDateTime businessDateTime = localDateTime
                    .atZone(OffsetDateTime.now().getOffset())
                    .withZoneSameInstant(ZoneId.of(businessDto.getTimezoneName()))
                    .toLocalDateTime();
            BusinessDateTimeDTO dateTimeDTO = new BusinessDateTimeDTO();
            dateTimeDTO.setBusinessId(businessDto.getId());
            dateTimeDTO.setLocalDateTime(businessDateTime);
            dateTimeDTO.setCurrentDate(businessDateTime.toLocalDate().toString());
            dateTimeDTO.setCurrentMinutes((int) businessDateTime.getLong(ChronoField.MINUTE_OF_DAY));
            dateTimeDTO.setTimezoneName(businessDto.getTimezoneName());
            return dateTimeDTO;
        }));
    }

    public static ClientInfoVO convert(MoeBusinessCustomerDTO customerDTO) {
        return new ClientInfoVO()
                .setIsBlockMessage(Objects.equals(customerDTO.getIsBlockMessage(), BooleanEnum.VALUE_TRUE));
    }

    public static AddressVO convert(CustomerAddressDto addressDto) {
        if (Objects.isNull(addressDto)) {
            return null;
        }
        return new AddressVO()
                .setAddress1(addressDto.getAddress1())
                .setAddress2(addressDto.getAddress2())
                .setCity(addressDto.getCity())
                .setCountry(addressDto.getCountry())
                .setState(addressDto.getState())
                .setLat(addressDto.getLat())
                .setLng(addressDto.getLng())
                .setZipcode(addressDto.getZipcode());
    }

    public static OBConfigVO convert(MoeBusinessBookOnline businessBookOnline, OBBusinessInfoDTO businessInfoDTO) {
        if (Objects.isNull(businessBookOnline)) {
            return null;
        }
        return new OBConfigVO()
                .setEnableOB(businessBookOnline.getIsEnable())
                .setFarthestDay(OBSettingUtil.calculateBookingRangeEndDays(businessBookOnline))
                .setQuerySmartScheduling(
                        Objects.equals(businessInfoDTO.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_MOBILE)
                                && Objects.equals(businessBookOnline.getSmartScheduleEnable(), CommonConstant.ENABLE))
                .setAvailableTimeType(businessBookOnline.getAvailableTimeType())
                .setTimeslotFormat(businessBookOnline.getTimeslotFormat())
                .setTimeslotMins(businessBookOnline.getTimeslotMins())
                .setBySlotTimeslotFormat(businessBookOnline.getBySlotTimeslotFormat())
                .setBySlotTimeslotMins(businessBookOnline.getBySlotTimeslotMins())
                .setBookOnlineName(businessBookOnline.getBookOnlineName())
                .setUseVersion(businessBookOnline.getUseVersion())
                .setArrivalWindowAfterMin(businessBookOnline.getArrivalWindowAfterMin())
                .setArrivalWindowBeforeMin(businessBookOnline.getArrivalWindowBeforeMin())
                .setBookingRangeStartOffset(businessBookOnline.getBookingRangeStartOffset())
                .setBookingRangeEndType(businessBookOnline.getBookingRangeEndType())
                .setBookingRangeEndOffset(businessBookOnline.getBookingRangeEndOffset())
                .setBookingRangeEndDate(businessBookOnline.getBookingRangeEndDate())
                .setIsNeedSendRenewNotification(businessBookOnline.getIsNeedSendRenewNotification())
                .setDisplayStaffSelectionPage(businessBookOnline.getDisplayStaffSelectionPage());
    }

    public static List<OBConfigVO> convert(
            List<MoeBusinessBookOnline> bookOnlineList, List<OBBusinessInfoDTO> businessInfoDTOList) {
        Map<Integer, MoeBusinessBookOnline> bookOnlineMap = new HashMap<>();
        bookOnlineList.forEach(ob -> bookOnlineMap.put(ob.getBusinessId(), ob));

        return businessInfoDTOList.stream()
                .map(info -> convert(bookOnlineMap.get(info.getId()), info))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ApptDetailVO convertDetail(MoeGroomingAppointment appointment) {
        if (Objects.isNull(appointment)) {
            return null;
        }
        return new ApptDetailVO()
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId())
                .setApptDate(appointment.getAppointmentDate())
                .setApptStartTime(appointment.getAppointmentStartTime())
                .setNoStartTime(appointment.getNoStartTime())
                .setApptEndTime(appointment.getAppointmentEndTime())
                .setStatus(appointment.getStatus())
                .setPaymentStatus(appointment.getIsPaid())
                .setSource(appointment.getSource());
    }

    public static BusinessInfoVO convert(OBBusinessInfoDTO businessInfoDTO) {
        if (Objects.isNull(businessInfoDTO)) {
            return null;
        }
        return new BusinessInfoVO()
                .setBusinessId(businessInfoDTO.getId())
                .setBusinessName(businessInfoDTO.getBusinessName())
                .setPhoneNumber(businessInfoDTO.getPhoneNumber())
                .setEmail(businessInfoDTO.getOwnerEmail())
                .setAddress1(businessInfoDTO.getAddress1())
                .setAddress2(businessInfoDTO.getAddress2())
                .setAddressCity(businessInfoDTO.getAddressCity())
                .setAddressState(businessInfoDTO.getAddressState())
                .setAddressZipcode(businessInfoDTO.getAddressZipcode())
                .setAddressCountry(businessInfoDTO.getCountry())
                .setAddressLat(businessInfoDTO.getAddressLat())
                .setAddressLng(businessInfoDTO.getAddressLng())
                .setAvatarPath(businessInfoDTO.getAvatarPath())
                .setBusinessMode(businessInfoDTO.getBusinessMode())
                .setTimezoneName(businessInfoDTO.getTimezoneName())
                .setCurrencySymbol(businessInfoDTO.getCurrencySymbol())
                .setCurrencyCode(businessInfoDTO.getCurrencyCode())
                .setTimeFormat(businessInfoDTO.getTimeFormat())
                .setTimeFormatType(businessInfoDTO.getTimeFormatType())
                .setDateFormatType(businessInfoDTO.getDateFormatType())
                .setDateFormat(businessInfoDTO.getDateFormat())
                .setAppType(businessInfoDTO.getAppType());
    }

    public static List<BusinessInfoVO> convert(List<OBBusinessInfoDTO> businessInfoDTOList) {
        return businessInfoDTOList.stream().map(ClientApptUtils::convert).collect(Collectors.toList());
    }

    public static ClientApptVO convert(MoeGroomingAppointment appt) {
        if (Objects.isNull(appt)) {
            return null;
        }
        return new ClientApptVO()
                .setBookingId(String.valueOf(appt.getId()))
                .setApptDate(appt.getAppointmentDate())
                .setApptStartTime(appt.getAppointmentStartTime())
                .setApptEndTime(appt.getAppointmentEndTime())
                .setBusinessId(appt.getBusinessId())
                .setSource(appt.getSource())
                .setSubmitTime(appt.getCreateTime())
                .setNoStartTime(appt.getNoStartTime());
    }

    public static List<ClientApptVO> convertAppt(List<MoeGroomingAppointment> appointmentList) {
        return appointmentList.stream().map(ClientApptUtils::convert).collect(Collectors.toList());
    }

    public static OBProfileVO convert(MoeBookOnlineProfile obProfile) {
        if (Objects.isNull(obProfile)) {
            return null;
        }
        return new OBProfileVO()
                .setBusinessId(obProfile.getBusinessId())
                .setBusinessName(obProfile.getBusinessName())
                .setEmail(obProfile.getBusinessEmail())
                .setAddress(obProfile.getAddress())
                .setAddressDetails(obProfile.getAddressDetails())
                .setPhoneNumber(obProfile.getPhoneNumber())
                .setAvatarPath(obProfile.getAvatarPath());
    }

    public static List<OBProfileVO> convertProfile(List<MoeBookOnlineProfile> profileList) {
        return profileList.stream().map(ClientApptUtils::convert).collect(Collectors.toList());
    }

    public static OBDepositVO convert(MoeBookOnlineDeposit deposit) {
        if (Objects.isNull(deposit) || DepositPaymentTypeEnum.PreAuth.equals(deposit.getDepositType())) {
            return null;
        }
        return new OBDepositVO()
                .setServiceTotal(deposit.getServiceTotal())
                .setTax(deposit.getTaxAmount())
                .setTips(deposit.getTipsAmount())
                .setServiceChargeAmount(deposit.getServiceChargeAmount())
                .setFees(deposit.getBookingFee().add(deposit.getConvenienceFee()))
                .setDepositAmount(deposit.getAmount().subtract(deposit.getConvenienceFee()))
                .setPaidAmount(deposit.getAmount().add(deposit.getBookingFee()))
                .setPrepayType(
                        StringUtils.hasText(deposit.getGuid())
                                        && deposit.getGuid().startsWith(DepositService.DEPOSIT_PREFIX)
                                ? OnlineBookingConst.PREPAY_TYPE_DEPOSIT
                                : OnlineBookingConst.PREPAY_TYPE_FULL_PAY);
    }

    public static CreditCardDetailVO convert(PaymentDTO paymentDTO) {
        if (Objects.isNull(paymentDTO)) {
            return null;
        }
        return new CreditCardDetailVO()
                .setCardType(paymentDTO.getCardType())
                .setCardNumber(paymentDTO.getCardNumber())
                .setExpYear(paymentDTO.getExpYear())
                .setExpMonth(paymentDTO.getExpMonth());
    }

    public static PetDetailVO convert(CustomerPetDetailDTO petDetailDTO) {
        if (Objects.isNull(petDetailDTO)) {
            return null;
        }
        return new PetDetailVO()
                .setPetId(petDetailDTO.getPetId())
                .setPetName(petDetailDTO.getPetName())
                .setPetTypeId(petDetailDTO.getPetTypeId())
                .setBreed(petDetailDTO.getBreed())
                .setAvatarPath(petDetailDTO.getAvatarPath())
                .setWeight(petDetailDTO.getWeight());
    }

    public static StaffDetailVO convert(MoeStaffDto staffDto) {
        if (Objects.isNull(staffDto)) {
            return null;
        }
        return new StaffDetailVO()
                .setStaffId(staffDto.getId())
                .setFirstName(staffDto.getFirstName())
                .setLastName(staffDto.getLastName())
                .setAvatarPath(staffDto.getAvatarPath());
    }

    public static BusinessTwilioVO convert(BusinessTwilioNumberDTO businessTwilioNumberDTO) {
        if (Objects.isNull(businessTwilioNumberDTO)) {
            return null;
        }
        return new BusinessTwilioVO().setTwilioNumber(businessTwilioNumberDTO.getTwilioNumber());
    }
}
