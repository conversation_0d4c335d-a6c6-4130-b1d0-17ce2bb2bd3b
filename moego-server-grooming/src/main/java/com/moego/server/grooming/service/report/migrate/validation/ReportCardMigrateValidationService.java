package com.moego.server.grooming.service.report.migrate.validation;

import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestionSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GroomingQuestionUniqueKey;
import com.moego.backend.proto.fulfillment.v1.QuestionType;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.backend.proto.pet.v1.Pet;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationError;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationResult;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Report Card 迁移数据校验服务（通用）
 *
 * 支持模板和问题数据的校验，提供通用的校验方法
 *
 * 校验策略：
 * 1. 批量校验：每迁移一批数据后立即校验该批次，快速发现问题
 * 2. 多层次校验：数据完整性、一致性、业务逻辑校验
 *
 * 性能优化：
 * 1. 批量查询减少网络开销
 * 2. 异步校验不阻塞主流程
 * 3. 分页校验避免内存溢出
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardMigrateValidationService {

    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;

    /**
     * 批量校验迁移后的模板数据
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceTemplates 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateTemplateBatch(List<MoeGroomingReportTemplate> sourceTemplates, int batchNumber) {
        log.info("开始批量校验第 {} 批数据，数量: {}", batchNumber, sourceTemplates.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceTemplates.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportTemplateUniqueKey> uniqueKeys = buildUniqueKeys(sourceTemplates);

            // 2. 批量查询目标数据
            GetTemplatesByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getTemplatesByUniqueKeys(GetTemplatesByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 校验数量
            if (targetResponse.getTemplatesCount() != sourceTemplates.size()) {
                log.error(
                        "[validate] Mismatch template count, source: {}, target: {}",
                        sourceTemplates.size(),
                        targetResponse.getTemplatesCount());
            }

            // 3. 执行数据校验
            for (MoeGroomingReportTemplate sourceTemplate : sourceTemplates) {
                List<ValidationError> errors =
                        validateSingleTemplate(sourceTemplate, targetResponse.getTemplatesList());
                errors.forEach(result::addError);
            }
            result.setSuccess(result.getErrors().isEmpty());
        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 批量校验迁移后的问题数据
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceQuestions 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateQuestionBatch(List<MoeGroomingReportQuestion> sourceQuestions, int batchNumber) {
        log.info("开始批量校验第 {} 批问题数据，数量: {}", batchNumber, sourceQuestions.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceQuestions.size());

        try {
            // 1. 构建唯一键列表
            List<GroomingQuestionUniqueKey> uniqueKeys = buildQuestionUniqueKeys(sourceQuestions);

            // 2. 批量查询目标数据
            GetGroomingQuestionsByQuestionKeysResponse targetResponse =
                    fulfillmentServiceClient.getGroomingQuestionsByQuestionKeys(
                            GetGroomingQuestionsByQuestionKeysRequest.newBuilder()
                                    .addAllQuestionKeys(uniqueKeys)
                                    .build());

            // 3. 执行校验
            for (MoeGroomingReportQuestion sourceQuestion : sourceQuestions) {
                List<ValidationError> errors =
                        validateSingleQuestion(sourceQuestion, targetResponse.getQuestionsList());
                errors.forEach(result::addError);
            }

            result.setSuccess(result.getErrors().isEmpty());
        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 校验单个模板数据
     */
    private List<ValidationError> validateSingleTemplate(
            MoeGroomingReportTemplate source, List<FulfillmentReportTemplateSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportTemplateSync target = findTargetTemplate(source, targetList);

        // 2. 数据校验
        return validateDataIntegrity(source, target); // 校验通过
    }

    /**
     * 数据校验 - 验证字段是否正确迁移
     */
    private List<ValidationError> validateDataIntegrity(
            MoeGroomingReportTemplate source, FulfillmentReportTemplateSync target) {
        List<ValidationError> errors = new ArrayList<>();
        // 校验所有除唯一键外的字段
        if (!Objects.equals(source.getThemeColor(), target.getThemeColor())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeColor")
                    .message("themeColor 不匹配")
                    .build();
            errors.add(error);
        }
        if (!Objects.equals(source.getLightThemeColor(), target.getLightThemeColor())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("lightThemeColor")
                    .message("lightThemeColor 不匹配")
                    .build();
            errors.add(error);
        }
        if (!Objects.equals(source.getThemeCode(), target.getThemeCode())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeCode")
                    .message("themeCode 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getThankYouMessage(), target.getThankYouMessage())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("thankYouMessage")
                    .message("thankYouMessage 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getTitle(), target.getTitle())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("title")
                    .message("title 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getShowShowcase(), target.getShowShowcase())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showShowcase")
                    .message("showShowcase 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowOverallFeedback(), target.getShowOverallFeedback())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showOverallFeedback")
                    .message("showOverallFeedback 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowPetCondition(), target.getShowPetCondition())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showPetCondition")
                    .message("showPetCondition 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowServiceStaffName(), target.getShowServiceStaffName())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showServiceStaffName")
                    .message("showServiceStaffName 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowNextAppointment(), target.getShowNextAppointment())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showNextAppointment")
                    .message("showNextAppointment 不匹配")
                    .build());
        }
        if (!Objects.equals(
                source.getNextAppointmentDateFormatType().intValue(),
                target.getNextAppointmentDateFormatType().getNumber())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("nextAppointmentDateFormatType")
                    .message("nextAppointmentDateFormatType 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowReviewBooster(), target.getShowReviewBooster())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showReviewBooster")
                    .message("showReviewBooster 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowYelpReview(), target.getShowYelpReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showYelpReview")
                    .message("showYelpReview 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowGoogleReview(), target.getShowGoogleReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showGoogleReview")
                    .message("showGoogleReview 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowFacebookReview(), target.getShowFacebookReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showFacebookReview")
                    .message("showFacebookReview 不匹配")
                    .build());
        }
        return errors;
    }

    /**
     * 构建唯一键列表
     */
    private List<FulfillmentReportTemplateUniqueKey> buildUniqueKeys(List<MoeGroomingReportTemplate> templates) {
        List<FulfillmentReportTemplateUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReportTemplate template : templates) {
            uniqueKeys.add(FulfillmentReportTemplateUniqueKey.newBuilder()
                    .setBusinessId(template.getBusinessId().longValue())
                    .setCompanyId(template.getCompanyId())
                    .setCareType(CareCategory.GROOMING)
                    .build());
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标模板
     */
    private FulfillmentReportTemplateSync findTargetTemplate(
            MoeGroomingReportTemplate source, List<FulfillmentReportTemplateSync> targetList) {
        return targetList.stream()
                .filter(target ->
                        target.getBusinessId() == source.getBusinessId().longValue()
                                && target.getCompanyId() == source.getCompanyId()
                                && target.getCareType() == CareCategory.GROOMING)
                .findFirst()
                .orElse(null);
    }

    /**
     * 校验单个问题数据
     */
    private List<ValidationError> validateSingleQuestion(
            MoeGroomingReportQuestion source, List<FulfillmentReportQuestionSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportQuestionSync target = findTargetQuestion(source, targetList);

        // 2. 数据完整性校验
        return validateQuestionDataIntegrity(source, target);
    }

    /**
     * 问题数据校验 - 验证字段是否正确迁移
     */
    private List<ValidationError> validateQuestionDataIntegrity(
            MoeGroomingReportQuestion source, FulfillmentReportQuestionSync target) {
        List<ValidationError> errors = new ArrayList<>();

        // 校验 category
        if (!Objects.equals(source.getCategory().intValue(), target.getCategoryValue())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("category")
                    .expectedValue(String.valueOf(source.getCategory()))
                    .actualValue(String.valueOf(target.getCategoryValue()))
                    .message("category 不匹配")
                    .build());
        }

        // 校验 type (需要转换字符串到枚举)
        String expectedTypeString = convertQuestionTypeToString(target.getType());
        if (!Objects.equals(source.getType(), expectedTypeString)) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("type")
                    .expectedValue(source.getType())
                    .actualValue(expectedTypeString)
                    .message("type 不匹配")
                    .build());
        }

        // 校验 extraJson
        String sourceExtraJson = source.getExtraJson() != null ? source.getExtraJson() : "";
        String targetExtraJson = target.getExtraJson();
        if (!Objects.equals(sourceExtraJson, targetExtraJson)) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .fieldName("extraJson")
                    .message("extraJson 不匹配")
                    .build());
        }

        // 校验 isDefault
        if (!Objects.equals(source.getIsDefault(), target.getIsDefault())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("isDefault")
                    .expectedValue(String.valueOf(source.getIsDefault()))
                    .actualValue(String.valueOf(target.getIsDefault()))
                    .message("isDefault 不匹配")
                    .build());
        }

        // 校验 isRequired
        if (!Objects.equals(source.getRequired(), target.getIsRequired())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("isRequired")
                    .expectedValue(String.valueOf(source.getRequired()))
                    .actualValue(String.valueOf(target.getIsRequired()))
                    .message("isRequired 不匹配")
                    .build());
        }

        // 校验 isTypeEditable
        if (!Objects.equals(source.getTypeEditable(), target.getIsTypeEditable())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("isTypeEditable")
                    .expectedValue(String.valueOf(source.getTypeEditable()))
                    .actualValue(String.valueOf(target.getIsTypeEditable()))
                    .message("isTypeEditable 不匹配")
                    .build());
        }

        // 校验 isTitleEditable
        if (!Objects.equals(source.getTitleEditable(), target.getIsTitleEditable())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("isTitleEditable")
                    .expectedValue(String.valueOf(source.getTitleEditable()))
                    .actualValue(String.valueOf(target.getIsTitleEditable()))
                    .message("isTitleEditable 不匹配")
                    .build());
        }

        // 校验 isOptionsEditable
        if (!Objects.equals(source.getOptionsEditable(), target.getIsOptionsEditable())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("isOptionsEditable")
                    .expectedValue(String.valueOf(source.getOptionsEditable()))
                    .actualValue(String.valueOf(target.getIsOptionsEditable()))
                    .message("isOptionsEditable 不匹配")
                    .build());
        }

        // 校验 sort
        if (!Objects.equals(source.getSort(), target.getSort())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("sort")
                    .expectedValue(String.valueOf(source.getSort()))
                    .actualValue(String.valueOf(target.getSort()))
                    .message("sort 不匹配")
                    .build());
        }

        return errors;
    }

    /**
     * 将 QuestionType 枚举转换为字符串
     */
    private String convertQuestionTypeToString(QuestionType questionType) {
        if (questionType == null) {
            return null;
        }
        switch (questionType) {
            case SINGLE_CHOICE:
                return "single_choice";
            case MULTI_CHOICE:
                return "multi_choice";
            case TEXT_INPUT:
                return "text_input";
            case BODY_VIEW:
                return "body_view";
            case SHORT_TEXT_INPUT:
                return "short_text_input";
            case TAG_CHOICE:
                return "tag_choice";
            default:
                return "unknown";
        }
    }

    /**
     * 构建问题唯一键列表
     */
    private List<GroomingQuestionUniqueKey> buildQuestionUniqueKeys(List<MoeGroomingReportQuestion> questions) {
        List<GroomingQuestionUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReportQuestion question : questions) {
            if (question.getBusinessId() != null && question.getCompanyId() != null && question.getTitle() != null) {
                uniqueKeys.add(GroomingQuestionUniqueKey.newBuilder()
                        .setBusinessId(question.getBusinessId().longValue())
                        .setCompanyId(question.getCompanyId())
                        .setTitle(question.getTitle())
                        .build());
            }
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标问题
     */
    private FulfillmentReportQuestionSync findTargetQuestion(
            MoeGroomingReportQuestion source, List<FulfillmentReportQuestionSync> targetList) {
        return targetList.stream()
                .filter(target ->
                        target.getBusinessId() == source.getBusinessId().longValue()
                                && target.getCompanyId() == source.getCompanyId()
                                && Objects.equals(target.getTitle(), source.getTitle()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 批量校验迁移后的报告数据（Grooming Report）
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceReports 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateGroomingReportBatch(List<MoeGroomingReport> sourceReports, int batchNumber) {
        log.info("开始批量校验第 {} 批 Grooming Report 数据，数量: {}", batchNumber, sourceReports.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceReports.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportUniqueKey> uniqueKeys = buildGroomingReportUniqueKeys(sourceReports);

            // 2. 批量查询目标数据
            GetReportsByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 3. 执行校验
            for (MoeGroomingReport sourceReport : sourceReports) {
                List<ValidationError> errors =
                        validateSingleGroomingReport(sourceReport, targetResponse.getReportsList());
                errors.forEach(result::addError);
            }

            result.setSuccess(result.getErrors().isEmpty());

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 批量校验迁移后的报告数据（Daily Report）
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceReports 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateDailyReportBatch(
            List<DailyReportConfigMigrateDef> sourceReports, int batchNumber, Map<String, CareCategory> careTypeMap) {
        log.info("开始批量校验第 {} 批 Daily Report 数据，数量: {}", batchNumber, sourceReports.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceReports.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportUniqueKey> uniqueKeys = buildDailyReportUniqueKeys(sourceReports, careTypeMap);

            // 2. 批量查询目标数据
            GetReportsByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 3. 执行校验
            for (DailyReportConfigMigrateDef sourceReport : sourceReports) {
                List<ValidationError> errors = validateSingleDailyReport(sourceReport, targetResponse.getReportsList());
                errors.forEach(result::addError);
            }

            result.setSuccess(result.getErrors().isEmpty());
        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 校验单个 Grooming Report 数据
     */
    private List<ValidationError> validateSingleGroomingReport(
            MoeGroomingReport source, List<FulfillmentReportSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportSync target = findTargetGroomingReport(source, targetList);

        // 2. 数据完整性校验
        return validateGroomingReportDataIntegrity(source, target);
    }

    /**
     * 校验单个 Daily Report 数据
     */
    private List<ValidationError> validateSingleDailyReport(
            DailyReportConfigMigrateDef source, List<FulfillmentReportSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportSync target = findTargetDailyReport(source, targetList);

        // 2. 数据完整性校验
        return validateDailyReportDataIntegrity(source, target);
    }

    /**
     * Grooming Report 数据完整性校验 - 验证关键字段是否正确迁移
     */
    private List<ValidationError> validateGroomingReportDataIntegrity(
            MoeGroomingReport source, FulfillmentReportSync target) {
        List<ValidationError> errors = new ArrayList<>();
        if (!Objects.equals(source.getPetTypeId().longValue(), target.getPetTypeId())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("petTypeId")
                    .expectedValue(String.valueOf(source.getPetTypeId()))
                    .actualValue(String.valueOf(target.getPetTypeId()))
                    .message("petTypeId 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getUuid(), target.getUuid())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("uuid")
                    .expectedValue(source.getUuid())
                    .actualValue(target.getUuid())
                    .message("uuid 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getStatus(), target.getStatus())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("status")
                    .expectedValue(source.getStatus())
                    .actualValue(target.getStatus())
                    .message("status 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getTemplateJson(), target.getTemplateJson())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("templateJson")
                    .message("templateJson 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getLinkOpenedCount(), target.getLinkOpenedCount())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("linkOpenedCount")
                    .expectedValue(String.valueOf(source.getLinkOpenedCount()))
                    .actualValue(String.valueOf(target.getLinkOpenedCount()))
                    .message("linkOpenedCount 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getThemeCode(), target.getThemeCode())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeCode")
                    .expectedValue(source.getThemeCode())
                    .actualValue(target.getThemeCode())
                    .message("themeCode 不匹配")
                    .build());
        }
        return errors;
    }

    /**
     * Daily Report 数据完整性校验 - 验证关键字段是否正确迁移
     */
    private List<ValidationError> validateDailyReportDataIntegrity(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        List<ValidationError> errors = new ArrayList<>();
        // petTypeId
        if (target.getPetTypeId() != Pet.PetType.DOG_VALUE) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("petTypeId")
                    .message("petTypeId 不匹配")
                    .build());
        }
        // uuid
        if (!Objects.equals(source.getUuid(), target.getUuid())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("uuid")
                    .expectedValue(source.getUuid())
                    .actualValue(target.getUuid())
                    .message("uuid 不匹配")
                    .build());
        }
        // status
        if (!Objects.equals(source.getStatus(), target.getStatus())) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("status")
                    .expectedValue(source.getStatus())
                    .actualValue(target.getStatus())
                    .message("status 不匹配")
                    .build());
        }
        // themeCode
        if (!Objects.equals(target.getThemeCode(), "Default")) {
            errors.add(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeCode")
                    .message("themeCode 不匹配")
                    .build());
        }
        return errors;
    }

    /**
     * Daily Report 数据一致性校验 - 对比源数据和目标数据的一致性
     */
    private ValidationError validateDailyReportDataConsistency(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        // 校验服务日期
        String expectedServiceDate = dateToString(source.getServiceDate());
        if (!Objects.equals(expectedServiceDate, target.getServiceDate())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("serviceDate")
                    .expectedValue(expectedServiceDate)
                    .actualValue(target.getServiceDate())
                    .message("serviceDate 不一致")
                    .build();
        }

        return null;
    }

    /**
     * Daily Report 业务逻辑校验 - 确保迁移后的数据符合业务规则
     */
    private ValidationError validateDailyReportBusinessLogic(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        // 1. 校验 CareType 必须是 DAYCARE 或 BOARDING
        if (target.getCareType() != CareCategory.DAYCARE && target.getCareType() != CareCategory.BOARDING) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("careType")
                    .expectedValue("DAYCARE or BOARDING")
                    .actualValue(target.getCareType().name())
                    .message("careType 必须是 DAYCARE 或 BOARDING")
                    .build();
        }

        // 2. 校验必填字段
        if (target.getBusinessId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("businessId")
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 必须大于 0")
                    .build();
        }

        if (target.getAppointmentId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("appointmentId")
                    .actualValue(String.valueOf(target.getAppointmentId()))
                    .message("appointmentId 必须大于 0")
                    .build();
        }

        // 3. 校验服务日期格式
        if (target.getServiceDate() == null || target.getServiceDate().trim().isEmpty()) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("serviceDate")
                    .actualValue(target.getServiceDate())
                    .message("serviceDate 不能为空")
                    .build();
        }

        return null;
    }

    /**
     * 构建 Grooming Report 唯一键列表
     */
    private List<FulfillmentReportUniqueKey> buildGroomingReportUniqueKeys(List<MoeGroomingReport> reports) {
        List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReport report : reports) {
            if (report.getBusinessId() != null && report.getGroomingId() != null && report.getPetId() != null) {
                uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                        .setBusinessId(report.getBusinessId().longValue())
                        .setAppointmentId(report.getGroomingId().longValue())
                        .setCareType(CareCategory.GROOMING)
                        .setPetId(report.getPetId())
                        .setServiceDate("")
                        .build());
            }
        }
        return uniqueKeys;
    }

    /**
     * 构建 Daily Report 唯一键列表
     */
    private List<FulfillmentReportUniqueKey> buildDailyReportUniqueKeys(
            List<DailyReportConfigMigrateDef> reports, Map<String, CareCategory> careTypeMap) {
        List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
        for (DailyReportConfigMigrateDef report : reports) {
            uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                    .setBusinessId(report.getBusinessId())
                    .setAppointmentId(report.getAppointmentId())
                    .setCareType(careTypeMap.get(report.getPetId() + "_" + report.getAppointmentId()))
                    .setPetId(report.getPetId())
                    .setServiceDate(dateToString(report.getServiceDate()))
                    .build());
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标 Grooming Report
     */
    private FulfillmentReportSync findTargetGroomingReport(
            MoeGroomingReport source, List<FulfillmentReportSync> targetList) {
        return targetList.stream()
                .filter(target -> target.getBusinessId()
                                == source.getBusinessId().longValue()
                        && target.getAppointmentId() == source.getGroomingId().longValue()
                        && target.getPetId() == source.getPetId().longValue()
                        && target.getCareType() == CareCategory.GROOMING)
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找对应的目标 Daily Report
     */
    private FulfillmentReportSync findTargetDailyReport(
            DailyReportConfigMigrateDef source, List<FulfillmentReportSync> targetList) {
        String expectedServiceDate = dateToString(source.getServiceDate());
        return targetList.stream()
                .filter(target -> target.getBusinessId() == source.getBusinessId()
                        && target.getAppointmentId() == source.getAppointmentId()
                        && Objects.equals(target.getPetId(), source.getPetId())
                        && Objects.equals(target.getServiceDate(), expectedServiceDate)
                        && (target.getCareType() == CareCategory.DAYCARE
                                || target.getCareType() == CareCategory.BOARDING))
                .findFirst()
                .orElse(null);
    }

    /**
     * 日期转换为字符串
     */
    private String dateToString(com.google.type.Date date) {
        if (date == null) {
            return "";
        }
        return String.format("%04d-%02d-%02d", date.getYear(), date.getMonth(), date.getDay());
    }
}
