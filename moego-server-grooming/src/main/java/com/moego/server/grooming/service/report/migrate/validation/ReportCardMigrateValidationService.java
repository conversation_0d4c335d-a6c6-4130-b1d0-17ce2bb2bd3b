package com.moego.server.grooming.service.report.migrate.validation;

import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestionSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetReportsByUniqueKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysResponse;
import com.moego.backend.proto.fulfillment.v1.GroomingQuestionUniqueKey;
import com.moego.backend.proto.fulfillment.v1.QuestionCategory;
import com.moego.backend.proto.fulfillment.v1.QuestionType;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.idl.models.appointment.v1.DailyReportConfigMigrateDef;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationError;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationResult;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Report Card 迁移数据校验服务（通用）
 *
 * 支持模板和问题数据的校验，提供通用的校验方法
 *
 * 校验策略：
 * 1. 批量校验：每迁移一批数据后立即校验该批次，快速发现问题
 * 2. 多层次校验：数据完整性、一致性、业务逻辑校验
 *
 * 性能优化：
 * 1. 批量查询减少网络开销
 * 2. 异步校验不阻塞主流程
 * 3. 分页校验避免内存溢出
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardMigrateValidationService {

    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;

    /**
     * 批量校验迁移后的模板数据
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceTemplates 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateTemplateBatch(List<MoeGroomingReportTemplate> sourceTemplates, int batchNumber) {
        log.info("开始批量校验第 {} 批数据，数量: {}", batchNumber, sourceTemplates.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceTemplates.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportTemplateUniqueKey> uniqueKeys = buildUniqueKeys(sourceTemplates);

            // 2. 批量查询目标数据
            GetTemplatesByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getTemplatesByUniqueKeys(GetTemplatesByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 校验数量
            if (targetResponse.getTemplatesCount() != sourceTemplates.size()) {
                log.error(
                        "[validate] Mismatch template count, source: {}, target: {}",
                        sourceTemplates.size(),
                        targetResponse.getTemplatesCount());
            }

            // 3. 执行数据校验
            for (MoeGroomingReportTemplate sourceTemplate : sourceTemplates) {
                List<ValidationError> errors =
                        validateSingleTemplate(sourceTemplate, targetResponse.getTemplatesList());
                errors.forEach(result::addError);
            }
            result.setSuccess(result.getErrors().isEmpty());
        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 批量校验迁移后的问题数据
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceQuestions 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateQuestionBatch(List<MoeGroomingReportQuestion> sourceQuestions, int batchNumber) {
        log.info("开始批量校验第 {} 批问题数据，数量: {}", batchNumber, sourceQuestions.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceQuestions.size());

        try {
            // 1. 构建唯一键列表
            List<GroomingQuestionUniqueKey> uniqueKeys = buildQuestionUniqueKeys(sourceQuestions);

            // 2. 批量查询目标数据
            GetGroomingQuestionsByQuestionKeysResponse targetResponse =
                    fulfillmentServiceClient.getGroomingQuestionsByQuestionKeys(
                            GetGroomingQuestionsByQuestionKeysRequest.newBuilder()
                                    .addAllQuestionKeys(uniqueKeys)
                                    .build());

            // 3. 执行校验
            for (MoeGroomingReportQuestion sourceQuestion : sourceQuestions) {
                ValidationError error = validateSingleQuestion(sourceQuestion, targetResponse.getQuestionsList());
                if (error != null) {
                    result.addError(error);
                } else {
                    result.incrementValidCount();
                }
            }

            result.setSuccess(result.getErrors().isEmpty());

            log.info(
                    "批量校验完成，批次: {}, 总数: {}, 有效: {}, 错误: {}",
                    batchNumber,
                    result.getTotalCount(),
                    result.getValidCount(),
                    result.getErrors().size());

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 校验单个模板数据
     */
    private List<ValidationError> validateSingleTemplate(
            MoeGroomingReportTemplate source, List<FulfillmentReportTemplateSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportTemplateSync target = findTargetTemplate(source, targetList);

        // 2. 数据校验
        return validateDataIntegrity(source, target); // 校验通过
    }

    /**
     * 数据校验 - 验证字段是否正确迁移
     */
    private List<ValidationError> validateDataIntegrity(
            MoeGroomingReportTemplate source, FulfillmentReportTemplateSync target) {
        List<ValidationError> errors = new ArrayList<>();
        // 校验所有除唯一键外的字段
        if (!Objects.equals(source.getThemeColor(), target.getThemeColor())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeColor")
                    .message("themeColor 不匹配")
                    .build();
            errors.add(error);
        }
        if (!Objects.equals(source.getLightThemeColor(), target.getLightThemeColor())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("lightThemeColor")
                    .message("lightThemeColor 不匹配")
                    .build();
            errors.add(error);
        }
        if (!Objects.equals(source.getThemeCode(), target.getThemeCode())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("themeCode")
                    .message("themeCode 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getThankYouMessage(), target.getThankYouMessage())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("thankYouMessage")
                    .message("thankYouMessage 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getTitle(), target.getTitle())) {
            ValidationError error = ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("title")
                    .message("title 不匹配")
                    .build();
            errors.add(error);
        }

        if (!Objects.equals(source.getShowShowcase(), target.getShowShowcase())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showShowcase")
                    .message("showShowcase 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowOverallFeedback(), target.getShowOverallFeedback())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showOverallFeedback")
                    .message("showOverallFeedback 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowPetCondition(), target.getShowPetCondition())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showPetCondition")
                    .message("showPetCondition 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowServiceStaffName(), target.getShowServiceStaffName())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showServiceStaffName")
                    .message("showServiceStaffName 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowNextAppointment(), target.getShowNextAppointment())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showNextAppointment")
                    .message("showNextAppointment 不匹配")
                    .build());
        }
        if (!Objects.equals(
                source.getNextAppointmentDateFormatType().intValue(),
                target.getNextAppointmentDateFormatType().getNumber())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("nextAppointmentDateFormatType")
                    .message("nextAppointmentDateFormatType 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowReviewBooster(), target.getShowReviewBooster())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showReviewBooster")
                    .message("showReviewBooster 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowYelpReview(), target.getShowYelpReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showYelpReview")
                    .message("showYelpReview 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowGoogleReview(), target.getShowGoogleReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showGoogleReview")
                    .message("showGoogleReview 不匹配")
                    .build());
        }
        if (!Objects.equals(source.getShowFacebookReview(), target.getShowFacebookReview())) {
            errors.add(ValidationError.builder()
                    .sourceId(source.getId())
                    .targetId(Math.toIntExact(target.getId()))
                    .fieldName("showFacebookReview")
                    .message("showFacebookReview 不匹配")
                    .build());
        }
        return errors;
    }

    /**
     * 构建唯一键列表
     */
    private List<FulfillmentReportTemplateUniqueKey> buildUniqueKeys(List<MoeGroomingReportTemplate> templates) {
        List<FulfillmentReportTemplateUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReportTemplate template : templates) {
            uniqueKeys.add(FulfillmentReportTemplateUniqueKey.newBuilder()
                    .setBusinessId(template.getBusinessId().longValue())
                    .setCompanyId(template.getCompanyId())
                    .setCareType(CareCategory.GROOMING)
                    .build());
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标模板
     */
    private FulfillmentReportTemplateSync findTargetTemplate(
            MoeGroomingReportTemplate source, List<FulfillmentReportTemplateSync> targetList) {
        return targetList.stream()
                .filter(target ->
                        target.getBusinessId() == source.getBusinessId().longValue()
                                && target.getCompanyId() == source.getCompanyId()
                                && target.getCareType() == CareCategory.GROOMING)
                .findFirst()
                .orElse(null);
    }

    /**
     * 校验单个问题数据
     */
    private ValidationError validateSingleQuestion(
            MoeGroomingReportQuestion source, List<FulfillmentReportQuestionSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportQuestionSync target = findTargetQuestion(source, targetList);
        if (target == null) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_MISSING)
                    .sourceId(source.getId())
                    .message(String.format(
                            "目标问题数据不存在，businessId: %d, companyId: %d, title: %s",
                            source.getBusinessId(), source.getCompanyId(), source.getTitle()))
                    .build();
        }

        // 2. 数据完整性校验
        ValidationError integrityError = validateQuestionDataIntegrity(source, target);
        if (integrityError != null) {
            return integrityError;
        }

        // 3. 数据一致性校验
        ValidationError consistencyError = validateQuestionDataConsistency(source, target);
        if (consistencyError != null) {
            return consistencyError;
        }

        // 4. 业务逻辑校验
        ValidationError businessError = validateQuestionBusinessLogic(source, target);
        if (businessError != null) {
            return businessError;
        }

        return null; // 校验通过
    }

    /**
     * 问题数据完整性校验 - 验证关键字段是否正确迁移
     */
    private ValidationError validateQuestionDataIntegrity(
            MoeGroomingReportQuestion source, FulfillmentReportQuestionSync target) {
        // 校验关键字段
        if (!Objects.equals(source.getBusinessId().longValue(), target.getBusinessId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("businessId")
                    .expectedValue(String.valueOf(source.getBusinessId()))
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 不匹配")
                    .build();
        }

        if (!Objects.equals(source.getCompanyId(), target.getCompanyId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("companyId")
                    .expectedValue(String.valueOf(source.getCompanyId()))
                    .actualValue(String.valueOf(target.getCompanyId()))
                    .message("companyId 不匹配")
                    .build();
        }

        return null;
    }

    /**
     * 问题数据一致性校验 - 对比源数据和目标数据的一致性
     */
    private ValidationError validateQuestionDataConsistency(
            MoeGroomingReportQuestion source, FulfillmentReportQuestionSync target) {
        // 校验布尔字段
        if (!Objects.equals(source.getIsDefault(), target.getIsDefault())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(source.getId())
                    .fieldName("isDefault")
                    .expectedValue(String.valueOf(source.getIsDefault()))
                    .actualValue(String.valueOf(target.getIsDefault()))
                    .message("isDefault 不一致")
                    .build();
        }

        if (!Objects.equals(source.getRequired(), target.getIsRequired())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(source.getId())
                    .fieldName("required")
                    .expectedValue(String.valueOf(source.getRequired()))
                    .actualValue(String.valueOf(target.getIsRequired()))
                    .message("required 字段不一致")
                    .build();
        }

        // 校验分类字段
        QuestionCategory expectedCategory =
                QuestionCategory.forNumber(source.getCategory().intValue());
        if (target.getCategory() != expectedCategory) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(source.getId())
                    .fieldName("category")
                    .expectedValue(expectedCategory.name())
                    .actualValue(target.getCategory().name())
                    .message("问题分类不一致")
                    .build();
        }

        return null;
    }

    /**
     * 问题业务逻辑校验 - 确保迁移后的数据符合业务规则
     */
    private ValidationError validateQuestionBusinessLogic(
            MoeGroomingReportQuestion source, FulfillmentReportQuestionSync target) {
        // 1. 校验 CareType 必须是 GROOMING
        if (target.getCareType() != CareCategory.GROOMING) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("careType")
                    .expectedValue("GROOMING")
                    .actualValue(target.getCareType().name())
                    .message("careType 必须是 GROOMING")
                    .build();
        }

        // 2. 校验必填字段
        if (target.getBusinessId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("businessId")
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 必须大于 0")
                    .build();
        }

        if (target.getCompanyId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("companyId")
                    .actualValue(String.valueOf(target.getCompanyId()))
                    .message("companyId 必须大于 0")
                    .build();
        }

        // 3. 校验问题标题不能为空
        target.getTitle();
        if (target.getTitle().trim().isEmpty()) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("title")
                    .actualValue(target.getTitle())
                    .message("问题标题不能为空")
                    .build();
        }

        // 4. 校验问题类型有效性
        if (target.getType() == QuestionType.QUESTION_TYPE_UNSPECIFIED) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("type")
                    .actualValue(target.getType().name())
                    .message("问题类型不能为未指定类型")
                    .build();
        }

        return null;
    }

    /**
     * 构建问题唯一键列表
     */
    private List<GroomingQuestionUniqueKey> buildQuestionUniqueKeys(List<MoeGroomingReportQuestion> questions) {
        List<GroomingQuestionUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReportQuestion question : questions) {
            if (question.getBusinessId() != null && question.getCompanyId() != null && question.getTitle() != null) {
                uniqueKeys.add(GroomingQuestionUniqueKey.newBuilder()
                        .setBusinessId(question.getBusinessId().longValue())
                        .setCompanyId(question.getCompanyId())
                        .setTitle(question.getTitle())
                        .build());
            }
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标问题
     */
    private FulfillmentReportQuestionSync findTargetQuestion(
            MoeGroomingReportQuestion source, List<FulfillmentReportQuestionSync> targetList) {
        return targetList.stream()
                .filter(target ->
                        target.getBusinessId() == source.getBusinessId().longValue()
                                && target.getCompanyId() == source.getCompanyId()
                                && Objects.equals(target.getTitle(), source.getTitle()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 批量校验迁移后的报告数据（Grooming Report）
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceReports 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateGroomingReportBatch(List<MoeGroomingReport> sourceReports, int batchNumber) {
        log.info("开始批量校验第 {} 批 Grooming Report 数据，数量: {}", batchNumber, sourceReports.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceReports.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportUniqueKey> uniqueKeys = buildGroomingReportUniqueKeys(sourceReports);

            // 2. 批量查询目标数据
            GetReportsByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 3. 执行校验
            for (MoeGroomingReport sourceReport : sourceReports) {
                ValidationError error = validateSingleGroomingReport(sourceReport, targetResponse.getReportsList());
                if (error != null) {
                    result.addError(error);
                } else {
                    result.incrementValidCount();
                }
            }

            result.setSuccess(result.getErrors().isEmpty());

            log.info(
                    "批量校验完成，批次: {}, 总数: {}, 有效: {}, 错误: {}",
                    batchNumber,
                    result.getTotalCount(),
                    result.getValidCount(),
                    result.getErrors().size());

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 批量校验迁移后的报告数据（Daily Report）
     * 在每个批次迁移完成后立即调用，快速发现问题
     *
     * @param sourceReports 源数据列表
     * @param batchNumber 批次号
     * @return 校验结果
     */
    public ValidationResult validateDailyReportBatch(
            List<DailyReportConfigMigrateDef> sourceReports, int batchNumber, Map<String, CareCategory> careTypeMap) {
        log.info("开始批量校验第 {} 批 Daily Report 数据，数量: {}", batchNumber, sourceReports.size());

        ValidationResult result = new ValidationResult();
        result.setBatchNumber(batchNumber);
        result.setTotalCount(sourceReports.size());

        try {
            // 1. 构建唯一键列表
            List<FulfillmentReportUniqueKey> uniqueKeys = buildDailyReportUniqueKeys(sourceReports, careTypeMap);

            // 2. 批量查询目标数据
            GetReportsByUniqueKeysResponse targetResponse =
                    fulfillmentServiceClient.getReportsByUniqueKeys(GetReportsByUniqueKeysRequest.newBuilder()
                            .addAllUniqueKeys(uniqueKeys)
                            .build());

            // 3. 执行校验
            for (DailyReportConfigMigrateDef sourceReport : sourceReports) {
                ValidationError error = validateSingleDailyReport(sourceReport, targetResponse.getReportsList());
                if (error != null) {
                    result.addError(error);
                } else {
                    result.incrementValidCount();
                }
            }

            result.setSuccess(result.getErrors().isEmpty());

            log.info(
                    "批量校验完成，批次: {}, 总数: {}, 有效: {}, 错误: {}",
                    batchNumber,
                    result.getTotalCount(),
                    result.getValidCount(),
                    result.getErrors().size());

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            result.setSuccess(false);
            result.addError(ValidationError.builder()
                    .errorType(ValidationError.ErrorType.SYSTEM_ERROR)
                    .message("批量校验异常: " + e.getMessage())
                    .build());
        }

        return result;
    }

    /**
     * 校验单个 Grooming Report 数据
     */
    private ValidationError validateSingleGroomingReport(
            MoeGroomingReport source, List<FulfillmentReportSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportSync target = findTargetGroomingReport(source, targetList);
        if (target == null) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_MISSING)
                    .sourceId(source.getId())
                    .message(String.format(
                            "目标 Grooming Report 数据不存在，businessId: %d, appointmentId: %d, petId: %s",
                            source.getBusinessId(), source.getGroomingId(), source.getPetId()))
                    .build();
        }

        // 2. 数据完整性校验
        ValidationError integrityError = validateGroomingReportDataIntegrity(source, target);
        if (integrityError != null) {
            return integrityError;
        }

        // 3. 数据一致性校验
        ValidationError consistencyError = validateGroomingReportDataConsistency(source, target);
        if (consistencyError != null) {
            return consistencyError;
        }

        // 4. 业务逻辑校验
        ValidationError businessError = validateGroomingReportBusinessLogic(source, target);
        if (businessError != null) {
            return businessError;
        }

        return null; // 校验通过
    }

    /**
     * 校验单个 Daily Report 数据
     */
    private ValidationError validateSingleDailyReport(
            DailyReportConfigMigrateDef source, List<FulfillmentReportSync> targetList) {
        // 1. 查找对应的目标数据
        FulfillmentReportSync target = findTargetDailyReport(source, targetList);
        if (target == null) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_MISSING)
                    .sourceId(Math.toIntExact(source.getId()))
                    .message(String.format(
                            "目标 Daily Report 数据不存在，businessId: %d, appointmentId: %d, petId: %s",
                            source.getBusinessId(), source.getAppointmentId(), source.getPetId()))
                    .build();
        }

        // 2. 数据完整性校验
        ValidationError integrityError = validateDailyReportDataIntegrity(source, target);
        if (integrityError != null) {
            return integrityError;
        }

        // 3. 数据一致性校验
        ValidationError consistencyError = validateDailyReportDataConsistency(source, target);
        if (consistencyError != null) {
            return consistencyError;
        }

        // 4. 业务逻辑校验
        ValidationError businessError = validateDailyReportBusinessLogic(source, target);
        if (businessError != null) {
            return businessError;
        }

        return null; // 校验通过
    }

    /**
     * Grooming Report 数据完整性校验 - 验证关键字段是否正确迁移
     */
    private ValidationError validateGroomingReportDataIntegrity(
            MoeGroomingReport source, FulfillmentReportSync target) {
        // 校验关键字段
        if (!Objects.equals(source.getBusinessId().longValue(), target.getBusinessId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("businessId")
                    .expectedValue(String.valueOf(source.getBusinessId()))
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 不匹配")
                    .build();
        }

        if (!Objects.equals(source.getGroomingId().longValue(), target.getAppointmentId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("appointmentId")
                    .expectedValue(String.valueOf(source.getGroomingId()))
                    .actualValue(String.valueOf(target.getAppointmentId()))
                    .message("appointmentId 不匹配")
                    .build();
        }

        if (source.getPetId().longValue() != target.getPetId()) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(source.getId())
                    .fieldName("petId")
                    .expectedValue(String.valueOf(source.getPetId()))
                    .actualValue(String.valueOf(target.getPetId()))
                    .message("petId 不匹配")
                    .build();
        }

        return null;
    }

    /**
     * Grooming Report 数据一致性校验 - 对比源数据和目标数据的一致性
     */
    private ValidationError validateGroomingReportDataConsistency(
            MoeGroomingReport source, FulfillmentReportSync target) {
        // 校验客户ID
        if (source.getCustomerId() != null
                && !Objects.equals(source.getCustomerId().longValue(), target.getCustomerId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(source.getId())
                    .fieldName("customerId")
                    .expectedValue(String.valueOf(source.getCustomerId()))
                    .actualValue(String.valueOf(target.getCustomerId()))
                    .message("customerId 不一致")
                    .build();
        }

        // 校验 UUID
        if (!Objects.equals(source.getUuid(), target.getUuid())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(source.getId())
                    .fieldName("uuid")
                    .expectedValue(source.getUuid())
                    .actualValue(target.getUuid())
                    .message("uuid 不一致")
                    .build();
        }

        return null;
    }

    /**
     * Grooming Report 业务逻辑校验 - 确保迁移后的数据符合业务规则
     */
    private ValidationError validateGroomingReportBusinessLogic(
            MoeGroomingReport source, FulfillmentReportSync target) {
        // 1. 校验 CareType 必须是 GROOMING
        if (target.getCareType() != CareCategory.GROOMING) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("careType")
                    .expectedValue("GROOMING")
                    .actualValue(target.getCareType().name())
                    .message("careType 必须是 GROOMING")
                    .build();
        }

        // 2. 校验必填字段
        if (target.getBusinessId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("businessId")
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 必须大于 0")
                    .build();
        }

        if (target.getAppointmentId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(source.getId())
                    .fieldName("appointmentId")
                    .actualValue(String.valueOf(target.getAppointmentId()))
                    .message("appointmentId 必须大于 0")
                    .build();
        }

        return null;
    }

    /**
     * Daily Report 数据完整性校验 - 验证关键字段是否正确迁移
     */
    private ValidationError validateDailyReportDataIntegrity(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        // 校验关键字段
        if (!Objects.equals(source.getBusinessId(), target.getBusinessId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("businessId")
                    .expectedValue(String.valueOf(source.getBusinessId()))
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 不匹配")
                    .build();
        }

        if (!Objects.equals(source.getAppointmentId(), target.getAppointmentId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("appointmentId")
                    .expectedValue(String.valueOf(source.getAppointmentId()))
                    .actualValue(String.valueOf(target.getAppointmentId()))
                    .message("appointmentId 不匹配")
                    .build();
        }

        if (!Objects.equals(source.getPetId(), target.getPetId())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_INTEGRITY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("petId")
                    .expectedValue(String.valueOf(source.getPetId()))
                    .actualValue(String.valueOf(target.getPetId()))
                    .message("petId 不匹配")
                    .build();
        }

        return null;
    }

    /**
     * Daily Report 数据一致性校验 - 对比源数据和目标数据的一致性
     */
    private ValidationError validateDailyReportDataConsistency(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        // 校验服务日期
        String expectedServiceDate = dateToString(source.getServiceDate());
        if (!Objects.equals(expectedServiceDate, target.getServiceDate())) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.DATA_CONSISTENCY)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("serviceDate")
                    .expectedValue(expectedServiceDate)
                    .actualValue(target.getServiceDate())
                    .message("serviceDate 不一致")
                    .build();
        }

        return null;
    }

    /**
     * Daily Report 业务逻辑校验 - 确保迁移后的数据符合业务规则
     */
    private ValidationError validateDailyReportBusinessLogic(
            DailyReportConfigMigrateDef source, FulfillmentReportSync target) {
        // 1. 校验 CareType 必须是 DAYCARE 或 BOARDING
        if (target.getCareType() != CareCategory.DAYCARE && target.getCareType() != CareCategory.BOARDING) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("careType")
                    .expectedValue("DAYCARE or BOARDING")
                    .actualValue(target.getCareType().name())
                    .message("careType 必须是 DAYCARE 或 BOARDING")
                    .build();
        }

        // 2. 校验必填字段
        if (target.getBusinessId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("businessId")
                    .actualValue(String.valueOf(target.getBusinessId()))
                    .message("businessId 必须大于 0")
                    .build();
        }

        if (target.getAppointmentId() <= 0) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("appointmentId")
                    .actualValue(String.valueOf(target.getAppointmentId()))
                    .message("appointmentId 必须大于 0")
                    .build();
        }

        // 3. 校验服务日期格式
        if (target.getServiceDate() == null || target.getServiceDate().trim().isEmpty()) {
            return ValidationError.builder()
                    .errorType(ValidationError.ErrorType.BUSINESS_LOGIC)
                    .sourceId(Math.toIntExact(source.getId()))
                    .fieldName("serviceDate")
                    .actualValue(target.getServiceDate())
                    .message("serviceDate 不能为空")
                    .build();
        }

        return null;
    }

    /**
     * 构建 Grooming Report 唯一键列表
     */
    private List<FulfillmentReportUniqueKey> buildGroomingReportUniqueKeys(List<MoeGroomingReport> reports) {
        List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
        for (MoeGroomingReport report : reports) {
            if (report.getBusinessId() != null && report.getGroomingId() != null && report.getPetId() != null) {
                uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                        .setBusinessId(report.getBusinessId().longValue())
                        .setAppointmentId(report.getGroomingId().longValue())
                        .setCareType(CareCategory.GROOMING)
                        .setPetId(report.getPetId())
                        .setServiceDate("")
                        .build());
            }
        }
        return uniqueKeys;
    }

    /**
     * 构建 Daily Report 唯一键列表
     */
    private List<FulfillmentReportUniqueKey> buildDailyReportUniqueKeys(
            List<DailyReportConfigMigrateDef> reports, Map<String, CareCategory> careTypeMap) {
        List<FulfillmentReportUniqueKey> uniqueKeys = new ArrayList<>();
        for (DailyReportConfigMigrateDef report : reports) {
            // 这里需要根据实际业务逻辑确定 CareType，暂时使用 DAYCARE
            uniqueKeys.add(FulfillmentReportUniqueKey.newBuilder()
                    .setBusinessId(report.getBusinessId())
                    .setAppointmentId(report.getAppointmentId())
                    .setCareType(careTypeMap.get(report.getPetId() + "_" + report.getAppointmentId()))
                    .setPetId(report.getPetId())
                    .setServiceDate(dateToString(report.getServiceDate()))
                    .build());
        }
        return uniqueKeys;
    }

    /**
     * 查找对应的目标 Grooming Report
     */
    private FulfillmentReportSync findTargetGroomingReport(
            MoeGroomingReport source, List<FulfillmentReportSync> targetList) {
        return targetList.stream()
                .filter(target -> target.getBusinessId()
                                == source.getBusinessId().longValue()
                        && target.getAppointmentId() == source.getGroomingId().longValue()
                        && target.getPetId() == source.getPetId().longValue()
                        && target.getCareType() == CareCategory.GROOMING)
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找对应的目标 Daily Report
     */
    private FulfillmentReportSync findTargetDailyReport(
            DailyReportConfigMigrateDef source, List<FulfillmentReportSync> targetList) {
        String expectedServiceDate = dateToString(source.getServiceDate());
        return targetList.stream()
                .filter(target -> target.getBusinessId() == source.getBusinessId()
                        && target.getAppointmentId() == source.getAppointmentId()
                        && Objects.equals(target.getPetId(), source.getPetId())
                        && Objects.equals(target.getServiceDate(), expectedServiceDate)
                        && (target.getCareType() == CareCategory.DAYCARE
                                || target.getCareType() == CareCategory.BOARDING))
                .findFirst()
                .orElse(null);
    }

    /**
     * 日期转换为字符串
     */
    private String dateToString(com.google.type.Date date) {
        if (date == null) {
            return "";
        }
        return String.format("%04d-%02d-%02d", date.getYear(), date.getMonth(), date.getDay());
    }
}
