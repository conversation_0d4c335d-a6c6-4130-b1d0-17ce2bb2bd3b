package com.moego.server.grooming.mapperbean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MoeGroomingServiceExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public MoeGroomingServiceExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Byte value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Byte value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Byte value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Byte value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Byte value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Byte> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Byte> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Byte value1, Byte value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNull() {
            addCriterion("tax_id is null");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNotNull() {
            addCriterion("tax_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaxIdEqualTo(Integer value) {
            addCriterion("tax_id =", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotEqualTo(Integer value) {
            addCriterion("tax_id <>", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThan(Integer value) {
            addCriterion("tax_id >", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("tax_id >=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThan(Integer value) {
            addCriterion("tax_id <", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThanOrEqualTo(Integer value) {
            addCriterion("tax_id <=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdIn(List<Integer> values) {
            addCriterion("tax_id in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotIn(List<Integer> values) {
            addCriterion("tax_id not in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdBetween(Integer value1, Integer value2) {
            addCriterion("tax_id between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotBetween(Integer value1, Integer value2) {
            addCriterion("tax_id not between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andInactiveIsNull() {
            addCriterion("inactive is null");
            return (Criteria) this;
        }

        public Criteria andInactiveIsNotNull() {
            addCriterion("inactive is not null");
            return (Criteria) this;
        }

        public Criteria andInactiveEqualTo(Byte value) {
            addCriterion("inactive =", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotEqualTo(Byte value) {
            addCriterion("inactive <>", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveGreaterThan(Byte value) {
            addCriterion("inactive >", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveGreaterThanOrEqualTo(Byte value) {
            addCriterion("inactive >=", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveLessThan(Byte value) {
            addCriterion("inactive <", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveLessThanOrEqualTo(Byte value) {
            addCriterion("inactive <=", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveIn(List<Byte> values) {
            addCriterion("inactive in", values, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotIn(List<Byte> values) {
            addCriterion("inactive not in", values, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveBetween(Byte value1, Byte value2) {
            addCriterion("inactive between", value1, value2, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotBetween(Byte value1, Byte value2) {
            addCriterion("inactive not between", value1, value2, "inactive");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNull() {
            addCriterion("color_code is null");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNotNull() {
            addCriterion("color_code is not null");
            return (Criteria) this;
        }

        public Criteria andColorCodeEqualTo(String value) {
            addCriterion("color_code =", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotEqualTo(String value) {
            addCriterion("color_code <>", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThan(String value) {
            addCriterion("color_code >", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("color_code >=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThan(String value) {
            addCriterion("color_code <", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThanOrEqualTo(String value) {
            addCriterion("color_code <=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLike(String value) {
            addCriterion("color_code like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotLike(String value) {
            addCriterion("color_code not like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeIn(List<String> values) {
            addCriterion("color_code in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotIn(List<String> values) {
            addCriterion("color_code not in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeBetween(String value1, String value2) {
            addCriterion("color_code between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotBetween(String value1, String value2) {
            addCriterion("color_code not between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIsNull() {
            addCriterion("show_base_price is null");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIsNotNull() {
            addCriterion("show_base_price is not null");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceEqualTo(Byte value) {
            addCriterion("show_base_price =", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotEqualTo(Byte value) {
            addCriterion("show_base_price <>", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceGreaterThan(Byte value) {
            addCriterion("show_base_price >", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_base_price >=", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceLessThan(Byte value) {
            addCriterion("show_base_price <", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceLessThanOrEqualTo(Byte value) {
            addCriterion("show_base_price <=", value, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceIn(List<Byte> values) {
            addCriterion("show_base_price in", values, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotIn(List<Byte> values) {
            addCriterion("show_base_price not in", values, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceBetween(Byte value1, Byte value2) {
            addCriterion("show_base_price between", value1, value2, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andShowBasePriceNotBetween(Byte value1, Byte value2) {
            addCriterion("show_base_price not between", value1, value2, "showBasePrice");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNull() {
            addCriterion("book_online_available is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNotNull() {
            addCriterion("book_online_available is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableEqualTo(Byte value) {
            addCriterion("book_online_available =", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotEqualTo(Byte value) {
            addCriterion("book_online_available <>", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThan(Byte value) {
            addCriterion("book_online_available >", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThanOrEqualTo(Byte value) {
            addCriterion("book_online_available >=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThan(Byte value) {
            addCriterion("book_online_available <", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThanOrEqualTo(Byte value) {
            addCriterion("book_online_available <=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIn(List<Byte> values) {
            addCriterion("book_online_available in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotIn(List<Byte> values) {
            addCriterion("book_online_available not in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available not between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIsNull() {
            addCriterion("is_all_staff is null");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIsNotNull() {
            addCriterion("is_all_staff is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffEqualTo(Byte value) {
            addCriterion("is_all_staff =", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotEqualTo(Byte value) {
            addCriterion("is_all_staff <>", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffGreaterThan(Byte value) {
            addCriterion("is_all_staff >", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_all_staff >=", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffLessThan(Byte value) {
            addCriterion("is_all_staff <", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffLessThanOrEqualTo(Byte value) {
            addCriterion("is_all_staff <=", value, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffIn(List<Byte> values) {
            addCriterion("is_all_staff in", values, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotIn(List<Byte> values) {
            addCriterion("is_all_staff not in", values, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffBetween(Byte value1, Byte value2) {
            addCriterion("is_all_staff between", value1, value2, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andIsAllStaffNotBetween(Byte value1, Byte value2) {
            addCriterion("is_all_staff not between", value1, value2, "isAllStaff");
            return (Criteria) this;
        }

        public Criteria andBreedFilterIsNull() {
            addCriterion("breed_filter is null");
            return (Criteria) this;
        }

        public Criteria andBreedFilterIsNotNull() {
            addCriterion("breed_filter is not null");
            return (Criteria) this;
        }

        public Criteria andBreedFilterEqualTo(Byte value) {
            addCriterion("breed_filter =", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterNotEqualTo(Byte value) {
            addCriterion("breed_filter <>", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterGreaterThan(Byte value) {
            addCriterion("breed_filter >", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterGreaterThanOrEqualTo(Byte value) {
            addCriterion("breed_filter >=", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterLessThan(Byte value) {
            addCriterion("breed_filter <", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterLessThanOrEqualTo(Byte value) {
            addCriterion("breed_filter <=", value, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterIn(List<Byte> values) {
            addCriterion("breed_filter in", values, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterNotIn(List<Byte> values) {
            addCriterion("breed_filter not in", values, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterBetween(Byte value1, Byte value2) {
            addCriterion("breed_filter between", value1, value2, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andBreedFilterNotBetween(Byte value1, Byte value2) {
            addCriterion("breed_filter not between", value1, value2, "breedFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterIsNull() {
            addCriterion("weight_filter is null");
            return (Criteria) this;
        }

        public Criteria andWeightFilterIsNotNull() {
            addCriterion("weight_filter is not null");
            return (Criteria) this;
        }

        public Criteria andWeightFilterEqualTo(Byte value) {
            addCriterion("weight_filter =", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterNotEqualTo(Byte value) {
            addCriterion("weight_filter <>", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterGreaterThan(Byte value) {
            addCriterion("weight_filter >", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterGreaterThanOrEqualTo(Byte value) {
            addCriterion("weight_filter >=", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterLessThan(Byte value) {
            addCriterion("weight_filter <", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterLessThanOrEqualTo(Byte value) {
            addCriterion("weight_filter <=", value, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterIn(List<Byte> values) {
            addCriterion("weight_filter in", values, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterNotIn(List<Byte> values) {
            addCriterion("weight_filter not in", values, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterBetween(Byte value1, Byte value2) {
            addCriterion("weight_filter between", value1, value2, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightFilterNotBetween(Byte value1, Byte value2) {
            addCriterion("weight_filter not between", value1, value2, "weightFilter");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitIsNull() {
            addCriterion("weight_down_limit is null");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitIsNotNull() {
            addCriterion("weight_down_limit is not null");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitEqualTo(BigDecimal value) {
            addCriterion("weight_down_limit =", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitNotEqualTo(BigDecimal value) {
            addCriterion("weight_down_limit <>", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitGreaterThan(BigDecimal value) {
            addCriterion("weight_down_limit >", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("weight_down_limit >=", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitLessThan(BigDecimal value) {
            addCriterion("weight_down_limit <", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("weight_down_limit <=", value, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitIn(List<BigDecimal> values) {
            addCriterion("weight_down_limit in", values, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitNotIn(List<BigDecimal> values) {
            addCriterion("weight_down_limit not in", values, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("weight_down_limit between", value1, value2, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightDownLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("weight_down_limit not between", value1, value2, "weightDownLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitIsNull() {
            addCriterion("weight_up_limit is null");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitIsNotNull() {
            addCriterion("weight_up_limit is not null");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitEqualTo(BigDecimal value) {
            addCriterion("weight_up_limit =", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitNotEqualTo(BigDecimal value) {
            addCriterion("weight_up_limit <>", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitGreaterThan(BigDecimal value) {
            addCriterion("weight_up_limit >", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("weight_up_limit >=", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitLessThan(BigDecimal value) {
            addCriterion("weight_up_limit <", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("weight_up_limit <=", value, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitIn(List<BigDecimal> values) {
            addCriterion("weight_up_limit in", values, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitNotIn(List<BigDecimal> values) {
            addCriterion("weight_up_limit not in", values, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("weight_up_limit between", value1, value2, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andWeightUpLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("weight_up_limit not between", value1, value2, "weightUpLimit");
            return (Criteria) this;
        }

        public Criteria andCoatFilterIsNull() {
            addCriterion("coat_filter is null");
            return (Criteria) this;
        }

        public Criteria andCoatFilterIsNotNull() {
            addCriterion("coat_filter is not null");
            return (Criteria) this;
        }

        public Criteria andCoatFilterEqualTo(Byte value) {
            addCriterion("coat_filter =", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterNotEqualTo(Byte value) {
            addCriterion("coat_filter <>", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterGreaterThan(Byte value) {
            addCriterion("coat_filter >", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterGreaterThanOrEqualTo(Byte value) {
            addCriterion("coat_filter >=", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterLessThan(Byte value) {
            addCriterion("coat_filter <", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterLessThanOrEqualTo(Byte value) {
            addCriterion("coat_filter <=", value, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterIn(List<Byte> values) {
            addCriterion("coat_filter in", values, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterNotIn(List<Byte> values) {
            addCriterion("coat_filter not in", values, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterBetween(Byte value1, Byte value2) {
            addCriterion("coat_filter between", value1, value2, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCoatFilterNotBetween(Byte value1, Byte value2) {
            addCriterion("coat_filter not between", value1, value2, "coatFilter");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationIsNull() {
            addCriterion("is_all_location is null");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationIsNotNull() {
            addCriterion("is_all_location is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationEqualTo(Byte value) {
            addCriterion("is_all_location =", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationNotEqualTo(Byte value) {
            addCriterion("is_all_location <>", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationGreaterThan(Byte value) {
            addCriterion("is_all_location >", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_all_location >=", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationLessThan(Byte value) {
            addCriterion("is_all_location <", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationLessThanOrEqualTo(Byte value) {
            addCriterion("is_all_location <=", value, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationIn(List<Byte> values) {
            addCriterion("is_all_location in", values, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationNotIn(List<Byte> values) {
            addCriterion("is_all_location not in", values, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationBetween(Byte value1, Byte value2) {
            addCriterion("is_all_location between", value1, value2, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andIsAllLocationNotBetween(Byte value1, Byte value2) {
            addCriterion("is_all_location not between", value1, value2, "isAllLocation");
            return (Criteria) this;
        }

        public Criteria andImagesIsNull() {
            addCriterion("images is null");
            return (Criteria) this;
        }

        public Criteria andImagesIsNotNull() {
            addCriterion("images is not null");
            return (Criteria) this;
        }

        public Criteria andImagesEqualTo(String value) {
            addCriterion("images =", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotEqualTo(String value) {
            addCriterion("images <>", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesGreaterThan(String value) {
            addCriterion("images >", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesGreaterThanOrEqualTo(String value) {
            addCriterion("images >=", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLessThan(String value) {
            addCriterion("images <", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLessThanOrEqualTo(String value) {
            addCriterion("images <=", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLike(String value) {
            addCriterion("images like", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotLike(String value) {
            addCriterion("images not like", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesIn(List<String> values) {
            addCriterion("images in", values, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotIn(List<String> values) {
            addCriterion("images not in", values, "images");
            return (Criteria) this;
        }

        public Criteria andImagesBetween(String value1, String value2) {
            addCriterion("images between", value1, value2, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotBetween(String value1, String value2) {
            addCriterion("images not between", value1, value2, "images");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNull() {
            addCriterion("service_item_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIsNotNull() {
            addCriterion("service_item_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeEqualTo(Integer value) {
            addCriterion("service_item_type =", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotEqualTo(Integer value) {
            addCriterion("service_item_type <>", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThan(Integer value) {
            addCriterion("service_item_type >", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_item_type >=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThan(Integer value) {
            addCriterion("service_item_type <", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeLessThanOrEqualTo(Integer value) {
            addCriterion("service_item_type <=", value, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeIn(List<Integer> values) {
            addCriterion("service_item_type in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotIn(List<Integer> values) {
            addCriterion("service_item_type not in", values, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andServiceItemTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_item_type not between", value1, value2, "serviceItemType");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNull() {
            addCriterion("price_unit is null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIsNotNull() {
            addCriterion("price_unit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceUnitEqualTo(Integer value) {
            addCriterion("price_unit =", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotEqualTo(Integer value) {
            addCriterion("price_unit <>", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThan(Integer value) {
            addCriterion("price_unit >", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("price_unit >=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThan(Integer value) {
            addCriterion("price_unit <", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitLessThanOrEqualTo(Integer value) {
            addCriterion("price_unit <=", value, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitIn(List<Integer> values) {
            addCriterion("price_unit in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotIn(List<Integer> values) {
            addCriterion("price_unit not in", values, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitBetween(Integer value1, Integer value2) {
            addCriterion("price_unit between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andPriceUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("price_unit not between", value1, value2, "priceUnit");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionIsNull() {
            addCriterion("add_to_commission is null");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionIsNotNull() {
            addCriterion("add_to_commission is not null");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionEqualTo(Boolean value) {
            addCriterion("add_to_commission =", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionNotEqualTo(Boolean value) {
            addCriterion("add_to_commission <>", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionGreaterThan(Boolean value) {
            addCriterion("add_to_commission >", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("add_to_commission >=", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionLessThan(Boolean value) {
            addCriterion("add_to_commission <", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionLessThanOrEqualTo(Boolean value) {
            addCriterion("add_to_commission <=", value, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionIn(List<Boolean> values) {
            addCriterion("add_to_commission in", values, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionNotIn(List<Boolean> values) {
            addCriterion("add_to_commission not in", values, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionBetween(Boolean value1, Boolean value2) {
            addCriterion("add_to_commission between", value1, value2, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andAddToCommissionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("add_to_commission not between", value1, value2, "addToCommission");
            return (Criteria) this;
        }

        public Criteria andCanTipIsNull() {
            addCriterion("can_tip is null");
            return (Criteria) this;
        }

        public Criteria andCanTipIsNotNull() {
            addCriterion("can_tip is not null");
            return (Criteria) this;
        }

        public Criteria andCanTipEqualTo(Boolean value) {
            addCriterion("can_tip =", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipNotEqualTo(Boolean value) {
            addCriterion("can_tip <>", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipGreaterThan(Boolean value) {
            addCriterion("can_tip >", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipGreaterThanOrEqualTo(Boolean value) {
            addCriterion("can_tip >=", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipLessThan(Boolean value) {
            addCriterion("can_tip <", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipLessThanOrEqualTo(Boolean value) {
            addCriterion("can_tip <=", value, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipIn(List<Boolean> values) {
            addCriterion("can_tip in", values, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipNotIn(List<Boolean> values) {
            addCriterion("can_tip not in", values, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipBetween(Boolean value1, Boolean value2) {
            addCriterion("can_tip between", value1, value2, "canTip");
            return (Criteria) this;
        }

        public Criteria andCanTipNotBetween(Boolean value1, Boolean value2) {
            addCriterion("can_tip not between", value1, value2, "canTip");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffIsNull() {
            addCriterion("require_dedicated_staff is null");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffIsNotNull() {
            addCriterion("require_dedicated_staff is not null");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffEqualTo(Boolean value) {
            addCriterion("require_dedicated_staff =", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffNotEqualTo(Boolean value) {
            addCriterion("require_dedicated_staff <>", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffGreaterThan(Boolean value) {
            addCriterion("require_dedicated_staff >", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffGreaterThanOrEqualTo(Boolean value) {
            addCriterion("require_dedicated_staff >=", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffLessThan(Boolean value) {
            addCriterion("require_dedicated_staff <", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffLessThanOrEqualTo(Boolean value) {
            addCriterion("require_dedicated_staff <=", value, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffIn(List<Boolean> values) {
            addCriterion("require_dedicated_staff in", values, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffNotIn(List<Boolean> values) {
            addCriterion("require_dedicated_staff not in", values, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffBetween(Boolean value1, Boolean value2) {
            addCriterion("require_dedicated_staff between", value1, value2, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedStaffNotBetween(Boolean value1, Boolean value2) {
            addCriterion("require_dedicated_staff not between", value1, value2, "requireDedicatedStaff");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingIsNull() {
            addCriterion("require_dedicated_lodging is null");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingIsNotNull() {
            addCriterion("require_dedicated_lodging is not null");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingEqualTo(Boolean value) {
            addCriterion("require_dedicated_lodging =", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingNotEqualTo(Boolean value) {
            addCriterion("require_dedicated_lodging <>", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingGreaterThan(Boolean value) {
            addCriterion("require_dedicated_lodging >", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("require_dedicated_lodging >=", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingLessThan(Boolean value) {
            addCriterion("require_dedicated_lodging <", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingLessThanOrEqualTo(Boolean value) {
            addCriterion("require_dedicated_lodging <=", value, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingIn(List<Boolean> values) {
            addCriterion("require_dedicated_lodging in", values, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingNotIn(List<Boolean> values) {
            addCriterion("require_dedicated_lodging not in", values, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingBetween(Boolean value1, Boolean value2) {
            addCriterion("require_dedicated_lodging between", value1, value2, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andRequireDedicatedLodgingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("require_dedicated_lodging not between", value1, value2, "requireDedicatedLodging");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterIsNull() {
            addCriterion("lodging_filter is null");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterIsNotNull() {
            addCriterion("lodging_filter is not null");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterEqualTo(Boolean value) {
            addCriterion("lodging_filter =", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterNotEqualTo(Boolean value) {
            addCriterion("lodging_filter <>", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterGreaterThan(Boolean value) {
            addCriterion("lodging_filter >", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterGreaterThanOrEqualTo(Boolean value) {
            addCriterion("lodging_filter >=", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterLessThan(Boolean value) {
            addCriterion("lodging_filter <", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterLessThanOrEqualTo(Boolean value) {
            addCriterion("lodging_filter <=", value, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterIn(List<Boolean> values) {
            addCriterion("lodging_filter in", values, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterNotIn(List<Boolean> values) {
            addCriterion("lodging_filter not in", values, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterBetween(Boolean value1, Boolean value2) {
            addCriterion("lodging_filter between", value1, value2, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andLodgingFilterNotBetween(Boolean value1, Boolean value2) {
            addCriterion("lodging_filter not between", value1, value2, "lodgingFilter");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListIsNull() {
            addCriterion("allowed_lodging_list is null");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListIsNotNull() {
            addCriterion("allowed_lodging_list is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListEqualTo(String value) {
            addCriterion("allowed_lodging_list =", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListNotEqualTo(String value) {
            addCriterion("allowed_lodging_list <>", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListGreaterThan(String value) {
            addCriterion("allowed_lodging_list >", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListGreaterThanOrEqualTo(String value) {
            addCriterion("allowed_lodging_list >=", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListLessThan(String value) {
            addCriterion("allowed_lodging_list <", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListLessThanOrEqualTo(String value) {
            addCriterion("allowed_lodging_list <=", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListLike(String value) {
            addCriterion("allowed_lodging_list like", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListNotLike(String value) {
            addCriterion("allowed_lodging_list not like", value, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListIn(List<String> values) {
            addCriterion("allowed_lodging_list in", values, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListNotIn(List<String> values) {
            addCriterion("allowed_lodging_list not in", values, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListBetween(String value1, String value2) {
            addCriterion("allowed_lodging_list between", value1, value2, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andAllowedLodgingListNotBetween(String value1, String value2) {
            addCriterion("allowed_lodging_list not between", value1, value2, "allowedLodgingList");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIsNull() {
            addCriterion("service_filter is null");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIsNotNull() {
            addCriterion("service_filter is not null");
            return (Criteria) this;
        }

        public Criteria andServiceFilterEqualTo(Boolean value) {
            addCriterion("service_filter =", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotEqualTo(Boolean value) {
            addCriterion("service_filter <>", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterGreaterThan(Boolean value) {
            addCriterion("service_filter >", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterGreaterThanOrEqualTo(Boolean value) {
            addCriterion("service_filter >=", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterLessThan(Boolean value) {
            addCriterion("service_filter <", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterLessThanOrEqualTo(Boolean value) {
            addCriterion("service_filter <=", value, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterIn(List<Boolean> values) {
            addCriterion("service_filter in", values, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotIn(List<Boolean> values) {
            addCriterion("service_filter not in", values, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterBetween(Boolean value1, Boolean value2) {
            addCriterion("service_filter between", value1, value2, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andServiceFilterNotBetween(Boolean value1, Boolean value2) {
            addCriterion("service_filter not between", value1, value2, "serviceFilter");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListIsNull() {
            addCriterion("allowed_pet_size_list is null");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListIsNotNull() {
            addCriterion("allowed_pet_size_list is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListEqualTo(String value) {
            addCriterion("allowed_pet_size_list =", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListNotEqualTo(String value) {
            addCriterion("allowed_pet_size_list <>", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListGreaterThan(String value) {
            addCriterion("allowed_pet_size_list >", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListGreaterThanOrEqualTo(String value) {
            addCriterion("allowed_pet_size_list >=", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListLessThan(String value) {
            addCriterion("allowed_pet_size_list <", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListLessThanOrEqualTo(String value) {
            addCriterion("allowed_pet_size_list <=", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListLike(String value) {
            addCriterion("allowed_pet_size_list like", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListNotLike(String value) {
            addCriterion("allowed_pet_size_list not like", value, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListIn(List<String> values) {
            addCriterion("allowed_pet_size_list in", values, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListNotIn(List<String> values) {
            addCriterion("allowed_pet_size_list not in", values, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListBetween(String value1, String value2) {
            addCriterion("allowed_pet_size_list between", value1, value2, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andAllowedPetSizeListNotBetween(String value1, String value2) {
            addCriterion("allowed_pet_size_list not between", value1, value2, "allowedPetSizeList");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterIsNull() {
            addCriterion("pet_size_filter is null");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterIsNotNull() {
            addCriterion("pet_size_filter is not null");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterEqualTo(Boolean value) {
            addCriterion("pet_size_filter =", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterNotEqualTo(Boolean value) {
            addCriterion("pet_size_filter <>", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterGreaterThan(Boolean value) {
            addCriterion("pet_size_filter >", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterGreaterThanOrEqualTo(Boolean value) {
            addCriterion("pet_size_filter >=", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterLessThan(Boolean value) {
            addCriterion("pet_size_filter <", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterLessThanOrEqualTo(Boolean value) {
            addCriterion("pet_size_filter <=", value, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterIn(List<Boolean> values) {
            addCriterion("pet_size_filter in", values, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterNotIn(List<Boolean> values) {
            addCriterion("pet_size_filter not in", values, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterBetween(Boolean value1, Boolean value2) {
            addCriterion("pet_size_filter between", value1, value2, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andPetSizeFilterNotBetween(Boolean value1, Boolean value2) {
            addCriterion("pet_size_filter not between", value1, value2, "petSizeFilter");
            return (Criteria) this;
        }

        public Criteria andMaxDurationIsNull() {
            addCriterion("max_duration is null");
            return (Criteria) this;
        }

        public Criteria andMaxDurationIsNotNull() {
            addCriterion("max_duration is not null");
            return (Criteria) this;
        }

        public Criteria andMaxDurationEqualTo(Integer value) {
            addCriterion("max_duration =", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationNotEqualTo(Integer value) {
            addCriterion("max_duration <>", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationGreaterThan(Integer value) {
            addCriterion("max_duration >", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_duration >=", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationLessThan(Integer value) {
            addCriterion("max_duration <", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationLessThanOrEqualTo(Integer value) {
            addCriterion("max_duration <=", value, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationIn(List<Integer> values) {
            addCriterion("max_duration in", values, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationNotIn(List<Integer> values) {
            addCriterion("max_duration not in", values, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationBetween(Integer value1, Integer value2) {
            addCriterion("max_duration between", value1, value2, "maxDuration");
            return (Criteria) this;
        }

        public Criteria andMaxDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("max_duration not between", value1, value2, "maxDuration");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_service
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
