package com.moego.server.grooming.service.ob;

import static com.moego.server.grooming.service.DepositService.PREAUTH_PREFIX;

import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.grooming.dto.ob.AssociateCustomerDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import com.moego.server.grooming.params.ob.PrepayDetailParams;
import com.moego.server.grooming.service.MoeBookOnlineDepositService;
import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.dto.CustomerStripInfoSaveResponse;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/12/14
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrepayService {

    private final MoeBookOnlineDepositService moeBookOnlineDepositService;
    private final OBAbandonRecordService abandonRecordService;

    private final IPaymentPaymentClient iPaymentPaymentClient;
    private final IPaymentStripeClient iPaymentStripeClient;
    private final IPaymentCreditCardClient iPaymentService;
    private final ICustomerCustomerClient iCustomerCustomerClient;

    /**
     * 根据 customer id 存储卡信息
     *
     * @param chargeToken charge token
     * @param businessId  business id
     * @param customerId  customer id
     * @param isNewCustomer is new client
     */
    public CustomerStripInfoSaveResponse saveCard(
            String chargeToken, Integer businessId, Integer customerId, boolean isNewCustomer) {
        if (!StringUtils.hasText(chargeToken)) {
            return null;
        }
        try {
            CustomerStripInfoRequest stripInfoRequest = new CustomerStripInfoRequest();
            stripInfoRequest.setChargeToken(chargeToken);
            stripInfoRequest.setCustomerId(customerId);
            return iPaymentService.saveNewCard(businessId, stripInfoRequest);
        } catch (Exception exception) {
            // 加卡失败删除新注册的用户
            if (customerId != null && isNewCustomer) {
                Integer count = iCustomerCustomerClient.deleteCustomer(businessId, customerId);
                abandonRecordService.unAssociateCustomer(new AssociateCustomerDTO(businessId, customerId, null));
                log.info("delete customer[{}] for save charge token failed. count={}", customerId, count);
            }
            throw exception;
        }
    }

    /**
     * 更新deposit、绑定payment记录、绑定stripeCustomerId
     *
     * @param obDeposit
     * @param obParams
     * @param invoiceId
     */
    public void updateDepositAndPaymentRecord(
            MoeBookOnlineDeposit obDeposit, BookOnlineSubmitParams obParams, @Nullable Integer invoiceId) {
        BookOnlineCustomerParams customerData = obParams.getCustomerData();
        // ob deposit记录更新groomingId，状态更新为require capture
        MoeBookOnlineDeposit update = new MoeBookOnlineDeposit();
        update.setId(obDeposit.getId());
        update.setGroomingId(obDeposit.getGroomingId());
        update.setStatus(BookOnlineDepositConst.REQUIRE_CAPTURE);
        PrepayDetailParams prepayDetail = obParams.getPrepayDetail();
        if (Objects.nonNull(prepayDetail)) {
            update.setServiceTotal(prepayDetail.getServiceTotal());
            update.setTaxAmount(prepayDetail.getTaxAmount());
            update.setServiceChargeAmount(prepayDetail.getServiceChargeAmount());
        }
        DiscountCodeParams discountCodeParams = obParams.getDiscountCodeParams();
        if (Objects.nonNull(discountCodeParams)) {
            update.setDiscountCodeId(discountCodeParams.getDiscountCodeId());
            update.setDiscountAmount(discountCodeParams.getDiscountAmount());
        }
        moeBookOnlineDepositService.update(update);

        // 把invoiceId更新到payment记录中
        // 之前的逻辑是异步执行，但其实没必要
        var paymentDTO = new PaymentDTO();
        if (invoiceId != null) {
            paymentDTO.setInvoiceId(invoiceId);
        }
        paymentDTO.setCustomerId(customerData.getCustomerId());
        paymentDTO.setId(obDeposit.getPaymentId());
        paymentDTO.setStatus(PaymentStatusEnum.PROCESSING);
        paymentDTO.setGroomingId(obDeposit.getGroomingId());
        iPaymentPaymentClient.updatePaymentRecord(paymentDTO);

        // 绑定stripe customer
        if (StringUtils.hasText(customerData.getStripeCustomerId())) {
            iPaymentStripeClient.saveStripeCustomer(
                    obDeposit.getBusinessId(), customerData.getCustomerId(), customerData.getStripeCustomerId());
        }
    }

    public void insertPreAuthRecord(BookOnlineSubmitParams obParams, Integer groomingId) {
        PreAuthDetailParams preAuthDetail = obParams.getPreAuthDetail();
        // ob deposit记录更新groomingId，状态更新为require capture
        MoeBookOnlineDeposit preAuth = new MoeBookOnlineDeposit();
        preAuth.setGroomingId(groomingId);
        preAuth.setStatus(BookOnlineDepositConst.PROCESSING);
        preAuth.setBusinessId(obParams.getBusinessId());
        preAuth.setCompanyId(obParams.getCompanyId());
        preAuth.setServiceTotal(
                preAuthDetail.getServiceTotal() == null ? BigDecimal.ZERO : preAuthDetail.getServiceTotal());
        preAuth.setTaxAmount(preAuthDetail.getTaxAmount() == null ? BigDecimal.ZERO : preAuthDetail.getTaxAmount());
        preAuth.setTipsAmount(preAuthDetail.getTipsAmount() == null ? BigDecimal.ZERO : preAuthDetail.getTipsAmount());
        preAuth.setServiceChargeAmount(
                preAuthDetail.getServiceChargeAmount() == null
                        ? BigDecimal.ZERO
                        : preAuthDetail.getServiceChargeAmount());
        preAuth.setBookingFee(preAuthDetail.getBookingFee() == null ? BigDecimal.ZERO : preAuthDetail.getBookingFee());
        preAuth.setGuid(PREAUTH_PREFIX + CommonUtil.getUuid());
        BigDecimal total = preAuthDetail
                .getServiceTotal()
                .add(preAuth.getTaxAmount())
                .add(preAuth.getTipsAmount())
                .add(preAuth.getServiceChargeAmount())
                .add(preAuth.getBookingFee());
        preAuth.setAmount(total);
        preAuth.setDepositType(DepositPaymentTypeEnum.PreAuth);
        DiscountCodeParams discountCodeParams = obParams.getDiscountCodeParams();
        if (Objects.nonNull(discountCodeParams)) {
            preAuth.setDiscountCodeId(discountCodeParams.getDiscountCodeId());
            preAuth.setDiscountAmount(discountCodeParams.getDiscountAmount());
        }
        moeBookOnlineDepositService.insertPreAuth(preAuth);
    }

    /**
     * Get grooming prepay detail
     *
     * @param businessId businessId
     * @param groomingId groomingId
     */
    public OBPrepayDetailDTO getPrepayDetail(Integer businessId, Integer groomingId, OrderModel order) {
        if (Objects.isNull(order)) {
            return null;
        }
        OBPrepayDetailDTO prepayDetailDTO = new OBPrepayDetailDTO()
                .setPaidAmount(BigDecimal.valueOf(order.getPaidAmount()))
                .setRefundAmount(BigDecimal.valueOf(order.getRefundedAmount()));
        // 查询定金记录
        MoeBookOnlineDeposit deposit = moeBookOnlineDepositService.getOBDepositByGroomingId(businessId, groomingId);
        if (!Objects.isNull(deposit)) {
            if (DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                prepayDetailDTO
                        .setPrepaidAmount(deposit.getAmount())
                        .setPrepayStatus(deposit.getStatus())
                        .setPrepayRate(moeBookOnlineDepositService.getPrepayRate(
                                deposit, BigDecimal.valueOf(order.getTotalAmount())));
            } else {
                prepayDetailDTO.setEnablePreAuth(true);
            }
        }
        return prepayDetailDTO;
    }

    public Map<Integer, OBPrepayDetailDTO> batchGetPrepayDetail(
            Integer businessId, Map<Integer, OrderModel> appointmentIdOrderMap) {
        Map<Integer, OBPrepayDetailDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(appointmentIdOrderMap)) {
            return result;
        }
        // 查询定金记录
        List<Integer> groomingIds = appointmentIdOrderMap.keySet().stream().toList();
        Map<Integer, MoeBookOnlineDeposit> deposits =
                moeBookOnlineDepositService.getOBDepositByGroomingIds(businessId, new HashSet<>(groomingIds)).stream()
                        .collect(Collectors.toMap(
                                MoeBookOnlineDeposit::getGroomingId, Function.identity(), (k1, k2) -> k2));
        appointmentIdOrderMap.forEach((key, order) -> {
            OBPrepayDetailDTO prepayDetailDTO = new OBPrepayDetailDTO()
                    .setPaidAmount(BigDecimal.valueOf(order.getPaidAmount()))
                    .setRefundAmount(BigDecimal.valueOf(order.getRefundedAmount()));
            MoeBookOnlineDeposit deposit = deposits.get(key);
            if (!Objects.isNull(deposit)) {
                if (DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                    prepayDetailDTO
                            .setPrepaidAmount(deposit.getAmount())
                            .setPrepayStatus(deposit.getStatus())
                            .setPrepayRate(moeBookOnlineDepositService.getPrepayRate(
                                    deposit, BigDecimal.valueOf(order.getTotalAmount())));
                } else {
                    prepayDetailDTO.setEnablePreAuth(true);
                }
            }
            result.put(key, prepayDetailDTO);
        });
        return result;
    }
}
