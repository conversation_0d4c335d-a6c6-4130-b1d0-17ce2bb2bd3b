package com.moego.server.grooming.mapperbean;

import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_accept_pet_type
 */
public class MoeBookOnlineAcceptPetType {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   company id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     * Database Column Remarks:
     *   pet type id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.pet_type_id
     *
     * @mbg.generated
     */
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   0-false, 1-true
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.accepted
     *
     * @mbg.generated
     */
    private Integer accepted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_accept_pet_type.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.id
     *
     * @return the value of moe_book_online_accept_pet_type.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.id
     *
     * @param id the value for moe_book_online_accept_pet_type.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.company_id
     *
     * @return the value of moe_book_online_accept_pet_type.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.company_id
     *
     * @param companyId the value for moe_book_online_accept_pet_type.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.business_id
     *
     * @return the value of moe_book_online_accept_pet_type.business_id
     *
     * @mbg.generated
     */
    public Long getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.business_id
     *
     * @param businessId the value for moe_book_online_accept_pet_type.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.pet_type_id
     *
     * @return the value of moe_book_online_accept_pet_type.pet_type_id
     *
     * @mbg.generated
     */
    public Integer getPetTypeId() {
        return petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.pet_type_id
     *
     * @param petTypeId the value for moe_book_online_accept_pet_type.pet_type_id
     *
     * @mbg.generated
     */
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.accepted
     *
     * @return the value of moe_book_online_accept_pet_type.accepted
     *
     * @mbg.generated
     */
    public Integer getAccepted() {
        return accepted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.accepted
     *
     * @param accepted the value for moe_book_online_accept_pet_type.accepted
     *
     * @mbg.generated
     */
    public void setAccepted(Integer accepted) {
        this.accepted = accepted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.created_at
     *
     * @return the value of moe_book_online_accept_pet_type.created_at
     *
     * @mbg.generated
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.created_at
     *
     * @param createdAt the value for moe_book_online_accept_pet_type.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_accept_pet_type.updated_at
     *
     * @return the value of moe_book_online_accept_pet_type.updated_at
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_accept_pet_type.updated_at
     *
     * @param updatedAt the value for moe_book_online_accept_pet_type.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
