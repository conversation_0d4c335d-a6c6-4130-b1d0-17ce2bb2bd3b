package com.moego.server.grooming.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.KeyModel;
import com.moego.idl.models.metadata.v1.ValueModel;
import com.moego.idl.service.metadata.v1.DescribeValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import io.grpc.StatusRuntimeException;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MetadataHelper {

    private static final String PACKAGE_WHITELIST = "package_enable_config";

    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    public boolean isInPackageWhitelist(long businessId) {
        return isFeatureEnabledWithIntValue(PACKAGE_WHITELIST, businessId);
    }

    boolean isFeatureEnabledWithIntValue(String feature, long ownerId) {
        var key = getKey(feature);
        if (key == null) {
            return false;
        }
        return isFeatureEnabled(key.getId(), ownerId);
    }

    boolean isFeatureEnabled(long keyId, long ownerId) {
        return metadataServiceBlockingStub
                .describeValues(DescribeValuesRequest.newBuilder()
                        .setKeyId(keyId)
                        .addOwnerIds(ownerId)
                        .build())
                .getValuesList()
                .stream()
                .map(ValueModel::getValue)
                .anyMatch(value -> "1".equals(value) || "true".equalsIgnoreCase(value));
    }

    @Nullable
    /*private*/ KeyModel getKey(String feature) {
        try {
            return metadataServiceBlockingStub
                    .getKey(GetKeyRequest.newBuilder().setName(feature).build())
                    .getKey();
        } catch (StatusRuntimeException e) {
            if (ExceptionUtil.extractCode(e) != Code.CODE_PARAMS_ERROR) {
                throw e;
            }
            return null;
        }
    }
}
