package com.moego.server.grooming.service;

import static com.moego.common.enums.GroomingAppointmentEnum.CANCEL_TYPE_BY_CLIENT;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.common.utils.PermissionUtil.CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST;
import static com.moego.common.utils.PermissionUtil.checkStaffPermissionsInfo;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moego.common.distributed.BWListManager;
import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.ConflictTypeEnum;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.QuestionConst;
import com.moego.common.enums.RepeatConst;
import com.moego.common.enums.RepeatModifyTypeEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ReviewBoosterConst;
import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PermissionUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.OutboxSendStatus;
import com.moego.idl.models.appointment.v1.StaffLocationStatus;
import com.moego.idl.models.appointment.v1.UpdateAppointmentTrackingDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentTrackingServiceGrpc;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.DeleteAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.GetOrInitAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.PatchTasksByAppointmentRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.appointment.v1.RescheduleBoardingAppointmentRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v1.UpsertAppointmentTrackingRequest;
import com.moego.idl.service.appointment.v2.ApplyPricingRuleRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.client.IPetCodeClient;
import com.moego.server.customer.client.IPetVaccineClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.CustomerPrimaryDto;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import com.moego.server.customer.dto.VaccineStatusDto;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.config.SmartScheduleConfiguration;
import com.moego.server.grooming.constant.GroomingNoteConstant;
import com.moego.server.grooming.convert.AppointmentConverter;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.AppointmentDateWithServiceDurationDto;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.BookOnlineQuestionSaveDto;
import com.moego.server.grooming.dto.BookOnlineRequestCountDto;
import com.moego.server.grooming.dto.CalendarConflictDTO;
import com.moego.server.grooming.dto.ConflictCheckDTO;
import com.moego.server.grooming.dto.ConflictDayInfoDTO;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.EvaluationServiceDTO;
import com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO;
import com.moego.server.grooming.dto.GroomingCustomerInfoDTO;
import com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO;
import com.moego.server.grooming.dto.GroomingEvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingIdPetIdDto;
import com.moego.server.grooming.dto.GroomingInvoiceDTO;
import com.moego.server.grooming.dto.GroomingPetCodeDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.GroomingTicketDetailDTO;
import com.moego.server.grooming.dto.HistoryCommentsDTO;
import com.moego.server.grooming.dto.InvoiceServiceDTO;
import com.moego.server.grooming.dto.LodgingInfo;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.PetDetailInvoiceDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.StaffBlockInfoDTO;
import com.moego.server.grooming.dto.StaffConflictDTO;
import com.moego.server.grooming.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.dto.WaitingListPetDetailDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.dto.appointment.history.CustomerReplyLogDTO;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.dto.appointment.history.UpdateStatusLogDTO;
import com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.AbandonRecordRecoverTypeEnum;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentConfirmTypeEnum;
import com.moego.server.grooming.enums.AppointmentPermissionEnums;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.GroomingHistoryType;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.enums.PickupNotificationStatusEnum;
import com.moego.server.grooming.enums.WaitListStatusEnum;
import com.moego.server.grooming.eventbus.AppointmentProducer;
import com.moego.server.grooming.eventbus.OnlineBookingProducer;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.listener.event.UpdateCustomerEvent;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapper.param.UpdateAbandonedRecordByCustomerIdParam;
import com.moego.server.grooming.mapper.po.TakeCaredPetPo;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingNoteExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.mapstruct.DatePreferenceMapper;
import com.moego.server.grooming.mapstruct.RepeatMapper;
import com.moego.server.grooming.mapstruct.TimePreferenceMapper;
import com.moego.server.grooming.params.AddRepeatParams;
import com.moego.server.grooming.params.AppointmentBlockParams;
import com.moego.server.grooming.params.AppointmentBlockRepeatParams;
import com.moego.server.grooming.params.AppointmentCheckParams;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.AppointmentRepeatModifyParams;
import com.moego.server.grooming.params.AppointmentRepeatParams;
import com.moego.server.grooming.params.BlockRepeatParams;
import com.moego.server.grooming.params.BookOnlineDepositV2Params;
import com.moego.server.grooming.params.CalendarCheckParams;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.CheckParams;
import com.moego.server.grooming.params.ColorEditParams;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.grooming.params.CustomerDeleteParams;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import com.moego.server.grooming.params.DeleteAppointmentParams;
import com.moego.server.grooming.params.EditCommentsParams;
import com.moego.server.grooming.params.EditContextParams;
import com.moego.server.grooming.params.EditIdParams;
import com.moego.server.grooming.params.IdParams;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.RepeatStaffInfoParams;
import com.moego.server.grooming.params.TransferAppointmentParams;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.grooming.params.status.StatusRevertParams;
import com.moego.server.grooming.params.status.StatusUpdateParams;
import com.moego.server.grooming.service.dto.CustomerAppointmentListDTO;
import com.moego.server.grooming.service.dto.CustomerAppointmentListStaffDTO;
import com.moego.server.grooming.service.dto.CustomizedServiceParamDTO;
import com.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto;
import com.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.remote.ServiceManagementService;
import com.moego.server.grooming.service.report.ReportOrderService;
import com.moego.server.grooming.service.statemachine.action.IStateTransitionAction;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import com.moego.server.grooming.service.statemachine.context.ConfirmActionContext;
import com.moego.server.grooming.service.utils.AppointmentUtil;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.service.utils.RepeatUtil;
import com.moego.server.grooming.utils.AddressUtil;
import com.moego.server.grooming.utils.AppointmentUtils;
import com.moego.server.grooming.utils.PetDetailUtil;
import com.moego.server.grooming.web.dto.Business2021SummaryDto;
import com.moego.server.grooming.web.dto.SummaryDto;
import com.moego.server.grooming.web.dto.ThanksgivingResultDto;
import com.moego.server.grooming.web.params.MoeGroomingAppointmentSpecialParam;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import com.moego.server.grooming.web.vo.GroomingCustomerQueryVO;
import com.moego.server.grooming.web.vo.UpcomingPreviewVo;
import com.moego.server.grooming.web.vo.UpdateStatusResultVO;
import com.moego.server.grooming.web.vo.WaitingPetDetailVO;
import com.moego.server.message.client.IBoosterClient;
import com.moego.server.message.client.IMessageSendClient;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.client.IReviewBoosterTaskClient;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.enums.ClientReplyTypeEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.params.GetReviewBoosterRecordParams;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.notification.NotificationApptAssignedParams;
import com.moego.server.message.params.notification.NotificationApptCancelledByClientParams;
import com.moego.server.message.params.notification.NotificationApptCancelledParams;
import com.moego.server.message.params.notification.NotificationApptConfirmedByClientParams;
import com.moego.server.message.params.notification.NotificationApptRescheduledParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestCanceledParams;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.client.IPaymentSubscriptionClient;
import com.moego.server.payment.dto.CompanyPermissionStateDto;
import com.moego.server.payment.dto.MoeGoPayTransactionSummaryDto;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class MoeGroomingAppointmentService {

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private MoeGroomingServiceOperationMapper serviceOperationMapper;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private TicketCommentService ticketCommentService;

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ReportOrderService reportOrderService;

    @Autowired
    private SplitTipsService splitTipsService;

    @Autowired
    private IReviewBoosterTaskClient iReviewBoosterTaskClient;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private IPaymentRefundClient iPaymentRefundClient;

    @Autowired
    private QuickBooksService quickBooksService;

    @Autowired
    private CalendarSyncService calendarSyncService;

    @Autowired
    private MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Autowired
    private MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private MoeRepeatService moeRepeatService;

    @Autowired
    private IPaymentSubscriptionClient iPaymentService;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private IPetVaccineClient iPetVaccineClient;

    @Autowired
    private IPetCodeClient iPetCodeClient;

    @Autowired
    private IBoosterClient iBoosterClient;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private IPaymentPaymentClient paymentPaymentClient;

    @Resource
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private AgreementServiceGrpc.AgreementServiceBlockingStub agreementClient;

    @Autowired
    private GroomingApptAsyncService asyncService;

    @Autowired
    private IPaymentPreAuthClient paymentPreAuthClient;

    @Autowired
    private ActiveMQService mqService;

    @Autowired
    private MoeAppointmentQueryService appointmentQueryService;

    @Autowired
    private MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Autowired
    private OBGroomingService obGroomingService;

    @Autowired
    private MoeGroomingNoteMapper groomingNoteMapper;

    @Autowired
    private List<IStateTransitionAction> stateTransitionActions;

    @Autowired
    private IMessageSendClient messageSendClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private AutoAssignService autoAssignService;

    @Autowired
    private WaitListService waitListService;

    @Autowired
    private IBusinessStaffService businessStaffService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Autowired
    private ServiceManagementService serviceManagementService;

    @Autowired
    private EvaluationServiceDetailService evaluationServiceDetailService;

    @Autowired
    private LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitService;

    @Autowired
    private AppointmentRepeatService appointmentRepeatService;

    @Autowired
    private BrandedAppNotificationService brandedAppNotificationService;

    @Autowired
    private AppointmentServiceDetailService serviceDetailService;

    @Autowired
    private BWListManager bwListManager;

    @Autowired
    private PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyService;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private AppointmentTrackingServiceGrpc.AppointmentTrackingServiceBlockingStub
            appointmentTrackingServiceBlockingStub;

    @Autowired
    private Producer producer;

    @Autowired
    private OnlineBookingProducer onlineBookingProducer;

    @Autowired
    private BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;

    @Autowired
    private AppointmentProducer appointmentProducer;

    @Autowired
    private AppointmentOutboxService appointmentOutboxService;

    @Autowired
    private OrderHelper orderHelper;

    // appoinment tracking 用
    public static final List<AppointmentStatusEnum> ArrivedStatus =
            List.of(AppointmentStatusEnum.CHECK_IN, AppointmentStatusEnum.READY, AppointmentStatusEnum.FINISHED);

    @Autowired
    private AppointmentTaskServiceGrpc.AppointmentTaskServiceBlockingStub appointmentTaskStub;

    @Autowired
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    @Autowired
    private SmartScheduleService smartScheduleService;

    @Autowired
    @Qualifier(value = SmartScheduleConfiguration.SMART_SCHEDULE_EXECUTOR_SERVICE)
    private ExecutorService executorService;

    @Autowired
    private BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;

    @Autowired
    private ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogStub;

    @Autowired
    private NewOrderHelper newOrderHelper;

    /**
     * 免费用户或者老老版本用户只能限制100个预约，在添加预约时检查
     * <p>
     * 1、老老版本付费用户、根据billing cycle的周期查询当前周期内是否创建了100个，已经创建了100个，那么就不允许在日历上创建预约了，不管在哪天创建都不允许
     * 2、免费用户、根据月份查询，比如今天是4月13号，那么会查询(4月1号-4月30)周期内的预约，大于100个，就不允许在日历上创建预约了，不管在哪天创建都不允许
     *
     * @param businessId
     */
    public void preconditionCheckingForFreeBiz(Integer businessId) {
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        if (companyDto == null || companyDto.getId() == null || companyDto.getId() == 0) {
            // 错误数据，不应该存在
            throw new CommonException(ResponseCodeEnum.TOO_MANY_APPOINTMENTS);
        } else {
            CompanyFunctionControlDto controlDto = AccountUtil.getCompanyControlDto(
                    companyDto.getLevel(), companyDto.getIsNewPricing(), companyDto.getEnableStripeReader());
            if (CompanyFunctionControlConst.APPOINTMENT_NUMBER_UNLIMITED.equals(controlDto.getAppointmentNumber())) {
                // 无限数量的商户
                return;
            }

            // 限制预约数量的商户
            CompanyPermissionStateDto stateDto = iPaymentService.getPermissionStateByCompanyId(companyDto.getId());
            String startDate;
            String endDate;
            if (stateDto == null
                    || CompanyFunctionControlConst.LEVEL_0.equals(companyDto.getLevel())
                    || stateDto.getBeginDate() == null
                    || stateDto.getExpireDate() == null) {
                // 没有付费过的会员 或者 已经付过费的会员
                endDate = DateUtil.getLastDateThisMonth();
                startDate = DateUtil.getDateMinusDays(endDate, CompanyFunctionControlConst.APPOINTMENT_QUERY_DAYS);
            } else {
                return;
            }
            Integer apptCount = moeGroomingAppointmentMapper.queryAppointmentCountRange(businessId, startDate, endDate);
            if (apptCount >= controlDto.getAppointmentNumber()) {
                throw new CommonException(ResponseCodeEnum.TOO_MANY_APPOINTMENTS);
            }
        }
    }

    public void checkPetDetailParams(
            Long companyId, Integer businessId, Integer customerId, List<PetDetailParams> petDetailParams) {
        if (!CollectionUtils.isEmpty(petDetailParams)) {
            if (customerId == null) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "customerId can not be null");
            }

            checkBusinessCustomer(businessId, customerId);
            petDetailParams.forEach(petService -> {
                if (petService.getPetId() != null
                        && !iPetClient.checkBusinessCustomerPet(petService.getPetId(), businessId, customerId)) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet mismatch");
                }

                if (petService.getServiceId() != null
                        && !companyGroomingServiceQueryService.checkCompanyIdServiceId(
                                companyId, petService.getServiceId())) {
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "pet service mismatch");
                }
                if (petService.getStaffId() != null) {
                    if (petService.getStaffId() < 0) {
                        throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid staff id");
                    }
                    if (petService.getStaffId() > 0) {
                        // query staff
                        StaffIdParams staffIdParams = new StaffIdParams();
                        staffIdParams.setStaffId(petService.getStaffId());
                        MoeStaffDto staffDto = iBusinessStaffClient.getStaff(staffIdParams);
                        if (staffDto == null || !staffDto.getCompanyId().equals(companyId)) {
                            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "staff id mismatch");
                        }
                    }
                }
            });
        }
    }

    /**
     * 创建appointment，并更新已支付金额
     * 用于OB创建预约时，更新已支付的定金金额
     *
     * @param createApptParams
     * @return
     */
    public AddResultDTO addGroomingAppointment(AppointmentParams createApptParams) {
        return addGroomingAppointment(createApptParams, null);
    }

    public AddResultDTO addGroomingAppointment(
            AppointmentParams createApptParams, @Nullable BookOnlineDepositV2Params depositParams) {
        if (createApptParams.getIsGcSyncDelay() == null) {
            createApptParams.setIsGcSyncDelay(false);
        }
        preconditionCheckingForFreeBiz(createApptParams.getBusinessId());

        if (createApptParams.getAllPetsStartAtSameTime() != null
                && createApptParams.getAppointmentStartTime() != null) {
            // 有传 allPetsStartAtSameTime 时, 重新设置每个 service 的 start time
            setServiceStartTime(
                    createApptParams.getPetServices(),
                    createApptParams.getAppointmentStartTime(),
                    createApptParams.getAllPetsStartAtSameTime());
        }
        AddResultDTO addResultDTO = new AddResultDTO();

        List<Integer> serviceIds = createApptParams.getPetServices().stream()
                .map(PetDetailParams::getServiceId)
                .distinct()
                .toList();
        Map<Long, ServiceBriefView> serviceMap = serviceManagementService.list(serviceIds);

        MoeGroomingAppointment moeGroomingAppointment = buildNewAppt(createApptParams);
        // BD: set service item type
        moeGroomingAppointment.setServiceTypeInclude(ServiceItemEnum.convertBitValueList(serviceMap.values().stream()
                .map(ServiceBriefView::getServiceItemTypeValue)
                .toList()));
        createApptParams.getPetServices().stream()
                .filter(petService -> petService.getServiceItemEnum() == null)
                .forEach(petService -> petService.setServiceItemEnum(ServiceItemEnum.fromServiceItem(
                        serviceMap.get(petService.getServiceId().longValue()).getServiceItemTypeValue())));

        int count = moeGroomingAppointmentMapper.insertSelective(moeGroomingAppointment);
        if (count > 0) {
            // 设置主键Id到返回结果
            addResultDTO.setResult(true);
            addResultDTO.setId(moeGroomingAppointment.getId());
            moePetDetailService.addMoePetDetails(moeGroomingAppointment, createApptParams.getPetServices());
            // 非 OB 创建的 appt 更新 customer 相关的 abandon records
            if (!Objects.equals(createApptParams.getSource(), GroomingAppointmentEnum.SOURCE_OB)) {
                ThreadPool.execute(() -> updateAbandonRecords4Customer(moeGroomingAppointment));
            }
            // record activity log
            ActivityLogRecorder.record(
                    createApptParams.getBusinessId(),
                    Objects.equals(createApptParams.getSource(), GroomingAppointmentEnum.SOURCE_OB)
                            ? createApptParams.getCustomerId()
                            : createApptParams.getCreatedById(),
                    Action.CREATE,
                    ResourceType.APPOINTMENT,
                    moeGroomingAppointment.getId(),
                    createApptParams);
        } else {
            log.warn("create createApptParams failed, request para: {}", JsonUtil.toJson(createApptParams));
            addResultDTO.setResult(false);
            return addResultDTO;
        }
        buildGroomNote(moeGroomingAppointment, createApptParams);

        // Apply pricing rule
        pricingRuleApplyService.applyPricingRule(ApplyPricingRuleRequest.newBuilder()
                .setSourceId(moeGroomingAppointment.getId())
                .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                .setCompanyId(createApptParams.getCompanyId())
                .setBusinessId(createApptParams.getBusinessId())
                .build());
        var enableNewOrder = newOrderHelper.enableNewOrder(createApptParams.getCompanyId());
        if (enableNewOrder) {
            newOrderHelper.insertNewOrder(moeGroomingAppointment.getId());
        }

        Long invoiceId = 0L;
        // 创建invoice记录
        if (Objects.equals(createApptParams.getSource(), GroomingAppointmentEnum.SOURCE_OB)) {
            if (enableNewOrder) {
                invoiceId = orderService.saveDepositOrderWhenCreatingAppointmentFromOB(
                        moeGroomingAppointment.getId(), Objects.requireNonNull(depositParams));
            } else {
                invoiceId = orderService.saveOrderWhenCreatingAppointmentFromOB(
                        createApptParams.getCompanyId(),
                        createApptParams.getBusinessId(),
                        moeGroomingAppointment.getId(),
                        createApptParams.getCreatedById());
            }
        } else {
            // 创建 repeated appt 的时候也会复用这个方法，因此也需要判断，非白名单才能创建
            if (!enableNewOrder) {
                invoiceId = orderService.saveOrderWhenCreatingAppointment(
                        createApptParams.getCompanyId(),
                        createApptParams.getBusinessId(),
                        moeGroomingAppointment.getId(),
                        createApptParams.getCreatedById());
            }
        }
        addResultDTO.setInvoiceId(invoiceId.intValue());
        mqService.publishAppointmentEvent(
                createApptParams, moeGroomingAppointment, invoiceId, AppointmentEventEnum.CREATE_SINGLE);
        if (Objects.equals(createApptParams.getSource(), GroomingAppointmentEnum.SOURCE_OB)) {
            bookingRequestStub.syncBookingRequestFromAppointment(SyncBookingRequestFromAppointmentRequest.newBuilder()
                    .addAppointmentId(moeGroomingAppointment.getId())
                    .build());
        }
        pushAppointmentBooked(moeGroomingAppointment);
        asyncService.apptAddNotify(createApptParams, moeGroomingAppointment);
        asyncService.syncApptInfoToCalender(
                moeGroomingAppointment, createApptParams.getIsGcSyncDelay()); // addGroomingAppointment
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(createApptParams.getBusinessId())
                .setCustomerId(createApptParams.getCustomerId()));
        appointmentProducer.pushAppointmentCreatedEvent(moeGroomingAppointment.getId());
        return addResultDTO;
    }

    private void pushAppointmentBooked(MoeGroomingAppointment appointment) {
        if (Objects.equals(appointment.getSource(), GroomingAppointmentEnum.SOURCE_OB)) {
            return;
        }
        brandedAppNotificationService.pushNotification(
                appointment.getId(), NotificationType.NOTIFICATION_TYPE_APPOINTMENT_BOOKED);
    }

    private void buildGroomNote(MoeGroomingAppointment moeGroomingAppointment, AppointmentParams appointmentParams) {
        String alertNotes = appointmentParams.getAlertNotes();
        String ticketComments = appointmentParams.getTicketComments();

        /**
         * note 类型
         * 1 alertNotes(特殊提示 只给business看  后续不关注 有时效性 (ob customer提交，后续customer无法查看))
         * 2 comment(订单描述 只给business看  后续持续review 有history ticket comments)
         */
        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(moeGroomingAppointment.getBusinessId());
        moeGroomingNote.setCompanyId(moeGroomingAppointment.getCompanyId());
        moeGroomingNote.setCustomerId(moeGroomingAppointment.getCustomerId());
        moeGroomingNote.setGroomingId(moeGroomingAppointment.getId());
        moeGroomingNote.setCreateBy(moeGroomingAppointment.getCreatedById());
        moeGroomingNote.setUpdateBy(moeGroomingAppointment.getCreatedById());
        moeGroomingNote.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());

        // 添加 alert notes
        if (StringUtils.hasText(alertNotes)) {
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
            moeGroomingNote.setNote(alertNotes);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }

        // 添加comments
        if (StringUtils.hasText(ticketComments)) {
            moeGroomingNote.setId(null);
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
            moeGroomingNote.setNote(ticketComments);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }
        if (StringUtils.hasText(appointmentParams.getAdditionalNote())) {
            moeGroomingNote.setId(null);
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_ADDITIONAL);
            moeGroomingNote.setNote(appointmentParams.getAdditionalNote());
            moeGroomingNote.setCreateBy(moeGroomingAppointment.getCustomerId());
            moeGroomingNote.setUpdateBy(moeGroomingAppointment.getCustomerId());
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }
    }

    private MoeGroomingAppointment buildNewAppt(AppointmentParams appointment) {
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        BeanUtils.copyProperties(appointment, moeGroomingAppointment);
        moeGroomingAppointment.setOrderId(CommonUtil.getUuid());
        moeGroomingAppointment.setStatus(
                appointment.getStatus() == null
                        ? AppointmentStatusEnum.UNCONFIRMED.getValue()
                        : appointment.getStatus());
        moeGroomingAppointment.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        boolean isInterval = Objects.nonNull(appointment.getAppointmentDateString())
                && Objects.nonNull(appointment.getEndDate())
                && Objects.equals(appointment.getAppointmentDateString(), appointment.getEndDate());
        if (isInterval) {
            Pair<LocalDateTime, LocalDateTime> pair = calculatePeriod(appointment.getPetServices());
            moeGroomingAppointment.setAppointmentDate(
                    pair.getFirst().toLocalDate().toString());
            moeGroomingAppointment.setAppointmentEndDate(
                    pair.getSecond().toLocalDate().toString());
            moeGroomingAppointment.setAppointmentStartTime(
                    pair.getFirst().toLocalTime().toSecondOfDay() / 60);
            moeGroomingAppointment.setAppointmentEndTime(
                    pair.getSecond().toLocalTime().toSecondOfDay() / 60);
        } else {
            moeGroomingAppointment.setAppointmentDate(appointment.getAppointmentDateString());
            moeGroomingAppointment.setAppointmentEndDate(
                    appointment.getAppointmentDateString()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
            // 计算结束时间,取最大值作为预约预计结束时间
            Integer max = getMax(appointment);
            moeGroomingAppointment.setAppointmentEndTime(max);
            // 计算开始时间，取服务最小时间作为预约开始时间
            Integer min = getMin(appointment);
            moeGroomingAppointment.setAppointmentStartTime(min);
        }
        return moeGroomingAppointment;
    }

    public void updateAbandonRecords4Customer(MoeGroomingAppointment appointment) {
        if (appointment.getCustomerId() == null) {
            return;
        }
        List<MoeBookOnlineAbandonRecord> abandonRecords =
                abandonRecordMapper.listByCustomerId(appointment.getBusinessId(), appointment.getCustomerId());
        AtomicInteger recoveredId = new AtomicInteger();
        // 最近的一条置为 recovered
        abandonRecords.stream().findFirst().ifPresent(lastAbandonedRecord -> {
            MoeBookOnlineAbandonRecord update = new MoeBookOnlineAbandonRecord();
            update.setId(lastAbandonedRecord.getId());
            update.setRecoveryType((long) AbandonRecordRecoverTypeEnum.RECOVERED_BY_SCHEDULE_APPOINTMENT.getValue());
            update.setRecoveryTime(DateUtil.get10Timestamp());
            update.setAbandonStatus(SearchAbandonedClientParam.AbandonStatus.RECOVERED.getValue());
            update.setAppointmentId(appointment.getId());
            abandonRecordMapper.updateByPrimaryKeySelective(update);
            recoveredId.set(lastAbandonedRecord.getId());
        });
        // 其余全部标记为删除
        boolean needDeleted = abandonRecords.stream()
                .anyMatch(abandonRecord -> !Objects.equals(abandonRecord.getId(), recoveredId.get()));
        if (needDeleted) {
            UpdateAbandonedRecordByCustomerIdParam p = new UpdateAbandonedRecordByCustomerIdParam();
            p.setIsDeleted(DeleteStatusEnum.IS_DELETE_TRUE);
            p.setDeleteType(AbandonDeleteTypeEnum.HISTORICAL_DATA_OVERWRITTEN.getType());
            abandonRecordMapper.updateAbandonedRecordByCustomerId(
                    appointment.getBusinessId(), appointment.getCustomerId(), p);
        }
    }

    /**
     * 根据 参数中List<PetDetailParams> 的scopeType 对其他未确认预约做修改
     *
     * @param appointmentParams
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer applyingCustomServiceForUnconfirmedAppt(AppointmentParams appointmentParams) {
        // 先更新 service time
        int changedCount = applyCustomServiceTime(
                appointmentParams.getBusinessId(),
                appointmentParams.getCustomerId(),
                appointmentParams.getId(),
                appointmentParams.getAppointmentDateString(),
                appointmentParams.getPetServices().stream()
                        .map(petDetailParams -> new CustomizedServiceParamDTO(
                                petDetailParams.getPetId(),
                                petDetailParams.getServiceId(),
                                ScopeModifyTypeEnum.fromScopeType(petDetailParams.getScopeTypePrice()),
                                petDetailParams.getServicePrice(),
                                ScopeModifyTypeEnum.fromScopeType(petDetailParams.getScopeTypeTime()),
                                petDetailParams.getServiceTime()))
                        .toList());
        // 再遍历更新 service price
        changedCount += applyCustomerServicePrice(
                appointmentParams.getBusinessId(),
                appointmentParams.getOperatorId(),
                appointmentParams.getAppointmentDateString(),
                appointmentParams.getPetServices().stream()
                        .map(petDetailParams -> new CustomizedServiceParamDTO(
                                petDetailParams.getPetId(),
                                petDetailParams.getServiceId(),
                                ScopeModifyTypeEnum.fromScopeType(petDetailParams.getScopeTypePrice()),
                                petDetailParams.getServicePrice(),
                                ScopeModifyTypeEnum.fromScopeType(petDetailParams.getScopeTypeTime()),
                                petDetailParams.getServiceTime()))
                        .toList());
        return changedCount;
    }

    public int applyCustomerServicePrice(
            Integer businessId,
            Integer operatorId,
            String appointmentDate,
            List<CustomizedServiceParamDTO> customizedServices) {
        int changedCount = 0;
        for (CustomizedServiceParamDTO customizedService : customizedServices) {
            final Integer scopeTypePrice = customizedService.scopeTypePrice().getScopeType();
            /*
             改价格：找出需要修改的appt，修改pet details价格，并同步到invoice
            */
            String priceStartDate = null;
            if (ScopeModifyTypeEnum.ALL_UPCOMING.getScopeType().equals(scopeTypePrice)) {
                priceStartDate = DateUtil.getNowDateString();
            } else if (ScopeModifyTypeEnum.THIS_FOLLOWING.getScopeType().equals(scopeTypePrice)) {
                priceStartDate = appointmentDate;
            }
            if (priceStartDate == null) {
                continue;
            }
            List<GroomingPetDetailDTO> toChangedAppts = moeGroomingPetDetailMapper.queryUpcomingApptsByPetId(
                    businessId, priceStartDate, customizedService.petId(), customizedService.serviceId());
            Map<Integer, List<GroomingPetDetailDTO>> groomingIdToPetDetailMap = new HashMap<>();
            toChangedAppts.forEach(pd -> {
                groomingIdToPetDetailMap
                        .computeIfAbsent(pd.getGroomingId(), key -> new ArrayList<>())
                        .add(pd);
            });
            groomingIdToPetDetailMap.forEach((groomingId, pdList) -> {
                applyCustomServicePrice(groomingId, businessId, operatorId, customizedService, pdList);
            });
            changedCount += groomingIdToPetDetailMap.size();
            log.info(
                    "change {} appointments after {} for custom price.",
                    groomingIdToPetDetailMap.size(),
                    priceStartDate);
        }
        return changedCount;
    }

    public int applyCustomServiceTime(
            Integer businessId,
            Integer customerId,
            Integer appointmentId,
            String appointmentDate,
            List<CustomizedServiceParamDTO> customizedServices) {
        List<CustomServiceParams> paramsList = new ArrayList<>();
        // 遍历收集需要更新的 groomingId
        for (CustomizedServiceParamDTO customizedService : customizedServices) {
            String startDate = null;
            if (ScopeModifyTypeEnum.ALL_UPCOMING.equals(customizedService.scopeTypeTime())) {
                startDate = DateUtil.getNowDateString();
            } else if (ScopeModifyTypeEnum.THIS_FOLLOWING.equals(customizedService.scopeTypeTime())) {
                startDate = appointmentDate;
            }
            if (startDate == null) {
                continue;
            }
            List<GroomingPetDetailDTO> followingAppts = moeGroomingPetDetailMapper.queryUpcomingApptsByPetId(
                    businessId, startDate, customizedService.petId(), customizedService.serviceId());
            // 如果没有 upcoming 的预约则可以忽略
            if (followingAppts == null || followingAppts.isEmpty()) {
                continue;
            }
            // 收集 petId + serviceId 影响到的 groomingId，排除本次编辑的 groomingId
            Set<Integer> groomingIds = followingAppts.stream()
                    .map(GroomingPetDetailDTO::getGroomingId)
                    .filter(groomingId -> !groomingId.equals(appointmentId))
                    .collect(toSet());
            // 如果存在 upcoming 的预约，则构造更新参数。
            if (groomingIds.isEmpty()) {
                continue;
            }
            CustomServiceParams params = new CustomServiceParams();
            params.setBusinessId(businessId);
            params.setCustomerId(customerId);
            params.setPetId(customizedService.petId());
            params.setServiceId(customizedService.serviceId());
            params.setServicePrice(customizedService.servicePrice());
            params.setServiceTime(customizedService.serviceTime());
            params.setStartDate(startDate);
            params.setGroomingIds(groomingIds); // 表明此次 apply 对应的预约集合
            paramsList.add(params);
            log.info(
                    "appointmentId: {}, petId: {}, serviceId: {}, serviceTime: {}, groomingIds: {}",
                    appointmentId,
                    params.getPetId(),
                    params.getServiceId(),
                    params.getServiceTime(),
                    params.getGroomingIds());
        }
        // 根据 grooming id 集合一次性查出所有需要关联更新的 pet detail 并按 groomingId 分组
        Set<Integer> groomingIds = paramsList.stream()
                .map(CustomServiceParams::getGroomingIds)
                .flatMap(Collection::stream)
                .collect(toSet());
        if (groomingIds.isEmpty()) {
            return 0;
        }
        List<MoeGroomingPetDetail> details =
                moeGroomingPetDetailMapper.selectPetDetailByGroomingIdList(new ArrayList<>(groomingIds));
        if (CollectionUtils.isEmpty(details)) {
            return 0;
        }
        Map<Integer, List<MoeGroomingPetDetail>> detailMap =
                details.stream().collect(groupingBy(MoeGroomingPetDetail::getGroomingId));
        // 以 grooming 为单位进行遍历，结合自定义参数调整每个 petDetail 中的 service 相关时间。
        int changedCount = 0;
        final long currentTime = CommonUtil.get10Timestamp();
        for (Map.Entry<Integer, List<MoeGroomingPetDetail>> entry : detailMap.entrySet()) {
            int n = updateCustomServiceTime(businessId, entry.getValue(), paramsList, entry.getKey(), currentTime);
            log.info("updated serviceTime: {} for grooming: {}", n, entry.getKey());
            if (0 < n) {
                ++changedCount;
            }
        }

        return changedCount;
    }

    // 更新一组 pet detail 中的 service 相关时间，返回实际更新的 detail 数量。
    @Transactional
    public int updateCustomServiceTime(
            Integer businessId,
            List<MoeGroomingPetDetail> details,
            List<CustomServiceParams> paramsList,
            Integer groomingId,
            long currentTime) {
        int changedCount = 0;
        // 先收集 service time 发生变化的 detail
        List<MoeGroomingPetDetail> targets = new ArrayList<>();
        for (MoeGroomingPetDetail detail : details) {
            MoeGroomingPetDetail target = new MoeGroomingPetDetail();
            target.setId(detail.getId());
            target.setServiceTime(detail.getServiceTime());
            for (CustomServiceParams params : paramsList) {
                // 只有 groomingId, petId, serviceId 匹配，且 serviceTime 不同的 detail 才会更新。
                if (params.getGroomingIds().contains(groomingId)
                        && params.getPetId().equals(detail.getPetId())
                        && params.getServiceId().equals(detail.getServiceId())
                        && (!params.getServiceTime().equals(detail.getServiceTime())
                                || !ScopeModifyTypeEnum.THIS_FUTURE
                                        .getScopeType()
                                        .equals(detail.getScopeTypeTime()))) {
                    target.setServiceTime(params.getServiceTime());
                    target.setScopeTypeTime(ScopeModifyTypeEnum.THIS_FUTURE.getScopeType());
                    log.info(
                            "petId: {}, serviceId: {}, serviceTime: {}, startDate: {}, groomingId: {}, petDetailId: {}",
                            params.getPetId(),
                            params.getServiceId(),
                            params.getServiceTime(),
                            params.getStartDate(),
                            groomingId,
                            target.getId());
                    ++changedCount;
                }
            }
            targets.add(target);
        }
        // 如果有则重置为 0，稍后重新统计实际影响的 detail 数量，否则直接返回.
        if (0 < changedCount) {
            changedCount = 0;
        } else {
            return 0;
        }
        // 判定不同 pet 的 detail 的最早 startTime 是否相同，如果是，则表明勾选了 "Multiple pets start at the same time"
        boolean isStartAtSameTime = details.stream()
                        .collect(toMap(MoeGroomingPetDetail::getPetId, MoeGroomingPetDetail::getStartTime, Long::min))
                        .values()
                        .stream()
                        .distinct()
                        .count()
                == 1;
        Map<Integer, List<GroomingServiceOperationDTO>> originOperationMap =
                groomingServiceOperationService.getOperationMapByGroomingId(businessId, groomingId);
        Map<Integer, List<GroomingServiceOperationDTO>> targetOperationMap =
                groomingServiceOperationService.getOperationMapByGroomingId(businessId, groomingId);
        // 重新遍历计算这个 grooming 下的所有 pet detail 的 service 时间
        Long appointmentStartTime = details.get(0).getStartTime();
        Long nextStartTime = appointmentStartTime;
        for (int i = 0; i < targets.size(); ++i) {
            MoeGroomingPetDetail origin = details.get(i);
            MoeGroomingPetDetail target = targets.get(i);
            setOperationTime(originOperationMap, targetOperationMap, origin, target);
            target.setStartTime(nextStartTime);
            target.setEndTime(target.getStartTime() + target.getServiceTime());
            // 只要任一时间字段不同则需要进行更新
            if (target.getScopeTypeTime() != null
                    || !target.getServiceTime().equals(origin.getServiceTime())
                    || !target.getStartTime().equals(origin.getStartTime())
                    || !target.getEndTime().equals(origin.getEndTime())) {
                target.setUpdateTime(currentTime);
                moeGroomingPetDetailMapper.updateByPrimaryKeySelective(target);
                ++changedCount;
            }
            if (i < targets.size() - 1 && targets.get(i + 1).getPetId().equals(target.getPetId())) {
                // 如果下一个 Pet detail 是同一只 Pet，则时间顺延
                nextStartTime = target.getEndTime();
            } else if (isStartAtSameTime) {
                // 如果下一个 Pet detail 不是同一只 Pet，并且 isStartAtSameTime，则开始时间重置为预约的开始时间
                nextStartTime = appointmentStartTime;
            }
        }
        // 如果有 pet detail 的时间发生过更新，则还需要更新 appointment 的 endTime。
        if (0 < changedCount) {
            long maxEndTime = targets.stream()
                    .mapToLong(MoeGroomingPetDetail::getEndTime)
                    .max()
                    .orElse(nextStartTime);
            MoeGroomingAppointment appointment = new MoeGroomingAppointment();
            appointment.setId(groomingId);
            appointment.setAppointmentEndTime((int) maxEndTime);
            appointment.setUpdateTime(currentTime);
            moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
        }
        if (!CollectionUtils.isEmpty(targetOperationMap)) {
            groomingServiceOperationService.updateOperation(targetOperationMap.values().stream()
                    .flatMap(Collection::stream)
                    .toList());
        }
        return changedCount;
    }

    private static void setOperationTime(
            Map<Integer, List<GroomingServiceOperationDTO>> originOperationMap,
            Map<Integer, List<GroomingServiceOperationDTO>> targetOperationMap,
            MoeGroomingPetDetail origin,
            MoeGroomingPetDetail target) {
        if (CollectionUtils.isEmpty(targetOperationMap)) {
            return;
        }
        List<GroomingServiceOperationDTO> originOperationList = originOperationMap.get(origin.getId());
        List<GroomingServiceOperationDTO> targetOperationList = targetOperationMap.get(target.getId());
        // 如果两个 service 的 operation 数量相同，且都是连续的，则可以直接复用
        if (!CollectionUtils.isEmpty(originOperationList)
                && Objects.equals(originOperationList.size(), targetOperationList.size())
                && checkOperationListIsSerial(originOperationList)
                && checkOperationListIsSerial(targetOperationList)) {
            for (int index = 0; index < targetOperationList.size(); index++) {
                GroomingServiceOperationDTO originOperation = originOperationList.get(index);
                GroomingServiceOperationDTO targetOperation = targetOperationList.get(index);
                targetOperation.setStartTime(originOperation.getStartTime());
                targetOperation.setDuration(originOperation.getDuration());
            }
        } else {
            // 如果不满足上述条件，则需要重新计算与分配 operation 的时间
            reassignOperationTime(target.getServiceTime(), origin.getServiceTime(), targetOperationList);
        }

        Integer minStartTime = targetOperationList.stream()
                .map(GroomingServiceOperationDTO::getStartTime)
                .min(Integer::compareTo)
                .get();
        Integer maxEndTime = targetOperationList.stream()
                .map(operation -> operation.getStartTime() + operation.getDuration())
                .max(Integer::compareTo)
                .get();
        target.setServiceTime(maxEndTime - minStartTime);
    }

    private static boolean checkOperationListIsSerial(List<GroomingServiceOperationDTO> originOperationList) {
        for (int index = originOperationList.size() - 1; index >= 1; index--) {
            int afterStartTime = originOperationList.get(index).getStartTime();
            int previousStartTime = originOperationList.get(index - 1).getStartTime()
                    + originOperationList.get(index - 1).getDuration();
            if (afterStartTime != previousStartTime) {
                return false;
            }
        }
        return true;
    }

    private static boolean checkOperationListIsParallel(List<GroomingServiceOperationDTO> originOperationList) {
        return originOperationList.stream()
                        .map(GroomingServiceOperationDTO::getStartTime)
                        .distinct()
                        .count()
                == 1;
    }

    private void applyCustomServicePrice(
            Integer groomingId,
            Integer businessId,
            Integer operatorId,
            CustomizedServiceParamDTO customizedService,
            List<GroomingPetDetailDTO> pdList) {
        log.info("grooming {} will change price for {} ", groomingId, customizedService.petId());

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingId(businessId, groomingId);

        final Long CURRENT = CommonUtil.get10Timestamp();
        pdList.forEach(pd -> {
            if (!CollectionUtils.isEmpty(operationMap)) {
                reassignOperationPrice(
                        customizedService.servicePrice(), pd.getServicePrice(), operationMap.get(pd.getId()));
            }
            MoeGroomingPetDetail atomPd = new MoeGroomingPetDetail();
            atomPd.setId(pd.getId());
            atomPd.setServicePrice(customizedService.servicePrice());
            atomPd.setScopeTypePrice(ScopeModifyTypeEnum.THIS_FUTURE.getScopeType());
            atomPd.setUpdateTime(CURRENT);
            moeGroomingPetDetailMapper.updateByPrimaryKeySelective(atomPd);
        });

        if (!CollectionUtils.isEmpty(operationMap)) {
            groomingServiceOperationService.updateOperation(
                    operationMap.values().stream().flatMap(Collection::stream).toList());
        }

        orderService.updateOrderByGroomingId(null, businessId, groomingId, operatorId);
        log.info("sync invoice successfully for {}", groomingId);
    }

    public MoeGroomingAppointment getAppointmentById(Integer businessId, Integer appointmentId) {
        return moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(appointmentId, businessId);
    }

    public List<MoeGroomingAppointment> getAppointmentByIds(Integer businessId, List<Integer> appointmentIds) {
        return moeGroomingAppointmentMapper.selectByIdListAndBusinessId(appointmentIds, businessId);
    }

    public List<MoeGroomingAppointment> getAppointmentByIdsV2(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Collections.emptyList();
        }
        return moeGroomingAppointmentMapper.selectByIdList(appointmentIds);
    }

    public ResponseResult<Integer> modifyGroomingAppointment(
            Integer tokenStaffId, AppointmentParams appointment, Boolean isNotRepeat) {
        MoeGroomingAppointment temp =
                appointmentQueryService.getAppointmentById(appointment.getBusinessId(), appointment.getId());
        if (temp == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        return modifyGroomingAppointment(temp, tokenStaffId, appointment, isNotRepeat);
    }

    //    @Transactional
    // https://sentry.moego.pet/organizations/moego/issues/281/?environment=prod&project=5&query=is%3Aunresolved
    public ResponseResult<Integer> modifyGroomingAppointment(
            MoeGroomingAppointment previous, Integer tokenStaffId, AppointmentParams appointment, Boolean isNotRepeat) {
        if (appointment.getIsGcSyncDelay() == null) {
            appointment.setIsGcSyncDelay(false);
        }
        MoeGroomingAppointment moeGroomingAppointment = buildAppt(appointment, previous);
        // waitList 转 appointment
        if (isScheduleWaitList(previous, appointment)) {
            String tz = waitListService.getBusinessTimeZone(
                    appointment.getBusinessId().longValue());
            moeGroomingAppointment.setWaitListStatus(WaitListStatusEnum.APPTONLY);
            waitListService.deleteByAppointmentId(
                    appointment.getCompanyId(),
                    appointment.getBusinessId().longValue(),
                    List.of(appointment.getId().longValue()),
                    tz);
        }
        if (isScheduleOBRequest(previous, appointment)) {
            mqService.publishBookingRequestEvent(new BookingRequestEventParams()
                    .setBusinessId(appointment.getBusinessId())
                    .setAppointmentId(appointment.getId())
                    .setEvent(BookingRequestEventParams.BookingRequestEvent.SCHEDULED));
        }
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(moeGroomingAppointment.getId());
        // 修改服务 fixme: update = delete+create 导致petDetailId变化，invoice也需要更新，当前并未更新，只刷新了svcId和price
        moePetDetailService.deleteByAppointId(moeGroomingAppointment.getId());
        moePetDetailService.addMoePetDetails(moeGroomingAppointment, appointment.getPetServices());
        modifyGroomNote(appointment, previous, tokenStaffId, isNotRepeat);

        orderService.updateOrderByGroomingId(
                appointment.getCompanyId(), appointment.getBusinessId(), appointment.getId(), tokenStaffId);

        if (isNotRepeat) {
            asyncService.apptUpdateNotify(moeGroomingAppointment, previous, tokenStaffId);
        }
        asyncService.syncApptInfoToCalender(
                moeGroomingAppointment, appointment.getIsGcSyncDelay()); // modifyGroomingAppointment
        mqService.publishAppointmentEvent(
                appointment, moeGroomingAppointment, null, AppointmentEventEnum.MODIFY_SINGLE);
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(moeGroomingAppointment.getBusinessId())
                .setCustomerId(moeGroomingAppointment.getCustomerId()));
        if (isScheduleOBRequest(previous, appointment)) {
            onlineBookingProducer.pushOnlineBookingAcceptedEvent(moeGroomingAppointment);
        }
        pushAppointmentScheduled(previous, appointment);

        return ResponseResult.success(i);
    }

    private void pushAppointmentScheduled(MoeGroomingAppointment previous, AppointmentParams appointment) {
        if (isScheduleOBWaitlist(previous, appointment)) {
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST);
        } else if (isScheduleOBRequest(previous, appointment)) {
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED);
        } else if (isRescheduleAppointment(previous, appointment)) {
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED);
        }
    }

    private boolean isScheduleOBWaitlist(MoeGroomingAppointment previous, AppointmentParams appointment) {
        return isScheduleOBRequest(previous, appointment) && isScheduleWaitList(previous, appointment);
    }

    private static boolean isScheduleOBRequest(MoeGroomingAppointment previous, AppointmentParams appointment) {
        return Objects.equals(previous.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                // null 也会更新为 appointment :)
                // see com.moego.server.grooming.service.MoeGroomingAppointmentService.buildAppt
                && !Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB);
    }

    private boolean isRescheduleAppointment(MoeGroomingAppointment previous, AppointmentParams appointment) {
        return Objects.equals(previous.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB)
                && (!Objects.equals(previous.getAppointmentDate(), appointment.getAppointmentDateString())
                        || Objects.equals(previous.getAppointmentStartTime(), appointment.getAppointmentStartTime()));
    }

    private static boolean isScheduleWaitList(MoeGroomingAppointment previous, AppointmentParams appointment) {
        return Objects.equals(previous.getIsWaitingList(), GroomingAppointmentEnum.IS_WAITING_LIST)
                && Objects.equals(appointment.getIsWaitingList(), GroomingAppointmentEnum.NOT_WAITING_LIST);
    }

    private void modifyGroomNote(
            AppointmentParams appointment, MoeGroomingAppointment temp, Integer tokenStaffId, Boolean isNotRepeat) {
        // 更新alertNotes和comments的bean
        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(temp.getBusinessId());
        moeGroomingNote.setCompanyId(temp.getCompanyId());
        moeGroomingNote.setCustomerId(temp.getCustomerId());
        moeGroomingNote.setGroomingId(temp.getId());
        moeGroomingNote.setCreateBy(tokenStaffId);
        moeGroomingNote.setUpdateBy(tokenStaffId);
        moeGroomingNote.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());

        // alert note修改
        // repeat修改预约时，有可能不需要修改alert notes
        if (appointment.getIsNeedUpdateAlertNotes() == null || appointment.getIsNeedUpdateAlertNotes()) {
            String alertNotes = appointment.getAlertNotes();
            if (alertNotes != null) {
                moeGroomingNote.setId(null);
                moeGroomingNote.setUpdateBy(tokenStaffId);
                moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
                moeGroomingNote.setNote(alertNotes);
                moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
            }
        }

        if (isNotRepeat) {
            String ticketComments = appointment.getTicketComments();
            // null 不作任何修改，空白字符删除，其他修改
            if (ticketComments == null) {
                return;
            }
            if (StringUtils.hasText(ticketComments)) {
                moeGroomingNote.setId(null);
                moeGroomingNote.setUpdateBy(tokenStaffId);
                moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
                moeGroomingNote.setNote(ticketComments);
                moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
            } else {
                removeAppointmentComment(temp.getId(), tokenStaffId, GroomingNoteConstant.PET_ID_FOR_GENERAL_COMMENT);
            }
        }
    }

    private MoeGroomingAppointment buildAppt(AppointmentParams appointment, MoeGroomingAppointment temp) {
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        String appointmentDate = appointment.getAppointmentDateString();
        if (StringUtils.hasText(appointmentDate)) {
            moeGroomingAppointment.setAppointmentDate(appointmentDate);
            moeGroomingAppointment.setAppointmentEndDate(
                    appointmentDate); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        }
        // 如果days diff有值，那么当前appt的日期，需要计算daysdiff后的变化
        if (appointment.getRepeatUpdateDaysDiff() != null && appointment.getRepeatUpdateDaysDiff() != 0) {
            moeGroomingAppointment.setAppointmentDate(
                    DateUtil.getStrDateByDaysDiff(temp.getAppointmentDate(), appointment.getRepeatUpdateDaysDiff()));
            moeGroomingAppointment.setAppointmentEndDate(DateUtil.getStrDateByDaysDiff(
                    temp.getAppointmentDate(),
                    appointment.getRepeatUpdateDaysDiff())); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        }

        // 修改预约的时候忽略 source 字段
        appointment.setSource(null);
        appointment.setCustomerId(null);
        // DONE(Frank): 修改时，customerId是不是不应该修改
        BeanUtils.copyProperties(appointment, moeGroomingAppointment);
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        // 将修改前预约时间做记录
        moeGroomingAppointment.setOldAppointmentDate(temp.getAppointmentDate());
        moeGroomingAppointment.setOldAppointmentStartTime(temp.getAppointmentStartTime());
        moeGroomingAppointment.setOldAppointmentEndTime(temp.getAppointmentEndTime());

        moeGroomingAppointment.setIsPaid(temp.getIsPaid());
        moeGroomingAppointment.setStatus(temp.getStatus());

        // 计算结束时间,取最大值作为预约预计结束时间
        Integer max = getMax(appointment);
        moeGroomingAppointment.setAppointmentEndTime(max);

        // 取最早开始时间作为预约开始时间
        Integer min = getMin(appointment);
        moeGroomingAppointment.setAppointmentStartTime(min);
        moeGroomingAppointment.setBookOnlineStatus(
                Objects.nonNull(appointment.getBookOnlineStatus())
                        ? appointment.getBookOnlineStatus()
                        : ServiceEnum.OB_DEFAULT_NORMAL);
        return moeGroomingAppointment;
    }

    public void modifyAppointmentRepeatAndNotify(
            MoeGroomingAppointment previousAppointment, AppointmentRepeatModifyParams modifyParams) {
        // 获取更新预约前的 staffIdList
        List<Integer> beforeUpdateStaffIdList =
                groomingServiceOperationService.queryStaffIdByGroomingId(modifyParams.getId());

        // 更新
        modifyParams.setIsGcSyncDelay(false);
        modifyAppointmentRepeatWithoutNotify(previousAppointment, modifyParams);

        // 发送通知
        ThreadPool.execute(() -> rescheduleAndAssignNotify(previousAppointment, modifyParams, beforeUpdateStaffIdList));
        ThreadPool.execute(() -> recordActivityLog(previousAppointment, modifyParams));
    }

    private void recordActivityLog(MoeGroomingAppointment oldAppointment, AppointmentRepeatModifyParams modifyParams) {
        recordReschedule(oldAppointment, modifyParams);
        recordEditPetAndServices(oldAppointment, modifyParams);
    }

    private void recordEditPetAndServices(
            MoeGroomingAppointment oldAppointment, AppointmentRepeatModifyParams modifyParams) {
        activityLogStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(oldAppointment.getCompanyId())
                .setBusinessId(oldAppointment.getBusinessId())
                .setIsRoot(true)
                .setOperatorId(String.valueOf(modifyParams.getStaffId()))
                .setAction(AppointmentAction.EDIT_PET_AND_SERVICES)
                .setResourceType(com.moego.idl.models.activity_log.v1.Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(modifyParams.getId()))
                .setDetails(JsonUtil.toJson(modifyParams))
                .build());
    }

    private void recordReschedule(MoeGroomingAppointment oldAppointment, AppointmentRepeatModifyParams modifyParams) {
        var oldDate = oldAppointment.getAppointmentDate();
        var oldStartTime = oldAppointment.getAppointmentStartTime();
        var newDate = StringUtils.hasText(modifyParams.getAppointmentDateString())
                ? modifyParams.getAppointmentDateString()
                : oldAppointment.getAppointmentDate();
        var newStartTime = modifyParams.getAppointmentStartTime() != null
                ? modifyParams.getAppointmentStartTime()
                : oldAppointment.getAppointmentStartTime();
        if (Objects.equals(oldDate, newDate) && Objects.equals(oldStartTime, newStartTime)) {
            return;
        }
        var timeZoneName = companyHelper.getCompanyTimeZoneName(oldAppointment.getCompanyId());
        var zoneId = ZoneId.of(timeZoneName);
        activityLogStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(oldAppointment.getCompanyId())
                .setBusinessId(oldAppointment.getBusinessId())
                .setOperatorId(String.valueOf(modifyParams.getStaffId()))
                .setAction(AppointmentAction.RESCHEDULE)
                .setResourceType(com.moego.idl.models.activity_log.v1.Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(modifyParams.getId()))
                .setDetails(JsonUtil.toJson(new ChangeTimeLogDTO(
                        toTimestamp(oldDate, oldStartTime, zoneId),
                        toTimestamp(newDate, newStartTime, zoneId),
                        AppointmentUpdatedBy.BY_BUSINESS)))
                .build());
    }

    private static long toTimestamp(String date, int startTime, ZoneId zoneId) {
        return LocalDate.parse(date)
                .atTime(startTime / 60, startTime % 60)
                .atZone(zoneId)
                .toInstant()
                .getEpochSecond();
    }

    private void rescheduleAndAssignNotify(
            MoeGroomingAppointment previousAppointment,
            AppointmentRepeatModifyParams modifyParams,
            List<Integer> beforeUpdateStaffIdList) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(modifyParams.getId());
        // 只有更新appointmentDate和startTime后，才会发送rescheduled通知
        boolean isUpdateDate =
                !moeGroomingAppointment.getAppointmentDate().equals(previousAppointment.getAppointmentDate());
        if (!moeGroomingAppointment.getAppointmentStartTime().equals(previousAppointment.getAppointmentStartTime())) {
            isUpdateDate = true;
        }

        Map<Boolean, Set<Integer>> staffIdMapList =
                groomingServiceOperationService.queryStaffIdByGroomingId(modifyParams.getId()).stream()
                        .collect(Collectors.partitioningBy(beforeUpdateStaffIdList::contains, toSet()));
        Set<Integer> notifyUpdateStaffIdList = staffIdMapList.get(Boolean.TRUE);
        Set<Integer> notifyCreateStaffIdList = staffIdMapList.get(Boolean.FALSE);

        // 给前端的数据体
        NotificationExtraApptCommonDto apptCommonDto = new NotificationExtraApptCommonDto();
        apptCommonDto.setAppointmentDate(previousAppointment.getAppointmentDate());
        apptCommonDto.setAppointmentStartTime(previousAppointment.getAppointmentStartTime());
        apptCommonDto.setNoStartTime(previousAppointment.getNoStartTime());
        apptCommonDto.setAppointmentEndTime(previousAppointment.getAppointmentEndTime());
        apptCommonDto.setCustomerId(previousAppointment.getCustomerId());
        apptCommonDto.setGroomingId(previousAppointment.getId());
        if (isUpdateDate && !CollectionUtils.isEmpty(notifyUpdateStaffIdList)) {
            // 调用通知发送
            NotificationApptRescheduledParams apptRescheduledParams = new NotificationApptRescheduledParams();
            apptRescheduledParams.setBusinessId(modifyParams.getBusinessId());
            apptRescheduledParams.setStaffIdList(notifyUpdateStaffIdList);
            apptRescheduledParams.setTokenStaffId(modifyParams.getStaffId());
            // customer firstName 和 lastName 在notification模块组装
            apptRescheduledParams.setWebPushDto(apptCommonDto);
            iNotificationClient.sendNotificationApptRescheduled(apptRescheduledParams);
        }
        if (!CollectionUtils.isEmpty(notifyCreateStaffIdList)) {
            // 发送给staff的创建预约通知
            NotificationApptAssignedParams apptAssignedParams = new NotificationApptAssignedParams();
            apptAssignedParams.setBusinessId(modifyParams.getBusinessId());
            apptAssignedParams.setStaffIdList(notifyCreateStaffIdList);
            apptAssignedParams.setTokenStaffId(modifyParams.getStaffId());
            // customer firstName 和 lastName 在notification模块组装
            apptAssignedParams.setWebPushDto(apptCommonDto);
            iNotificationClient.sendNotificationApptAssigned(apptAssignedParams);
        }
    }

    public void modifyAppointmentRepeatWithoutNotify(
            MoeGroomingAppointment appointment, AppointmentRepeatModifyParams modifyParams) {
        Integer businessId = modifyParams.getBusinessId();
        Integer id = modifyParams.getId();

        List<PetDetailParams> serviceList;
        Integer appointmentStartTime = modifyParams.getAppointmentStartTime();
        if (Objects.isNull(appointmentStartTime)) {
            appointmentStartTime = appointment.getAppointmentStartTime();
        }
        if (CollectionUtils.isEmpty(modifyParams.getServiceList())) {
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                    groomingServiceOperationService.getOperationMapByGroomingId(businessId, appointment.getId());
            serviceList = moePetDetailService.queryPetDetailByOneGroomingId(appointment.getId()).stream()
                    .map(moeGroomingPetDetail -> {
                        PetDetailParams petDetailParams = new PetDetailParams();
                        BeanUtils.copyProperties(moeGroomingPetDetail, petDetailParams);
                        petDetailParams.setStartTime(Math.toIntExact(moeGroomingPetDetail.getStartTime()));
                        if (!CollectionUtils.isEmpty(operationMap)
                                && operationMap.containsKey(moeGroomingPetDetail.getId())) {
                            petDetailParams.setOperationList(operationMap.get(moeGroomingPetDetail.getId()));
                        }
                        return petDetailParams;
                    })
                    .collect(toList());
        } else {
            serviceList = new ArrayList<>(modifyParams.getServiceList());
        }

        // set service start time todo 待替换成 setServiceStartTime 方法
        AtomicInteger startTime = new AtomicInteger(appointmentStartTime);
        if (Boolean.TRUE.equals(modifyParams.getStartAtSameTime())) {
            Map<Integer, Integer> petIdStartTime = new HashMap<>(8);
            serviceList.forEach(service -> {
                Integer currentStartTime = petIdStartTime.getOrDefault(service.getPetId(), startTime.get());
                Integer previousServiceStartTime = service.getStartTime();
                service.setStartTime(currentStartTime);

                int deltaTime = service.getStartTime() - previousServiceStartTime;
                if (!CollectionUtils.isEmpty(service.getOperationList())) {
                    service.getOperationList()
                            .forEach(operation -> operation.setStartTime(operation.getStartTime() + deltaTime));
                }
                petIdStartTime.put(service.getPetId(), currentStartTime + service.getServiceTime());
            });
        } else {
            serviceList.forEach(service -> {
                Integer previousServiceStartTime = service.getStartTime();
                service.setStartTime(startTime.getAndAdd(service.getServiceTime()));

                int deltaTime = service.getStartTime() - previousServiceStartTime;
                if (!CollectionUtils.isEmpty(service.getOperationList())) {
                    service.getOperationList()
                            .forEach(operation -> operation.setStartTime(operation.getStartTime() + deltaTime));
                }
            });
        }

        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();

        String appointmentDate = modifyParams.getAppointmentDateString();
        if (StringUtils.hasText(appointmentDate)) {
            moeGroomingAppointment.setAppointmentDate(appointmentDate);
            moeGroomingAppointment.setAppointmentEndDate(
                    appointmentDate); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        }

        moeGroomingAppointment.setId(id);

        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        // 将修改前预约时间做记录
        moeGroomingAppointment.setOldAppointmentDate(appointment.getAppointmentDate());
        moeGroomingAppointment.setOldAppointmentStartTime(appointment.getAppointmentStartTime());
        moeGroomingAppointment.setOldAppointmentEndTime(appointment.getAppointmentEndTime());
        // 如果修改前 bookOnlineStatus  = 1, 预约有更新则更新为 0，OB Request 编辑后更新为普通预约
        if (Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
            moeGroomingAppointment.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB);
        }

        // 取最早开始时间作为预约开始时间
        // todo newAppointmentStartTime 后面移除 pet 的时候可能会变，这里要 fix 一下
        int newAppointmentStartTime = serviceList.stream()
                .mapToInt(PetDetailParams::getStartTime)
                .summaryStatistics()
                .getMin();
        moeGroomingAppointment.setAppointmentStartTime(newAppointmentStartTime);
        // 计算结束时间,取最大值作为预约预计结束时间
        int newAppointmentEndTime = serviceList.stream()
                .mapToInt(service -> service.getStartTime() + service.getServiceTime())
                .summaryStatistics()
                .getMax();
        moeGroomingAppointment.setAppointmentEndTime(newAppointmentEndTime);

        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(moeGroomingAppointment.getId());

        // delete service which pet pass away
        if (!CollectionUtils.isEmpty(modifyParams.getServiceList())) {
            MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                    InfoIdParams.builder().infoId(businessId).build());
            if (businessInfo == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not exists");
            }
            Integer nowMinute = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
            LocalDate nowLocalDate = LocalDate.now(
                    TimeZone.getTimeZone(businessInfo.getTimezoneName()).toZoneId());
            LocalDate appointmentDateString = LocalDate.parse(moeGroomingAppointment.getAppointmentDate());
            boolean appointmentTimeIsAfterNow = appointmentDateString.isAfter(nowLocalDate)
                    || (appointmentDateString.isEqual(nowLocalDate)
                            && Math.toIntExact(newAppointmentEndTime) >= nowMinute);
            if (appointmentTimeIsAfterNow) {
                Set<Integer> validPetIdList =
                        iPetClient
                                .getCustomerPetListByCustomerId(Collections.singletonList(appointment.getCustomerId()))
                                .stream()
                                .map(CustomerPetDetailDTO::getPetId)
                                .collect(toSet());
                serviceList.removeIf(service -> !validPetIdList.contains(service.getPetId()));
            }
        }

        // 修改服务 fixme: update = delete+create 导致petDetailId变化，invoice也需要更新，当前并未更新，只刷新了svcId和price
        moePetDetailService.deleteByAppointId(moeGroomingAppointment.getId());
        moePetDetailService.addMoePetDetails(moeGroomingAppointment, serviceList);

        // Apply pricing rule
        pricingRuleApplyService.applyPricingRule(ApplyPricingRuleRequest.newBuilder()
                .setSourceId(moeGroomingAppointment.getId())
                .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .build());

        orderService.updateOrderByGroomingId(
                moeGroomingAppointment.getCompanyId(), businessId, id, modifyParams.getStaffId());
        mqService.publishAppointmentEventV2(
                modifyParams.getPreAuthParams(), moeGroomingAppointment, null, AppointmentEventEnum.MODIFY_SINGLE);
        appointmentProducer.pushAppointmentUpdatedEvent(moeGroomingAppointment.getId());

        int syncApptId = id;
        String apptDate = moeGroomingAppointment.getAppointmentDate();
        boolean isGcSyncDelay = modifyParams.getIsGcSyncDelay();
        ThreadPool.execute(() -> {
            calendarSyncService.checkBusinessHaveGoogleCalendarSync( // modifyAppointmentRepeatWithoutNotify
                    businessId, syncApptId, apptDate, isGcSyncDelay);
            quickBooksService.addRedisSyncGroomingData(businessId, id, appointmentDate);
        });

        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(businessId)
                .setCustomerId(moeGroomingAppointment.getCustomerId()));
    }

    public void setServiceStartTime(
            List<PetDetailParams> serviceList, Integer appointmentStartTime, Boolean startAtSameTime) {
        // set service start time
        AtomicInteger startTime = new AtomicInteger(appointmentStartTime);
        if (Boolean.TRUE.equals(startAtSameTime)) {
            Map<Integer, Integer> petIdStartTime = new HashMap<>(8);
            serviceList.forEach(service -> {
                Integer currentStartTime = petIdStartTime.getOrDefault(service.getPetId(), startTime.get());
                service.setStartTime(currentStartTime);
                service.setEndTime(currentStartTime + service.getServiceTime());
                // 计算 operation startTime
                setServiceOperationStartTime(service, currentStartTime);
                petIdStartTime.put(service.getPetId(), currentStartTime + service.getServiceTime());
            });
        } else {
            serviceList.forEach(service -> {
                Integer currentStartTime = startTime.getAndAdd(service.getServiceTime());
                service.setStartTime(currentStartTime);
                service.setEndTime(currentStartTime + service.getServiceTime());
                // 计算 operation startTime
                setServiceOperationStartTime(service, currentStartTime);
            });
        }
    }

    private void setServiceOperationStartTime(PetDetailParams service, Integer serviceStartTime) {
        if (!Boolean.TRUE.equals(service.getEnableOperation()) || CollectionUtils.isEmpty(service.getOperationList())) {
            return;
        }
        final AtomicInteger curOperationStartTime = new AtomicInteger(serviceStartTime);
        service.getOperationList().forEach(operation -> {
            if (service.getWorkMode() == MoePetDetailService.PARALLEL_WORK_MODE) {
                operation.setStartTime(curOperationStartTime.get());
            } else if (service.getWorkMode() == MoePetDetailService.SEQUENCE_WORK_MODE) {
                operation.setStartTime(curOperationStartTime.getAndAdd(operation.getDuration()));
            }
        });
    }

    public Integer getMax(AppointmentParams appointment) {
        List<Integer> temp = new ArrayList<>();
        for (PetDetailParams petDetailParams : appointment.getPetServices()) {
            Integer serviceTime = petDetailParams.getServiceTime();
            if (petDetailParams.getEndTime() != null) {
                temp.add(petDetailParams.getEndTime());
            } else if (petDetailParams.getStartTime() != null && serviceTime != null) {
                temp.add(petDetailParams.getStartTime() + serviceTime);
            }
        }
        return Collections.max(temp);
    }

    private Integer getMin(AppointmentParams appointment) {
        List<Integer> temp = new ArrayList<>();
        for (PetDetailParams petDetailParams : appointment.getPetServices()) {
            Integer startTime = petDetailParams.getStartTime();
            if (startTime != null) {
                temp.add(startTime);
            }
        }
        return Collections.min(temp);
    }

    public Pair<LocalDateTime, LocalDateTime> calculatePeriod(List<PetDetailParams> petDetails) {
        return petDetails.stream()
                .filter(petDetail -> Objects.equals(
                        petDetail.getServiceType(), com.moego.idl.models.offering.v1.ServiceType.SERVICE_VALUE))
                .map(petDetail -> {
                    LocalDate startDate = LocalDate.parse(petDetail.getStartDate());
                    LocalDate endDate = StringUtils.hasText(petDetail.getEndDate())
                            ? LocalDate.parse(petDetail.getEndDate())
                            : startDate;
                    LocalTime startTime = LocalTime.ofSecondOfDay(petDetail.getStartTime() * 60);
                    LocalTime endTime = LocalTime.ofSecondOfDay(petDetail.getEndTime() * 60);

                    return Pair.of(LocalDateTime.of(startDate, startTime), LocalDateTime.of(endDate, endTime));
                })
                .reduce((pair1, pair2) -> {
                    LocalDateTime earliestStart =
                            pair1.getFirst().isBefore(pair2.getFirst()) ? pair1.getFirst() : pair2.getFirst();
                    LocalDateTime latestEnd =
                            pair1.getSecond().isAfter(pair2.getSecond()) ? pair1.getSecond() : pair2.getSecond();
                    return Pair.of(earliestStart, latestEnd);
                })
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet details must be specified"));
    }

    @Deprecated
    // @Transactional
    public ResponseResult<Integer> checkInOrOutGroomingAppointment(CheckParams checkParams, Boolean isIn) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                checkParams.getId(), checkParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        if (AppointmentStatusEnum.CANCELED
                .getValue()
                .equals(moeGroomingAppointment.getStatus())) { // fixme 只检查status，忽略is_deprecate？
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "the appointment is cancel. ");
        }
        AppointmentStatusEnum status = AppointmentStatusEnum.values()[moeGroomingAppointment.getStatus()];

        Long checkInTime = moeGroomingAppointment.getCheckInTime();
        moeGroomingAppointment = new MoeGroomingAppointment();
        if (isIn) {
            moeGroomingAppointment.setCheckInTime(
                    checkParams.getTime() != null ? checkParams.getTime() : CommonUtil.get10Timestamp());
            if (AppointmentStatusEnum.UNCONFIRMED.equals(status) || AppointmentStatusEnum.CONFIRMED.equals(status)) {
                moeGroomingAppointment.setStatusBeforeCheckin(status);
            }
        } else {
            if (PrimitiveTypeUtil.isNumberNullOrZero(checkInTime)) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "please check in first");
            }
            if (hasRelateWaitList(moeGroomingAppointment)) {
                waitListService.batchDeleteRelatedWaitList(
                        moeGroomingAppointment.getCompanyId(),
                        moeGroomingAppointment.getBusinessId().longValue(),
                        waitListService.getBusinessTimeZone(
                                moeGroomingAppointment.getBusinessId().longValue()),
                        Collections.singletonList(moeGroomingAppointment.getId()));
            }
            moeGroomingAppointment.setCheckOutTime(
                    checkParams.getTime() != null ? checkParams.getTime() : CommonUtil.get10Timestamp());
            moeGroomingAppointment.setStatus(AppointmentStatusEnum.FINISHED.getValue());
            moeGroomingAppointment.setStatusBeforeFinish(status);
        }

        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        moeGroomingAppointment.setId(checkParams.getId());
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        autoReviewBoosterCheck(moeGroomingAppointmentMapper.selectByPrimaryKey(moeGroomingAppointment.getId()));
        return ResponseResult.success(i);
    }

    // @Transactional
    public ResponseResult<Integer> editAppointmentNotification(EditIdParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setIsPustNotification(BooleanEnum.VALUE_TRUE);
        moeGroomingAppointment.setId(editIdParams.getId());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        return ResponseResult.success(i);
    }

    // @Transactional
    // https://sentry.moego.pet/organizations/moego/issues/281/?environment=prod&project=5&query=is%3Aunresolved
    public ResponseResult<Integer> editAppointmentPaid(EditIdParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        MoeGroomingAppointment toUpdate = new MoeGroomingAppointment();
        toUpdate.setId(editIdParams.getId());
        // appointment是取消状态，即支付了no show fee之后，不更新paid状态
        if (AppointmentStatusEnum.CANCELED.getValue().equals(moeGroomingAppointment.getStatus())
                && editIdParams.getNowShowFee() != null) {
            toUpdate.setNoShowFee(editIdParams.getNowShowFee());
            int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(toUpdate);
            if (i > 0) {
                ActivityLogRecorder.record(
                        editIdParams.getBusinessId(),
                        AppointmentAction.UPDATE_NO_SHOW,
                        ResourceType.APPOINTMENT,
                        toUpdate.getId(),
                        toUpdate);
            }
            return ResponseResult.success(i);
        }
        toUpdate.setIsPaid(editIdParams.getIsPaid());
        toUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        // 定金支付成功后自动confirm，OB定金除外
        if (Boolean.TRUE.equals(editIdParams.getIsDeposit()) && !Boolean.TRUE.equals(editIdParams.getIsFromOB())) {
            switch (AppointmentStatusEnum.values()[moeGroomingAppointment.getStatus()]) {
                case UNCONFIRMED -> {
                    toUpdate.setStatus(AppointmentStatusEnum.CONFIRMED.getValue());
                    toUpdate.setConfirmedTime(toUpdate.getUpdateTime());
                    toUpdate.setConfirmByType(GroomingAppointmentEnum.CONFIRM_TYPE_BY_DEPOSIT);
                }
                case CHECK_IN -> toUpdate.setStatusBeforeCheckin(AppointmentStatusEnum.CONFIRMED);
                case READY -> {
                    toUpdate.setStatusBeforeCheckin(AppointmentStatusEnum.CONFIRMED);
                    if (Objects.equals(AppointmentStatusEnum.UNKNOWN, moeGroomingAppointment.getStatusBeforeReady())
                            || Objects.equals(
                                    AppointmentStatusEnum.UNCONFIRMED, moeGroomingAppointment.getStatusBeforeReady())) {
                        toUpdate.setStatusBeforeReady(AppointmentStatusEnum.CONFIRMED);
                    }
                }
                case FINISHED -> {
                    toUpdate.setStatusBeforeCheckin(AppointmentStatusEnum.CONFIRMED);
                    if (Objects.equals(AppointmentStatusEnum.UNKNOWN, moeGroomingAppointment.getStatusBeforeFinish())
                            || Objects.equals(
                                    AppointmentStatusEnum.UNCONFIRMED,
                                    moeGroomingAppointment.getStatusBeforeFinish())) {
                        toUpdate.setStatusBeforeFinish(AppointmentStatusEnum.CONFIRMED);
                    }
                    if (Objects.equals(AppointmentStatusEnum.UNKNOWN, moeGroomingAppointment.getStatusBeforeReady())
                            || Objects.equals(
                                    AppointmentStatusEnum.UNCONFIRMED, moeGroomingAppointment.getStatusBeforeReady())) {
                        toUpdate.setStatusBeforeReady(AppointmentStatusEnum.CONFIRMED);
                    }
                }
                default -> {}
            }
        }
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(toUpdate);
        if (i > 0) {
            ActivityLogRecorder.record(
                    editIdParams.getBusinessId(),
                    AppointmentAction.UPDATE_PAYMENT_STATUS,
                    ResourceType.APPOINTMENT,
                    toUpdate.getId(),
                    toUpdate);
            if (AppointmentStatusEnum.UNCONFIRMED.equals(
                            AppointmentStatusEnum.fromValue(moeGroomingAppointment.getStatus()))
                    && AppointmentStatusEnum.CONFIRMED.equals(AppointmentStatusEnum.fromValue(toUpdate.getStatus()))) {
                ActivityLogRecorder.record(
                        editIdParams.getBusinessId(),
                        Optional.ofNullable(AuthContext.get().staffId()).orElse(0L),
                        AppointmentAction.UPDATE_STATUS,
                        ResourceType.APPOINTMENT,
                        toUpdate.getId(),
                        new UpdateStatusLogDTO(AppointmentStatusEnum.UNCONFIRMED, AppointmentStatusEnum.CONFIRMED));
            }
        }
        return ResponseResult.success(i);
    }

    public Boolean apptReopen(Integer groomingId, Integer businessId) {
        // 获取预约并检查
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (moeGroomingAppointment == null
                || !moeGroomingAppointment.getBusinessId().equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        // 获取invoice并检查
        MoeGroomingInvoice moeGroomingInvoice = invoiceService.queryInvoiceByGroomingId(businessId, groomingId);
        if (moeGroomingInvoice == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        // 如果appt已取消，不能reopen，目前只能针对finish的预约reopen
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "appointment has been cancelled, please refresh and try again.");
        }

        if (bwListManager.isInWhiteList(BWListManager.ORDER_REINVENT, businessId.toString())
                && InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(moeGroomingInvoice.getStatus())) {
            // 已经完成的订单不允许更新
            throw ExceptionUtil.bizException(Code.CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE, "Invoice is finished.");
        }

        MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
        /**
         * 设置 payment 状态为 partial paid(仅仅在full paid时)
         * 设置 appointment 状态为 confirmed
         * 清除 check out 记录
         */
        if (GroomingAppointmentEnum.PAID.equals(moeGroomingAppointment.getIsPaid())) {
            updateBean.setIsPaid(GroomingAppointmentEnum.PARTIAL_PAY);
        }
        updateBean.setStatus(AppointmentStatusEnum.CONFIRMED.getValue());
        updateBean.setCheckOutTime((long) 0);
        updateBean.setId(groomingId);
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        switch (moeGroomingAppointment.getStatusBeforeFinish()) {
            case UNCONFIRMED, CONFIRMED -> {
                updateBean.setCheckInTime(0L);
                updateBean.setReadyTime(0L);
            }
            case CHECK_IN -> {
                updateBean.setReadyTime(0L);
            }
            default -> {}
        }
        updateBean.setStatusBeforeFinish(AppointmentStatusEnum.UNKNOWN);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);
        // 修改invoice status为正在进行
        orderService.updateOrderStatus(moeGroomingInvoice.getId(), InvoiceStatusEnum.INVOICE_STATUS_PROCESSING);
        // reopen时失效掉tip split记录
        splitTipsService.invalidTipSplitRecord(businessId, moeGroomingInvoice.getId());
        return true;
    }

    /**
     * 退款和 mark as no show 均只支持处理当前预约
     */
    public Integer cancelAppointmentRepeat(Integer repeatType, Integer tokenBusinessId, CancelParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(editIdParams.getId());
        if (moeGroomingAppointment == null || !tokenBusinessId.equals(moeGroomingAppointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        boolean isDeclineOBRequest = Objects.equals(
                moeGroomingAppointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB);
        if (isDeclineOBRequest) {
            mqService.publishBookingRequestEvent(new BookingRequestEventParams()
                    .setBusinessId(moeGroomingAppointment.getBusinessId())
                    .setAppointmentId(moeGroomingAppointment.getId())
                    .setEvent(BookingRequestEventParams.BookingRequestEvent.DECLINED));
        }

        // 已有 waitList 解绑
        waitListService.detachFromOldAppointment(moeGroomingAppointment);
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        if (repeatId == null || repeatId == 0) {
            repeatType = 1;
        }
        // 先取消第一个预约
        editAppointmentCancel(editIdParams, editIdParams.getReleasePreAuth(), false); // cancel repeat first appt

        pushAppointmentCanceled(moeGroomingAppointment);

        if (repeatType == null || Objects.equals(RepeatModifyTypeEnum.ONLY_THIS.getRepeatType(), repeatType)) {
            ThreadPool.execute(() -> {
                appointmentTrackingServiceBlockingStub.updateAppointmentTracking(
                        UpdateAppointmentTrackingRequest.newBuilder()
                                .setCompanyId(moeGroomingAppointment.getCompanyId())
                                .addAppointmentIds(moeGroomingAppointment.getId())
                                .setAppointmentTracking(UpdateAppointmentTrackingDef.newBuilder()
                                        .setStaffLocationStatus(StaffLocationStatus.SHARING_STOPPED)
                                        .build())
                                .build());
            });
            // 只 apply 当前预约，不需要处理其余repeat预约
            return 1;
        } else {
            List<Integer> needCancelApptIds = null;
            if (Objects.equals(RepeatModifyTypeEnum.THIS_AND_FOLLOWING.getRepeatType(), repeatType)) {
                // 修改本预约及之后预约
                needCancelApptIds =
                        moeGroomingAppointmentMapper
                                .queryApptsByRepeatId(
                                        tokenBusinessId, repeatId, moeGroomingAppointment.getAppointmentDate())
                                .stream()
                                .map(a -> a.getId())
                                .collect(toList());
            } else if (Objects.equals(RepeatModifyTypeEnum.ALL.getRepeatType(), repeatType)) {
                // 修改所有未完成预约
                needCancelApptIds =
                        moeGroomingAppointmentMapper.queryApptsByRepeatId(tokenBusinessId, repeatId, null).stream()
                                .map(a -> a.getId())
                                .collect(toList());
            }
            List<Integer> ids = new ArrayList<>(moeGroomingAppointment.getId());
            if (needCancelApptIds != null) {
                ids.addAll(needCancelApptIds);
            }
            ThreadPool.execute(() -> {
                appointmentTrackingServiceBlockingStub.updateAppointmentTracking(
                        UpdateAppointmentTrackingRequest.newBuilder()
                                .setCompanyId(moeGroomingAppointment.getCompanyId())
                                .addAllAppointmentIds(
                                        ids.stream().map(Integer::longValue).collect(toList()))
                                .setAppointmentTracking(UpdateAppointmentTrackingDef.newBuilder()
                                        .setStaffLocationStatus(StaffLocationStatus.SHARING_STOPPED)
                                        .build())
                                .build());
            });
            if (CollectionUtils.isEmpty(needCancelApptIds)) {
                return 1;
            }
            moeGroomingAppointmentMapper.updateBatchCancelByPrimaryKey(
                    tokenBusinessId,
                    null,
                    editIdParams.getAccountId(),
                    editIdParams.getCancelByType(),
                    CommonUtil.get10Timestamp(),
                    CommonUtil.get10Timestamp(),
                    needCancelApptIds);
            // 在grooming notes中记录 cancel reason
            List<MoeGroomingNote> recordList = new ArrayList<>();
            for (Integer needCancelApptId : needCancelApptIds) {
                recordList.add(createGroomingNote(
                        tokenBusinessId,
                        moeGroomingAppointment.getCustomerId(),
                        editIdParams.getAccountId(),
                        needCancelApptId,
                        GroomingAppointmentEnum.NOTE_CANCEL,
                        editIdParams.getCancelReason()));
            }
            moeGroomingNoteService.batchInsert(recordList);
            // qb cancel
            List<Integer> finalNeedCancelApptIds = needCancelApptIds;
            ThreadPool.execute(() -> {
                // cancel appt
                for (Integer needCancelApptId : finalNeedCancelApptIds) {
                    calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                            tokenBusinessId, needCancelApptId, null, true); // batch cancel appt
                    quickBooksService.addRedisSyncGroomingData(tokenBusinessId, needCancelApptId, null);
                }
                // 标记订单状态、释放product库存
                orderService.updateOrderWhenCancelAppts(tokenBusinessId, finalNeedCancelApptIds);
                deleteAppointmentTask(finalNeedCancelApptIds, moeGroomingAppointment.getServiceTypeInclude());
            });
            mqService.publishAppointmentCancelEvent(moeGroomingAppointment, needCancelApptIds, true);
            publisher.publishEvent(new UpdateCustomerEvent(this)
                    .setBusinessId(moeGroomingAppointment.getBusinessId())
                    .setCustomerId(moeGroomingAppointment.getCustomerId()));
            return needCancelApptIds.size();
        }
    }

    private void deleteAppointmentTask(List<Integer> appointmentIds, Integer serviceTypeInclude) {
        boolean isBDAppt = ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                || ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude);
        if (!isBDAppt) {
            return;
        }
        // BD only
        appointmentTaskStub.deleteAppointmentTask(DeleteAppointmentTaskRequest.newBuilder()
                .setFilter(DeleteAppointmentTaskRequest.Filter.newBuilder()
                        .addAllAppointmentIds(appointmentIds.stream()
                                .map(Integer::longValue)
                                .distinct()
                                .toList())
                        .build())
                .build());
    }

    private void pushAppointmentCanceled(MoeGroomingAppointment appointment) {
        if (Objects.equals(appointment.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED);
        } else {
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_APPOINTMENT_CANCELLED);
        }
    }

    // @Transactional
    // https://sentry.moego.pet/organizations/moego/issues/281/?environment=prod&project=5&query=is%3Aunresolved
    public ResponseResult<Integer> editAppointmentCancel(
            CancelParams editIdParams, boolean shouldReleasePreAuth, boolean isGcSyncDelay) {
        MoeGroomingAppointment oldMoeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (oldMoeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        // 根据 checkFinishStatus 字段来决定是否要判断预约状态，如果是已经 finish/cancelled 了，则停止 cancel 操作，目前是针对 replyN 内部调用做这个判断
        if (Boolean.TRUE.equals(editIdParams.getCheckFinishStatus())
                && isApptFinishOrCancelled(oldMoeGroomingAppointment.getStatus())) {
            log.info(
                    "editAppointmentCancel appointment's current status is {}, could not cancel!",
                    oldMoeGroomingAppointment.getStatus());
            return ResponseResult.success(0);
        }

        // 如果是ob request，取消不发送通知
        boolean isObRequest = false;
        if (oldMoeGroomingAppointment.getBookOnlineStatus().equals(ServiceEnum.OB_NOT_CONFIRM)) {
            editIdParams.setCancelReason(ServiceEnum.OB_CANCEL_REASON); // fixme 用户提交的reason直接丢弃？
            isObRequest = true;
        }
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(editIdParams.getId());
        appointment.setStatus(AppointmentStatusEnum.CANCELED.getValue());
        appointment.setBookOnlineStatus(ServiceEnum.OB_DEFAULT_NORMAL);
        appointment.setIsWaitingList(ServiceEnum.IS_WAITING_LIST_FALSE);
        appointment.setNoShow(editIdParams.getNoShow());
        // NoShow 时记录 NoShow staffID
        if (GroomingAppointmentEnum.NO_SHOW_TRUE.equals(editIdParams.getNoShow())
                && editIdParams.getAccountId() != null) {
            appointment.setNoShowBy((long) editIdParams.getAccountId());
        }
        // 已经被 cancel 的预约继续标记成 no-show 不需要重新更新 cancelBy 和 canceled time
        if (oldMoeGroomingAppointment.getCancelBy() == 0) {
            appointment.setCancelBy(editIdParams.getAccountId());
        }
        if (oldMoeGroomingAppointment.getCanceledTime() == 0) {
            appointment.setCanceledTime(CommonUtil.get10Timestamp());
        }
        appointment.setCancelByType(editIdParams.getCancelByType());
        appointment.setUpdateTime(CommonUtil.get10Timestamp());
        int i = doAppointmentCancel(
                appointment,
                appointmentProducer.buildAppointmentCancelledEvent(editIdParams, oldMoeGroomingAppointment));

        // 如果是取消 no-show ，需要取消 no-show 订单
        if (editIdParams.getNoShow() != null
                && editIdParams.getNoShow() == 2
                && oldMoeGroomingAppointment.getNoShow() == 1) {
            orderHelper.cancelNoShowOrderForAppointment(
                    oldMoeGroomingAppointment.getBusinessId().longValue(),
                    editIdParams.getId(),
                    editIdParams.getAccountId().longValue());
        }

        // 在grooming notes中记录 cancel reason
        MoeGroomingNote moeGroomingNote = createGroomingNote(
                editIdParams.getBusinessId(),
                appointment.getCustomerId(),
                editIdParams.getAccountId(),
                editIdParams.getId(),
                GroomingAppointmentEnum.NOTE_CANCEL,
                editIdParams.getCancelReason());
        moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);

        // ob定金的取消操作
        if (isObRequest && Boolean.TRUE.equals(editIdParams.getRefundPrepaid())) {
            ThreadPool.execute(() -> {
                MoeBookOnlineDeposit deposit = moeBookOnlineDepositService.getOBDepositByGroomingId(
                        editIdParams.getBusinessId(), editIdParams.getId());
                if (deposit != null && Objects.equals(deposit.getStatus(), BookOnlineDepositConst.REQUIRE_CAPTURE)) {
                    CreateRefundByPaymentIdParams params = new CreateRefundByPaymentIdParams();
                    params.setPaymentId(deposit.getPaymentId());
                    params.setReason("cancel for online booking decline");
                    params.setBookingFee(deposit.getBookingFee());
                    iPaymentRefundClient.createRefundByPaymentId(editIdParams.getBusinessId(), params);
                    // 退款之后更新ob deposit状态
                    deposit.setStatus(BookOnlineDepositConst.CANCEL);
                    moeBookOnlineDepositService.update(deposit);
                }
            });
        }

        // ob request取消，不发送任何通知。Client portal cancel 的发送通知!
        if (!isObRequest
                || Objects.equals(
                        editIdParams.getCancelByType(), GroomingAppointmentEnum.CANCEL_TYPE_BY_CLIENT_PORTAL)) {
            ThreadPool.execute(() -> editAppointmentCancelNotify(editIdParams, oldMoeGroomingAppointment));
        }
        ThreadPool.execute(() -> {
            // cancel appt
            calendarSyncService.checkBusinessHaveGoogleCalendarSync( // editAppointmentCancel
                    oldMoeGroomingAppointment.getBusinessId(),
                    oldMoeGroomingAppointment.getId(),
                    oldMoeGroomingAppointment.getAppointmentDate(),
                    isGcSyncDelay);
            quickBooksService.addRedisSyncGroomingData(
                    oldMoeGroomingAppointment.getBusinessId(),
                    oldMoeGroomingAppointment.getId(),
                    oldMoeGroomingAppointment.getAppointmentDate());
            // 释放product库存
            orderService.updateWhenCancelAppts(
                    oldMoeGroomingAppointment.getBusinessId(),
                    Collections.singletonList(oldMoeGroomingAppointment.getId()));
            deleteAppointmentTask(
                    List.of(oldMoeGroomingAppointment.getId()), oldMoeGroomingAppointment.getServiceTypeInclude());
        });
        mqService.publishAppointmentCancelEvent(oldMoeGroomingAppointment, null, shouldReleasePreAuth);
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(oldMoeGroomingAppointment.getBusinessId())
                .setCustomerId(oldMoeGroomingAppointment.getCustomerId()));
        return ResponseResult.success(i);
    }

    @Transactional
    public Integer doAppointmentCancel(MoeGroomingAppointment appointment, EventRecord<EventData> cancelEventRecord) {
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
        Long outboxId = appointmentOutboxService.create(AppointmentProducer.APPOINTMENT_EVENT_TOPIC, cancelEventRecord);
        ThreadPool.execute(() -> {
            // 推送事件到 Event Bus
            producer.send(AppointmentProducer.APPOINTMENT_EVENT_TOPIC, cancelEventRecord);
            // 更新事件推送状态
            appointmentOutboxService.updateStatus(outboxId, OutboxSendStatus.SEND_SUCCEED);
        });

        return i;
    }

    private void editAppointmentCancelNotify(
            CancelParams editIdParams, MoeGroomingAppointment oldMoeGroomingAppointment) {
        // 获取发送的staffIds
        Set<Integer> staffIds = appointmentQueryService.getAppointmentRelatedStaffIds(editIdParams.getId());
        if (CANCEL_TYPE_BY_CLIENT.equals(editIdParams.getCancelByType())) {
            log.info("appt cancel by client notify");
            // 调用通知发送
            NotificationApptCancelledByClientParams apptCancelledByClientParams =
                    new NotificationApptCancelledByClientParams();
            apptCancelledByClientParams.setBusinessId(editIdParams.getBusinessId());
            apptCancelledByClientParams.setStaffIdList(staffIds);
            apptCancelledByClientParams.setIsNotifyBusinessOwner(Boolean.TRUE);

            // 给前端的数据体
            NotificationExtraApptCommonDto webPushDto = new NotificationExtraApptCommonDto();
            webPushDto.setAppointmentDate(oldMoeGroomingAppointment.getAppointmentDate());
            webPushDto.setAppointmentStartTime(oldMoeGroomingAppointment.getAppointmentStartTime());
            webPushDto.setNoStartTime(oldMoeGroomingAppointment.getNoStartTime());
            webPushDto.setAppointmentEndTime(oldMoeGroomingAppointment.getAppointmentEndTime());
            webPushDto.setCustomerId(oldMoeGroomingAppointment.getCustomerId());
            webPushDto.setGroomingId(oldMoeGroomingAppointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            apptCancelledByClientParams.setWebPushDto(webPushDto);
            iNotificationClient.sendApptCancelledByClient(apptCancelledByClientParams);
        } else if (Objects.equals(
                editIdParams.getCancelByType(), GroomingAppointmentEnum.CANCEL_TYPE_BY_CLIENT_PORTAL)) {
            NotificationOBRequestCanceledParams obRequestCanceledParams = new NotificationOBRequestCanceledParams();
            obRequestCanceledParams.setBusinessId(editIdParams.getBusinessId());
            obRequestCanceledParams.setStaffIdList(staffIds);
            obRequestCanceledParams.setIsNotifyBusinessOwner(Boolean.TRUE);
            // 给前端的数据体
            NotificationExtraApptCommonDto webPushDto = new NotificationExtraApptCommonDto();
            webPushDto.setAppointmentDate(oldMoeGroomingAppointment.getAppointmentDate());
            webPushDto.setAppointmentStartTime(oldMoeGroomingAppointment.getAppointmentStartTime());
            webPushDto.setNoStartTime(oldMoeGroomingAppointment.getNoStartTime());
            webPushDto.setAppointmentEndTime(oldMoeGroomingAppointment.getAppointmentEndTime());
            webPushDto.setCustomerId(oldMoeGroomingAppointment.getCustomerId());
            webPushDto.setGroomingId(oldMoeGroomingAppointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            obRequestCanceledParams.setWebPushDto(webPushDto);
            iNotificationClient.sendNotificationOBRequestCanceled(obRequestCanceledParams);
        } else {
            // Staff only 才发送通知
            if (!AppointmentUtils.isStaffOnly(oldMoeGroomingAppointment)) {
                return;
            }
            // block 不发通知
            if (AppointmentUtils.isBlock(oldMoeGroomingAppointment)) {
                return;
            }
            // 调用通知发送
            NotificationApptCancelledParams apptCancelledParams = new NotificationApptCancelledParams();
            apptCancelledParams.setBusinessId(oldMoeGroomingAppointment.getBusinessId());
            apptCancelledParams.setStaffIdList(staffIds);
            apptCancelledParams.setTokenStaffId(editIdParams.getAccountId());
            // 给前端的数据体
            NotificationExtraApptCommonDto apptCancelledDto = new NotificationExtraApptCommonDto();
            apptCancelledDto.setAppointmentDate(oldMoeGroomingAppointment.getAppointmentDate());
            apptCancelledDto.setAppointmentStartTime(oldMoeGroomingAppointment.getAppointmentStartTime());
            apptCancelledDto.setNoStartTime(oldMoeGroomingAppointment.getNoStartTime());
            apptCancelledDto.setAppointmentEndTime(oldMoeGroomingAppointment.getAppointmentEndTime());
            apptCancelledDto.setCustomerId(oldMoeGroomingAppointment.getCustomerId());
            apptCancelledDto.setGroomingId(oldMoeGroomingAppointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            apptCancelledParams.setWebPushDto(apptCancelledDto);
            iNotificationClient.sendNotificationApptCancelled(apptCancelledParams);
        }
    }

    private MoeGroomingNote createGroomingNoteWithPetInfo(
            Integer businessId,
            Integer customerId,
            Integer actionAccountId,
            Integer groomingId,
            Byte type,
            String note,
            Long petId) {
        MoeGroomingNote moeGroomingNote =
                createGroomingNote(businessId, customerId, actionAccountId, groomingId, type, note);
        moeGroomingNote.setPetId(petId);
        return moeGroomingNote;
    }

    private MoeGroomingNote createGroomingNote(
            Integer businessId,
            Integer customerId,
            Integer actionAccountId,
            Integer groomingId,
            Byte type,
            String note) {
        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(businessId);
        moeGroomingNote.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        moeGroomingNote.setCustomerId(customerId);
        moeGroomingNote.setGroomingId(groomingId);
        moeGroomingNote.setCreateBy(actionAccountId);
        moeGroomingNote.setUpdateBy(actionAccountId);
        moeGroomingNote.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setType(type);
        moeGroomingNote.setNote(note);
        return moeGroomingNote;
    }

    // @Transactional
    // https://sentry.moego.pet/organizations/moego/issues/281/?environment=prod&project=5&query=is%3Aunresolved

    public ResponseResult<Integer> editAppointmentFinish(EditIdParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
        }

        // 如果 appt 已经是 finish 状态，直接返回即可
        if (Objects.equals(AppointmentStatusEnum.FINISHED.getValue(), moeGroomingAppointment.getStatus())) {
            return ResponseResult.success(0);
        }

        // 如果appt已取消，则返回空，因为有支付noShowFee的场景，也会调用到这里，所以不能直接抛异常
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            return ResponseResult.success(0);
        }

        // 删除关联的 waitList
        if (hasRelateWaitList(moeGroomingAppointment)) {
            waitListService.batchDeleteRelatedWaitList(
                    moeGroomingAppointment.getCompanyId(),
                    moeGroomingAppointment.getBusinessId().longValue(),
                    waitListService.getBusinessTimeZone(
                            moeGroomingAppointment.getBusinessId().longValue()),
                    Collections.singletonList(moeGroomingAppointment.getId()));
        }

        MoeGroomingAppointment toUpdate = new MoeGroomingAppointment();
        toUpdate.setId(editIdParams.getId());
        toUpdate.setStatus(AppointmentStatusEnum.FINISHED.getValue());
        toUpdate.setStatusBeforeFinish(AppointmentStatusEnum.values()[moeGroomingAppointment.getStatus()]);
        // 如从unconfirm直接finish checkIn checkOut时间为当前时间
        // finish判断是否check in 如果没有，默认当前时间
        final Long currentTime = CommonUtil.get10Timestamp();
        if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getCheckInTime())) {
            toUpdate.setCheckInTime(currentTime);
            toUpdate.setCheckOutTime(currentTime);
            toUpdate.setReadyTime(currentTime);
        } else if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getReadyTime())) {
            toUpdate.setReadyTime(currentTime);
            toUpdate.setCheckOutTime(currentTime);
        } else if (PrimitiveTypeUtil.isNumberNullOrZero(moeGroomingAppointment.getCheckOutTime())) {
            toUpdate.setCheckOutTime(currentTime);
        }
        toUpdate.setUpdateTime(currentTime);
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(toUpdate);
        if (i > 0) {
            ActivityLogRecorder.record(
                    editIdParams.getBusinessId(),
                    Optional.ofNullable(AuthContext.get().staffId()).orElse(0L), // 如果拿不到 staff ID 就用 0，表示 System 写入
                    AppointmentAction.UPDATE_STATUS,
                    ResourceType.APPOINTMENT,
                    toUpdate.getId(),
                    new UpdateStatusLogDTO(
                            AppointmentStatusEnum.fromValue(moeGroomingAppointment.getStatus()),
                            AppointmentStatusEnum.FINISHED));
            appointmentProducer.pushAppointmentFinishedEvent(toUpdate.getId());
        }
        autoReviewBoosterCheck(moeGroomingAppointmentMapper.selectByPrimaryKey(toUpdate.getId()));
        return ResponseResult.success(i);
    }

    public void autoReviewBoosterCheck(MoeGroomingAppointment appt) {
        try {
            if (AppointmentStatusEnum.FINISHED.getValue().equals(appt.getStatus())) {
                ThreadPool.execute(() -> {
                    iReviewBoosterTaskClient.reviewBoosterForGroomingIdCheck(
                            appt.getBusinessId(), appt.getId(), appt.getCheckOutTime());
                });
            }
        } catch (Exception e) {
            log.error("grooming autoReviewBoosterCheck error", e);
        }
    }

    // @Transactional
    // https://sentry.moego.pet/organizations/moego/issues/281/?environment=prod&project=5&query=is%3Aunresolved
    public GroomingTicketDetailDTO queryTicketDetail(
            Long companyId,
            Integer tokenBusinessId,
            Integer id,
            Integer tokenStaffId,
            boolean isMigrated,
            List<Integer> serviceItems) {
        GroomingTicketDetailDTO groomingTicketDetailDTO = new GroomingTicketDetailDTO();
        // 查询预约详细
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(id);
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        if (isMigrated) {
            if (!Objects.equals(moeGroomingAppointment.getCompanyId(), companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
            }
        } else {
            if (!Objects.equals(moeGroomingAppointment.getBusinessId(), tokenBusinessId)) {
                throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
            }
        }
        boolean isGetObInfo =
                GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.equals(moeGroomingAppointment.getBookOnlineStatus());
        // convert预约信息
        convertDTO(groomingTicketDetailDTO, moeGroomingAppointment);
        groomingTicketDetailDTO.setBookOnlineStatus(moeGroomingAppointment.getBookOnlineStatus());
        groomingTicketDetailDTO.setIsWaitingList(moeGroomingAppointment.getIsWaitingList());
        groomingTicketDetailDTO.setServiceItems(
                ServiceItemEnum.convertBitValueList(moeGroomingAppointment.getServiceTypeInclude()).stream()
                        .map(ServiceItemEnum::getServiceItem)
                        .distinct()
                        .toList());

        // grooming note
        groomingTicketDetailDTO.setGroomingTicketComments(
                moeGroomingNoteService.getGroomingTicketComments(moeGroomingAppointment));
        groomingTicketDetailDTO.setGroomingAlertNotes(
                moeGroomingNoteService.getGroomingAlertNotes(moeGroomingAppointment));
        groomingTicketDetailDTO.setAdditionalNote(moeGroomingNoteService.getAdditionalNote(id));

        Integer businessId = moeGroomingAppointment.getBusinessId();
        Integer customerId = moeGroomingAppointment.getCustomerId();

        // 查询cancelReason信息
        MoeGroomingNote cancelNote = moeGroomingNoteService.getNoteByGroomingIdAndType(
                moeGroomingAppointment.getId(), GroomingAppointmentEnum.NOTE_CANCEL.intValue());
        if (cancelNote != null) {
            groomingTicketDetailDTO.setCancelReason(cancelNote.getNote());
        }

        // 查询宠物服务信息
        List<GroomingPetInfoDetailDTO> petInfoDetails =
                getGroomingPetInfoDetailDTOS(isGetObInfo, businessId, moeGroomingAppointment, serviceItems);

        groomingTicketDetailDTO.setPetInfoDetails(petInfoDetails);
        groomingTicketDetailDTO.setAllPetsStartAtSameTime(moePetDetailService.isAllPetsStartAtSameTime(petInfoDetails));
        // 查询顾客信息
        CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
        customerInfoIdParams.setCompanyId(companyId);
        customerInfoIdParams.setBusinessId(businessId);
        customerInfoIdParams.setCustomerId(customerId);
        // 调用customer服务查询顾客信息
        CustomerPrimaryDto customerDetailWithPrimary =
                iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);
        GroomingCustomerInfoDTO groomingCustomerInfoDTO = new GroomingCustomerInfoDTO();
        BeanUtils.copyProperties(customerDetailWithPrimary, groomingCustomerInfoDTO);
        if (Objects.nonNull(customerDetailWithPrimary.getStatus())) {
            groomingCustomerInfoDTO.setIsDeleted(
                    Objects.equals(DeleteStatusEnum.STATUS_DELETE, customerDetailWithPrimary.getStatus()));
        }

        CustomerAddressDto address = customerDetailWithPrimary.getAddress();
        // 组装address
        StringJoiner fullAddress = AddressUtil.getFullAddress(
                address.getAddress1(),
                address.getAddress2(),
                address.getCity(),
                address.getState(),
                address.getCountry(),
                address.getZipcode());

        groomingCustomerInfoDTO.setClientFullAddress(fullAddress.toString());
        StaffPermissions staffPermissions = iBusinessStaffClient.getBusinessRoleByStaffId(tokenStaffId);
        boolean isHidePhoneNumber =
                !PermissionUtil.checkStaffPermissionsInfo(staffPermissions, PermissionUtil.VIEW_CLIENT_PHONE);
        if (isHidePhoneNumber) {
            groomingCustomerInfoDTO.setPhoneNumber(
                    PermissionUtil.phoneNumberConfusion(groomingCustomerInfoDTO.getPhoneNumber()));
            groomingCustomerInfoDTO.setOwnerPhoneNumber(
                    PermissionUtil.phoneNumberConfusion(groomingCustomerInfoDTO.getOwnerPhoneNumber()));
        }
        boolean isHideEmail =
                !PermissionUtil.checkStaffPermissionsInfo(staffPermissions, PermissionUtil.VIEW_CLIENT_EMAIL);
        if (isHideEmail) {
            groomingCustomerInfoDTO.setEmail(PermissionUtil.emailConfusion(groomingCustomerInfoDTO.getEmail()));
        }

        CustomerLastFinishedApptMapDto mapDto = appointmentQueryService.getCustomerLastFinishedAppointment(
                isMigrated, companyId, businessId, Collections.singletonList(customerId));
        boolean isNewCustomer = Objects.isNull(mapDto.getLastFinishedApptMap())
                || Objects.isNull(mapDto.getLastFinishedApptMap().get(customerId));
        groomingCustomerInfoDTO.setIsNewCustomer(isNewCustomer);

        groomingTicketDetailDTO.setGroomingCustomerInfo(groomingCustomerInfoDTO);
        // 查询预约创建人信息
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(moeGroomingAppointment.getCreatedById());

        MoeStaffDto staffDto = iBusinessStaffClient.getStaff(staffIdParams);
        if (staffDto != null) {
            groomingTicketDetailDTO.setCreateByLastName(staffDto.getLastName());
            groomingTicketDetailDTO.setCreateByFirstName(staffDto.getFirstName());
        }

        // 判断是否有需要签署的 agreement
        BatchGetAgreementUnsignedAppointmentResponse response = agreementClient.batchGetAgreementUnsignedAppointment(
                BatchGetAgreementUnsignedAppointmentRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addCustomerWithAppointmentId(
                                BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId.newBuilder()
                                        .setCustomerId(groomingTicketDetailDTO.getCustomerId())
                                        .setAppointmentId(groomingTicketDetailDTO.getId())
                                        .build())
                        .build());
        List<Long> unsignedAppointmentIds = response.getAppointmentIdList();
        groomingTicketDetailDTO.setRequiredSign(!CollectionUtils.isEmpty(unsignedAppointmentIds)
                && unsignedAppointmentIds.contains(
                        groomingTicketDetailDTO.getId().longValue()));

        // 查询是否已经发送过review booster
        List<ReviewBoosterRecordDTO> reviewRecords =
                iBoosterClient.getAppointmentReviewRecord(new GetReviewBoosterRecordParams()
                        .setBusinessId(businessId)
                        .setCustomerId(groomingTicketDetailDTO.getCustomerId())
                        .setGroomingId(groomingTicketDetailDTO.getId())
                        .setSource(ReviewBoosterConst.REVIEW_SOURCE_SMS));
        groomingTicketDetailDTO.setReviewBoosterSent(!CollectionUtils.isEmpty(reviewRecords));

        // For new order: 不返回 invoice 和 ob_deposit 信息
        // see
        // https://moegoworkspace.slack.com/archives/C0890NTRP2L/p1749089747853109?thread_ts=1749033126.255849&cid=C0890NTRP2L
        if (!newOrderHelper.isNewOrder(id)) {
            // 查询预约 invoice id
            MoeGroomingInvoice invoice = invoiceService.queryInvoiceByGroomingId(businessId, id);
            if (invoice != null) {
                groomingTicketDetailDTO.setInvoiceId(invoice.getId());
                groomingTicketDetailDTO.setPaidAmount(invoice.getPaidAmount());
                groomingTicketDetailDTO.setRefundAmount(invoice.getRefundedAmount());
                // 查询定金记录
                MoeBookOnlineDeposit deposit = moeBookOnlineDepositService.getOBDepositByGroomingId(businessId, id);
                if (!Objects.isNull(deposit) && DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                    groomingTicketDetailDTO.setPrepaidAmount(deposit.getAmount());
                    groomingTicketDetailDTO.setPrepayStatus(deposit.getStatus());
                    groomingTicketDetailDTO.setPrepayRate(
                            moeBookOnlineDepositService.getPrepayRate(deposit, invoice.getPaymentAmount()));
                }
            }
        }

        // 查询noshow invoice信息
        MoeGroomingInvoice noShowInvoice = invoiceService.queryInvoiceByGroomingIdAndType(
                groomingTicketDetailDTO.getId(), InvoiceStatusEnum.TYPE_NOSHOW);
        if (noShowInvoice != null && noShowInvoice.getId() > 0) {
            groomingTicketDetailDTO.setNoShowStatus(noShowInvoice.getStatus());
            groomingTicketDetailDTO.setNoShowInvoiceId(noShowInvoice.getId());
            groomingTicketDetailDTO.setNoShowPaymentStatus(noShowInvoice.getPaymentStatus());
        }
        // 如果是ob的请求接口，就需要返回client相关的数据
        if (isGetObInfo) {
            Set<Integer> petIds = new HashSet<>();
            groomingTicketDetailDTO.getPetInfoDetails().forEach(petDetail -> {
                petIds.add(petDetail.getPetId());
                petDetail.setPetAnswerJson("");
                petDetail.setPetQuestionJson("");
            });
            CustomerIdWithPetIdsParams petIdsParams = new CustomerIdWithPetIdsParams();
            petIdsParams.setBusinessId(businessId);
            petIdsParams.setCustomerId(groomingTicketDetailDTO.getCustomerId());
            petIdsParams.setPetIds(new ArrayList<>(petIds));
            List<BookOnlineQuestionSaveDto> questionSaveDtos =
                    moeGroomingBookOnlineService.getQuestionSaveByCustomerInfo(petIdsParams);
            groomingTicketDetailDTO.setCustomerAnswerJson("");
            groomingTicketDetailDTO.setCustomerQuestionJson("");
            for (BookOnlineQuestionSaveDto questionSaveDto : questionSaveDtos) {
                if (questionSaveDto.getType().equals(QuestionConst.TYPE_PET_OWNER_QUESTION)) {
                    groomingTicketDetailDTO.setCustomerAnswerJson(questionSaveDto.getQuestionJson());
                    groomingTicketDetailDTO.setCustomerQuestionJson(questionSaveDto.getFormJson());
                }
                groomingTicketDetailDTO.getPetInfoDetails().forEach(petDetail -> {
                    if (questionSaveDto.getType().equals(QuestionConst.TYPE_PET_QUESTION)
                            && questionSaveDto.getPetId().equals(petDetail.getPetId())) {
                        petDetail.setPetAnswerJson(questionSaveDto.getQuestionJson());
                        petDetail.setPetQuestionJson(questionSaveDto.getFormJson());
                    }
                });
            }
        }

        groomingTicketDetailDTO.setPreAuthInfo(paymentPreAuthClient.queryByTicketId(businessId, id));

        // 查询关联的 wait list
        if (hasRelateWaitList(moeGroomingAppointment)) {
            MoeWaitList waitList = waitListService.getWaitListByAppointment(businessId.longValue(), id.longValue());
            if (waitList != null) {
                groomingTicketDetailDTO.setWaitListId(waitList.getId());
            }
        }
        // 查 appointment tracking, 报错不展示
        try {
            var tracking = appointmentTrackingServiceBlockingStub
                    .getOrInitAppointmentTracking(GetOrInitAppointmentTrackingRequest.newBuilder()
                            .setAppointmentId(id)
                            .build())
                    .getAppointmentTracking();
            groomingTicketDetailDTO.setAppointmentTracking(AppointmentConverter.INSTANCE.toTrackingViewDto(tracking));
        } catch (Exception e) {
            // TODO: 加 datadog log monitor
            log.error("query appointment tracking error", e);
        }
        return groomingTicketDetailDTO;
    }

    boolean hasRelateWaitList(MoeGroomingAppointment moeGroomingAppointment) {
        return WaitListStatusEnum.APPTANDWAITLIST.equals(moeGroomingAppointment.getWaitListStatus());
    }

    public GroomingTicketDetailDTO getAppointmentDetailForGoogleCalendarSync(
            Integer tokenBusinessId, Integer groomingId) {
        GroomingTicketDetailDTO groomingTicketDetailDTO = new GroomingTicketDetailDTO();
        // 查询预约详细
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(groomingId, tokenBusinessId);
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        // convert预约信息
        convertDTO(groomingTicketDetailDTO, moeGroomingAppointment);
        groomingTicketDetailDTO.setBookOnlineStatus(moeGroomingAppointment.getBookOnlineStatus());
        groomingTicketDetailDTO.setIsWaitingList(moeGroomingAppointment.getIsWaitingList());

        // 查询宠物服务信息
        List<GroomingPetInfoDetailDTO> petInfoDetails = getGroomingPetInfoDetailDTOS(
                false, tokenBusinessId, moeGroomingAppointment, List.of(ServiceItemEnum.GROOMING.getServiceItem()));

        groomingTicketDetailDTO.setPetInfoDetails(petInfoDetails);
        // 查询顾客信息
        CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
        customerInfoIdParams.setBusinessId(moeGroomingAppointment.getBusinessId());
        customerInfoIdParams.setCustomerId(moeGroomingAppointment.getCustomerId());
        // 调用customer服务查询顾客信息
        CustomerPrimaryDto customerDetailWithPrimary =
                iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);
        GroomingCustomerInfoDTO groomingCustomerInfoDTO = new GroomingCustomerInfoDTO();
        BeanUtils.copyProperties(customerDetailWithPrimary, groomingCustomerInfoDTO);

        CustomerAddressDto address = customerDetailWithPrimary.getAddress();
        // 组装address
        StringJoiner fullAddress = AddressUtil.getFullAddress(
                address.getAddress1(),
                address.getAddress2(),
                address.getCity(),
                address.getState(),
                address.getCountry(),
                address.getZipcode());

        groomingCustomerInfoDTO.setClientFullAddress(fullAddress.toString());
        groomingTicketDetailDTO.setGroomingCustomerInfo(groomingCustomerInfoDTO);
        return groomingTicketDetailDTO;
    }

    private List<GroomingPetInfoDetailDTO> getGroomingPetInfoDetailDTOS(
            Boolean isGetObInfo,
            Integer tokenBusinessId,
            MoeGroomingAppointment moeGroomingAppointment,
            List<Integer> serviceItems) {
        List<GroomingPetInfoDetailDTO> petInfoDetails = new ArrayList<>();
        List<GroomingPetDetailDTO> moeGroomingPetDetails =
                moeGroomingPetDetailMapper.queryPetDetailByServiceItems(moeGroomingAppointment.getId(), serviceItems);

        List<Integer> groomingServiceIdList = moeGroomingPetDetails.stream()
                .filter(GroomingPetDetailDTO::getEnableOperation)
                .map(GroomingPetDetailDTO::getId)
                .toList();
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        tokenBusinessId, groomingServiceIdList);

        // boarding split lodging
        var boardingSplitLodgings = boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAppointmentIds(moeGroomingAppointment.getId())
                        .build())
                .getBoardingSplitLodgingsList();

        moeGroomingPetDetails.forEach(groomingPetDetailDTO -> petInfoDetails.stream()
                .filter(petInfoDetail -> petInfoDetail.getPetId().equals(groomingPetDetailDTO.getPetId()))
                .findFirst()
                .ifPresentOrElse(
                        petInfoDetail -> petInfoDetail
                                .getGroomingPetServiceDTOS()
                                .add(getGroomingPetServiceDTO(
                                        moeGroomingAppointment,
                                        groomingPetDetailDTO,
                                        operationMap,
                                        boardingSplitLodgings)),
                        () -> {
                            GroomingPetInfoDetailDTO groomingPetInfoDetailDTO = new GroomingPetInfoDetailDTO();
                            groomingPetInfoDetailDTO.setPetId(groomingPetDetailDTO.getPetId());
                            List<GroomingPetServiceDTO> list = new ArrayList<>();
                            list.add(getGroomingPetServiceDTO(
                                    moeGroomingAppointment, groomingPetDetailDTO, operationMap, boardingSplitLodgings));
                            groomingPetInfoDetailDTO.setGroomingPetServiceDTOS(list);
                            petInfoDetails.add(groomingPetInfoDetailDTO);
                        }));

        // 调用customer查询宠物信息和petcode信息
        List<Integer> petIds = new ArrayList<>();
        for (GroomingPetInfoDetailDTO groomingPetInfoDetailDTO : petInfoDetails) {
            petIds.add(groomingPetInfoDetailDTO.getPetId());
        }
        List<CustomerPetPetCodeDTO> customerPetPetCodeListByIdList =
                iPetCodeClient.getCustomerPetPetCodeListByIdList(isGetObInfo, petIds);

        for (CustomerPetPetCodeDTO customerPetPetCodeDTO : customerPetPetCodeListByIdList) {
            for (GroomingPetInfoDetailDTO groomingPetInfoDetailDTO : petInfoDetails) {
                if (groomingPetInfoDetailDTO.getPetId().equals(customerPetPetCodeDTO.getPetId())) {
                    BeanUtils.copyProperties(customerPetPetCodeDTO, groomingPetInfoDetailDTO);
                    List<GroomingPetCodeDTO> petCodes = new ArrayList<>();
                    for (MoePetCodeInfoDTO moePetCodeInfoDTO : customerPetPetCodeDTO.getMoePetCodeInfos()) {
                        GroomingPetCodeDTO groomingPetCodeDTO = new GroomingPetCodeDTO();
                        BeanUtils.copyProperties(moePetCodeInfoDTO, groomingPetCodeDTO);
                        petCodes.add(groomingPetCodeDTO);
                    }
                    groomingPetInfoDetailDTO.setPetCodes(petCodes);
                    List<VaccineBindingRecordDto> vaccineBindings = new ArrayList<>();
                    if (customerPetPetCodeDTO.getVaccineList() == null) {
                        customerPetPetCodeDTO.setVaccineList(new ArrayList<>());
                    }
                    customerPetPetCodeDTO.getVaccineList().forEach(vaccine -> {
                        VaccineBindingRecordDto recordDto = new VaccineBindingRecordDto();
                        BeanUtils.copyProperties(vaccine, recordDto);
                        vaccineBindings.add(recordDto);
                    });
                    groomingPetInfoDetailDTO.setVaccineBindings(vaccineBindings);
                    break;
                }
            }
        }

        // 查询宠物疫苗状态
        Map<Integer, VaccineStatusDto> vaccineStatus =
                iPetVaccineClient.getVaccineStatusByPetIdList(tokenBusinessId, petIds);

        for (GroomingPetInfoDetailDTO groomingPetInfoDetailDTO : petInfoDetails) {
            // 默认返回false
            groomingPetInfoDetailDTO.setVaccineStatus(false);
            VaccineStatusDto vaccineStatusDto = vaccineStatus.get(groomingPetInfoDetailDTO.getPetId());
            if (vaccineStatusDto != null) {
                // 如果疫苗通知关闭，则不显示
                if (vaccineStatusDto.getExpiryNotification() != null
                        && vaccineStatusDto.getExpiryNotification().intValue() == 0) {
                    groomingPetInfoDetailDTO.setVaccineStatus(false);
                } else if (vaccineStatusDto.getExpiryNotification() != null
                        && vaccineStatusDto.getExpiryNotification().intValue() == 1) {
                    groomingPetInfoDetailDTO.setVaccineStatus(vaccineStatusDto.getHaveExpired());
                }
            }
        }

        return petInfoDetails;
    }

    private GroomingPetServiceDTO getGroomingPetServiceDTO(
            MoeGroomingAppointment moeGroomingAppointment,
            GroomingPetDetailDTO groomingPetDetailDTO,
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap,
            final List<BoardingSplitLodgingModel> boardingSplitLodgings) {
        GroomingPetServiceDTO groomingPetServiceDTO = new GroomingPetServiceDTO();

        BeanUtils.copyProperties(groomingPetDetailDTO, groomingPetServiceDTO);
        groomingPetServiceDTO.setPetDetailId(groomingPetDetailDTO.getId());
        // 查询员工信息
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(moeGroomingAppointment.getBusinessId());
        staffIdParams.setStaffId(groomingPetDetailDTO.getStaffId());
        // FIXME: 循环里调用business服务
        MoeStaffDto staffDto = iBusinessStaffClient.getStaff(staffIdParams);
        if (staffDto != null) {
            groomingPetServiceDTO.setStaffFirstName(staffDto.getFirstName());
            groomingPetServiceDTO.setStaffLastName(staffDto.getLastName());
        }
        if (Boolean.TRUE.equals(groomingPetDetailDTO.getEnableOperation())) {
            groomingPetServiceDTO.setOperationList(operationMap.get(groomingPetDetailDTO.getId()));
        }
        var lodgingUnitIds = boardingSplitLodgings.stream()
                .filter(def -> Objects.equals(
                        def.getPetDetailId(), groomingPetDetailDTO.getId().longValue()))
                .map(BoardingSplitLodgingModel::getLodgingId)
                .toList();
        if (!CollectionUtils.isEmpty(lodgingUnitIds)) {
            List<LodgingUnitModel> lodgings = lodgingUnitService
                    .mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                            .addAllIdList(lodgingUnitIds.stream().distinct().toList())
                            .build())
                    .getLodgingUnitListList();
            groomingPetServiceDTO.setLodgingInfos(getLodgingInfos(lodgings, lodgingUnitIds));
        }
        if (!PrimitiveTypeUtil.isNullOrZero(groomingPetDetailDTO.getLodgingId())) {
            List<LodgingUnitModel> lodgings = lodgingUnitService
                    .mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                            .addIdList(groomingPetDetailDTO.getLodgingId())
                            .build())
                    .getLodgingUnitListList();
            Optional.ofNullable(CollectionUtils.firstElement(lodgings))
                    .ifPresent(lodging -> groomingPetServiceDTO.setLodgingUnitName(lodging.getName()));
        }
        groomingPetServiceDTO.setServiceItemType(groomingPetDetailDTO.getServiceItemType());

        if (Strings.isNotBlank(groomingPetDetailDTO.getSpecificDates())) {
            var specificDates = JsonUtil.toList(groomingPetDetailDTO.getSpecificDates(), String.class);
            groomingPetServiceDTO.setSpecificDates(specificDates);
        }
        return groomingPetServiceDTO;
    }

    private static List<LodgingInfo> getLodgingInfos(
            List<LodgingUnitModel> lodgingUnitModels, List<Long> lodgingUnitId) {
        var lodgingUnitModelMap =
                lodgingUnitModels.stream().collect(toMap(LodgingUnitModel::getId, Function.identity()));
        return lodgingUnitId.stream()
                .map(lodgingUnitModelMap::get)
                .filter(Objects::nonNull)
                .map(model -> {
                    var lodgingInfo = new LodgingInfo();
                    lodgingInfo.setLodgingUnitId(model.getId());
                    lodgingInfo.setLodgingUnitName(model.getName());
                    lodgingInfo.setLodgingTypeId(model.getLodgingTypeId());
                    lodgingInfo.setSort(model.getSort());
                    return lodgingInfo;
                })
                .toList();
    }

    private void convertDTO(
            GroomingTicketDetailDTO groomingTicketDetailDTO, MoeGroomingAppointment moeGroomingAppointment) {
        groomingTicketDetailDTO.setAppointmentDate(moeGroomingAppointment.getAppointmentDate());
        groomingTicketDetailDTO.setAppointmentEndDate(moeGroomingAppointment.getAppointmentEndDate());
        groomingTicketDetailDTO.setAppointmentStartTime(moeGroomingAppointment.getAppointmentStartTime());
        groomingTicketDetailDTO.setAppointmentEndTime(moeGroomingAppointment.getAppointmentEndTime());
        groomingTicketDetailDTO.setCheckInTime(moeGroomingAppointment.getCheckInTime());
        groomingTicketDetailDTO.setReadyTime(moeGroomingAppointment.getReadyTime());
        groomingTicketDetailDTO.setCheckOutTime(moeGroomingAppointment.getCheckOutTime());
        groomingTicketDetailDTO.setColorCode(moeGroomingAppointment.getColorCode());
        groomingTicketDetailDTO.setCreatedById(moeGroomingAppointment.getCreatedById());
        groomingTicketDetailDTO.setUpdatedById(moeGroomingAppointment.getUpdatedById());
        groomingTicketDetailDTO.setCreateTime(moeGroomingAppointment.getCreateTime());
        groomingTicketDetailDTO.setId(moeGroomingAppointment.getId());
        groomingTicketDetailDTO.setRepeatId(moeGroomingAppointment.getRepeatId());
        groomingTicketDetailDTO.setCustomerId(moeGroomingAppointment.getCustomerId());
        groomingTicketDetailDTO.setIsPaid(moeGroomingAppointment.getIsPaid().intValue());
        groomingTicketDetailDTO.setNoShow(moeGroomingAppointment.getNoShow().intValue());
        groomingTicketDetailDTO.setNoShowFee(moeGroomingAppointment.getNoShowFee());
        groomingTicketDetailDTO.setSource(moeGroomingAppointment.getSource());
        groomingTicketDetailDTO.setPickupNotificationSendStatus(
                PickupNotificationStatusEnum.values()[moeGroomingAppointment.getPickupNotificationSendStatus()]);
        groomingTicketDetailDTO.setPickupNotificationFailedReason(
                moeGroomingAppointment.getPickupNotificationFailedReason());

        /*
         * 兼容代码 by ZhangDong
         * 因为 appointment 新增了两个状态字段，但是前端存在版本碎片问题，无法正确解析，因此需要增加兼容逻辑
         * status: 旧字段，状态枚举为 1 - 4，旧版本前端会读该字段
         * appointmentStatus：新字段，状态枚举为 1 - 6，新版本前端会读该字段
         */
        groomingTicketDetailDTO.setStatus(getCompatibleStatus(moeGroomingAppointment.getStatus()));
        groomingTicketDetailDTO.setAppointmentStatus(getCompatibleAppointmentStatus(
                moeGroomingAppointment.getStatus(), moeGroomingAppointment.getCheckInTime()));
        /*----------------------*/

        groomingTicketDetailDTO.setNoStartTime(moeGroomingAppointment.getNoStartTime());
        groomingTicketDetailDTO.setSourcePlatform(moeGroomingAppointment.getSourcePlatform());
        groomingTicketDetailDTO.setIsAutoAccept(moeGroomingAppointment.getIsAutoAccept());
        groomingTicketDetailDTO.setAutoAssign(AutoAssignConverter.INSTANCE.entityToDTO(
                autoAssignService.getAutoAssign(moeGroomingAppointment.getId())));
        groomingTicketDetailDTO.setBusinessId(moeGroomingAppointment.getBusinessId());
    }

    // 旧字段兼容新状态值
    public Integer getCompatibleStatus(Byte status) {
        switch (AppointmentStatusEnum.values()[status]) {
            case UNCONFIRMED, CONFIRMED, FINISHED, CANCELED -> {
                return status.intValue();
            }
            case READY, CHECK_IN -> {
                return AppointmentStatusEnum.CONFIRMED.ordinal();
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_INVALID_STATUS, "invalid status");
        }
    }

    // 新字段兼容旧状态值
    public AppointmentStatusEnum getCompatibleAppointmentStatus(Byte status, Long checkInTime) {
        switch (AppointmentStatusEnum.values()[status]) {
            case READY, CHECK_IN, FINISHED, CANCELED -> {
                return AppointmentStatusEnum.values()[status];
            }
            case UNCONFIRMED, CONFIRMED -> {
                if (checkInTime != null && checkInTime > 0) {
                    return AppointmentStatusEnum.CHECK_IN;
                }
                return AppointmentStatusEnum.values()[status];
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_INVALID_STATUS, "invalid status");
        }
    }

    public void editAppointmentAlertNotesWithRepeat(
            EditContextParams editContextParams, Integer repeatType, Integer businessId) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(editContextParams.getAppointmentId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        String date = moeGroomingAppointment.getAppointmentDate();
        if (repeatId == null || repeatId == 0) {
            repeatType = GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_ONLY_ONE;
        }
        // 当前预约的更新请求参数
        EditCommentsParams editCommentsParams = new EditCommentsParams();
        editCommentsParams.setTicketId(editContextParams.getAppointmentId());
        editCommentsParams.setBusinessId(businessId);
        editCommentsParams.setAlertNotes(editContextParams.getContext());
        // 需要批量修改的预约id list
        List<Integer> needUpdateApptIdList = null;

        editAppointmentAlertNotes(editCommentsParams);
        if (GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_ONLY_ONE.equals(repeatType)) { // appointmentDateString
            return;
        }

        if (GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_FOLLOWING.equals(repeatType)) {
            // 修改本预约及之后预约
            needUpdateApptIdList =
                    moeGroomingAppointmentMapper.queryApptsByRepeatId(businessId, repeatId, date).stream()
                            .map(MoeGroomingAppointment::getId)
                            .collect(toList());
        } else if (GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_ALL.equals(repeatType)) {
            // 修改所有未完成预约
            needUpdateApptIdList =
                    moeGroomingAppointmentMapper.queryApptsByRepeatId(businessId, repeatId, null).stream()
                            .map(MoeGroomingAppointment::getId)
                            .collect(toList());
        } else {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "type is error，please check");
        }
        // 异步批量修改其余预约
        if (!CollectionUtils.isEmpty(needUpdateApptIdList)) {
            List<Integer> finalNeedUpdateApptIdList = needUpdateApptIdList;
            ThreadPool.execute(() -> {
                for (Integer id : finalNeedUpdateApptIdList) {
                    if (id.intValue() == moeGroomingAppointment.getId().intValue()) {
                        continue;
                    }
                    editCommentsParams.setTicketId(id);
                    editAppointmentAlertNotes(editCommentsParams);
                }
            });
        }
    }

    // @Transactional
    public ResponseResult<Integer> editAppointmentAlertNotes(EditCommentsParams editCommentsParams) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(editCommentsParams.getTicketId());
        if (moeGroomingAppointment == null
                || !editCommentsParams.getBusinessId().equals(moeGroomingAppointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
        }

        MoeGroomingNote moeGroomingNote = createGroomingNote(
                editCommentsParams.getBusinessId(),
                moeGroomingAppointment.getCustomerId(),
                editCommentsParams.getAccountId(),
                editCommentsParams.getTicketId(),
                GroomingAppointmentEnum.NOTE_ALERT,
                editCommentsParams.getAlertNotes());
        int editNum = moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);

        return ResponseResult.success(editNum);
    }

    // @Transactional
    public Integer editAppointmentComments(EditCommentsParams params, boolean isMigrated) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(params.getTicketId());
        if (appointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
        }

        if (!isMigrated) {
            if (!Objects.equals(appointment.getBusinessId(), params.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
            }
        } else {
            if (!Objects.equals(appointment.getCompanyId(), params.getCompanyId())) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment");
            }
            permissionHelper.checkPermission(
                    params.getCompanyId(),
                    Set.of(appointment.getBusinessId().longValue()),
                    params.getStaffId().longValue(),
                    PermissionEnums.EDIT_TICKET_COMMENT_AND_GROOMING_REPORT);
        }

        // 先修改当前预约的 ticket comments
        saveAppointmentComment(params, appointment);

        Integer repeatId = appointment.getRepeatId();
        Integer repeatType = params.getRepeatType();
        if (repeatId == 0
                || repeatType == null
                || Objects.equals(RepeatModifyTypeEnum.ONLY_THIS.getRepeatType(), repeatType)) {
            return 1;
        }

        List<MoeGroomingAppointment> needUpdateAppts;
        if (RepeatModifyTypeEnum.THIS_AND_FOLLOWING.getRepeatType().equals(repeatType)) {
            // 修改本预约及之后预约
            needUpdateAppts = moeGroomingAppointmentMapper.queryApptsByRepeatId(
                    appointment.getBusinessId(), repeatId, appointment.getAppointmentDate());

        } else {
            // 修改所有未完成预约
            needUpdateAppts =
                    moeGroomingAppointmentMapper.queryApptsByRepeatId(appointment.getBusinessId(), repeatId, null);
        }
        // 异步批量修改其余预约
        if (!CollectionUtils.isEmpty(needUpdateAppts)) {
            ThreadPool.execute(() -> needUpdateAppts.stream()
                    .filter(appt -> !Objects.equals(appt.getId(), params.getTicketId()))
                    .forEach(appt -> saveAppointmentComment(params, appt)));
        }
        return needUpdateAppts.size() + 1;
    }

    private int saveAppointmentComment(EditCommentsParams params, MoeGroomingAppointment appointment) {
        if (StringUtils.hasText(params.getTicketComments())) {
            MoeGroomingNote moeGroomingNote = createGroomingNoteWithPetInfo(
                    appointment.getBusinessId(),
                    appointment.getCustomerId(),
                    params.getAccountId(),
                    appointment.getId(),
                    GroomingAppointmentEnum.NOTE_COMMENT,
                    params.getTicketComments(),
                    params.getPetId());
            return moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        } else {
            // 如果 ticketComments 为空，那么删除之前的 note
            return removeAppointmentComment(appointment.getId(), params.getStaffId(), params.getPetId());
        }
    }

    /**
     * get all comments of target customer
     *
     * @param tokenBusinessId
     * @param customerId
     * @return List
     */
    public List<HistoryCommentsDTO> queryCustomerHistoryComments(
            Integer tokenBusinessId, Integer customerId, Long petId) {
        List<HistoryCommentsDTO> historyCommentsDTOS =
                moeGroomingNoteService.getCustomerHistoryCommentList(tokenBusinessId, customerId, petId);
        // 查询编辑人
        Set<Integer> staffIdSet = new HashSet<>();
        historyCommentsDTOS.forEach(e -> {
            staffIdSet.add(e.getCreator());
            staffIdSet.add(e.getEditor());
        });

        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(tokenBusinessId);
        staffIdListParams.setStaffIdList(new ArrayList<>(staffIdSet));
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);

        for (HistoryCommentsDTO h : historyCommentsDTOS) {
            for (MoeStaffDto s : staffList) {
                if (s.getId() != null && s.getId().equals(h.getCreator())) {
                    h.setCreatorFirstName(s.getFirstName());
                    h.setCreatorLastName(s.getLastName());
                }
                if (s.getId() != null && s.getId().equals(h.getEditor())) {
                    h.setEditorFirstName(s.getFirstName());
                    h.setEditorLastName(s.getLastName());
                }
                if (StringUtils.hasText(h.getCreatorFirstName()) && StringUtils.hasText(h.getEditorFirstName())) {
                    break;
                }
            }
        }

        // comment 去重 ERP-4023
        // 使用 last edit date time (只精确到分钟)、 staff、 comment 三个值去重，只展示一个， 展示 appointment date 离当前日期最近的一个 history
        DateTimeFormatter minuteFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 用来去重的 map
        Map<String, HistoryCommentsDTO> uniqueCommentsMap = new HashMap<>();

        for (HistoryCommentsDTO comment : historyCommentsDTOS) {
            // 精确到分钟
            LocalDateTime updateTime = Instant.ofEpochSecond(comment.getUpdateTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            String updateTimeMinutes = updateTime.format(minuteFormatter);
            String key = comment.getComments() + "_" + updateTimeMinutes + "_" + comment.getEditor();
            var existingComment = uniqueCommentsMap.get(key);

            // key 相同的情况下，判断那天更近
            if (existingComment == null
                    || ticketCommentService.isCloserToToday(
                            existingComment.getAppointmentDate(), comment.getAppointmentDate())) {
                uniqueCommentsMap.put(key, comment);
            }
        }
        return new ArrayList<>(uniqueCommentsMap.values())
                .stream()
                        .sorted(Comparator.comparing(HistoryCommentsDTO::getUpdateTime)
                                .reversed())
                        .collect(Collectors.toList());
    }

    /**
     * 检查冲突顺序：
     * 1. staff working time冲突检查
     * 2. appt time冲突检查
     * 3. block time冲突检查
     */
    @Deprecated
    public List<ConflictDayInfoDTO> checkConflictForRepeat(
            List<LocalDate> dates, PreviewRepeatParams previewRepeatParams) {
        Integer businessId = previewRepeatParams.getBusinessId();
        Integer tokenStaffId = previewRepeatParams.getStaffId();
        Integer existAppointmentId = previewRepeatParams.getExistAppointmentId(); // 基于已有预约创建 repeat 时，检查冲突需要排除自己
        List<Integer> existAppointmentIdList =
                Objects.nonNull(existAppointmentId) ? List.of(existAppointmentId) : List.of();
        Integer existRepeatId = previewRepeatParams.getRepeatId(); // 编辑已有 repeat 的时候，检查冲突需要排除自己
        List<RepeatStaffInfoParams> repeatStaffInfoParams = previewRepeatParams.getRepeatStaffInfoParams();

        List<ConflictDayInfoDTO> conflictDayInfoDTOS = new LinkedList<>();

        if (CollectionUtils.isEmpty(dates) || CollectionUtils.isEmpty(repeatStaffInfoParams)) {
            return conflictDayInfoDTOS;
        }

        // 计算总的 service duration
        int serviceDuration = repeatStaffInfoParams.stream()
                .mapToInt(RepeatStaffInfoParams::getServiceTime)
                .sum();
        List<String> appointmentDates = new ArrayList<>(150);
        // 返回每天是否冲突信息
        for (LocalDate date : dates) {
            ConflictDayInfoDTO conflictDayInfoDTO = new ConflictDayInfoDTO();
            conflictDayInfoDTO.setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            conflictDayInfoDTO.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_NORMAL);

            // 只允许一个 staff，这里对 staffId、startTime、serviceTime 赋值，因为新的 repeat 需要显示开始时间
            RepeatStaffInfoParams param = repeatStaffInfoParams.get(0);
            conflictDayInfoDTO.setStaffId(param.getStaffId());
            conflictDayInfoDTO.setStartTime(param.getStartTime());
            conflictDayInfoDTO.setDuration(serviceDuration);
            conflictDayInfoDTO.setIsHistory(false);

            conflictDayInfoDTOS.add(conflictDayInfoDTO);
            appointmentDates.add(conflictDayInfoDTO.getDate());
        }

        dates.sort(LocalDate::compareTo);
        List<Integer> staffIdList = repeatStaffInfoParams.stream()
                .map(RepeatStaffInfoParams::getStaffId)
                .toList();
        // 检查staff工作时间
        WorkingDailyQueryRangeVo workingDailyQueryParams = new WorkingDailyQueryRangeVo()
                .setStaffIdList(staffIdList)
                .setStartDate(dates.get(0).toString())
                .setEndDate(dates.get(dates.size() - 1).toString());
        List<StaffWorkingRangeDto> staffWorkTime = iBusinessStaffClient
                .queryRange(businessId, tokenStaffId, workingDailyQueryParams)
                .getData();

        // 未查到工作时间，统一返回false
        if (CollectionUtils.isEmpty(staffWorkTime)) {
            for (ConflictDayInfoDTO conflictDayInfoDTO : conflictDayInfoDTOS) {
                conflictDayInfoDTO.setDuration(repeatStaffInfoParams.get(0).getServiceTime());
                conflictDayInfoDTO.setStartTime(repeatStaffInfoParams.get(0).getStartTime());
                conflictDayInfoDTO.setStaffId(repeatStaffInfoParams.get(0).getStaffId());
                conflictDayInfoDTO.setIsNotConflict(false);
                conflictDayInfoDTO.setConflictType(1);
            }
        }

        // 比对是否工作时间内
        for (RepeatStaffInfoParams repeatStaffInfoParam : repeatStaffInfoParams) {
            int compareStart = repeatStaffInfoParam.getStartTime();
            int compareEnd = repeatStaffInfoParam.getStartTime() + repeatStaffInfoParam.getServiceTime();
            for (StaffWorkingRangeDto staffWorkingRangeDto : staffWorkTime) {
                if (!repeatStaffInfoParam.getStaffId().equals(staffWorkingRangeDto.getStaffId())) {
                    continue;
                }
                Map<String, List<TimeRangeDto>> timeRange = staffWorkingRangeDto.getTimeRange();
                for (ConflictDayInfoDTO conflictDayInfoDTO : conflictDayInfoDTOS) {
                    List<TimeRangeDto> timeRangeDtos = timeRange.get(conflictDayInfoDTO.getDate());
                    if (AppointmentUtil.checkWorkingHourConflict(compareStart, compareEnd, timeRangeDtos)) {
                        conflictDayInfoDTO.setDuration(repeatStaffInfoParam.getServiceTime());
                        conflictDayInfoDTO.setStartTime(repeatStaffInfoParam.getStartTime());
                        conflictDayInfoDTO.setStaffId(repeatStaffInfoParam.getStaffId());
                        conflictDayInfoDTO.setIsNotConflict(false);
                        conflictDayInfoDTO.setConflictType(ConflictTypeEnum.WORK_TIME.getValue());
                    }
                }
                // 找到当前 staff 对应的 workingHour 了，退出循环
                break;
            }
        }

        // 比对是否工作时间冲突：appointment和block
        for (RepeatStaffInfoParams repeatStaffInfoParam : repeatStaffInfoParams) {
            int compareStart = repeatStaffInfoParam.getStartTime();
            int compareEnd = repeatStaffInfoParam.getStartTime() + repeatStaffInfoParam.getServiceTime();
            List<StaffConflictDTO> staffConflictDTOS =
                    moeGroomingPetDetailMapper.queryPetDetailByAppointmentDatesAndStaffIds(
                            businessId,
                            appointmentDates,
                            List.of(repeatStaffInfoParam.getStaffId()),
                            existAppointmentIdList,
                            existRepeatId);
            for (ConflictDayInfoDTO conflictDayInfoDTO : conflictDayInfoDTOS) {
                // 已经有 conflictType，跳过
                if (conflictDayInfoDTO.getConflictType() != null) {
                    continue;
                }
                for (StaffConflictDTO staffConflictDTO : staffConflictDTOS) {
                    if (!staffConflictDTO.getAppointmentDate().equals(conflictDayInfoDTO.getDate())) {
                        // 非当前 repeat 日期，跳过
                        continue;
                    }
                    if (AppointmentUtil.checkAppointmentOrBlockConflict(compareStart, compareEnd, staffConflictDTO)) {
                        conflictDayInfoDTO.setDuration(repeatStaffInfoParam.getServiceTime());
                        conflictDayInfoDTO.setStartTime(repeatStaffInfoParam.getStartTime());
                        conflictDayInfoDTO.setStaffId(repeatStaffInfoParam.getStaffId());
                        conflictDayInfoDTO.setIsNotConflict(false);
                        conflictDayInfoDTO.setConflictType(
                                Objects.equals(staffConflictDTO.getIsBlock(), GroomingAppointmentEnum.IS_BLOCK_TRUE)
                                        ? ConflictTypeEnum.BLOCK.getValue()
                                        : ConflictTypeEnum.APPOINTMENT.getValue());
                    }
                }
            }
        }

        for (ConflictDayInfoDTO conflictDayInfoDTO : conflictDayInfoDTOS) {
            if (conflictDayInfoDTO.getConflictType() == null) {
                conflictDayInfoDTO.setIsNotConflict(true);
            }
        }
        conflictDayInfoDTOS.sort(Comparator.comparing(ConflictDayInfoDTO::getDate));

        return conflictDayInfoDTOS;
    }

    public List<ConflictCheckDTO> batchCheckConflict(
            List<AppointmentCheckParams> appointmentCheckParamList, Integer businessId, Integer staffId) {
        return appointmentCheckParamList.stream()
                .map(param -> {
                    param.setBusinessId(businessId);
                    param.setTokenStaffId(staffId);
                    if (!StringUtils.hasText(param.getAppointmentDate())) {
                        param.setAppointmentDate(param.getAppointmentTime());
                    }
                    return CompletableFuture.supplyAsync(() -> checkConflict(param), ThreadPool.getExecutor());
                })
                .map(CompletableFuture::join)
                .toList();
    }

    // @Transactional
    public ConflictCheckDTO checkConflict(AppointmentCheckParams appointmentCheckParams) {
        ConflictCheckDTO conflictCheckDTO = new ConflictCheckDTO();
        conflictCheckDTO.setStaffId(appointmentCheckParams.getStaffId());
        conflictCheckDTO.setAppointmentDate(appointmentCheckParams.getAppointmentDate());
        conflictCheckDTO.setStartTime(appointmentCheckParams.getStartTime());
        int compareStart = appointmentCheckParams.getStartTime();
        int compareEnd = appointmentCheckParams.getStartTime() + appointmentCheckParams.getDuration();

        // 检查 staff 工作时间
        List<StaffWorkingRangeDto> data = getStaffWorkingRangeDtos(appointmentCheckParams);
        if (data == null || data.isEmpty()) {
            // day off
            conflictCheckDTO.setIsNotConflict(false);
            conflictCheckDTO.setType(1);
            return conflictCheckDTO;
        }

        StaffWorkingRangeDto staffWorkingRangeDto = data.get(0);
        Map<String, List<TimeRangeDto>> timeRange = staffWorkingRangeDto.getTimeRange();
        List<TimeRangeDto> timeRangeDtos = timeRange.get(appointmentCheckParams.getAppointmentTime());
        if (AppointmentUtil.checkWorkingHourConflict(compareStart, compareEnd, timeRangeDtos)) {
            conflictCheckDTO.setIsNotConflict(false);
            conflictCheckDTO.setType(ConflictTypeEnum.WORK_TIME.getValue());
            return conflictCheckDTO;
        }

        // 查询根据日期和员工id查询员工当天的预约
        boolean isConflict = checkAppointmentConflict(appointmentCheckParams);
        if (isConflict) {
            conflictCheckDTO.setIsNotConflict(false);
            conflictCheckDTO.setType(ConflictTypeEnum.APPOINTMENT.getValue());
            return conflictCheckDTO;
        }

        // 校验block
        List<MoeGroomingPetDetail> moeGroomingPetDetailsBlock =
                moeGroomingPetDetailMapper.queryPetDetailByAppointmentDateAndStaffIdBlock(
                        appointmentCheckParams.getAppointmentDate(),
                        appointmentCheckParams.getBusinessId(),
                        appointmentCheckParams.getStaffId());
        for (MoeGroomingPetDetail moeGroomingPetDetail : moeGroomingPetDetailsBlock) {
            int start = moeGroomingPetDetail.getStartTime().intValue();
            int end = moeGroomingPetDetail.getStartTime().intValue() + moeGroomingPetDetail.getServiceTime();

            if (AppointmentUtil.isNotConflict(compareStart, compareEnd, start, end)) {
                continue;
            }

            conflictCheckDTO.setIsNotConflict(false);
            conflictCheckDTO.setType(ConflictTypeEnum.BLOCK.getValue());
            return conflictCheckDTO;
        }
        conflictCheckDTO.setIsNotConflict(true);
        return conflictCheckDTO;
    }

    private boolean checkAppointmentConflict(AppointmentCheckParams appointmentCheckParams) {
        int compareStart = appointmentCheckParams.getStartTime();
        int compareEnd = appointmentCheckParams.getStartTime() + appointmentCheckParams.getDuration();

        List<MoeGroomingPetDetail> moeGroomingPetDetails =
                moePetDetailService.queryPetDetailByAppointmentDateAndStaffId(
                        appointmentCheckParams.getBusinessId(),
                        appointmentCheckParams.getAppointmentDate(),
                        appointmentCheckParams.getStaffId());

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        appointmentCheckParams.getBusinessId(),
                        moeGroomingPetDetails.stream()
                                .map(MoeGroomingPetDetail::getId)
                                .toList());

        for (MoeGroomingPetDetail moeGroomingPetDetail : moeGroomingPetDetails) {
            if (moeGroomingPetDetail.getGroomingId().equals(appointmentCheckParams.getGroomingId())) {
                continue;
            }

            int start = 0, end = 0;
            if (!CollectionUtils.isEmpty(operationMap) && operationMap.containsKey(moeGroomingPetDetail.getId())) {
                // 如果存在 operation, 则使用 operation 的时间
                for (GroomingServiceOperationDTO operation : operationMap.get(moeGroomingPetDetail.getId())) {
                    if (Objects.equals(appointmentCheckParams.getStaffId(), operation.getStaffId())) {
                        start = operation.getStartTime();
                        end = start + operation.getDuration();
                        break;
                    }
                }
            } else {
                start = moeGroomingPetDetail.getStartTime().intValue();
                end = start + moeGroomingPetDetail.getServiceTime();
            }

            if (AppointmentUtil.isNotConflict(compareStart, compareEnd, start, end)) {
                continue;
            }
            return true;
        }

        return false;
    }

    public CalendarConflictDTO checkCalendarConflict(Integer businessId, CalendarCheckParams calendarCheckParams) {
        // 检查工作时间
        if (checkWorkingTimeConflict(businessId, calendarCheckParams)) {
            return new CalendarConflictDTO(true, ConflictTypeEnum.WORK_TIME.getValue());
        }

        // 检查员工当天的预约
        if (checkAppointmentConflict(businessId, calendarCheckParams)) {
            return new CalendarConflictDTO(true, ConflictTypeEnum.APPOINTMENT.getValue());
        }

        // 检查 Block
        if (checkBlockConflict(businessId, calendarCheckParams)) {
            return new CalendarConflictDTO(true, ConflictTypeEnum.BLOCK.getValue());
        }

        return new CalendarConflictDTO(false, null);
    }

    private boolean checkWorkingTimeConflict(Integer businessId, CalendarCheckParams calendarCheckParams) {
        // 检查 staff 工作时间
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffWorkingHour =
                iBusinessStaffClient.getStaffWorkingHourWithOverrideDate(
                        businessId,
                        List.of(calendarCheckParams.getStaffId()),
                        calendarCheckParams.getAppointmentDate(),
                        calendarCheckParams.getAppointmentDate());
        if (CollectionUtils.isEmpty(staffWorkingHour)
                || CollectionUtils.isEmpty(staffWorkingHour.get(calendarCheckParams.getStaffId()))) {
            return true;
        }

        Map<LocalDate, List<TimeRangeDto>> localDateListMap = staffWorkingHour.get(calendarCheckParams.getStaffId());
        List<TimeRangeDto> timeRange = localDateListMap.get(LocalDate.parse(calendarCheckParams.getAppointmentDate()));
        // 无工作时间
        if (CollectionUtils.isEmpty(timeRange)) {
            return true;
        }

        int compareStart = calendarCheckParams.getStartTime();
        int compareEnd = calendarCheckParams.getStartTime() + calendarCheckParams.getDuration();

        for (TimeRangeDto timeRangeDto : timeRange) {
            Integer startTime = timeRangeDto.getStartTime();
            Integer endTime = timeRangeDto.getEndTime();

            // 比对是否在工作时间范围内
            if (compareStart < startTime || compareStart >= endTime || compareEnd > endTime) {
                return true;
            }
        }
        return false;
    }

    private boolean checkBlockConflict(Integer businessId, CalendarCheckParams calendarCheckParams) {
        List<MoeGroomingPetDetail> moeGroomingPetDetailsBlock =
                moeGroomingPetDetailMapper.queryPetDetailByAppointmentDateAndStaffIdBlock(
                        calendarCheckParams.getAppointmentDate(), businessId, calendarCheckParams.getStaffId());

        int compareStart = calendarCheckParams.getStartTime();
        int compareEnd = calendarCheckParams.getStartTime() + calendarCheckParams.getDuration();

        for (MoeGroomingPetDetail moeGroomingPetDetail : moeGroomingPetDetailsBlock) {
            int start = moeGroomingPetDetail.getStartTime().intValue();
            int end = moeGroomingPetDetail.getStartTime().intValue() + moeGroomingPetDetail.getServiceTime();

            if (AppointmentUtil.isNotConflict(compareStart, compareEnd, start, end)) {
                continue;
            }

            return true;
        }
        return false;
    }

    private boolean checkAppointmentConflict(Integer businessId, CalendarCheckParams calendarCheckParams) {
        List<MoeGroomingPetDetail> moeGroomingPetDetails =
                moePetDetailService.queryPetDetailByAppointmentDateAndStaffId(
                        businessId, calendarCheckParams.getAppointmentDate(), calendarCheckParams.getStaffId());

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        businessId,
                        moeGroomingPetDetails.stream()
                                .map(MoeGroomingPetDetail::getId)
                                .toList());

        int compareStart = calendarCheckParams.getStartTime();
        int compareEnd = calendarCheckParams.getStartTime() + calendarCheckParams.getDuration();

        for (MoeGroomingPetDetail moeGroomingPetDetail : moeGroomingPetDetails) {
            if (moeGroomingPetDetail.getGroomingId().equals(calendarCheckParams.getGroomingId())) {
                continue;
            }

            int startTime = 0, endTime = 0;
            if (!CollectionUtils.isEmpty(operationMap) && operationMap.containsKey(moeGroomingPetDetail.getId())) {
                // 如果存在 operation, 则使用 operation 的时间
                for (GroomingServiceOperationDTO operation : operationMap.get(moeGroomingPetDetail.getId())) {
                    if (Objects.equals(calendarCheckParams.getStaffId(), operation.getStaffId())) {
                        startTime = operation.getStartTime();
                        endTime = startTime + operation.getDuration();
                        break;
                    }
                }
            } else {
                startTime = moeGroomingPetDetail.getStartTime().intValue();
                endTime = startTime + moeGroomingPetDetail.getServiceTime();
            }

            boolean isConflict = !AppointmentUtil.isNotConflict(compareStart, compareEnd, startTime, endTime);
            if (isConflict) {
                return true;
            }
        }
        return false;
    }

    private List<StaffWorkingRangeDto> getStaffWorkingRangeDtos(AppointmentCheckParams appointmentCheckParams) {
        WorkingDailyQueryRangeVo workingDailyQueryRangeVo = new WorkingDailyQueryRangeVo();
        List<Integer> staffIdList = new ArrayList<>();
        staffIdList.add(appointmentCheckParams.getStaffId());
        workingDailyQueryRangeVo.setStaffIdList(staffIdList);
        workingDailyQueryRangeVo.setStartDate(appointmentCheckParams.getAppointmentDate());
        workingDailyQueryRangeVo.setEndDate(appointmentCheckParams.getAppointmentDate());
        ResponseResult<List<StaffWorkingRangeDto>> listResponseResult = iBusinessStaffClient.queryRange(
                appointmentCheckParams.getBusinessId(),
                appointmentCheckParams.getTokenStaffId(),
                workingDailyQueryRangeVo);

        // 未找到员工工作时间
        return listResponseResult.getData();
    }

    // @Transactional
    public AddResultDTO addAppointmentBlock(AppointmentBlockParams appointmentBlockParams) {
        if (appointmentBlockParams.getIsGcSyncDelay() == null) {
            appointmentBlockParams.setIsGcSyncDelay(false);
        }
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setAppointmentDate(appointmentBlockParams.getAppointmentDate());
        moeGroomingAppointment.setAppointmentEndDate(
                appointmentBlockParams.getAppointmentDate()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        moeGroomingAppointment.setAppointmentStartTime(appointmentBlockParams.getStartTime());
        moeGroomingAppointment.setAppointmentEndTime(appointmentBlockParams.getEndTime());
        moeGroomingAppointment.setColorCode(appointmentBlockParams.getColorCode());
        moeGroomingAppointment.setIsBlock(1);
        moeGroomingAppointment.setStatus(BooleanEnum.VALUE_TRUE);
        moeGroomingAppointment.setIsDeprecate(BooleanEnum.VALUE_FALSE.intValue());
        moeGroomingAppointment.setRepeatId(appointmentBlockParams.getRepeatId());
        moeGroomingAppointment.setBusinessId(appointmentBlockParams.getTokenBusinessId());
        moeGroomingAppointment.setCompanyId(appointmentBlockParams.getTokenCompanyId());
        moeGroomingAppointment.setCreatedById(appointmentBlockParams.getTokenStaffId());
        moeGroomingAppointment.setSource(appointmentBlockParams.getSource());
        moeGroomingAppointment.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        moeGroomingAppointmentMapper.insertSelective(moeGroomingAppointment);

        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
        moeGroomingPetDetail.setStartDate(appointmentBlockParams.getAppointmentDate());
        moeGroomingPetDetail.setEndDate(appointmentBlockParams.getAppointmentDate());
        moeGroomingPetDetail.setStaffId(appointmentBlockParams.getStaffId());
        moeGroomingPetDetail.setGroomingId(moeGroomingAppointment.getId());
        moeGroomingPetDetail.setStartTime(appointmentBlockParams.getStartTime().longValue());
        moeGroomingPetDetail.setServiceTime(
                appointmentBlockParams.getEndTime() - appointmentBlockParams.getStartTime());
        moeGroomingPetDetail.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingPetDetail.setEndTime(appointmentBlockParams.getEndTime().longValue());

        moeGroomingPetDetailMapper.insertSelective(moeGroomingPetDetail);

        if (StringUtils.hasText(appointmentBlockParams.getDesc())) {
            ThreadPool.execute(() -> {
                // 新增 comment note
                MoeGroomingNote moeGroomingNote = createGroomingNote(
                        moeGroomingAppointment.getBusinessId(),
                        moeGroomingAppointment.getCustomerId(),
                        moeGroomingAppointment.getCreatedById(),
                        moeGroomingAppointment.getId(),
                        GroomingAppointmentEnum.NOTE_COMMENT,
                        appointmentBlockParams.getDesc());
                moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
            });
        }

        calendarSyncService.checkBusinessHaveGoogleCalendarSync( // block insert
                moeGroomingAppointment.getBusinessId(),
                moeGroomingAppointment.getId(),
                moeGroomingAppointment.getAppointmentDate(),
                appointmentBlockParams.getIsGcSyncDelay());

        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setId(moeGroomingAppointment.getId());
        addResultDTO.setResult(true);
        return addResultDTO;
    }

    // @Transactional
    public ResponseResult<Integer> modifyAppointmentBlock(AppointmentBlockParams appointmentBlockParams) {
        MoeGroomingAppointment oldBlockAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                appointmentBlockParams.getTicketId(), appointmentBlockParams.getTokenBusinessId());
        if (oldBlockAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        if (appointmentBlockParams.getIsGcSyncDelay() == null) {
            appointmentBlockParams.setIsGcSyncDelay(false);
        }

        MoeGroomingAppointment newBlcokAppointment = new MoeGroomingAppointment();
        newBlcokAppointment.setBusinessId(appointmentBlockParams.getTokenBusinessId());
        newBlcokAppointment.setAppointmentDate(appointmentBlockParams.getAppointmentDate());
        newBlcokAppointment.setAppointmentEndDate(
                appointmentBlockParams.getAppointmentDate()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        newBlcokAppointment.setAppointmentStartTime(appointmentBlockParams.getStartTime());
        newBlcokAppointment.setAppointmentEndTime(appointmentBlockParams.getEndTime());
        newBlcokAppointment.setColorCode(appointmentBlockParams.getColorCode());
        newBlcokAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        newBlcokAppointment.setId(appointmentBlockParams.getTicketId());
        newBlcokAppointment.setStatus(BooleanEnum.VALUE_TRUE);
        newBlcokAppointment.setIsDeprecate(BooleanEnum.VALUE_FALSE.intValue());
        // 检查是否需要更新，比较每个非null的值
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(newBlcokAppointment);

        // 开始更新petDetail
        MoeGroomingPetDetail newBlockDetail = new MoeGroomingPetDetail();
        newBlockDetail.setStaffId(appointmentBlockParams.getStaffId());
        newBlockDetail.setGroomingId(appointmentBlockParams.getTicketId());
        newBlockDetail.setStartTime(appointmentBlockParams.getStartTime().longValue());
        newBlockDetail.setServiceTime(appointmentBlockParams.getEndTime() - appointmentBlockParams.getStartTime());
        newBlockDetail.setEndTime(appointmentBlockParams.getEndTime().longValue());
        newBlockDetail.setUpdateTime(CommonUtil.get10Timestamp());
        newBlockDetail.setStartDate(appointmentBlockParams.getAppointmentDate());
        newBlockDetail.setEndDate(appointmentBlockParams.getAppointmentDate());
        // 查询block pet detail 旧的做对比判断是否更新
        List<MoeGroomingPetDetail> petDetails =
                moePetDetailService.queryPetDetailByOneGroomingId(appointmentBlockParams.getTicketId());
        if (!CollectionUtils.isEmpty(petDetails)) {
            // block 只会有一个 petDetail
            MoeGroomingPetDetail oldBlockDetail = petDetails.get(0);
            newBlockDetail.setId(oldBlockDetail.getId());
            moeGroomingPetDetailMapper.updateByPrimaryKeySelective(newBlockDetail);
        }
        // insert or update note
        MoeGroomingNote moeGroomingNote = createGroomingNote(
                newBlcokAppointment.getBusinessId(),
                oldBlockAppointment.getCustomerId(),
                appointmentBlockParams.getTokenStaffId(),
                newBlcokAppointment.getId(),
                GroomingAppointmentEnum.NOTE_COMMENT,
                appointmentBlockParams.getDesc());
        moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);

        // 默认要更新，且传了值明确要求要更新
        if (appointmentBlockParams.getCheckGcExport() == null || appointmentBlockParams.getCheckGcExport()) {
            int apptId = appointmentBlockParams.getTicketId();
            String apptTime = appointmentBlockParams.getAppointmentTime();
            boolean isGcSyncDelay = appointmentBlockParams.getIsGcSyncDelay();
            ThreadPool.execute(() -> {
                calendarSyncService.checkBusinessHaveGoogleCalendarSync( // block update
                        appointmentBlockParams.getTokenBusinessId(), apptId, apptTime, isGcSyncDelay);
            });
        }
        return ResponseResult.success(1);
    }

    public Boolean checkBlobkIsNeedUpdate(
            MoeGroomingAppointment oldAppointment, MoeGroomingAppointment newAppointment) {
        if (newAppointment.getAppointmentDate() != null
                && !newAppointment.getAppointmentDate().equals(oldAppointment.getAppointmentDate())) {
            return true;
        }
        if (newAppointment.getAppointmentStartTime() != null
                && !newAppointment.getAppointmentStartTime().equals(oldAppointment.getAppointmentStartTime())) {
            return true;
        }
        if (newAppointment.getAppointmentEndTime() != null
                && !newAppointment.getAppointmentEndTime().equals(oldAppointment.getAppointmentEndTime())) {
            return true;
        }
        if (newAppointment.getColorCode() != null
                && !newAppointment.getColorCode().equals(oldAppointment.getColorCode())) {
            return true;
        }
        if (newAppointment.getStatus() != null && !newAppointment.getStatus().equals(BooleanEnum.VALUE_TRUE)) {
            return true;
        }
        return (newAppointment.getIsDeprecate() != null
                && !newAppointment.getIsDeprecate().equals(BooleanEnum.VALUE_FALSE.intValue()));
    }

    public Boolean checkBlobkDetailIsNeedUpdate(MoeGroomingPetDetail oldPetDetail, MoeGroomingPetDetail newPetDetail) {
        if (newPetDetail.getStaffId() != null && !newPetDetail.getStaffId().equals(oldPetDetail.getStaffId())) {
            return true;
        }
        if (newPetDetail.getStartTime() != null && !newPetDetail.getStartTime().equals(oldPetDetail.getStartTime())) {
            return true;
        }
        return newPetDetail.getEndTime() != null && !newPetDetail.getEndTime().equals(oldPetDetail.getEndTime());
    }

    public Integer deleteAppointmentBlockAndRepeatBlock(IdParams idParams, Integer tokenBusinessId) {
        final Integer REPEAT_UPDATE_ONE = 1;
        final Integer REPEAT_UPDATE_FOLLOWING = 2;
        final Integer REPEAT_UPDATE_ALL = 3;
        Integer repeatType = idParams.getRepeatType();
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                idParams.getId(), idParams.getBusinessId());
        if (moeGroomingAppointment == null || !tokenBusinessId.equals(moeGroomingAppointment.getBusinessId())) {
            log.warn(
                    "cant deleteAppointmentBlock, not found appointment by {}, in business [{}]",
                    idParams.getId(),
                    idParams.getBusinessId());
            return 0;
        }
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        // repeat 无效或者没有
        if (repeatId == null || repeatId == 0 || repeatType == null || repeatType == 0) {
            repeatType = REPEAT_UPDATE_ONE;
        }
        List<Integer> deleteBlockId = new ArrayList<>();

        if (REPEAT_UPDATE_FOLLOWING.equals(repeatType)) {
            deleteBlockId.addAll(moeGroomingAppointmentMapper.selectBlockByRepeatType(
                    tokenBusinessId, repeatId, moeGroomingAppointment.getAppointmentDate()));
            // update this and following
            moeGroomingAppointmentMapper.cancelBlockByRepeatType(
                    tokenBusinessId, repeatId, moeGroomingAppointment.getAppointmentDate(), DateUtil.get10Timestamp());
            deleteBlockId.add(moeGroomingAppointment.getId());
        } else if (REPEAT_UPDATE_ALL.equals(repeatType)) {
            deleteBlockId.addAll(moeGroomingAppointmentMapper.selectBlockByRepeatType(tokenBusinessId, repeatId, null));
            // update all block
            moeGroomingAppointmentMapper.cancelBlockByRepeatType(
                    tokenBusinessId, repeatId, null, DateUtil.get10Timestamp());
        } else {
            // update this
            Integer customerId = moeGroomingAppointment.getCustomerId();
            Integer businessId = moeGroomingAppointment.getBusinessId();
            if (!moeGroomingAppointment.getIsDeprecate().equals(BooleanEnum.VALUE_TRUE.intValue())
                    || !moeGroomingAppointment.getStatus().equals(AppointmentStatusEnum.CANCELED.getValue())) {
                moeGroomingAppointment = new MoeGroomingAppointment();
                moeGroomingAppointment.setId(idParams.getId());
                moeGroomingAppointment.setIsDeprecate(BooleanEnum.VALUE_TRUE.intValue());
                moeGroomingAppointment.setStatus(AppointmentStatusEnum.CANCELED.getValue());
                moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
                publisher.publishEvent(
                        new UpdateCustomerEvent(this).setBusinessId(businessId).setCustomerId(customerId));
            }
        }
        ThreadPool.execute(() -> {
            // update this
            calendarSyncService.checkBusinessHaveGoogleCalendarSync( // deleteAppointmentBlockAndRepeatBlock
                    idParams.getBusinessId(), idParams.getId(), null, false);
            // update other repeat
            for (Integer blockId : deleteBlockId) {
                calendarSyncService.checkBusinessHaveGoogleCalendarSync( // deleteAppointmentBlockAndRepeatBlock
                        idParams.getBusinessId(), blockId, null, true);
            }
        });
        return 1;
    }

    // @Transactional
    public Integer deleteAppointmentBlock(IdParams idParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                idParams.getId(), idParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            log.warn(
                    "cant deleteAppointmentBlock, not found appointment by {}, in business [{}]",
                    idParams.getId(),
                    idParams.getBusinessId());
            return 0;
        }
        moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setId(idParams.getId());
        moeGroomingAppointment.setIsDeprecate(BooleanEnum.VALUE_TRUE.intValue());
        moeGroomingAppointment.setStatus(AppointmentStatusEnum.CANCELED.getValue());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        int result = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);

        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(moeGroomingAppointment.getBusinessId())
                .setCustomerId(moeGroomingAppointment.getCustomerId()));
        return result;
    }

    // @Transactional
    public AddResultDTO addAppointmentRepeat(
            Integer appointmentId, List<AppointmentRepeatParams> appointmentRepeatParams, Integer businessId) {
        preconditionCheckingForFreeBiz(businessId);
        if (CollectionUtils.isEmpty(appointmentRepeatParams)) {
            return new AddResultDTO().setResult(false);
        }
        // 在已有预约基础上新增repeat预约需把当前预约加入repeat
        if (appointmentId != null) {
            appointmentRepeatService.updateRepeatAppointment(
                    appointmentId, appointmentRepeatParams.get(0).getRepeatId(), null);
        }
        return batchAddAppointments(businessId, appointmentRepeatParams);
    }

    /**
     * 批量创建预约，repeat on rules 或者 selected dates（非 repeat 的批量创建）
     */
    public AddResultDTO batchAddAppointments(
            Integer businessId, List<AppointmentRepeatParams> appointmentRepeatParams) {
        if (CollectionUtils.isEmpty(appointmentRepeatParams)) {
            return new AddResultDTO().setResult(false);
        }
        // 新增一条，如无问题则返回结果，其它的开线程异步处理
        AppointmentParams appointmentParams = new AppointmentParams();
        BeanUtils.copyProperties(appointmentRepeatParams.get(0), appointmentParams);
        appointmentParams.setIsGcSyncDelay(false);
        appointmentParams.setIsRepeatFirstAppt(true);
        AddResultDTO result = addGroomingAppointment(appointmentParams);
        if (appointmentRepeatParams.size() > 1) {
            // 如果是展示在 lodging view 的 daycare，这里需要同步创建
            // See https://moego.atlassian.net/browse/MER-491
            boolean hasDaycare = appointmentRepeatParams.stream()
                    .map(AppointmentRepeatParams::getPetServices)
                    .flatMap(Collection::stream)
                    .anyMatch(e -> Objects.equals(e.getServiceItemEnum(), ServiceItemEnum.DAYCARE)
                            && isNormal(e.getLodgingId()));
            int size = appointmentRepeatParams.size();
            if (hasDaycare) {
                // 同步创建 14 天的 appointment，保证用户看到的结果是准确的
                int twoWeeks = 14;

                createAppointments(businessId, appointmentRepeatParams.subList(1, Math.min(size, twoWeeks)));

                if (size > twoWeeks) {
                    ThreadPool.execute(
                            () -> createAppointments(businessId, appointmentRepeatParams.subList(twoWeeks, size)));
                }
            } else {
                ThreadPool.execute(() -> createAppointments(businessId, appointmentRepeatParams.subList(1, size)));
            }
        }
        return result;
    }

    private void createAppointments(Integer businessId, List<AppointmentRepeatParams> appointmentRepeatParams) {
        for (int i = 0; i < appointmentRepeatParams.size(); i++) {
            AppointmentParams newApptParams = new AppointmentParams();
            BeanUtils.copyProperties(appointmentRepeatParams.get(i), newApptParams);
            newApptParams.setIsGcSyncDelay(true);
            addGroomingAppointment(newApptParams);
        }

        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(businessId)
                .setCustomerId(appointmentRepeatParams.get(0).getCustomerId()));
    }

    // @Transactional
    public ResponseResult<Integer> modifyAppointmentRepeat(
            Integer type, Integer tokenBusinessId, Integer tokenStaffId, AppointmentParams appointmentParams) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentParams.getId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        String date = moeGroomingAppointment.getAppointmentDate();
        appointmentParams.setIsWaitingList(moeGroomingAppointment.getIsWaitingList());
        appointmentParams.setIsPaid(moeGroomingAppointment.getIsPaid());
        appointmentParams.setCustomerId(moeGroomingAppointment.getCustomerId());
        appointmentParams.setRepeatId(moeGroomingAppointment.getRepeatId());
        if (repeatId == null || repeatId == 0) {
            throw new CommonException(ResponseCodeEnum.REPEAT_IS_NOT_FOUND);
        }
        Long daysDiff = DateUtil.getDaysDiffByTwoDate(date, appointmentParams.getAppointmentDateString());
        boolean isNeedUpdateAlertNotes = false;
        // 当预约的修改需要应用到其它预约时，alert note没有修改就不应用
        if (appointmentParams.getAlertNotes() != null && type != 1) {
            MoeGroomingNote moeGroomingNote = moeGroomingNoteService.getNoteByGroomingIdAndType(
                    moeGroomingAppointment.getId(), GroomingAppointmentEnum.NOTE_ALERT.intValue());
            String oldAlertNote = null;
            if (moeGroomingNote != null) {
                oldAlertNote = moeGroomingNote.getNote();
            }
            if (oldAlertNote == null) {
                if (!StringUtils.isEmpty(appointmentParams.getAlertNotes())) {
                    isNeedUpdateAlertNotes = true;
                }
            }
            if (oldAlertNote != null) {
                if (!oldAlertNote.equals(appointmentParams.getAlertNotes())) {
                    isNeedUpdateAlertNotes = true;
                }
            }
        }
        int updateCount = 1;
        RepeatModifyTypeEnum repeatType = RepeatModifyTypeEnum.getRepeatModifyTypeEnum(type);
        if (repeatType == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "type is error，please check");
        }
        switch (repeatType) {
            case ONLY_THIS -> {
                modifyGroomingAppointment(tokenStaffId, appointmentParams, true);
                updateCount = 1;
            }
            case THIS_AND_FOLLOWING -> {
                // 修改本预约及之后预约
                List<Integer> ids =
                        moeGroomingAppointmentMapper.queryApptsByRepeatId(tokenBusinessId, repeatId, date).stream()
                                .map(a -> a.getId())
                                .collect(toList());
                modifyGroomingAppointment(tokenStaffId, appointmentParams, true);
                appointmentParams.setRepeatUpdateDaysDiff(daysDiff);
                // 多个修改时除当前外预约日期不得修改
                appointmentParams.setAppointmentDateString(null);
                appointmentParams.setIsNeedUpdateAlertNotes(isNeedUpdateAlertNotes);
                updateCount = ids.size() + 1;
                ThreadPool.execute(() -> {
                    updateRepeatBatch(tokenStaffId, appointmentParams, moeGroomingAppointment, ids);
                });
            }
            case ALL -> {
                // 修改所有未完成预约
                List<Integer> ids =
                        moeGroomingAppointmentMapper.queryApptsByRepeatId(tokenBusinessId, repeatId, null).stream()
                                .map(a -> a.getId())
                                .collect(toList());
                modifyGroomingAppointment(tokenStaffId, appointmentParams, true);
                appointmentParams.setRepeatUpdateDaysDiff(daysDiff);

                // 多个修改时预约日期除当前外不得修改
                appointmentParams.setAppointmentDateString(null);
                appointmentParams.setIsNeedUpdateAlertNotes(isNeedUpdateAlertNotes);
                updateCount = ids.size();
                ThreadPool.execute(() -> {
                    updateRepeatBatch(tokenStaffId, appointmentParams, moeGroomingAppointment, ids);
                });
            }
            default -> throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "type is error，please check");
        }
        return ResponseResult.success(updateCount);
    }

    public void modifyAppointmentRepeatV2(AppointmentRepeatModifyParams modifyParams) {
        final Integer businessId = modifyParams.getBusinessId();
        final Integer groomingId = modifyParams.getId();
        MoeGroomingAppointment previousAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(groomingId, businessId);
        if (Objects.isNull(previousAppointment)) {
            throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }
        Integer repeatId = previousAppointment.getRepeatId();
        if (Objects.isNull(repeatId) || repeatId == 0) {
            throw ExceptionUtil.bizException(Code.CODE_REPEAT_IS_NOT_FOUND, "not found repeat");
        }

        // modify current appointment
        modifyAppointmentRepeatAndNotify(previousAppointment, modifyParams);
        if (RepeatModifyTypeEnum.ONLY_THIS.getRepeatType().equals(modifyParams.getRepeatType())) {
            return;
        }

        ThreadPool.execute(() -> {
            String appointmentDate = previousAppointment.getAppointmentDate();
            if (Objects.isNull(modifyParams.getAppointmentDateString())) {
                modifyParams.setAppointmentDateString(appointmentDate);
            }
            Long daysDiff = DateUtil.getDaysDiffByTwoDate(appointmentDate, modifyParams.getAppointmentDateString());
            if (RepeatModifyTypeEnum.THIS_AND_FOLLOWING.getRepeatType().equals(modifyParams.getRepeatType())) {
                moeGroomingAppointmentMapper.queryApptsByRepeatId(businessId, repeatId, appointmentDate).stream()
                        .filter(moeGroomingAppointment -> !Objects.equals(moeGroomingAppointment.getId(), groomingId))
                        .forEach(moeGroomingAppointment -> setThenModifyAppointmentRepeatWithoutNotify(
                                modifyParams, daysDiff, moeGroomingAppointment));
            } else if (RepeatModifyTypeEnum.ALL.getRepeatType().equals(modifyParams.getRepeatType())) {
                moeGroomingAppointmentMapper.queryApptsByRepeatId(businessId, repeatId, null).stream()
                        .filter(moeGroomingAppointment -> !Objects.equals(moeGroomingAppointment.getId(), groomingId))
                        .forEach(moeGroomingAppointment -> setThenModifyAppointmentRepeatWithoutNotify(
                                modifyParams, daysDiff, moeGroomingAppointment));
            }
        });
    }

    private void setThenModifyAppointmentRepeatWithoutNotify(
            AppointmentRepeatModifyParams modifyParams, Long daysDiff, MoeGroomingAppointment moeGroomingAppointment) {
        modifyParams.setId(moeGroomingAppointment.getId());
        modifyParams.setAppointmentDateString(
                DateUtil.getStrDateByDaysDiff(moeGroomingAppointment.getAppointmentDate(), daysDiff));
        modifyParams.setIsGcSyncDelay(true);
        modifyAppointmentRepeatWithoutNotify(moeGroomingAppointment, modifyParams);
        ThreadPool.execute(() -> recordActivityLog(moeGroomingAppointment, modifyParams));
    }

    private void updateRepeatBatch(
            Integer tokenStaffId,
            AppointmentParams appointmentParams,
            MoeGroomingAppointment moeGroomingAppointment,
            List<Integer> ids) {
        for (Integer id : ids) {
            if (id.intValue() == moeGroomingAppointment.getId().intValue()) {
                continue;
            }
            appointmentParams.setId(id);
            appointmentParams.setIsGcSyncDelay(true);
            modifyGroomingAppointment(tokenStaffId, appointmentParams, false);
        }
    }

    // @Transactional fixme repeat
    public ResponseResult<Integer> addAppointmentBlockRepeat(
            Integer tokenBusinessId, Long tokenCompanyId, Integer tokenStaffId, BlockRepeatParams blockRepeatParam) {
        AddRepeatParams addRepeatParams = blockRepeatParam.getRepeatParams();
        addRepeatParams.setBusinessId(tokenBusinessId);
        addRepeatParams.setCompanyId(tokenCompanyId);
        addRepeatParams.setStaffId(tokenStaffId);

        AddResultDTO addResultDTO = moeRepeatService.addRepeatRule(addRepeatParams);
        List<LocalDate> localDates =
                RepeatUtil.checkAndComputeDate(RepeatMapper.INSTANCE.toRepeatParams(addRepeatParams));

        List<MoeGroomingAppointment> appointments = new ArrayList<>();
        List<MoeGroomingPetDetail> petDetails = new ArrayList<>();
        List<MoeGroomingNote> notes = new ArrayList<>();
        Map<String, AppointmentBlockParams> dateToParamsMap = new HashMap<>();

        List<AppointmentBlockRepeatParams> appointmentBlockRepeatParams = new ArrayList<>();
        for (LocalDate localDate : localDates) {
            AppointmentBlockRepeatParams e = new AppointmentBlockRepeatParams();
            e.setRepeatId(addResultDTO.getId());
            e.setTokenBusinessId(tokenBusinessId);
            e.setTokenStaffId(tokenStaffId);
            e.setTokenCompanyId(tokenCompanyId);

            e.setColorCode(blockRepeatParam.getColorCode());
            e.setSource(blockRepeatParam.getSource());
            e.setDesc(blockRepeatParam.getDesc());
            e.setStartTime(blockRepeatParam.getStartTime());
            e.setEndTime(blockRepeatParam.getEndTime());
            e.setStaffId(blockRepeatParam.getStaffId());

            String appointmentTime = DateUtil.convertLocalDateToDateString(localDate);
            e.setAppointmentTime(appointmentTime);

            appointmentBlockRepeatParams.add(e);
        }

        /**
         * 只有第一个预约实时同步，后面的都延时同步
         */
        for (int i = 0; i < appointmentBlockRepeatParams.size(); i++) {
            var blockRepeatParams = appointmentBlockRepeatParams.get(i);
            AppointmentBlockParams appointmentBlockParams = new AppointmentBlockParams();

            appointmentBlockParams.setAppointmentDate(blockRepeatParams.getAppointmentTime());
            appointmentBlockParams.setIsGcSyncDelay(true);
            BeanUtils.copyProperties(blockRepeatParams, appointmentBlockParams);
            if (i == 0) {
                appointmentBlockParams.setIsGcSyncDelay(false);
            }

            dateToParamsMap.put(blockRepeatParams.getAppointmentTime(), appointmentBlockParams);
            appointments.add(createAppointmentForAppointmentBlock(appointmentBlockParams));
        }

        moeGroomingAppointmentMapper.batchInsertBlockRepeat(appointments);

        for (MoeGroomingAppointment appointment : appointments) {
            AppointmentBlockParams appointmentBlockParams = dateToParamsMap.get(appointment.getAppointmentDate());
            MoeGroomingPetDetail petDetail = createPetDetailForAppointmentBlock(appointmentBlockParams);

            petDetail.setGroomingId(appointment.getId());
            petDetails.add(petDetail);

            if (StringUtils.hasText(blockRepeatParam.getDesc())) {
                MoeGroomingNote moeGroomingNote = createGroomingNote(
                        appointment.getBusinessId(),
                        PrimitiveTypeUtil.DEFAULT_ZERO_LONG.intValue(), // block 无 customerId, 用 0 代替，防止批量创建报错
                        appointment.getCreatedById(),
                        appointment.getId(),
                        GroomingAppointmentEnum.NOTE_COMMENT,
                        appointmentBlockParams.getDesc());
                notes.add(moeGroomingNote);
            }

            // todo 异步执行
            calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                    appointment.getBusinessId(),
                    appointment.getId(),
                    appointment.getAppointmentDate(),
                    appointmentBlockParams.getIsGcSyncDelay());
        }

        moeGroomingPetDetailMapper.batchInsertBlockPetDetail(petDetails);

        if (!CollectionUtils.isEmpty(notes)) {
            ThreadPool.execute(() -> {
                // 新增 comment note
                moeGroomingNoteService.batchInsert(notes);
            });
        }

        return ResponseResult.success(appointmentBlockRepeatParams.size());
    }

    private MoeGroomingAppointment createAppointmentForAppointmentBlock(AppointmentBlockParams params) {

        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setAppointmentDate(params.getAppointmentDate());
        appointment.setAppointmentEndDate(params.getAppointmentDate()); // 假设不跨天
        appointment.setAppointmentStartTime(params.getStartTime());
        appointment.setAppointmentEndTime(params.getEndTime());
        appointment.setColorCode(params.getColorCode());
        appointment.setIsBlock(1);
        appointment.setStatus(BooleanEnum.VALUE_TRUE);
        appointment.setIsDeprecate(BooleanEnum.VALUE_FALSE.intValue());
        appointment.setRepeatId(params.getRepeatId());
        appointment.setBusinessId(params.getTokenBusinessId());
        appointment.setCompanyId(params.getTokenCompanyId());
        appointment.setCreatedById(params.getTokenStaffId());
        appointment.setSource(params.getSource());
        appointment.setCreateTime(CommonUtil.get10Timestamp());
        appointment.setUpdateTime(CommonUtil.get10Timestamp());
        return appointment;
    }

    private MoeGroomingPetDetail createPetDetailForAppointmentBlock(AppointmentBlockParams params) {
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate(params.getAppointmentDate());
        petDetail.setEndDate(params.getAppointmentDate());
        petDetail.setStaffId(params.getStaffId());
        petDetail.setStartTime(params.getStartTime().longValue());
        petDetail.setServiceTime(params.getEndTime() - params.getStartTime());
        petDetail.setUpdateTime(CommonUtil.get10Timestamp());
        petDetail.setEndTime(params.getEndTime().longValue());
        return petDetail;
    }

    public ResponseResult<Integer> modifyAppointmentBlockRepeat(
            Integer type, AppointmentBlockParams appointmentBlockParams) {
        MoeGroomingAppointment moeGroomingAppointment =
                moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentBlockParams.getTicketId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        Integer tokenBusinessId = appointmentBlockParams.getTokenBusinessId();
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        String curDate = moeGroomingAppointment.getAppointmentDate();
        String updateDate = appointmentBlockParams.getAppointmentDate();
        boolean changeDate = !curDate.equals(updateDate);

        if (GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_ONLY_ONE.equals(type)) {
            appointmentBlockParams.setRepeatId(0);
            modifyAppointmentBlock(appointmentBlockParams);
            return ResponseResult.success(1);
        } else if (GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_FOLLOWING.equals(type)
                || GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_ALL.equals(type)) {
            // 然后再按照重复规则来修改
            List<MoeGroomingAppointment> blocks =
                    moeGroomingAppointmentMapper.queryBlocksByRepeatId(tokenBusinessId, repeatId, null);
            List<Integer> ids = new ArrayList<>();
            blocks.forEach(block -> ids.add(block.getId()));
            int modifyIndex = ids.indexOf(appointmentBlockParams.getTicketId()); // 当前block在series的序号
            MoeGroomingRepeat repeatRule =
                    moeRepeatService.queryRepeatRule(appointmentBlockParams.getTokenBusinessId(), repeatId);
            // 兼容1.0规则，创建的blocks会比times多1个
            if (Objects.equals(GroomingAppointmentEnum.REPEAT_TYPE_TIMES, repeatRule.getType())
                    && blocks.size() > repeatRule.getTimes()) {
                repeatRule.setTimes(blocks.size());
            }
            // 计算最初重复的日期
            List<LocalDate> dates =
                    RepeatUtil.checkAndComputeDate(RepeatMapper.INSTANCE.beanToRepeatParams(repeatRule));
            // 根据要修改的date与最初日期来计算偏离值
            LocalDate localDate = dates.get(modifyIndex);
            String initialCurDate = DateUtil.convertLocalDateToDateString(localDate);
            Long daysDiff = DateUtil.getDaysDiffByTwoDate(initialCurDate, updateDate);
            // 根据type来修改following或者修改all
            int start = GroomingAppointmentEnum.MODIFY_REPEAT_BLOCK_FOLLOWING.equals(type) ? modifyIndex : 0;
            for (int i = start; i < dates.size(); i++) {
                if (AppointmentStatusEnum.CANCELED
                        .getValue()
                        .equals(blocks.get(i).getStatus())) {
                    continue;
                }
                // 如果日期参数没有改变，则不修改日期，如果日期修改了，则按重复规则重新修改时间
                if (changeDate) {
                    String blockDate = DateUtil.convertLocalDateToDateString(dates.get(i));
                    String newDate = DateUtil.getStrDateByDaysDiff(blockDate, daysDiff);

                    try {
                        // 如果是设置截止日期，把超出截止日期的关闭不显示
                        if (GroomingAppointmentEnum.REPEAT_TYPE_END_DATE.equals(repeatRule.getType())
                                && DateUtil.convertStringToDate(newDate).after(repeatRule.getSetEndOn())) {
                            IdParams idParams = new IdParams();
                            idParams.setBusinessId(moeGroomingAppointment.getBusinessId());
                            idParams.setId(ids.get(i));
                            deleteAppointmentBlock(idParams);
                            continue;
                        }
                    } catch (ParseException e) {
                        throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "type attr is error");
                    }
                    appointmentBlockParams.setAppointmentDate(newDate);
                } else {
                    appointmentBlockParams.setAppointmentDate(null);
                }

                appointmentBlockParams.setTicketId(ids.get(i));
                appointmentBlockParams.setIsGcSyncDelay(
                        !moeGroomingAppointment.getId().equals(appointmentBlockParams.getTicketId()));
                modifyAppointmentBlock(appointmentBlockParams);
            }
            return ResponseResult.success(1);
        } else {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "type attr is error");
        }
    }

    public List<Integer> filterValidAppointmentIds(Integer companyId, List<Integer> ids) {
        return moeGroomingAppointmentMapper.queryDeletableAppts(companyId, ids);
    }

    // @Transactional
    public Integer deleteAppointment(Integer tokenBusinessId, DeleteAppointmentParams deleteAppointmentParams) {
        List<Integer> ids = deleteAppointmentParams.getIds();
        List<MoeGroomingAppointment> appointments = null;
        // 根据repeat id 删除
        if (deleteAppointmentParams.getRepeatId() != null) {
            appointments = moeGroomingAppointmentMapper.queryApptsByRepeatId(
                    tokenBusinessId,
                    deleteAppointmentParams.getRepeatId(),
                    deleteAppointmentParams.getAppointmentDate());
            ids = appointments.stream().map(a -> a.getId()).collect(toList());
        }

        if (ids == null || ids.size() == 0) {
            return 0;
        }

        // 校验businessId是否一致
        List<MoeGroomingAppointment> appointmentList =
                moeGroomingAppointmentMapper.selectBusinessCustomerIdByApptId(ids, tokenBusinessId);
        if (CollectionUtils.isEmpty(appointmentList) || ids.size() != appointmentList.size()) {
            throw new CommonException(ResponseCodeEnum.APPOINTMENT_NOT_FOUND);
        }
        Integer businessId = appointmentList.get(0).getBusinessId();
        if (!tokenBusinessId.equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "businessId is invalid");
        }

        moeGroomingAppointmentMapper.deleteAppointments(ids, tokenBusinessId);
        moeGroomingPetDetailMapper.deleteByGroomingIds(ids);

        List<Integer> finalIds = ids;
        ThreadPool.execute(() -> {
            // delete appt
            for (Integer apptId : finalIds) {
                calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                        tokenBusinessId, apptId, null, true); // deleteAppointment
                quickBooksService.addRedisSyncGroomingData(tokenBusinessId, apptId, null);

                appointmentProducer.pushAppointmentDeletedEvent(apptId);
            }
            // 标记订单状态、释放product库存
            orderService.updateOrderWhenCancelAppts(tokenBusinessId, finalIds);
        });

        // 修改recurring的定义，还原成1.0的方式，这里注释掉isRecurring的使用
        //        if (!CollectionUtils.isEmpty(appointments)) {
        //            Integer customerId = appointments.get(0).getCustomerId();
        //            List<MoeGroomingAppointment> rp =
        // moeGroomingAppointmentMapper.checkCustomerHasRepeatAppts(customerId);
        //            if (rp.isEmpty()) {
        //                iCustomerGroomingClient.markCustomerNonRecurring(customerId);
        //            }
        //        }

        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(businessId)
                .setCustomerId(appointmentList.get(0).getCustomerId()));

        ids.forEach(id -> ActivityLogRecorder.record(Action.DELETE, ResourceType.APPOINTMENT, id, null));

        return ids.size();
    }

    // 查询预约中的服务和价格，多个petDetail可以对应一个serviceId, 根据serviceId+servicePrice合并为一个invoice
    // serviceId+servicePrice 不再合并，跟随 svc-appointment 一同修改，兼容旧客户端
    public ResponseResult<GroomingInvoiceDTO> queryGroomingInvoice(
            Long companyId, Integer businessId, Integer groomingId) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "grooming primary id is not valid");
        }

        GroomingInvoiceDTO groomingInvoiceDTO = new GroomingInvoiceDTO();

        // 查询服务信息
        List<PetDetailInvoiceDTO> petDetailInvoices =
                companyGroomingServiceQueryService.queryPetDetailInvoiceByGroomingId(companyId, businessId, groomingId);
        // 根据 service id 分组，用于获取 service name，tax id，service description 等信息
        Map<Integer, PetDetailInvoiceDTO> serviceMap = petDetailInvoices.stream()
                .collect(toMap(PetDetailInvoiceDTO::getServiceId, Function.identity(), (a, b) -> a));
        List<MoeGroomingPetDetail> petDetails = toPetDetail(petDetailInvoices);
        serviceDetailService.fixServiceType(petDetails);

        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = petDetails.stream()
                .filter(petDetail -> Objects.equals(
                        petDetail.getServiceType(), com.moego.idl.models.offering.v1.ServiceType.SERVICE_VALUE))
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.toMap(MoeGroomingPetDetail::getServiceId, Function.identity(), (p1, p2) -> p1)));

        List<InvoiceServiceDTO> invoiceServices = new ArrayList<>();
        // TODO(ritchie)： 去掉合并逻辑
        for (MoeGroomingPetDetail petDetail : petDetails) {
            int quantity = PetDetailUtil.getQuantity(petDetail, petServiceMap);
            //            Optional<InvoiceServiceDTO> optionalExist = invoiceServices.stream()
            //                    .filter(s -> s.getServiceId().equals(petDetail.getServiceId()))
            //                    .filter(s -> s.getServicePrice().compareTo(petDetail.getServicePrice()) == 0)
            //                    .findFirst();
            //            if (optionalExist.isPresent()) {
            //                InvoiceServiceDTO exist = optionalExist.get();
            //                exist.setQuantity(exist.getQuantity() + quantity);
            //                continue;
            //            }

            InvoiceServiceDTO invoiceServiceDTO = new InvoiceServiceDTO();
            PetDetailInvoiceDTO service = serviceMap.get(petDetail.getServiceId());
            invoiceServiceDTO.setPetId(petDetail.getPetId());
            // service 取最新数据
            invoiceServiceDTO.setServiceDescription(service.getServiceDescription());
            invoiceServiceDTO.setServiceName(service.getServiceName());
            invoiceServiceDTO.setTaxId(service.getTaxId());
            invoiceServiceDTO.setServiceType(service.getServiceType());
            // price 取快照数据
            invoiceServiceDTO.setPetDetailId(petDetail.getId());
            invoiceServiceDTO.setServiceId(petDetail.getServiceId());
            invoiceServiceDTO.setServicePrice(petDetail.getServicePrice());
            invoiceServiceDTO.setQuantity(quantity);

            invoiceServices.add(invoiceServiceDTO);
        }

        groomingInvoiceDTO.setCustomerId(moeGroomingAppointment.getCustomerId());
        groomingInvoiceDTO.setGroomingId(moeGroomingAppointment.getId());
        groomingInvoiceDTO.setBusinessId(moeGroomingAppointment.getBusinessId());
        groomingInvoiceDTO.setSource(moeGroomingAppointment.getSource());
        groomingInvoiceDTO.setServiceInvoices(invoiceServices);

        return ResponseResult.success(groomingInvoiceDTO);
    }

    private List<MoeGroomingPetDetail> toPetDetail(List<PetDetailInvoiceDTO> invoices) {
        return invoices.stream()
                .map(invoice -> {
                    MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
                    petDetail.setId(invoice.getPetDetailId());
                    petDetail.setPetId(invoice.getPetId());
                    petDetail.setServiceId(invoice.getServiceId());
                    petDetail.setServiceItemType(invoice.getServiceItemType());
                    petDetail.setServiceType(invoice.getServiceType());
                    petDetail.setServicePrice(invoice.getServicePrice());
                    petDetail.setPriceUnit(invoice.getPriceUnit());
                    petDetail.setStartDate(invoice.getStartDate());
                    petDetail.setEndDate(invoice.getEndDate());
                    petDetail.setStartTime(invoice.getStartTime());
                    petDetail.setEndTime(invoice.getEndTime());
                    petDetail.setQuantityPerDay(1);
                    return petDetail;
                })
                .toList();
    }

    // @Transactional
    public ResponseResult<Integer> editAppointmentUnConfirm(EditIdParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        moeGroomingAppointment.setId(editIdParams.getId());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        return ResponseResult.success(i);
    }

    public ResponseResult<CustomerAppointmentListDTO> queryGroomingCustomerAppointment(
            GroomingCustomerQueryVO groomingCustomerQueryVO, Integer tokenStaffId) {
        Integer searchBusinessId = groomingCustomerQueryVO.getSearchBusinessId();
        if (groomingCustomerQueryVO.getSkipCancel() == null) {
            groomingCustomerQueryVO.setSkipCancel(false);
        }
        String timezoneName = companyHelper.getCompanyTimeZoneName(groomingCustomerQueryVO.getCompanyId());

        List<CustomerGrooming> customerGroomings;
        CustomerAppointmentListDTO pageInfo;
        if (groomingCustomerQueryVO.getType() != null
                && Objects.equals(groomingCustomerQueryVO.getType(), GroomingHistoryType.WAITING)) {
            pageInfo = queryWaitListCustomerGrooming(groomingCustomerQueryVO, tokenStaffId);
            customerGroomings = pageInfo.getList();
        } else {
            LocalDateTime now = LocalDateTime.now();
            String s = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
            Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
            groomingCustomerQueryVO.setAppointmentDate(s);
            groomingCustomerQueryVO.setEndTime(nowMinutes);
            try (Page<CustomerGrooming> page = PageHelper.startPage(
                            groomingCustomerQueryVO.getPageNum(), groomingCustomerQueryVO.getPageSize())
                    .doSelectPage(() -> moeGroomingAppointmentMapper.queryGroomingCustomerAppointmentNum(
                            groomingCustomerQueryVO.toBuilder()
                                    .businessId(searchBusinessId)
                                    .build()))) {
                customerGroomings = page.getResult();
            }
            /*
            兼容逻辑，当使用新的状态字段过滤时，confirmed/unconfirmed 状态的筛选，要排除掉 confirmed/unconfirmed with checkin 状态的数据
             */

            List<Integer> appointmentIds =
                    customerGroomings.stream().map(CustomerGrooming::getId).collect(toList());

            pageInfo = new CustomerAppointmentListDTO(customerGroomings);
            if (appointmentIds.isEmpty()) {
                return ResponseResult.success(pageInfo);
            }

            // business id 为null时，表示不过滤business id
            customerGroomings = reportOrderService.queryGroomingCustomerAppointment(
                    searchBusinessId, appointmentIds, groomingCustomerQueryVO.getOrderType());
        }
        List<Integer> appointmentIds =
                customerGroomings.stream().map(CustomerGrooming::getId).toList();
        if (appointmentIds.isEmpty()) {
            return ResponseResult.success(pageInfo);
        }

        fillEvaluationServiceDetails(customerGroomings);
        fillEvaluationServices(pageInfo, customerGroomings);

        // add cancel reason if available
        List<Integer> cancelledGroomingIds = customerGroomings.stream()
                .filter(grooming -> grooming.getStatus()
                        == AppointmentStatusEnum.CANCELED.getValue().intValue())
                .map(CustomerGrooming::getId)
                .collect(toList());
        if (!cancelledGroomingIds.isEmpty()) {
            List<MoeGroomingNote> moeGroomingNotes = moeGroomingNoteService.getNoteListByGroomingIdListAndType(
                    cancelledGroomingIds, GroomingAppointmentEnum.NOTE_CANCEL.intValue());
            for (MoeGroomingNote note : moeGroomingNotes) {
                for (CustomerGrooming grooming : customerGroomings) {
                    if (grooming.getId().equals(note.getGroomingId())) {
                        grooming.setCancelReason(note.getNote());
                    }
                }
            }
        }

        // query relative data
        List<Integer> groomingIds = new ArrayList<>();
        Set<Integer> petIdSet = new HashSet<>();
        Set<Integer> staffIdSet = new HashSet<>();
        Set<Integer> serviceIdSet = new HashSet<>();

        // 部分service无staffId 初始化为空
        for (CustomerGrooming customerGrooming : customerGroomings) {
            for (GroomingCustomerPetdetailDTO petDetail : customerGrooming.getPetServiceList()) {
                if (Objects.isNull(petDetail.getStaffIds())) {
                    petDetail.setStaffIds(List.of());
                }
            }
        }

        for (CustomerGrooming customerGrooming : customerGroomings) {
            groomingIds.add(customerGrooming.getId());
            for (GroomingCustomerPetdetailDTO petDetail : customerGrooming.getPetServiceList()) {
                petIdSet.add(petDetail.getPetId());
                staffIdSet.add(petDetail.getStaffId());
                serviceIdSet.add(petDetail.getServiceId());
                staffIdSet.addAll(
                        petDetail.getStaffIds().stream().map(Long::intValue).toList());
            }
            for (GroomingEvaluationServiceDetailDTO evaluationDetail : customerGrooming.getEvaluationServiceList()) {
                petIdSet.add(evaluationDetail.getPetId().intValue());
            }
        }
        List<CustomerPetDetailDTO> customerPetListByIdList =
                iPetClient.getCustomerPetListByIdList(new ArrayList<>(petIdSet));
        List<MoeStaffDto> staffList;
        List<MoeGroomingServiceDTO> groomingServiceDTOS;
        List<MoeBookOnlineDeposit> deposits;
        if (searchBusinessId == null) {
            staffList = iBusinessStaffClient.getStaffListV2(new StaffIdListParams(null, new ArrayList<>(staffIdSet)));
            groomingServiceDTOS = groomingServiceService.getServicesByServiceIds(null, new ArrayList<>(serviceIdSet));
            deposits = moeBookOnlineDepositService.getOBDepositByGroomingIdsV2(new HashSet<>(appointmentIds));
        } else {
            staffList = iBusinessStaffClient.getStaffList(
                    new StaffIdListParams(searchBusinessId, new ArrayList<>(staffIdSet)));
            groomingServiceDTOS =
                    groomingServiceService.getServicesByServiceIds(searchBusinessId, new ArrayList<>(serviceIdSet));
            deposits = moeBookOnlineDepositService.getOBDepositByGroomingIds(
                    searchBusinessId, new HashSet<>(appointmentIds));
        }

        // @Deprecated, should not create new entity
        for (CustomerGrooming customerGrooming : customerGroomings) {
            for (GroomingCustomerPetdetailDTO petDetail : customerGrooming.getPetServiceList()) {
                for (CustomerPetDetailDTO customerPetDetailDTO : customerPetListByIdList) {
                    if (petDetail.getPetId().equals(customerPetDetailDTO.getPetId())) {
                        petDetail.setPetName(customerPetDetailDTO.getPetName());
                        petDetail.setPetBreed(customerPetDetailDTO.getBreed());
                        break;
                    }
                }
            }
        }

        pageInfo.setPetList(customerPetListByIdList);
        pageInfo.setStaffList(CommonUtil.copyListProperties(staffList, CustomerAppointmentListStaffDTO.class));
        pageInfo.setServiceList(groomingServiceDTOS);
        // DONE new order flow 未创单的预约，需要从 appointment 上计算 subtotal
        if (newOrderHelper.enableNewOrder(groomingCustomerQueryVO.getCompanyId())) {
            setPaymentSummaryForNewOrder(
                    groomingCustomerQueryVO.getCompanyId(), groomingCustomerQueryVO.getBusinessId(), customerGroomings);
        } else {
            // 查询invoice，用于计算prepay金额所占比例
            List<MoeGroomingInvoice> invoices = invoiceService.queryInvoiceByGroomingIds(
                    searchBusinessId, groomingIds, InvoiceStatusEnum.TYPE_APPOINTMENT);
            Map<Integer, MoeGroomingInvoice> invoiceMap = invoices.stream()
                    .collect(toMap(MoeGroomingInvoice::getGroomingId, Function.identity(), (a, b) -> b));
            customerGroomings.forEach(grooming -> {
                MoeGroomingInvoice moeGroomingInvoice = invoiceMap.get(grooming.getId());
                if (Objects.isNull(moeGroomingInvoice)) {
                    return;
                }
                grooming.setHasExtraOrder(moeGroomingInvoice.getHasExtraOrder());
                grooming.setSubTotalAmount(moeGroomingInvoice.getSubTotalAmount());
            });

            // 查询 deposit，用于计算 prepay 金额所占比例
            Map<Integer, BigDecimal> invoiceAmountMap = invoices.stream()
                    .collect(toMap(MoeGroomingInvoice::getGroomingId, MoeGroomingInvoice::getPaymentAmount));
            if (!CollectionUtils.isEmpty(deposits)) {
                Map<Integer, MoeBookOnlineDeposit> depositMap =
                        deposits.stream().collect(toMap(MoeBookOnlineDeposit::getGroomingId, d -> d));
                customerGroomings.forEach(grooming -> {
                    MoeBookOnlineDeposit deposit = depositMap.get(grooming.getId());
                    if (!Objects.isNull(deposit) && DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                        grooming.setPrepaidAmount(deposit.getAmount());
                        grooming.setPrepayStatus(deposit.getStatus());
                        grooming.setPrepayRate(moeBookOnlineDepositService.getPrepayRate(
                                deposit, invoiceAmountMap.getOrDefault(grooming.getId(), BigDecimal.ZERO)));
                    }
                });
            }
        }

        // 不是waiting需要增加noshow invoice信息
        if (groomingCustomerQueryVO.getType() != null
                && !Objects.equals(groomingCustomerQueryVO.getType(), GroomingHistoryType.WAITING)) {
            List<Integer> noShowGroomingIds = new LinkedList<>();
            for (CustomerGrooming customerGrooming : customerGroomings) {
                if (customerGrooming.getNoShow() == GroomingAppointmentEnum.NO_SHOW_TRUE.intValue()) {
                    noShowGroomingIds.add(customerGrooming.getId());
                }
            }
            if (!CollectionUtils.isEmpty(noShowGroomingIds)) {
                var appointmentNoShowOrders = orderHelper.getAppointmentLatestNoShowOrders(noShowGroomingIds);
                for (CustomerGrooming customerGrooming : customerGroomings) {
                    var orderModel = appointmentNoShowOrders.get(customerGrooming.getId());
                    if (orderModel == null) {
                        continue;
                    }

                    var moeGroomingInvoice = orderService.convertToInvoice(orderModel);
                    customerGrooming.setNoShowOrderId(moeGroomingInvoice.getId().longValue());
                    customerGrooming.setInvoiceId(moeGroomingInvoice.getId());
                    customerGrooming.setNoShowStatus(moeGroomingInvoice.getStatus());
                    customerGrooming.setNoShowPaymentStatus(moeGroomingInvoice.getPaymentStatus());
                    // DONE new order flow 里可以存在 deposit 被 forfeit 到 no-show fee 的情况，导致 total 为 0，但实际是应该有 no-show fee 的
                    customerGrooming.setNoShowFee(moeGroomingInvoice.getSubTotalAmount());
                    // Set cancellation fee
                    boolean isCancellationFee = moeGroomingInvoice.getItems().stream()
                            .allMatch(item ->
                                    Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_CANCELLATION_FEE.getType()));
                    if (isCancellationFee) {
                        customerGrooming.setCancellationFee(moeGroomingInvoice.getPaymentAmount());
                    }
                }
            }
        }

        if (groomingCustomerQueryVO.getType() != null
                && Objects.equals(groomingCustomerQueryVO.getType(), GroomingHistoryType.WAITING)) {
            List<MoeWaitList> waitLists = waitListService.batchGetWaitListByAppointmentV2(
                    groomingIds.stream().map(Integer::longValue).toList());
            Map<Long, MoeWaitList> waitListMap =
                    waitLists.stream().collect(Collectors.toMap(MoeWaitList::getAppointmentId, Function.identity()));
            Map<Integer, MoeStaffDto> staffInfo = waitListService.getStaffInfo(null, waitLists);

            customerGroomings.forEach(grooming -> {
                MoeWaitList waitList = waitListMap.get(grooming.getId().longValue());
                if (waitList != null) {
                    WaitListCompatibleDTO waitListCompatibleDTO = new WaitListCompatibleDTO();
                    waitListCompatibleDTO.setRealWaitListId(waitList.getId());
                    waitListCompatibleDTO.setCreateAt(waitList.getCreatedAt());
                    waitListCompatibleDTO.setTimePreference(
                            TimePreferenceMapper.INSTANCE.toDto(waitList.getTimePreference()));
                    waitListCompatibleDTO.setDatePreference(
                            DatePreferenceMapper.INSTANCE.toDto(waitList.getDatePreference()));
                    waitListCompatibleDTO.setStaffPreference(
                            waitListService.staffPreferencePO2DTO(waitList.getStaffPreference(), staffInfo));
                    grooming.setWaitListDTO(waitListCompatibleDTO);
                }
            });
        }
        pageInfo.setList(customerGroomings);

        return ResponseResult.success(pageInfo);
    }

    private void fillEvaluationServiceDetails(List<CustomerGrooming> customerGroomings) {
        if (CollectionUtils.isEmpty(customerGroomings)) {
            return;
        }
        List<Integer> appointmentIds =
                customerGroomings.stream().map(CustomerGrooming::getId).toList();
        List<EvaluationServiceDetail> evaluations = evaluationServiceDetailService.queryByGroomingIds(appointmentIds);
        Map<Long, List<EvaluationServiceDetail>> evaluationMap =
                evaluations.stream().collect(Collectors.groupingBy(EvaluationServiceDetail::getAppointmentId));
        for (CustomerGrooming grooming : customerGroomings) {
            List<EvaluationServiceDetail> evaluationServiceDetails =
                    evaluationMap.get(grooming.getId().longValue());
            if (CollectionUtils.isEmpty(evaluationServiceDetails)) {
                grooming.setEvaluationServiceList(List.of());
                continue;
            }
            List<GroomingEvaluationServiceDetailDTO> dtos = evaluationServiceDetails.stream()
                    .map(k -> {
                        GroomingEvaluationServiceDetailDTO dto = new GroomingEvaluationServiceDetailDTO();
                        dto.setEvaluationServiceDetailId(k.getId());
                        dto.setPetId(k.getPetId());
                        dto.setServiceId(k.getServiceId());
                        dto.setServiceTime(k.getServiceTime());
                        dto.setServicePrice(k.getServicePrice());
                        dto.setStartTime(k.getStartTime().longValue());
                        dto.setEndTime(k.getEndTime().longValue());
                        return dto;
                    })
                    .toList();
            grooming.setEvaluationServiceList(dtos);
        }
    }

    private void setPaymentSummaryForNewOrder(long companyId, long businessId, List<CustomerGrooming> appointments) {
        if (CollectionUtils.isEmpty(appointments)) {
            return;
        }
        var appointmentIds = appointments.stream()
                .map(CustomerGrooming::getId)
                .map(Integer::longValue)
                .toList();
        var appointmentIdToEstimatedOrder = appointmentStub
                .previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllAppointmentIds(appointmentIds)
                        .build())
                .getEstimatedOrdersList()
                .stream()
                .collect(toMap(
                        PreviewEstimateOrderResponse.EstimatedOrder::getAppointmentId,
                        Function.identity(),
                        (a, b) -> a));
        appointments.forEach(grooming -> {
            var estimatedOrder =
                    appointmentIdToEstimatedOrder.get(grooming.getId().longValue());
            if (estimatedOrder == null) {
                return;
            }
            grooming.setSubTotalAmount(MoneyUtils.fromGoogleMoney(estimatedOrder.getEstimatedTotal()));
        });
    }

    private void fillEvaluationServices(CustomerAppointmentListDTO pageInfo, List<CustomerGrooming> customerGroomings) {
        pageInfo.setEvaluationServiceList(List.of());
        if (CollectionUtils.isEmpty(customerGroomings)) {
            return;
        }

        List<Long> serviceIds = customerGroomings.stream()
                .map(CustomerGrooming::getEvaluationServiceList)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(GroomingEvaluationServiceDetailDTO::getServiceId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(serviceIds)) {
            return;
        }

        pageInfo.setEvaluationServiceList(evaluationServiceDetailService.getEvaluationServiceList(serviceIds).stream()
                .map(k -> {
                    EvaluationServiceDTO dto = new EvaluationServiceDTO();
                    dto.setId(k.getId());
                    dto.setName(k.getName());
                    return dto;
                })
                .toList());
    }

    CustomerAppointmentListDTO queryWaitListCustomerGrooming(
            GroomingCustomerQueryVO groomingCustomerQueryVO, Integer tokenStaffId) {
        List<CustomerGrooming> customerGroomings = new ArrayList<>();
        CustomerAppointmentListDTO pageInfo = new CustomerAppointmentListDTO(customerGroomings);
        String order;
        if (groomingCustomerQueryVO.getOrderType().equals(2)) {
            order = "created_at desc";
        } else {
            order = "created_at";
        }
        List<MoeWaitList> waitListList =
                waitListService.queryWaitList(groomingCustomerQueryVO.getCompanyId(), null, null, null, order);
        List<Integer> groomingIds =
                waitListList.stream().map(k -> k.getAppointmentId().intValue()).toList();
        if (CollectionUtils.isEmpty(groomingIds)) {
            pageInfo.setTotal(0);
            return pageInfo;
        }
        List<CustomerGrooming> tmpGroomingList =
                moeGroomingAppointmentMapper.queryGroomingCustomerAppointmentByIds(groomingIds);

        StaffPermissions staffPermissions = businessStaffService.getBusinessRoleByStaffId(tokenStaffId);
        boolean canAccessOBWaitList =
                checkStaffPermissionsInfo(staffPermissions, CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST);
        // 按 customer、ob waitList 权限过滤
        tmpGroomingList = tmpGroomingList.stream()
                .filter(k -> k.getCustomerId().equals(groomingCustomerQueryVO.getCustomerId()))
                .toList();
        if (!canAccessOBWaitList) {
            tmpGroomingList = tmpGroomingList.stream()
                    .filter(k ->
                            !k.getBookOnlineStatus().equals(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.intValue()))
                    .toList();
        }
        // 对查询结果按入参排序
        for (MoeWaitList waitList : waitListList) {
            for (CustomerGrooming customerGrooming : tmpGroomingList) {
                if (customerGrooming.getId().equals(waitList.getAppointmentId().intValue())) {
                    customerGroomings.add(customerGrooming);
                    break;
                }
            }
        }
        Integer pageNum = groomingCustomerQueryVO.getPageNum();
        Integer pageSize = groomingCustomerQueryVO.getPageSize();
        int totalSize = customerGroomings.size();
        customerGroomings = customerGroomings.stream()
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(toList());
        pageInfo.setList(customerGroomings);
        pageInfo.setTotal(totalSize);

        // 查询multi staffIds
        Map<Integer, List<Long>> multiStaffsMap = serviceOperationMapper.selectByGroomingIdList(groomingIds).stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingServiceOperation::getGroomingServiceId,
                        Collectors.mapping(operation -> operation.getStaffId().longValue(), Collectors.toList())));

        customerGroomings.forEach(appointment -> {
            appointment.getPetServiceList().forEach(petService -> {
                List<Long> staffIds =
                        new ArrayList<>(multiStaffsMap.getOrDefault(petService.getPetDetailId(), List.of()));
                if (petService.getStaffId() != null) {
                    staffIds.add(petService.getStaffId().longValue());
                }
                petService.setStaffIds(staffIds.stream().distinct().toList());
            });
        });

        /*
         * 兼容代码 by ZhangDong
         * 因为 appointment 新增了两个状态字段，但是前端存在版本碎片问题，无法正确解析，因此需要增加兼容逻辑
         * status: 旧字段，状态枚举为 1 - 4，旧版本前端会读该字段
         * appointmentStatus：新字段，状态枚举为 1 - 6，新版本前端会读该字段
         */
        customerGroomings.forEach(a -> {
            Byte status = a.getStatus().byteValue();
            a.setStatus(getCompatibleStatus(status));
            a.setAppointmentStatus(getCompatibleAppointmentStatus(status, a.getCheckInTime()));
        });
        // ------------------------------

        return pageInfo;
    }

    public List<BookOnlineAutoMoveAppointmentDTO> queryAllBookingAppointmentForAutoMove() {
        return moeGroomingAppointmentMapper.queryAllBookingAutoMoveAppointment(obGroomingService.queryTaskScrollId());
    }

    public BookOnlineRequestCountDto getBookOnlineRequestCountForNotification(Integer businessId) {
        BookOnlineRequestCountDto countDto = new BookOnlineRequestCountDto();
        countDto.setOnlineBookingCount(moeGroomingAppointmentMapper.queryAppointmentCount(
                businessId, GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB, 1));
        countDto.setOnlineBookingWaitlistCount(moeGroomingAppointmentMapper.queryAppointmentCount(
                businessId, GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB, 2));
        countDto.setUnpaidApptCount(reportOrderService.queryUnpaidApptsCountWithAmount(businessId, null, null));
        countDto.setOnlineBookingAbandonedCount(getAbandonRecordCount(businessId));
        return countDto;
    }

    // @Transactional
    public ResponseResult<Integer> editAppointmentUnCheckIn(EditIdParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        MoeGroomingAppointment toUpdate = new MoeGroomingAppointment();
        toUpdate.setCheckInTime(0L);
        toUpdate.setId(editIdParams.getId());
        toUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        if (AppointmentStatusEnum.CHECK_IN.getValue().equals(moeGroomingAppointment.getStatus())
                || AppointmentStatusEnum.READY.getValue().equals(moeGroomingAppointment.getStatus())) {
            toUpdate.setStatus(
                    AppointmentStatusEnum.UNKNOWN.equals(moeGroomingAppointment.getStatusBeforeCheckin())
                            ? moeGroomingAppointment.getStatusBeforeReady().getValue()
                            : moeGroomingAppointment.getStatusBeforeCheckin().getValue());
            toUpdate.setStatusBeforeReady(AppointmentStatusEnum.UNKNOWN);
            toUpdate.setStatusBeforeCheckin(AppointmentStatusEnum.UNKNOWN);
            toUpdate.setReadyTime(0L);
        }

        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(toUpdate);
        return ResponseResult.success(i);
    }

    // @Transactional
    public ResponseResult<AddResultDTO> addGroomingAppointmentWaiting(AppointmentParams appointment) {
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();

        moeGroomingAppointment.setAppointmentDate(appointment.getAppointmentDateString());
        moeGroomingAppointment.setAppointmentEndDate(
                appointment.getAppointmentDateString()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date

        BeanUtils.copyProperties(appointment, moeGroomingAppointment);

        moeGroomingAppointment.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
        moeGroomingAppointment.setOrderId(CommonUtil.getUuid());
        moeGroomingAppointment.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        moeGroomingAppointment.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setMoveWaitingBy(appointment.getCreatedById());

        moeGroomingAppointmentMapper.insertSelective(moeGroomingAppointment);
        waitListService.addToWaitList(List.of(moeGroomingAppointment.getId()), false);

        if (appointment.getPetServices() != null && appointment.getPetServices().size() > 0) {
            moePetDetailService.addMoePetDetailsWaiting(moeGroomingAppointment, appointment.getPetServices());
        }

        String alertNotes = appointment.getAlertNotes();
        String ticketComments = appointment.getTicketComments();

        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(moeGroomingAppointment.getBusinessId());
        moeGroomingNote.setCompanyId(moeGroomingAppointment.getCompanyId());
        moeGroomingNote.setCustomerId(moeGroomingAppointment.getCustomerId());
        moeGroomingNote.setGroomingId(moeGroomingAppointment.getId());
        moeGroomingNote.setCreateBy(moeGroomingAppointment.getCreatedById());
        moeGroomingNote.setUpdateBy(moeGroomingAppointment.getCreatedById());
        moeGroomingNote.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());

        // 添加alertNotes
        if (StringUtils.hasText(alertNotes)) {
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
            moeGroomingNote.setNote(alertNotes);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }

        // 添加comments
        if (StringUtils.hasText(ticketComments)) {
            moeGroomingNote.setId(null);
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
            moeGroomingNote.setNote(ticketComments);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }

        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId(moeGroomingAppointment.getId());

        return ResponseResult.success(addResultDTO);
    }

    public ResponseResult<Integer> modifyGroomingAppointmentWaiting(
            Integer tokenStaffId, AppointmentParams appointment) {
        MoeGroomingAppointment temp = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                appointment.getId(), appointment.getBusinessId());
        if (temp == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();

        String appointmentDate = appointment.getAppointmentDateString();
        if (StringUtils.hasText(appointmentDate)) {
            moeGroomingAppointment.setAppointmentDate(appointmentDate);
            moeGroomingAppointment.setAppointmentEndDate(
                    appointmentDate); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        }

        BeanUtils.copyProperties(appointment, moeGroomingAppointment);
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());

        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);

        moePetDetailService.deleteByAppointId(moeGroomingAppointment.getId());
        if (appointment.getPetServices() != null && appointment.getPetServices().size() > 0) {
            moePetDetailService.addMoePetDetailsWaiting(moeGroomingAppointment, appointment.getPetServices());
        }

        String alertNotes = appointment.getAlertNotes();
        String ticketComments = appointment.getTicketComments();
        // 更新alertNotes和comments

        MoeGroomingNote moeGroomingNote = new MoeGroomingNote();
        moeGroomingNote.setBusinessId(temp.getBusinessId());
        moeGroomingNote.setCompanyId(temp.getCompanyId());
        moeGroomingNote.setCustomerId(temp.getCustomerId());
        moeGroomingNote.setGroomingId(temp.getId());
        moeGroomingNote.setCreateBy(tokenStaffId);
        moeGroomingNote.setUpdateBy(tokenStaffId);
        moeGroomingNote.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingNote.setUpdateTime(CommonUtil.get10Timestamp());

        if (alertNotes != null) {
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_ALERT);
            moeGroomingNote.setNote(alertNotes);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }

        if (ticketComments != null) {
            moeGroomingNote.setId(null);
            moeGroomingNote.setType(GroomingAppointmentEnum.NOTE_COMMENT);
            moeGroomingNote.setNote(ticketComments);
            moeGroomingNoteService.atomicityInsertOrUpdate(moeGroomingNote);
        }
        return ResponseResult.success(i);
    }

    public ResponseResult<List<GroomingAppointmentWaitingListDTO>> queryAppointmentWaitingList(
            WaitingPetDetailVO waitingPetDetailVO, Integer tokenStaffId) {
        List<GroomingAppointmentWaitingListDTO> groomingAppointmentWaitingLists =
                moeGroomingAppointmentMapper.queryAppointmentWaitingList(waitingPetDetailVO);

        List<Integer> groomingIdList = groomingAppointmentWaitingLists.stream()
                .map(GroomingAppointmentWaitingListDTO::getTicketId)
                .toList();
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingIdList(
                        waitingPetDetailVO.getBusinessId(), groomingIdList);

        List<GroomingQueryDto> ticketInfo = new LinkedList<>();
        List<Integer> petIds = new LinkedList<>();
        List<Integer> staffIds = new LinkedList<>();

        for (GroomingAppointmentWaitingListDTO groomingAppointmentWaitingListDTO : groomingAppointmentWaitingLists) {
            if (groomingAppointmentWaitingListDTO.getCustomerAddressId() != null) {
                GroomingQueryDto groomingQueryDto = new GroomingQueryDto();
                groomingQueryDto.setCustomerAddressId(groomingAppointmentWaitingListDTO.getCustomerAddressId());
                groomingQueryDto.setGroomingId(groomingAppointmentWaitingListDTO.getTicketId());
                groomingQueryDto.setCustomerId(groomingAppointmentWaitingListDTO.getCustomerId());

                ticketInfo.add(groomingQueryDto);
            }
            if (!CollectionUtils.isEmpty(groomingAppointmentWaitingListDTO.getWaitingListPetDetails())) {
                groomingAppointmentWaitingListDTO.getWaitingListPetDetails().forEach(e -> {
                    if (e.getPetId() != null) {
                        petIds.add(e.getPetId());
                    }
                    if (e.getStaffId() != null) {
                        staffIds.add(e.getStaffId());
                    }
                    if (!CollectionUtils.isEmpty(operationMap) && operationMap.containsKey(e.getPetDetailId())) {
                        e.setOperationList(operationMap.get(e.getPetDetailId()));
                    }
                });
            }
        }

        // 查询customer信息
        if (!CollectionUtils.isEmpty(ticketInfo)) {
            queryWaitingCustomerInfo(groomingAppointmentWaitingLists, ticketInfo, tokenStaffId);
        }

        // 查询宠物信息
        if (!CollectionUtils.isEmpty(petIds)) {
            getWaitingPets(groomingAppointmentWaitingLists, petIds);
        }

        // 查询staff信息
        if (!CollectionUtils.isEmpty(staffIds)) {
            queryWaitingStaffInfo(waitingPetDetailVO, groomingAppointmentWaitingLists, staffIds);
        }

        return ResponseResult.success(groomingAppointmentWaitingLists);
    }

    private void queryWaitingStaffInfo(
            WaitingPetDetailVO waitingPetDetailVO,
            List<GroomingAppointmentWaitingListDTO> groomingAppointmentWaitingLists,
            List<Integer> staffIds) {
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(waitingPetDetailVO.getBusinessId());
        staffIdListParams.setStaffIdList(staffIds);

        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);

        for (GroomingAppointmentWaitingListDTO groomingAppointmentWaitingListDTO : groomingAppointmentWaitingLists) {
            if (CollectionUtils.isEmpty(groomingAppointmentWaitingListDTO.getWaitingListPetDetails())) {
                continue;
            }

            for (WaitingListPetDetailDTO waitingListPetDetailDTO :
                    groomingAppointmentWaitingListDTO.getWaitingListPetDetails()) {
                for (MoeStaffDto moeStaffDto : staffList) {
                    if (moeStaffDto.getId().equals(waitingListPetDetailDTO.getStaffId())) {
                        waitingListPetDetailDTO.setStaffFirstName(moeStaffDto.getFirstName());
                        waitingListPetDetailDTO.setStaffLastName(moeStaffDto.getLastName());
                        break;
                    }
                }
            }
        }
    }

    private void getWaitingPets(
            List<GroomingAppointmentWaitingListDTO> groomingAppointmentWaitingLists, List<Integer> petIds) {
        List<CustomerPetDetailDTO> customerPetListByIdList = iPetClient.getCustomerPetListByIdList(petIds);

        for (GroomingAppointmentWaitingListDTO groomingAppointmentWaitingListDTO : groomingAppointmentWaitingLists) {
            if (CollectionUtils.isEmpty(groomingAppointmentWaitingListDTO.getWaitingListPetDetails())) {
                continue;
            }

            for (WaitingListPetDetailDTO waitingListPetDetailDTO :
                    groomingAppointmentWaitingListDTO.getWaitingListPetDetails()) {
                for (CustomerPetDetailDTO customerPetDetail : customerPetListByIdList) {
                    if (customerPetDetail.getPetId().equals(waitingListPetDetailDTO.getPetId())) {
                        waitingListPetDetailDTO.setPetName(customerPetDetail.getPetName());
                        waitingListPetDetailDTO.setPetBreed(customerPetDetail.getBreed());
                        break;
                    }
                }
            }
        }
    }

    private void queryWaitingCustomerInfo(
            List<GroomingAppointmentWaitingListDTO> groomingAppointmentWaitingLists,
            List<GroomingQueryDto> ticketInfo,
            Integer tokenStaffId) {
        GroomingCustomerInfoParams groomingCustomerInfoParams = new GroomingCustomerInfoParams();
        groomingCustomerInfoParams.setTokenStaffId(tokenStaffId);
        groomingCustomerInfoParams.setTicketInfo(ticketInfo);

        List<GroomingCalenderCustomerInfo> groomingCalenderCustomerInfos =
                iCustomerGroomingClient.getGroomingCalenderCustomerInfo(groomingCustomerInfoParams);
        for (GroomingAppointmentWaitingListDTO groomingAppointmentWaitingListDTO : groomingAppointmentWaitingLists) {
            for (GroomingCalenderCustomerInfo groomingCalenderCustomerInfo : groomingCalenderCustomerInfos) {
                if (groomingCalenderCustomerInfo
                                .getCustomerId()
                                .equals(groomingAppointmentWaitingListDTO.getCustomerId())
                        && groomingCalenderCustomerInfo
                                .getGroomingId()
                                .equals(groomingAppointmentWaitingListDTO.getTicketId())) {
                    groomingAppointmentWaitingListDTO.setAddress1(groomingCalenderCustomerInfo.getAddress1());
                    groomingAppointmentWaitingListDTO.setAddress2(groomingCalenderCustomerInfo.getAddress2());
                    groomingAppointmentWaitingListDTO.setCity(groomingCalenderCustomerInfo.getCity());
                    groomingAppointmentWaitingListDTO.setCountry(groomingCalenderCustomerInfo.getCountry());
                    groomingAppointmentWaitingListDTO.setZipcode(groomingCalenderCustomerInfo.getZipcode());
                    groomingAppointmentWaitingListDTO.setState(groomingCalenderCustomerInfo.getState());

                    groomingAppointmentWaitingListDTO.setClientPhoneNumber(
                            groomingCalenderCustomerInfo.getClientPhoneNumber());

                    groomingAppointmentWaitingListDTO.setCustomerFirstName(
                            groomingCalenderCustomerInfo.getCustomerFirstName());
                    groomingAppointmentWaitingListDTO.setCustomerLastName(
                            groomingCalenderCustomerInfo.getCustomerLastName());
                    break;
                }
            }
        }

        setClientFullAddress(groomingAppointmentWaitingLists);
    }

    private void setClientFullAddress(List<GroomingAppointmentWaitingListDTO> groomingAppointmentWaitingLists) {
        groomingAppointmentWaitingLists.stream().forEach(e -> {
            StringJoiner sj = AddressUtil.getFullAddress(
                    e.getAddress1(), e.getAddress2(), e.getCity(), e.getState(), e.getCountry(), e.getZipcode());

            e.setClientFullAddress(sj.toString());
        });
    }

    // @Transactional
    public ResponseResult<Integer> editAppointmentColorCode(ColorEditParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        moeGroomingAppointment = new MoeGroomingAppointment();

        moeGroomingAppointment.setId(editIdParams.getId());
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointment.setColorCode(editIdParams.getColorCode());
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        return ResponseResult.success(i);
    }

    public void modifyAppointmentColorRepeat(ColorEditParams editIdParams) {
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (Objects.isNull(moeGroomingAppointment)) {
            throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }
        Integer repeatId = moeGroomingAppointment.getRepeatId();
        String date = moeGroomingAppointment.getAppointmentDate();
        Integer repeatType = editIdParams.getRepeatType();
        if (Objects.isNull(repeatId) || repeatId == 0) {
            repeatType = RepeatModifyTypeEnum.ONLY_THIS.getRepeatType();
        }

        // 修改本预约
        editAppointmentColorCode(editIdParams);
        if (RepeatModifyTypeEnum.ONLY_THIS.getRepeatType().equals(repeatType)) {
            return;
        }

        Integer finalRepeatType = repeatType;
        ThreadPool.execute(() -> {
            if (RepeatModifyTypeEnum.THIS_AND_FOLLOWING.getRepeatType().equals(finalRepeatType)) {
                // 修改之后预约
                moeGroomingAppointmentMapper.queryApptsByRepeatId(editIdParams.getBusinessId(), repeatId, date).stream()
                        .map(MoeGroomingAppointment::getId)
                        .forEach(id -> {
                            editIdParams.setId(id);
                            editAppointmentColorCode(editIdParams);
                        });
            } else if (RepeatModifyTypeEnum.ALL.getRepeatType().equals(finalRepeatType)) {
                // 修改所有未完成预约
                moeGroomingAppointmentMapper.queryApptsByRepeatId(editIdParams.getBusinessId(), repeatId, null).stream()
                        .map(MoeGroomingAppointment::getId)
                        .filter(id -> !Objects.equals(id, moeGroomingAppointment.getId()))
                        .forEach(id -> {
                            editIdParams.setId(id);
                            editAppointmentColorCode(editIdParams);
                        });
            }
        });
    }

    public void checkBusinessStaff(Integer businessId, Integer staffId) {
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(staffId);
        MoeStaffDto staffDto = iBusinessStaffClient.getStaff(staffIdParams);
        if (staffDto == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "staff mismatch");
        }
    }

    public void checkBusinessCustomer(Integer businessId, Integer customerId) {
        if (businessId == null
                || customerId == null
                || !iCustomerCustomerClient.checkingBizByCustomerId(businessId, customerId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "customer mismatch");
        }
    }

    /**
     * 批处理操作： 一次性把sourceStaffId的预约修改为targetStaffId
     *
     * @param businessId
     * @param apptParam
     * @return
     */
    public Boolean transferAppointment(Integer businessId, TransferAppointmentParams apptParam) {
        Integer sourceStaffId = apptParam.getSourceStaffId();
        Integer targetStaffId = apptParam.getTargetStaffId();

        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String nowDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        List<Integer> allChangeGroomingId =
                moeGroomingPetDetailMapper.queryTransferAppointment(businessId, sourceStaffId, nowDate, nowMinutes);

        // record transfer activity logs
        allChangeGroomingId.forEach(
                id -> ActivityLogRecorder.record(AppointmentAction.TRANSFER, ResourceType.APPOINTMENT, id, null));

        moeGroomingPetDetailMapper.transferAppointment(
                sourceStaffId, targetStaffId, CommonUtil.get10Timestamp(), allChangeGroomingId);
        // transfer operation
        groomingServiceOperationService.transferOperation(sourceStaffId, targetStaffId, allChangeGroomingId);
        ThreadPool.execute(() -> {
            // update appt
            for (Integer groomingId : allChangeGroomingId) {
                calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                        businessId, groomingId, null, true); // transferAppointment
            }
        });
        return true;
    }

    public CustomerGroomingAppointmentDTO getCustomerLastAppointmentForOnlineBooking(
            Integer customerId, Integer businessId) {
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }
        LocalDateTime now = LocalDateTime.now();
        String s = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        CustomerGroomingAppointmentDTO last = moeGroomingAppointmentMapper.queryCustomerLastOrNextAppointment(
                businessInfo.getCompanyId().longValue(), customerId, s, nowMinutes, null, true, false);
        if (last != null) {
            List<CustomerGroomingAppointmentPetDetailDTO> customerGroomingAppointmentPetDetailLast =
                    moeGroomingPetDetailMapper.queryCustomerAppointmentPetDetail(last.getId());
            companyGroomingServiceQueryService.obServiceQueryForPetDetailDto(
                    businessId, customerGroomingAppointmentPetDetailLast);
            Set<Integer> petIdSet = new HashSet<>();
            for (CustomerGroomingAppointmentPetDetailDTO petDetail : customerGroomingAppointmentPetDetailLast) {
                petIdSet.add(petDetail.getPetId());
            }
            List<CustomerPetDetailDTO> petDetailDTOS = iPetClient.getCustomerPetListByIdList(new ArrayList<>(petIdSet));
            for (CustomerGroomingAppointmentPetDetailDTO petDetail : customerGroomingAppointmentPetDetailLast) {
                for (CustomerPetDetailDTO petDetailDTO : petDetailDTOS) {
                    if (petDetailDTO.getPetId().equals(petDetail.getPetId())) {
                        petDetail.setPetName(petDetailDTO.getPetName());
                        petDetail.setPetBreed(petDetailDTO.getBreed());
                        petDetail.setLifeStatus(petDetailDTO.getLifeStatus());
                        petDetail.setPetIsDelete(petDetailDTO.getStatus());
                    }
                }
            }

            last.setPetDetails(customerGroomingAppointmentPetDetailLast);
        }

        return last;
    }

    public boolean batchDeleteByCustomerId(Integer staffId, BusinessClientsDTO businessClientsDTO) {
        if (Objects.isNull(businessClientsDTO.businessId())
                || CollectionUtils.isEmpty(businessClientsDTO.customerIds())) {
            return true;
        }
        String tz = companyHelper.getCompanyTimeZoneName(businessClientsDTO.companyId());
        List<MoeGroomingAppointment> needDeleteApptList = moeGroomingAppointmentMapper.batchSelectCustomerDeleteAppt(
                businessClientsDTO.businessId(), businessClientsDTO.customerIds());
        if (CollectionUtils.isEmpty(needDeleteApptList)) {
            return true;
        }
        List<Long> needDeleteApptIdList =
                needDeleteApptList.stream().map(k -> k.getId().longValue()).toList();

        waitListService.deleteByAppointmentId(
                businessClientsDTO.companyId(), businessClientsDTO.businessId().longValue(), needDeleteApptIdList, tz);

        moeGroomingAppointmentMapper.batchDeleteApptByCustomerId(
                businessClientsDTO.businessId(),
                businessClientsDTO.customerIds(),
                staffId,
                CommonUtil.get10Timestamp());
        afterBatchDeleteAppt(needDeleteApptList, businessClientsDTO.businessId());
        return true;
    }

    public void afterBatchDeleteAppt(List<MoeGroomingAppointment> needDeleteApptList, Integer businessId) {
        ThreadPool.execute(() -> {
            for (MoeGroomingAppointment needDeleteAppt : needDeleteApptList) {
                // customer delete appt
                calendarSyncService.checkBusinessHaveGoogleCalendarSync( // batchDeleteByCustomerId
                        businessId, needDeleteAppt.getId(), needDeleteAppt.getAppointmentDate(), true);
                quickBooksService.addRedisSyncGroomingData(
                        businessId, needDeleteAppt.getId(), needDeleteAppt.getAppointmentDate());
            }
            // 标记订单状态、释放product库存
            List<Integer> needDeleteApptIds = needDeleteApptList.stream()
                    .map(MoeGroomingAppointment::getId)
                    .collect(toList());
            orderService.updateOrderWhenCancelAppts(businessId, needDeleteApptIds);
        });
    }

    public ResponseResult<Integer> toWaiting(EditIdParams editIdParams) {
        MoeGroomingAppointment oldMoeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(
                editIdParams.getId(), editIdParams.getBusinessId());
        if (oldMoeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        Long companyId = oldMoeGroomingAppointment.getCompanyId();

        Byte isWaitingList = oldMoeGroomingAppointment.getIsWaitingList();
        final Boolean isOnlineBooking =
                GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB.equals(oldMoeGroomingAppointment.getBookOnlineStatus());
        MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();
        moeGroomingAppointment.setId(editIdParams.getId());
        if (GroomingAppointmentEnum.NOT_WAITING_LIST.equals(isWaitingList)) {
            moeGroomingAppointment.setIsWaitingList(GroomingAppointmentEnum.IS_WAITING_LIST);
            // 加入waiting，移除repeat
            moeGroomingAppointment.setRepeatId(0);
            moeGroomingAppointment.setMoveWaitingBy(editIdParams.getAccountId());
            if (isOnlineBooking && editIdParams.getIsCalledByTaskModule()) {
                // OB异步通知
                ThreadPool.execute(() -> {
                    OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams = new OnlineBookWaitingNotifyParams();
                    onlineBookWaitingNotifyParams.setCompanyId(companyId);
                    onlineBookWaitingNotifyParams.setBusinessId(editIdParams.getBusinessId());
                    onlineBookWaitingNotifyParams.setGroomingId(editIdParams.getId());
                    onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_WAITING);
                    iNotificationClient.bookOnlineNotify(onlineBookWaitingNotifyParams);
                });
            }
        } else if (GroomingAppointmentEnum.IS_WAITING_LIST.equals(isWaitingList)) {
            moeGroomingAppointment.setIsWaitingList(GroomingAppointmentEnum.NOT_WAITING_LIST);
        } else {
            log.warn("unknown is_waiting_list value {}, groomingId = {}", isWaitingList, editIdParams.getId());
            throw new CommonException("unknown appointment is_waiting_list status");
        }
        moeGroomingAppointment.setUpdateTime(CommonUtil.get10Timestamp());
        int i = moeGroomingAppointmentMapper.updateByPrimaryKeySelective(moeGroomingAppointment);
        if (GroomingAppointmentEnum.NOT_WAITING_LIST.equals(isWaitingList)) {
            waitListService.addToWaitList(List.of(moeGroomingAppointment.getId()), false);
        }

        ThreadPool.execute(() -> {
            // move to waiting list
            calendarSyncService.checkBusinessHaveGoogleCalendarSync( // toWaiting
                    oldMoeGroomingAppointment.getBusinessId(),
                    oldMoeGroomingAppointment.getId(),
                    oldMoeGroomingAppointment.getAppointmentDate());
            quickBooksService.addRedisSyncGroomingData(
                    oldMoeGroomingAppointment.getBusinessId(),
                    oldMoeGroomingAppointment.getId(),
                    oldMoeGroomingAppointment.getAppointmentDate());
        });
        mqService.publishAppointmentCancelEvent(oldMoeGroomingAppointment, null, true);
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(oldMoeGroomingAppointment.getBusinessId())
                .setCustomerId(oldMoeGroomingAppointment.getCustomerId()));
        return ResponseResult.success(i);
    }

    public ResponseResult<Integer> editAppointmentConfirm(ConfirmParams editIdParams) {
        if (GroomingAppointmentEnum.CONFIRM_TYPE_BY_CLIENT.equals(editIdParams.getConfirmByType())) {
            confirmAppointmentByClient(editIdParams.getId(), editIdParams.getConfirmByMethod());
        } else {
            updateAppointmentStatus(
                    editIdParams.getBusinessId(),
                    editIdParams.getAccountId(),
                    new StatusUpdateParams(editIdParams.getId(), AppointmentStatusEnum.CONFIRMED, null, null));
        }
        return ResponseResult.success(1);
    }

    public ResponseResult<GroomingStaffUpcomingAppointmentCountDto> queryStaffUpComingAppointCount(
            Integer businessId, Integer staffId) {
        GroomingStaffUpcomingAppointmentCountDto res = new GroomingStaffUpcomingAppointmentCountDto();

        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        List<Integer> num =
                moeGroomingAppointmentMapper.queryStaffUpComingAppointCount(businessId, staffId, date, nowMinutes);

        res.setStaffId(staffId);
        res.setUpcomingCount(num == null ? 0 : num.size());
        return ResponseResult.success(res);
    }

    public ResponseResult<GroomingServiceUpcomingAppointmentCountDto> queryServiceUpComingAppointCount(
            Integer businessId, Integer serviceId) {
        GroomingServiceUpcomingAppointmentCountDto res = new GroomingServiceUpcomingAppointmentCountDto();

        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        // DONE(Frank): 无businessId 导致查询无索引
        List<Integer> num =
                moeGroomingAppointmentMapper.queryServiceUpComingAppointCount(businessId, serviceId, date, nowMinutes);

        res.setServiceId(serviceId);
        res.setUpcomingCount(num == null ? 0 : num.size());
        return ResponseResult.success(res);
    }

    public Integer queryStaffUpComingOperationCount(Integer businessId, Integer sourceStaffId, Integer targetStaffId) {
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (Objects.isNull(businessInfo)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        List<Integer> groomingIdList = moeGroomingAppointmentMapper.queryStaffUpComingAppointCount(
                businessId, sourceStaffId, date, nowMinutes);

        return groomingServiceOperationService.queryStaffUpComingOperationCount(
                targetStaffId, sourceStaffId, groomingIdList);
    }

    public CustomerAppointmentListDTO queryPreviewListForCustomer(
            long companyId, Integer businessId, UpcomingPreviewVo previewVo) {

        // 查询business信息
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        var businessInfo = iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(infoIdParams);
        // 查询customer信息
        var customerInfo = iCustomerCustomerClient.getCustomerWithDeletedNoBusinessId(previewVo.getCustomerId());
        if (customerInfo == null) {
            return new CustomerAppointmentListDTO(Collections.emptyList());
        }

        List<CustomerUpComingAppointDTO> queryResultList =
                appointmentQueryService.queryCustomerUpComingAppointForClientShare(
                        companyId, previewVo, businessInfo, customerInfo);
        List<Integer> ids = new ArrayList<>();
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : queryResultList) {
            ids.add(customerUpComingAppointDTO.getId());
        }
        if (CollectionUtils.isEmpty(ids)) {
            return new CustomerAppointmentListDTO(Collections.emptyList());
        }
        List<CustomerGrooming> customerGroomings =
                moeGroomingAppointmentMapper.queryGroomingCustomerAppointmentByIds(ids);
        // 处理一下兼容状态
        customerGroomings.forEach(a -> {
            Byte status = a.getStatus().byteValue();
            a.setStatus(getCompatibleStatus(status));
            a.setAppointmentStatus(getCompatibleAppointmentStatus(status, a.getCheckInTime()));
        });

        var appointmentIdToSubtotal = invoiceService.querySubtotalByGroomingIds(
                companyId, businessId, ids, InvoiceStatusEnum.TYPE_APPOINTMENT);
        customerGroomings.forEach(grooming -> {
            var subtotal = appointmentIdToSubtotal.get(grooming.getId());
            if (Objects.isNull(subtotal)) {
                return;
            }
            grooming.setSubTotalAmount(subtotal);
        });

        return prepareRelevantData(businessId, customerGroomings);
    }

    public CustomerAppointmentListDTO prepareRelevantData(
            Integer businessId, List<CustomerGrooming> customerGroomings) {
        CustomerAppointmentListDTO pageInfo = new CustomerAppointmentListDTO(customerGroomings);

        // query relative data
        Set<Integer> petIds = new HashSet<>();
        Set<Integer> staffIds = new HashSet<>();
        Set<Integer> serviceIds = new HashSet<>();
        for (CustomerGrooming customerGrooming : customerGroomings) {
            for (GroomingCustomerPetdetailDTO petDetail : customerGrooming.getPetServiceList()) {
                petIds.add(petDetail.getPetId());
                staffIds.add(petDetail.getStaffId());
                serviceIds.add(petDetail.getServiceId());
            }
        }
        // pet info
        List<CustomerPetDetailDTO> customerPetListByIdList =
                iPetClient.getCustomerPetListByIdList(new ArrayList<>(petIds));
        for (CustomerGrooming customerGrooming : customerGroomings) {
            for (GroomingCustomerPetdetailDTO petDetail : customerGrooming.getPetServiceList()) {
                for (CustomerPetDetailDTO customerPetDetailDTO : customerPetListByIdList) {
                    if (petDetail.getPetId().equals(customerPetDetailDTO.getPetId())) {
                        petDetail.setPetName(customerPetDetailDTO.getPetName());
                        petDetail.setPetBreed(customerPetDetailDTO.getBreed());
                        break;
                    }
                }
            }
        }
        pageInfo.setPetList(customerPetListByIdList);
        // staff
        List<MoeStaffDto> staffList =
                iBusinessStaffClient.getStaffList(new StaffIdListParams(businessId, new ArrayList<>(staffIds)));
        pageInfo.setStaffList(CommonUtil.copyListProperties(staffList, CustomerAppointmentListStaffDTO.class));

        // service
        List<MoeGroomingServiceDTO> groomingServiceDTOS =
                groomingServiceService.getServicesByServiceIds(businessId, new ArrayList<>(serviceIds));
        pageInfo.setServiceList(groomingServiceDTOS);
        // appointment list
        pageInfo.setList(customerGroomings);
        return pageInfo;
    }

    public Boolean customerDelete(CustomerDeleteParams deleteParams) {

        var migrateInfo = migrateHelper.getMigrationInfo(deleteParams.getBusinessId());
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();
        var staffId = deleteParams.getStaffId();

        // FIXME: 取 company 的 timezone
        String tz =
                waitListService.getBusinessTimeZone(deleteParams.getBusinessId().longValue());

        Set<Integer> workingLocationIds = null;
        if (migrated) {
            workingLocationIds = staffServiceBlockingStub
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff()
                    .getWorkingLocationListList()
                    .stream()
                    .map(LocationBriefView::getId)
                    .map(Long::intValue)
                    .collect(toSet());
        }

        List<MoeGroomingAppointment> needDeleteApptList =
                moeGroomingAppointmentMapper.selectCustomerDeleteAppt(companyId, deleteParams.getCustomerId());
        if (CollectionUtils.isEmpty(needDeleteApptList)) {
            return true;
        }
        var businessAppointments =
                needDeleteApptList.stream().collect(Collectors.groupingBy(MoeGroomingAppointment::getBusinessId));
        if (workingLocationIds != null && !workingLocationIds.containsAll(businessAppointments.keySet())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PERMISSION_NOT_ENOUGH,
                    "Failed to delete client. Current client has unfinished appointments outside of your working businesses, please remove all unfinished appointments before proceed.");
        }

        for (var entry : businessAppointments.entrySet()) {
            var businessId = entry.getKey();
            var appointments = entry.getValue();

            List<Long> needDeleteApptIdList =
                    appointments.stream().map(k -> k.getId().longValue()).toList();

            waitListService.deleteByAppointmentId(companyId, Long.valueOf(businessId), needDeleteApptIdList, tz);
            moeGroomingAppointmentMapper.customerDelete(
                    companyId, deleteParams.getCustomerId(), deleteParams.getStaffId(), CommonUtil.get10Timestamp());
            afterBatchDeleteAppt(needDeleteApptList, businessId);
        }
        return true;
    }

    public MoeGroomingAppointment getAppointment(Integer appointmentId) {
        return moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
    }

    public void setNoShoFee(Integer appointmentId, BigDecimal fee) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId);
        if (appointment.getNoShow().compareTo(Byte.parseByte("1")) != 0) {
            throw new CommonException(ResponseCodeEnum.APPOINTMENT_INVALID_STATUS);
        }
        appointment.setNoShowFee(fee);
        // FIXME(p2): 应该只更新需要修改的字段，而不是全部字段
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointment);
    }

    public List<TimeRangeDto> queryStaffAppointTimeByDate(Integer businessId, String appointmentDate, Integer staffId) {
        return moeGroomingAppointmentMapper.queryStaffAppointTimeByDate(businessId, appointmentDate, staffId);
    }

    /**
     * 根据appointmentDates、startTimes查找所选时间内appointment的pet 数量，同一个appt pet去重，不同appt不去重
     *
     * @param businessId
     * @param appointmentDates
     * @param startTimes
     * @return
     */
    public Collection<StaffTimeslotPetCountDTO> queryStaffTimeslotPetCountByTime(
            Integer businessId,
            Set<String> appointmentDates,
            Set<Integer> startTimes,
            Set<Integer> staffIds,
            Integer filterAppointmentId) {
        if (CollectionUtils.isEmpty(appointmentDates) || Objects.isNull(businessId)) {
            return Collections.emptyList();
        }

        String companyTimeZone = companyHelper.getCompanyTimeZoneName(companyHelper.mustGetCompanyId(businessId));
        var startDate = appointmentDates.stream().min(Comparator.naturalOrder()).orElseThrow();
        var endDate = appointmentDates.stream().max(Comparator.naturalOrder()).orElseThrow();

        LocalDate currentStartDate = LocalDate.parse(startDate);
        LocalDate finalEndDate = LocalDate.parse(endDate);

        // 创建异步任务列表
        List<CompletableFuture<List<SmartScheduleGroomingDetailsDTO>>> futures = new ArrayList<>();

        // 内部接口只支持最多查询 60 天，因此这里需要分段查询，step = 30 天
        // 这里不直接一次性查询60天是因为 smartScheduleService.queryByBusinessIdBetweenDates 方法会将 startDateRange=[endDate-60,endDate]
        // 如果 step = 60, 那么 endDate 经过 +60 和 -60 后，会导致 appointmentDate 在 endDate-60 之前的都查询不到
        while (currentStartDate.isBefore(finalEndDate) || currentStartDate.isEqual(finalEndDate)) {
            // 计算当前查询的结束日期：当前开始日期+30天），但不超过最终结束日期
            LocalDate periodStartDate = currentStartDate;
            LocalDate periodEndDate =
                    currentStartDate.plusDays(30).isAfter(finalEndDate) ? finalEndDate : currentStartDate.plusDays(30);

            // 创建异步任务
            futures.add(CompletableFuture.supplyAsync(
                    () -> smartScheduleService.queryByBusinessIdBetweenDates(
                            businessId,
                            periodStartDate,
                            periodEndDate,
                            staffIds.stream().toList(),
                            companyTimeZone),
                    executorService));

            if (periodEndDate.isEqual(finalEndDate)) {
                break;
            }
            currentStartDate = periodEndDate;
        }

        var petDetails = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .filter(e -> PrimitiveTypeUtil.isNullOrZero(filterAppointmentId)
                        || !Objects.equals(filterAppointmentId, e.getGroomingId()))
                .filter(e -> appointmentDates.contains(e.getStartDate())
                        && (CollectionUtils.isEmpty(startTimes)
                                || startTimes.contains(e.getStartTime().intValue())))
                .toList();

        return getStaffTimeslotPetCountByPetDetails(petDetails);
    }

    /**
     * 根据 petDetails 计算每个 timeslot 的宠物数量和预约数量
     * 同一个pet下：
     * - service 可以连接上，(service2.start_time = service1.end_time)，这个pet只占用 service1 的 start_time 的slot
     * - service 不连接，中间有间隔，这个pet占用各个 service 的 start time 的那个slot
     *
     * @param petDetails 宠物预约详情列表
     * @return 每个 timeslot 的统计结果
     */
    public static List<StaffTimeslotPetCountDTO> getStaffTimeslotPetCountByPetDetails(
            List<SmartScheduleGroomingDetailsDTO> petDetails) {
        // 过滤无效数据
        var filteredDetails = petDetails.stream()
                .filter(e -> e.getPetId() != 0)
                .filter(e -> e.getStartTime() != null && e.getEndTime() != null)
                .toList();

        // 按照 date、startTime、staffId 分组
        Map<Triple<String, Integer, Integer>, List<SmartScheduleGroomingDetailsDTO>> slotStaffDetails = new HashMap<>();

        // 根据连续性处理每个 pet_detail
        filteredDetails.stream()
                .collect(Collectors.groupingBy(dto -> Triple.of(dto.getStartDate(), dto.getStaffId(), dto.getPetId())))
                .forEach((petDetailKey, details) -> {
                    String date = petDetailKey.getLeft();
                    Integer staffId = petDetailKey.getMiddle();

                    // 对详情按开始时间排序
                    details.sort(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime));

                    // 检查连续性并记录 timeslot
                    for (int i = 0; i < details.size(); ) {
                        SmartScheduleGroomingDetailsDTO currentPetDetail = details.get(i);
                        Integer slotTime = currentPetDetail.getStartTime().intValue();
                        Triple<String, Integer, Integer> slotKey = Triple.of(date, slotTime, staffId);
                        List<SmartScheduleGroomingDetailsDTO> slotDetails =
                                slotStaffDetails.computeIfAbsent(slotKey, k -> new ArrayList<>());
                        slotDetails.add(currentPetDetail);

                        // 检查是否有连续的服务
                        int j = i + 1;
                        while (j < details.size()
                                && details.get(j - 1)
                                        .getEndTime()
                                        .equals(details.get(j).getStartTime())) {
                            slotDetails.add(details.get(j));
                            j++;
                        }

                        // 跳过已处理的连续 pet_detail
                        i = j;
                    }
                });

        // 构建结果
        var results = new ArrayList<StaffTimeslotPetCountDTO>();

        for (Map.Entry<Triple<String, Integer, Integer>, List<SmartScheduleGroomingDetailsDTO>> entry :
                slotStaffDetails.entrySet()) {
            Triple<String, Integer, Integer> slotGroup = entry.getKey();
            List<SmartScheduleGroomingDetailsDTO> slotDetails = entry.getValue();

            StaffTimeslotPetCountDTO dto = new StaffTimeslotPetCountDTO();
            dto.setSlotDate(slotGroup.getLeft());
            dto.setSlotTime(slotGroup.getMiddle());
            dto.setStaffId(slotGroup.getRight());
            dto.setPetDetails(slotDetails); // 设置原始详情列表

            dto.setPetCount((int) slotDetails.stream()
                    .map(SmartScheduleGroomingDetailsDTO::getPetId)
                    .distinct()
                    .count());

            dto.setAppointmentCount((int) slotDetails.stream()
                    .map(SmartScheduleGroomingDetailsDTO::getGroomingId)
                    .distinct()
                    .count());

            results.add(dto);
        }

        return results;
    }

    /**
     * 查询所选时间内的blocks
     *
     * @param businessId
     * @param appointmentDates
     * @return
     */
    public List<StaffBlockInfoDTO> queryStaffBlocksByDates(Integer businessId, Set<String> appointmentDates) {
        return moeGroomingAppointmentMapper.selectBlockByApptDates(businessId, appointmentDates);
    }

    /**
     * 查询指定 staff 在所选时间内的所有 block
     *
     * @param businessId
     * @param appointmentDates
     * @param staffIds
     * @return
     */
    public List<StaffBlockInfoDTO> queryBlockListByDatesAndStaffIds(
            Integer businessId, Set<String> appointmentDates, Set<Integer> staffIds) {
        return moeGroomingAppointmentMapper.selectBlockListByDatesAndStaffIds(businessId, appointmentDates, staffIds);
    }

    public Business2021SummaryDto getBusiness2021Summary(Integer businessId, Integer staffId) {
        Business2021SummaryDto summaryDto = new Business2021SummaryDto();
        Integer serviceDurationTime =
                moeGroomingAppointmentMapper.getBusiness2021AllServiceDuration(businessId, staffId);
        // all duration
        summaryDto.setAllServiceDurationHours(
                (int) Math.round(serviceDurationTime == null ? 0 : serviceDurationTime / 60.0));
        // longest work
        AppointmentDateWithServiceDurationDto serviceDurationDto =
                moeGroomingAppointmentMapper.getBusiness2021LongestWorking(businessId, staffId);
        if (serviceDurationDto != null) {
            summaryDto.setLongestWorkingDay(serviceDurationDto.getAppointmentDate());
            summaryDto.setLongestWorkingHours((int) Math.round(serviceDurationDto.getLongestServiceTime() / 60.0));
        } else {
            summaryDto.setLongestWorkingDay("");
            summaryDto.setLongestWorkingHours(0);
        }
        // took care of pet cunt
        List<GroomingIdPetIdDto> idDtoList = moeGroomingAppointmentMapper.getBusiness2021PetIdList(businessId, staffId);
        summaryDto.setTakeCarePetCount(idDtoList.size());
        // pet breed count
        Map<Integer, Integer> petCountMap = new HashMap<>();
        for (GroomingIdPetIdDto idDto : idDtoList) {
            Integer count = petCountMap.get(idDto.getPetId());
            if (count == null) {
                count = 0;
            }
            count++;
            petCountMap.put(idDto.getPetId(), count);
        }
        List<Integer> petIdList =
                idDtoList.stream().map(GroomingIdPetIdDto::getPetId).collect(toList());
        List<GroomingCalenderPetInfo> petInfoList = iPetClient.getGroomingCalenderPetInfo(petIdList);
        Map<Integer, String> petBreedMap = petInfoList.stream()
                .collect(toMap(GroomingCalenderPetInfo::getPetId, GroomingCalenderPetInfo::getPetBreed));

        Map<String, Integer> petBreedCount = new HashMap<>();
        int mostBreedCount = 0;
        String mostBreed = "";

        for (var entry : petCountMap.entrySet()) {
            Integer petId = entry.getKey();
            String breed = petBreedMap.get(petId);
            Integer petCount = petCountMap.get(petId);

            Integer breedCount = petBreedCount.get(breed);
            if (breedCount == null) {
                breedCount = 0;
            }
            breedCount += petCount;
            petBreedCount.put(breed, breedCount);
            if (breedCount > mostBreedCount) {
                mostBreed = breed;
                mostBreedCount = breedCount;
            }
        }
        summaryDto.setPetBreedMost(mostBreed);
        summaryDto.setPetBreedMostCount(mostBreedCount);
        return summaryDto;
    }

    public ThanksgivingResultDto getPetCountUtilChristmas(Integer businessId) {
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }
        LocalDateTime now = LocalDateTime.now();
        String todayDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        ThanksgivingResultDto thanksgivingResultDto = new ThanksgivingResultDto();
        thanksgivingResultDto.setPetCount(
                moeGroomingAppointmentMapper.getPetCountUtilChristmas(businessId, todayDate, nowMinutes));
        thanksgivingResultDto.setAnnualPetCount(
                moeGroomingAppointmentMapper.get2021AnnualPetCountUtilChristmas(businessId));
        return thanksgivingResultDto;
    }

    /**
     * 将service最新的duration、price应用到upcoming unconfirm预约上
     * as 上线后不会被调用到
     *
     * @param businessId
     * @param serviceId
     */
    @Deprecated
    public void serviceApplyToUpcomingAppts(Integer businessId, Integer serviceId, Integer operatorId) {
        // 查询service信息
        MoeGroomingService service = companyGroomingServiceQueryService.selectByPrimaryKey(
                serviceId); // query duration && price for busienssId
        final Integer currentDuration = service.getDuration();
        final BigDecimal currentPrice = service.getPrice();
        serviceApplyToUpcomingAppts(businessId, serviceId, operatorId, currentDuration, currentPrice);
    }

    public void serviceApplyToUpcomingAppts(
            Integer businessId,
            Integer serviceId,
            Integer operatorId,
            Integer currentDuration,
            BigDecimal currentPrice) {
        // 查询business info
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String startDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        // 查询upcoming内有多少包含service的预约
        List<Integer> needUpdateApptId =
                moeGroomingPetDetailMapper.queryUpcomingApptsByServiceId(businessId, startDate, nowMinutes, serviceId);
        if (CollectionUtils.isEmpty(needUpdateApptId)) {
            return;
        }
        for (Integer apptId : needUpdateApptId) {
            MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(apptId);
            List<MoeGroomingPetDetail> petDetailList =
                    moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(apptId);

            Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                    groomingServiceOperationService.getOperationMapByGroomingId(businessId, apptId);

            AtomicBoolean isUpdateTime = new AtomicBoolean(false);
            petDetailList.stream()
                    .filter(petDetail -> petDetail.getServiceId().equals(serviceId))
                    .forEach(petDetail -> {
                        // time
                        Integer previousServiceTime = petDetail.getServiceTime();
                        if (!previousServiceTime.equals(currentDuration)) {
                            petDetail.setServiceTime(currentDuration);
                            isUpdateTime.set(true);
                            if (!CollectionUtils.isEmpty(operationMap)) {
                                reassignOperationTime(
                                        currentDuration, previousServiceTime, operationMap.get(petDetail.getId()));
                            }
                        }
                        // price
                        BigDecimal previousPrice = petDetail.getServicePrice();
                        if (!previousPrice.equals(currentPrice)) {
                            petDetail.setServicePrice(currentPrice);
                            if (!CollectionUtils.isEmpty(operationMap)) {
                                reassignOperationPrice(
                                        currentPrice, previousPrice, operationMap.get(petDetail.getId()));
                            }
                        }
                    });

            if (isUpdateTime.get()) {
                reassignServiceTime(appointment, petDetailList);
            }

            // save petDetail
            petDetailList.forEach(petDetail -> {
                MoeGroomingPetDetail petDetailUpdateBean = new MoeGroomingPetDetail();
                petDetailUpdateBean.setId(petDetail.getId());
                petDetailUpdateBean.setServicePrice(petDetail.getServicePrice());
                petDetailUpdateBean.setServiceTime(petDetail.getServiceTime());
                petDetailUpdateBean.setStartTime(petDetail.getStartTime());
                petDetailUpdateBean.setEndTime(petDetail.getEndTime());
                petDetailUpdateBean.setUpdateTime(DateUtil.get10Timestamp());
                moeGroomingPetDetailMapper.updateByPrimaryKeySelective(petDetailUpdateBean);
            });

            // save operation record
            if (!CollectionUtils.isEmpty(operationMap)) {
                groomingServiceOperationService.updateOperation(operationMap.values().stream()
                        .flatMap(Collection::stream)
                        .toList());
            }

            orderService.updateOrderByGroomingId(null, businessId, appointment.getId(), operatorId);

            ThreadPool.execute(() -> {
                if (isUpdateTime.get()) {
                    calendarSyncService.checkBusinessHaveGoogleCalendarSync( // serviceApplyToUpcomingAppts
                            appointment.getBusinessId(), appointment.getId(), appointment.getAppointmentDate(), true);
                }
                quickBooksService.addRedisSyncGroomingData(
                        appointment.getBusinessId(), appointment.getId(), appointment.getAppointmentDate());
            });
        }
    }

    private void reassignServiceTime(MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetailList) {
        orderByPetDetail(petDetailList);
        long minStartTime = petDetailList.stream()
                .mapToLong(MoeGroomingPetDetail::getStartTime)
                .min()
                .orElseThrow();
        long maxEndTime = petDetailList.stream()
                .mapToLong(MoeGroomingPetDetail::getEndTime)
                .max()
                .orElseThrow();
        // save appointment
        MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
        updateBean.setId(appointment.getId());
        updateBean.setAppointmentStartTime(Math.toIntExact(minStartTime));
        updateBean.setAppointmentEndTime(Math.toIntExact(maxEndTime));
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);

        // record activity log
        ActivityLogRecorder.record(Action.UPDATE, ResourceType.APPOINTMENT, appointment.getId(), updateBean);
    }

    private static void reassignOperationTime(
            Integer currentDuration, Integer previousServiceTime, List<GroomingServiceOperationDTO> operationList) {
        if (CollectionUtils.isEmpty(operationList)) {
            return;
        }

        // 如果上次时长为 0，则按照当前时长直接分配
        if (Objects.equals(previousServiceTime, 0)) {
            operationList.forEach(operation -> operation.setDuration(currentDuration));
            return;
        }

        IntFunction<Integer> durationFunction = duration -> currentDuration * duration / previousServiceTime;

        // 如果是并行的操作，则按照比例分配
        if (checkOperationListIsParallel(operationList)) {
            operationList.forEach(
                    operationDTO -> operationDTO.setDuration(durationFunction.apply(operationDTO.getDuration())));
            return;
        }

        // 如果是串行的操作，则按照顺序分配
        operationList.sort(Comparator.comparing(GroomingServiceOperationDTO::getStartTime));
        int totalDuration = 0;
        for (int i = 0; i < operationList.size(); i++) {
            GroomingServiceOperationDTO operationDTO = operationList.get(i);

            // 最后一个操作的时长倒减
            if (i == operationList.size() - 1) {
                GroomingServiceOperationDTO previousOperationDTO = operationList.get(i - 1);
                operationDTO.setStartTime(previousOperationDTO.getStartTime() + previousOperationDTO.getDuration());
                operationDTO.setDuration(currentDuration - totalDuration);
                return;
            }

            operationDTO.setDuration(durationFunction.apply(operationDTO.getDuration()));
            totalDuration += operationDTO.getDuration();
            // 第一个操作的开始时间不变，时长按照比例分配
            if (i == 0) {
                continue;
            }
            GroomingServiceOperationDTO previousOperationDTO = operationList.get(i - 1);
            operationDTO.setStartTime(previousOperationDTO.getStartTime() + previousOperationDTO.getDuration());
        }
    }

    private static void reassignOperationPrice(
            BigDecimal currentPrice, BigDecimal previousPrice, List<GroomingServiceOperationDTO> operationList) {
        if (CollectionUtils.isEmpty(operationList)) {
            return;
        }
        // 如果当前价格为 0
        if (BigDecimal.ZERO.compareTo(currentPrice) == 0) {
            operationList.forEach(operationDTO -> operationDTO.setPrice(BigDecimal.ZERO));
            return;
        }

        Function<BigDecimal, BigDecimal> priceFunction;
        // 如果上次价格为 0，则按照当前价格直接分配，否则按照比例缩放
        if (BigDecimal.ZERO.compareTo(previousPrice) == 0) {
            operationList.forEach(operationDTO -> operationDTO.setPrice(BigDecimal.ZERO));
            priceFunction = price -> currentPrice
                    .multiply(price)
                    .divide(BigDecimal.valueOf(operationList.size()), 2, RoundingMode.HALF_UP);
        } else {
            priceFunction = price -> currentPrice.multiply(price).divide(previousPrice, 2, RoundingMode.HALF_UP);
        }

        BigDecimal remainPrice = currentPrice;
        BigDecimal remainPriceRatio = BigDecimal.ONE;
        Iterator<GroomingServiceOperationDTO> iterator = operationList.iterator();
        while (iterator.hasNext()) {
            GroomingServiceOperationDTO operationDTO = iterator.next();
            if (!iterator.hasNext()) {
                operationDTO.setPrice(remainPrice);
                operationDTO.setPriceRatio(remainPriceRatio);
                break;
            }
            BigDecimal serviceContribution = priceFunction.apply(operationDTO.getPrice());
            operationDTO.setPrice(serviceContribution);
            remainPrice = remainPrice.subtract(serviceContribution);

            BigDecimal serviceContributionRatio = serviceContribution.divide(currentPrice, 2, RoundingMode.HALF_UP);
            operationDTO.setPriceRatio(serviceContributionRatio);
            remainPriceRatio = remainPriceRatio.subtract(serviceContributionRatio);
        }
    }

    public void orderByPetDetail(List<MoeGroomingPetDetail> petDetailList) {
        petDetailList.sort(Comparator.comparing(MoeGroomingPetDetail::getStartTime));
        long lastEndTime = -1L;
        for (MoeGroomingPetDetail petDetail : petDetailList) {
            if (lastEndTime != -1L) {
                petDetail.setStartTime(lastEndTime);
            }
            lastEndTime = petDetail.getStartTime() + petDetail.getServiceTime();
            petDetail.setEndTime(lastEndTime);
        }
    }

    /**
     * Get remain pets to be groomed from startDate to endDate.
     * <p> If staffId is not business owner, only return the pets that belong to the staff.
     * <p> If staffId is business owner, return all pets that belong to the business.
     *
     * @param businessId businessId
     * @param staffId    staffId, can be null, if null, return all pets that belong to the business
     * @param startDate  startDate, now if null
     * @param endDate    endDate
     * @param queryAll   whether query pets of any status appointment (not include canceled), default false, only query pets of in-progress appointments
     * @return remain pets to be groomed from startDate to endDate
     */
    public int getRemainPetCountWithin(
            Integer businessId, Integer staffId, @Nullable String startDate, String endDate, boolean queryAll) {
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        Stream<MoeGroomingAppointment> appointments = moeGroomingAppointmentMapper
                .getAppointmentsWithin(
                        businessId,
                        getStartDate(startDate, businessInfo),
                        getStartMinutes(startDate, businessInfo),
                        getEndDate(endDate),
                        getEndMinutes(endDate))
                .stream()
                .filter(appt -> appt.getStatus() != null
                        && !Objects.equals(appt.getStatus(), AppointmentStatusEnum.CANCELED.getValue()) /*canceled*/);

        if (!queryAll) {
            appointments = appointments.filter(MoeGroomingAppointmentService::inProgressAppointment);
        }

        List<MoeGroomingAppointment> appts = appointments.collect(toList());

        if (CollectionUtils.isEmpty(appts)) {
            return 0;
        }

        boolean isBusinessOwner =
                staffId == null || Objects.equals(staffId, iBusinessStaffClient.getOwnerStaffId(businessId));

        List<Integer> apptIds =
                appts.stream().map(MoeGroomingAppointment::getId).collect(toList());
        var petDetails = moePetDetailService.queryPetDetailByAppointmentIds(apptIds);
        Map</*apptId*/ Integer, /*petCount*/ Integer> counts = new HashMap<>();
        // 一个 appt 可能有多个 service, 一个 pet 有多个 service 只算一个 pet
        petDetails.stream()
                .collect(groupingBy(MoeGroomingPetDetail::getGroomingId))
                .forEach((apptId, services) -> {
                    int count = (int) services.stream()
                            // If the staff is not the business owner, filter pets that only belong to the staff
                            .filter(service -> isBusinessOwner || Objects.equals(service.getStaffId(), staffId))
                            .map(MoeGroomingPetDetail::getPetId)
                            .distinct()
                            .count();
                    counts.put(apptId, count);
                });
        return counts.values().stream().mapToInt(Integer::intValue).sum();
    }

    private static boolean inProgressAppointment(MoeGroomingAppointment appt) {
        return appt.getStatus() == 1 || /* unconfirmed */ appt.getStatus() == 2 /* confirmed */;
    }

    private static String getStartDate(@Nullable String start, MoeBusinessDto businessInfo) {
        if (!StringUtils.hasText(start)) {
            LocalDateTime now = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.of(businessInfo.getTimezoneName()));
            return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        if (start.contains(" ")) {
            // include time, e.g. 2021-11-22 11:10:40
            return start.split(" ")[0];
        }
        return start;
    }

    private static String getEndDate(@NotNull String time) {
        if (time.contains(" ")) {
            // include time, e.g. 2021-11-22 11:10:40
            return time.split(" ")[0];
        }
        return time;
    }

    private static int getStartMinutes(@Nullable String time, MoeBusinessDto businessInfo) {
        if (!StringUtils.hasText(time)) {
            LocalDateTime now = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.of(businessInfo.getTimezoneName()));
            return now.getHour() * 60 + now.getMinute();
        }
        if (time.contains(" ")) {
            // include time, e.g. 2021-11-22 11:10:40
            LocalDateTime dateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return dateTime.getHour() * 60 + dateTime.getMinute();
        }
        // only date, e.g. 2022-11-22
        return 0;
    }

    private static int getEndMinutes(@NotNull String time) {
        if (time.contains(" ")) {
            // include time, e.g. 2021-11-22 11:10:40
            LocalDateTime dateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return dateTime.getHour() * 60 + dateTime.getMinute();
        }
        // only date, e.g. 2022-11-22
        // NOTE: we need data that includes this day
        return 24 * 60;
    }

    /**
     * 根据 appointmentDates、startTimes 查找所选时间内 appointment 的 petId
     *
     * @param businessId
     * @param appointmentDates
     * @param startTimes
     * @return
     */
    public List<AppointmentPetIdDTO> queryStaffAppointmentPetIdByTime(
            Integer businessId, Set<String> appointmentDates, Set<Integer> startTimes) {
        return moeGroomingAppointmentMapper.queryStaffAppointmentPetIdByTime(businessId, appointmentDates, startTimes);
    }

    /**
     * Get summary within the date range.
     *
     * <p> If the staff is not the business owner, only return the summary of the staff.
     * <p> If the staff is the business owner, return the summary of all staffs (include himself).
     *
     * @param businessId businessId
     * @param staffId    staffId
     * @param startDate  startDate format: yyyy-MM-dd
     * @param endDate    endDate format: yyyy-MM-dd
     * @return {@link SummaryDto}
     */
    public SummaryDto getSummary(Integer businessId, Integer staffId, String startDate, String endDate) {
        boolean isBusinessOwner = Objects.equals(staffId, iBusinessStaffClient.getOwnerStaffId(businessId));

        SummaryDto result = new SummaryDto();
        result.setStaffSummary(getStaffOrBusinessSummary(businessId, staffId, startDate, endDate));

        if (!isBusinessOwner) {
            return result;
        }

        result.setBusinessSummary(getBusinessSummary(businessId, startDate, endDate));
        return result;
    }

    private SummaryDto.StaffSummary getStaffOrBusinessSummary(
            int businessId, Integer staffId, String startDate, String endDate) {
        SummaryDto.StaffSummary result = new SummaryDto.StaffSummary();

        Optional.ofNullable(moeGroomingAppointmentMapper.getServiceDuration(businessId, staffId, startDate, endDate))
                .map(duration -> {
                    SummaryDto.StaffSummary.Service service = new SummaryDto.StaffSummary.Service();
                    service.setTotalServiceTimeInMin(duration);
                    return service;
                })
                .ifPresent(result::setService);

        Optional.of(getTakeCaredPet(businessId, staffId, startDate, endDate))
                .map(po -> {
                    SummaryDto.StaffSummary.TakeCaredPet takeCaredPet = new SummaryDto.StaffSummary.TakeCaredPet();
                    takeCaredPet.setCount(po.getCount());
                    return takeCaredPet;
                })
                .ifPresent(result::setTakeCaredPet);

        Optional.ofNullable(moeGroomingAppointmentMapper.getLongestWorkingDay(businessId, staffId, startDate, endDate))
                .map(day -> {
                    SummaryDto.StaffSummary.LongestWorkingDay longestWorkingDay =
                            new SummaryDto.StaffSummary.LongestWorkingDay();
                    longestWorkingDay.setDate(day.getAppointmentDate());
                    longestWorkingDay.setMinutes(day.getLongestServiceTime());
                    return longestWorkingDay;
                })
                .ifPresent(result::setLongestWorkingDay);

        Optional.ofNullable(getMostPetBreed(businessId, staffId, startDate, endDate))
                .map(breed -> {
                    SummaryDto.StaffSummary.MostPetBreed mostPetBreed = new SummaryDto.StaffSummary.MostPetBreed();
                    mostPetBreed.setBreed(breed.getBreed());
                    mostPetBreed.setCount(breed.getCount());
                    return mostPetBreed;
                })
                .ifPresent(result::setMostPetBreed);

        return result;
    }

    private SummaryDto.BusinessSummary getBusinessSummary(int businessId, String startDate, String endDate) {
        SummaryDto.BusinessSummary result = new SummaryDto.BusinessSummary();

        InfoIdParams params = new InfoIdParams();
        params.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(params);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not found: " + businessId);
        }

        // MoeGo pay
        SummaryDto.BusinessSummary.MoegoPaySummary moegoPaySummary = new SummaryDto.BusinessSummary.MoegoPaySummary();
        if (Objects.equals(businessInfo.getPrimaryPayType(), PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE)) {
            moegoPaySummary.setEnabled(true);
            MoeGoPayTransactionSummaryDto moeGoPayTransactionSummary =
                    getMoeGoPayTransactionCount(businessId, startDate, endDate);
            moegoPaySummary.setTotalAmount(moeGoPayTransactionSummary.getTotalAmount());
            moegoPaySummary.setCount(moeGoPayTransactionSummary.getTotalCount());
        }
        result.setMoeGoPaySummary(moegoPaySummary);

        // Online booking
        MoeBusinessBookOnline businessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (businessBookOnline == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "business book online not found, business id: " + businessId);
        }
        SummaryDto.BusinessSummary.OnlineBookingSummary onlineBookingSummary =
                new SummaryDto.BusinessSummary.OnlineBookingSummary();
        if (Objects.equals(businessBookOnline.getIsEnable(), CustomerContactEnum.BUSINESS_IS_ENABLE)) {
            onlineBookingSummary.setEnabled(true);
            onlineBookingSummary.setCount(
                    moeGroomingAppointmentMapper.getOnlineBookingCount(businessId, startDate, endDate));
        }
        result.setOnlineBookingSummary(onlineBookingSummary);

        result.setStaffSummary(getStaffOrBusinessSummary(businessId, null, startDate, endDate));
        return result;
    }

    private MoeGoPayTransactionSummaryDto getMoeGoPayTransactionCount(
            Integer businessId, String startDate, String endDate) {
        InfoIdParams params = new InfoIdParams();
        params.setInfoId(businessId);
        MoeBusinessDto business = iBusinessBusinessClient.getBusinessInfo(params);
        if (business == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not found: " + businessId);
        }
        String tz = business.getTimezoneName();
        if (!StringUtils.hasText(tz)) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "business '" + businessId + "' has empty timezone");
        }
        long start = DateUtil.timestamp(startDate, tz) / 1000;
        long end = (DateUtil.timestamp(endDate, tz) + 60 * 60 * 24 * 1000) / 1000;
        return paymentPaymentClient.getMoeGoPayTransactionSummary(businessId, start, end);
    }

    private SummaryDto.StaffSummary.MostPetBreed getMostPetBreed(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        // 拿到 staff groom 过的宠物id
        List<Integer> petIds = moePetDetailService.getGroomedPetIds(businessId, staffId, startDate, endDate);

        Map</*breed*/ String, /*count*/ Long> breedCountMap = iPetClient.getCustomerPetListByIdList(petIds).stream()
                .filter(pet -> StringUtils.hasText(pet.getBreed()))
                .collect(groupingBy(CustomerPetDetailDTO::getBreed, counting()));

        return breedCountMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> {
                    SummaryDto.StaffSummary.MostPetBreed mostPetBreed = new SummaryDto.StaffSummary.MostPetBreed();
                    mostPetBreed.setBreed(entry.getKey());
                    mostPetBreed.setCount(Math.toIntExact(entry.getValue()));
                    return mostPetBreed;
                })
                .orElse(null);
    }

    private TakeCaredPetPo getTakeCaredPet(Integer businessId, Integer staffId, String startDate, String endDate) {
        TakeCaredPetPo po = new TakeCaredPetPo();

        List<Integer> apptIds =
                moeGroomingAppointmentMapper.getNotCanceledAppointmentIds(businessId, staffId, startDate, endDate);

        if (CollectionUtils.isEmpty(apptIds)) {
            po.setCount(0);
            return po;
        }

        List<GroomingPetDetailDTO> petDetails = moePetDetailService.queryPetDetailByGroomingId(apptIds);
        Map</*apptId*/ Integer, /*petCount*/ Integer> counts = new HashMap<>();
        // 一个 appt 可能有多个 service, 一个 pet 有多个 service 只算一个 pet
        petDetails.stream()
                .collect(groupingBy(GroomingPetDetailDTO::getGroomingId))
                .forEach((apptId, services) -> {
                    int count = (int) services.stream()
                            .map(GroomingPetDetailDTO::getPetId)
                            .distinct()
                            .count();
                    counts.put(apptId, count);
                });

        int sum = counts.values().stream().mapToInt(Integer::intValue).sum();

        po.setCount(sum);
        return po;
    }

    /**
     * 判断预约状态是否为 finish/cancelled
     *
     * @param apptStatus
     * @return
     */
    private boolean isApptFinishOrCancelled(Byte apptStatus) {
        return (Objects.equals(apptStatus, AppointmentStatusEnum.FINISHED.getValue())
                || Objects.equals(apptStatus, AppointmentStatusEnum.CANCELED.getValue()));
    }

    public void modifySpecialAppointment(Integer businessId, MoeGroomingAppointmentSpecialParam param) {
        var groomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(param.getId());
        if (groomingAppointment == null || !businessId.equals(groomingAppointment.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found appointment info");
        }
        MoeGroomingAppointment record = new MoeGroomingAppointment();
        record.setId(param.getId());
        record.setRepeatId(param.getRepeatId());
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(record);
    }

    private int removeAppointmentComment(int appointmentId, int staffId, long petId) {
        MoeGroomingNoteExample example = new MoeGroomingNoteExample();
        MoeGroomingNoteExample.Criteria criteria = example.createCriteria();
        criteria.andGroomingIdEqualTo(appointmentId)
                .andPetIdEqualTo(petId)
                .andTypeEqualTo(GroomingAppointmentEnum.NOTE_COMMENT)
                .andIsDeletedEqualTo(false);

        MoeGroomingNote updateBean = new MoeGroomingNote();
        updateBean.setIsDeleted(true);
        updateBean.setUpdateBy(staffId);
        updateBean.setUpdateTime(CommonUtil.get10Timestamp());
        return groomingNoteMapper.updateByExampleSelective(updateBean, example);
    }

    public UpdateStatusResultVO updateAppointmentStatus(
            Integer businessId, Integer staffId, StatusUpdateParams params) {
        Integer groomingId = params.getGroomingId();
        AppointmentStatusEnum newStatus = params.getStatus();
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (appointment == null || !businessId.equals(appointment.getBusinessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not found appointment info");
        }
        String tz =
                waitListService.getBusinessTimeZone(appointment.getBusinessId().longValue());

        IStateTransitionAction action = stateTransitionActions.stream()
                .filter(a -> a.suit(newStatus))
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status"));
        int affectRows;
        String failedReason = null;
        switch (newStatus) {
            case UNCONFIRMED, CHECK_IN -> {
                // UnConfirm 状态和 Check-in 状态无需额外操作，直接扭转即可
                affectRows = action.execute(appointment, null);
            }
            case CONFIRMED -> {
                // Confirm 状态需要记录操作人信息
                ActionContext context = new ConfirmActionContext(staffId, AppointmentConfirmTypeEnum.BUSINESS);
                affectRows = action.execute(appointment, context);
            }
            case READY -> {
                // Ready 需要根据用户选择去发送通知
                affectRows = 0;
                if (!AppointmentStatusEnum.READY.getValue().equals(appointment.getStatus())
                        && !AppointmentStatusEnum.FINISHED.getValue().equals(appointment.getStatus())) {
                    affectRows = action.execute(appointment, null);
                    if (affectRows > 0) {
                        asyncService.asyncNotificationAppClientReady(appointment.getId());
                    }
                }
                if (params.getMessageMethodForPickupNotification() != null
                        && !MessageMethodTypeEnum.MESSAGE_METHOD_UNKNOWN.equals(
                                params.getMessageMethodForPickupNotification())) {
                    failedReason = messageSendClient.sendReadyForPickupMessageV2(
                            businessId,
                            staffId,
                            appointment.getCustomerId(),
                            groomingId,
                            params.getMessageMethodForPickupNotification());
                    updateStatusForPickupNotification(groomingId, failedReason);
                    ActivityLogRecorder.record(
                            AppointmentAction.SEND_NOTIFICATION,
                            ResourceType.APPOINTMENT,
                            appointment.getId(),
                            new SendNotificationLogDTO(
                                    params.getMessageMethodForPickupNotification(),
                                    Strings.isEmpty(failedReason),
                                    failedReason,
                                    NotificationTypeEnum.PICKUP_REMINDER));
                }
            }
            case FINISHED -> {
                // Finish 需要去确认 review booster
                if (hasRelateWaitList(appointment)) {
                    waitListService.batchDeleteRelatedWaitList(
                            appointment.getCompanyId(), businessId.longValue(), tz, List.of(appointment.getId()));
                }

                adjustBoardingAppointmentByActualCheckoutDate(appointment, params);

                affectRows = action.execute(appointment, null);
                if (affectRows > 0) {
                    autoReviewBoosterCheck(moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId));
                    asyncService.asyncNotificationAppClientFinished(appointment.getId());
                    // DONE new order flow 旧订单关单判断为 fully paid + appointment finished，新订单关单改为 fully paid
                    // 履约完成后，同步到 order fulfillment status
                    if (!newOrderHelper.isNewOrder(appointment.getId())) {
                        boolean updated = orderService.updateOrderModel(OrderModel.newBuilder()
                                .setId(orderService
                                        .getOrderByGroomingId(businessId, groomingId)
                                        .getId())
                                .setBusinessId(businessId)
                                .setFulfillmentStatus(OrderModel.FulfillmentStatus.COMPLETED.name())
                                .build());
                        log.info(
                                "Update order fulfillment status to COMPLETED, businessId: {}, groomingId: {}, updated: {}",
                                businessId,
                                groomingId,
                                updated);
                    }
                }
                completedAppointmentTasks(appointment);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status");
        }
        if (ArrivedStatus.contains(newStatus)) {
            appointmentTrackingServiceBlockingStub.upsertAppointmentTracking(
                    UpsertAppointmentTrackingRequest.newBuilder()
                            .setAppointmentId(groomingId)
                            .setAppointmentTracking(UpdateAppointmentTrackingDef.newBuilder()
                                    .setStaffLocationStatus(StaffLocationStatus.ARRIVED)
                                    .build())
                            .build());
        }
        return new UpdateStatusResultVO(affectRows > 0, Strings.isEmpty(failedReason), failedReason);
    }

    private void adjustBoardingAppointmentByActualCheckoutDate(
            MoeGroomingAppointment appointment, StatusUpdateParams statusUpdateParams) {

        // 根据 '实际的 check out date' 修改 boarding appointment 的 services/addons
        // 如果 '实际的 check out date' < end date：需要将对应 services/addons 减少
        // 如果 '实际的 check out date' > end date：需要将对应 services/addons 增加

        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(appointment.getServiceTypeInclude());
        if (mainServiceItemType != ServiceItemEnum.BOARDING) {
            return;
        }

        var actualCheckOutDate = Optional.ofNullable(statusUpdateParams.getCheckOut())
                .map(StatusUpdateParams.CheckOut::getEndDate)
                .map(LocalDate::toString)
                .orElse(null);
        if (actualCheckOutDate == null) {
            return;
        }

        appointmentStub.rescheduleBoardingAppointment(RescheduleBoardingAppointmentRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setAppointmentId(appointment.getId())
                .setEndDate(actualCheckOutDate)
                .build());
    }

    public void completedAppointmentTasks(MoeGroomingAppointment appointment) {
        boolean isBDAppt = ServiceItemEnum.BOARDING.isIncludedIn(appointment.getServiceTypeInclude())
                || ServiceItemEnum.DAYCARE.isIncludedIn(appointment.getServiceTypeInclude());
        if (!isBDAppt) {
            return;
        }
        ThreadPool.execute(() -> appointmentTaskStub.patchTasksByAppointment(PatchTasksByAppointmentRequest.newBuilder()
                .setAppointmentId(appointment.getId())
                .setStatus(AppointmentTaskStatus.COMPLETED)
                .build()));
    }

    public Boolean revertAppointmentStatus(Integer businessId, StatusRevertParams params) {
        Integer groomingId = params.getGroomingId();
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (appointment == null || !businessId.equals(appointment.getBusinessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not found appointment info");
        }

        AppointmentStatusEnum currentStatus;
        /*
         * 兼容旧数据，会存在 confirmed/unconfirmed with checkin 状态
         */
        if (!Objects.isNull(appointment.getCheckInTime())
                && appointment.getCheckInTime() > 0
                && (AppointmentStatusEnum.UNCONFIRMED.getValue().equals(appointment.getStatus())
                        || AppointmentStatusEnum.CONFIRMED.getValue().equals(appointment.getStatus()))) {
            currentStatus = AppointmentStatusEnum.CHECK_IN;
        } else {
            currentStatus = AppointmentStatusEnum.values()[appointment.getStatus()];
        }

        IStateTransitionAction action = stateTransitionActions.stream()
                .filter(a -> a.suit(currentStatus))
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status"));

        switch (currentStatus) {
            case CHECK_IN, READY -> {
                return action.revert(appointment) > 0;
            }
            case FINISHED -> {
                // 获取invoice并检查
                MoeGroomingInvoice moeGroomingInvoice = invoiceService.queryInvoiceByGroomingId(businessId, groomingId);
                if (moeGroomingInvoice == null) {
                    throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
                }
                if (bwListManager.isInWhiteList(BWListManager.ORDER_REINVENT, businessId.toString())
                        && InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(moeGroomingInvoice.getStatus())) {
                    // 已经完成的订单不允许更新
                    throw ExceptionUtil.bizException(
                            Code.CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE, "Invoice is finished.");
                }

                int rows = action.revert(appointment);
                if (rows > 0) {
                    autoReviewBoosterCheck(moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId));
                }

                // 修改invoice status为正在进行
                orderService.updateOrderStatus(moeGroomingInvoice.getId(), InvoiceStatusEnum.INVOICE_STATUS_PROCESSING);
                // reopen时失效掉tip split记录
                splitTipsService.invalidTipSplitRecord(businessId, moeGroomingInvoice.getId());
                return rows > 0;
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status");
        }
    }

    public void updateStatusForPickupNotification(Integer groomingId, String failedReason) {
        MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
        appointmentToUpdate.setId(groomingId);
        appointmentToUpdate.setPickupNotificationSendStatus(
                Strings.isEmpty(failedReason)
                        ? PickupNotificationStatusEnum.SUCCESS.getValue()
                        : PickupNotificationStatusEnum.FAILED.getValue());
        appointmentToUpdate.setPickupNotificationFailedReason(failedReason);
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
    }

    private void confirmAppointmentByClient(Integer groomingId, MessageMethodTypeEnum messageMethodTypeEnum) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (appointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not found appointment info");
        }

        boolean isUpdated = false;
        switch (AppointmentStatusEnum.values()[appointment.getStatus()]) {
            case UNCONFIRMED -> {
                IStateTransitionAction action = stateTransitionActions.stream()
                        .filter(a -> a.suit(AppointmentStatusEnum.CONFIRMED))
                        .findFirst()
                        .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status"));
                ActionContext context = new ConfirmActionContext(null, AppointmentConfirmTypeEnum.CUSTOMER);
                action.execute(appointment, context);
                isUpdated = true;
            }
            case CONFIRMED -> {
                // already confirmed, do nothing
            }
            case CHECK_IN -> {
                MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
                updateBean.setStatusBeforeCheckin(AppointmentStatusEnum.CONFIRMED);
                updateBean.setUpdateTime(DateUtil.get10Timestamp());

                MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
                MoeGroomingAppointmentExample.Criteria criteria = example.createCriteria();
                criteria.andIdEqualTo(groomingId);
                criteria.andStatusEqualTo(AppointmentStatusEnum.CHECK_IN.getValue());
                criteria.andStatusBeforeCheckinEqualTo(AppointmentStatusEnum.UNCONFIRMED);

                moeGroomingAppointmentMapper.updateByExampleSelective(updateBean, example);
            }
            case READY -> {
                MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
                updateBean.setStatusBeforeReady(AppointmentStatusEnum.CONFIRMED);
                updateBean.setUpdateTime(DateUtil.get10Timestamp());

                MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
                MoeGroomingAppointmentExample.Criteria criteria = example.createCriteria();
                criteria.andIdEqualTo(groomingId);
                criteria.andStatusEqualTo(AppointmentStatusEnum.READY.getValue());
                criteria.andStatusBeforeReadyEqualTo(AppointmentStatusEnum.UNCONFIRMED);

                moeGroomingAppointmentMapper.updateByExampleSelective(updateBean, example);
            }
            case FINISHED -> {
                MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
                updateBean.setStatusBeforeFinish(AppointmentStatusEnum.CONFIRMED);
                updateBean.setUpdateTime(DateUtil.get10Timestamp());

                MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
                MoeGroomingAppointmentExample.Criteria criteria = example.createCriteria();
                criteria.andIdEqualTo(groomingId);
                criteria.andStatusEqualTo(AppointmentStatusEnum.FINISHED.getValue());
                criteria.andStatusBeforeFinishEqualTo(AppointmentStatusEnum.UNCONFIRMED);

                moeGroomingAppointmentMapper.updateByExampleSelective(updateBean, example);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not support status");
        }

        ActivityLogRecorder.record(
                appointment.getBusinessId(),
                appointment.getCustomerId(),
                AppointmentAction.CUSTOMER_REPLY,
                ResourceType.APPOINTMENT,
                appointment.getId(),
                new CustomerReplyLogDTO(isUpdated, messageMethodTypeEnum, ClientReplyTypeEnum.CONFIRM_APPOINTMENT));

        ThreadPool.execute(() -> {
            log.info("appt confirm by client notify");
            // 调用通知发送
            List<MoeGroomingPetDetail> petDetailList =
                    moeGroomingPetDetailMapper.queryPetDetailCountByGroomingId(appointment.getId());
            NotificationApptConfirmedByClientParams apptConfirmedByClientParams =
                    new NotificationApptConfirmedByClientParams();
            apptConfirmedByClientParams.setBusinessId(appointment.getBusinessId());
            // 获取发送的staffIdList
            Set<Integer> staffIdList =
                    petDetailList.stream().map(MoeGroomingPetDetail::getStaffId).collect(toSet());
            staffIdList.addAll(groomingServiceOperationService.queryStaffIdByGroomingId(appointment.getId()));
            apptConfirmedByClientParams.setStaffIdList(staffIdList);
            // 给前端的数据体
            NotificationExtraApptCommonDto webPushDto = new NotificationExtraApptCommonDto();
            webPushDto.setAppointmentDate(appointment.getAppointmentDate());
            webPushDto.setAppointmentStartTime(appointment.getAppointmentStartTime());
            webPushDto.setNoStartTime(appointment.getNoStartTime());
            webPushDto.setAppointmentEndTime(appointment.getAppointmentEndTime());
            webPushDto.setCustomerId(appointment.getCustomerId());
            webPushDto.setGroomingId(appointment.getId());
            // customer firstName 和 lastName 在notification模块组装
            apptConfirmedByClientParams.setWebPushDto(webPushDto);
            iNotificationClient.sendApptConfirmedByClient(apptConfirmedByClientParams);
        });
    }

    private int getAbandonRecordCount(Integer businessId) {
        return abandonRecordMapper.countRecoverableAbandonedClients(
                businessId,
                DateUtil.get10Timestamp() - 30 * 24 * 60 * 60L,
                DateUtil.get10Timestamp() - 60 * 60L,
                OBStepEnum.listRecoverableSteps());
    }

    /**
     * 针对 as 迁移后的用户，在 Appointment 相关的权限检查上，需要从 business level 放开至 company level
     *
     * @param companyId:                 company id from token
     * @param tokenStaffId:              staff id from token
     * @param appointmentId:             appointment id to be checked
     * @param appointmentPermissionEnums {@link AppointmentPermissionEnums}: permission to be checked,
     * @return: 为了保持兼容性，这个方法会返回 Appointment 所属的 business
     */
    public Integer checkPermissionToAppointment(
            Long companyId,
            Long tokenStaffId,
            Long appointmentId,
            AppointmentPermissionEnums appointmentPermissionEnums) {
        if (appointmentId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment id is null");
        }
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(appointmentId.intValue());
        if (appointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not found");
        }

        PermissionEnums permissionEnums =
                switch (appointmentPermissionEnums) {
                    case CREATE -> PermissionEnums.CREATE_APPOINTMENT;
                    case UPDATE -> PermissionEnums.EDIT_APPOINTMENT;
                    case CANCEL -> PermissionEnums.CANCEL_APPOINTMENT;
                    case CREATE_AND_EDIT_COMMENT -> PermissionEnums.EDIT_TICKET_COMMENT_AND_GROOMING_REPORT;
                    case CREATE_AND_EDIT_BLOCK -> PermissionEnums.CREATE_AND_EDIT_BLOCK;
                };

        permissionHelper.checkPermission(
                companyId, Set.of(appointment.getBusinessId().longValue()), tokenStaffId, permissionEnums);
        return appointment.getBusinessId();
    }

    public Boolean adminDeleteCanceledAppointment(Long appointmentId) {
        // set is deprecate
        var updateBean = new MoeGroomingAppointment();
        updateBean.setIsDeprecate(BooleanEnum.VALUE_TRUE.intValue());
        updateBean.setUpdateTime(DateUtil.get10Timestamp());
        // where
        var example = new MoeGroomingAppointmentExample();
        var exampleCriteria = example.createCriteria();
        exampleCriteria.andIdEqualTo(appointmentId.intValue());
        exampleCriteria.andStatusEqualTo(AppointmentStatusEnum.CANCELED.getValue());
        return moeGroomingAppointmentMapper.updateByExampleSelective(updateBean, example) > 0;
    }

    public List<MoeGroomingAppointment> getAppointmentByIdsAndDateRange(
            Integer businessId, List<Integer> appointmentIds, String startDate, String endDate) {
        return moeGroomingAppointmentMapper.getAppointmentByIdsAndDateRange(
                businessId, appointmentIds, startDate, endDate);
    }
}
