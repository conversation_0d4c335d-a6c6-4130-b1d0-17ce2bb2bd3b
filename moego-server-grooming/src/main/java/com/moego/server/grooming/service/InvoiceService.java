package com.moego.server.grooming.service;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

import com.google.protobuf.Timestamp;
import com.moego.common.distributed.BWListManager;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.dto.CustomerPaymentSummary;
import com.moego.common.dto.GuidDto;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentSettingConst;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.security.Des3Util;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PermissionUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailModel;
import com.moego.idl.models.membership.v1.MembershipUsageView;
import com.moego.idl.models.membership.v1.SourceType;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GetFulfillmentRequest;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.ListGroupClassDetailsRequest;
import com.moego.idl.service.fulfillment.v1.StaffTimeSlotServiceGrpc;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageRequest;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.RedeemMembershipRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListInstancesRequest;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitResponse;
import com.moego.idl.service.order.v2.OrderServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.convert.PetDetailConverter;
import com.moego.server.grooming.convert.TimestampConverter;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.dto.GroomingPackageHistoryDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.IdAndCreateTimeDTO;
import com.moego.server.grooming.dto.InvoicePayOnlineDTO;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.dto.LodgingInfo;
import com.moego.server.grooming.dto.OrderInvoiceSummaryDTO;
import com.moego.server.grooming.dto.OrderItemPetDetailDTO;
import com.moego.server.grooming.dto.OrderItemPetEvaluationDetailDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.helper.CustomerHelper;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingInvoiceApplyPackageMapper;
import com.moego.server.grooming.mapper.MoeGroomingPackageHistoryMapper;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceApplyPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.EditIdParams;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.params.PackageUsedParams;
import com.moego.server.grooming.params.SetPaymentParams;
import com.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto;
import com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import com.moego.server.grooming.utils.PetDetailUtil;
import com.moego.server.grooming.web.vo.ApplyPackageServiceVo;
import com.moego.server.grooming.web.vo.DeleteApplyPackageServiceVo;
import com.moego.server.message.client.IMessageDetailClient;
import com.moego.server.message.client.IMessageSendClient;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.params.MoeMessageTargetQueryParams;
import com.moego.server.message.params.SendServiceMessageParams;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.PaymentSettingForClientDTO;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.dto.SmartTipConfigDTO;
import com.moego.server.payment.dto.SmartTipConfigForClientDTO;
import com.moego.server.payment.params.GetPaymentParams;
import com.moego.server.payment.params.UpdateInvoiceAndCaptureParams;
import jakarta.annotation.Nonnull;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class InvoiceService {

    @Autowired
    private DepositService depositService;

    @Autowired
    MoeGroomingInvoiceApplyPackageMapper invoiceApplyPackageMapper;

    @Autowired
    MoeGroomingAppointmentService appointmentService;

    @Autowired
    private FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentStub;

    @Autowired
    private GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailStub;

    @Autowired
    private StaffTimeSlotServiceGrpc.StaffTimeSlotServiceBlockingStub staffTimeSlotStub;

    @Autowired
    private GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassStub;

    @Autowired
    private MoeGroomingOnlineFeeInvoiceService onlineFeeInvoiceService;

    @Autowired
    QuickBooksService quickBooksService;

    @Autowired
    MoePackageService moePackageService;

    @Autowired
    MoePetDetailService petDetailService;

    @Autowired
    AppointmentServiceDetailService appointmentServiceDetailService;

    @Autowired
    OrderService orderService;

    @Autowired
    IPaymentPaymentClient iPaymentService;

    @Autowired
    IPaymentCreditCardClient iPaymentCreditCardClient;

    @Autowired
    private IPaymentSettingClient iPaymentSettingClient;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MoeBookOnlineDepositService bookOnlineDepositService;

    @Value("${customer.invoice.url}")
    private String invoiceUrl;

    @Value("${customer.upcoming.key}")
    private String upcomingKey;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private MoeGroomingPackageHistoryMapper moeGroomingPackageHistoryMapper;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private IPaymentPreAuthClient paymentPreAuthClient;

    @Autowired
    private IMessageDetailClient iMessageDetailClient;

    @Autowired
    private SplitTipsService splitTipsService;

    @Autowired
    private IMessageSendClient messageSendClient;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Autowired
    private BWListManager bwListManager;

    @Autowired
    private LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitService;

    @Autowired
    private PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyService;

    @Autowired
    private MembershipServiceGrpc.MembershipServiceBlockingStub membershipServiceBlockingStub;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderV2ServiceBlockingStub;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private CustomerHelper customerHelper;

    @Autowired
    private BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Autowired
    private NewOrderHelper newOrderHelper;

    @Autowired
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    public InvoiceSummaryDTO getById(Integer invoiceId, Integer tokenStaffId) {
        MoeGroomingInvoice groomingInvoice = orderService.getOrderWithItemsById(invoiceId);
        InvoiceSummaryDTO invoice = orderService.convertToSummaryDTO(groomingInvoice);
        if (tokenStaffId != null) {
            StaffPermissions businessRoleByStaffId = iBusinessStaffClient.getBusinessRoleByStaffId(tokenStaffId);
            boolean isHideEmail =
                    !PermissionUtil.checkStaffPermissionsInfo(businessRoleByStaffId, PermissionUtil.VIEW_CLIENT_EMAIL);
            if (isHideEmail) {
                invoice.setCustomerEmail(PermissionUtil.emailConfusion(invoice.getCustomerEmail()));
            }
        }

        // 非 appointment/noshow 类型的 invoice 不需要查 appointment 信息
        if (!Objects.equals(invoice.getType(), OrderSourceType.APPOINTMENT.getSource())
                && !Objects.equals(invoice.getType(), OrderSourceType.NO_SHOW.getSource())) {
            return invoice;
        }

        MoeGroomingAppointment appointment = getAppointmentByGroomingId(invoice.getGroomingId());
        invoice.setAppointmentStatus(appointment.getStatus());
        invoice.setAppointmentCheckInTime(appointment.getCheckInTime());
        invoice.setAppointmentCheckOutTime(appointment.getCheckOutTime());
        invoice.setAppointmentDate(appointment.getAppointmentDate());
        invoice.setAppointmentStartTime(appointment.getAppointmentStartTime());

        List<GroomingPetDetailDTO> groomingPetDetails =
                petDetailService.queryPetDetailByGroomingId(Collections.singletonList(invoice.getGroomingId()));
        List<CustomerPetDetailDTO> pets = iPetClient.getCustomerPetListByIdList(
                groomingPetDetails.stream().map(GroomingPetDetailDTO::getPetId).collect(Collectors.toList()));

        // 获取staffMap
        Map<Integer, MoeStaffDto> staffMap = getStaffMap(groomingInvoice.getBusinessId(), groomingPetDetails);
        Map<Integer, CustomerPetDetailDTO> petMap =
                pets.stream().collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, p -> p));

        invoice.getItems().forEach(invoiceItem -> {
            List<InvoiceSummaryDTO.PetDetailDTO> petDetails = new ArrayList<>();
            if (OrderItemType.ITEM_TYPE_SERVICE.getType().equals(invoiceItem.getType())) {
                petDetails = groomingPetDetails.stream()
                        .filter(petDetail -> petDetail.getServiceId().equals(invoiceItem.getServiceId())
                                && petDetail.getServicePrice().compareTo(invoiceItem.getServiceUnitPrice()) == 0)
                        .filter(petDetail -> petMap.containsKey(petDetail.getPetId()))
                        .map(gt -> {
                            CustomerPetDetailDTO pet = petMap.get(gt.getPetId());
                            InvoiceSummaryDTO.PetDetailDTO dto = new InvoiceSummaryDTO.PetDetailDTO();
                            dto.setPetId(gt.getPetId());
                            dto.setPetName(pet.getPetName());
                            dto.setPetBreed(pet.getPetBreed());
                            dto.setServiceId(gt.getServiceId());
                            dto.setServiceTime(gt.getServiceTime());
                            dto.setServicePrice(gt.getServicePrice());
                            dto.setStartTime(gt.getStartTime());
                            if (staffMap.containsKey(gt.getStaffId())) {
                                MoeStaffDto staff = staffMap.get(gt.getStaffId());
                                dto.setStaffFirstName(staff.getFirstName());
                                dto.setStaffLastName(staff.getLastName());
                            }
                            return dto;
                        })
                        .collect(Collectors.toList());
            }
            // 兼容旧客户端，需要有petId才会展示这个item
            if (CollectionUtils.isEmpty(petDetails)) {
                InvoiceSummaryDTO.PetDetailDTO dto = new InvoiceSummaryDTO.PetDetailDTO();
                dto.setPetId(invoiceItem.getId());
                petDetails.add(dto);
            }
            invoiceItem.setPetDetails(petDetails);
        });

        return invoice;
    }

    public InvoiceSummaryDTO setTips(InvoiceAmountVo param) {
        return setTips(null, null, param);
    }

    /**
     * 设置 tip
     * - value type 为空时， 默认按照 amount 类型处理
     *
     * @param businessId
     * @param param
     * @return
     */
    public InvoiceSummaryDTO setTips(Integer businessId, Integer operatorId, InvoiceAmountVo param) {
        orderService.setTips(businessId, operatorId, param);
        return param.isOmitResult() ? null : getById(param.getInvoiceId(), null);
    }

    public InvoiceSummaryDTO setDiscount(Integer businessId, Integer operatorId, InvoiceAmountVo param) {
        //        MoeGroomingInvoice invoice = orderService.getOrderWithItemsById(businessId, param.getInvoiceId());
        //
        //        if (InvoiceValueType.AMOUNT.check(param.getValueType())) {
        //            invoice.setDiscountAmount(param.getValue());
        //        } else if (InvoiceValueType.PERCENTAGE.check(param.getValueType())) {
        //            invoice.setDiscountRate(param.getValue());
        //        } else {
        //            throw new CommonException(ResponseCodeEnum.INVALID_VALUE_TYPE);
        //        }
        //
        //        invoice.setDiscountType(param.getValueType());
        //        invoice.calculate();
        //        saveInvoiceAndInvoiceItem(invoice);
        orderService.setDiscount(businessId, operatorId, param);
        return getById(param.getInvoiceId(), null);
    }

    public InvoiceSummaryDTO placeOrder(Integer invoiceId) {
        MoeGroomingInvoice invoice = orderService.getOrderWithItemsById(invoiceId);
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
            throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS, "invoice has been paid");
        }
        if (InvoiceStatusEnum.INVOICE_STATUS_PROCESSING.equals(invoice.getStatus())) {
            return orderService.convertToSummaryDTO(invoice);
        }

        // 替换为order
        orderService.updateOrderStatus(invoiceId, InvoiceStatusEnum.INVOICE_STATUS_PROCESSING);
        return orderService.convertToSummaryDTO(invoice);
    }

    public boolean usePackageForGrooming(Integer invoiceId) {
        MoeGroomingInvoice invoice = orderService.getOrderWithItemsById(invoiceId);
        usePackageForGroomingWithLock(invoice);
        return true;
    }

    public MoeGroomingInvoice setCompletedWithoutSummary(Integer businessId, Integer invoiceId) {
        OrderModel order = orderService.getOrderModelById(businessId, invoiceId);
        MoeGroomingInvoice invoice = orderService.convertToInvoice(order);
        if (invoice.getRemainAmount().compareTo(BigDecimal.ZERO) != 0) {
            log.error("set completed invalid invoice status: {}", JsonUtil.toJson(invoice));
            throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS);
        }
        boolean notExtraOrder = !OrderModel.OrderType.EXTRA.equals(order.getOrderType());
        boolean notInWhiteList = !bwListManager.isInWhiteList(BWListManager.ORDER_REINVENT, businessId.toString());
        usePackageForGroomingWithLock(invoice);

        // membership info for redeem when mark as completed
        ThreadPool.execute(() -> {
            long companyId = companyHelper.mustGetCompanyId(businessId);
            redeemMembershipBenefits(
                    companyId, businessId.longValue(), order.getId(), order.getSourceType(), order.getSourceId());
        });

        MoeGroomingAppointment appointment = getAppointmentByGroomingId(invoice.getGroomingId());
        if (notExtraOrder && !AppointmentStatusEnum.CANCELED.getValue().equals(appointment.getStatus())) {
            EditIdParams editIdParams = new EditIdParams();
            editIdParams.setId(invoice.getGroomingId());
            editIdParams.setBusinessId(invoice.getBusinessId());
            editIdParams.setIsPaid(GroomingAppointmentEnum.PAID);
            appointmentService.editAppointmentPaid(editIdParams);
            if (notInWhiteList) {
                // 非开白用户才自动finish appointment
                if (!enableFullPaymentNoStatusChange(appointment.getCompanyId())) {
                    appointmentService.editAppointmentFinish(editIdParams);
                }
            }
        }

        // 上面先更新履约完成，再根据是否开白来update invoice status
        if (notInWhiteList) {
            // complete in order service automatically based on fulfilment status and payment status
            orderService.updateOrderStatus(invoiceId, InvoiceStatusEnum.INVOICE_STATUS_COMPLETED);
        }

        return invoice;
    }

    private boolean enableFullPaymentNoStatusChange(Long companyId) {
        return featureFlagApi.isOn(
                FeatureFlags.FULL_PAYMENT_NO_STATUS_CHANGE,
                FeatureFlagContext.builder().company(companyId).build());
    }

    private void usePackageForGroomingWithLock(MoeGroomingInvoice invoice) {
        String resourceKey = lockManager.getResourceKey(LockManager.GROOMING_PACKAGE_HISTORY, invoice.getId());
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lockWithRetry(resourceKey, value)) {
                this.usePackageForGrooming(invoice);
            } else {
                log.error("get lock failed for " + resourceKey);
            }
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    /**
     * apply package to history
     * It is possible to insert history concurrently
     *
     * @param invoice invoice detail
     */
    public void usePackageForGrooming(MoeGroomingInvoice invoice) {
        List<MoeGroomingInvoiceApplyPackage> applyPackages =
                invoiceApplyPackageMapper.selectByInvoiceId(invoice.getId());
        if (applyPackages == null || applyPackages.isEmpty()) {
            return;
        }

        // moe_grooming_package_service 和 moe_grooming_package_history 这两张表很神奇，强烈建议修改，此处 @PC @Ritchie
        // 这两张表没有 business_id，并且两张表没有任何字段关联，导致难以定位 history 记录的是哪个 package_service。
        // 此处的 map 结构采用 package_id + service_id 的方式强行关联两张表的数据
        Map<Pair<Integer, Integer>, Pair<Integer, Integer>> deductMap = new HashMap<>();
        for (MoeGroomingInvoiceApplyPackage applyPackage : applyPackages) {
            deductMap.compute(Pair.of(applyPackage.getPackageId(), applyPackage.getServiceId()), (k, v) -> {
                if (v == null) {
                    return Pair.of(applyPackage.getPackageServiceId(), applyPackage.getQuantity());
                } else {
                    return Pair.of(applyPackage.getPackageServiceId(), v.getSecond() + applyPackage.getQuantity());
                }
            });
        }
        // 减去已经存在的 history 记录中扣除的部分
        List<GroomingPackageHistoryDTO> histories =
                moeGroomingPackageHistoryMapper.queryPackageHistoryByInvoiceId(invoice.getId());
        for (GroomingPackageHistoryDTO history : histories) {
            deductMap.computeIfPresent(
                    Pair.of(history.getPackageId(), history.getServiceId()),
                    (k, v) -> Pair.of(v.getFirst(), v.getSecond() - history.getQuantity()));
        }

        // 构造 package used 列表
        List<PackageUsedParams> packageUsedParamsList = new ArrayList<>();
        for (Map.Entry<Pair<Integer, Integer>, Pair<Integer, Integer>> entry : deductMap.entrySet()) {
            Integer usedQuantity = entry.getValue().getSecond();
            if (0 < usedQuantity) {
                PackageUsedParams params = new PackageUsedParams();
                params.setCustomerId(invoice.getCustomerId());
                params.setBusinessId(invoice.getBusinessId());
                params.setInvoiceId(invoice.getId());
                params.setGroomingId(invoice.getGroomingId());
                params.setPackageId(entry.getKey().getFirst());
                params.setServiceId(entry.getKey().getSecond());
                params.setPackageServiceId(entry.getValue().getFirst());
                params.setQuantity(usedQuantity);
                packageUsedParamsList.add(params);
            }
        }

        moePackageService.usePackageForGrooming(packageUsedParamsList);
    }

    public InvoiceSummaryDTO setCompleted(Long companyId, Integer businessId, Integer invoiceId) {
        MoeGroomingInvoice invoice = setCompletedWithoutSummary(businessId, invoiceId);
        ThreadPool.execute(() -> {
            String result = messageSendClient.sendAutoMessage(SendServiceMessageParams.builder()
                    .companyId(companyId)
                    .businessId(invoice.getBusinessId())
                    .customerId(invoice.getCustomerId())
                    .targetTypeEnums(MessageTargetTypeEnums.AUTO_RECEIPT)
                    .autoMessageTemplate(AutoMessageTemplateEnum.RECEIPT)
                    .groomingId(invoice.getGroomingId())
                    .targetId(invoice.getId())
                    .build());
            log.info("auto send message result: {}, param: {}", result, invoice.getId());
        });
        return orderService.convertToSummaryDTO(invoice);
    }

    // DONE(Frank): 检查应该放在入口处，而不是函数已执行完。状态已被修改再报错，也不会回滚了
    public InvoiceSummaryDTO confirmPayment(Long tokenCompanyId, Integer invoiceId) {
        OrderDetailModel orderDetail = orderService.getOrderDetail(null, invoiceId, null);
        MoeGroomingInvoice invoice = orderService.convertToInvoice(orderDetail);

        GetPaymentParams param = new GetPaymentParams();
        if (Objects.equals(
                orderDetail.getOrder().getSourceType(),
                com.moego.idl.models.order.v1.OrderSourceType.FULFILLMENT.name().toLowerCase())) {
            param.setModule(PaymentMethodEnum.MODULE_FULFILLMENT);
        } else {
            param.setModule(PaymentMethodEnum.MODULE_GROOMING);
        }
        param.setInvoiceId(invoiceId);
        PaymentSummary paymentSummary = iPaymentService.getPayments(param).getData();

        if (paymentSummary == null) {
            return orderService.convertToSummaryDTO(invoice);
        }

        SetPaymentParams params = new SetPaymentParams();
        BeanUtils.copyProperties(paymentSummary, params);

        // set isDeposit(获取最新一条 payment 记录的 isDeposit)
        Byte isDeposit = 0;
        long latestTime = 0;
        if (!CollectionUtils.isEmpty(paymentSummary.getPayments())) {
            for (PaymentSummary.PaymentDto payment : paymentSummary.getPayments()) {
                if (payment.getUpdateTime() > latestTime) {
                    latestTime = payment.getUpdateTime();
                    isDeposit = payment.getIsDeposit();
                }
            }
        }
        params.setIsDeposit(PaymentMethodEnum.TYPE_DEFAULT_TRUE.equals(isDeposit));

        if (paymentSummary.getRefunds() != null) {
            params.setRefunds(paymentSummary.getRefunds().stream()
                    .map(r -> {
                        SetPaymentParams.RefundDTO dto = new SetPaymentParams.RefundDTO();
                        BeanUtils.copyProperties(r, dto);
                        return dto;
                    })
                    .collect(Collectors.toList()));
        }

        setPaymentResultStatus(tokenCompanyId, invoice, params);

        // membership info for redeem when fully pay
        OrderModel order = orderDetail.getOrder();
        ThreadPool.execute(() -> redeemMembershipBenefits(
                tokenCompanyId, order.getBusinessId(), order.getId(), order.getSourceType(), order.getSourceId()));

        return orderService.convertToSummaryDTO(invoice);
    }

    private void redeemMembershipBenefits(
            Long companyId, Long businessId, Long orderId, String orderSourceType, Long orderSourceId) {
        boolean noMembershipUsage = membershipServiceBlockingStub
                        .getRecommendBenefitUsage(GetRecommendBenefitUsageRequest.newBuilder()
                                .setOrderId(orderId)
                                .build())
                        .getUsageViewsCount()
                <= 0;
        if (noMembershipUsage) {
            return;
        }

        RedeemMembershipRequest.Builder builder = RedeemMembershipRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setOrderId(orderId);
        if (Objects.equals(InvoiceStatusEnum.TYPE_APPOINTMENT, orderSourceType)) {
            builder.setSourceId(orderSourceId).setSourceType(SourceType.APPOINTMENT);
        }
        membershipServiceBlockingStub.redeemMemberships(builder.build());
    }

    public Boolean setPaymentResultForFulfillment(SetPaymentParams params) {
        log.info("set payment result: {}", JsonUtil.toJson(params));
        MoeGroomingInvoice invoice = orderService.getOrderWithItemsById(params.getInvoiceId());
        orderService.updateOrderPaidResult(invoice, params);
        return true;
    }

    public Boolean setPaymentResult(SetPaymentParams params) {
        log.info("set payment result: {}", JsonUtil.toJson(params));
        MoeGroomingInvoice invoice = orderService.getOrderWithItemsById(params.getInvoiceId());
        Long companyId = businessInfoHelper.getCompanyIdByBusinessId(invoice.getBusinessId());
        setPaymentResultStatus(companyId, invoice, params);
        return true;
    }

    private MoeGroomingInvoice setPaymentResultStatus(
            Long companyId, MoeGroomingInvoice invoice, SetPaymentParams params) {
        // 更新order
        if (Objects.equals(
                invoice.getType(),
                com.moego.idl.models.order.v1.OrderSourceType.FULFILLMENT.name().toLowerCase())) {
            orderService.updateOrderPaidResult(invoice, params);
            return invoice;
        }
        BigDecimal remainAmount = orderService.updateOrderPaidResult(invoice, params);
        if (InvoiceStatusEnum.TYPE_NOSHOW.equals(invoice.getType())
                || (InvoiceStatusEnum.TYPE_APPOINTMENT.equals(invoice.getType())
                        && OrderModel.OrderType.ORIGIN.name().equalsIgnoreCase(invoice.getOrderType()))) {
            // DONE new order flow
            if (!newOrderHelper.isNewOrder(invoice.getGroomingId())) {
                EditIdParams editIdParams = new EditIdParams();
                editIdParams.setId(invoice.getGroomingId());
                editIdParams.setBusinessId(invoice.getBusinessId());
                // 预约的支付状态暂时保留，后续需要取自 order 的 payment status
                editIdParams.setIsPaid(
                        InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())
                                ? GroomingAppointmentEnum.PAID
                                : GroomingAppointmentEnum.PARTIAL_PAY);
                if (InvoiceStatusEnum.TYPE_NOSHOW.equals(invoice.getType())) {
                    editIdParams.setNowShowFee(params.getPaidAmount());
                }
                editIdParams.setIsDeposit(params.getIsDeposit());
                editIdParams.setIsFromOB(params.getIsFromOB());
                appointmentService.editAppointmentPaid(editIdParams);
            }
        }
        // fix: #ERP-801
        // DEPRECATED: 如果是 online pay 的方式（非押金），则主动设置 appointment 为 finished，
        // DEPRECATED: 如果是其他支付方式，由前端主动调用 /grooming/invoice/complete 接口来修改状态
        // 所以异步支付的场景，统一由后端标记为finish   REF：ERP-1665
        // OB Prepay来源的请求也不更新
        // add 如果是preauth ob 可以fully pay
        if (remainAmount.compareTo(BigDecimal.ZERO) <= 0
                && !Boolean.TRUE.equals(params.getIsRefund())
                && !Boolean.TRUE.equals(params.getIsDeposit())
                && !(Boolean.TRUE.equals(params.getIsFromOB()) && !Boolean.TRUE.equals(params.getIsPreAuth()))) {
            this.setCompletedWithoutSummary(invoice.getBusinessId(), invoice.getId());
            ThreadPool.execute(() -> {
                // TODO(ritchie): 解耦， 订阅 order status changes event
                String result = messageSendClient.sendAutoMessage(SendServiceMessageParams.builder()
                        .companyId(companyId)
                        .businessId(invoice.getBusinessId())
                        .customerId(invoice.getCustomerId())
                        .targetTypeEnums(MessageTargetTypeEnums.AUTO_RECEIPT)
                        .autoMessageTemplate(AutoMessageTemplateEnum.RECEIPT)
                        .groomingId(invoice.getGroomingId())
                        .targetId(invoice.getId())
                        .build());
                log.info("auto send message result: {}, param: {}", result, invoice.getId());
            });
        }

        return invoice;
    }

    public InvoiceSummaryDTO saveNoShowInvoice(
            Long companyId, Integer groomingId, BigDecimal amount, Integer operatorId) {
        MoeGroomingInvoice noShowInvoice = createNoShowInvoice(companyId, groomingId, amount, operatorId);
        // 只更新invoice关联关系,不使用preauth金额
        paymentPreAuthClient.updateInvoiceAndCapture(UpdateInvoiceAndCaptureParams.builder()
                .businessId(noShowInvoice.getBusinessId())
                .customerId(noShowInvoice.getCustomerId())
                .ticketId(groomingId)
                .invoiceId(noShowInvoice.getId())
                .needCapture(false)
                .build());
        return orderService.convertToSummaryDTO(noShowInvoice);
    }

    public InvoiceSummaryDTO saveNoShowInvoiceAndPreAuth(
            Long companyId, Integer groomingId, BigDecimal amount, Integer operatorId) {
        // 更新invoice关联关系,并capture preauth金额
        MoeGroomingInvoice noShowInvoice = createNoShowInvoice(companyId, groomingId, amount, operatorId);
        paymentPreAuthClient.updateInvoiceAndCapture(UpdateInvoiceAndCaptureParams.builder()
                .businessId(noShowInvoice.getBusinessId())
                .customerId(noShowInvoice.getCustomerId())
                .ticketId(groomingId)
                .invoiceId(noShowInvoice.getId())
                .needCapture(true)
                .build());
        return orderService.convertToSummaryDTO(noShowInvoice);
    }

    private MoeGroomingInvoice createNoShowInvoice(
            Long companyId, Integer groomingId, BigDecimal amount, Integer operatorId) {
        // TODO business id 不传也可以走索引，待后续 order 加上 company id 这里再把校验加回来
        MoeGroomingInvoice existInvoice =
                orderService.getOrderByGroomingIdAndType(groomingId, InvoiceStatusEnum.TYPE_NOSHOW);
        // 状态为 INVOICE_STATUS_REMOVE 时，重新创建 noShow invoice
        if (existInvoice != null && !InvoiceStatusEnum.INVOICE_STATUS_REMOVED.equals(existInvoice.getStatus())) {
            //            if (!businessId.equals(existInvoice.getBusinessId())) {
            //                throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given
            // business.");
            //            }
            if (amount != null && existInvoice.getTotalAmount().compareTo(amount) != 0) {
                appointmentService.setNoShoFee(groomingId, amount);
                existInvoice.setNoShowItem(amount);
                existInvoice.setUpdateBy(operatorId);
                //                existInvoice.calculate();
                orderService.updateOrder(companyId, existInvoice);
            }
            return existInvoice;
        }
        // prepare no show data for appointment
        MoeGroomingAppointment appointment = getAppointmentByGroomingId(groomingId);
        if (!companyId.equals(appointment.getCompanyId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not appointment for given business.");
        }
        var customerInfo =
                customerHelper.getCustomerInfo(appointment.getCustomerId().longValue());
        String customerName = null;
        if (customerInfo != null) {
            customerName = customerInfo.getFirstName() + " " + customerInfo.getLastName();
        }

        appointmentService.setNoShoFee(groomingId, amount);
        orderHelper.createNoShowOrderForAppointment(appointment, operatorId.longValue(), amount, customerName);

        ThreadPool.execute(() -> {
            quickBooksService.addRedisSyncGroomingData(
                    appointment.getBusinessId(), appointment.getId(), appointment.getAppointmentDate());
        });
        // 查询invoice最新信息
        return orderService.getOrderByGroomingIdAndType(groomingId, InvoiceStatusEnum.TYPE_NOSHOW);
    }

    public boolean updateInvoiceServiceTax(Integer businessId, ServiceTaxUpdateForApptDto updateParams) {
        return orderService.updateItemTax(businessId, updateParams);
    }

    public MoeGroomingInvoice queryInvoiceByGroomingId(Integer businessId, Integer groomingId) {
        MoeGroomingInvoice invoice = orderService.getOrderByGroomingId(businessId, groomingId);
        if (invoice == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        return invoice;
    }

    public List<InvoiceSummaryDTO> selectListByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        List<MoeGroomingInvoice> moeGroomingInvoices = orderService.getListByIds(ids);
        if (CollectionUtils.isEmpty(moeGroomingInvoices)) {
            return Collections.emptyList();
        }
        // 获取customerIdList--填充email属性
        List<Integer> customerIds = new ArrayList<>();
        moeGroomingInvoices.forEach(i -> {
            customerIds.add(i.getCustomerId());
        });
        // 批量获取customer
        CustomerIdListParams idListParams = new CustomerIdListParams();
        idListParams.setIdList(customerIds);
        List<MoeBusinessCustomerDTO> moeBusinessCustomerDTOS = iCustomerCustomerClient.queryCustomerList(idListParams);
        if (CollectionUtils.isEmpty(moeBusinessCustomerDTOS)) {
            throw new CommonException(ResponseCodeEnum.CUSTOMER_NOT_FOUND);
        }
        List<InvoiceSummaryDTO> result = new ArrayList<>();
        for (MoeGroomingInvoice invoice : moeGroomingInvoices) {
            InvoiceSummaryDTO invoiceSummaryDTO = new InvoiceSummaryDTO();
            BeanUtils.copyProperties(invoice, invoiceSummaryDTO);
            if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(invoice.getStatus())) {
                invoiceSummaryDTO.setStatus("completed");
            } else if (InvoiceStatusEnum.INVOICE_STATUS_PROCESSING.equals(invoice.getStatus())) {
                invoiceSummaryDTO.setStatus("processing");
            } else {
                invoiceSummaryDTO.setStatus("created");
            }
            for (MoeBusinessCustomerDTO customerDTO : moeBusinessCustomerDTOS) {
                if (customerDTO.getCustomerId().equals(invoice.getCustomerId())) {
                    invoiceSummaryDTO.setCustomerEmail(customerDTO.getEmail());
                    break;
                }
            }
            result.add(invoiceSummaryDTO);
        }

        return result;
    }

    public List<InvoiceSummaryDTO> selectInvoicesByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        return orderService.getListByIds(ids).stream()
                .map(invoice -> {
                    InvoiceSummaryDTO invoiceSummaryDTO = new InvoiceSummaryDTO();
                    BeanUtils.copyProperties(invoice, invoiceSummaryDTO);
                    return invoiceSummaryDTO;
                })
                .collect(Collectors.toList());
    }

    public List<CustomerPaymentSummary> queryCustomerPaymentSummary(List<Integer> customerIds, Integer businessId) {
        if (customerIds == null || customerIds.isEmpty()) {
            return new ArrayList<>();
        }
        return orderService.getCustomerPaymentSummaries(businessId, customerIds);
    }

    public InvoiceSummaryDTO removeApplyPackageService(Integer businessId, ApplyPackageServiceVo vo) {
        return orderService.removePackage(
                businessId,
                vo.getInvoiceId(),
                vo.getPackageServiceId(),
                vo.getServiceId(),
                vo.getQuantity(),
                vo.getCheckRefund());
    }

    public InvoiceSummaryDTO removeApplyPackageServiceV2(Long businessId, DeleteApplyPackageServiceVo vo) {
        return orderService.removePackageV2(businessId, vo.getInvoiceId(), vo.getApplyPackageId(), vo.getCheckRefund());
    }

    //    @Transactional
    public InvoiceSummaryDTO applyPackage(
            Integer businessId, Integer invoiceId, Integer operatorId, boolean checkRefund) {
        return orderService.applyPackage(businessId, invoiceId, operatorId, checkRefund);
    }

    public InvoiceSummaryDTO getWithPaymentById(Integer invoiceId, Integer tokenStaffId) {
        InvoiceSummaryDTO invoice = getById(invoiceId, tokenStaffId);
        log.info("invoice detail: {}", invoice);

        GetPaymentParams p = new GetPaymentParams();
        p.setModule(PaymentMethodEnum.MODULE_GROOMING);
        p.setInvoiceId(invoiceId);

        PaymentSummary paymentSummary = iPaymentService.getPayments(p).getData();
        if (paymentSummary != null) {
            List<PaymentSummary.PaymentDto> payments = paymentSummary.getPayments();
            if (!CollectionUtils.isEmpty(payments)) {
                Set<Integer> paymentIds =
                        payments.stream().map(PaymentSummary.PaymentDto::getId).collect(Collectors.toSet());
                List<BookOnlineDepositDTO> prepayDTOs =
                        bookOnlineDepositService.getOBDepositByPaymentIds(invoice.getBusinessId(), paymentIds);
                Set<Integer> prepayPaymentIds = prepayDTOs.stream()
                        .filter(dto -> Objects.equals(DepositPaymentTypeEnum.PrePay, dto.getDepositType()))
                        .map(BookOnlineDepositDTO::getPaymentId)
                        .collect(Collectors.toSet());
                for (PaymentSummary.PaymentDto paymentDto : paymentSummary.getPayments()) {
                    paymentDto.setIsPrepay(prepayPaymentIds.contains(paymentDto.getId()));
                }
            }
            invoice.setPaymentSummary(paymentSummary);
        }

        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(invoice.getBusinessId());
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(infoIdParams);
        invoice.setBusinessName(businessDto.getBusinessName());

        MoeGroomingAppointment appointment = getAppointmentByGroomingId(invoice.getGroomingId());
        invoice.setCheckInTime(appointment.getCheckInTime());
        invoice.setCheckOutTime(appointment.getCheckOutTime());
        invoice.setAppointmentDate(appointment.getAppointmentDate());
        invoice.setAppointmentStartTime(appointment.getAppointmentStartTime());
        invoice.setAppointmentEndTime(appointment.getAppointmentEndTime());
        // MOE-2685
        // 后端以4位精度计算，导致前端显示total异常，以最终paymentAmount作为显示
        invoice.setTotalAmount(invoice.getPaymentAmount());

        // get deposit info

        return invoice;
    }

    public OrderInvoiceSummaryDTO getOrderDetailWithPaymentV1(
            long companyId, Integer businessId, Integer staffId, Integer orderId) {
        OrderInvoiceSummaryDTO orderSummary = getOrderDetailWithPayment(companyId, businessId, staffId, orderId);

        // 老接口适配，把 service_charge 类型改成 service
        orderSummary.getItems().forEach(item -> {
            if (Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType())) {
                item.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            }
        });

        return orderSummary;
    }

    public OrderInvoiceSummaryDTO getOrderDetailWithPaymentV2(
            long companyId, Integer businessId, Integer staffId, Integer orderId) {
        return getOrderDetailWithPayment(companyId, businessId, staffId, orderId);
    }

    /**
     * 获取订单详情，包含支付信息
     * 这里的 order 可以是 extra order id
     *
     * @param companyId
     * @param businessId
     * @param staffId
     * @param orderId
     * @return
     */
    public OrderInvoiceSummaryDTO getOrderDetailWithPayment(
            long companyId, Integer businessId, Integer staffId, Integer orderId) {

        OrderInvoiceSummaryDTO orderSummary = orderService.getOrderInvoiceSummaryById(businessId, orderId);

        mergeServiceCharge(orderSummary);

        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(orderSummary.getBusinessId());
        if (companyDto == null || !companyDto.getId().equals((int) companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid invoice id");
        }
        boolean isOriginOrder = OrderModel.OrderType.ORIGIN.name().equals(orderSummary.getOrderType());
        if (staffId != null) {
            boolean isHideEmail = !permissionHelper.hasPermission(
                    companyId, staffId.longValue(), PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);

            if (isHideEmail && !StringUtils.isEmpty(orderSummary.getCustomerEmail())) {
                orderSummary.setCustomerEmail(PermissionUtil.emailConfusion(orderSummary.getCustomerEmail()));
            }
        }
        // 重要：这里存在跨 location 查询的场景，businessId 应该用 orderSummary 中的 businessId，而不是 token 中的
        businessId = orderSummary.getBusinessId();

        // membership info for getting usages
        GetRecommendBenefitUsageResponse response = membershipServiceBlockingStub.getRecommendBenefitUsage(
                GetRecommendBenefitUsageRequest.newBuilder().setOrderId(orderId).build());
        Map<Long /* membership id */, OrderInvoiceSummaryDTO.MembershipDTO> memebershipMap =
                getMembershipDTOMap(response);
        List<OrderInvoiceSummaryDTO.MembershipDTO> usedMembershipDTOS = response.getUsageViewsList().stream()
                .map(MembershipUsageView::getMembershipId)
                .distinct()
                .map(memebershipMap::get)
                .filter(Objects::nonNull)
                .toList();
        if (!CollectionUtils.isEmpty(usedMembershipDTOS)) {
            orderSummary.setMembershipList(usedMembershipDTOS);
            orderSummary.setDiscountList(List.of());
        }

        Map<Long /* order item id */, List<OrderInvoiceSummaryDTO.MembershipDTO>> itemMemebershipMap =
                getItemMemebershipMap(memebershipMap, response.getUsageViewsList());

        boolean isFulfillment = Objects.equals(
                com.moego.idl.models.order.v1.OrderSourceType.FULFILLMENT.name().toLowerCase(), orderSummary.getType());
        if (isFulfillment) {
            fillFulfillmentOrderDetail(orderSummary);
        }

        boolean isGroomingInvoice = OrderSourceType.APPOINTMENT.getSource().equalsIgnoreCase(orderSummary.getType())
                || OrderSourceType.NO_SHOW.getSource().equalsIgnoreCase(orderSummary.getType());
        // grooming invoice 查询 appointment info、deposit info
        if (isGroomingInvoice) {
            var appointmentId = orderSummary.getGroomingId();
            MoeGroomingAppointment appointment = getAppointmentByGroomingId(appointmentId);
            OrderInvoiceSummaryDTO.AppointmentInfoDTO appointmentInfo = new OrderInvoiceSummaryDTO.AppointmentInfoDTO();
            appointmentInfo.setAppointmentStatus(appointment.getStatus());
            appointmentInfo.setCheckInTime(appointment.getCheckInTime());
            appointmentInfo.setCheckOutTime(appointment.getCheckOutTime());
            appointmentInfo.setAppointmentDate(appointment.getAppointmentDate());
            appointmentInfo.setAppointmentStartTime(appointment.getAppointmentStartTime());
            orderSummary.setAppointmentInfo(appointmentInfo);
            var allPetDetailsPair = appointmentServiceDetailService.getAppointmentServiceDetails(
                    List.of(appointmentId), orderSummary.getId(), isOriginOrder);
            List<GroomingPetDetailDTO> allPetDetails = allPetDetailsPair.key();
            List<EvaluationServiceDetail> evaluations = allPetDetailsPair.value();
            // service map
            List<MoeGroomingPetDetail> allPetServiceDetails = allPetDetails.stream()
                    .map(PetDetailConverter.INSTANCE::toEntity)
                    .collect(Collectors.toList());
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap =
                    PetDetailUtil.getPetServiceMap(allPetServiceDetails);
            // get pricing rule apply log
            List<PricingRuleApplyLogModel> applyLogs =
                    getPricingRuleApplyLogModels(companyId, appointment, allPetDetails);
            // 过滤当前invoice的数据
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                    groomingServiceOperationService.getOperationMapByGroomingId(
                            businessId, appointmentId, orderSummary.getId(), isOriginOrder);
            // boarding split lodging
            var boardingSplitLodgings = boardingSplitLodgingService
                    .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                            .addAppointmentIds(appointmentId)
                            .build())
                    .getBoardingSplitLodgingsList();

            Map<Integer, MoeStaffDto> staffMap = getStaffMap(businessId, allPetDetails);
            var lodgingIds = allPetDetails.stream()
                    .map(GroomingPetDetailDTO::getLodgingId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            var splitLodgingIds = boardingSplitLodgings.stream()
                    .map(BoardingSplitLodgingModel::getLodgingId)
                    .toList();
            Map<Long, Pair<LodgingUnitModel, LodgingTypeModel>> lodgingMap =
                    getLodgingMap(Stream.concat(lodgingIds.stream(), splitLodgingIds.stream())
                            .distinct()
                            .toList());
            Map<Integer, CustomerPetDetailDTO> petMap =
                    appointmentServiceDetailService.getPetMap(allPetDetails, evaluations);
            // pet details
            if (!CollectionUtils.isEmpty(orderSummary.getItems())) {
                if (!isOriginOrder) {
                    Set<Integer> alreadyUsedPetDetailId = new HashSet<>();
                    orderSummary.getItems().forEach(invoiceItem -> {
                        invoiceItem.setPetDetails(buildExtraOrderPetDetailOrderItems(
                                alreadyUsedPetDetailId,
                                allPetServiceDetails,
                                invoiceItem,
                                petMap,
                                staffMap,
                                operationMap,
                                lodgingMap,
                                petServiceMap));
                        invoiceItem.setPetEvaluationDetails(
                                buildPetEvaluationDetailOrderItems(evaluations, invoiceItem, petMap));

                        List<OrderInvoiceSummaryDTO.MembershipDTO> membershipDTOs = itemMemebershipMap.getOrDefault(
                                invoiceItem.getId().longValue(), List.of());
                        if (!CollectionUtils.isEmpty(membershipDTOs)) {
                            invoiceItem.setMembershipDetailList(membershipDTOs);
                            invoiceItem.setDiscountInfo(null);
                        }
                    });
                } else {
                    Set<Integer> boundPetDetailIds = new HashSet<>();
                    orderSummary.getItems().forEach(invoiceItem -> {
                        var petDetails = buildPetDetailOrderItems(
                                allPetServiceDetails,
                                invoiceItem,
                                petMap,
                                staffMap,
                                operationMap,
                                lodgingMap,
                                petServiceMap,
                                applyLogs,
                                boardingSplitLodgings);
                        var uniquePetDetails = getUniquePetDetailList(petDetails, invoiceItem, boundPetDetailIds);
                        invoiceItem.setPetDetails(uniquePetDetails);

                        invoiceItem.setPetEvaluationDetails(
                                buildPetEvaluationDetailOrderItems(evaluations, invoiceItem, petMap));

                        List<OrderInvoiceSummaryDTO.MembershipDTO> membershipDTOs = itemMemebershipMap.getOrDefault(
                                invoiceItem.getId().longValue(), List.of());
                        if (!CollectionUtils.isEmpty(membershipDTOs)) {
                            invoiceItem.setMembershipDetailList(membershipDTOs);
                            invoiceItem.setDiscountInfo(null);
                        }
                    });
                }
            }

            // 查询deposit信息
            // 注意：新旧 deposit 不可混用，如果此单有旧的 deposit，此处会覆盖前面通过 deposit order 转换的 depositInfo
            MoeInvoiceDeposit deposit = depositService.getDepositByInvoiceId(orderId);
            if (deposit != null) {
                DepositDto depositDto = new DepositDto();
                BeanUtils.copyProperties(deposit, depositDto);
                orderSummary.setDepositInfo(depositDto);
            }
        }

        // 如果是product ，需要展示membership的数据
        if (OrderSourceType.PRODUCT.getSource().equalsIgnoreCase(orderSummary.getType())
                || OrderSourceType.PACKAGE.getSource().equalsIgnoreCase(orderSummary.getType())) {
            orderSummary.getItems().forEach(invoiceItem -> {
                List<OrderInvoiceSummaryDTO.MembershipDTO> membershipDTOs =
                        itemMemebershipMap.getOrDefault(invoiceItem.getId().longValue(), List.of());
                if (!CollectionUtils.isEmpty(membershipDTOs)) {
                    invoiceItem.setMembershipDetailList(membershipDTOs);
                    invoiceItem.setDiscountInfo(null);
                }
            });
        }

        // payment summary
        String paymentModule = isFulfillment
                ? PaymentMethodEnum.MODULE_FULFILLMENT
                : (isGroomingInvoice ? PaymentMethodEnum.MODULE_GROOMING : PaymentMethodEnum.MODULE_RETAIL);
        if (StringUtils.equals(orderSummary.getType(), PaymentMethodEnum.MODULE_MEMBERSHIP)) {
            paymentModule = PaymentMethodEnum.MODULE_MEMBERSHIP;
        }
        Integer invoiceIdForOldRecord = isGroomingInvoice || isFulfillment
                ? orderId
                : orderService.getOldInvoiceId(orderId); // 如果是retail订单，需要判断是否是老数据

        GetPaymentParams p = new GetPaymentParams();
        p.setModule(paymentModule);
        p.setInvoiceId(invoiceIdForOldRecord);
        PaymentSummary paymentSummary = iPaymentService.getPayments(p).getData();
        if (paymentSummary != null) {
            paymentSummary.setInvoiceId(orderId);
            List<PaymentSummary.PaymentDto> payments = paymentSummary.getPayments();
            if (isOriginOrder && !CollectionUtils.isEmpty(payments)) {
                Set<Integer> paymentIds =
                        payments.stream().map(PaymentSummary.PaymentDto::getId).collect(Collectors.toSet());
                List<BookOnlineDepositDTO> prepayDTOs =
                        bookOnlineDepositService.getOBDepositByPaymentIds(businessId, paymentIds);
                Set<Integer> prepayPaymentIds = prepayDTOs.stream()
                        .filter(dto -> Objects.equals(DepositPaymentTypeEnum.PrePay, dto.getDepositType()))
                        .map(BookOnlineDepositDTO::getPaymentId)
                        .collect(Collectors.toSet());
                for (PaymentSummary.PaymentDto paymentDto : paymentSummary.getPayments()) {
                    paymentDto.setIsPrepay(prepayPaymentIds.contains(paymentDto.getId()));
                }
            }
            orderSummary.setPaymentSummary(paymentSummary);
        }
        if (isOriginOrder) {
            PreAuthDTO preAuthDTO = paymentPreAuthClient.queryByTicketId(businessId, orderSummary.getGroomingId());
            if (preAuthDTO != null) {
                orderSummary.setPreAuthInfo(preAuthDTO);
            }
        }
        // business info
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(infoIdParams);
        orderSummary.setBusinessName(businessDto.getBusinessName());

        // receipt sent info
        List<MessageDetailDTO> receiptSentList =
                iMessageDetailClient.queryMessageDetailByTargetTypeAndId(MoeMessageTargetQueryParams.builder()
                        .targetId(orderId)
                        .targetType(MessageTargetTypeEnums.AUTO_RECEIPT.getValue())
                        .build());

        orderSummary.setReceiptSentList(receiptSentList.stream()
                .map(msg -> {
                    IdAndCreateTimeDTO dto = new IdAndCreateTimeDTO();
                    dto.setId(msg.getId());
                    dto.setCreateTime(msg.getCreateTime());
                    return dto;
                })
                .toList());

        return orderSummary;
    }

    private Pair<String, Integer> convertTimestampToDateTime(long timestamp, String timeZoneName) {
        var dateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.of(timeZoneName));
        return Pair.of(dateTime.toLocalDate().toString(), dateTime.getHour() * 60 + dateTime.getMinute());
    }

    private void fillFulfillmentOrderDetail(OrderInvoiceSummaryDTO orderSummary) {
        var fulfillment = fulfillmentStub
                .getFulfillment(GetFulfillmentRequest.newBuilder()
                        .setFulfillmentId(orderSummary.getGroomingId())
                        .build())
                .getFulfillment();
        var appointmentInfo = new OrderInvoiceSummaryDTO.AppointmentInfoDTO();
        appointmentInfo.setAppointmentStatus((byte) fulfillment.getStatus().getNumber());
        var timeZoneName = companyHelper.getCompanyTimeZoneName(fulfillment.getCompanyId());
        var dateTime = convertTimestampToDateTime(fulfillment.getStartDateTime().getSeconds(), timeZoneName);
        appointmentInfo.setAppointmentDate(dateTime.getFirst());
        appointmentInfo.setAppointmentStartTime(dateTime.getSecond());
        orderSummary.setAppointmentInfo(appointmentInfo);

        var groupClassDetails = groupClassDetailStub
                .listGroupClassDetails(ListGroupClassDetailsRequest.newBuilder()
                        .setFilter(ListGroupClassDetailsRequest.Filter.newBuilder()
                                .addFulfillmentIds(orderSummary.getGroomingId()))
                        .build())
                .getGroupClassDetailsList();
        var instanceIdToInfo = getGroupClassInstances(groupClassDetails);
        var staffIdToInfo =
                getStaffIdToInfo((int) fulfillment.getBusinessId(), new ArrayList<>(instanceIdToInfo.values()));
        var petIdToInfo = getPetIdToInfo(groupClassDetails);

        orderSummary.getItems().forEach(item -> {
            var petDetails = groupClassDetails.stream()
                    .filter(groupClassDetail -> matchInvoiceItem(item, groupClassDetail))
                    .map(groupClassDetail -> {
                        OrderItemPetDetailDTO petDetail = new OrderItemPetDetailDTO();
                        var pet = petIdToInfo.getOrDefault(
                                (int) groupClassDetail.getPetId(), new CustomerPetPetCodeDTO());
                        var instance = instanceIdToInfo.getOrDefault(
                                groupClassDetail.getGroupClassInstanceId(), GroupClassInstance.getDefaultInstance());
                        var staff = staffIdToInfo.getOrDefault((int) instance.getStaffId(), new MoeStaffDto());
                        petDetail.setPetId(pet.getPetId());
                        petDetail.setPetName(pet.getPetName());
                        petDetail.setPetBreed(pet.getBreed());
                        petDetail.setServiceId((int) groupClassDetail.getGroupClassId());
                        petDetail.setStartTime(instance.getStartTime().getHours() * 60L
                                + instance.getStartTime().getMinutes());
                        petDetail.setServicePrice(MoneyUtils.fromGoogleMoney(instance.getPrice()));
                        petDetail.setServiceItemType(ServiceItemType.GROUP_CLASS_VALUE);
                        petDetail.setQuantity(1);
                        petDetail.setStaffId((int) instance.getStaffId());
                        petDetail.setStaffFirstName(staff.getFirstName());
                        petDetail.setStaffLastName(staff.getLastName());
                        staffIdToInfo.get((int) instance.getStaffId());

                        return petDetail;
                    })
                    .toList();

            item.setPetDetails(petDetails);
        });
    }

    private static boolean matchInvoiceItem(
            OrderInvoiceSummaryDTO.OrderItemDTO item, GroupClassDetailModel groupClassDetail) {
        return Objects.equals(item.getType(), OrderItemType.ITEM_TYPE_SERVICE.getType())
                && Objects.equals(item.getPetId().longValue(), groupClassDetail.getPetId())
                && Objects.equals(item.getObjectId().longValue(), groupClassDetail.getGroupClassId());
    }

    private Map<Integer, CustomerPetPetCodeDTO> getPetIdToInfo(List<GroupClassDetailModel> groupClassDetails) {
        var petIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getPetId)
                .distinct()
                .map(Long::intValue)
                .toList();
        return customerHelper.getPetInfoMap(petIds);
    }

    private Map<Integer, MoeStaffDto> getStaffIdToInfo(
            Integer businessId, List<GroupClassInstance> groupClassInstances) {
        var staffIds = groupClassInstances.stream()
                .map(GroupClassInstance::getStaffId)
                .distinct()
                .map(Long::intValue)
                .toList();
        return companyHelper.getStaffInfoMap(businessId, staffIds);
    }

    private Map<Long, GroupClassInstance> getGroupClassInstances(List<GroupClassDetailModel> groupClassDetails) {
        var instanceIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getGroupClassInstanceId)
                .distinct()
                .toList();
        return groupClassStub
                .listInstances(
                        ListInstancesRequest.newBuilder().addAllIds(instanceIds).build())
                .getGroupClassInstancesList()
                .stream()
                .collect(Collectors.toMap(GroupClassInstance::getId, Function.identity()));
    }

    @Nonnull
    private static Map<Long /* membership id */, OrderInvoiceSummaryDTO.MembershipDTO> getMembershipDTOMap(
            GetRecommendBenefitUsageResponse response) {
        List<OrderInvoiceSummaryDTO.MembershipDTO> membershipDTOS = response.getAllMembershipsList().stream()
                .map(membership -> {
                    OrderInvoiceSummaryDTO.MembershipDTO membershipDTO = new OrderInvoiceSummaryDTO.MembershipDTO();
                    membershipDTO.setId(membership.getId());
                    membershipDTO.setName(membership.getName());
                    membershipDTO.setDescription(membership.getDescription());
                    return membershipDTO;
                })
                .toList();

        return membershipDTOS.stream()
                .collect(toMap(OrderInvoiceSummaryDTO.MembershipDTO::getId, Function.identity(), (a, b) -> a));
    }

    @Nonnull
    private static Map<Long /* order item id */, List<OrderInvoiceSummaryDTO.MembershipDTO>> getItemMemebershipMap(
            Map<Long, OrderInvoiceSummaryDTO.MembershipDTO> memebershipMap, List<MembershipUsageView> usageViewsList) {
        return usageViewsList.stream()
                .map(usage -> {
                    OrderInvoiceSummaryDTO.MembershipDTO membershipDTO = memebershipMap.get(usage.getMembershipId());
                    if (Objects.isNull(membershipDTO)) {
                        return null;
                    }
                    OrderInvoiceSummaryDTO.MembershipDTO thisMembershipDTO = new OrderInvoiceSummaryDTO.MembershipDTO();
                    thisMembershipDTO.setId(membershipDTO.getId());
                    thisMembershipDTO.setName(membershipDTO.getName());
                    thisMembershipDTO.setDescription(membershipDTO.getDescription());
                    thisMembershipDTO.setDiscountAmount(BigDecimal.valueOf(usage.getPriceReduction()));
                    return new AbstractMap.SimpleEntry<>(usage.getOrderItemId(), thisMembershipDTO);
                })
                .filter(Objects::nonNull)
                .collect(groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    private List<PricingRuleApplyLogModel> getPricingRuleApplyLogModels(
            long companyId, MoeGroomingAppointment appointment, List<GroomingPetDetailDTO> allPetDetails) {
        boolean hasBoardingOrDaycareService = ServiceItemEnum.BOARDING.isIncludedIn(appointment.getServiceTypeInclude())
                || ServiceItemEnum.DAYCARE.isIncludedIn(appointment.getServiceTypeInclude());
        if (!hasBoardingOrDaycareService) {
            return List.of();
        }
        List<PricingRuleApplyLogModel> applyLogs = pricingRuleApplyService
                .listPricingRuleApplyLog(ListPricingRuleApplyLogRequest.newBuilder()
                        .setSourceId(appointment.getId().longValue())
                        .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                        .setCompanyId(companyId)
                        .build())
                .getPricingRuleApplyLogsList();

        // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式
        Map<Integer, Map<Integer, GroomingPetDetailDTO>> petServiceMap =
                PetDetailDTOUtil.getPetServiceMap(allPetDetails);

        return allPetDetails.stream()
                .map(petDetail -> {
                    List<PricingRuleApplyLogModel> currentApplyLog = applyLogs.stream()
                            .filter(applyLog -> Objects.equals(
                                            applyLog.getPetId(),
                                            petDetail.getPetId().longValue())
                                    && Objects.equals(
                                            applyLog.getServiceId(),
                                            petDetail.getServiceId().longValue()))
                            .toList();
                    if (CollectionUtils.isEmpty(currentApplyLog)) {
                        return new ArrayList<PricingRuleApplyLogModel>();
                    }

                    List<String> dates = PetDetailDTOUtil.getServiceDates(petDetail, petServiceMap);
                    if (CollectionUtils.isEmpty(dates)) {
                        return currentApplyLog;
                    }

                    double originalPrice = currentApplyLog.get(0).getOriginalPrice();
                    Map<String, Double> logDateToPriceMap = currentApplyLog.stream()
                            .filter(PricingRuleApplyLogModel::hasServiceDate)
                            .collect(Collectors.toMap(
                                    PricingRuleApplyLogModel::getServiceDate,
                                    PricingRuleApplyLogModel::getAdjustedPrice,
                                    (p1, p2) -> p1));
                    return dates.stream()
                            .map(date -> PricingRuleApplyLogModel.newBuilder()
                                    .setPetId(petDetail.getPetId().longValue())
                                    .setServiceId(petDetail.getServiceId().longValue())
                                    .setOriginalPrice(originalPrice)
                                    .setAdjustedPrice(logDateToPriceMap.getOrDefault(date, originalPrice))
                                    .setServiceDate(date)
                                    .setSourceId(appointment.getId().longValue())
                                    .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                                    .build())
                            .toList();
                })
                .flatMap(List::stream)
                .toList();
    }

    private static void mergeServiceCharge(OrderInvoiceSummaryDTO orderSummary) {
        if (CollectionUtils.isEmpty(orderSummary.getItems())) {
            return;
        }

        var lineItems = new ArrayList<>(orderSummary.getItems());

        var serviceChargeLineItems = lineItems.stream()
                .filter(e -> Objects.equals(e.getType(), OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType()))
                .toList();

        // 合并相同 service charge
        var serviceChargeIdToServiceCharges =
                serviceChargeLineItems.stream().collect(groupingBy(OrderInvoiceSummaryDTO.OrderItemDTO::getObjectId));

        var mergedServiceCharge = serviceChargeIdToServiceCharges.values().stream()
                .map(items -> {
                    var merged = new OrderInvoiceSummaryDTO.OrderItemDTO();
                    BeanUtils.copyProperties(items.get(0), merged);
                    merged.setQuantity(items.stream()
                            .mapToInt(OrderInvoiceSummaryDTO.OrderItemDTO::getQuantity)
                            .sum());
                    merged.setTotalAmount(items.stream()
                            .map(OrderInvoiceSummaryDTO.OrderItemDTO::getTotalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    merged.setSubTotalAmount(items.stream()
                            .map(OrderInvoiceSummaryDTO.OrderItemDTO::getSubTotalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    merged.setDiscountAmount(items.stream()
                            .map(OrderInvoiceSummaryDTO.OrderItemDTO::getDiscountAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    merged.setTaxAmount(items.stream()
                            .map(OrderInvoiceSummaryDTO.OrderItemDTO::getTaxAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    var taxInfo = merged.getTaxInfo();
                    if (taxInfo != null) {
                        var tax = items.stream()
                                .map(OrderInvoiceSummaryDTO.OrderItemDTO::getTaxInfo)
                                .map(OrderInvoiceSummaryDTO.TaxDTO::getTaxAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        taxInfo.setTaxAmount(tax);
                    }
                    return merged;
                })
                .toList();

        lineItems.removeAll(serviceChargeLineItems);
        lineItems.addAll(mergedServiceCharge);

        orderSummary.setItems(lineItems);
    }

    private List<OrderItemPetDetailDTO> buildExtraOrderPetDetailOrderItems(
            Set<Integer> alreadyUsedPetDetailId,
            List<MoeGroomingPetDetail> petDetails,
            OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            Map<Integer, CustomerPetDetailDTO> petMap,
            Map<Integer, MoeStaffDto> staffMap,
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap,
            Map<Long, Pair<LodgingUnitModel, LodgingTypeModel>> lodgingMap,
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        List<OrderItemPetDetailDTO> petDetailList = petDetails.stream()
                // 过滤掉已经用过的petDetailId
                .filter(petDetail -> !alreadyUsedPetDetailId.contains(petDetail.getId()))
                // 先保证只存在一个同样serviceId、price的pet detail
                .collect(Collectors.toMap(
                        petDetail ->
                                new AbstractMap.SimpleEntry<>(petDetail.getServiceId(), petDetail.getServicePrice()),
                        petDetail -> petDetail,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .filter(petDetail -> petDetail.getServiceId().equals(invoiceItem.getObjectId())
                        && petDetail.getServicePrice().compareTo(invoiceItem.getUnitPrice()) == 0)
                .filter(petDetail -> petMap.containsKey(petDetail.getPetId()))
                .map(gt -> {
                    CustomerPetDetailDTO pet = petMap.get(gt.getPetId());
                    OrderItemPetDetailDTO dto = new OrderItemPetDetailDTO();
                    BeanUtils.copyProperties(gt, dto);
                    dto.setPetDetailId(gt.getId());
                    dto.setPetName(pet.getPetName());
                    dto.setPetBreed(pet.getPetBreed());
                    if (gt.getServiceType() != null && gt.getServiceType() != 0) {
                        dto.setQuantity(PetDetailUtil.getQuantity(gt, petServiceMap));
                    }
                    if (staffMap.containsKey(gt.getStaffId())) {
                        MoeStaffDto staff = staffMap.get(gt.getStaffId());
                        dto.setStaffId(staff.getId());
                        dto.setStaffFirstName(staff.getFirstName());
                        dto.setStaffLastName(staff.getLastName());
                    }
                    dto.setOperationList(operationMap.getOrDefault(gt.getId(), List.of()));
                    if (lodgingMap.containsKey(gt.getLodgingId())) {
                        dto.setLodgingId(gt.getLodgingId());
                        Pair<LodgingUnitModel, LodgingTypeModel> pair = lodgingMap.get(gt.getLodgingId());
                        dto.setLodgingUnitName(pair.getFirst().getName());
                        dto.setLodgingTypeName(pair.getSecond().getName());
                    }
                    // 把已经使用的pet detail id加入
                    alreadyUsedPetDetailId.add(gt.getId());
                    return dto;
                })
                .collect(Collectors.toList());
        // 兼容旧客户端，需要有 petId 才会展示这个item, 这里把 itemId 当 petId 填充，保证唯一即可
        if (CollectionUtils.isEmpty(petDetailList) && !CollectionUtils.isEmpty(staffMap)) {
            OrderItemPetDetailDTO dto = new OrderItemPetDetailDTO();
            dto.setPetId(invoiceItem.getId());
            dto.setStaffId(new ArrayList<>(staffMap.values()).get(0).getId());
            petDetailList.add(dto);
        }
        return petDetailList;
    }

    private List<OrderItemPetDetailDTO> buildPetDetailOrderItems(
            List<MoeGroomingPetDetail> petDetails,
            OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            Map<Integer, CustomerPetDetailDTO> petMap,
            Map<Integer, MoeStaffDto> staffMap,
            Map<Integer, List<GroomingServiceOperationDTO>> operationMap,
            Map<Long, Pair<LodgingUnitModel, LodgingTypeModel>> lodgingMap,
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap,
            List<PricingRuleApplyLogModel> applyLogs,
            final List<BoardingSplitLodgingModel> boardingSplitLodgings) {
        List<OrderItemPetDetailDTO> petDetailList = petDetails.stream()
                .filter(matchPetDetailPrice(invoiceItem, applyLogs, boardingSplitLodgings))
                .filter(petDetail -> petMap.containsKey(petDetail.getPetId()))
                /*如果 pet id 为 0， item 已合并，逻辑保持不变， 否则petId需要匹配*/
                .filter(petDetail -> PrimitiveTypeUtil.isNullOrZero(invoiceItem.getPetId())
                        || petDetail.getPetId().equals(invoiceItem.getPetId()))
                .map(gt -> {
                    CustomerPetDetailDTO pet = petMap.get(gt.getPetId());
                    OrderItemPetDetailDTO dto = new OrderItemPetDetailDTO();
                    BeanUtils.copyProperties(gt, dto);
                    dto.setPetDetailId(gt.getId());
                    dto.setPetName(pet.getPetName());
                    dto.setPetBreed(pet.getPetBreed());
                    if (gt.getServiceType() != null && gt.getServiceType() != 0) {
                        dto.setQuantity(PetDetailUtil.getQuantity(gt, petServiceMap));
                    }
                    if (staffMap.containsKey(gt.getStaffId())) {
                        MoeStaffDto staff = staffMap.get(gt.getStaffId());
                        dto.setStaffId(staff.getId());
                        dto.setStaffFirstName(staff.getFirstName());
                        dto.setStaffLastName(staff.getLastName());
                    }
                    dto.setOperationList(operationMap.getOrDefault(gt.getId(), List.of()));
                    if (lodgingMap.containsKey(gt.getLodgingId())) {
                        dto.setLodgingId(gt.getLodgingId());
                        Pair<LodgingUnitModel, LodgingTypeModel> pair = lodgingMap.get(gt.getLodgingId());
                        dto.setLodgingUnitName(pair.getFirst().getName());
                        dto.setLodgingTypeName(pair.getSecond().getName());
                    }

                    if (CommonUtil.isNormal(gt.getLodgingId())) {
                        dto.setLodgingInfos(getLodgingInfos(lodgingMap, List.of(gt.getLodgingId())));
                    } else {
                        var splitLodgingModels = boardingSplitLodgings.stream()
                                .filter(boardingSplitLodging ->
                                        matchLodging(invoiceItem, applyLogs, gt, boardingSplitLodging))
                                .toList();
                        if (!CollectionUtils.isEmpty(splitLodgingModels)) {
                            dto.setLodgingInfos(getLodgingInfos(
                                    lodgingMap,
                                    splitLodgingModels.stream()
                                            .map(BoardingSplitLodgingModel::getLodgingId)
                                            .toList()));
                        }
                    }

                    return dto;
                })
                .collect(Collectors.toList());
        // 兼容旧客户端，需要有 petId 才会展示这个item, 这里把 itemId 当 petId 填充，保证唯一即可
        if (CollectionUtils.isEmpty(petDetailList) && !CollectionUtils.isEmpty(staffMap)) {
            OrderItemPetDetailDTO dto = new OrderItemPetDetailDTO();
            dto.setPetId(invoiceItem.getId());
            dto.setStaffId(new ArrayList<>(staffMap.values()).get(0).getId());
            petDetailList.add(dto);
        }
        return petDetailList;
    }

    private static boolean matchLodging(
            final OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            final List<PricingRuleApplyLogModel> applyLogs,
            final MoeGroomingPetDetail petDetail,
            final BoardingSplitLodgingModel boardingSplitLodging) {
        var splitLodgingMatchPrice = Objects.equals(
                        boardingSplitLodging.getPetDetailId(), petDetail.getId().longValue())
                && MoneyUtils.fromGoogleMoney(boardingSplitLodging.getPrice()).compareTo(invoiceItem.getUnitPrice())
                        == 0;
        if (splitLodgingMatchPrice) {
            return true;
        }
        return !CollectionUtils.isEmpty(applyLogs)
                && applyLogs.stream()
                        .anyMatch(applyLog ->
                                Objects.equals(petDetail.getServiceId().longValue(), applyLog.getServiceId())
                                        && Objects.equals(petDetail.getPetId().longValue(), applyLog.getPetId())
                                        && BigDecimal.valueOf(applyLog.getAdjustedPrice())
                                                        .compareTo(invoiceItem.getUnitPrice())
                                                == 0
                                        && !StringUtils.isEmpty(applyLog.getServiceDate())
                                        && isDateInRange(
                                                applyLog.getServiceDate(),
                                                boardingSplitLodging.getStartDateTime(),
                                                boardingSplitLodging.getEndDateTime()));
    }

    private static boolean isDateInRange(String date, Timestamp startDateTimestamp, Timestamp endDateTimestamp) {
        var serviceDate = LocalDate.parse(date).atTime(LocalTime.MAX);
        var startDateTime = TimestampConverter.INSTANCE.toLocalDateTime(startDateTimestamp);
        var endDateTime = TimestampConverter.INSTANCE.toLocalDateTime(endDateTimestamp);
        return !serviceDate.isBefore(startDateTime) && !serviceDate.isAfter(endDateTime);
    }

    private static List<LodgingInfo> getLodgingInfos(
            Map<Long, Pair<LodgingUnitModel, LodgingTypeModel>> lodgingMap, List<Long> lodgingUnitId) {
        return lodgingUnitId.stream()
                .map(lodgingMap::get)
                .filter(Objects::nonNull)
                .map(pair -> {
                    LodgingUnitModel lodgingUnit = pair.getFirst();
                    LodgingTypeModel lodgingType = pair.getSecond();
                    var lodgingInfo = new LodgingInfo();
                    lodgingInfo.setLodgingUnitId(lodgingUnit.getId());
                    lodgingInfo.setLodgingUnitName(lodgingUnit.getName());
                    lodgingInfo.setLodgingTypeId(lodgingType.getId());
                    lodgingInfo.setLodgingTypeName(lodgingType.getName());
                    lodgingInfo.setSort(lodgingUnit.getSort());
                    return lodgingInfo;
                })
                .toList();
    }

    /**
     * 针对 pet detail 里相同 service 和 price 的数据，在 order item 里数据处理不一样
     * 旧 invoice：不同 pet 合并成一个 item，quantity 为总和，item:petDetail 为 1:n
     * 新 invoice：不同 pet 不合并，item:petDetail 为 1:1
     *
     * @param petDetailList     pet detail list
     * @param invoiceItem       invoice item detail
     * @param boundPetDetailIds bound pet detail ids
     * @return de-duplicate pet detail list
     */
    static List<OrderItemPetDetailDTO> getUniquePetDetailList(
            List<OrderItemPetDetailDTO> petDetailList,
            OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            Set<Integer> boundPetDetailIds) {
        List<OrderItemPetDetailDTO> filteredPetDetails = petDetailList;
        if (isOldInvoice(invoiceItem)) {
            // 确保 invoice item 映射的 pet detail 里，每个 pet 只有一个
            filteredPetDetails = petDetailList.stream()
                    .filter(p -> !boundPetDetailIds.contains(p.getPetDetailId()))
                    .collect(groupingBy(OrderItemPetDetailDTO::getPetId))
                    .values()
                    .stream()
                    .map(CollectionUtils::firstElement)
                    .toList();
        }
        filteredPetDetails.forEach(p -> boundPetDetailIds.add(p.getPetDetailId()));
        return filteredPetDetails;
    }

    private static boolean isOldInvoice(OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem) {
        return PrimitiveTypeUtil.isNullOrZero(invoiceItem.getPetId());
    }

    private static Predicate<MoeGroomingPetDetail> matchPetDetailPrice(
            OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            List<PricingRuleApplyLogModel> applyLogs,
            final List<BoardingSplitLodgingModel> boardingSplitLodgings) {
        return petDetail -> {
            var serviceIsSame = petDetail.getServiceId().equals(invoiceItem.getObjectId());
            if (!serviceIsSame) {
                return false;
            }
            // 多个 pet detail 对应同一个 service id 和 price
            if (petDetail.getServicePrice().compareTo(invoiceItem.getUnitPrice()) == 0) {
                return true;
            }
            boolean priceMatchPricingRuleApplyLog = applyLogs.stream()
                    .anyMatch(applyLog -> Objects.equals(
                                    petDetail.getServiceId().longValue(), applyLog.getServiceId())
                            && Objects.equals(petDetail.getPetId().longValue(), applyLog.getPetId())
                            && BigDecimal.valueOf(applyLog.getAdjustedPrice()).compareTo(invoiceItem.getUnitPrice())
                                    == 0);
            // 多个 pet detail 对应同一个 service id，但 price 和 invoice 不一致，但存在对应的 pricing rule apply log
            if (priceMatchPricingRuleApplyLog) {
                return true;
            }

            return boardingSplitLodgings.stream()
                    .anyMatch(boardingSplitLodging ->
                            Objects.equals(petDetail.getId().longValue(), boardingSplitLodging.getPetDetailId())
                                    && MoneyUtils.fromGoogleMoney(boardingSplitLodging.getPrice())
                                                    .compareTo(invoiceItem.getUnitPrice())
                                            == 0);
        };
    }

    private List<OrderItemPetEvaluationDetailDTO> buildPetEvaluationDetailOrderItems(
            List<EvaluationServiceDetail> evaluations,
            OrderInvoiceSummaryDTO.OrderItemDTO invoiceItem,
            Map<Integer, CustomerPetDetailDTO> petMap) {
        Integer invoicePetId = invoiceItem.getPetId();

        return evaluations.stream()
                .filter(evaluation -> evaluation
                                .getServiceId()
                                .equals(invoiceItem.getObjectId().longValue())
                        && evaluation.getServicePrice().compareTo(invoiceItem.getUnitPrice()) == 0
                        && (invoicePetId == null
                                || invoicePetId == 0
                                || invoicePetId.equals(evaluation.getPetId().intValue())))
                .filter(evaluation -> petMap.containsKey(evaluation.getPetId().intValue()))
                .map(evaluation -> {
                    CustomerPetDetailDTO pet = petMap.get(evaluation.getPetId().intValue());
                    OrderItemPetEvaluationDetailDTO dto = new OrderItemPetEvaluationDetailDTO();
                    dto.setPetId(pet.getPetId());
                    dto.setPetName(pet.getPetName());
                    dto.setPetBreed(pet.getPetBreed());
                    dto.setServiceId(evaluation.getServiceId().intValue());
                    dto.setServiceTime(evaluation.getServiceTime());
                    dto.setServicePrice(evaluation.getServicePrice());
                    dto.setStartTime(evaluation.getStartTime().longValue());
                    dto.setServiceItemType(ServiceItemType.EVALUATION_VALUE);
                    if (null != evaluation.getStaffId()) {
                        dto.setStaffId(evaluation.getStaffId().intValue());
                    }
                    dto.setQuantity(invoiceItem.getQuantity()); // default 1 for evaluation item, not combined
                    return dto;
                })
                .toList();
    }

    private Map<Integer, MoeStaffDto> getStaffMap(Integer businessId, List<GroomingPetDetailDTO> groomingPetDetails) {
        if (CollectionUtils.isEmpty(groomingPetDetails)) {
            return Collections.emptyMap();
        }
        // 查询staff信息
        Set<Integer> staffIds = groomingPetDetails.stream()
                .map(GroomingPetDetailDTO::getStaffId)
                .collect(Collectors.toSet());
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        staffIdListParams.setStaffIdList(new ArrayList<>(staffIds));
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);
        return staffList.stream().collect(Collectors.toMap(MoeStaffDto::getId, s -> s));
    }

    private Map<Long, Pair<LodgingUnitModel, LodgingTypeModel>> getLodgingMap(List<Long> lodgingIds) {
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return Map.of();
        }
        MGetLodgingUnitResponse response = lodgingUnitService.mGetLodgingUnit(
                MGetLodgingUnitRequest.newBuilder().addAllIdList(lodgingIds).build());
        Map<Long, LodgingTypeModel> lodgingTypeMap = response.getLodgingTypeListList().stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity(), (a, b) -> a));
        return response.getLodgingUnitListList().stream()
                .collect(Collectors.toMap(
                        LodgingUnitModel::getId,
                        lodgingUnit -> {
                            LodgingTypeModel lodgingType = lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
                            if (lodgingType == null) {
                                throw ExceptionUtil.bizException(Code.CODE_LODGING_TYPE_NOT_FOUND);
                            }
                            return Pair.of(lodgingUnit, lodgingType);
                        },
                        (a, b) -> a));
    }

    public List<MoeGroomingInvoice> queryInvoiceByGroomingIds(
            Integer businessId, List<Integer> groomingIds, String type) {
        return orderService.getListByGroomingIds(businessId, groomingIds, type);
    }

    public Map<Integer, BigDecimal> querySubtotalByGroomingIds(
            long companyId, Integer businessId, List<Integer> appointmentIds, String type) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        if (!newOrderHelper.isNewOrder(companyId)) {
            return orderService.getListByGroomingIds(businessId, appointmentIds, type).stream()
                    .collect(
                            Collectors.toMap(MoeGroomingInvoice::getGroomingId, MoeGroomingInvoice::getSubTotalAmount));
        }

        return appointmentStub
                .previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllAppointmentIds(
                                appointmentIds.stream().map(Integer::longValue).toList())
                        .build())
                .getEstimatedOrdersList()
                .stream()
                .collect(toMap(
                        o -> Math.toIntExact(o.getAppointmentId()),
                        o -> MoneyUtils.fromGoogleMoney(o.getEstimatedTotal())));
    }

    public ResponseResult<GroomingInvoiceSendUrlDto> getInvoiceSendUrl(Integer invoiceId) {
        GroomingInvoiceSendUrlDto res = new GroomingInvoiceSendUrlDto();
        String encode = Des3Util.encode(upcomingKey, invoiceId.toString());
        String url = invoiceUrl + encode;
        res.setSendUrl(url);
        res.setId(encode);
        return ResponseResult.success(res);
    }

    /**
     * 根据 encode id 解析 invoice id 并根据 invoice id 查询 invoice
     *
     * @param encodeId
     * @return
     */
    public InvoicePayOnlineDTO queryInvoiceByInvoiceIdEncode(String encodeId) {
        // 解析 invoice id
        Integer invoiceId = parseInvoiceIdByEncodeId(encodeId);

        // 根据 invoice id 查询 invoice
        InvoicePayOnlineDTO invoice = buildInvoicePayOnlineDTO(invoiceId);
        if (invoice == null) {
            throw new CommonException(
                    ResponseCodeEnum.INVOICE_NOT_FOUND, "Not invoice for given id <" + encodeId + ">.");
        }
        return invoice;
    }

    /**
     * 根据 encode id 解析出 invoice id
     *
     * @param encodeId
     * @return
     */
    private Integer parseInvoiceIdByEncodeId(String encodeId) {
        String decodeId = Des3Util.decode(upcomingKey, encodeId);
        if (StringUtils.isBlank(decodeId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "encode id error");
        }
        return Integer.parseInt(decodeId);
    }

    /**
     * 根据 guid 查询 invoice detail（用于 pay online）
     * 合单支付复用此接口查询非 invoice 等信息
     *  invoices 信息调用 api-v3 新接口：rpc ListOnlineOrderDetail(ListOnlineOrderDetailParams)
     * @param guid
     * @return
     */
    public InvoicePayOnlineDTO queryInvoiceDetailByGuidForClient(String guid) {
        Integer invoiceId;
        DepositDto depositDto = null;
        // 押金账单
        if (guid.startsWith(DepositService.DEPOSIT_PREFIX)) {
            MoeInvoiceDeposit deposit = depositService.getDepositByGuid(guid);
            if (deposit == null) {
                throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND, "Not invoice for given id.");
            }
            // 如果预约已经被取消，则账单失效
            MoeGroomingInvoice moeGroomingInvoice =
                    orderService.getOrderById(deposit.getBusinessId(), deposit.getInvoiceId());
            MoeGroomingAppointment appointment = getAppointmentByGroomingId(moeGroomingInvoice.getGroomingId());
            if (AppointmentStatusEnum.CANCELED.getValue().equals(appointment.getStatus())) {
                throw new CommonException(ResponseCodeEnum.APPOINTMENT_CANCELED_INVOICE_INVALID);
            }

            invoiceId = deposit.getInvoiceId();
            depositDto = new DepositDto();
            BeanUtils.copyProperties(deposit, depositDto);
            // 用定金金额计算convenienceFee，用于C端获取convenienceFee
            depositDto.setInitProcessingFee(iPaymentService.getConvenienceFee(
                    deposit.getBusinessId(), deposit.getAmount(), PaymentStripeStatus.CARD_PAY));
        } else if (guid.startsWith("bulk_")) {
            com.moego.idl.service.order.v1.ListOrderDetailResponse resp = orderV2ServiceBlockingStub.listOrderDetail(
                    com.moego.idl.service.order.v2.ListOrderDetailRequest.newBuilder()
                            .setGuid(guid)
                            .build());
            if (CollectionUtils.isEmpty(resp.getOrdersList())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid bulk guid");
            }
            invoiceId = Math.toIntExact(resp.getOrders(0).getOrder().getId());
        } else {
            MoeGroomingInvoice moeGroomingInvoice = orderService.getOrderByGuid(guid);
            if (moeGroomingInvoice == null) {
                throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND, "Not invoice for given id.");
            }

            // 如果是预约账单（非违约金账单）,校验账单是否失效
            if (InvoiceStatusEnum.TYPE_APPOINTMENT.equals(moeGroomingInvoice.getType())) {
                MoeGroomingAppointment appointment = getAppointmentByGroomingId(moeGroomingInvoice.getGroomingId());
                if (AppointmentStatusEnum.CANCELED.getValue().equals(appointment.getStatus())) {
                    throw new CommonException(ResponseCodeEnum.APPOINTMENT_CANCELED_INVOICE_INVALID);
                }
                if (InvoiceStatusEnum.INVOICE_STATUS_REMOVED.equals(moeGroomingInvoice.getStatus())) {
                    throw new CommonException(ResponseCodeEnum.INVOICE_INVALID_STATUS, "Invoice has been canceled.");
                }
            }
            invoiceId = moeGroomingInvoice.getId();
        }

        return buildInvoicePayOnlineDTO(invoiceId, depositDto);
    }

    /**
     * 根据 grooming id 查询订阅详情
     *
     * @param groomingId
     * @return
     */
    private MoeGroomingAppointment getAppointmentByGroomingId(Integer groomingId) {
        return appointmentService.getAppointment(groomingId);
    }

    public MoeGroomingInvoice getInvoiceByPrimaryId(Integer invoiceId) {
        return orderService.getOrderById(null, invoiceId);
    }

    /**
     * 重载方法：通过invoiceId构建online invoice返回对象，无deposit信息
     *
     * @param invoiceId invoice id
     * @return
     */
    public InvoicePayOnlineDTO buildInvoicePayOnlineDTO(Integer invoiceId) {
        return buildInvoicePayOnlineDTO(invoiceId, null);
    }

    /**
     * 构建online invoice返回对象
     *
     * @param invoiceId
     * @param depositDto
     * @return
     */
    public InvoicePayOnlineDTO buildInvoicePayOnlineDTO(Integer invoiceId, DepositDto depositDto) {
        InvoiceSummaryDTO invoiceSummaryDTO = getById(invoiceId, null);
        // 查询business信息
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(invoiceSummaryDTO.getBusinessId());
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(infoIdParams);
        if (businessInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid business id for this invoice");
        }
        // 返回business时间格式
        BusinessPreferenceDto businessPreference =
                iBusinessBusinessClient.getBusinessPreference(invoiceSummaryDTO.getBusinessId());
        // get square info
        GetSquareTokenResponse squareInfo = iPaymentCreditCardClient.getToken(invoiceSummaryDTO.getBusinessId());

        // 查询payment setting
        PaymentSettingDTO paymentSetting = iPaymentSettingClient.getPaymentSetting(businessInfo.getId());
        PaymentSettingForClientDTO paymentSettingForClient = new PaymentSettingForClientDTO();
        BeanUtils.copyProperties(paymentSetting, paymentSettingForClient);

        // 设置invoice/deposit是否需要收processing fee
        if (depositDto != null) {
            depositDto.setRequiredProcessingFee(requiredProcessingFee(
                    businessInfo, paymentSetting, invoiceId, InvoiceStatusEnum.TYPE_ONLINE_DEPOSIT));
        } else {
            invoiceSummaryDTO.setRequiredProcessingFee(requiredProcessingFee(
                    businessInfo, paymentSetting, invoiceId, InvoiceStatusEnum.TYPE_ONLINE_INVOICE));
            if (Boolean.TRUE.equals(invoiceSummaryDTO.getRequiredProcessingFee())
                    && invoiceSummaryDTO.getInitProcessingFee() != null) {
                invoiceSummaryDTO.setTotalAmountWithFee(
                        invoiceSummaryDTO.getTotalAmountWithFee().add(invoiceSummaryDTO.getInitProcessingFee()));
                invoiceSummaryDTO.setAmountDue(
                        invoiceSummaryDTO.getAmountDue().add(invoiceSummaryDTO.getInitProcessingFee()));
            }
        }
        // 查询smart tip配置
        SmartTipConfigDTO tipConfig = iPaymentSettingClient.getSmartTipConfig(businessInfo.getId());
        SmartTipConfigForClientDTO tipConfigForClient = new SmartTipConfigForClientDTO();
        BeanUtils.copyProperties(tipConfig, tipConfigForClient);
        //  补充支付详情到 C 端
        invoiceSummaryDTO.setPaymentSummary(getGroomingPaymentSummaryForClient(businessInfo.getId(), invoiceId));
        // 如果包含 booking fee，修改 payment history
        if (!PrimitiveTypeUtil.isNullOrZero(invoiceSummaryDTO.getBookingFeePaymentId())) {
            invoiceSummaryDTO.getPaymentSummary().getPayments().forEach(payment -> {
                if (payment.getId().equals(invoiceSummaryDTO.getBookingFeePaymentId())) {
                    payment.setAmount(payment.getAmount().add(invoiceSummaryDTO.getBookingFeeAmount()));
                }
            });
        }
        // 为pay online 补充 discount 详情(包含 client credit）
        invoiceSummaryDTO.setDiscountList(orderService.getDiscountInfoList(invoiceId));
        return InvoicePayOnlineDTO.builder()
                .businessId(businessInfo.getId())
                .avatarPath(businessInfo.getAvatarPath())
                .businessName(businessInfo.getBusinessName())
                .businessInfo(businessInfo)
                .squareInfo(squareInfo)
                .invoiceInfo(invoiceSummaryDTO)
                .businessPreference(businessPreference)
                .stripeAccountId(iPaymentCreditCardClient.getStripeAccountId(invoiceSummaryDTO.getBusinessId()))
                .depositInfo(depositDto)
                .paymentSetting(paymentSettingForClient)
                .tipConfig(tipConfigForClient)
                .preferredTip(iCustomerCustomerClient.getPreferredTip(
                        businessInfo.getId(), invoiceSummaryDTO.getCustomerId()))
                .isInInvoiceWhiteList(bwListManager.isInWhiteList(
                        BWListManager.ORDER_REINVENT, businessInfo.getId().toString()))
                .build();
    }

    private PaymentSummary getGroomingPaymentSummaryForClient(Integer businessId, Integer invoiceId) {
        GetPaymentParams p = new GetPaymentParams();
        p.setModule(PaymentMethodEnum.MODULE_GROOMING);
        p.setInvoiceId(invoiceId);
        PaymentSummary paymentSummary = iPaymentService.getPayments(p).getData();
        if (paymentSummary == null) {
            return null;
        }
        List<PaymentSummary.PaymentDto> payments = paymentSummary.getPayments();
        if (CollectionUtils.isEmpty(payments)) {
            return paymentSummary;
        }

        // 标记 isPrePay tag
        Set<Integer> paymentIds =
                payments.stream().map(PaymentSummary.PaymentDto::getId).collect(Collectors.toSet());
        List<BookOnlineDepositDTO> prepayDTOs =
                bookOnlineDepositService.getOBDepositByPaymentIds(businessId, paymentIds);
        Set<Integer> prepayPaymentIds = prepayDTOs.stream()
                .filter(dto -> Objects.equals(DepositPaymentTypeEnum.PrePay, dto.getDepositType()))
                .map(BookOnlineDepositDTO::getPaymentId)
                .collect(Collectors.toSet());
        for (PaymentSummary.PaymentDto paymentDto : paymentSummary.getPayments()) {
            paymentDto.setIsPrepay(prepayPaymentIds.contains(paymentDto.getId()));
        }
        return paymentSummary;
    }

    /**
     * 获取 invoice guid，用于生成 pay online 链接，带 invoice 当前 updateTime
     */
    public GuidDto getInvoiceGuidDTO(Integer businessId, Integer invoiceId, Boolean requiredProcessingFee) {
        MoeGroomingInvoice invoice = orderService.getOrderById(businessId, invoiceId);

        if (StringUtils.isEmpty(invoice.getGuid())) {
            // 第一次生成guid，只在需要添加processing fee时新增记录
            if (Boolean.TRUE.equals(requiredProcessingFee)) {
                onlineFeeInvoiceService.addOrUpdateOnlineFeeRecord(
                        businessId, invoiceId, InvoiceStatusEnum.TYPE_ONLINE_INVOICE, requiredProcessingFee);
            }

            invoice.setGuid(CommonUtil.getUuid());
            orderService.updateOrderGuid(invoice);
            return new GuidDto().setGuid(invoice.getGuid()).setNeedRefreshInvoice(Boolean.TRUE);
        } else {
            if (requiredProcessingFee != null) {
                // 在已有guid的情况下，需要刷新requiredProcessingFee开关
                onlineFeeInvoiceService.addOrUpdateOnlineFeeRecord(
                        businessId, invoiceId, InvoiceStatusEnum.TYPE_ONLINE_INVOICE, requiredProcessingFee);
            }
            return new GuidDto().setGuid(invoice.getGuid()).setNeedRefreshInvoice(Boolean.FALSE);
        }
    }

    /**
     * 获取 invoice guid，用于生成 pay online链接
     */
    public String getInvoiceGuid(Integer businessId, Integer invoiceId, Boolean requiredProcessingFee) {
        return getInvoiceGuidDTO(businessId, invoiceId, requiredProcessingFee).getGuid();
    }

    public MoeGroomingInvoice queryInvoiceByGroomingIdAndType(Integer groomingId, String type) {
        if (StringUtils.isBlank(type)) {
            type = InvoiceStatusEnum.TYPE_APPOINTMENT;
        }
        return orderService.getOrderByGroomingIdAndType(groomingId, type);
    }

    /**
     * 判断是否需要加processing fee
     *
     * @param businessInfo
     * @param paymentSetting
     * @param invoiceId
     * @param onlineInvoiceType
     * @return
     */
    private Boolean requiredProcessingFee(
            MoeBusinessDto businessInfo, PaymentSettingDTO paymentSetting, Integer invoiceId, Byte onlineInvoiceType) {
        if (businessInfo == null || paymentSetting == null) {
            return false;
        }
        // 1.首选支付方式为stripe
        if (!PaymentMethodEnum.CARD_PROCESSOR_TYPE_STRIPE.equals(businessInfo.getPrimaryPayType())) {
            return false;
        }
        // 2.全局开关打开
        if (!PaymentSettingConst.PROCESSING_FEE_PAY_BY_CLIENT.equals(paymentSetting.getProcessingFeePayBy())) {
            return false;
        }
        // 3.单次开关打开
        MoeGroomingOnlineFeeInvoice onlineFeeInvoice =
                onlineFeeInvoiceService.selectOnlineFeeRecord(businessInfo.getId(), invoiceId, onlineInvoiceType);
        if (onlineFeeInvoice == null || !BooleanEnum.VALUE_TRUE.equals(onlineFeeInvoice.getRequiredFee())) {
            return false;
        }

        return true;
    }

    /**
     * 回退订单状态，以支持编辑 invoice 金额信息
     */
    public Boolean revertOrder(Integer businessId, Integer groomingId) {
        // 获取预约并检查
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(groomingId);
        if (moeGroomingAppointment == null
                || !moeGroomingAppointment.getBusinessId().equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        // 获取invoice并检查
        MoeGroomingInvoice moeGroomingInvoice = queryInvoiceByGroomingId(businessId, groomingId);
        if (moeGroomingInvoice == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND);
        }
        // 如果appt已取消，不能reopen，目前只能针对finish的预约reopen
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "appointment has been cancelled, please refresh and try again.");
        }

        // 不允许已经 complete 的 order 再次更新 status
        if (bwListManager.isInWhiteList(BWListManager.ORDER_REINVENT, businessId.toString())
                && InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(moeGroomingInvoice.getStatus())) {
            // 已经完成的订单不允许更新
            throw ExceptionUtil.bizException(Code.CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE, "Invoice is finished.");
        }

        /**
         * 设置 payment 状态为 partial paid(仅仅在full paid时)
         */
        if (GroomingAppointmentEnum.PAID.equals(moeGroomingAppointment.getIsPaid())) {
            MoeGroomingAppointment updateBean = new MoeGroomingAppointment();
            updateBean.setId(groomingId);
            updateBean.setUpdateTime(DateUtil.get10Timestamp());
            updateBean.setIsPaid(GroomingAppointmentEnum.PARTIAL_PAY);
            moeGroomingAppointmentMapper.updateByPrimaryKeySelective(updateBean);
        }

        // 修改invoice status为正在进行
        orderService.updateOrderStatus(moeGroomingInvoice.getId(), InvoiceStatusEnum.INVOICE_STATUS_PROCESSING);
        // reopen时失效掉tip split记录
        splitTipsService.invalidTipSplitRecord(businessId, moeGroomingInvoice.getId());

        // revert order 之后删除 redeem 记录
        moePackageService.deleteRedeemRecords(List.of(moeGroomingInvoice.getId()));

        return true;
    }
}
