package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_book_online_abandon_record_event_log
 */
public class MoeBookOnlineAbandonRecordEventLog {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_event_log.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   moe_book_online_abandon_record.id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_event_log.abandon_record_id
     *
     * @mbg.generated
     */
    private Integer abandonRecordId;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_book_online_abandon_record_event_log.created_at
     *
     * @mbg.generated
     */
    private Date createdAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_event_log.id
     *
     * @return the value of moe_book_online_abandon_record_event_log.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_event_log.id
     *
     * @param id the value for moe_book_online_abandon_record_event_log.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_event_log.abandon_record_id
     *
     * @return the value of moe_book_online_abandon_record_event_log.abandon_record_id
     *
     * @mbg.generated
     */
    public Integer getAbandonRecordId() {
        return abandonRecordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_event_log.abandon_record_id
     *
     * @param abandonRecordId the value for moe_book_online_abandon_record_event_log.abandon_record_id
     *
     * @mbg.generated
     */
    public void setAbandonRecordId(Integer abandonRecordId) {
        this.abandonRecordId = abandonRecordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_book_online_abandon_record_event_log.created_at
     *
     * @return the value of moe_book_online_abandon_record_event_log.created_at
     *
     * @mbg.generated
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_book_online_abandon_record_event_log.created_at
     *
     * @param createdAt the value for moe_book_online_abandon_record_event_log.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
