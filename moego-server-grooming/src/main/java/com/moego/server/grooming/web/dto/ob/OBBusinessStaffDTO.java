package com.moego.server.grooming.web.dto.ob;

import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/19
 */
@Data
public class OBBusinessStaffDTO {

    @NotNull
    @Schema(description = "business id")
    private Integer businessId;

    /**
     * business online booking name
     */
    private String obName;

    @Schema(description = "existing client id, or null for new user")
    private Integer customerId;

    @Valid
    @NotEmpty
    @Schema(description = "selected pet service")
    private List<@NotNull SelectedPetServiceDTO> petServiceList;

    @Valid
    @NotNull
    private OBTimeSlotSimpleDTO timeSlotParam;
}
