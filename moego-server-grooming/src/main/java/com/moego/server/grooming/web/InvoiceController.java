package com.moego.server.grooming.web;

import com.moego.common.dto.GuidDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.dto.InvoicePayOnlineDTO;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.dto.OrderInvoiceSummaryDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.OrderAction;
import com.moego.server.grooming.helper.OrderHelper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeInvoiceDeposit;
import com.moego.server.grooming.params.ApptReopenParams;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.grooming.service.DepositService;
import com.moego.server.grooming.service.InvoiceService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto;
import com.moego.server.grooming.service.packages.PackageApplyService;
import com.moego.server.grooming.web.vo.ApplyPackageServiceVo;
import com.moego.server.grooming.web.vo.CreateNoShowInvoiceVo;
import com.moego.server.grooming.web.vo.DeleteApplyPackageServiceVo;
import com.moego.server.grooming.web.vo.InvoiceIdVo;
import com.moego.server.grooming.web.vo.packages.InvoiceDetailVo;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/invoice")
public class InvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DepositService depositService;

    @Autowired
    private PackageApplyService packageApplyService;

    @Autowired
    private OrderHelper orderHelper;

    // DONE(Frank): 验证 groomingId -> businessId
    @PostMapping("/noshow")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_NO_SHOW,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#param.groomingId",
            details = "#param")
    public ResponseResult<InvoiceSummaryDTO> createNoShowInvoice(
            AuthContext context, @RequestBody CreateNoShowInvoiceVo param) {
        return ResponseResult.success(invoiceService.saveNoShowInvoice(
                context.companyId(), param.getGroomingId(), param.getAmount(), context.getStaffId()));
    }

    @PostMapping("/preauth/noshow")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_NO_SHOW,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#param.groomingId",
            details = "#param")
    public ResponseResult<InvoiceSummaryDTO> createNoShowInvoiceAndPreAuth(
            AuthContext context, @RequestBody CreateNoShowInvoiceVo param) {
        return ResponseResult.success(invoiceService.saveNoShowInvoiceAndPreAuth(
                context.companyId(), param.getGroomingId(), param.getAmount(), context.getStaffId()));
    }

    /**
     * 标记 检查并标记invoice状态
     * @param param
     * @return
     */
    @PostMapping("/place-order")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<InvoiceSummaryDTO> save(@RequestBody InvoiceIdVo param) {
        return ResponseResult.success(invoiceService.placeOrder(param.getInvoiceId()));
    }

    @PostMapping("/complete")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceSummaryDTO> setCompleted(AuthContext context, @RequestBody InvoiceIdVo param) {
        return ResponseResult.success(
                invoiceService.setCompleted(context.companyId(), context.getBusinessId(), param.getInvoiceId()));
    }

    @GetMapping("/detail")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceSummaryDTO> getById(AuthContext context, @RequestParam Integer invoiceId) {
        InvoiceSummaryDTO invoice = invoiceService.getById(invoiceId, context.getStaffId());
        if (invoice == null) {
            throw new CommonException(ResponseCodeEnum.INVOICE_NOT_FOUND, "Not invoice for given id.");
        }
        if (!context.getBusinessId().equals(invoice.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }
        invoice.setTotalAmount(invoice.getPaymentAmount());
        return ResponseResult.success(invoice);
    }

    // DONE(Frank): should be anonymous
    @GetMapping("/detail/encode")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<InvoicePayOnlineDTO> queryInvoiceByInvoiceIdEncode(@RequestParam String id) {
        return ResponseResult.success(invoiceService.queryInvoiceByInvoiceIdEncode(id));
    }

    // DONE(Frank): 此接口需要限制访问, 需要检查 invoiceId -> businessId
    @GetMapping("/send/url")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingInvoiceSendUrlDto> getInvoiceSendUrl(
            AuthContext context, @RequestParam Integer invoiceId) {
        MoeGroomingInvoice invoice = invoiceService.getInvoiceByPrimaryId(invoiceId);
        if (!context.getBusinessId().equals(invoice.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }

        return invoiceService.getInvoiceSendUrl(invoiceId);
    }

    // DONE(Frank): 此接口应匿名
    @GetMapping("/client/detail")
    @Operation(summary = "pay online for client 数据详情下发")
    @Auth(AuthType.ANONYMOUS)
    public InvoicePayOnlineDTO queryInvoiceByInvoiceGuid(@RequestParam String guid) {
        if (ObjectUtils.isEmpty(guid)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid url for pay online.");
        }
        return invoiceService.queryInvoiceDetailByGuidForClient(guid);
    }

    @GetMapping("/client/url")
    @Auth(AuthType.BUSINESS)
    public GuidDto getInvoiceGuid(
            AuthContext context,
            @RequestParam Integer invoiceId,
            @RequestParam(required = false) Boolean requiredProcessingFee) {
        return invoiceService.getInvoiceGuidDTO(context.getBusinessId(), invoiceId, requiredProcessingFee);
    }

    @GetMapping("/detailWithPayment")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public ResponseResult<InvoiceSummaryDTO> getWithPaymentById(AuthContext context, @RequestParam Integer invoiceId) {
        InvoiceSummaryDTO result = invoiceService.getWithPaymentById(invoiceId, context.getStaffId());
        if (!context.getBusinessId().equals(result.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.UNAUTHORIZED_ERROR, "Not invoice for given business.");
        }

        MoeInvoiceDeposit deposit = depositService.getDepositByInvoiceId(invoiceId);
        if (deposit != null) {
            DepositDto depositDto = new DepositDto();
            BeanUtils.copyProperties(deposit, depositDto);
            result.setDepositInfo(depositDto);
        }
        return ResponseResult.success(result);
    }

    @GetMapping("/order/detail")
    @Auth(AuthType.BUSINESS)
    @Deprecated
    public OrderInvoiceSummaryDTO getOrderDetail(AuthContext context, @RequestParam Integer orderId) {

        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        return invoiceService.getOrderDetailWithPaymentV1(companyId, businessId, staffId, orderId);
    }

    @GetMapping("/v2/order/detail")
    @Auth(AuthType.BUSINESS)
    public OrderInvoiceSummaryDTO getOrderDetailV2(AuthContext context, @RequestParam Integer orderId) {
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        return invoiceService.getOrderDetailWithPaymentV2(companyId, businessId, staffId, orderId);
    }

    @PostMapping("/set-discount")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceSummaryDTO> setDiscount(AuthContext context, @RequestBody InvoiceAmountVo param) {
        return ResponseResult.success(invoiceService.setDiscount(context.getBusinessId(), context.getStaffId(), param));
    }

    /**
     * 迁移至retail  /set-discount
     * @param context
     * @param param
     * @return
     */
    @PostMapping("/set-tips")
    @Auth(AuthType.ANONYMOUS)
    @Deprecated
    public ResponseResult<InvoiceSummaryDTO> setTips(AuthContext context, @RequestBody InvoiceAmountVo param) {
        return ResponseResult.success(invoiceService.setTips(context.getBusinessId(), context.getStaffId(), param));
    }

    // DONE(Frank): 需要检查 invoiceId -> businessId
    @PostMapping("/confirm-payment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceSummaryDTO> confirmPayment(AuthContext context, @RequestBody InvoiceIdVo param) {
        return ResponseResult.success(invoiceService.confirmPayment(context.companyId(), param.getInvoiceId()));
    }

    @GetMapping("/packageAvailable")
    @Auth(AuthType.BUSINESS)
    public Boolean hasAvailablePackage(@RequestParam Integer invoiceId) {
        orderHelper.checkOrder(invoiceId, AuthContext.get().companyId());
        return orderService.hasAvailablePackage(null, invoiceId);
    }

    @PostMapping("/apply-package")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<InvoiceSummaryDTO> applyPackage(AuthContext context, @RequestBody InvoiceIdVo param) {
        return ResponseResult.success(invoiceService.applyPackage(
                context.getBusinessId(), param.getInvoiceId(), context.getStaffId(), param.getCheckRefund()));
    }

    @PostMapping("/apply-package-by-items")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<String> applyPackageByItems(@RequestBody InvoiceDetailVo param) {
        packageApplyService.replaceApplyPackages(param.getInvoiceId(), param.getApplyPackages());
        return ResponseResult.success();
    }

    /**
     * @deprecated by Bryson, use {@link #removeApplyPackageServiceV2} instead
     */
    @DeleteMapping("/apply-package")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = OrderAction.CANCEL_APPLY_PACKAGE,
            resourceType = ResourceType.ORDER,
            resourceId = "#param.invoiceId",
            details = "#param")
    @Deprecated(since = "2025-02-07", forRemoval = true)
    public ResponseResult<InvoiceSummaryDTO> removeApplyPackageService(
            AuthContext context, @RequestBody ApplyPackageServiceVo param) {
        return ResponseResult.success(invoiceService.removeApplyPackageService(context.getBusinessId(), param));
    }

    @PutMapping("/revert")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.REOPEN,
            resourceType = ResourceType.APPOINTMENT,
            resourceId = "#apptReopenParams.groomingId")
    public ResponseResult<Boolean> revertInvoice(
            AuthContext context, @Validated @RequestBody ApptReopenParams apptReopenParams) {
        return ResponseResult.success(
                invoiceService.revertOrder(context.getBusinessId(), apptReopenParams.getGroomingId()));
    }

    @DeleteMapping("/v2/apply-package")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = OrderAction.CANCEL_APPLY_PACKAGE,
            resourceType = ResourceType.ORDER,
            resourceId = "#param.invoiceId",
            details = "#param")
    public ResponseResult<InvoiceSummaryDTO> removeApplyPackageServiceV2(
            AuthContext context, @RequestBody DeleteApplyPackageServiceVo param) {
        return ResponseResult.success(invoiceService.removeApplyPackageServiceV2(context.businessId(), param));
    }
}
