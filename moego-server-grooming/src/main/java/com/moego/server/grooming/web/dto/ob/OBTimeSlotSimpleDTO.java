package com.moego.server.grooming.web.dto.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class OBTimeSlotSimpleDTO {

    @NotNull
    @Pattern(message = "Invalid date format, valid example: 2022-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})$")
    @Schema(description = "start date of the query")
    private String startDate;

    @Deprecated
    @Max(365)
    @Schema(description = "the number of days for the furthest query, max value is 365")
    private Integer farthestDay;

    @Deprecated
    @Schema(description = "enable smart schedule flag")
    private boolean querySmartScheduling;

    @Schema(description = "latitude")
    private String lat;

    @Schema(description = "longitude")
    private String lng;

    @Schema(description = "zipcode")
    private String zipcode;
}
