<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingOnlineFeeInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="required_fee" jdbcType="TINYINT" property="requiredFee" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, invoice_id, type, required_fee, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_online_fee_invoice
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_online_fee_invoice
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_online_fee_invoice (business_id, invoice_id, type,
      required_fee, create_time, update_time,
      company_id)
    values (#{businessId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{type,jdbcType=TINYINT},
      #{requiredFee,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_online_fee_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="requiredFee != null">
        required_fee,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="requiredFee != null">
        #{requiredFee,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_online_fee_invoice
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="requiredFee != null">
        required_fee = #{requiredFee,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingOnlineFeeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_online_fee_invoice
    set business_id = #{businessId,jdbcType=INTEGER},
      invoice_id = #{invoiceId,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      required_fee = #{requiredFee,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByInvoiceIdAndType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_online_fee_invoice
    where business_id = #{businessId,jdbcType=INTEGER}
    and invoice_id = #{invoiceId,jdbcType=INTEGER}
    and type = #{type,jdbcType=TINYINT}
    limit 1
  </select>

  <select id="selectByInvoiceIdList" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM moe_grooming.moe_grooming_online_fee_invoice
    WHERE type = #{type,jdbcType=TINYINT}
    AND invoice_id IN
    <foreach close=")" collection="invoiceIdList" item="invoiceId" open="(" separator=",">
      #{invoiceId}
    </foreach>
  </select>

  <update id="batchUpdateRequiredFeeByInvoiceIdList">
    UPDATE moe_grooming_online_fee_invoice
    SET required_fee = #{requiredFee,jdbcType=TINYINT}
    WHERE type = #{type,jdbcType=TINYINT}
    AND invoice_id IN
    <foreach close=")" collection="invoiceIdList" item="invoiceId" open="(" separator=",">
      #{invoiceId}
    </foreach>
  </update>

  <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    INSERT INTO moe_grooming_online_fee_invoice (business_id, invoice_id, type,
    required_fee, company_id)
    VALUES
    <foreach collection="list" index="index" item="record" separator=",">
      (#{record.businessId}, #{record.invoiceId}, #{record.type},
      #{record.requiredFee}, #{record.companyId})
    </foreach>
  </insert>
</mapper>
