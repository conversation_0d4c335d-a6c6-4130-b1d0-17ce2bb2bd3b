package com.moego.server.grooming.utils;

import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.partitioningBy;

import com.moego.server.grooming.enums.AbandonRecordRecoverTypeEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@UtilityClass
public class OBAbandonedUtil {

    /**
     * 过滤 abandoned records。
     *
     * <p> 对于 Recovered 记录不需要过滤。
     * <p> 对于 Abandoned 记录，需要根据以下规则过滤：
     * <ul>
     *     <li>如果是 existing client，需要根据 customer id 过滤取最新</li>
     *     <li>如果是 new client，需要根据 phone number 过滤取最新</li>
     * </ul>
     *
     * @param records {@link MoeBookOnlineAbandonRecord}
     * @return filtered records
     */
    public static List<MoeBookOnlineAbandonRecord> filterLatestRecords(Collection<MoeBookOnlineAbandonRecord> records) {
        Map<Boolean, List<MoeBookOnlineAbandonRecord>> grouped = records.stream()
                .collect(partitioningBy(
                        r -> r.getRecoveryType().intValue() != AbandonRecordRecoverTypeEnum.ABANDONED.getValue()));

        /*
        - Recovered 记录不需要过滤
        - 对于 Abandoned 记录，
            1. 如果是 existing client，需要根据 customer id 过滤取最新
            2. 如果是 new client，需要根据 phone number 过滤取最新

        see https://moego.atlassian.net/wiki/spaces/MO/pages/215253113/Online+booking+abandon+user+PRD
         */
        List<MoeBookOnlineAbandonRecord> recoveredRecords = grouped.getOrDefault(true, List.of());
        List<MoeBookOnlineAbandonRecord> result = new ArrayList<>(recoveredRecords);

        List<MoeBookOnlineAbandonRecord> abandonedRecords = grouped.getOrDefault(false, List.of());

        Map<Boolean, List<MoeBookOnlineAbandonRecord>> existingClientRecords = abandonedRecords.stream()
                .collect(partitioningBy(r -> Objects.equals(
                        SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue(), r.getLeadType())));

        // existing 按照 customer id 过滤取最新的一条
        Map<Integer, MoeBookOnlineAbandonRecord> existingCustomerIdToRecord = new HashMap<>();
        existingClientRecords.getOrDefault(true, List.of()).stream()
                .filter(r -> r.getCustomerId() != null)
                .collect(groupingBy(MoeBookOnlineAbandonRecord::getCustomerId))
                .forEach((customerId, groupedRecords) -> groupedRecords.stream()
                        .max(comparing(MoeBookOnlineAbandonRecord::getAbandonTime, nullsFirst(naturalOrder())))
                        .ifPresent(r -> {
                            result.add(r);
                            existingCustomerIdToRecord.put(r.getCustomerId(), r);
                        }));

        // new 按照 phone number 过滤取最新的一条
        Map<Integer, MoeBookOnlineAbandonRecord> newCustomerIdToRecord = new HashMap<>();
        existingClientRecords.getOrDefault(false, List.of()).stream()
                .filter(r -> StringUtils.hasText(r.getPhoneNumber()))
                .collect(groupingBy(MoeBookOnlineAbandonRecord::getPhoneNumber))
                .forEach((phoneNumber, groupedRecords) -> groupedRecords.stream()
                        .max(comparing(MoeBookOnlineAbandonRecord::getAbandonTime, nullsFirst(naturalOrder())))
                        .ifPresent(r -> {
                            result.add(r);
                            if (r.getCustomerId() != null) {
                                newCustomerIdToRecord.merge(
                                        r.getCustomerId(),
                                        r,
                                        (oldV, newV) -> oldV.getAbandonTime().compareTo(newV.getAbandonTime()) > 0
                                                ? oldV
                                                : newV);
                            }
                        }));

        // 可能存在同一个 customer id 在 existing 和 new 两种类型下都有记录的情况，保留最新的一条
        List<MoeBookOnlineAbandonRecord> needRemoveRecords = new ArrayList<>();
        newCustomerIdToRecord.forEach(
                (customerId, r) -> existingCustomerIdToRecord.merge(customerId, r, (oldV, newV) -> {
                    if (oldV.getAbandonTime().compareTo(newV.getAbandonTime()) > 0) {
                        needRemoveRecords.add(newV);
                        return oldV;
                    } else {
                        needRemoveRecords.add(oldV);
                        return newV;
                    }
                }));

        result.removeAll(needRemoveRecords);

        return result;
    }
}
