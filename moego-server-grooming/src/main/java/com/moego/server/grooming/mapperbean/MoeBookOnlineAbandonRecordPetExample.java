package com.moego.server.grooming.mapperbean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MoeBookOnlineAbandonRecordPetExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public MoeBookOnlineAbandonRecordPetExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIsNull() {
            addCriterion("booking_flow_id is null");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIsNotNull() {
            addCriterion("booking_flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdEqualTo(String value) {
            addCriterion("booking_flow_id =", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotEqualTo(String value) {
            addCriterion("booking_flow_id <>", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdGreaterThan(String value) {
            addCriterion("booking_flow_id >", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("booking_flow_id >=", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLessThan(String value) {
            addCriterion("booking_flow_id <", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLessThanOrEqualTo(String value) {
            addCriterion("booking_flow_id <=", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdLike(String value) {
            addCriterion("booking_flow_id like", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotLike(String value) {
            addCriterion("booking_flow_id not like", value, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdIn(List<String> values) {
            addCriterion("booking_flow_id in", values, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotIn(List<String> values) {
            addCriterion("booking_flow_id not in", values, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdBetween(String value1, String value2) {
            addCriterion("booking_flow_id between", value1, value2, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andBookingFlowIdNotBetween(String value1, String value2) {
            addCriterion("booking_flow_id not between", value1, value2, "bookingFlowId");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNull() {
            addCriterion("pet_id is null");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNotNull() {
            addCriterion("pet_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdEqualTo(Integer value) {
            addCriterion("pet_id =", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotEqualTo(Integer value) {
            addCriterion("pet_id <>", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThan(Integer value) {
            addCriterion("pet_id >", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pet_id >=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThan(Integer value) {
            addCriterion("pet_id <", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThanOrEqualTo(Integer value) {
            addCriterion("pet_id <=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdIn(List<Integer> values) {
            addCriterion("pet_id in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotIn(List<Integer> values) {
            addCriterion("pet_id not in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdBetween(Integer value1, Integer value2) {
            addCriterion("pet_id between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pet_id not between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andIndexIdIsNull() {
            addCriterion("index_id is null");
            return (Criteria) this;
        }

        public Criteria andIndexIdIsNotNull() {
            addCriterion("index_id is not null");
            return (Criteria) this;
        }

        public Criteria andIndexIdEqualTo(Integer value) {
            addCriterion("index_id =", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdNotEqualTo(Integer value) {
            addCriterion("index_id <>", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdGreaterThan(Integer value) {
            addCriterion("index_id >", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("index_id >=", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdLessThan(Integer value) {
            addCriterion("index_id <", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdLessThanOrEqualTo(Integer value) {
            addCriterion("index_id <=", value, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdIn(List<Integer> values) {
            addCriterion("index_id in", values, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdNotIn(List<Integer> values) {
            addCriterion("index_id not in", values, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdBetween(Integer value1, Integer value2) {
            addCriterion("index_id between", value1, value2, "indexId");
            return (Criteria) this;
        }

        public Criteria andIndexIdNotBetween(Integer value1, Integer value2) {
            addCriterion("index_id not between", value1, value2, "indexId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNull() {
            addCriterion("service_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdIsNotNull() {
            addCriterion("service_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdEqualTo(Integer value) {
            addCriterion("service_id =", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotEqualTo(Integer value) {
            addCriterion("service_id <>", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThan(Integer value) {
            addCriterion("service_id >", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_id >=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThan(Integer value) {
            addCriterion("service_id <", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_id <=", value, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdIn(List<Integer> values) {
            addCriterion("service_id in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotIn(List<Integer> values) {
            addCriterion("service_id not in", values, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdBetween(Integer value1, Integer value2) {
            addCriterion("service_id between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andServiceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_id not between", value1, value2, "serviceId");
            return (Criteria) this;
        }

        public Criteria andAddonIdsIsNull() {
            addCriterion("addon_ids is null");
            return (Criteria) this;
        }

        public Criteria andAddonIdsIsNotNull() {
            addCriterion("addon_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAddonIdsEqualTo(String value) {
            addCriterion("addon_ids =", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsNotEqualTo(String value) {
            addCriterion("addon_ids <>", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsGreaterThan(String value) {
            addCriterion("addon_ids >", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsGreaterThanOrEqualTo(String value) {
            addCriterion("addon_ids >=", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsLessThan(String value) {
            addCriterion("addon_ids <", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsLessThanOrEqualTo(String value) {
            addCriterion("addon_ids <=", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsLike(String value) {
            addCriterion("addon_ids like", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsNotLike(String value) {
            addCriterion("addon_ids not like", value, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsIn(List<String> values) {
            addCriterion("addon_ids in", values, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsNotIn(List<String> values) {
            addCriterion("addon_ids not in", values, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsBetween(String value1, String value2) {
            addCriterion("addon_ids between", value1, value2, "addonIds");
            return (Criteria) this;
        }

        public Criteria andAddonIdsNotBetween(String value1, String value2) {
            addCriterion("addon_ids not between", value1, value2, "addonIds");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIsNull() {
            addCriterion("pet_type_id is null");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIsNotNull() {
            addCriterion("pet_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdEqualTo(Integer value) {
            addCriterion("pet_type_id =", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotEqualTo(Integer value) {
            addCriterion("pet_type_id <>", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdGreaterThan(Integer value) {
            addCriterion("pet_type_id >", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pet_type_id >=", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdLessThan(Integer value) {
            addCriterion("pet_type_id <", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdLessThanOrEqualTo(Integer value) {
            addCriterion("pet_type_id <=", value, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdIn(List<Integer> values) {
            addCriterion("pet_type_id in", values, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotIn(List<Integer> values) {
            addCriterion("pet_type_id not in", values, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdBetween(Integer value1, Integer value2) {
            addCriterion("pet_type_id between", value1, value2, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andPetTypeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pet_type_id not between", value1, value2, "petTypeId");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNull() {
            addCriterion("avatar_path is null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNotNull() {
            addCriterion("avatar_path is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathEqualTo(String value) {
            addCriterion("avatar_path =", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotEqualTo(String value) {
            addCriterion("avatar_path <>", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThan(String value) {
            addCriterion("avatar_path >", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThanOrEqualTo(String value) {
            addCriterion("avatar_path >=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThan(String value) {
            addCriterion("avatar_path <", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThanOrEqualTo(String value) {
            addCriterion("avatar_path <=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLike(String value) {
            addCriterion("avatar_path like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotLike(String value) {
            addCriterion("avatar_path not like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIn(List<String> values) {
            addCriterion("avatar_path in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotIn(List<String> values) {
            addCriterion("avatar_path not in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathBetween(String value1, String value2) {
            addCriterion("avatar_path between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotBetween(String value1, String value2) {
            addCriterion("avatar_path not between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andPetNameIsNull() {
            addCriterion("pet_name is null");
            return (Criteria) this;
        }

        public Criteria andPetNameIsNotNull() {
            addCriterion("pet_name is not null");
            return (Criteria) this;
        }

        public Criteria andPetNameEqualTo(String value) {
            addCriterion("pet_name =", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameNotEqualTo(String value) {
            addCriterion("pet_name <>", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameGreaterThan(String value) {
            addCriterion("pet_name >", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameGreaterThanOrEqualTo(String value) {
            addCriterion("pet_name >=", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameLessThan(String value) {
            addCriterion("pet_name <", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameLessThanOrEqualTo(String value) {
            addCriterion("pet_name <=", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameLike(String value) {
            addCriterion("pet_name like", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameNotLike(String value) {
            addCriterion("pet_name not like", value, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameIn(List<String> values) {
            addCriterion("pet_name in", values, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameNotIn(List<String> values) {
            addCriterion("pet_name not in", values, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameBetween(String value1, String value2) {
            addCriterion("pet_name between", value1, value2, "petName");
            return (Criteria) this;
        }

        public Criteria andPetNameNotBetween(String value1, String value2) {
            addCriterion("pet_name not between", value1, value2, "petName");
            return (Criteria) this;
        }

        public Criteria andBreedIsNull() {
            addCriterion("breed is null");
            return (Criteria) this;
        }

        public Criteria andBreedIsNotNull() {
            addCriterion("breed is not null");
            return (Criteria) this;
        }

        public Criteria andBreedEqualTo(String value) {
            addCriterion("breed =", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedNotEqualTo(String value) {
            addCriterion("breed <>", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedGreaterThan(String value) {
            addCriterion("breed >", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedGreaterThanOrEqualTo(String value) {
            addCriterion("breed >=", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedLessThan(String value) {
            addCriterion("breed <", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedLessThanOrEqualTo(String value) {
            addCriterion("breed <=", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedLike(String value) {
            addCriterion("breed like", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedNotLike(String value) {
            addCriterion("breed not like", value, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedIn(List<String> values) {
            addCriterion("breed in", values, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedNotIn(List<String> values) {
            addCriterion("breed not in", values, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedBetween(String value1, String value2) {
            addCriterion("breed between", value1, value2, "breed");
            return (Criteria) this;
        }

        public Criteria andBreedNotBetween(String value1, String value2) {
            addCriterion("breed not between", value1, value2, "breed");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("weight is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("weight is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(String value) {
            addCriterion("weight =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(String value) {
            addCriterion("weight <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(String value) {
            addCriterion("weight >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(String value) {
            addCriterion("weight >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(String value) {
            addCriterion("weight <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(String value) {
            addCriterion("weight <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLike(String value) {
            addCriterion("weight like", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotLike(String value) {
            addCriterion("weight not like", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<String> values) {
            addCriterion("weight in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<String> values) {
            addCriterion("weight not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(String value1, String value2) {
            addCriterion("weight between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(String value1, String value2) {
            addCriterion("weight not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andHairLengthIsNull() {
            addCriterion("hair_length is null");
            return (Criteria) this;
        }

        public Criteria andHairLengthIsNotNull() {
            addCriterion("hair_length is not null");
            return (Criteria) this;
        }

        public Criteria andHairLengthEqualTo(String value) {
            addCriterion("hair_length =", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthNotEqualTo(String value) {
            addCriterion("hair_length <>", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthGreaterThan(String value) {
            addCriterion("hair_length >", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthGreaterThanOrEqualTo(String value) {
            addCriterion("hair_length >=", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthLessThan(String value) {
            addCriterion("hair_length <", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthLessThanOrEqualTo(String value) {
            addCriterion("hair_length <=", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthLike(String value) {
            addCriterion("hair_length like", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthNotLike(String value) {
            addCriterion("hair_length not like", value, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthIn(List<String> values) {
            addCriterion("hair_length in", values, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthNotIn(List<String> values) {
            addCriterion("hair_length not in", values, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthBetween(String value1, String value2) {
            addCriterion("hair_length between", value1, value2, "hairLength");
            return (Criteria) this;
        }

        public Criteria andHairLengthNotBetween(String value1, String value2) {
            addCriterion("hair_length not between", value1, value2, "hairLength");
            return (Criteria) this;
        }

        public Criteria andVaccineListIsNull() {
            addCriterion("vaccine_list is null");
            return (Criteria) this;
        }

        public Criteria andVaccineListIsNotNull() {
            addCriterion("vaccine_list is not null");
            return (Criteria) this;
        }

        public Criteria andVaccineListEqualTo(String value) {
            addCriterion("vaccine_list =", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListNotEqualTo(String value) {
            addCriterion("vaccine_list <>", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListGreaterThan(String value) {
            addCriterion("vaccine_list >", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListGreaterThanOrEqualTo(String value) {
            addCriterion("vaccine_list >=", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListLessThan(String value) {
            addCriterion("vaccine_list <", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListLessThanOrEqualTo(String value) {
            addCriterion("vaccine_list <=", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListLike(String value) {
            addCriterion("vaccine_list like", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListNotLike(String value) {
            addCriterion("vaccine_list not like", value, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListIn(List<String> values) {
            addCriterion("vaccine_list in", values, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListNotIn(List<String> values) {
            addCriterion("vaccine_list not in", values, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListBetween(String value1, String value2) {
            addCriterion("vaccine_list between", value1, value2, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andVaccineListNotBetween(String value1, String value2) {
            addCriterion("vaccine_list not between", value1, value2, "vaccineList");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNull() {
            addCriterion("birthday is null");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNotNull() {
            addCriterion("birthday is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdayEqualTo(String value) {
            addCriterion("birthday =", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotEqualTo(String value) {
            addCriterion("birthday <>", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThan(String value) {
            addCriterion("birthday >", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThanOrEqualTo(String value) {
            addCriterion("birthday >=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThan(String value) {
            addCriterion("birthday <", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThanOrEqualTo(String value) {
            addCriterion("birthday <=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLike(String value) {
            addCriterion("birthday like", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotLike(String value) {
            addCriterion("birthday not like", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayIn(List<String> values) {
            addCriterion("birthday in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotIn(List<String> values) {
            addCriterion("birthday not in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayBetween(String value1, String value2) {
            addCriterion("birthday between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotBetween(String value1, String value2) {
            addCriterion("birthday not between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("gender is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("gender is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(Byte value) {
            addCriterion("gender =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(Byte value) {
            addCriterion("gender <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(Byte value) {
            addCriterion("gender >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(Byte value) {
            addCriterion("gender >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(Byte value) {
            addCriterion("gender <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(Byte value) {
            addCriterion("gender <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<Byte> values) {
            addCriterion("gender in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<Byte> values) {
            addCriterion("gender not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(Byte value1, Byte value2) {
            addCriterion("gender between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(Byte value1, Byte value2) {
            addCriterion("gender not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andFixedIsNull() {
            addCriterion("fixed is null");
            return (Criteria) this;
        }

        public Criteria andFixedIsNotNull() {
            addCriterion("fixed is not null");
            return (Criteria) this;
        }

        public Criteria andFixedEqualTo(String value) {
            addCriterion("fixed =", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotEqualTo(String value) {
            addCriterion("fixed <>", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedGreaterThan(String value) {
            addCriterion("fixed >", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedGreaterThanOrEqualTo(String value) {
            addCriterion("fixed >=", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedLessThan(String value) {
            addCriterion("fixed <", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedLessThanOrEqualTo(String value) {
            addCriterion("fixed <=", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedLike(String value) {
            addCriterion("fixed like", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotLike(String value) {
            addCriterion("fixed not like", value, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedIn(List<String> values) {
            addCriterion("fixed in", values, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotIn(List<String> values) {
            addCriterion("fixed not in", values, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedBetween(String value1, String value2) {
            addCriterion("fixed between", value1, value2, "fixed");
            return (Criteria) this;
        }

        public Criteria andFixedNotBetween(String value1, String value2) {
            addCriterion("fixed not between", value1, value2, "fixed");
            return (Criteria) this;
        }

        public Criteria andBehaviorIsNull() {
            addCriterion("behavior is null");
            return (Criteria) this;
        }

        public Criteria andBehaviorIsNotNull() {
            addCriterion("behavior is not null");
            return (Criteria) this;
        }

        public Criteria andBehaviorEqualTo(String value) {
            addCriterion("behavior =", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorNotEqualTo(String value) {
            addCriterion("behavior <>", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorGreaterThan(String value) {
            addCriterion("behavior >", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorGreaterThanOrEqualTo(String value) {
            addCriterion("behavior >=", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorLessThan(String value) {
            addCriterion("behavior <", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorLessThanOrEqualTo(String value) {
            addCriterion("behavior <=", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorLike(String value) {
            addCriterion("behavior like", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorNotLike(String value) {
            addCriterion("behavior not like", value, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorIn(List<String> values) {
            addCriterion("behavior in", values, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorNotIn(List<String> values) {
            addCriterion("behavior not in", values, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorBetween(String value1, String value2) {
            addCriterion("behavior between", value1, value2, "behavior");
            return (Criteria) this;
        }

        public Criteria andBehaviorNotBetween(String value1, String value2) {
            addCriterion("behavior not between", value1, value2, "behavior");
            return (Criteria) this;
        }

        public Criteria andVetNameIsNull() {
            addCriterion("vet_name is null");
            return (Criteria) this;
        }

        public Criteria andVetNameIsNotNull() {
            addCriterion("vet_name is not null");
            return (Criteria) this;
        }

        public Criteria andVetNameEqualTo(String value) {
            addCriterion("vet_name =", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameNotEqualTo(String value) {
            addCriterion("vet_name <>", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameGreaterThan(String value) {
            addCriterion("vet_name >", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameGreaterThanOrEqualTo(String value) {
            addCriterion("vet_name >=", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameLessThan(String value) {
            addCriterion("vet_name <", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameLessThanOrEqualTo(String value) {
            addCriterion("vet_name <=", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameLike(String value) {
            addCriterion("vet_name like", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameNotLike(String value) {
            addCriterion("vet_name not like", value, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameIn(List<String> values) {
            addCriterion("vet_name in", values, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameNotIn(List<String> values) {
            addCriterion("vet_name not in", values, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameBetween(String value1, String value2) {
            addCriterion("vet_name between", value1, value2, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetNameNotBetween(String value1, String value2) {
            addCriterion("vet_name not between", value1, value2, "vetName");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberIsNull() {
            addCriterion("vet_phone_number is null");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberIsNotNull() {
            addCriterion("vet_phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberEqualTo(String value) {
            addCriterion("vet_phone_number =", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberNotEqualTo(String value) {
            addCriterion("vet_phone_number <>", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberGreaterThan(String value) {
            addCriterion("vet_phone_number >", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("vet_phone_number >=", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberLessThan(String value) {
            addCriterion("vet_phone_number <", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("vet_phone_number <=", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberLike(String value) {
            addCriterion("vet_phone_number like", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberNotLike(String value) {
            addCriterion("vet_phone_number not like", value, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberIn(List<String> values) {
            addCriterion("vet_phone_number in", values, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberNotIn(List<String> values) {
            addCriterion("vet_phone_number not in", values, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberBetween(String value1, String value2) {
            addCriterion("vet_phone_number between", value1, value2, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("vet_phone_number not between", value1, value2, "vetPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andVetAddressIsNull() {
            addCriterion("vet_address is null");
            return (Criteria) this;
        }

        public Criteria andVetAddressIsNotNull() {
            addCriterion("vet_address is not null");
            return (Criteria) this;
        }

        public Criteria andVetAddressEqualTo(String value) {
            addCriterion("vet_address =", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressNotEqualTo(String value) {
            addCriterion("vet_address <>", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressGreaterThan(String value) {
            addCriterion("vet_address >", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressGreaterThanOrEqualTo(String value) {
            addCriterion("vet_address >=", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressLessThan(String value) {
            addCriterion("vet_address <", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressLessThanOrEqualTo(String value) {
            addCriterion("vet_address <=", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressLike(String value) {
            addCriterion("vet_address like", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressNotLike(String value) {
            addCriterion("vet_address not like", value, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressIn(List<String> values) {
            addCriterion("vet_address in", values, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressNotIn(List<String> values) {
            addCriterion("vet_address not in", values, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressBetween(String value1, String value2) {
            addCriterion("vet_address between", value1, value2, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andVetAddressNotBetween(String value1, String value2) {
            addCriterion("vet_address not between", value1, value2, "vetAddress");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameIsNull() {
            addCriterion("emergency_contact_name is null");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameIsNotNull() {
            addCriterion("emergency_contact_name is not null");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameEqualTo(String value) {
            addCriterion("emergency_contact_name =", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameNotEqualTo(String value) {
            addCriterion("emergency_contact_name <>", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameGreaterThan(String value) {
            addCriterion("emergency_contact_name >", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("emergency_contact_name >=", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameLessThan(String value) {
            addCriterion("emergency_contact_name <", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameLessThanOrEqualTo(String value) {
            addCriterion("emergency_contact_name <=", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameLike(String value) {
            addCriterion("emergency_contact_name like", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameNotLike(String value) {
            addCriterion("emergency_contact_name not like", value, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameIn(List<String> values) {
            addCriterion("emergency_contact_name in", values, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameNotIn(List<String> values) {
            addCriterion("emergency_contact_name not in", values, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameBetween(String value1, String value2) {
            addCriterion("emergency_contact_name between", value1, value2, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactNameNotBetween(String value1, String value2) {
            addCriterion("emergency_contact_name not between", value1, value2, "emergencyContactName");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneIsNull() {
            addCriterion("emergency_contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneIsNotNull() {
            addCriterion("emergency_contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneEqualTo(String value) {
            addCriterion("emergency_contact_phone =", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneNotEqualTo(String value) {
            addCriterion("emergency_contact_phone <>", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneGreaterThan(String value) {
            addCriterion("emergency_contact_phone >", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("emergency_contact_phone >=", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneLessThan(String value) {
            addCriterion("emergency_contact_phone <", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("emergency_contact_phone <=", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneLike(String value) {
            addCriterion("emergency_contact_phone like", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneNotLike(String value) {
            addCriterion("emergency_contact_phone not like", value, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneIn(List<String> values) {
            addCriterion("emergency_contact_phone in", values, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneNotIn(List<String> values) {
            addCriterion("emergency_contact_phone not in", values, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneBetween(String value1, String value2) {
            addCriterion("emergency_contact_phone between", value1, value2, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andEmergencyContactPhoneNotBetween(String value1, String value2) {
            addCriterion("emergency_contact_phone not between", value1, value2, "emergencyContactPhone");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersIsNull() {
            addCriterion("pet_question_answers is null");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersIsNotNull() {
            addCriterion("pet_question_answers is not null");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersEqualTo(String value) {
            addCriterion("pet_question_answers =", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersNotEqualTo(String value) {
            addCriterion("pet_question_answers <>", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersGreaterThan(String value) {
            addCriterion("pet_question_answers >", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersGreaterThanOrEqualTo(String value) {
            addCriterion("pet_question_answers >=", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersLessThan(String value) {
            addCriterion("pet_question_answers <", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersLessThanOrEqualTo(String value) {
            addCriterion("pet_question_answers <=", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersLike(String value) {
            addCriterion("pet_question_answers like", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersNotLike(String value) {
            addCriterion("pet_question_answers not like", value, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersIn(List<String> values) {
            addCriterion("pet_question_answers in", values, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersNotIn(List<String> values) {
            addCriterion("pet_question_answers not in", values, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersBetween(String value1, String value2) {
            addCriterion("pet_question_answers between", value1, value2, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andPetQuestionAnswersNotBetween(String value1, String value2) {
            addCriterion("pet_question_answers not between", value1, value2, "petQuestionAnswers");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_book_online_abandon_record_pet
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
