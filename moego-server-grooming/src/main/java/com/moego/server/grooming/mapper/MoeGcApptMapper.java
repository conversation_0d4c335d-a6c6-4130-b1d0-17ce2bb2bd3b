package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcAppt;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGcApptMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    int insert(MoeGcAppt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcAppt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    MoeGcAppt selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcAppt record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_appt
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcAppt record);

    List<MoeGcAppt> selectByCalendarIdGroomingId(
            @Param("businessId") Integer businessId,
            @Param("calendarId") Integer calendarId,
            @Param("groomingId") Integer groomingId);

    List<MoeGcAppt> selectByBusinessIdGroomingId(
            @Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);

    List<MoeGcAppt> selectImportBlockByEventId(
            @Param("businessId") Integer businessId,
            @Param("gcCalendarId") Integer gcCalendarId,
            @Param("importEventId") String importEventId);

    List<MoeGcAppt> selectDeletedImportBlockById(
            @Param("businessId") Integer businessId,
            @Param("gcCalendarId") Integer gcCalendarId,
            @Param("idList") List<Integer> idList);

    MoeGcAppt selectByBlockId(@Param("businessId") Integer businessId, @Param("groomingId") Integer groomingId);
}
