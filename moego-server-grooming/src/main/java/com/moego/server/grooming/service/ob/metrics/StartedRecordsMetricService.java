package com.moego.server.grooming.service.ob.metrics;

import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.service.dto.ob.OBMetricTimeRangeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class StartedRecordsMetricService implements IOBMetricsService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;

    @Override
    public Integer sumMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        int startedNotRecoveredRecords = abandonRecordMapper.countStartedNotRecoveredRecords(
                timeRangeDTO.businessId(), timeRangeDTO.startTime(), timeRangeDTO.endTime());
        int recoveredRecords = abandonRecordMapper.countRecoveredRecords(
                timeRangeDTO.businessId(),
                timeRangeDTO.startTime(),
                timeRangeDTO.endTime(),
                OBStepEnum.listRecoverableSteps());
        return startedNotRecoveredRecords + recoveredRecords;
    }

    @Override
    public Object proportionMetrics(OBMetricTimeRangeDTO timeRangeDTO) {
        return "";
    }

    @Override
    public OBMetricsEnum getMetricsName() {
        return OBMetricsEnum.started_records;
    }
}
