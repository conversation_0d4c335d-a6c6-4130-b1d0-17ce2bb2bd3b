package com.moego.server.grooming.web.params;

import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.URL;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Data
@Accessors(chain = true)
public class OBLandingPageMergeParams {

    @Valid
    @Schema(description = "Update biz settings profile params")
    private OBMergeProfileParams profile;

    @Schema(description = "Update biz settings working hours params")
    private BusinessWorkingHourDayDetailDTO workingHours;

    @Data
    @Accessors(chain = true)
    public static class OBMergeProfileParams {

        @URL
        @Size(max = 255)
        @Schema(description = "Business info logo path")
        private String avatarPath;

        @Size(max = 255)
        @Schema(description = "Business info name")
        private String businessName;

        @Size(max = 50)
        @Schema(description = "Business info phone number")
        private String phoneNumber;

        @Size(max = 255)
        @Schema(description = "Full address")
        private String address;

        @Size(max = 255)
        @Schema(description = "Address 1")
        private String address1;

        @Size(max = 255)
        @Schema(description = "Address 2")
        private String address2;

        @Size(max = 50)
        @Schema(description = "Address city")
        private String addressCity;

        @Size(max = 50)
        @Schema(description = "Address state")
        private String addressState;

        @Size(max = 50)
        @Schema(description = "Address zipcode")
        private String addressZipcode;

        @Size(max = 50)
        @Schema(description = "Address country")
        private String addressCountry;

        @Size(max = 50)
        @Schema(description = "Address latitude")
        private String addressLat;

        @Size(max = 50)
        @Schema(description = "Address longitude")
        private String addressLng;

        @Size(max = 255)
        @Schema(description = "Facebook")
        private String facebook;

        @Size(max = 255)
        @Schema(description = "Instagram")
        private String instagram;

        @Size(max = 1000)
        @Schema(description = "Google")
        private String google;

        @Size(max = 255)
        @Schema(description = "Yelp")
        private String yelp;

        @Size(max = 255)
        @Schema(description = "Website")
        private String website;
    }
}
