package com.moego.server.grooming.service.dto.client;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListCommonApptDTO extends BaseBusinessCustomerIdDTO {

    private String currentDate;

    private Integer currentMinutes;

    private Byte isDeprecate;

    private Byte isBlock;

    private Byte bookOnlineStatus;

    private Byte isWaitingList;

    private Integer source;

    private Integer createdById;

    private Byte status;

    private List<AppointmentStatusEnum> statusList;

    private Boolean isHistory;
}
