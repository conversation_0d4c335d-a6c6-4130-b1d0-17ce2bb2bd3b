package com.moego.server.grooming.service.report.migrate;

import com.github.pagehelper.PageHelper;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateQuestionsRequest;
import com.moego.backend.proto.fulfillment.v1.BatchMigrateQuestionsResponse;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestionSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.GetGroomingQuestionsByQuestionKeysRequest;
import com.moego.backend.proto.fulfillment.v1.GroomingQuestionUniqueKey;
import com.moego.server.grooming.config.ReportCardMigrateConfig;
import com.moego.server.grooming.convert.ReportCardMigrateConverter;
import com.moego.server.grooming.mapper.MoeGroomingReportQuestionMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.service.report.migrate.validation.ReportCardMigrateValidationService;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationError;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationResult;
import com.moego.server.grooming.service.report.migrate.validation.dto.ValidationSummary;
import com.moego.server.grooming.service.utils.ReportCardMigrateUtils;
import com.moego.server.grooming.web.dto.ReportCardMigrateResultDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Report Card 问题迁移服务
 * 负责将 moe_grooming_report_question 数据迁移到 fulfillment_report_question
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReportCardQuestionMigrateService {

    private final MoeGroomingReportQuestionMapper sourceMapper;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentServiceClient;
    private final ReportCardMigrateValidationService validationService;

    /**
     * 迁移问题数据
     * 需要处理template_id的关联关系，使用新表的ID
     */
    public ReportCardMigrateResultDTO migrate(Integer businessId, boolean isValidation) {
        LocalDateTime startTime = LocalDateTime.now();
        String taskId = "question_" + System.currentTimeMillis();

        log.info("开始迁移问题数据，taskId: {}, businessId: {}", taskId, businessId);

        try {
            // 1. 首先统计可用总记录数，用于进度跟踪
            long totalCount = getTotalQuestionCount(businessId);
            log.info("问题数据总数: {} 条，开始分页迁移", totalCount);

            if (totalCount == 0) {
                return ReportCardMigrateUtils.buildSuccessResult(
                        taskId,
                        startTime,
                        ReportCardMigrateConfig.TableNames.QUESTION,
                        ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_QUESTION,
                        ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_QUESTION,
                        0,
                        0,
                        0);
            }

            // 2. 分页查询并处理数据
            long migratedCount = 0;
            long skippedCount = 0;
            long failedCount = 0;
            int batchSize = ReportCardMigrateConfig.MigrationConfig.BATCH_SIZE;
            int offset = 0;
            int batchNumber = 1;

            while (true) {
                // 分页查询数据，避免一次性加载所有数据到内存
                List<MoeGroomingReportQuestion> batch = getQuestionBatch(businessId, offset, batchSize);

                if (batch.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }

                log.info("处理第 {} 批数据，偏移量: {}, 批次大小: {}, 实际查询到: {} 条", batchNumber, offset, batchSize, batch.size());

                // 选择模式
                if (isValidation) {
                    log.info("[validation] batch number: {}, size: {}", batchNumber, batch.size());
                    performBatchValidation(batch, batchNumber);
                } else {
                    // 处理当前批次的数据
                    BatchResult batchResult = processBatchQuestions(batch);
                    migratedCount += batchResult.migratedCount;
                    skippedCount += batchResult.skippedCount;
                    failedCount += batchResult.failedCount;

                    // 输出进度信息
                    long processedCount = migratedCount + skippedCount + failedCount;
                    int progressPercent = ReportCardMigrateUtils.calculateProgressPercent(processedCount, totalCount);
                    log.info(
                            "批次 {} 处理完成，当前进度: {} ({}/{}), 成功: {}, 跳过: {}, 失败: {}",
                            batchNumber,
                            progressPercent + "%",
                            processedCount,
                            totalCount,
                            batchResult.migratedCount,
                            batchResult.skippedCount,
                            batchResult.failedCount);
                }

                // 准备下一批次
                offset += batchSize;
                batchNumber++;

                // 如果查询到的数据少于批次大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    break;
                }
            }

            log.info("问题数据迁移完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}", totalCount, migratedCount, skippedCount, failedCount);

            return ReportCardMigrateUtils.buildSuccessResult(
                    taskId,
                    startTime,
                    ReportCardMigrateConfig.TableNames.QUESTION,
                    ReportCardMigrateConfig.SourceTables.MOE_GROOMING_REPORT_QUESTION,
                    ReportCardMigrateConfig.TargetTables.FULFILLMENT_REPORT_QUESTION,
                    totalCount,
                    migratedCount,
                    skippedCount);

        } catch (Exception e) {
            log.error("问题数据迁移过程中发生异常", e);
            return ReportCardMigrateUtils.buildFailureResult(taskId, startTime, e.getMessage());
        }
    }

    /**
     * 获取问题总数量
     */
    private long getTotalQuestionCount(Integer businessId) {
        return sourceMapper.countByBusinessId(businessId);
    }

    /**
     * 分页查询问题数据
     */
    private List<MoeGroomingReportQuestion> getQuestionBatch(Integer businessId, int offset, int limit) {
        // 计算页码（PageHelper 使用 1 基础的页码）
        int pageNum = (offset / limit) + 1;

        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(pageNum, limit, "id ASC");

        try {
            // 使用 Mapper 中已有的方法，PageHelper 会自动应用分页
            return sourceMapper.selectByBusinessId(businessId);
        } finally {
            // 清理 PageHelper 的线程本地变量
            PageHelper.clearPage();
        }
    }

    /**
     * 处理单个批次的问题数据
     */
    private BatchResult processBatchQuestions(List<MoeGroomingReportQuestion> batch) {
        long migratedCount = 0;
        long skippedCount = 0;
        long failedCount = 0;

        try {
            // 1. 首先获取所有相关的 template 信息，建立 template_id 映射关系

            var existingQuestions = fulfillmentServiceClient.getGroomingQuestionsByQuestionKeys(
                    GetGroomingQuestionsByQuestionKeysRequest.newBuilder()
                            .addAllQuestionKeys(batch.stream()
                                    .filter(question -> question.getCompanyId() != 0 && question.getBusinessId() != 0)
                                    .map(question -> GroomingQuestionUniqueKey.newBuilder()
                                            .setBusinessId(
                                                    question.getBusinessId().longValue())
                                            .setCompanyId(question.getCompanyId())
                                            .setTitle(question.getTitle())
                                            .build())
                                    .toList())
                            .build());

            // 4. 过滤出需要迁移的数据
            List<MoeGroomingReportQuestion> questionsToMigrate = new ArrayList<>();
            for (MoeGroomingReportQuestion question : batch) {
                boolean exists = existingQuestions.getQuestionsList().stream()
                        .anyMatch(existing -> existing.getBusinessId() == question.getBusinessId()
                                && existing.getCompanyId() == question.getCompanyId()
                                && existing.getTitle().equals(question.getTitle()));

                if (exists) {
                    log.info(
                            "问题已存在，跳过迁移: businessId={}, key={}, category={}, id={}",
                            question.getBusinessId(),
                            question.getKey(),
                            question.getCategory(),
                            question.getId());
                    skippedCount++;
                } else {
                    questionsToMigrate.add(question);
                }
            }

            if (!questionsToMigrate.isEmpty()) {
                // 5. 转换数据
                List<FulfillmentReportQuestionSync> convertedQuestions =
                        ReportCardMigrateConverter.INSTANCE.convertQuestions(questionsToMigrate).stream()
                                .filter(question -> question.getCompanyId() != 0 && question.getBusinessId() != 0)
                                .toList();

                if (!convertedQuestions.isEmpty()) {
                    // 6. 批量迁移
                    BatchMigrateQuestionsRequest request = BatchMigrateQuestionsRequest.newBuilder()
                            .addAllQuestions(convertedQuestions)
                            .build();

                    BatchMigrateQuestionsResponse response = fulfillmentServiceClient.batchMigrateQuestions(request);

                    migratedCount += response.getSuccessCount();
                    skippedCount += response.getSkippedCount();
                    failedCount += response.getFailedCount();

                    log.info(
                            "批量迁移完成: 成功={}, 跳过={}, 失败={}",
                            response.getSuccessCount(),
                            response.getSkippedCount(),
                            response.getFailedCount());

                    // 批量校验：每迁移一批数据后立即校验该批次
                    performBatchValidation(questionsToMigrate, convertedQuestions.size());
                }
            }

        } catch (Exception e) {
            log.error("批量迁移问题失败", e);
            failedCount += batch.size();
        }

        return new BatchResult(migratedCount, skippedCount, failedCount);
    }

    /**
     * 执行批量校验
     * 在每个批次迁移完成后立即调用，快速发现问题
     */
    private void performBatchValidation(List<MoeGroomingReportQuestion> batch, int batchNumber) {
        try {
            log.info("开始批量校验问题数据，批次: {}, 数量: {}", batchNumber, batch.size());

            ValidationResult validationResult = validationService.validateQuestionBatch(batch, batchNumber);

            if (validationResult.isSuccess()) {
                log.info("批量校验通过: {}", validationResult.getSummary());
            } else {
                log.warn("批量校验发现问题: {}", validationResult.getSummary());

                // 记录详细错误信息
                for (ValidationError error : validationResult.getErrors()) {
                    log.warn("校验错误: {}", error.getFullDescription());
                }

                handleValidationErrors(validationResult.getErrors(), batchNumber);
            }

        } catch (Exception e) {
            log.error("批量校验过程中发生异常，批次: {}", batchNumber, e);
            // 校验异常不应中断迁移流程，但需要记录
        }
    }

    /**
     * 处理校验错误
     * 根据错误类型和严重程度决定处理策略
     */
    private void handleValidationErrors(List<ValidationError> errors, int batchNumber) {
        int criticalErrors = 0;
        int warningErrors = 0;

        for (ValidationError error : errors) {
            switch (error.getErrorType()) {
                case DATA_MISSING:
                case SYSTEM_ERROR:
                    criticalErrors++;
                    break;
                case DATA_INTEGRITY:
                case BUSINESS_LOGIC:
                    criticalErrors++;
                    break;
                case DATA_CONSISTENCY:
                    warningErrors++;
                    break;
            }
        }

        if (criticalErrors > 0) {
            log.error("批次 {} 发现 {} 个严重错误，{} 个警告错误", batchNumber, criticalErrors, warningErrors);

            double errorRate = (double) criticalErrors / errors.size();
            if (errorRate > 0.1) { // 10% 错误率阈值
                log.error("批次 {} 错误率为: {}", batchNumber, errorRate * 100);
            }
        } else if (warningErrors > 0) {
            log.warn("批次 {} 发现 {} 个警告错误，可继续迁移", batchNumber, warningErrors);
        }
    }

    /**
     * 将校验信息添加到迁移结果中
     */
    private void addValidationInfoToResult(ReportCardMigrateResultDTO result, ValidationSummary validationSummary) {
        if (validationSummary == null) {
            return;
        }

        // 构建校验信息
        StringBuilder validationInfo = new StringBuilder();
        validationInfo.append("\n\n=== 问题数据校验结果 ===\n");
        validationInfo.append(validationSummary.getSummary());

        if (validationSummary.hasErrors()) {
            validationInfo.append("\n\n=== 校验错误详情 ===\n");
            for (ValidationError error : validationSummary.getErrors()) {
                validationInfo.append("- ").append(error.getFullDescription()).append("\n");
            }
        }

        if (validationSummary.getSamplingValidation() != null
                && validationSummary.getSamplingValidation().hasErrors()) {
            validationInfo.append("\n=== 抽样校验错误 ===\n");
            for (ValidationError error :
                    validationSummary.getSamplingValidation().getErrors()) {
                validationInfo.append("- ").append(error.getFullDescription()).append("\n");
            }
        }

        // 如果校验失败，更新结果状态和错误信息
        if (!validationSummary.isSuccess()) {
            result.setSuccess(false);
            String originalError = result.getErrorMessage();
            String enhancedError =
                    (originalError != null ? originalError : "问题数据迁移完成但校验发现问题") + validationInfo.toString();
            result.setErrorMessage(enhancedError);
        } else {
            // 校验成功时，也可以在错误信息中添加校验摘要（作为信息性内容）
            String originalError = result.getErrorMessage();
            if (originalError == null || originalError.isEmpty()) {
                result.setErrorMessage("迁移成功" + validationInfo.toString());
            } else {
                result.setErrorMessage(originalError + validationInfo.toString());
            }
        }
    }

    /**
     * 批次处理结果内部类
     */
    private record BatchResult(long migratedCount, long skippedCount, long failedCount) {}
}
