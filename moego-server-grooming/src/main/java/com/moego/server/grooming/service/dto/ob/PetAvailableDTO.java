package com.moego.server.grooming.service.dto.ob;

import com.moego.idl.models.smart_scheduler.v1.TimeSlotType;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;

@Data
public class PetAvailableDTO {

    private Boolean isAllPetsAvailable;

    private List<Set<Integer>> petAvailableSubList;

    private Map<Set<Integer>, List<TimeSlotType>> petSubListToTimeSlotTypeMap;
}
