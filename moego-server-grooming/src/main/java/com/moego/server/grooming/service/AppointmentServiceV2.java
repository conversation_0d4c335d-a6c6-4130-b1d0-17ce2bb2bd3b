package com.moego.server.grooming.service;

import static com.moego.server.grooming.constant.AppointmentStatusSet.IN_PROGRESS_STATUS_SET;
import static com.moego.server.grooming.enums.WaitListStatusEnum.APPTONLY;
import static com.moego.server.grooming.service.MoePetDetailService.SEQUENCE_WORK_MODE;

import com.moego.common.distributed.LockManager;
import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.RepeatModifyTypeEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.organization.v1.LocationDateTimeDef;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetWorkingLocationDateTimeRequest;
import com.moego.idl.service.organization.v1.GetWorkingLocationIdsByStaffIdsRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.dto.appointment.AppointmentOperationDTO;
import com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.PaymentStatusEnum;
import com.moego.server.grooming.enums.calendar.CardTypeEnum;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.listener.event.UpdateCustomerEvent;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample;
import com.moego.server.grooming.mapperbean.MoeWaitList;
import com.moego.server.grooming.params.CustomerServiceParams;
import com.moego.server.grooming.params.appointment.DeletePetParams;
import com.moego.server.grooming.params.appointment.EditAppointmentColorCodeParams;
import com.moego.server.grooming.params.appointment.EditAppointmentPetDetailsParams;
import com.moego.server.grooming.params.appointment.EditPetParams;
import com.moego.server.grooming.params.appointment.PetParams;
import com.moego.server.grooming.params.appointment.QuickAddAppointmentParam;
import com.moego.server.grooming.params.appointment.ServiceAndOperationParams;
import com.moego.server.grooming.params.appointment.SetMultiPetsStartTime;
import com.moego.server.grooming.params.appointment.StaffUpcomingAppointmentCountParams;
import com.moego.server.grooming.params.appointment.StaffUpcomingOperationCountParams;
import com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2;
import com.moego.server.grooming.params.appointment.UpdateActionTimeParams;
import com.moego.server.grooming.params.appointment.WaitListRescheduleParams;
import com.moego.server.grooming.result.ServiceOperationListResult;
import com.moego.server.grooming.service.dto.CustomizedServiceParamDTO;
import com.moego.server.grooming.service.dto.ob.OBPrepayDetailDTO;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.service.ob.PrepayService;
import com.moego.server.grooming.service.storage.AppointmentStorageService;
import com.moego.server.grooming.web.params.calendar.CardRescheduleParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AppointmentServiceV2 {
    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private GroomingServiceService groomingServiceService;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private AppointmentMapperProxy moeGroomingAppointmentMapper;

    @Autowired
    private PetDetailMapperProxy moeGroomingPetDetailMapper;

    @Autowired
    private MoeGroomingServiceOperationMapper moeGroomingServiceOperationMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PrepayService prepayService;

    @Autowired
    private ActiveMQService mqService;

    @Autowired
    private GroomingApptAsyncService asyncService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private AppointmentStorageService appointmentStorageService;

    @Autowired
    private MoeGroomingNoteMapper moeGroomingNoteMapper;

    @Autowired
    private MoeGroomingAppointmentService appointmentService;

    @Autowired
    private MoeGroomingCustomerServicesService customerServicesService;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private WaitListService waitListService;

    @Autowired
    private CalendarCardService calendarCardService;

    @Autowired
    private LockManager moegoLockManager;

    @Autowired
    private OBAddressService obAddressService;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessClient;

    @Autowired
    private CalendarSyncService calendarSyncService;

    @Autowired
    private MoeBookOnlineDepositService moeBookOnlineDepositService;

    @Autowired
    private BrandedAppNotificationService brandedAppNotificationService;

    @Autowired
    private NewOrderHelper newOrderHelper;

    public Long quickAddAppointment(QuickAddAppointmentParam param) {
        // TODO: 这里的参数校验需要重新做，暂时先跳过
        //        checkParams(param);

        List<MoeGroomingPetDetail> petDetails = moePetDetailService.buildPetDetailList(
                param.petList(),
                param.businessId(),
                param.startDate(),
                param.startTime(),
                param.allPetsStartAtSameTime());
        MoeGroomingAppointment appointment = buildNewAppointment(param, petDetails);
        appointmentStorageService.insertAppointment(
                appointment, petDetails, buildServiceOperationList(petDetails, param.petList()));
        // 非 OB 创建的 appt 更新 customer 相关的 abandon records
        if (!Objects.equals(appointment.getSource(), GroomingAppointmentEnum.SOURCE_OB)) {
            ThreadPool.execute(() -> appointmentService.updateAbandonRecords4Customer(appointment));
            brandedAppNotificationService.pushNotification(
                    appointment.getId(), NotificationType.NOTIFICATION_TYPE_APPOINTMENT_BOOKED);
        }

        if (!Strings.isEmpty(param.alertNotes())) {
            MoeGroomingNote groomingNote = buildGroomingNote(
                    param.alertNotes(),
                    GroomingAppointmentEnum.NOTE_ALERT,
                    param.businessId(),
                    param.companyId(),
                    param.tokenStaffId(),
                    param.customerId().intValue(),
                    appointment.getId());
            moeGroomingNoteMapper.insertSelective(groomingNote);
        }

        if (!Strings.isEmpty(param.ticketComment())) {
            MoeGroomingNote groomingNote = buildGroomingNote(
                    param.ticketComment(),
                    GroomingAppointmentEnum.NOTE_COMMENT,
                    param.businessId(),
                    param.companyId(),
                    param.tokenStaffId(),
                    param.customerId().intValue(),
                    appointment.getId());
            moeGroomingNoteMapper.insertSelective(groomingNote);
        }
        Long invoiceId = null;
        if (newOrderHelper.enableNewOrder(param.companyId())) {
            var enablePreAuth = param.preAuthParams() != null
                    && Boolean.TRUE.equals(param.preAuthParams().preAuthEnable());
            if (enablePreAuth) {
                invoiceId = newOrderHelper.createPreAuthOrder(appointment.getId());
            }
        } else {
            invoiceId = orderService.saveOrderWhenCreatingAppointment(
                    param.companyId(), appointment.getBusinessId(), appointment.getId(), appointment.getCreatedById());
        }

        saveAndApplyCustomerService(appointment, param.tokenStaffId(), param.petList());

        if (invoiceId != null) {
            mqService.publishAppointmentEventV2(
                    param.preAuthParams(), appointment, invoiceId, AppointmentEventEnum.CREATE_SINGLE);
        }

        Set<Integer> staffIdList = param.petList().stream()
                .flatMap(pet -> pet.serviceList().stream().map(ServiceAndOperationParams::staffId))
                .collect(Collectors.toSet());
        asyncService.apptAddNotifyV2(staffIdList, appointment);
        asyncService.syncApptInfoToCalender(appointment); // quickAddAppointment
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId()));

        return appointment.getId().longValue();
    }

    /**
     * order 存在则更新 order；否则创建 order。
     * 从 waitList 转 appointment 时，order 可能已存在，此时更新 order 即可
     */
    public void onAppointmentCreated(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails, Integer updatedBy) {
        Long invoiceId = null;
        MoeGroomingInvoice existingInvoice =
                orderService.getOrderByGroomingIdAndType(appointment.getId(), InvoiceStatusEnum.TYPE_APPOINTMENT);
        if (existingInvoice == null) {
            // DONE new order flow
            if (!newOrderHelper.enableNewOrder(appointment.getCompanyId())) {
                invoiceId = orderService.saveOrderWhenCreatingAppointment(
                        appointment.getCompanyId(),
                        appointment.getBusinessId(),
                        appointment.getId(),
                        appointment.getCreatedById());
            }
        } else {
            invoiceId = Long.valueOf(existingInvoice.getId());

            // Fixes: https://moego.atlassian.net/browse/MER-2797
            orderService.updateOrderByGroomingId(
                    appointment.getCompanyId(), appointment.getBusinessId(), appointment.getId(), updatedBy);
        }

        mqService.publishAppointmentEventV2(null, appointment, invoiceId, AppointmentEventEnum.CREATE_SINGLE);

        Set<Integer> staffIdList =
                petDetails.stream().map(MoeGroomingPetDetail::getStaffId).collect(Collectors.toSet());
        asyncService.apptAddNotifyV2(staffIdList, appointment);
        asyncService.syncApptInfoToCalender(appointment);
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId()));
    }

    /**
     * 没有标记订单状态、释放product库存。
     * 目前用在 ob request / appointment 转 waitList 场景
     */
    public void onAppointmentCanceled(MoeGroomingAppointment oldAppointment) {
        ThreadPool.execute(() -> {
            asyncService.syncApptInfoToCalender(oldAppointment);
            publisher.publishEvent(new UpdateCustomerEvent(this)
                    .setBusinessId(oldAppointment.getBusinessId())
                    .setCustomerId(oldAppointment.getCustomerId()));
        });
        mqService.publishAppointmentCancelEvent(oldAppointment, null, true);
    }

    public void editAppointment(MoeGroomingAppointment record) {
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(record);
    }

    public void batchUpdateAppointment(List<MoeGroomingAppointment> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        moeGroomingAppointmentMapper.batchUpdateByPrimaryKeySelective(records);
    }

    public void editAppointmentPetAndService(EditAppointmentPetDetailsParams params) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (Objects.isNull(appointment) || !Objects.equals(appointment.getBusinessId(), params.businessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }
        // 保存第一个预约
        savePetDetails(appointment, params, true);

        // repeat 预约更新 apply to upcoming/all
        RepeatModifyTypeEnum repeatModifyType = RepeatModifyTypeEnum.getRepeatModifyTypeEnum(params.repeatType());
        if (repeatModifyType != null && appointment.getRepeatId() != null) {
            ThreadPool.execute(() -> {
                // 异步修改 upcoming/all
                List<MoeGroomingAppointment> updateAppointmentList =
                        switch (repeatModifyType) {
                            case THIS_AND_FOLLOWING -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                            case ALL -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), null);
                            default -> List.of();
                        };
                updateAppointmentList.stream()
                        .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                        .forEach(repeatAppointment -> {
                            EditAppointmentPetDetailsParams updateParams = params.toBuilder()
                                    .appointmentId(repeatAppointment.getId().longValue())
                                    .build();
                            savePetDetails(repeatAppointment, updateParams, false); // 后续预约更新不需要通知
                        });
            });
        }
    }

    private void savePetDetails(
            MoeGroomingAppointment appointment, EditAppointmentPetDetailsParams params, boolean needNotify) {
        List<MoeGroomingPetDetail> petDetails = moePetDetailService.buildPetDetailList(
                params.petList(),
                params.businessId(),
                appointment.getAppointmentDate(),
                appointment.getAppointmentStartTime(),
                params.allPetsStartAtSameTime());
        petDetails.forEach(petDetail -> {
            petDetail.setGroomingId(appointment.getId());
            petDetail.setStartDate(appointment.getAppointmentDate());
            petDetail.setEndDate(appointment.getAppointmentDate());
        });
        Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperationMap =
                buildServiceOperationList(petDetails, params.petList());
        appointment.setAppointmentEndTime(petDetails.stream()
                .map(p -> p.getEndTime().intValue())
                .max(Integer::compareTo)
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_SERVER_ERROR, String.format("get max end time failed, param: %s", params))));

        appointmentStorageService.updatePetDetails(appointment, petDetails, serviceOperationMap);

        ActivityLogRecorder.record(
                AppointmentAction.EDIT_PET_AND_SERVICES, ResourceType.APPOINTMENT, params.appointmentId(), params);

        orderService.updateOrderByGroomingId(
                appointment.getCompanyId(), appointment.getBusinessId(), appointment.getId(), params.tokenStaffId());

        if (needNotify) {
            asyncService.apptUpdateNotify(appointment, appointment, params.tokenStaffId());
        }
        asyncService.syncApptInfoToCalender(appointment); // editAppointmentPetAndService
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId()));
    }

    /**
     * 修改 appt 中的 pet 和 service，一次只能操作一只 pet，有以下几种操作方式：
     * 1、新增 pet，此时 originPetId 为 null
     * 2、替换 pet，此时 originPetId 为原 pet 的 id
     * 3、已有 pet 修改 service，此时 originPetId 与 petParams 中的 PetId 相同
     */
    public void editAppointmentPet(EditPetParams params) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (Objects.isNull(appointment) || !Objects.equals(appointment.getBusinessId(), params.businessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }
        // 保存 appointment pet 信息, 第一个 appointment 需要 notify
        saveAppointmentPet(appointment, params, true);

        // repeat 预约更新 apply to upcoming/all
        RepeatModifyTypeEnum repeatModifyType = RepeatModifyTypeEnum.getRepeatModifyTypeEnum(params.repeatType());
        if (repeatModifyType != null && appointment.getRepeatId() != null) {
            ThreadPool.execute(() -> {
                // 异步修改 upcoming/all
                List<MoeGroomingAppointment> updateAppointmentList =
                        switch (repeatModifyType) {
                            case THIS_AND_FOLLOWING -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                            case ALL -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), null);
                            default -> List.of();
                        };
                updateAppointmentList.stream()
                        .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                        .forEach(repeatAppointment -> {
                            EditPetParams updateParams = params.toBuilder()
                                    .appointmentId(repeatAppointment.getId().longValue())
                                    .build();
                            saveAppointmentPet(repeatAppointment, updateParams, false); // 后续预约更新不需要通知
                        });
            });
        }
        // 内部逻辑为异步更新
        saveAndApplyCustomerService(appointment, params.tokenStaffId(), List.of(params.petParams()));
    }

    private void saveAppointmentPet(MoeGroomingAppointment appointment, EditPetParams params, boolean needNotify) {
        // repeat 更新时，可能会存在参数没有 originPetId 但更新的 pet 在预约中已有，所以需要查询一次当前预约的 petDetail 来判断是新增还是更新
        List<MoeGroomingPetDetail> petDetails = moePetDetailService.queryPetDetailsByAppointmentId(appointment.getId());
        boolean isExistingPet = petDetails.stream()
                .anyMatch(pd -> Objects.equals(pd.getPetId(), params.petParams().petId()));

        if (!isExistingPet && (Objects.isNull(params.originPetId()) || params.originPetId() == 0)) {
            addAppointmentPet(appointment, params.petParams(), params.allPetsStartAtSameTime());
        } else {
            Integer originPetId = isExistingPet ? params.petParams().petId() : params.originPetId();
            editAppointmentPet(appointment, params.petParams(), params.allPetsStartAtSameTime(), originPetId);
        }

        ActivityLogRecorder.record(
                AppointmentAction.EDIT_PET_AND_SERVICES, ResourceType.APPOINTMENT, params.appointmentId(), params);

        // TODO new order flow
        if (!newOrderHelper.isNewOrder(appointment.getId())) {
            orderService.updateOrderByGroomingId(
                    appointment.getCompanyId(),
                    appointment.getBusinessId(),
                    appointment.getId(),
                    params.tokenStaffId());
        }

        if (needNotify) {
            asyncService.apptUpdateNotify(appointment, appointment, params.tokenStaffId());
        }
        asyncService.syncApptInfoToCalender(appointment); // editAppointmentPet
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId()));
    }

    /**
     * 在 appt 中新增一只 pet
     */
    private void addAppointmentPet(
            MoeGroomingAppointment existAppointment, PetParams petParams, Boolean allPetsStartAtSameTime) {
        // pet 的开始时间，如果是所有 pet 都从同一时间开始，则取 appt 的开始时间，否则取 appt 的结束时间
        int startTime = Objects.nonNull(allPetsStartAtSameTime) && allPetsStartAtSameTime
                ? existAppointment.getAppointmentStartTime()
                : existAppointment.getAppointmentEndTime();
        List<MoeGroomingPetDetail> petDetailList = moePetDetailService.buildPetDetailList(
                List.of(petParams),
                existAppointment.getBusinessId(),
                existAppointment.getAppointmentDate(),
                startTime,
                allPetsStartAtSameTime);
        petDetailList.forEach(petDetail -> {
            petDetail.setGroomingId(existAppointment.getId());
            petDetail.setStartDate(existAppointment.getAppointmentDate());
            petDetail.setEndDate(existAppointment.getAppointmentDate());
        });
        Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperationMap =
                buildServiceOperationList(petDetailList, List.of(petParams));
        // 预约新的结束时间，只需要比较原始的 appt endTime 和新增 pet 的 endTime，取更大者即可
        existAppointment.setAppointmentEndTime(Math.max(
                existAppointment.getAppointmentEndTime(),
                petDetailList.stream()
                        .map(MoeGroomingPetDetail::getEndTime)
                        .max(Long::compareTo)
                        .get()
                        .intValue()));
        appointmentStorageService.addAppointmentPet(existAppointment, petDetailList, serviceOperationMap);
    }

    /**
     * 编辑 appt 中的 pet，包括修改 pet 下的 service，或者更换 pet
     */
    private void editAppointmentPet(
            MoeGroomingAppointment existAppointment,
            PetParams petParams,
            Boolean allPetsStartAtSameTime,
            Integer originPetId) {
        // 读取已有的 pet detail 信息
        List<MoeGroomingPetDetail> existPetDetails =
                moePetDetailService.queryPetDetailsByAppointmentId(existAppointment.getId());
        if (existPetDetails.stream().noneMatch(pd -> Objects.equals(pd.getPetId(), originPetId))) {
            // 如果修改的 petId 没有在 existingPetDetail 中，跳过更新
            log.warn("editAppointmentPet skip ticket {} for pet {}", existAppointment.getId(), originPetId);
            return;
        }

        // 读取已有的 service operation 信息
        MoeGroomingServiceOperationExample operationExample = new MoeGroomingServiceOperationExample();
        MoeGroomingServiceOperationExample.Criteria operationCriteria = operationExample.createCriteria();
        operationCriteria.andGroomingIdEqualTo(existAppointment.getId());
        List<MoeGroomingServiceOperation> existServiceOperations =
                moeGroomingServiceOperationMapper.selectByExample(operationExample);

        // 针对要修改的 pet，计算修改前的开始时间和结束时间
        Long originStartTime = existPetDetails.stream()
                .filter(petDetail -> petDetail.getPetId().equals(originPetId))
                .map(MoeGroomingPetDetail::getStartTime)
                .min(Long::compareTo)
                .orElse(0L);
        Long originEndTime = existPetDetails.stream()
                .filter(petDetail -> petDetail.getPetId().equals(originPetId))
                .map(MoeGroomingPetDetail::getEndTime)
                .max(Long::compareTo)
                .orElse(0L);

        List<MoeGroomingPetDetail> petDetailList = moePetDetailService.buildPetDetailList(
                List.of(petParams),
                existAppointment.getBusinessId(),
                existAppointment.getAppointmentDate(),
                originStartTime.intValue(),
                allPetsStartAtSameTime);
        petDetailList.forEach(petDetail -> {
            petDetail.setGroomingId(existAppointment.getId());
            petDetail.setStartDate(existAppointment.getAppointmentDate());
            petDetail.setEndDate(existAppointment.getAppointmentDate());
        });
        Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperationMap =
                buildServiceOperationList(petDetailList, List.of(petParams));

        Long newEndTime = petDetailList.stream()
                .map(MoeGroomingPetDetail::getEndTime)
                .max(Long::compareTo)
                .orElse(0L);

        // 位于要修改的 pet 后面的 pet detail，需要进行平移，平移的时间跨度为新的 endTime - 原始的 endTime
        List<MoeGroomingPetDetail> petDetailsNeedToUpdate = (Objects.nonNull(allPetsStartAtSameTime)
                        && allPetsStartAtSameTime)
                ? new ArrayList<>()
                : existPetDetails.stream()
                        .filter(petDetail -> petDetail.getStartTime() >= originEndTime)
                        .map(petDetail -> {
                            MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
                            moeGroomingPetDetail.setId(petDetail.getId());
                            moeGroomingPetDetail.setStartTime(petDetail.getStartTime() + newEndTime - originEndTime);
                            moeGroomingPetDetail.setEndTime(
                                    moeGroomingPetDetail.getStartTime() + petDetail.getServiceTime());
                            return moeGroomingPetDetail;
                        })
                        .toList();

        Map<Integer, List<MoeGroomingServiceOperation>> serviceOperationByPetDetailId = existServiceOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        List<MoeGroomingServiceOperation> operationsNeedToUpdate = petDetailsNeedToUpdate.stream()
                .filter(petDetail -> serviceOperationByPetDetailId.containsKey(petDetail.getId()))
                .flatMap(petDetail -> serviceOperationByPetDetailId.get(petDetail.getId()).stream())
                .map(operation -> {
                    MoeGroomingServiceOperation moeGroomingServiceOperation = new MoeGroomingServiceOperation();
                    moeGroomingServiceOperation.setId(operation.getId());
                    moeGroomingServiceOperation.setStartTime(
                            (int) (operation.getStartTime() + newEndTime - originEndTime));
                    return moeGroomingServiceOperation;
                })
                .toList();

        long newAppointmentEndTime;
        if (Objects.nonNull(allPetsStartAtSameTime) && allPetsStartAtSameTime) {
            // startAtSameTime 的情况下，比较当前 pet 和其他 pet 的 endTime 取最大值
            long otherPetMaxEndTime = existPetDetails.stream()
                    .filter(petDetail -> !petDetail.getPetId().equals(originPetId))
                    .map(MoeGroomingPetDetail::getEndTime)
                    .max(Long::compareTo)
                    .orElse(0L);
            newAppointmentEndTime = Math.max(newEndTime, otherPetMaxEndTime);
        } else {
            // 非 startAtSameTime 的情况下，比较所有 pet 加上 diff 后最新的 endTime，//todo 待确定这里为什么不更新其它 pet
            long diff = newEndTime - originEndTime;
            newAppointmentEndTime = existPetDetails.stream()
                    .map(petDetail -> {
                        if (petDetail.getStartTime() >= originStartTime) {
                            return petDetail.getEndTime() + diff;
                        }
                        return petDetail.getEndTime();
                    })
                    .max(Long::compareTo)
                    .orElse(newEndTime);
        }
        if (existAppointment.getAppointmentEndTime() != newAppointmentEndTime) {
            // 记录 appointment 旧的 appointmentDate、startTime、endTime
            existAppointment.setOldAppointmentDate(existAppointment.getAppointmentDate());
            existAppointment.setOldAppointmentStartTime(existAppointment.getAppointmentStartTime());
            existAppointment.setOldAppointmentEndTime(existAppointment.getAppointmentEndTime());
            // 更新 appointment 的 endTime
            existAppointment.setAppointmentEndTime((int) newAppointmentEndTime);
        }

        appointmentStorageService.editAppointmentPet(
                existAppointment,
                originPetId,
                petDetailList,
                serviceOperationMap,
                petDetailsNeedToUpdate,
                operationsNeedToUpdate);
    }

    public void deleteAppointmentPet(DeletePetParams params) {
        // 先读取 appointment 信息
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (Objects.isNull(appointment) || !Objects.equals(appointment.getBusinessId(), params.businessId())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("appointment not exist, id: %d", params.appointmentId()));
        }

        deleteAppointmentPet(appointment, params, true);

        // repeat 预约更新 apply to upcoming/all
        RepeatModifyTypeEnum repeatModifyType = RepeatModifyTypeEnum.getRepeatModifyTypeEnum(params.repeatType());
        if (repeatModifyType != null && appointment.getRepeatId() != null) {
            ThreadPool.execute(() -> {
                // 异步修改 upcoming/all
                List<MoeGroomingAppointment> updateAppointmentList =
                        switch (repeatModifyType) {
                            case THIS_AND_FOLLOWING -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                            case ALL -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), null);
                            default -> List.of();
                        };
                updateAppointmentList.stream()
                        .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                        .forEach(repeatAppointment -> {
                            DeletePetParams updateParams = params.toBuilder()
                                    .appointmentId(repeatAppointment.getId().longValue())
                                    .build();
                            deleteAppointmentPet(repeatAppointment, updateParams, false);
                        });
            });
        }
    }

    private void deleteAppointmentPet(MoeGroomingAppointment appointment, DeletePetParams params, boolean needNotify) {
        // 读取 Pet detail 信息
        List<MoeGroomingPetDetail> existPetDetails =
                moePetDetailService.queryPetDetailsByAppointmentId(appointment.getId());

        // 拿到要删除的 pet detail
        List<MoeGroomingPetDetail> petDetailToBeDeleted = existPetDetails.stream()
                .filter(petDetail -> petDetail.getPetId().equals(params.petId()))
                .toList();
        if (CollectionUtils.isEmpty(petDetailToBeDeleted)) {
            // 找不到 pet detail 时跳过更新操作
            log.warn("deleteAppointmentPet skip ticket {} for pet {}", appointment.getId(), params.petId());
            return;
        }

        // 计算要删除的 pet detail 的开始时间和结束时间
        long originStartTime = petDetailToBeDeleted.stream()
                .map(MoeGroomingPetDetail::getStartTime)
                .min(Long::compareTo)
                .orElse(appointment.getAppointmentStartTime().longValue());
        Long originEndTime = petDetailToBeDeleted.stream()
                .map(MoeGroomingPetDetail::getEndTime)
                .max(Long::compareTo)
                .orElse(appointment.getAppointmentEndTime().longValue());

        // 拿到删除之后还保留的 pet detail
        List<MoeGroomingPetDetail> petDetailsAfterDeleted = existPetDetails.stream()
                .filter(petDetail -> !petDetail.getPetId().equals(params.petId()))
                .toList();

        /**
         * 删除某个 pet 后，需要将 calendar 上位于该 pet 的卡片后面的卡片，向前平移，判断逻辑为
         * 1. 如果该 pet 的开始时间，晚于被删除的 pet 的结束时间，则需要平移
         * 2. 平移的时间跨度，为被删除 pet 的 endTime - startTime
         */
        List<MoeGroomingPetDetail> petDetailsNeedToUpdate =
                (Objects.nonNull(params.allPetsStartAtSameTime()) && params.allPetsStartAtSameTime())
                        ? new ArrayList<>()
                        : petDetailsAfterDeleted.stream()
                                .filter(petDetail -> petDetail.getStartTime() >= originEndTime)
                                .map(petDetail -> {
                                    MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail();
                                    moeGroomingPetDetail.setId(petDetail.getId());
                                    moeGroomingPetDetail.setStartTime(
                                            petDetail.getStartTime() - originEndTime + originStartTime);
                                    moeGroomingPetDetail.setEndTime(
                                            moeGroomingPetDetail.getStartTime() + petDetail.getServiceTime());
                                    return moeGroomingPetDetail;
                                })
                                .toList();
        MoeGroomingServiceOperationExample operationExample = new MoeGroomingServiceOperationExample();
        MoeGroomingServiceOperationExample.Criteria operationCriteria = operationExample.createCriteria();
        operationCriteria.andGroomingIdEqualTo(params.appointmentId().intValue());
        List<MoeGroomingServiceOperation> existServiceOperations =
                moeGroomingServiceOperationMapper.selectByExample(operationExample);
        Map<Integer, List<MoeGroomingServiceOperation>> serviceOperationByPetDetailId = existServiceOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        List<MoeGroomingServiceOperation> operationsNeedToUpdate = petDetailsNeedToUpdate.stream()
                .filter(petDetail -> serviceOperationByPetDetailId.containsKey(petDetail.getId()))
                .flatMap(petDetail -> serviceOperationByPetDetailId.get(petDetail.getId()).stream())
                .map(operation -> {
                    MoeGroomingServiceOperation moeGroomingServiceOperation = new MoeGroomingServiceOperation();
                    moeGroomingServiceOperation.setId(operation.getId());
                    moeGroomingServiceOperation.setStartTime(
                            (int) (operation.getStartTime() - originEndTime + originStartTime));
                    return moeGroomingServiceOperation;
                })
                .toList();

        appointment.setAppointmentEndTime(petDetailsAfterDeleted.stream()
                .map(petDetail -> {
                    if (petDetail.getStartTime() >= originEndTime) {
                        return petDetail.getEndTime() - originEndTime + originStartTime;
                    }
                    return petDetail.getEndTime();
                })
                .max(Long::compareTo)
                .get()
                .intValue());

        appointmentStorageService.editAppointmentPet(
                appointment, params.petId(), List.of(), Map.of(), petDetailsNeedToUpdate, operationsNeedToUpdate);

        ActivityLogRecorder.record(
                AppointmentAction.EDIT_PET_AND_SERVICES, ResourceType.APPOINTMENT, params.appointmentId(), params);

        orderService.updateOrderByGroomingId(
                appointment.getCompanyId(), appointment.getBusinessId(), appointment.getId(), params.tokenStaffId());

        if (needNotify) {
            asyncService.apptUpdateNotify(appointment, appointment, params.tokenStaffId());
        }
        asyncService.syncApptInfoToCalender(appointment); // deleteAppointmentPet
        publisher.publishEvent(new UpdateCustomerEvent(this)
                .setBusinessId(appointment.getBusinessId())
                .setCustomerId(appointment.getCustomerId()));
    }

    private void saveAndApplyCustomerService(
            MoeGroomingAppointment appointment, Integer tokenStaffId, List<PetParams> pets) {
        pets.forEach(pet -> pet.serviceList().forEach(service -> {
            if (Objects.nonNull(service.scopeTypePrice())
                    && !Objects.equals(service.scopeTypePrice(), ScopeModifyTypeEnum.DO_NOT_SAVE)) {
                CustomerServiceParams params = new CustomerServiceParams();
                params.setBusinessId(appointment.getBusinessId());
                params.setCompanyId(appointment.getCompanyId());
                params.setCreateBy(tokenStaffId);
                params.setCustomerId(appointment.getCustomerId());
                params.setPetId(pet.petId());
                params.setServiceId(service.serviceId());
                params.setServiceType(service.serviceType());
                params.setServiceFee(service.servicePrice());
                params.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_PRICE);
                customerServicesService.addCustomerService(params);

                String resourceCacheKey = moegoLockManager.getResourceKey(LockManager.PET_DETAIL, pet.petId());
                String randomValue = CommonUtil.getUuid();
                try {
                    if (moegoLockManager.lock(resourceCacheKey, randomValue)) {
                        ThreadPool.execute(() -> {
                            appointmentService.applyCustomerServicePrice(
                                    appointment.getBusinessId(),
                                    tokenStaffId,
                                    appointment.getAppointmentDate(),
                                    List.of(new CustomizedServiceParamDTO(
                                            pet.petId(),
                                            service.serviceId(),
                                            service.scopeTypePrice(),
                                            service.servicePrice(),
                                            service.scopeTypeTime(),
                                            service.serviceTime())));
                        });
                    } else {
                        throw new CommonException(
                                ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceCacheKey);
                    }
                } finally {
                    moegoLockManager.unlock(resourceCacheKey, randomValue);
                }
            }

            if (Objects.nonNull(service.scopeTypeTime())
                    && !Objects.equals(service.scopeTypeTime(), ScopeModifyTypeEnum.DO_NOT_SAVE)) {
                CustomerServiceParams params = new CustomerServiceParams();
                params.setBusinessId(appointment.getBusinessId());
                params.setCompanyId(appointment.getCompanyId());
                params.setCreateBy(tokenStaffId);
                params.setCustomerId(appointment.getCustomerId());
                params.setPetId(pet.petId());
                params.setServiceId(service.serviceId());
                params.setServiceType(service.serviceType());
                params.setServiceTime(service.serviceTime());
                params.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_TIME);
                customerServicesService.addCustomerService(params);

                String resourceCacheKey = moegoLockManager.getResourceKey(LockManager.PET_DETAIL, pet.petId());
                String randomValue = CommonUtil.getUuid();
                try {
                    if (moegoLockManager.lock(resourceCacheKey, randomValue)) {
                        ThreadPool.execute(() -> {
                            appointmentService.applyCustomServiceTime(
                                    appointment.getBusinessId(),
                                    appointment.getCustomerId(),
                                    appointment.getId(),
                                    appointment.getAppointmentDate(),
                                    List.of(new CustomizedServiceParamDTO(
                                            pet.petId(),
                                            service.serviceId(),
                                            service.scopeTypePrice(),
                                            service.servicePrice(),
                                            service.scopeTypeTime(),
                                            service.serviceTime())));
                        });
                    } else {
                        throw new CommonException(
                                ResponseCodeEnum.PARALLEL_ERROR, "get lock failed for " + resourceCacheKey);
                    }
                } finally {
                    moegoLockManager.unlock(resourceCacheKey, randomValue);
                }
            }
        }));
    }

    public void updateActionTime(UpdateActionTimeParams params) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (Objects.isNull(appointment)
                || appointment.getBusinessId().intValue() != params.businessId().intValue()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("appointment not exist, id: %d", params.appointmentId()));
        }

        switch (params.statusForTimeToChange()) {
            case CONFIRMED -> {
                if (Objects.isNull(appointment.getConfirmedTime()) || appointment.getConfirmedTime() == 0) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "need to confirm first");
                }
                MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
                appointmentToUpdate.setConfirmedTime(params.actionTime());
                appointmentToUpdate.setId(appointment.getId());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
                ActivityLogRecorder.record(
                        AppointmentAction.CHANGE_CONFIRM_TIME,
                        ResourceType.APPOINTMENT,
                        params.appointmentId(),
                        new ChangeTimeLogDTO(
                                appointment.getConfirmedTime(), params.actionTime(), AppointmentUpdatedBy.BY_BUSINESS));
            }
            case CHECK_IN -> {
                if (Objects.isNull(appointment.getCheckInTime()) || appointment.getCheckInTime() == 0) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "need to check in first");
                }
                MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
                appointmentToUpdate.setCheckInTime(params.actionTime());
                appointmentToUpdate.setId(appointment.getId());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
                ActivityLogRecorder.record(
                        AppointmentAction.CHANGE_CHECK_IN_TIME,
                        ResourceType.APPOINTMENT,
                        params.appointmentId(),
                        new ChangeTimeLogDTO(
                                appointment.getCheckInTime(), params.actionTime(), AppointmentUpdatedBy.BY_BUSINESS));
            }
            case READY -> {
                if (Objects.isNull(appointment.getReadyTime()) || appointment.getReadyTime() == 0) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "need to ready first");
                }
                MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
                appointmentToUpdate.setReadyTime(params.actionTime());
                appointmentToUpdate.setId(appointment.getId());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
                ActivityLogRecorder.record(
                        AppointmentAction.CHANGE_READY_TIME,
                        ResourceType.APPOINTMENT,
                        params.appointmentId(),
                        new ChangeTimeLogDTO(
                                appointment.getReadyTime(), params.actionTime(), AppointmentUpdatedBy.BY_BUSINESS));
            }
            case FINISHED -> {
                if (Objects.isNull(appointment.getCheckOutTime()) || appointment.getCheckOutTime() == 0) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "need to finish first");
                }
                MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
                appointmentToUpdate.setCheckOutTime(params.actionTime());
                appointmentToUpdate.setId(appointment.getId());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
                ActivityLogRecorder.record(
                        AppointmentAction.CHANGE_CHECK_OUT_TIME,
                        ResourceType.APPOINTMENT,
                        params.appointmentId(),
                        new ChangeTimeLogDTO(
                                appointment.getCheckOutTime(), params.actionTime(), AppointmentUpdatedBy.BY_BUSINESS));
            }
            case CANCELED -> {
                if (Objects.isNull(appointment.getCanceledTime()) || appointment.getCanceledTime() == 0) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "need to cancel first");
                }
                MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
                appointmentToUpdate.setCanceledTime(params.actionTime());
                appointmentToUpdate.setId(appointment.getId());
                moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
                ActivityLogRecorder.record(
                        AppointmentAction.CHANGE_CANCEL_TIME,
                        ResourceType.APPOINTMENT,
                        params.appointmentId(),
                        new ChangeTimeLogDTO(
                                appointment.getCanceledTime(), params.actionTime(), AppointmentUpdatedBy.BY_BUSINESS));
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "statusForTimeToChange error");
        }
    }

    public void updateColorCode(EditAppointmentColorCodeParams params) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (appointment == null || !Objects.equals(appointment.getBusinessId(), params.businessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not exist");
        }

        // 更新当前预约 color code
        updateAppointmentColorCode(appointment.getId(), params);

        // repeat 预约更新 apply to upcoming/all
        RepeatModifyTypeEnum repeatModifyType = RepeatModifyTypeEnum.getRepeatModifyTypeEnum(params.repeatType());
        if (repeatModifyType != null && appointment.getRepeatId() != null) {
            ThreadPool.execute(() -> {
                // 异步修改 upcoming/all
                List<MoeGroomingAppointment> updateAppointmentList =
                        switch (repeatModifyType) {
                            case THIS_AND_FOLLOWING -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                            case ALL -> moeGroomingAppointmentMapper.queryApptsByRepeatId(
                                    params.businessId(), appointment.getRepeatId(), null);
                            default -> List.of();
                        };
                updateAppointmentList.stream()
                        .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                        .forEach(repeatAppointment -> updateAppointmentColorCode(repeatAppointment.getId(), params));
            });
        }
    }

    private void updateAppointmentColorCode(Integer appointmentId, EditAppointmentColorCodeParams params) {
        MoeGroomingAppointment appointmentToUpdate = new MoeGroomingAppointment();
        appointmentToUpdate.setColorCode(params.colorCode());
        appointmentToUpdate.setId(appointmentId);
        appointmentToUpdate.setBusinessId(params.businessId());
        appointmentToUpdate.setUpdatedById(params.tokenStaffId().longValue());
        appointmentToUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingAppointmentMapper.updateByPrimaryKeySelective(appointmentToUpdate);
    }

    public ServiceOperationListResult getServiceOperationList(Integer businessId, Integer petDetailId) {
        MoeGroomingPetDetail petDetail = moeGroomingPetDetailMapper.selectByPrimaryKey(petDetailId);
        if (Objects.isNull(petDetail)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petDetailId not exist");
        }

        if (!petDetail.getEnableOperation()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petDetailId not enable operation");
        }

        List<MoeGroomingServiceOperation> serviceOperationList =
                moeGroomingServiceOperationMapper.selectByBusinessIdAndGroomingServiceId(businessId, petDetailId);
        if (CollectionUtils.isEmpty(serviceOperationList)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceOperationList not exist");
        }

        return new ServiceOperationListResult(
                petDetailId,
                petDetail.getPetId(),
                petDetail.getServiceId(),
                "", // TODO boarding: @zhangdong 确认这里的 service name
                serviceOperationList.stream()
                        .map(serviceOperation ->
                                new AppointmentOperationDTO(serviceOperation.getId(), serviceOperation.getStaffId()))
                        .collect(Collectors.toList()));
    }

    /**
     * 对于多只 pet 的 appointment，设置串行还是并行进行服务
     */
    public void setMultiPetsStartTime(SetMultiPetsStartTime params) {
        MoeGroomingAppointment appointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                params.appointmentId().intValue());
        if (Objects.isNull(appointment) || !Objects.equals(appointment.getBusinessId(), params.tokenBusinessId())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("appointment not exist, id: %d", params.appointmentId()));
        }

        List<MoeGroomingPetDetail> existPetDetails =
                moePetDetailService.queryPetDetailsByAppointmentId(appointment.getId());
        Map<Integer, List<MoeGroomingPetDetail>> petDetailsWithPetIdMap =
                existPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        List<Integer> existPetIds =
                existPetDetails.stream().map(MoeGroomingPetDetail::getPetId).toList();
        if (params.petIds().size() != existPetIds.size()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petIds size not match");
        }

        List<MoeGroomingServiceOperation> serviceOperations = new ArrayList<>();
        List<Integer> petDetailsIdsForOperation = existPetDetails.stream()
                .filter(MoeGroomingPetDetail::getEnableOperation)
                .map(MoeGroomingPetDetail::getId)
                .toList();
        if (CollectionUtils.isNotEmpty(petDetailsIdsForOperation)) {
            MoeGroomingServiceOperationExample operationExample = new MoeGroomingServiceOperationExample();
            operationExample
                    .createCriteria()
                    .andGroomingIdEqualTo(appointment.getId())
                    .andGroomingServiceIdIn(petDetailsIdsForOperation);
            serviceOperations = moeGroomingServiceOperationMapper.selectByExample(operationExample);
        }
        final Map<Integer, List<MoeGroomingServiceOperation>> serviceOperationMap = serviceOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));

        int startTime = appointment.getAppointmentStartTime();
        for (Integer petId : params.petIds()) {
            if (!petDetailsWithPetIdMap.containsKey(petId)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, String.format("petId %d not exist", petId));
            }
            List<MoeGroomingPetDetail> petDetails = petDetailsWithPetIdMap.get(petId);
            if (CollectionUtils.isEmpty(petDetails)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, String.format("petId %d not exist", petId));
            }
            petDetails.sort(Comparator.comparing(MoeGroomingPetDetail::getStartTime));
            int diff = petDetails.get(0).getStartTime().intValue() - startTime;
            petDetails.forEach(petDetail -> {
                petDetail.setStartTime(petDetail.getStartTime() - diff);
                petDetail.setEndTime(petDetail.getStartTime() + petDetail.getServiceTime());
                if (serviceOperationMap.containsKey(petDetail.getPetId())) {
                    List<MoeGroomingServiceOperation> operations = serviceOperationMap.get(petDetail.getPetId());
                    operations.forEach(operation -> {
                        operation.setStartTime(operation.getStartTime() - diff);
                    });
                }
            });
            // 获取最后一个 pet 的结束时间
            if (!params.allPetsStartAtSameTime()) {
                startTime = petDetails.get(petDetails.size() - 1).getEndTime().intValue();
            }
        }

        appointmentStorageService.updateMultiPetStartTime(
                appointment,
                petDetailsWithPetIdMap.values().stream().flatMap(List::stream).toList(),
                serviceOperationMap.values().stream().flatMap(List::stream).toList());
    }

    public StaffUpcomingAppointmentCountDTO queryStaffUpComingAppointmentCountV2(
            StaffUpcomingAppointmentCountParams params) {
        Integer tokenCompanyId = params.tokenCompanyId();
        Long staffId = params.staffId();
        List<Long> businessIds = params.businessIds();

        // 获取需要查询的 staff 的 working location ids
        var response =
                businessClient.getWorkingLocationIdsByStaffIds(GetWorkingLocationIdsByStaffIdsRequest.newBuilder()
                        .setCompanyId(tokenCompanyId)
                        .addStaffId(staffId)
                        .build());
        if (response.getStaffLocationIdsCount() == 0) {
            return buildEmptyUpcomingCountDTO(params);
        }
        List<Long> allWorkingLocationIds = response.getStaffLocationIds(0).getLocationIdList();
        // 如果没有指定 businessIds，则查询所有 working location 的 upcoming appointment
        if (CollectionUtils.isEmpty(businessIds)) {
            businessIds = allWorkingLocationIds;
        } else {
            businessIds.retainAll(allWorkingLocationIds);
        }
        if (CollectionUtils.isEmpty(businessIds)) {
            return buildEmptyUpcomingCountDTO(params);
        }

        var dateTimeResponse = businessClient.getWorkingLocationDateTime(GetWorkingLocationDateTimeRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .build());
        Map<Long, LocationDateTimeDef> locationDateTimeMap = dateTimeResponse.getLocationDateTimeList().stream()
                .collect(Collectors.toMap(LocationDateTimeDef::getBusinessId, Function.identity()));

        // todo 多线程
        var locationUpcomingCountList = businessIds.stream()
                .map(businessId -> {
                    var builder = StaffUpcomingAppointmentCountDTO.LocationUpcomingAppointmentCount.builder()
                            .businessId(businessId);
                    LocationDateTimeDef locationDateTime = locationDateTimeMap.get(businessId);
                    if (Objects.isNull(locationDateTime)) {
                        return builder.upcomingAppointmentCount(0).build();
                    }
                    List<Integer> num = moeGroomingAppointmentMapper.queryStaffUpComingAppointCount(
                            businessId.intValue(),
                            staffId.intValue(),
                            locationDateTime.getCurrentDate(),
                            locationDateTime.getCurrentMinutes());
                    return builder.upcomingAppointmentCount(num == null ? 0 : num.size())
                            .build();
                })
                .toList();

        return StaffUpcomingAppointmentCountDTO.builder()
                .staffId(params.staffId())
                .locationUpcomingCountList(locationUpcomingCountList)
                .build();
    }

    private StaffUpcomingAppointmentCountDTO buildEmptyUpcomingCountDTO(StaffUpcomingAppointmentCountParams params) {
        return StaffUpcomingAppointmentCountDTO.builder()
                .staffId(params.staffId())
                .locationUpcomingCountList(params.businessIds().stream()
                        .map(businessId -> StaffUpcomingAppointmentCountDTO.LocationUpcomingAppointmentCount.builder()
                                .businessId(businessId)
                                .upcomingAppointmentCount(0)
                                .build())
                        .toList())
                .build();
    }

    /**
     * 查询指定 sourceStaff 的 upcoming 预约中和 targetStaff startAtSameTime 的 operation 数量
     * 复用 {@link MoeGroomingAppointmentService#queryStaffUpComingOperationCount(Integer, Integer, Integer)} 方法}
     *
     * @param params 查询参数
     * @return 数量
     */
    public Integer queryStaffUpComingOperationCountV2(StaffUpcomingOperationCountParams params) {
        Long businessId = params.businessId();
        Long targetStaffId = params.targetStaffId();
        Long sourceStaffId = params.sourceStaffId();
        if (Objects.isNull(targetStaffId) || Objects.isNull(sourceStaffId)) {
            return 0;
        }

        var dateTimeResponse = businessClient.getWorkingLocationDateTime(GetWorkingLocationDateTimeRequest.newBuilder()
                .addBusinessIds(businessId)
                .build());
        Map<Long, LocationDateTimeDef> locationDateTimeMap = dateTimeResponse.getLocationDateTimeList().stream()
                .collect(Collectors.toMap(LocationDateTimeDef::getBusinessId, Function.identity()));
        if (!locationDateTimeMap.containsKey(businessId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not exists");
        }

        LocationDateTimeDef locationDateTime = locationDateTimeMap.get(businessId);
        List<Integer> groomingIdList = moeGroomingAppointmentMapper.queryStaffUpComingAppointCount(
                businessId.intValue(),
                sourceStaffId.intValue(),
                locationDateTime.getCurrentDate(),
                locationDateTime.getCurrentMinutes());
        if (CollectionUtils.isEmpty(groomingIdList)) {
            return 0;
        }

        return moeGroomingServiceOperationMapper.countOperationWithSameStartTimeByGroomingIdList(
                targetStaffId.intValue(), sourceStaffId.intValue(), groomingIdList);
    }

    public Boolean transferStaffUpcomingAppointment(TransferAppointmentParamsV2 params) {
        Integer sourceStaffId = params.getSourceStaffId().intValue();
        var transferList = params.getTransferList();
        if (CollectionUtils.isEmpty(transferList)) {
            return true;
        }

        List<Long> businessIds = params.getTransferList().stream()
                .map(TransferAppointmentParamsV2.LocationStaffPair::getBusinessId)
                .toList();

        var dateTimeResponse = businessClient.getWorkingLocationDateTime(GetWorkingLocationDateTimeRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .build());
        Map<Long, LocationDateTimeDef> locationDateTimeMap = dateTimeResponse.getLocationDateTimeList().stream()
                .collect(Collectors.toMap(LocationDateTimeDef::getBusinessId, Function.identity()));

        // todo 多线程
        transferList.forEach(transfer -> {
            Integer businessId = transfer.getBusinessId().intValue();
            Integer targetStaffId = transfer.getTargetStaffId().intValue();

            LocationDateTimeDef locationDateTime = locationDateTimeMap.get(transfer.getBusinessId());
            if (Objects.isNull(locationDateTime)) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, String.format("businessId %d not exist", transfer.getBusinessId()));
            }
            String nowDate = locationDateTime.getCurrentDate();
            Integer nowMinutes = locationDateTime.getCurrentMinutes();

            List<Integer> allChangeGroomingId =
                    moeGroomingPetDetailMapper.queryTransferAppointment(businessId, sourceStaffId, nowDate, nowMinutes);
            if (CollectionUtils.isEmpty(allChangeGroomingId)) {
                return;
            }
            // record transfer activity logs
            allChangeGroomingId.forEach(
                    id -> ActivityLogRecorder.record(AppointmentAction.TRANSFER, ResourceType.APPOINTMENT, id, null));

            moeGroomingPetDetailMapper.transferAppointment(
                    sourceStaffId, targetStaffId, CommonUtil.get10Timestamp(), allChangeGroomingId);
            // transfer operation
            moeGroomingServiceOperationMapper.transferOperation(sourceStaffId, targetStaffId, allChangeGroomingId);
            ThreadPool.execute(() -> {
                // update appt
                for (Integer groomingId : allChangeGroomingId) {
                    calendarSyncService.checkBusinessHaveGoogleCalendarSync(
                            businessId, groomingId, null, true); // transferAppointment
                }
            });
        });
        return true;
    }

    private MoeGroomingNote buildGroomingNote(
            String note,
            Byte noteType,
            Integer businessId,
            Long companyId,
            Integer staffId,
            Integer customerId,
            Integer appointmentId) {
        Long now = CommonUtil.get10Timestamp();
        MoeGroomingNote groomingNote = new MoeGroomingNote();
        groomingNote.setBusinessId(businessId);
        groomingNote.setCompanyId(companyId);
        groomingNote.setCustomerId(customerId);
        groomingNote.setGroomingId(appointmentId);
        groomingNote.setCreateBy(staffId);
        groomingNote.setUpdateBy(staffId);
        groomingNote.setType(noteType);
        groomingNote.setNote(note);
        groomingNote.setCreateTime(now);
        groomingNote.setUpdateTime(now);
        return groomingNote;
    }

    /**
     * 分为两种情况
     * 1. 没有关联的 appointment，此时需要创建 appointment，并删除该 waitList。对于已删除的 service，需要过滤掉
     * 2. 存在关联的 appointment，此时对关联的 appointment 做 reschedule，并删除该 waitList
     */
    public Long rescheduleFromWaitList(WaitListRescheduleParams param) {
        MoeWaitList moeWaitList =
                waitListService.getWaitListById(param.businessId().longValue(), param.waitListId());
        if (moeWaitList == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "waitListId not exist");
        }
        MoeGroomingAppointment existAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(
                moeWaitList.getAppointmentId().intValue());
        if (existAppointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not found");
        }
        String tz = waitListService.getBusinessTimeZone(param.businessId().longValue());
        switch (existAppointment.getWaitListStatus()) {
            case WAITLISTONLY -> {
                List<MoeGroomingPetDetail> moeGroomingPetDetails = buildMoePetDetailFromWaitList(
                        existAppointment, param.startDate(), param.startTime(), param.staffId());
                moePetDetailService.deleteByAppointId(existAppointment.getId());
                moePetDetailService.addPetDetails(existAppointment, moeGroomingPetDetails);
                Integer serviceDuration = moePetDetailService.calculateServiceDuration(moeGroomingPetDetails);
                // 更新 appointment 的时间
                MoeGroomingAppointment appointmentToBeUpdated = new MoeGroomingAppointment();
                appointmentToBeUpdated.setId(existAppointment.getId());
                appointmentToBeUpdated.setAppointmentDate(param.startDate());
                appointmentToBeUpdated.setAppointmentEndDate(
                        param.startDate()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
                appointmentToBeUpdated.setAppointmentStartTime(param.startTime());
                appointmentToBeUpdated.setAppointmentEndTime(param.startTime() + serviceDuration);
                appointmentToBeUpdated.setUpdatedById(param.tokenStaffId().longValue());
                editAppointment(appointmentToBeUpdated);
                onAppointmentCreated(existAppointment, moeGroomingPetDetails, param.tokenStaffId());
                // capture payment automatically
                moeBookOnlineDepositService.capturePaymentIntent(
                        existAppointment.getBusinessId(), existAppointment.getId());
            }
            case APPTANDWAITLIST -> {
                CardRescheduleParams cardRescheduleParams = CardRescheduleParams.builder()
                        .businessId(param.businessId())
                        .tokenStaffId(param.tokenStaffId())
                        .appointmentId(moeWaitList.getAppointmentId().intValue())
                        .staffId(param.staffId())
                        .startDate(param.startDate())
                        .startTime(param.startTime())
                        .cardType(CardTypeEnum.APPOINTMENT)
                        .build();
                calendarCardService.reschedule(cardRescheduleParams);
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not a waitList");
        }

        MoeGroomingAppointment apptUpdate = new MoeGroomingAppointment();
        apptUpdate.setId(existAppointment.getId());
        apptUpdate.setIsWaitingList(GroomingAppointmentEnum.NOT_WAITING_LIST);
        apptUpdate.setWaitListStatus(APPTONLY);
        apptUpdate.setBookOnlineStatus(ServiceEnum.OB_DEFAULT_NORMAL);
        apptUpdate.setNoStartTime(false);
        apptUpdate.setUpdatedById(param.tokenStaffId().longValue());
        apptUpdate.setUpdateTime(CommonUtil.get10Timestamp());
        editAppointment(apptUpdate);
        waitListService.deleteMoeWaitList(param.businessId().longValue(), param.waitListId(), tz);

        return existAppointment.getId().longValue();
    }

    /**
     * 从 waitList 中构造 petDetail，包含以下操作
     * 1. 检查每只 pet 的 service 是否有效
     * 2. 过滤掉已删除的 service
     * 3. 更新 petService 开始结束时间
     * 4. 设置 petService 的 staff
     */
    List<MoeGroomingPetDetail> buildMoePetDetailFromWaitList(
            MoeGroomingAppointment existAppointment, String startDate, Integer startTime, Integer staffId) {
        List<MoeGroomingPetDetail> moeGroomingPetDetails =
                waitListService.getPetService(existAppointment).values().stream()
                        .flatMap(List::stream)
                        .toList();
        List<Integer> serviceIdList = moeGroomingPetDetails.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .toList();
        if (CollectionUtils.isEmpty(serviceIdList) || serviceIdList.stream().anyMatch(k -> k == null || k <= 0)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petService invalid");
        }
        Map<Integer, MoeGroomingService> serviceMap =
                groomingServiceService.getServiceMap(existAppointment.getBusinessId(), serviceIdList);
        boolean isAllPetStartAtSameTime = moePetDetailService.isAllPetsStartAtSameTimeV3(moeGroomingPetDetails);
        moeGroomingPetDetails = moeGroomingPetDetails.stream()
                .filter(k -> {
                    MoeGroomingService moeGroomingService = serviceMap.get(k.getServiceId());
                    return moeGroomingService != null
                            && !moeGroomingService.getStatus().equals(DeleteStatusEnum.STATUS_DELETE);
                })
                .toList();
        if (CollectionUtils.isEmpty(moeGroomingPetDetails)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no valid service");
        }
        moeGroomingPetDetails.forEach(k -> k.setStaffId(staffId));
        Map<Integer, List<MoeGroomingPetDetail>> petDetailMap =
                moeGroomingPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        return moePetDetailService.reBuildPetDetail(petDetailMap, startDate, startTime, isAllPetStartAtSameTime);
    }

    private MoeGroomingAppointment buildNewAppointment(
            QuickAddAppointmentParam param, List<MoeGroomingPetDetail> petDetails) {
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setOrderId(CommonUtil.getUuid());
        appointment.setBusinessId(param.businessId());
        appointment.setCompanyId(param.companyId());
        appointment.setCustomerId(param.customerId().intValue());
        appointment.setAppointmentDate(param.startDate());
        appointment.setAppointmentEndDate(param.startDate()); // 旧接口，不支持跨天的 appointment，因此可以直接用 start date 代表 end date
        appointment.setAppointmentStartTime(param.startTime());
        appointment.setAppointmentEndTime(petDetails.stream()
                .map(p -> p.getEndTime().intValue())
                .max(Integer::compareTo)
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_SERVER_ERROR, String.format("get max end time failed, param: %s", param))));
        appointment.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue());
        appointment.setIsPaid((byte) PaymentStatusEnum.UNPAID.getValue());
        appointment.setColorCode(param.colorCode());
        appointment.setSource(param.source());
        appointment.setCreatedById(param.tokenStaffId());
        appointment.setCreateTime(CommonUtil.get10Timestamp());
        return appointment;
    }

    /**
     * @param petParams              detail of pet, from front end
     * @param businessId             business id
     * @param customerId             customer id
     * @param startTime              earliest start time of all pets
     * @param allPetsStartAtSameTime whether all pets start at same time
     * @return list of pet detail
     */
    private List<MoeGroomingPetDetail> buildPetDetailList(
            List<PetParams> petParams,
            Long businessId,
            Long customerId,
            Integer startTime,
            Boolean allPetsStartAtSameTime) {
        List<Integer> serviceIdList = petParams.stream()
                .flatMap(pet -> pet.serviceList().stream().map(ServiceAndOperationParams::serviceId))
                .collect(Collectors.toList());
        List<Integer> petIdList = petParams.stream().map(PetParams::petId).collect(Collectors.toList());
        Map<Integer, MoeGroomingService> serviceMap = buildServiceMap(businessId.intValue(), serviceIdList);
        List<MoeGroomingCustomerServices> customerServicesList =
                buildCustomerServiceList(petIdList, serviceIdList, businessId, customerId);
        Map<Pair<Integer, Integer>, BigDecimal> scopeTypePriceMap = buildScopeTypePriceMap(customerServicesList);
        Map<Pair<Integer, Integer>, Integer> scopeTypeTimeMap = buildScopeTypeTimeMap(customerServicesList);

        List<MoeGroomingPetDetail> petDetailList = new ArrayList<>();
        AtomicReference<Integer> nextStartTime = new AtomicReference<>(startTime);
        petParams.forEach(pet -> {
            if (Objects.nonNull(allPetsStartAtSameTime) && allPetsStartAtSameTime) {
                nextStartTime.set(startTime);
            }
            pet.serviceList().forEach(service -> {
                if (!serviceMap.containsKey(service.serviceId())) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, String.format("serviceId %d not exist", service.serviceId()));
                }
                MoeGroomingService moeGroomingService = serviceMap.get(service.serviceId());
                MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
                petDetail.setPetId(pet.petId());
                petDetail.setServiceId(service.serviceId());
                petDetail.setStaffId(service.staffId());
                petDetail.setServiceType(moeGroomingService.getType().intValue());
                petDetail.setServicePrice(
                        Objects.nonNull(service.servicePrice())
                                ? service.servicePrice()
                                : scopeTypePriceMap.getOrDefault(
                                        Pair.of(pet.petId(), service.serviceId()), moeGroomingService.getPrice()));
                petDetail.setServiceTime(
                        Objects.nonNull(service.serviceTime())
                                ? service.serviceTime()
                                : scopeTypeTimeMap.getOrDefault(
                                        Pair.of(pet.petId(), service.serviceId()), moeGroomingService.getDuration()));
                petDetail.setStartTime(nextStartTime.get().longValue());
                petDetail.setEndTime(nextStartTime.get().longValue() + petDetail.getServiceTime());
                nextStartTime.updateAndGet(v -> v + petDetail.getServiceTime());
                petDetail.setScopeTypePrice(
                        (Objects.nonNull(service.scopeTypePrice())
                                        && !ScopeModifyTypeEnum.DO_NOT_SAVE.equals(service.scopeTypePrice()))
                                ? ScopeModifyTypeEnum.THIS_FUTURE.getScopeType()
                                : ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                petDetail.setScopeTypeTime(
                        (Objects.nonNull(service.serviceTime())
                                        && !ScopeModifyTypeEnum.DO_NOT_SAVE.equals(service.scopeTypeTime()))
                                ? ScopeModifyTypeEnum.THIS_FUTURE.getScopeType()
                                : ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType());
                petDetail.setUpdateTime(CommonUtil.get10Timestamp());
                if (Objects.nonNull(service.enableOperation()) && service.enableOperation()) {
                    petDetail.setWorkMode(service.workMode());
                    petDetail.setEnableOperation(true);
                }
                petDetailList.add(petDetail);
            });
        });
        return petDetailList;
    }

    // 构造一个 map，key 为 petId+serviceId，value 为对应的 operation list
    private Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> buildServiceOperationList(
            List<MoeGroomingPetDetail> petDetails, List<PetParams> pets) {
        Map<Pair<Integer, Integer>, MoeGroomingPetDetail> petDetailMap = petDetails.stream()
                .collect(Collectors.toMap(
                        petDetail -> Pair.of(petDetail.getPetId(), petDetail.getServiceId()), petDetail -> petDetail));
        Map<Pair<Integer, Integer>, List<MoeGroomingServiceOperation>> serviceOperationMap = new HashMap<>();
        pets.forEach(pet -> pet.serviceList().stream()
                .filter(service -> Objects.nonNull(service.enableOperation()) && service.enableOperation())
                .forEach(service -> {
                    Pair<Integer, Integer> key = Pair.of(pet.petId(), service.serviceId());
                    if (!petDetailMap.containsKey(key)) {
                        throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR,
                                String.format("petId %d serviceId %d not exist", pet.petId(), service.serviceId()));
                    }
                    MoeGroomingPetDetail petDetail = petDetailMap.get(key);
                    AtomicInteger startTime =
                            new AtomicInteger(petDetail.getStartTime().intValue());
                    int workMode = petDetail.getWorkMode();
                    serviceOperationMap.put(
                            key,
                            service.operationList().stream()
                                    .map(operation -> {
                                        MoeGroomingServiceOperation serviceOperation =
                                                new MoeGroomingServiceOperation();
                                        serviceOperation.setDuration(operation.duration());
                                        serviceOperation.setOperationName(operation.operationName());
                                        serviceOperation.setPrice(operation.price());
                                        serviceOperation.setPriceRatio(operation.priceRatio());
                                        serviceOperation.setStaffId(
                                                operation.staffId().intValue());
                                        serviceOperation.setPetId(pet.petId());
                                        serviceOperation.setStartTime(startTime.get());
                                        serviceOperation.setComment(""); // 预留字段，不能为空，因此设置空字符串
                                        if (SEQUENCE_WORK_MODE == workMode) {
                                            startTime.addAndGet(operation.duration());
                                        }
                                        return serviceOperation;
                                    })
                                    .toList());
                }));

        return serviceOperationMap;
    }

    private BigDecimal calculateAmount(List<MoeGroomingPetDetail> petDetailList) {
        return petDetailList.stream()
                .map(MoeGroomingPetDetail::getServicePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Map<Pair<Integer, Integer>, BigDecimal> buildScopeTypePriceMap(
            final List<MoeGroomingCustomerServices> customerServicesList) {
        return customerServicesList.stream()
                .filter(customerService -> Objects.nonNull(customerService.getPetId())
                        && Objects.nonNull(customerService.getServiceId())
                        && Objects.nonNull(customerService.getSaveType())
                        && ServiceEnum.SAVE_TYPE_PRICE.equals(customerService.getSaveType()))
                .collect(Collectors.toMap(
                        customerService -> Pair.of(customerService.getPetId(), customerService.getServiceId()),
                        MoeGroomingCustomerServices::getServiceFee,
                        (v1, v2) -> v1));
    }

    private Map<Pair<Integer, Integer>, Integer> buildScopeTypeTimeMap(
            final List<MoeGroomingCustomerServices> customerServicesList) {
        return customerServicesList.stream()
                .filter(customerService -> Objects.nonNull(customerService.getPetId())
                        && Objects.nonNull(customerService.getServiceId())
                        && Objects.nonNull(customerService.getSaveType())
                        && ServiceEnum.SAVE_TYPE_TIME.equals(customerService.getSaveType()))
                .collect(Collectors.toMap(
                        customerService -> Pair.of(customerService.getPetId(), customerService.getServiceId()),
                        MoeGroomingCustomerServices::getServiceTime,
                        (v1, v2) -> v1));
    }

    private Map<Integer, MoeGroomingService> buildServiceMap(Integer businessId, List<Integer> serviceIdList) {
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        businessId, serviceIdList);
        return serviceList.stream().collect(Collectors.toMap(MoeGroomingService::getId, service -> service));
    }

    private List<MoeGroomingCustomerServices> buildCustomerServiceList(
            List<Integer> petIdList, List<Integer> serviceIdList, Long businessId, Long customerId) {
        return companyGroomingServiceQueryService.getCustomizeServices(
                businessId.intValue(), customerId.intValue(), petIdList, serviceIdList, null);
    }

    private Boolean checkStaff(Long businessId, Long staffId) {
        StaffIdParams staffParams = new StaffIdParams();
        staffParams.setBusinessId(businessId.intValue());
        staffParams.setStaffId(staffId.intValue());
        return Objects.nonNull(iBusinessStaffClient.getStaff(staffParams));
    }

    public CustomerAddressDto getAppointmentAddress(MoeGroomingAppointment moeGroomingAppointment) {
        return batchGetAppointmentAddress(List.of(moeGroomingAppointment)).get(moeGroomingAppointment.getId());
    }

    /**
     * @return <appointmentId, customerAddress>
     */
    public Map<Integer, CustomerAddressDto> batchGetAppointmentAddress(
            List<MoeGroomingAppointment> moeGroomingAppointmentList) {
        Map<Integer, CustomerAddressDto> result = new HashMap<>();
        if (CollectionUtils.isEmpty(moeGroomingAppointmentList)) {
            return result;
        }
        List<Integer> customerIds = moeGroomingAppointmentList.stream()
                .map(MoeGroomingAppointment::getCustomerId)
                .distinct()
                .toList();
        Map<Integer, CustomerAddressDto> customerAddressMap = obAddressService.batchGetPrimaryAddress(customerIds);
        moeGroomingAppointmentList.forEach(appointment -> {
            CustomerAddressDto address = customerAddressMap.get(appointment.getCustomerId());
            if (address != null) {
                result.put(appointment.getId(), address);
            }
        });

        return result;
    }

    OBPrepayDetailDTO getPrepayDetail(MoeGroomingAppointment moeGroomingAppointment) {
        return batchGetPrepayDetail(moeGroomingAppointment.getBusinessId(), List.of(moeGroomingAppointment))
                .get(moeGroomingAppointment.getId());
    }

    Map<Integer, OBPrepayDetailDTO> batchGetPrepayDetail(
            Integer businessId, List<MoeGroomingAppointment> moeGroomingAppointmentList) {
        moeGroomingAppointmentList = moeGroomingAppointmentList.stream().toList();
        List<Integer> appointmentIds = moeGroomingAppointmentList.stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
        Map<Integer, OBPrepayDetailDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return result;
        }
        Map<Integer, OrderModel> orderDetailModelMap =
                orderService.batchGetOrderDetailByGroomingIds(businessId, appointmentIds).stream()
                        .collect(Collectors.toMap(
                                k -> (int) k.getOrder().getSourceId(), OrderDetailModel::getOrder, (k1, k2) -> k2));
        return prepayService.batchGetPrepayDetail(businessId, orderDetailModelMap);
    }

    GroomingCalenderCustomerInfo getCustomerInfo(Integer tokenStaffId, MoeGroomingAppointment appointment) {
        return batchGetCustomerInfo(tokenStaffId, List.of(appointment)).get(appointment.getId());
    }

    /**
     * @return <appointmentId, GroomingCalenderCustomerInfo>
     */
    Map<Integer, GroomingCalenderCustomerInfo> batchGetCustomerInfo(
            Integer tokenStaffId, List<MoeGroomingAppointment> appointmentList) {
        Map<Integer, GroomingCalenderCustomerInfo> result = new HashMap<>();
        if (CollectionUtils.isEmpty(appointmentList)) {
            return result;
        }
        List<GroomingQueryDto> ticketInfo = appointmentList.stream()
                .map(k -> {
                    GroomingQueryDto groomingQueryDto = new GroomingQueryDto();
                    groomingQueryDto.setGroomingId(k.getId());
                    groomingQueryDto.setCustomerId(k.getCustomerId());
                    return groomingQueryDto;
                })
                .toList();
        GroomingCustomerInfoParams groomingCustomerInfoParams = new GroomingCustomerInfoParams();
        groomingCustomerInfoParams.setTokenStaffId(tokenStaffId);
        groomingCustomerInfoParams.setTicketInfo(ticketInfo);
        result = iCustomerGroomingClient.getGroomingCalenderCustomerInfo(groomingCustomerInfoParams).stream()
                .collect(Collectors.toMap(GroomingCalenderCustomerInfo::getGroomingId, k -> k, (k1, k2) -> k2));
        return result;
    }

    List<MoeGroomingAppointment> getOldWaitListToBeUpdated(long companyId, List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return List.of();
        }
        List<MoeGroomingAppointment> oldWaitList = moeGroomingAppointmentMapper.queryWaitList(
                companyId, businessIdList, IN_PROGRESS_STATUS_SET, List.of(APPTONLY), null);
        return oldWaitList.stream().filter(k -> k.getCustomerId() > 0).toList();
    }
}
