package com.moego.server.grooming.service.client;

import com.github.pagehelper.Page;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.PageDTO;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.response.ResponseResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.AppointmentParams;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.grooming.service.ActiveMQService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingReportService;
import com.moego.server.grooming.service.dto.client.ClientCancelApptDTO;
import com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO;
import com.moego.server.grooming.service.ob.OBAddressService;
import com.moego.server.grooming.service.ob.OBBusinessProfileService;
import com.moego.server.grooming.service.ob.OBClientService;
import com.moego.server.grooming.service.ob.OBCustomerService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.service.utils.ClientApptHelper;
import com.moego.server.grooming.web.vo.client.ApptDetailVO;
import com.moego.server.grooming.web.vo.client.ClientApptDetailVO;
import com.moego.server.grooming.web.vo.client.ClientApptListVO;
import com.moego.server.grooming.web.vo.client.ClientApptVO;
import com.moego.server.grooming.web.vo.client.ClientInfoVO;
import com.moego.server.grooming.web.vo.client.GroomingReportRecordVO;
import com.moego.server.grooming.web.vo.client.UpdateApptVO;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@Slf4j
@Service
@AllArgsConstructor
public class ClientApptService {

    private final AppointmentMapperProxy appointmentMapper;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final PetDetailMapperProxy petDetailMapper;

    private final MoeGroomingAppointmentService appointmentService;
    private final ClientApptPetDetailService clientApptPetDetailService;
    private final ClientPaymentService clientPaymentService;
    private final OBBusinessProfileService businessProfileService;
    private final MoeGroomingReportService groomingReportService;
    private final OBCustomerService customerService;
    private final OBClientService clientService;

    private final ICustomerCustomerClient customerClient;
    private final OBAddressService obAddressService;
    private final IBusinessBusinessClient businessClient;
    private final IMessageClient messageClient;
    private final BusinessInfoHelper businessInfoHelper;
    private final ClientApptHelper clientApptHelper;
    private final ActiveMQService activeMQService;

    public ClientApptListVO convertDO2VO(Page<MoeGroomingAppointment> page) {
        List<MoeGroomingAppointment> apptList = page.getResult();
        ClientApptListVO apptListVO = new ClientApptListVO();
        if (CollectionUtils.isEmpty(apptList)) {
            apptListVO.setApptPage(
                    PageDTO.create(Collections.emptyList(), page.getTotal(), page.getPageNum(), page.getPageSize()));
            return apptListVO;
        }
        List<ClientApptVO> apptVOList = clientApptHelper.convertAppts(apptList);
        PageDTO<ClientApptVO> pageDTO =
                PageDTO.create(apptVOList, page.getTotal(), page.getPageNum(), page.getPageSize());
        List<Integer> businessIdList = apptList.stream()
                .map(MoeGroomingAppointment::getBusinessId)
                .distinct()
                .collect(Collectors.toList());
        CommonIdsParams idsParams = new CommonIdsParams();
        idsParams.setIds(businessIdList);
        List<OBBusinessInfoDTO> businessInfoDTOList = businessClient.getBusinessInfoListForOB(idsParams);
        List<MoeBookOnlineProfile> profileList = businessProfileService.listProfileByBusinessId(businessIdList);
        List<MoeBusinessBookOnline> bookOnlineList = bookOnlineMapper.getBusinessBookOnlineList(businessIdList);

        return apptListVO
                .setApptPage(pageDTO)
                .setObProfileList(ClientApptUtils.convertProfile(profileList))
                .setBusinessInfoList(businessInfoHelper.businessInfoDTO2VO(businessInfoDTOList))
                .setObConfigList(ClientApptUtils.convert(bookOnlineList, businessInfoDTOList));
    }

    public MoeGroomingAppointment getClientAppt(Integer apptId, List<BaseBusinessCustomerIdDTO> customerIdDTOList) {
        MoeGroomingAppointment appointment = appointmentMapper.selectByPrimaryKey(apptId);
        if (Objects.isNull(appointment)) {
            throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND, "Appointment not found");
        }
        for (BaseBusinessCustomerIdDTO customerIdDTO : customerIdDTOList) {
            if (Objects.equals(customerIdDTO.getCustomerId(), appointment.getCustomerId())
                    && Objects.equals(customerIdDTO.getCompanyId(), appointment.getCompanyId())) {
                return appointment;
            }
        }
        throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND, "Appointment not found");
    }

    public ClientApptDetailVO getApptDetail(String bookingId, List<BaseBusinessCustomerIdDTO> customerIdDTOList) {
        Integer apptId = Integer.valueOf(bookingId);
        MoeGroomingAppointment appointment = getClientAppt(apptId, customerIdDTOList);
        ApptDetailVO appt = clientApptHelper.convertApptDetail(appointment);
        if (Objects.isNull(appt)) {
            return new ClientApptDetailVO();
        }
        InfoIdParams idParams = new InfoIdParams();
        idParams.setInfoId(appointment.getBusinessId());
        OBBusinessInfoDTO businessInfoDTO = businessClient.getBusinessInfoForOB(idParams);
        MoeBookOnlineProfile bookOnlineProfile =
                businessProfileService.getProfileByBusinessId(appointment.getBusinessId());
        MoeBusinessCustomerDTO customerDTO = customerClient.getCustomerWithDeleted(appointment.getCustomerId());
        CustomerAddressDto primaryAddress = obAddressService.getPrimaryAddress(appointment.getCustomerId());
        MoeBusinessBookOnline businessBookOnline = bookOnlineMapper.selectByBusinessId(appointment.getBusinessId());
        BusinessTwilioNumberDTO businessTwilioNumber =
                messageClient.getBusinessTwilioNumber(appointment.getBusinessId());
        // 查询预约关联的 grooming report
        List<GroomingReportRecordVO> groomingReportRecords =
                groomingReportService.getGroomingReportRecordsForClient(appointment);

        ApptDetailVO apptDetail =
                appt.setBookingId(bookingId).setApptType(ClientApptUtils.getApptType(appointment, businessInfoDTO));

        ClientInfoVO clientInfo = ClientApptUtils.convert(customerDTO);
        clientInfo.setIsBlockOnlineBooking(
                clientService.checkCustomerIsBlocked(customerDTO.getCompanyId(), customerDTO.getCustomerId()));

        return new ClientApptDetailVO()
                .setAppt(apptDetail)
                .setClientInfo(clientInfo)
                .setItemList(clientApptPetDetailService.listApptPetDetail(appointment))
                .setObProfile(ClientApptUtils.convert(bookOnlineProfile))
                .setBusinessInfo(businessInfoHelper.businessInfoDTO2VO(businessInfoDTO))
                .setPrepayPayment(clientPaymentService.getPrepayPayment(appointment))
                .setObConfig(ClientApptUtils.convert(businessBookOnline, businessInfoDTO))
                .setServiceAddress(ClientApptUtils.convert(primaryAddress))
                .setBusinessTwilio(ClientApptUtils.convert(businessTwilioNumber))
                .setGroomingReportRecords(groomingReportRecords);
    }

    public boolean cancelAppt(ClientCancelApptDTO cancelApptDTO) {
        Integer apptId = Integer.valueOf(cancelApptDTO.getBookingId());
        MoeGroomingAppointment appt = getClientAppt(apptId, cancelApptDTO.getCustomerIdDTOList());
        // Check apptType is pending
        if (!ClientApptUtils.isRequests(appt)) {
            return false;
        }
        // Cancel and notification business owner and staff
        CancelParams cancelParams = new CancelParams();
        cancelParams.setId(appt.getId());
        cancelParams.setBusinessId(appt.getBusinessId());
        cancelParams.setCancelReason(ClientApptConst.OBRequestNotificationTitle.CANCELED_BY_CLIENT);
        cancelParams.setNoShow(GroomingAppointmentEnum.NO_SHOW_FALSE);
        cancelParams.setCancelByType(GroomingAppointmentEnum.CANCEL_TYPE_BY_CLIENT_PORTAL);
        cancelParams.setAccountId(cancelApptDTO.getClientId());
        cancelParams.setRefundPrepaid(Boolean.TRUE);
        ResponseResult<Integer> result =
                appointmentService.editAppointmentCancel(cancelParams, true, true); // cancel appt
        activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                .setBusinessId(appt.getBusinessId())
                .setAppointmentId(appt.getId())
                .setEvent(BookingRequestEventParams.BookingRequestEvent.DECLINED));
        return result.getSuccess() && !Objects.equals(result.getData(), 0);
    }

    public UpdateApptVO updateAppt(ClientUpdateApptDTO updateApptDTO) {
        Integer apptId = Integer.valueOf(updateApptDTO.getBookingId());
        MoeGroomingAppointment appt = getClientAppt(apptId, updateApptDTO.getCustomerIdDTOList());
        // Check apptType is pending
        if (!ClientApptUtils.isRequests(appt)) {
            return UpdateApptVO.builder().updateSuccess(false).build();
        }
        MoeBusinessBookOnline bookOnline = bookOnlineMapper.selectByBusinessId(appt.getBusinessId());
        CustomerHasRequestDTO requestDTO =
                customerService.getCustomerHasRequestUpdate(appt.getBusinessId(), appt.getCustomerId());
        boolean autoAcceptRequest = OBRequestSubmittedAutoTypeEnum.isAutoAcceptRequest(
                bookOnline.getRequestSubmittedAutoType(), requestDTO.hasRequestUpdate());
        AppointmentParams appointmentParams = new AppointmentParams();
        appointmentParams.setId(apptId);
        appointmentParams.setBusinessId(appt.getBusinessId());
        appointmentParams.setIsWaitingList(appt.getIsWaitingList());
        appointmentParams.setAppointmentDateString(updateApptDTO.getApptDate());
        appointmentParams.setAppointmentStartTime(updateApptDTO.getApptStartTime());
        if (autoAcceptRequest) {
            appointmentParams.setBookOnlineStatus(GroomingAppointmentEnum.BOOK_ONLINE_STATUS_NOT_OB);
        } else {
            appointmentParams.setBookOnlineStatus(appt.getBookOnlineStatus());
        }
        List<GroomingPetDetailDTO> petDetailDTOList = petDetailMapper.queryPetDetailByGroomingId(apptId);
        AtomicReference<Integer> apptStartTime = new AtomicReference<>(updateApptDTO.getApptStartTime());
        List<PetDetailParams> petDetailParamsList = petDetailDTOList.stream()
                .map(petDetailDTO -> {
                    PetDetailParams petDetailParams = new PetDetailParams();
                    petDetailParams.setGroomingId(petDetailDTO.getGroomingId());
                    petDetailParams.setServiceTime(petDetailDTO.getServiceTime());
                    petDetailParams.setStartTime(apptStartTime.get());
                    apptStartTime.updateAndGet(v -> v + petDetailDTO.getServiceTime());
                    petDetailParams.setPetId(petDetailDTO.getPetId());
                    petDetailParams.setStaffId(updateApptDTO.getStaffId());
                    petDetailParams.setServiceId(petDetailDTO.getServiceId());
                    petDetailParams.setServiceType(petDetailDTO.getServiceType());
                    petDetailParams.setServicePrice(petDetailDTO.getServicePrice());
                    petDetailParams.setServiceTime(petDetailDTO.getServiceTime());
                    petDetailParams.setStar(Objects.equals(petDetailDTO.getStarStaffId(), petDetailDTO.getStaffId()));
                    petDetailParams.setScopeTypeTime(petDetailDTO.getScopeTypeTime());
                    petDetailParams.setScopeTypePrice(petDetailDTO.getScopeTypePrice());
                    return petDetailParams;
                })
                .collect(Collectors.toList());
        appointmentParams.setPetServices(petDetailParamsList);
        ResponseResult<Integer> result = appointmentService.modifyGroomingAppointment(
                appt, updateApptDTO.getClientId(), appointmentParams, Boolean.TRUE);
        activeMQService.publishBookingRequestEvent(new BookingRequestEventParams()
                .setBusinessId(appt.getBusinessId())
                .setAppointmentId(appt.getId())
                .setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED));
        return UpdateApptVO.builder()
                .updateSuccess(result.getSuccess() && !Objects.equals(result.getData(), 0))
                .autoAcceptRequest(autoAcceptRequest)
                .build();
    }
}
