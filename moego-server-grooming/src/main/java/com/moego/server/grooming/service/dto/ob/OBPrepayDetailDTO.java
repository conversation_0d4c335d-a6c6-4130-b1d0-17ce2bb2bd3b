package com.moego.server.grooming.service.dto.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/7/12
 */
@Data
@Accessors(chain = true)
public class OBPrepayDetailDTO {

    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    @Schema(description = "预先支付的金额")
    private BigDecimal prepaidAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "预支付金额占总金额的比例")
    private Double prepayRate;

    @Schema(description = "预支付记录状态，参考BookOnlineDepositConst")
    private Byte prepayStatus;

    private Boolean enablePreAuth = false;
}
