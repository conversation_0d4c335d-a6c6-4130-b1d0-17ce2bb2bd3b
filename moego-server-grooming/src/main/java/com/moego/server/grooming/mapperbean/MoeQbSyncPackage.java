package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_qb_sync_package
 */
public class MoeQbSyncPackage {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   quickbook connect id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.connect_id
     *
     * @mbg.generated
     */
    private Integer connectId;

    /**
     * Database Column Remarks:
     *   quickbook realm id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.realm_id
     *
     * @mbg.generated
     */
    private String realmId;

    /**
     * Database Column Remarks:
     *   quickbook service id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.qb_service_id
     *
     * @mbg.generated
     */
    private String qbServiceId;

    /**
     * Database Column Remarks:
     *   moego product id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.package_id
     *
     * @mbg.generated
     */
    private Integer packageId;

    /**
     * Database Column Remarks:
     *   moego product name
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.package_name
     *
     * @mbg.generated
     */
    private String packageName;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   moego product description
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_qb_sync_package.package_description
     *
     * @mbg.generated
     */
    private String packageDescription;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.id
     *
     * @return the value of moe_qb_sync_package.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.id
     *
     * @param id the value for moe_qb_sync_package.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.business_id
     *
     * @return the value of moe_qb_sync_package.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.business_id
     *
     * @param businessId the value for moe_qb_sync_package.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.connect_id
     *
     * @return the value of moe_qb_sync_package.connect_id
     *
     * @mbg.generated
     */
    public Integer getConnectId() {
        return connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.connect_id
     *
     * @param connectId the value for moe_qb_sync_package.connect_id
     *
     * @mbg.generated
     */
    public void setConnectId(Integer connectId) {
        this.connectId = connectId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.realm_id
     *
     * @return the value of moe_qb_sync_package.realm_id
     *
     * @mbg.generated
     */
    public String getRealmId() {
        return realmId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.realm_id
     *
     * @param realmId the value for moe_qb_sync_package.realm_id
     *
     * @mbg.generated
     */
    public void setRealmId(String realmId) {
        this.realmId = realmId == null ? null : realmId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.qb_service_id
     *
     * @return the value of moe_qb_sync_package.qb_service_id
     *
     * @mbg.generated
     */
    public String getQbServiceId() {
        return qbServiceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.qb_service_id
     *
     * @param qbServiceId the value for moe_qb_sync_package.qb_service_id
     *
     * @mbg.generated
     */
    public void setQbServiceId(String qbServiceId) {
        this.qbServiceId = qbServiceId == null ? null : qbServiceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.package_id
     *
     * @return the value of moe_qb_sync_package.package_id
     *
     * @mbg.generated
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.package_id
     *
     * @param packageId the value for moe_qb_sync_package.package_id
     *
     * @mbg.generated
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.package_name
     *
     * @return the value of moe_qb_sync_package.package_name
     *
     * @mbg.generated
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.package_name
     *
     * @param packageName the value for moe_qb_sync_package.package_name
     *
     * @mbg.generated
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.create_time
     *
     * @return the value of moe_qb_sync_package.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.create_time
     *
     * @param createTime the value for moe_qb_sync_package.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.update_time
     *
     * @return the value of moe_qb_sync_package.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.update_time
     *
     * @param updateTime the value for moe_qb_sync_package.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.company_id
     *
     * @return the value of moe_qb_sync_package.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.company_id
     *
     * @param companyId the value for moe_qb_sync_package.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_qb_sync_package.package_description
     *
     * @return the value of moe_qb_sync_package.package_description
     *
     * @mbg.generated
     */
    public String getPackageDescription() {
        return packageDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_qb_sync_package.package_description
     *
     * @param packageDescription the value for moe_qb_sync_package.package_description
     *
     * @mbg.generated
     */
    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription == null ? null : packageDescription.trim();
    }
}
