package com.moego.server.grooming.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeSlot {

    // step 1
    private int start;
    private int end;
    private int beforeServiceId;
    private int beforeApptId;
    private int beforeCustomerId;
    private boolean beforeApptIsBlock;
    private int afterServiceId;
    private int afterApptId;
    private int afterCustomerId;
    private boolean afterApptIsBlock;

    // step 2
    private String beforeLat;
    private String beforeLng;
    private String afterLat;
    private String afterLng;

    // step 3
    private int driveInMinutes;
    private double driveInMiles;
    private int driveOutMinutes;
    private double driveOutMiles;
    private boolean driveFromStartLocation;
    private boolean driveToEndLocation;

    private int availableTime;

    public void updateAvailableTime(int duration) {
        this.availableTime = this.end - this.start - duration;
    }

    public boolean isBeforeAddressValid() {
        return !StringUtils.isEmpty(this.getBeforeLat()) && !StringUtils.isEmpty(this.getBeforeLng());
    }

    public boolean isAfterAddressValid() {
        return !StringUtils.isEmpty(this.getAfterLat()) && !StringUtils.isEmpty(this.getAfterLng());
    }
}
