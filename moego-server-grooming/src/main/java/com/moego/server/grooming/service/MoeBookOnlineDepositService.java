package com.moego.server.grooming.service;

import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.listener.event.BookOnlineDepositEvent;
import com.moego.server.grooming.mapper.MoeBookOnlineDepositMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit;
import com.moego.server.grooming.mapperbean.MoeBookOnlineDepositExample;
import com.moego.server.grooming.params.MoeBookOnlineDepositVO;
import com.moego.server.payment.api.IPaymentStripeService;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class MoeBookOnlineDepositService {

    private final MoeBookOnlineDepositMapper obDepositMapper;
    private final IPaymentStripeService iPaymentStripeService;
    private final ApplicationEventPublisher publisher;

    /**
     * Insert book online deposit, return id.
     *
     * <p> Using table default value when property is null.
     *
     * @param obDeposit book online deposit
     * @return id
     */
    public int insert(MoeBookOnlineDeposit obDeposit) {
        obDepositMapper.insertSelective(obDeposit);

        publisher.publishEvent(new BookOnlineDepositEvent.Created(this, mustGetBookOnlineDeposit(obDeposit.getId())));

        return obDeposit.getId();
    }

    /**
     * Update book online deposit, return affected rows.
     *
     * <p> null property will not be updated.
     *
     * @param obDeposit book online deposit
     * @return affected rows
     */
    public int update(MoeBookOnlineDeposit obDeposit) {

        if (obDeposit.getId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "id is null");
        }

        var before = mustGetBookOnlineDeposit(obDeposit.getId());

        var result = obDepositMapper.updateByPrimaryKeySelective(obDeposit);
        if (result > 0) {
            var after = mustGetBookOnlineDeposit(obDeposit.getId());
            publisher.publishEvent(new BookOnlineDepositEvent.Updated(this, before, after));
        }

        return result;
    }

    /**
     * 创建或更新OB定金记录
     *
     * @param obDepositVO
     * @return
     */
    public Integer createOrUpdateDepositByGuid(MoeBookOnlineDepositVO obDepositVO) {
        if (StringUtils.isEmpty(obDepositVO.getGuid())
                && PrimitiveTypeUtil.isNumberNullOrZero(obDepositVO.getPaymentId())) {
            return 0;
        }
        MoeBookOnlineDeposit obDeposit = obDepositMapper.selectByGuid(obDepositVO.getGuid());
        if (obDeposit == null) {
            obDeposit = new MoeBookOnlineDeposit();
            BeanUtils.copyProperties(obDepositVO, obDeposit);
            obDeposit.setStatus(BookOnlineDepositConst.PROCESSING);
            obDeposit.setDepositType(DepositPaymentTypeEnum.PrePay);
            return insert(obDeposit);
        } else {
            obDeposit.setAmount(obDepositVO.getAmount());
            obDeposit.setPaymentId(obDepositVO.getPaymentId());
            obDeposit.setStatus(obDepositVO.getStatus());
            return update(obDeposit);
        }
    }

    /**
     * 通过paymentId更新obDeposit状态为支付状态
     *
     * @param obDepositVO
     * @return
     */
    public Integer updateDepositPaidByPaymentId(MoeBookOnlineDepositVO obDepositVO) {
        MoeBookOnlineDeposit obDeposit =
                obDepositMapper.selectByPaymentId(obDepositVO.getBusinessId(), obDepositVO.getPaymentId());
        if (obDeposit == null) {
            return 0;
        }
        MoeBookOnlineDeposit update = new MoeBookOnlineDeposit();
        update.setId(obDeposit.getId());
        update.setAmount(obDepositVO.getAmount());
        update.setBookingFee(obDepositVO.getBookingFee());
        update.setTipsAmount(obDepositVO.getTipsAmount());
        update.setStatus(obDepositVO.getStatus());
        return update(update);
    }

    @Nullable
    public MoeBookOnlineDeposit getOBDepositByDeGuid(@Nullable String deGuid) {
        if (!StringUtils.hasText(deGuid)) {
            return null;
        }
        return obDepositMapper.selectByGuid(deGuid);
    }

    public MoeBookOnlineDeposit getOBDepositByGroomingId(Integer businessId, Integer groomingId) {
        return obDepositMapper.selectByGroomingId(businessId, groomingId);
    }

    public void capturePaymentIntent(Integer businessId, Integer groomingId) {
        MoeBookOnlineDeposit deposit = getOBDepositByGroomingId(businessId, groomingId);
        if (deposit != null) {
            iPaymentStripeService.capturePaymentIntent(deposit.getPaymentId());
        }
    }

    /**
     * 计算prepay所占比例，去掉convenience、tipsAmount
     *
     * @param obDeposit   prepay记录
     * @param totalAmount invoice总金额
     * @return
     */
    public Double getPrepayRate(MoeBookOnlineDeposit obDeposit, BigDecimal totalAmount) {
        if (obDeposit == null) return 0d;
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) return 1d;

        BigDecimal amountExcludeTipAndFee =
                obDeposit.getAmount().subtract(obDeposit.getConvenienceFee()).subtract(obDeposit.getTipsAmount());

        return amountExcludeTipAndFee
                .divide(totalAmount, 2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    public List<MoeBookOnlineDeposit> getOBDepositByGroomingIds(Integer businessId, Set<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return new ArrayList<>();
        }
        return obDepositMapper.selectByGroomingIds(businessId, groomingIds);
    }

    public List<MoeBookOnlineDeposit> getOBDepositByGroomingIdsV2(Set<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return new ArrayList<>();
        }
        return obDepositMapper.selectByGroomingIdsV2(groomingIds);
    }

    public BookOnlineDepositDTO getOBDepositByPaymentId(Integer businessId, Integer paymentId) {
        MoeBookOnlineDeposit deposit = obDepositMapper.selectByPaymentId(businessId, paymentId);
        if (deposit == null) {
            return null;
        }
        BookOnlineDepositDTO depositDTO = new BookOnlineDepositDTO();
        BeanUtils.copyProperties(deposit, depositDTO);
        return depositDTO;
    }

    /**
     * 查询 OB prepay deposit 记录
     *
     * @param businessId
     * @param paymentIds
     * @return
     */
    public List<BookOnlineDepositDTO> getOBDepositByPaymentIds(Integer businessId, Set<Integer> paymentIds) {
        List<BookOnlineDepositDTO> depositDTOs = new ArrayList<>();
        if (CollectionUtils.isEmpty(paymentIds)) {
            return depositDTOs;
        }
        List<MoeBookOnlineDeposit> deposits =
                obDepositMapper.selectByPaymentIdsAndDepositType(businessId, paymentIds, DepositPaymentTypeEnum.PrePay);
        for (MoeBookOnlineDeposit deposit : deposits) {
            BookOnlineDepositDTO depositDTO = new BookOnlineDepositDTO();
            BeanUtils.copyProperties(deposit, depositDTO);
            depositDTOs.add(depositDTO);
        }
        return depositDTOs;
    }

    public List<BookOnlineDepositDTO> getOBDepositByPaymentIdsV2(Long companyId, Set<Integer> paymentIds) {
        List<BookOnlineDepositDTO> depositDTOs = new ArrayList<>();
        if (CollectionUtils.isEmpty(paymentIds)) {
            return depositDTOs;
        }
        List<MoeBookOnlineDeposit> deposits = obDepositMapper.selectByPaymentIdsV2(companyId, paymentIds);
        for (MoeBookOnlineDeposit deposit : deposits) {
            BookOnlineDepositDTO depositDTO = new BookOnlineDepositDTO();
            BeanUtils.copyProperties(deposit, depositDTO);
            depositDTOs.add(depositDTO);
        }
        return depositDTOs;
    }

    public void insertPreAuth(MoeBookOnlineDeposit preAuth) {
        obDepositMapper.insertSelective(preAuth);
    }

    public int updateDepositPaidByGroomingId(MoeBookOnlineDepositVO obDepositVO) {
        MoeBookOnlineDeposit obDeposit =
                obDepositMapper.selectByGroomingId(obDepositVO.getBusinessId(), obDepositVO.getGroomingId());
        if (obDeposit == null) {
            return 0;
        }
        MoeBookOnlineDeposit update = new MoeBookOnlineDeposit();
        update.setId(obDeposit.getId());
        update.setPaymentId(obDepositVO.getPaymentId());
        update.setStatus(BookOnlineDepositConst.REQUIRE_CAPTURE);
        return update(update);
    }

    public Integer migrateDiscountCodeIds(Map<Long, Long> discountCodeIdMap) {
        return obDepositMapper.updateDiscountCodeId(discountCodeIdMap);
    }

    /**
     * List OB deposit by bookingRequestIds.
     *
     * @param businessId businessId, nullable
     * @param bookingRequestIds bookingRequestIds
     * @return list of {@link BookOnlineDepositDTO}
     */
    public List<MoeBookOnlineDeposit> listOBDepositByBookingRequestIds(
            @Nullable Integer businessId, Collection<Long> bookingRequestIds) {
        if (ObjectUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }

        var example = new MoeBookOnlineDepositExample();
        var criteria = example.createCriteria();
        if (businessId != null) {
            criteria.andBusinessIdEqualTo(businessId);
        }
        criteria.andBookingRequestIdIn(List.copyOf(bookingRequestIds));

        return obDepositMapper.selectByExample(example);
    }

    private MoeBookOnlineDeposit mustGetBookOnlineDeposit(int id) {
        var result = obDepositMapper.selectByPrimaryKey(id);
        if (result == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "BookOnlineDeposit not found: " + id);
        }
        return result;
    }
}
