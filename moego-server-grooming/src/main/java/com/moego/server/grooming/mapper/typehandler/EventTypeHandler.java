package com.moego.server.grooming.mapper.typehandler;

import com.moego.idl.models.event_bus.v1.EventType;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

public class EventTypeHandler extends BaseTypeHandler<EventType> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, EventType parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public EventType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getOutboxSendStatus(rs.getInt(columnName), rs.wasNull());
    }

    @Override
    public EventType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getOutboxSendStatus(rs.getInt(columnIndex), rs.wasNull());
    }

    @Override
    public EventType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getOutboxSendStatus(cs.getInt(columnIndex), cs.wasNull());
    }

    private EventType getOutboxSendStatus(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return EventType.TYPE_UNSPECIFIED;
        }

        var type = EventType.forNumber(number);
        return type != null ? type : EventType.UNRECOGNIZED;
    }
}
