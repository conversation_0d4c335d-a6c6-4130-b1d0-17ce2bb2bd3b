package com.moego.server.grooming.server;

import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.server.grooming.api.IGroomingMergeServiceBase;
import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.server.grooming.service.AppointmentMergeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class GroomingMergeServer extends IGroomingMergeServiceBase {

    private final AppointmentMergeService appointmentMergeService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;

    @Override
    public void mergeGroomingObConfigPackage(CustomerPetMergeRelationDTO mergeRelation) {
        // query company all location ids for index query
        var locationList = businessService
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(mergeRelation.getCompanyId())
                        .build())
                .getLocationList();
        mergeRelation.setAllLocationIds(
                locationList.stream().map(LocationBriefView::getId).toList());
        // merge ob config
        appointmentMergeService.mergeCustomerObConfigData(mergeRelation);
        // merge grooming package
        appointmentMergeService.mergeCustomerGroomingPackageData(mergeRelation);
    }
}
