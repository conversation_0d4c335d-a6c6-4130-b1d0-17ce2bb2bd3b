package com.moego.server.grooming.service.report.migrate.validation.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * 数据校验错误信息
 */
@Data
@Builder
public class ValidationError {

    /**
     * 错误类型枚举
     */
    @Getter
    public enum ErrorType {
        /**
         * 数据完整性错误 - 关键字段缺失或不正确
         */
        DATA_INTEGRITY("数据完整性错误"),

        /**
         * 数据一致性错误 - 源数据与目标数据不一致
         */
        DATA_CONSISTENCY("数据一致性错误"),

        /**
         * 业务逻辑错误 - 不符合业务规则
         */
        BUSINESS_LOGIC("业务逻辑错误"),

        /**
         * 数据缺失错误 - 目标数据不存在
         */
        DATA_MISSING("数据缺失错误"),

        /**
         * 系统错误 - 校验过程中的系统异常
         */
        SYSTEM_ERROR("系统错误");

        private final String description;

        ErrorType(String description) {
            this.description = description;
        }
    }

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 源数据ID
     */
    private Integer sourceId;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 期望值
     */
    private String expectedValue;

    /**
     * 实际值
     */
    private String actualValue;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 错误详情
     */
    private String details;

    /**
     * 获取完整的错误描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(errorType.getDescription()).append("] ");
        sb.append(message);

        if (sourceId != null) {
            sb.append(" (源ID: ").append(sourceId).append(")");
        }

        if (fieldName != null) {
            sb.append(" [字段: ").append(fieldName).append("]");
        }

        if (expectedValue != null && actualValue != null) {
            sb.append(" [期望: ")
                    .append(expectedValue)
                    .append(", 实际: ")
                    .append(actualValue)
                    .append("]");
        }

        if (details != null) {
            sb.append(" 详情: ").append(details);
        }

        return sb.toString();
    }
}
