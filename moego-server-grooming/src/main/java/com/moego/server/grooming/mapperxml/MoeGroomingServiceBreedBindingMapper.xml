<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingServiceBreedBindingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="is_all" jdbcType="BIT" property="isAll" />
    <result column="breed_name_list" jdbcType="CHAR" property="breedNameList" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="breed_names" jdbcType="LONGVARCHAR" property="breedNames" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, service_id, pet_type_id, status, create_time, update_time, is_all,
    breed_name_list, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    breed_names
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_grooming_service_breed_binding
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_service_breed_binding
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_breed_binding (business_id, service_id, pet_type_id,
      status, create_time, update_time,
      is_all, breed_name_list, company_id,
      breed_names)
    values (#{businessId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{petTypeId,jdbcType=INTEGER},
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{isAll,jdbcType=BIT}, #{breedNameList,jdbcType=CHAR}, #{companyId,jdbcType=BIGINT},
      #{breedNames,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_service_breed_binding
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="petTypeId != null">
        pet_type_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isAll != null">
        is_all,
      </if>
      <if test="breedNameList != null">
        breed_name_list,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="breedNames != null">
        breed_names,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isAll != null">
        #{isAll,jdbcType=BIT},
      </if>
      <if test="breedNameList != null">
        #{breedNameList,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="breedNames != null">
        #{breedNames,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_breed_binding
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="isAll != null">
        is_all = #{isAll,jdbcType=BIT},
      </if>
      <if test="breedNameList != null">
        breed_name_list = #{breedNameList,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="breedNames != null">
        breed_names = #{breedNames,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_breed_binding
    set business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_all = #{isAll,jdbcType=BIT},
      breed_name_list = #{breedNameList,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      breed_names = #{breedNames,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_service_breed_binding
    set business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      is_all = #{isAll,jdbcType=BIT},
      breed_name_list = #{breedNameList,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBusinessIdAndServiceId" resultMap="ResultMapWithBLOBs">
    select <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming.moe_grooming_service_breed_binding
    where business_id = #{businessId}
    <if test="serviceId != null">
        and service_id = #{serviceId}
    </if>
    and status = 0
  </select>
  <select id="selectByCompanyIdAndServiceId" resultMap="ResultMapWithBLOBs">
    select <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming.moe_grooming_service_breed_binding
    where company_id = #{companyId}
    <if test="serviceId != null">
        and service_id = #{serviceId}
    </if>
    and status = 0
  </select>
  <select id="selectByCidBidsAndServiceId" resultMap="ResultMapWithBLOBs">
    select <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming.moe_grooming_service_breed_binding
    where
    company_id = #{companyId,jdbcType=BIGINT}
    <if test="businessIds != null and businessIds.size()&gt;0">
      AND `business_id` IN
      <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="serviceId != null">
        and service_id = #{serviceId}
    </if>
    and status = 0
  </select>


  <insert id="batchInsertRecords">
    insert into moe_grooming.moe_grooming_service_breed_binding
    (`business_id`, `service_id`, `pet_type_id`, `breed_names`, `create_time`, `update_time`, `is_all`, `breed_name_list`, `company_id`)
    values
    <foreach collection="records" index="index" item="record" separator=",">
      (#{record.businessId}, #{record.serviceId}, #{record.petTypeId}, #{record.breedNames}, #{record.createTime}, #{record.updateTime}, #{record.isAll}, #{record.breedNameList}, #{record.companyId})
    </foreach>
  </insert>

  <insert id="batchInsertRecordsWithCid">
    insert into moe_grooming.moe_grooming_service_breed_binding
    (`service_id`, `pet_type_id`, `breed_names`, `create_time`, `update_time`, `is_all`, `breed_name_list`, `company_id`)
    values
    <foreach collection="records" index="index" item="record" separator=",">
      (#{record.serviceId}, #{record.petTypeId}, #{record.breedNames}, #{record.createTime}, #{record.updateTime}, #{record.isAll}, #{record.breedNameList}, #{record.companyId})
    </foreach>
  </insert>

  <select id="selectByBusinessIdAndBreedName" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />, <include refid="Blob_Column_List" />
    from moe_grooming.moe_grooming_service_breed_binding
    where business_id = #{businessId}
    and (breed_names = #{breedName} or breed_name_list = JSON_ARRAY(#{breedName}))
      and status = 0
  </select>

  <update id="batchUpdateRecords" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding">
    <foreach collection="list" index="index" item="item" separator=";">
      update moe_grooming.moe_grooming_service_breed_binding
      <set>
        <if test="item.breedNames != null">
          breed_names = #{item.breedNames},
        </if>
        <if test="item.status != null">
          `status` = #{item.status},
        </if>
        <if test="item.isAll != null">
          is_all = #{item.isAll},
        </if>
        <if test="item.breedNameList != null">
          breed_name_list = #{item.breedNameList},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime},
        </if>
      </set>
      where id = #{item.id}
    </foreach>
  </update>
</mapper>
