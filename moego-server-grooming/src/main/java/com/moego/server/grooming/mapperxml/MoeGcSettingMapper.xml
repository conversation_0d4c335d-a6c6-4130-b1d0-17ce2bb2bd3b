<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="google_auth_id" jdbcType="INTEGER" property="googleAuthId" />
    <result column="sync_type" jdbcType="TINYINT" property="syncType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="calendar_name" jdbcType="VARCHAR" property="calendarName" />
    <result column="event_title" jdbcType="LONGVARCHAR" property="eventTitle" />
    <result column="event_description" jdbcType="LONGVARCHAR" property="eventDescription" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, google_auth_id, sync_type, status, create_time, update_time,
    calendar_name, event_title, event_description, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_gc_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_setting (business_id, staff_id, google_auth_id,
      sync_type, status, create_time,
      update_time, calendar_name, company_id,
      event_title, event_description)
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{googleAuthId,jdbcType=INTEGER},
      #{syncType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{calendarName,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT},
      #{eventTitle,jdbcType=LONGVARCHAR}, #{eventDescription,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="googleAuthId != null">
        google_auth_id,
      </if>
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calendarName != null">
        calendar_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="eventTitle != null">
        event_title,
      </if>
      <if test="eventDescription != null">
        event_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="googleAuthId != null">
        #{googleAuthId,jdbcType=INTEGER},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="calendarName != null">
        #{calendarName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="eventTitle != null">
        #{eventTitle,jdbcType=LONGVARCHAR},
      </if>
      <if test="eventDescription != null">
        #{eventDescription,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_setting
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="googleAuthId != null">
        google_auth_id = #{googleAuthId,jdbcType=INTEGER},
      </if>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="calendarName != null">
        calendar_name = #{calendarName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="eventTitle != null">
        event_title = #{eventTitle,jdbcType=LONGVARCHAR},
      </if>
      <if test="eventDescription != null">
        event_description = #{eventDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_setting
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      google_auth_id = #{googleAuthId,jdbcType=INTEGER},
      sync_type = #{syncType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      calendar_name = #{calendarName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
      event_title = #{eventTitle,jdbcType=LONGVARCHAR},
      event_description = #{eventDescription,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByStaffIdBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_setting
        where staff_id = #{staffId,jdbcType=INTEGER}
        AND business_id = #{businessId,jdbcType=INTEGER}
        AND status = 1
        order by id desc limit 1
    </select>
    <select id="selectByGoogleAuthId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_setting
        where
        google_auth_id = #{googleAuthId,jdbcType=INTEGER}
    </select>
    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_setting
        where
        business_id = #{businessId,jdbcType=INTEGER} and status = 1
    </select>
    <select id="selectNeedImportSetting" resultMap="BaseResultMap">
        select
          gcs.id, gcs.business_id, gcs.staff_id, gcs.google_auth_id, gcs.sync_type, gcs.status, gcs.create_time, gcs.update_time,
          gcs.calendar_name, gcs.event_title, gcs.event_description
        from moe_gc_setting gcs left join moe_gc_auth_staff gcas on gcs.google_auth_id = gcas.id
        where gcs.status = 1
        and gcas.status = 1
        and (gcs.sync_type=1 or gcs.sync_type=3)
    </select>
    <select id="selectNeedExportSetting" resultMap="BaseResultMap">
        select
          gcs.id, gcs.business_id, gcs.staff_id, gcs.google_auth_id, gcs.sync_type, gcs.status, gcs.create_time, gcs.update_time,
          gcs.calendar_name, gcs.event_title, gcs.event_description
        from moe_gc_setting gcs left join moe_gc_auth_staff gcas on gcs.google_auth_id = gcas.id
        where gcs.status = 1
        and gcas.status = 1
        and (gcs.sync_type=1 or gcs.sync_type=2)
    </select>
</mapper>
