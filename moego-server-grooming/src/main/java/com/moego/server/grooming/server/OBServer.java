package com.moego.server.grooming.server;

import com.moego.common.params.CustomerIdsParams;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.server.grooming.api.IOBServiceBase;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.params.BookOnlineAgreementParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.bo.AssignBO;
import com.moego.server.grooming.service.ob.OBCustomerService;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.PrepayService;
import com.moego.server.payment.dto.CustomerStripInfoSaveResponse;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class OBServer extends IOBServiceBase {

    private final MoeGroomingBookOnlineService groomingBookOnlineService;
    private final PrepayService prepayService;
    private final OBCustomerService obCustomerService;
    private final OBGroomingService obGroomingService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;

    @Override
    public boolean saveAgreementForObSubmit(SaveAgreementForObSubmitParam param) {
        List<SaveAgreementForObSubmitParam.Agreement> agreements = param.agreements();
        if (ObjectUtils.isEmpty(agreements)) {
            return true;
        }

        List<BookOnlineAgreementParams> params = agreements.stream()
                .map(e -> {
                    BookOnlineAgreementParams p = new BookOnlineAgreementParams();
                    p.setAgreementId(e.agreementId());
                    p.setAgreementConfirmed(e.agreementConfirmed());
                    p.setSignature(e.signature());
                    p.setAgreementHeader(e.agreementHeader());
                    p.setAgreementContent(e.agreementContent());
                    return p;
                })
                .toList();

        groomingBookOnlineService.saveAgreementForBookingRequest(params, param.bookingRequestId());
        return true;
    }

    @Override
    public SaveCardResult saveCard(SaveCardParam param) {
        CustomerStripInfoSaveResponse response = prepayService.saveCard(
                param.chargeToken(), param.businessId(), param.customerId(), param.isNewCustomer());
        return SaveCardResult.builder()
                .businessId(response.getBusinessId())
                .customerId(response.getCustomerId())
                .stripeCustomerId(response.getStripeCustomerId())
                .stripeBizAccountId(response.getStripeBizAccountId())
                .paymentMethodId(response.getPaymentMethodId())
                .cardNumber(response.getCardNumber())
                .cardType(response.getCardType())
                .build();
    }

    @Override
    public Map<Integer, CustomerHasRequestDTO> listCustomerHasRequestUpdate(CustomerIdsParams params) {
        return obCustomerService.listCustomerHasRequestUpdate(params.getBusinessId(), params.getCustomerIds());
    }

    @Override
    public AutoAssignResult doAutoAssign(BookOnlineSubmitParams obParams) {
        if (obParams.getCompanyId() == null && obParams.getBusinessId() != null) {
            var companyId = businessService
                    .getCompanyId(GetCompanyIdRequest.newBuilder()
                            .setBusinessId(obParams.getBusinessId())
                            .build())
                    .getCompanyId();
            obParams.setCompanyId(companyId);
        }
        AssignBO assignBO =
                obGroomingService.doAssign(obParams, obParams.getCustomerData().getCustomerId(), null);
        return AutoAssignResult.builder()
                .staffId(assignBO.getStaffId())
                .appointmentTime(assignBO.getAppointmentTime())
                .build();
    }
}
