package question

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
)

type ReadWriter interface {
	Create(ctx context.Context, question *Question) error
	Update(ctx context.Context, question *Question) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*Question, error)
	FindByFilter(ctx context.Context, filter Filter) ([]*Question, error)
	BatchCreate(ctx context.Context, questions []*Question) error
	BatchUpdate(ctx context.Context, questions []*Question) error
	BatchDelete(ctx context.Context, filter Filter) error
	BatchUpsert(ctx context.Context, questions []*Question) error
	FindByFilters(ctx context.Context, filters []Filter) ([]*Question, error)
	FindByFilterAndTitles(ctx context.Context, filter Filter, titles []string) ([]*Question, error)
	Count(ctx context.Context, param reportrepo.BaseParam) (int64, error)
}

type questionImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportQuestionRepo() ReadWriter {
	database := db.GetDB()

	return &questionImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *questionImpl) Count(ctx context.Context, param reportrepo.BaseParam) (int64, error) {
	var count int64
	var clauses []clause.Expression
	if param.BusinessID > 0 {
		clauses = []clause.Expression{
			clause.Eq{Column: ColumnQuestionBusinessID, Value: param.BusinessID},
		}
	}
	if err := i.db.WithContext(ctx).Clauses(clauses...).Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *questionImpl) Create(ctx context.Context, question *Question) error {
	return i.db.WithContext(ctx).Create(question).Error
}

func (i *questionImpl) Update(ctx context.Context, question *Question) error {
	result := i.db.WithContext(ctx).Save(question)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *questionImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&Question{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *questionImpl) FindByID(ctx context.Context, id int64) (*Question, error) {
	var question Question
	err := i.db.WithContext(ctx).First(&question, id).Error
	if err != nil {
		return nil, err
	}

	return &question, nil
}

func (i *questionImpl) FindByFilter(ctx context.Context, filter Filter) ([]*Question, error) {
	var questions []*Question

	clauses := make([]clause.Expression, 0)
	if filter.CompanyID != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionCompanyID, Value: *filter.CompanyID})
	}
	if filter.BusinessID != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionBusinessID, Value: *filter.BusinessID})
	}
	if filter.CareType != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionCareType, Value: *filter.CareType})
	}

	err := i.db.WithContext(ctx).
		Clauses(clauses...).
		Order(ColumnQuestionSort).
		Find(&questions).Error
	if err != nil {
		return nil, err
	}

	return questions, nil
}

func (i *questionImpl) BatchCreate(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).CreateInBatches(questions, MaxBatchCreateSize).Error
}

func (i *questionImpl) BatchUpdate(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}

	for _, question := range questions {
		if err := i.db.WithContext(ctx).Updates(question).Error; err != nil {
			return err
		}
	}

	return nil
}

func (i *questionImpl) BatchDelete(ctx context.Context, filter Filter) error {
	clauses := make([]clause.Expression, 0)
	if filter.CompanyID != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionCompanyID, Value: *filter.CompanyID})
	}
	if filter.BusinessID != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionBusinessID, Value: *filter.BusinessID})
	}
	if filter.CareType != nil {
		clauses = append(clauses, clause.Eq{Column: ColumnQuestionCareType, Value: *filter.CareType})
	}
	if len(filter.IDList) > 0 {
		clauses = append(clauses, clause.IN{Column: ColumnQuestionID, Values: convertToInterface(filter.IDList)})
	}
	result := i.db.WithContext(ctx).
		Clauses(clauses...).
		Delete(&Question{})
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (i *questionImpl) BatchUpsert(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除该模板下的所有问题
		if len(questions) > 0 {
			clauses := []clause.Expression{
				clause.Eq{Column: ColumnQuestionCompanyID, Value: questions[0].CompanyID},
				clause.Eq{Column: ColumnQuestionBusinessID, Value: questions[0].BusinessID},
				clause.Eq{Column: ColumnQuestionCareType, Value: questions[0].CareType},
			}
			if err := tx.Clauses(clauses...).Delete(&Question{}).Error; err != nil {
				return err
			}
		}

		// 批量创建新问题
		return tx.CreateInBatches(questions, MaxBatchCreateSize).Error
	})
}

func (i *questionImpl) FindByFilters(ctx context.Context, filters []Filter) ([]*Question, error) {
	var questions []*Question

	clauses := make([]clause.Expression, 0)
	for _, filter := range filters {
		subClauses := make([]clause.Expression, 0)
		if filter.CompanyID != nil {
			subClauses = append(subClauses, clause.Eq{Column: ColumnQuestionCompanyID, Value: *filter.CompanyID})
		}
		if filter.BusinessID != nil {
			subClauses = append(subClauses, clause.Eq{Column: ColumnQuestionBusinessID, Value: *filter.BusinessID})
		}
		if filter.CareType != nil {
			subClauses = append(subClauses, clause.Eq{Column: ColumnQuestionCareType, Value: *filter.CareType})
		}
		clauses = append(clauses, clause.Or(subClauses...))
	}

	err := i.db.WithContext(ctx).
		Clauses(clauses...).
		Order(ColumnQuestionSort).
		Find(&questions).Error
	if err != nil {
		return nil, err
	}

	return questions, nil
}

func (i *questionImpl) FindByFilterAndTitles(
	ctx context.Context, filter Filter, titles []string) ([]*Question, error) {
	var questions []*Question

	clauses := make([]clause.Expression, 0)
	clauses = append(clauses, clause.Eq{Column: ColumnQuestionCompanyID, Value: filter.CompanyID})
	clauses = append(clauses, clause.Eq{Column: ColumnQuestionBusinessID, Value: filter.BusinessID})
	clauses = append(clauses, clause.Eq{Column: ColumnQuestionCareType, Value: filter.CareType})
	clauses = append(clauses, clause.IN{Column: ColumnQuestionTitle, Values: convertToInterface(titles)})

	err := i.db.WithContext(ctx).
		Clauses(clauses...).
		Order(ColumnQuestionSort).
		Find(&questions).Error
	if err != nil {
		return nil, err
	}

	return questions, nil
}

// convertToInterface 将各种类型的切片转换为[]interface{}
func convertToInterface[T any](slice []T) []interface{} {
	if len(slice) == 0 {
		return nil
	}
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}

	return result
}
