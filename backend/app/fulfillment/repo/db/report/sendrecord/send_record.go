package sendrecord

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type ReadWriter interface {
	Create(ctx context.Context, record *SendRecord) error
	Update(ctx context.Context, record *SendRecord) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*SendRecord, error)
	FindByReportIDAndSendMethod(
		ctx context.Context, reportID int64, sendMethod int32) (*SendRecord, error)
	Upsert(ctx context.Context, record *SendRecord) error
	FindByUniqueKeys(ctx context.Context, uniqueKeys []Filter) ([]*SendRecord, error)
	BatchCreate(ctx context.Context, records []*SendRecord) error
	List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*SendRecord, error)
	Count(ctx context.Context, baseParam *BaseParam, filter *Filter) (int64, error)
}

type sendRecordImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportSendRecordRepo() ReadWriter {
	database := db.GetDB()

	return &sendRecordImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *sendRecordImpl) Create(ctx context.Context, record *SendRecord) error {
	return i.db.WithContext(ctx).Create(record).Error
}

func (i *sendRecordImpl) Update(ctx context.Context, record *SendRecord) error {
	result := i.db.WithContext(ctx).Save(record)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *sendRecordImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&SendRecord{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *sendRecordImpl) FindByID(ctx context.Context, id int64) (*SendRecord, error) {
	var record SendRecord
	err := i.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return nil, err
	}

	return &record, nil
}

func (i *sendRecordImpl) FindByReportIDAndSendMethod(
	ctx context.Context, reportID int64, sendMethod int32) (*SendRecord, error) {
	var record SendRecord
	clauses := []clause.Expression{
		clause.Eq{Column: ColumnSendRecordReportID, Value: reportID},
		clause.Eq{Column: ColumnSendRecordSendMethod, Value: sendMethod},
	}
	err := i.db.WithContext(ctx).
		Clauses(clauses...).
		First(&record).Error
	if err != nil {
		return nil, err
	}

	return &record, nil
}

func (i *sendRecordImpl) Upsert(ctx context.Context, record *SendRecord) error {
	// 尝试根据 report_id + send_method 查找现有记录
	existing, err := i.FindByReportIDAndSendMethod(ctx, record.ReportID, record.SendMethod)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，创建新记录
			return i.Create(ctx, record)
		}

		return err
	}

	// 记录存在，更新现有记录
	record.ID = existing.ID
	record.CreateTime = existing.CreateTime // 保持原始创建时间

	return i.Update(ctx, record)
}

func (i *sendRecordImpl) FindByUniqueKeys(ctx context.Context,
	uniqueKeys []Filter) ([]*SendRecord, error) {
	if len(uniqueKeys) == 0 {
		return []*SendRecord{}, nil
	}

	var records []*SendRecord
	query := i.db.WithContext(ctx)

	// 构建 OR 条件查询
	var orClauses []clause.Expression

	for _, uniqueKey := range uniqueKeys {
		var andClauses []clause.Expression
		if uniqueKey.SendMethod != 0 {
			andClauses = []clause.Expression{
				clause.Eq{Column: ColumnSendRecordReportID, Value: uniqueKey.ReportID},
				clause.Eq{Column: ColumnSendRecordSendMethod, Value: uniqueKey.SendMethod},
			}
		} else {
			andClauses = []clause.Expression{
				clause.Eq{Column: ColumnSendRecordReportID, Value: uniqueKey.ReportID},
			}
		}
		orClauses = append(orClauses, clause.And(andClauses...))
	}

	if len(orClauses) > 0 {
		query = query.Clauses(clause.Or(orClauses...))
	}

	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to query records by unique keys: %w", err)
	}

	return records, nil
}

func (i *sendRecordImpl) BatchCreate(ctx context.Context, records []*SendRecord) error {
	if len(records) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).CreateInBatches(records, MaxBatchCreateSize).Error
}

func (i *sendRecordImpl) List(ctx context.Context, baseParam *BaseParam, filter *Filter) ([]*SendRecord, error) {
	if baseParam == nil {
		return []*SendRecord{}, nil
	}

	query := i.buildQuery(ctx, baseParam, filter)
	var records []*SendRecord

	// 应用分页
	offset, limit := i.buildPaginationInfo(baseParam.PaginationInfo)
	if err := query.
		Offset(int(offset)).
		Limit(int(limit)).
		Clauses(clause.OrderBy{Columns: []clause.OrderByColumn{
			{Column: clause.Column{Name: ColumnSendRecordCreateTime}, Desc: true},
		}}).
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to list send records: %w", err)
	}

	return records, nil
}

func (i *sendRecordImpl) Count(ctx context.Context, baseParam *BaseParam, filter *Filter) (int64, error) {
	if baseParam == nil {
		return 0, nil
	}

	query := i.buildQuery(ctx, baseParam, filter)
	var count int64

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count send records: %w", err)
	}

	return count, nil
}

// buildQuery 构建查询条件
func (i *sendRecordImpl) buildQuery(ctx context.Context, baseParam *BaseParam, filter *Filter) *gorm.DB {
	query := i.db.WithContext(ctx).Model(&SendRecord{})

	// 应用基础参数过滤条件
	if baseParam.CompanyID > 0 {
		query = query.Where(ColumnSendRecordCompanyID+" = ?", baseParam.CompanyID)
	}
	if baseParam.BusinessID > 0 {
		query = query.Where(ColumnSendRecordBusinessID+" = ?", baseParam.BusinessID)
	}

	// 应用过滤条件
	if filter != nil {
		if filter.ReportID > 0 {
			query = query.Where(ColumnSendRecordReportID+" = ?", filter.ReportID)
		}
		if len(filter.AppointmentIDs) > 0 {
			query = query.Where(ColumnSendRecordAppointmentID+" IN ?", filter.AppointmentIDs)
		}
		if len(filter.PetIDs) > 0 {
			query = query.Where(ColumnSendRecordPetID+" IN ?", filter.PetIDs)
		}
		if len(filter.SendMethods) > 0 {
			query = query.Where(ColumnSendRecordSendMethod+" IN ?", filter.SendMethods)
		}
		if len(filter.ReportIDs) > 0 {
			query = query.Where(ColumnSendRecordReportID+" IN ?", filter.ReportIDs)
		}
		// 注意：CareTypes 过滤需要通过 JOIN report 表来实现，这里暂时跳过
		// 如果需要支持 CareTypes 过滤，需要修改查询逻辑
	}

	return query
}

// buildPaginationInfo 构建分页信息
func (i *sendRecordImpl) buildPaginationInfo(paginationInfo *PaginationInfo) (int32, int32) {
	if paginationInfo == nil {
		return defaultOffset, defaultLimit // 默认分页
	}

	offset := paginationInfo.Offset
	limit := paginationInfo.Limit
	if limit <= 0 {
		limit = defaultLimit
	}

	return offset, limit
}
