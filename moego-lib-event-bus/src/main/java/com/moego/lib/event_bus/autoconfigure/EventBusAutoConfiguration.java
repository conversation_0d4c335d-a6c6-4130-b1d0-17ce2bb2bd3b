package com.moego.lib.event_bus.autoconfigure;

import com.moego.lib.event_bus.env.EnvHelper;
import com.moego.lib.event_bus.producer.Producer;
import java.util.Map;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.core.KafkaTemplate;

@AutoConfiguration
@EnableConfigurationProperties({EventBusProperties.class})
public class EventBusAutoConfiguration {

    @Bean
    @ConditionalOnProperty(prefix = "moego.event-bus.producer", name = "enabled", havingValue = "true")
    static BeanDefinitionRegistryPostProcessor eventBusBeanDefinitionRegistryPostProcessor() {
        return new EventBusBeanDefinitionRegistryPostProcessor();
    }

    @Bean
    @ConditionalOnProperty(prefix = "moego.event-bus.producer", name = "enabled", havingValue = "true")
    Producer producer(
            Map<String, KafkaTemplate<String, String>> kafkaTemplates,
            EventBusProperties properties,
            EnvHelper envHelper) {
        return new Producer(kafkaTemplates, properties, envHelper);
    }
}
