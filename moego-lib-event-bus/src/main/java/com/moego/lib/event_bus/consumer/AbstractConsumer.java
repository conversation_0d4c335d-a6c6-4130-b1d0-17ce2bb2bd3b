package com.moego.lib.event_bus.consumer;

import static org.apache.kafka.clients.consumer.ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.AUTO_OFFSET_RESET_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.GROUP_ID_CONFIG;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.google.protobuf.TypeRegistry;
import com.moego.lib.event_bus.autoconfigure.EventBusProperties;
import com.moego.lib.event_bus.env.EnvHelper;
import com.moego.lib.event_bus.event.EventParser;
import com.moego.lib.event_bus.event.EventRecord;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.ResolvableType;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.AcknowledgingMessageListener;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.KafkaMessageListenerContainer;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
public abstract class AbstractConsumer<T extends Message>
        implements AcknowledgingMessageListener<String, String>,
                SmartInitializingSingleton,
                ApplicationListener<ApplicationReadyEvent> {

    private final TypeRegistry typeRegistry;
    private final Class<T> messageClass;

    private final AtomicBoolean isAppReady = new AtomicBoolean(false);

    @Autowired
    private EventBusProperties properties;

    @Autowired
    private EnvHelper envHelper;

    @Value("${spring.application.name:moego}")
    private String defaultGroupId;

    @SuppressFBWarnings("CT_CONSTRUCTOR_THROW")
    protected AbstractConsumer() {
        ResolvableType resolvableType = ResolvableType.forClass(getClass());
        // 获取泛型参数
        Class<T> messageClass =
                (Class<T>) resolvableType.getSuperType().getGeneric(0).resolve();
        assert messageClass != null;
        this.messageClass = messageClass;

        try {
            var descriptor = (Descriptors.Descriptor)
                    messageClass.getMethod("getDescriptor").invoke(null);
            this.typeRegistry = TypeRegistry.newBuilder().add(descriptor).build();
        } catch (Throwable e) {
            throw new RuntimeException("Failed to get descriptor of class: %s".formatted(messageClass.getName()), e);
        }
    }

    /**
     * 待消费的 topic 名称. 请子类实现该方法以提供 topic 名称
     * @return topic name
     */
    protected abstract String topicName();

    /**
     * 待消费 topic 所在的 broker 名称. 默认取第一个 broker.
     * 如果需要自定义 broker, 请子类实现该方法并返回对应的 broker 名称.
     * @return broker name
     */
    protected String brokerName() {
        return null;
    }

    /**
     * 消费者组 ID. 默认会根据应用名称生成一个消费者组 ID.
     * 如果需要自定义消费者组 ID, 请子类实现该方法并返回自定义的消费者组 ID.
     * @return group id
     */
    protected String groupId() {
        return defaultGroupId;
    }

    /**
     * event 消费逻辑, 需要子类自行实现.
     * 子类的实现时需要保证:
     * 1. 如果消费失败, 抛异常. 无异常认为消费成功.
     * 2. 消费逻辑务必保证幂等性, 因为消息可能会重复投递或者重复消费.
     * @param event event
     */
    protected abstract void consume(EventRecord<T> event);

    /**
     * 创建 ContainerProperties, 用于创建 KafkaMessageListenerContainer.
     * @return ContainerProperties
     */
    private ContainerProperties containerProperties() {
        var topicName = envHelper.convertTopicName(topicName());
        ContainerProperties containerProperties = new ContainerProperties(topicName);
        containerProperties.setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        containerProperties.setMessageListener(this);
        return containerProperties;
    }

    /**
     * 创建 DefaultKafkaConsumerFactory, 用于创建 KafkaMessageListenerContainer.
     * @return DefaultKafkaConsumerFactory
     */
    private DefaultKafkaConsumerFactory<String, String> consumerFactory() {
        var broker = properties.getBrokers().stream()
                .filter(b -> brokerName() == null || b.getName().equals(brokerName()))
                .findFirst()
                .orElse(null);

        if (broker == null) {
            throw new IllegalArgumentException("Broker not found");
        }

        if (CollectionUtils.isEmpty(broker.getAddresses())) {
            throw new IllegalArgumentException("Broker address is empty");
        }

        var groupId = Optional.ofNullable(properties.getConsumer().getGroupId())
                .filter(StringUtils::hasText)
                .orElse(groupId());
        if (!StringUtils.hasText(groupId)) {
            throw new IllegalArgumentException("groupId should not be empty");
        }
        log.info("Consumer group id: {}", groupId);

        // 消费者配置
        var deserializer = new StringDeserializer();

        // 自动偏移重置
        var autoOffsetReset = properties.getConsumer().getAutoOffsetReset();
        if (autoOffsetReset == null) {
            autoOffsetReset = AutoOffsetReset.LATEST;
        }
        // 自动提交偏移量
        var enableAutoCommit = properties.getConsumer().isEnableAutoCommit();
        int autoCommitIntervalMs = properties.getConsumer().getAutoCommitIntervalMs();

        var props = new HashMap<String, Object>();
        props.put(BOOTSTRAP_SERVERS_CONFIG, broker.getAddresses());
        props.put(GROUP_ID_CONFIG, groupId);
        // 在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
        props.put(AUTO_OFFSET_RESET_CONFIG, autoOffsetReset.getValue());
        // 不自动提交偏移量, 统一手动 ack
        props.put(ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        // 自动提交偏移量的时间间隔
        props.put(AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitIntervalMs);

        // security
        if (broker.getSecurity().isEnabled()) {
            props.putAll(broker.getSecurity().getProperties());
        }

        return new DefaultKafkaConsumerFactory<>(props, deserializer, deserializer);
    }

    /**
     * 在所有单例 bean 实例化完成后, 启动 KafkaMessageListenerContainer.
     */
    @Override
    public void afterSingletonsInstantiated() {
        if (properties.getConsumer().isEnabled()) {
            var container = new KafkaMessageListenerContainer<>(consumerFactory(), containerProperties());
            container.start();
        }
    }

    /**
     * 监听 Spring boot 应用启动事件
     * @param event ApplicationReadyEvent
     */
    @Override
    public final void onApplicationEvent(ApplicationReadyEvent event) {
        isAppReady.set(true);
    }

    /**
     * 接收 kafka 消息
     * @param record the data to be processed.
     * @param ack the acknowledgment.
     */
    @Override
    public final void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack) {
        // 应用成功启动之前，不处理消息
        if (!isAppReady.get()) {
            return;
        }

        var topic = record.topic();
        var key = record.key();
        var message = record.value();

        if (properties.getConsumer().isLogReceive()) {
            log.info("Received message: topic={}, key={}, message={}", topic, key, message);
        }

        // 解析 event
        try {
            var event = EventParser.parse(record, typeRegistry, messageClass);
            consume(event);
        } catch (Throwable t) {
            log.error("Failed to parse event of topic: {}, key: {}, message: {}", topic, key, message, t);
            // TODO: 采集指标, 统计解析失败的次数
            return;
        }

        if (ack != null) {
            ack.acknowledge();
        }
        // TODO: 采集指标, 统计消息消费成功的次数
    }
}
