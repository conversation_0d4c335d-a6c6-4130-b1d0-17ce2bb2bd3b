package com.moego.lib.risk.control.recaptcha;

import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc;
import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc.RecaptchaServiceBlockingStub;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpEnabled;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataClientInterceptor;
import com.moego.lib.risk.control.config.RiskControlProperties;
import com.moego.lib.risk.control.recaptcha.grpc.GrpcMethodRecaptchaHolder;
import com.moego.lib.risk.control.recaptcha.grpc.RecaptchaServerInterceptor;
import com.moego.lib.risk.control.recaptcha.http.RecaptchaHandlerInterceptor;
import io.grpc.BindableService;
import io.grpc.ManagedChannelBuilder;
import java.util.stream.Collectors;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@EnableConfigurationProperties(RiskControlProperties.class)
@Configuration(proxyBeanMethods = false)
public class RecaptchaConfiguration {

    @Bean
    public RecaptchaServiceBlockingStub recaptchaServiceBlockingStub(
            RiskControlProperties properties, GrpcMetadataClientInterceptor interceptor) {
        return RecaptchaServiceGrpc.newBlockingStub(
                ManagedChannelBuilder.forAddress(properties.getName(), properties.getPort())
                        .usePlaintext()
                        .intercept(interceptor)
                        .build());
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpEnabled
    static class Http {
        @Bean
        @Order(BeanOrder.GrpcServer.AUTH + 1)
        @ConditionalOnHttpEnabled
        public RecaptchaHandlerInterceptor recaptchaHandlerInterceptor(
                RecaptchaServiceBlockingStub recaptchaServiceBlockingStub, StringRedisTemplate stringRedisTemplate) {
            return new RecaptchaHandlerInterceptor(recaptchaServiceBlockingStub, stringRedisTemplate);
        }

        @Bean
        @Order(BeanOrder.GrpcServer.AUTH + 1)
        public WebMvcConfigurer recaptchaWebMvcConfigurer(RecaptchaHandlerInterceptor recaptchaHandlerInterceptor) {
            return new WebMvcConfigurer() {
                @Override
                public void addInterceptors(InterceptorRegistry registry) {
                    registry.addInterceptor(recaptchaHandlerInterceptor);
                }
            };
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcEnabled
    static class Grpc {
        @Bean
        @Order(BeanOrder.GrpcServer.AUTH + 1)
        @ConditionalOnGrpcServerEnabled
        public RecaptchaServerInterceptor recaptchaServerInterceptor(
                GrpcMethodRecaptchaHolder grpcMethodRecaptchaHolder,
                RecaptchaServiceBlockingStub recaptchaServiceBlockingStub,
                StringRedisTemplate stringRedisTemplate) {
            return new RecaptchaServerInterceptor(
                    grpcMethodRecaptchaHolder, recaptchaServiceBlockingStub, stringRedisTemplate);
        }

        @Bean
        @ConditionalOnGrpcServerEnabled
        public GrpcMethodRecaptchaHolder grpcMethodRecaptchaHolder(ObjectProvider<BindableService> servicesProvider) {
            return new GrpcMethodRecaptchaHolder(
                    servicesProvider.orderedStream().collect(Collectors.toList()));
        }
    }
}
