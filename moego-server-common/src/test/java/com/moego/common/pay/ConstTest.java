package com.moego.common.pay;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.utils.PaymentUtil;
import com.moego.common.utils.payment.ProcessingFee;
import com.moego.common.utils.payment.ProcessingFeePair;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/5/27 4:40 PM
 */
public class ConstTest {

    @Test
    public void testStripeFee() {
        Integer amount = 10000;
        Integer cofAmount = PaymentUtil.getStripeFeeByPaymentMethod(amount, PaymentStripeStatus.COF_PAY);
        Integer readerAmount = PaymentUtil.getStripeFeeByPaymentMethod(amount, PaymentStripeStatus.BLUETOOTH_PAY);
        assertEquals(Double.valueOf(amount * 0.034 + 30).intValue(), cofAmount.intValue());
        assertEquals(Double.valueOf(amount * 0.029 + 50).intValue(), readerAmount.intValue());
        assertEquals(
                PaymentUtil.getStripeFeeByPaymentMethod(-1, PaymentStripeStatus.BLUETOOTH_PAY)
                        .intValue(),
                0);
        assertEquals(
                PaymentUtil.getStripeFeeByPaymentMethod(amount, null),
                PaymentUtil.getStripeFeeByPaymentMethod(amount, PaymentStripeStatus.CARD_PAY));
    }

    @Test
    public void testGetStripeFee() {
        ProcessingFeePair pair = new ProcessingFeePair();
        pair.setCent(0);
        pair.setPercent(0.05);
        ProcessingFee processingFee = new ProcessingFee();
        processingFee.setOnline(pair);
        Byte paymentMethod = 1;

        Integer chargeAmount = 1025;
        Integer expectedFee = 51;
        // 51.25 -> 51
        Integer actualFee = PaymentUtil.getStripeFee(chargeAmount, paymentMethod, processingFee);
        assertEquals(expectedFee, actualFee);

        chargeAmount = 1030;
        expectedFee = 52;
        // 51.5->52
        actualFee = PaymentUtil.getStripeFee(chargeAmount, paymentMethod, processingFee);
        assertEquals(expectedFee, actualFee);

        chargeAmount = 1050;
        expectedFee = 52;
        // 52.5->52
        actualFee = PaymentUtil.getStripeFee(chargeAmount, paymentMethod, processingFee);
        assertEquals(expectedFee, actualFee);

        chargeAmount = 1058;
        expectedFee = 53;
        // 52.9->53
        actualFee = PaymentUtil.getStripeFee(chargeAmount, paymentMethod, processingFee);
        assertEquals(expectedFee, actualFee);
    }
}
