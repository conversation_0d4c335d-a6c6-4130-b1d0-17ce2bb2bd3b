package com.moego.common.exception;

import static com.moego.common.constant.CommonConstant.X_MOE_STATUS;
import static com.moego.common.enums.ResponseCodeEnum.CODE_SEND_LIMIT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * {@link ExceptionHandlerAdvice} tester.
 */
@SpringBootTest(
        classes = ExceptionHandlerAdviceIntegrationTests.Config.class,
        webEnvironment = RANDOM_PORT,
        properties = {"spring.application.name=exception-handler-advice-integration-tests"})
class ExceptionHandlerAdviceIntegrationTests {

    @Autowired
    private TestRestTemplate restTemplate;

    @LocalServerPort
    int port;

    @Test
    void testRuntimeException() {
        ResponseEntity<String> resp =
                restTemplate.getForEntity("http://localhost:" + port + "/runtimeException", String.class);
        assertThat(resp.getStatusCodeValue()).isEqualTo(500);
        assertThat(resp.getHeaders()).containsKey(X_MOE_STATUS);
        assertThat(resp.getBody())
                .contains(
                        "{\"code\":500,\"message\":\"server error\",\"data\":\"test\",\"causedBy\":\"java.lang.RuntimeException: test\",\"success\":false}");
    }

    @Test
    void testCommonException() {
        ResponseEntity<String> resp =
                restTemplate.getForEntity("http://localhost:" + port + "/commonException", String.class);
        assertThat(resp.getStatusCodeValue()).isEqualTo(200);
        assertThat(resp.getHeaders()).containsKey(X_MOE_STATUS);
        assertThat(resp.getHeaders().get(X_MOE_STATUS))
                .containsExactly(CODE_SEND_LIMIT.getCode().toString());
        assertThat(resp.getBody()).contains(CODE_SEND_LIMIT.getMessage());
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Config {

        @RestController
        static class Controller {

            @GetMapping("/runtimeException")
            public String runtimeException() {
                throw new RuntimeException("test");
            }

            @GetMapping("/commonException")
            public String commonException() {
                throw new CommonException(CODE_SEND_LIMIT);
            }
        }
    }
}
