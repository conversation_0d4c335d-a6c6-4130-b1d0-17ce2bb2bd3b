package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class LocalizeUtilTest {

    @BeforeAll
    public static void setUp() throws Exception {}

    @Test
    public void removeCountryCode_US_umber_success() {
        String actual = LocalizeUtil.removeE164NumberCountryCode("+1**********");
        assertEquals("**********", actual);
    }

    @Test
    public void removeCountryCode_UK_number_success() {
        String actual = LocalizeUtil.removeE164NumberCountryCode("+44**********");
        assertEquals("**********", actual);
    }

    @Test
    public void removeCountryCode_Australia_number_success() {
        String actual = LocalizeUtil.removeE164NumberCountryCode("+61*********");
        assertEquals("*********", actual);
    }
}
