package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.text.ParseException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;

public class DateTimeTest {

    @Test
    public void testSquareDate() {
        String exp = "2121-07-23T08:04:49Z";
        assertFalse(DateUtil.isSquareExpiresBeforeNow(exp));
    }

    @Test
    public void testDateConvert() {
        String date = "2012-12-23";
        LocalDate ld1 = LocalDate.parse(date);
        LocalDate ld2 = LocalDate.of(2012, 12, 23);
        assertEquals(ld1, ld2); // 自动调用object的equals方法
        System.out.println(TimeUnit.HOURS.toSeconds(48));
        System.out.println(48 * 60 * 60);
    }

    @Test
    public void dateDemo() {
        // localDate表示特定的某一天
        LocalDate localDate = LocalDate.now();
        // format output
        System.out.println(localDate.toString());
        // 一年中的第几天
        System.out.println(localDate.getDayOfYear());
        // 一个月中的第几天
        System.out.println(localDate.getDayOfMonth());
    }

    @Test
    public void testNextMonthDay() {
        // test across two months
        LocalDate dayOfMonthEnd = LocalDate.parse("2020-11-30");
        LocalDate nextDay = dayOfMonthEnd.plusDays(1);
        assertEquals(LocalDate.parse("2020-12-01"), nextDay);
        // test October which has 31 days
        LocalDate dayOfMonthEnd2 = LocalDate.parse("2020-10-30");
        LocalDate nextDay2 = dayOfMonthEnd2.plusDays(1);
        assertEquals(LocalDate.parse("2020-10-31"), nextDay2);
    }

    @Test
    public void testLocalTime() {
        LocalTime lt = LocalTime.now();
        System.out.println(lt.getMinute());
        LocalTime sixThirty = LocalTime.parse("06:30");
        assertEquals(6, sixThirty.getHour());
        LocalTime sevenThirty = sixThirty.plusHours(1);
        assertEquals(7, sevenThirty.getHour());
        assertTrue(sixThirty.isBefore(sevenThirty));
        System.out.println(LocalTime.MAX);
    }

    @Test
    public void testLocalDateTime() {
        LocalDateTime dt = LocalDateTime.of(2015, Month.FEBRUARY, 20, 06, 30);
        // you can use api from date and time
        System.out.println(dt);
    }

    @Test
    public void testDurationAndPeriod() {
        LocalDate initialDate = LocalDate.parse("2007-05-10");

        LocalDate finalDate = initialDate.plus(Period.ofDays(5));
        assertEquals(-5, ChronoUnit.DAYS.between(finalDate, initialDate));

        LocalTime initialTime = LocalTime.of(6, 30, 0);

        LocalTime finalTime = initialTime.plus(Duration.ofSeconds(30));
        assertEquals(30, ChronoUnit.SECONDS.between(initialTime, finalTime));
    }

    @Test
    public void testDateFormat() {
        LocalDateTime localDateTime = LocalDateTime.of(2015, Month.JANUARY, 25, 6, 30);
        System.out.println(localDateTime.format(DateTimeFormatter.BASIC_ISO_DATE));
        System.out.println(localDateTime.format(DateTimeFormatter.ISO_DATE));
        System.out.println(localDateTime.format(DateTimeFormatter.ofLocalizedDateTime(FormatStyle.MEDIUM)));
        System.out.println(localDateTime.format(
                DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm").withLocale(Locale.UK)));
    }

    @Test
    public void testCalender() throws ParseException {
        Date startDate = DateUtil.convertStringToDate("2020-12-20");
        Date endDate = DateUtil.convertStringToDate("2020-12-25");
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(startDate);
        do {
            System.out.println(calendar.getTime());
            calendar.add(Calendar.DATE, 1);
        } while (calendar.getTime().getTime() <= endDate.getTime());
    }
}
