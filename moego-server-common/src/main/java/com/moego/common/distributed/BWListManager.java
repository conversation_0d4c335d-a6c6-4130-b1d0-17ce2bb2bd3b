package com.moego.common.distributed;

import com.moego.common.utils.RedisUtil;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/8/7
 *
 * based on redis, provide Black and White List Management
 */
@Service
@ConditionalOnClass(RedisTemplate.class)
@Slf4j
public class BWListManager {

    private final RedisUtil redisUtil;

    public static final String ORDER_REINVENT = "order_reinvent";
    private static final String WHITE_PREFIX = "WHITE_LIST:";
    private static final String BLACK_PREFIX = "BLACK_LIST:";
    public static final String NAMESPACE = System.getenv("NAMESPACE");
    public static final String NAMESPACE_PROD = "ns-production";

    private static final Set<String> FEATURE_SET = Set.of(ORDER_REINVENT);

    public BWListManager(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    private String getWhiteListRedisKey(String featureName) {
        if (!FEATURE_SET.contains(featureName)) {
            throw new IllegalArgumentException("Feature name is not supported");
        }
        return WHITE_PREFIX + featureName;
    }

    private String getBlackListRedisKey(String featureName) {
        if (!FEATURE_SET.contains(featureName)) {
            throw new IllegalArgumentException("Feature name is not supported");
        }
        return BLACK_PREFIX + featureName;
    }

    public boolean isInWhiteList(String featureName, String value) {
        return redisUtil.sIsMember(getWhiteListRedisKey(featureName), value);
    }

    public Long addToWhiteList(String featureName, String value) {
        String whiteListRedisKey = getWhiteListRedisKey(featureName);
        log.info("Add {} to white list of {}", value, whiteListRedisKey);
        return redisUtil.sAdd(whiteListRedisKey, value);
    }

    public Long addToWhiteList(String featureName, String[] values) {
        String whiteListRedisKey = getWhiteListRedisKey(featureName);
        log.info("Add {} to white list of {}", values, whiteListRedisKey);
        return redisUtil.sAdd(whiteListRedisKey, values);
    }

    public Long removeFromWhiteList(String featureName, String value) {
        String whiteListRedisKey = getWhiteListRedisKey(featureName);
        log.info("Remove {} from white list of {}", value, whiteListRedisKey);
        return redisUtil.sRemove(whiteListRedisKey, value);
    }

    public boolean isInBlackList(String featureName, String value) {
        return redisUtil.sIsMember(getBlackListRedisKey(featureName), value);
    }

    public Long addToBlackList(String featureName, String value) {
        String redisKey = getBlackListRedisKey(featureName);
        log.info("Add {} to black list of {}", value, redisKey);
        return redisUtil.sAdd(redisKey, value);
    }

    public Long removeFromBlackList(String featureName, String value) {
        String redisKey = getBlackListRedisKey(featureName);
        log.info("Remove {} from black list of {}", value, redisKey);
        return redisUtil.sRemove(redisKey, value);
    }

    public Set<String> getAllWhitelist(String featureName) {
        return redisUtil.setMembers(getWhiteListRedisKey(featureName));
    }
}
