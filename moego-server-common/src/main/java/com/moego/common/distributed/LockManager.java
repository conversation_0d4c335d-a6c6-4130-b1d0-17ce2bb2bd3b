package com.moego.common.distributed;

import com.moego.common.utils.RedisUtil;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/9/6 6:00 PM
 */
@Service
@ConditionalOnClass(RedisTemplate.class)
@Slf4j
public class LockManager {

    private static final int DEFAULT_EXPIRE_TIME_IN_SECONDS = 60;

    public static final Long REMINDER_TASK_DEFALUT_EXPIRE_SECONDS = 600L;
    private static final String PREFIX = "DISTRIBUTED_LOCK:";

    private static final int DEFAULT_RETRY_COUNT = 3;
    private static final long DEFAULT_RETRY_INTERVAL_MS = 100;

    /**
     * 需要并发控制的资源在这里统一定义，防止key冲突
     */
    public static final String INVOICE = "invoice";

    public static final String PET_DETAIL = "petDetail";
    public static final String OB_SUBMIT = "obSubmit";
    public static final String GC_IMPORT = "gcImport";
    public static final String OB_PAY = "obPay";
    public static final String OB_ABANDON = "OB_ABANDON";
    // grooming package add
    public static final String GROOMING_PACKAGE_ADD = "grooming_package_add";
    // mass text 发送任务
    public static final String MASS_TEXT_BATCH = "mass_text_send";
    // review booster 发送任务
    public static final String AUTO_REVIEW_BOOSTER = "auto_review_booster";
    public static final String AUTO_REVIEW_BOOSTER_FOR_GROOMINGID = "arb_grooming";
    public static final String STRIPE_CUSTOMER = "STRIPE_CUSTOMER";
    public static final String BUSINESS_REFERRAL = "BUSINESS_REFERRAL";
    // payment setting初始化
    public static final String PAYMENT_SETTING = "PAYMENT_SETTING";
    public static final String REMINDER_TASK = "REMINDER_TASK";

    public static final String SMART_TIP_CONFIG = "SMART_TIP_CONFIG";
    public static final String A2P_SUBMIT = "A2P_SUBMIT";
    public static final String POOL_UPDATE_LOCK = "POOL_UPDATE_LOCK";

    public static final String PET_SIZE = "PET_SIZE";

    public static final String CLIENT_PORTAL_LINK = "CLIENT_PORTAL_LINK";

    public static final String PAYMENT_SPLIT_PAYMENT = "SPLIT_PAYMENT";
    // package history
    public static final String GROOMING_PACKAGE_HISTORY = "GROOMING_PACKAGE_HISTORY";

    public static final String SPLIT_TIPS_RECORD = "SPLIT_TIPS_RECORD";

    public static final String PAYROLL_SETTINGS = "PAYROLL_SETTINGS";

    public static final String OB_AVAILABLE_STAFF = "OB_AVAILABLE_STAFF";

    public static final String DISPUTE_CLOSE = "DISPUTE_CLOSE";

    public static final String DISPUTE_CREATE = "DISPUTE_CREATE";

    public static final String REFUND_UPDATE = "REFUND_UPDATE";
    public static final String READER_CAPTURE = "READER_CAPTURE";
    public static final String APPT_PREAUTH_EVENT_LOCK = "APPT_PREAUTH_EVENT_LOCK";
    public static final String APPT_PUT_LOCK = "APPT_PUT_LOCK";

    public static final String GROOMING_REPORT = "GROOMING_REPORT";
    public static final String AUTO_SEND_MESSAGE = "AUTO_SEND_MESSAGE";
    public static final String MESSAGE_THREAD_CREATE = "MESSAGE_THREAD_CREATE";

    public static final String PAYMENT_WEBHOOK_SET_TIPS = "PAYMENT_WEBHOOK_SET_TIPS";
    public static final String PAYMENT_DATA_COLLECT_LOCK = "PAYMENT_DATA_COLLECT_LOCK";

    public static final String SCHEDULE_MESSAGE = "SCHEDULE_MESSAGE";

    public static final String INVITATION_CODE = "INVITATION_CODE";

    public static final String OB_STAFF_TIME = "OB_STAFF_TIME";
    public static final String NEW_OB_STAFF_TIME = "NEW_OB_STAFF_TIME";
    public static final String OB_REQUEST_SYNC = "OB_REQUEST_SYNC";

    public static final String EDIT_STAFF_TIPS_COMMISSION = "EDIT_STAFF_TIPS_COMMISSION";

    public static final String ORDER_MESSAGE_DELIVERY = "ORDER_MESSAGE_DELIVERY";
    public static final String PAYMENT_MESSAGE_DELIVERY = "PAYMENT_MESSAGE_DELIVERY";

    private final RedisUtil redisUtil;

    public LockManager(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    public String getResourceKey(String resourceName, Number resourceId) {
        return resourceName + ":" + resourceId;
    }

    public String getResourceKey(String resourceName, String resourceKey) {
        return resourceName + ":" + resourceKey;
    }

    /**
     * 获取锁的值
     *
     * @param key
     * @return
     */
    public String getLockValue(String key) {
        return redisUtil.get(PREFIX + key);
    }

    public boolean lock(String key, String value) {
        boolean locked = true;
        try {
            locked = redisUtil.setEXAndNX(PREFIX + key, value, DEFAULT_EXPIRE_TIME_IN_SECONDS);
        } catch (RuntimeException e) {
            log.error("get lock for {} failed", key, e);
        }
        return locked;
    }

    public boolean unlock(String key, String value) {
        boolean unlocked = true;
        try {
            unlocked = redisUtil.delWithValue(PREFIX + key, value);
        } catch (RuntimeException e) {
            log.error("unlock for {} failed", key, e);
        }
        return unlocked;
    }

    public boolean lock(String key, String value, Long expireSeconds) {
        boolean locked = true;
        try {
            locked = redisUtil.setEXAndNX(PREFIX + key, value, expireSeconds);
        } catch (RuntimeException e) {
            log.error("get lock for {} failed", key, e);
        }
        return locked;
    }

    /**
     * 加锁，失败重试
     *
     * @param key
     * @param value
     * @return
     */
    public boolean lockWithRetry(String key, String value) {
        return lockWithRetry(
                key, value, DEFAULT_EXPIRE_TIME_IN_SECONDS, DEFAULT_RETRY_COUNT, DEFAULT_RETRY_INTERVAL_MS);
    }

    /**
     * 加锁，自定义过期时间，失败重试
     *
     * @param key
     * @param value
     * @param expireSeconds
     * @return
     */
    public boolean lockWithRetry(String key, String value, long expireSeconds) {
        return lockWithRetry(key, value, expireSeconds, DEFAULT_RETRY_COUNT, DEFAULT_RETRY_INTERVAL_MS);
    }

    /**
     * 加锁，失败重试
     *
     * @param key
     * @param value
     * @param retryCount      重试次数
     * @param retryIntervalMs 重试间隔（单位：毫秒）
     * @return
     */
    public boolean lockWithRetry(String key, String value, int retryCount, long retryIntervalMs) {
        return lockWithRetry(key, value, DEFAULT_EXPIRE_TIME_IN_SECONDS, retryCount, retryIntervalMs);
    }

    /**
     * 加锁，失败重试
     *
     * @param key
     * @param value
     * @param expireSeconds   过期时间
     * @param retryCount      重试次数
     * @param retryIntervalMs 重试间隔（单位：毫秒）
     * @return
     */
    public boolean lockWithRetry(String key, String value, long expireSeconds, int retryCount, long retryIntervalMs) {
        boolean locked = true;
        while (retryCount-- > 0) {
            try {
                locked = redisUtil.setEXAndNX(PREFIX + key, value, expireSeconds);
                // 加锁成功，直接返回
                if (locked) break;
                // 加锁失败，重试
                TimeUnit.MILLISECONDS.sleep(retryIntervalMs);
            } catch (RuntimeException | InterruptedException ignore) {
                log.error("get lock for {} failed", key, ignore);
            }
        }
        return locked;
    }

    /**
     * 刷新锁的时间
     *
     * @param key
     * @return
     */
    public boolean refreshLockTime(String key) {
        return redisUtil.expire(key, DEFAULT_EXPIRE_TIME_IN_SECONDS, TimeUnit.SECONDS);
    }
}
