package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface ServiceEnum {
    Byte TYPE_SERVICE = 1;
    Byte TYPE_ADD_ONS = 2;
    String OB_SERVICE_KEY = "serviceList";
    String OB_ADDONS_KEY = "addonsList";
    String OB_CUSTOMIZE_SERVICE_KEY = "petServiceList";
    String OB_CUSTOMIZE_ADDONS_KEY = "petAddonsList";
    /** moe_grooming_customer_services
     * save price/time for client:1-price；2-time
     */
    Byte SAVE_TYPE_PRICE = 1;

    Byte SAVE_TYPE_TIME = 2;
    Byte ENABLE_NO_SHOW_FEE_TRUE = 1;
    Byte ENABLE_NO_SHOW_FEE_FALSE = 0;
    /**
     * appointment color code
     */
    String DEFAULT_COLOR = "#000000";
    /**
     * auto accept for ob
     */
    Byte AUTO_ACCEPT_TRUE = 1;

    Byte AUTO_ACCEPT_FALSE = 0;
    /**
     * auto accept for ob
     */
    Byte ONLY_SHOW_ONE_EACH_DAY_STAFF_TRUE = 1;

    Byte ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE = 0;
    /**
     *   `book_online_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1 未确认
     */
    Byte OB_DEFAULT_NORMAL = 0;

    Byte OB_NOT_CONFIRM = 1;
    String OB_CANCEL_REASON = "Declined Online Booking Request";

    /**
     * is_waiting_list
     */
    Byte IS_WAITING_LIST_TRUE = 1;

    Byte IS_WAITING_LIST_FALSE = 0;

    Byte IS_ALL_LOCATION_TRUE = 1;
    Byte IS_ALL_LOCATION_FALSE = 0;
    Byte IS_ALL_STAFF_TRUE = 1;
    Byte IS_ALL_STAFF_FALSE = 0;
    Byte SHOW_BASE_PRICE_TRUE = 1;
    Byte SHOW_BASE_PRICE_FALSE = 0;
    /**
     * book_online_available
     */
    Byte BOOK_ONLINE_AVAILABLE_TRUE = 1;

    Byte BOOK_ONLINE_AVAILABLE_FALSE = 0;
    /**
     *  1 boarding 2 daycare 3 grooming 4 training
     */
    String SVC_TYPE_BOARDING = "1";

    String SVC_TYPE_DAYCARE = "2";
    String SVC_TYPE_GROOMING = "3";
    String SVC_TYPE_TRAINING = "4";
    /**
     * max length of service category name
     */
    Integer MAX_LENGTH_OF_NAME = 100;

    Byte FILTER_CLOSE = 0;
    Byte FILTER_OPEN = 1;

    Byte SERVICE_BREED_BINDING_STATUS_NORMAL = 0;
    Byte SERVICE_BREED_BINDING_STATUS_INVALID = 1;

    String ALL_BREEDS_VALUE = "**ALL_BREEDS**";

    Byte LOCATION_IS_DELETED_TRUE = 1;
    Byte LOCATION_IS_DELETED_FALSE = 0;
}
