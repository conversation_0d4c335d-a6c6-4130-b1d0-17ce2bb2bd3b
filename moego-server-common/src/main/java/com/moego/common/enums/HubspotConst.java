package com.moego.common.enums;

public interface HubspotConst {
    String BUSINESS_TYPE_SINGLE_SALON = "Single location";
    String BUSINESS_TYPE_MULTI_SALON = "Multi locations";
    String BUSINESS_TYPE_SINGLE_VAN = "Single van";
    String BUSINESS_TYPE_MULTI_VANS = "Multi vans";
    String BUSINESS_TYPE_HYBRID = "Hybrid";

    String[] ALL_PET_PER_MONTH_OPTIONS = {"0-100", "100 - 200", "200 - 500", "500- 1000", "1000+"};
    String[] ALL_VAN_OPTIONS = {"1", "2-4", "5-20", "21-100", "100+"};
    String[] ALL_LOCATION_OPTIONS = {"1", "2", "3-10", "11-50", "50+"};
    String[] ALL_USE_BEFORE_OPTIONS = {
        "Just started my business", "Move from paper", "Transfer from another software",
    };

    String KNOW_ABOUT_US_OTHER = "Other";

    String TIER_1 = "Tier 1 - Ent, fran";
    String TIER_2 = "Tier 2 - multi";
    String TIER_3 = "Tier 3 - single";
    String TIER_4 = "Tier 4 - solo";
}
