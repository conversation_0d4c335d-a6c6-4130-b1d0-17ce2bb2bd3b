package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface MessageReminderConst {
    String[] CONFIRM_STR_ARR = {
        "Y",
        "y",
        "yes",
        "y.",
        "yes.",
        " y, tx.",
        "yes,tx.",
        "no problem",
        "no worries",
        "\"y\"",
        "'y'",
        "ytx",
        "yes\uD83D\uDC36",
        "y tx.",
        "yes tx",
    };

    String[] CONFIRM_CONTAINS_STR_ARR = {"no problem", "no worry", "no worries"};

    String[] CANCEL_STR_ARR = {
        "n", "no", "n.", "no.", "n,tx.", "no,tx.", "\"n\"", "'n'", "ntx", "no\uD83D\uDC36", "n tx", "no tx",
    };
    Byte TASK_STATUS_NORMAL = 1;
    Byte TASK_STATUS_COMPLETE = 2;
    Byte TASK_STATUS_ERROR = 3;
    Byte TASK_STATUS_TIMEOUT = 4;

    Byte AUTO_SEND_TYPE_AUTO = 1;
    Byte AUTO_SEND_TYPE_MANUAL = 2;
    int REMINDER_DEFAULT_AT_HOUR = 480;
    int REBOOK_REMINDER_DEFAULT_BEFORE_DAY = 0;

    Integer REMINDER_BEFORE_DAY_NO_SEND = 8;
    Integer REMINDER_AFTER_MINUTE_NO_SEND = -1;
}
