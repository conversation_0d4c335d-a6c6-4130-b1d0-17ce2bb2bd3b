package com.moego.common.enums;

/**
 * <AUTHOR>
 * @since 2022/6/30
 */
public interface BusinessReferralConst {
    /**
     * cancelled in 7-day trial
     */
    Byte REFERRAL_CANCELLED = -1;
    /**
     * cancelled 7 days ~ 3 months
     */
    Byte REFERRAL_CANCELLED_PAUSE = -2;
    /**
     * free trial
     */
    Byte REFERRAL_SIGNED_UP = 0;
    /**
     * upgraded < 7 days
     */
    Byte REFERRAL_UPGRADED = 1;
    /**
     * upgraded over 7-day
     */
    Byte REFERRAL_SUCCESS = 2;
    /**
     * upgraded over 3-month
     */
    Byte REFERRAL_OVER_3_MONTH = 3;

    Byte BONUS_PENDING = 0;
    Byte BONUS_CONFIRMED = 1;
    /**
     * checked fixed reward
     */
    Byte BONUS_CHECKED = 2;

    Byte RULE_INACTIVE = 0;
    Byte RULE_ACTIVE = 1;

    String CODE_PREFIX = "MOE";

    String STRIPE_COUPON_NAME = "MoeGo referral 30% off first month";
    Byte ADJUSTMENT_TYPE_CREDIT = 1;
    Byte ADJUSTMENT_TYPE_DEBIT = 2;

    /**
     * event reminder referral page
     */
    Byte PAGE_ID_REFERRAL = 1;

    /**
     * event reminder type
     */
    Byte REMINDER_TYPE_DOT = 1;

    Byte REMINDER_TYPE_POPUPS = 2;

    Byte REMINDER_CODE_COMMON_CHANGE = 0;

    Byte REMINDER_CODE_FIRST_EARN = 1;

    Byte REMINDER_CODE_FIRST_CHARGE_THIRD = 2;

    Byte REMINDER_CODE_BONUS_TIER = 3;
}
