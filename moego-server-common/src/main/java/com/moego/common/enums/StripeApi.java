package com.moego.common.enums;

/**
 * payment intent params
 *
 * <AUTHOR>
 * @since 2022/6/29 10:51
 */
public interface StripeApi {
    Long LIST_DEFAULT_LIMIT = 100L;
    String NUMBER_OF = "Number of ";
    String VAL_TRUE = "true";
    String VAL_FALSE = "false";
    String COMPLETED = "Completed";
    String FAILED = "Failed";
    String HARDWARE_ORDER_ONLY = "HardwareOrderOnly";

    static String getSaleSummaryItemName(String companyCustomerStatus) {
        switch (companyCustomerStatus) {
            case VAL_TRUE -> {
                return "upgrade successfully with hardware: ";
            }
            case VAL_FALSE -> {
                return "hardware order failed: ";
            }
            case HARDWARE_ORDER_ONLY -> {
                return "order successfully only hardware: ";
            }
            default -> {
                return companyCustomerStatus + ": ";
            }
        }
    }

    String AMOUNT = "amount";
    String CURRENCY = "currency";
    String PAYMENT_METHOD_TYPES = "payment_method_types";
    String CARD_PRESENT = "card_present";
    String CAPTURE_METHOD = "capture_method";
    String SETUP_FUTURE_USAGE = "setup_future_usage";
    String METADATA = "metadata";
    String DESCRIPTION = "description";
    String STATEMENT_DESCRIPTOR = "statement_descriptor";
    String CUSTOMER = "customer";
    String PAYMENT_METHOD = "payment_method";
    String OFF_SESSION = "off_session";
    String CONFIRM = "confirm";
    String APPLICATION_FEE_AMOUNT = "application_fee_amount";
    String TRANSFER_DATA = "transfer_data[destination]";
    String ON_BEHALF_OF = "on_behalf_of";
    String SOURCE = "source";
    String CHARGE_CONNECTED_ACCOUNT = "connected_account";
    String CHARGE_TYPE = "charge_type";
    String CHARGE_TYPE_PREAUTH = "preauth";
    String CHARGE_TYPE_FEE = "processing_fee";
    String CHARGE_TYPE_CANCELLATION_FEE = "cancellation_fee";

    String STATEMENT_DESCRIPTOR_SUFFIX = "statement_descriptor_suffix";

    String EXTERNAL_ACCOUNT = "external_account";
    String PM_DATA_TYPE = "payment_method_data[type]";
    String PM_DATA_TOKEN = "payment_method_data[card[token]]";
    String PAYMENT_METHOD_CARD = "card";
    String PAYMENT_METHOD_BANK_ACCOUNT = "us_bank_account";
    Integer DEFAULT_PRE_AUTH_AMOUNT_IN_CENT = 50;
    String PRE_AUTH_DESC = "MoeGo PreAuthentication";

    String REQUIRES_CAPTURE = "requires_capture";
    String STRIPE_SUCCEED = "succeeded";
    /**
     * card funding type
     */
    String FUNDING_CREDIT = "credit";

    String FUNDING_DEBIT = "debit";
    String FUNDING_PREPAID = "prepaid";

    /**
     * General params for all endpoint
     */
    String EXPAND = "expand"; // https://stripe.com/docs/api/expanding_objects

    String EXPAND_SOURCES = "sources";
    String EXPAND_SUBSCRIPTIONS = "subscriptions";
    String EXPAND_INVOICE = "data.invoice";
    String EXPAND_LATEST_INVOICE = "latest_invoice";
    String EXPAND_LATEST_CHARGE = "latest_charge";
    String EXPAND_DEFAULT_SOURCE = "default_source";
    String EXPAND_INVOICE_BALANCE = "invoice_credit_balance";
    String REFUND_SOURCE = "refund_source";
    String MANUAL_REFUND = "manual";

    String ADMIN = "admin";

    String PREAUTH_ID = "PREAUTH_ID";

    String PREAUTH_CAPTURE_SOURCE = "preauth_capture_source";
    String PREAUTH_CAPTURE_SOURCE_MANUAL = "manual";
    String PREAUTH_CAPTURE_SOURCE_AUTO = "auto";

    String ACCOUNT_ID = "account_id";
    String COMPANY_ID = "company_id";
    String ENTERPRISE_ID = "enterprise_id";
    String ENTERPRISE_OWNER_EMAIL = "enterprise_owner_email";
    String PO_NUMBER_PREFIX = "MG-";
    String HARDWARE_ORDER_ID = "stripe_order";
    String TYPE_BUNDLE_SALE_TYPE = "bundle_sale_type";
    String TYPE_BUNDLE_SALE_CODE = "bundle_sale_type_code";

    String TRANSFER_ID = "transfer_id";
    String HARDWARE_DISCOUNT_KEY = "hardware_discount";
}
