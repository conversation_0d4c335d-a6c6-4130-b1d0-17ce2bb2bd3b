package com.moego.common.enums;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceItemEnum {
    GROOMING(1, 0b001),
    BOARDING(2, 0b010),
    DAYCARE(3, 0b100),
    EVALUATION(4, 0b1000),
    DOG_WALKING(5, 0b10000),
    GROUP_CLASS(6, 0b100000),
    ;

    private final Integer serviceItem;

    private final Integer bitValue;

    public static ServiceItemEnum fromServiceItem(Integer serviceItem) {
        for (ServiceItemEnum serviceItemEnum : ServiceItemEnum.values()) {
            if (serviceItemEnum.getServiceItem().equals(serviceItem)) {
                return serviceItemEnum;
            }
        }
        return null;
    }

    /**
     * Convert service item list to bit value.
     *
     * @param serviceItemList service item list
     * @return bit value
     */
    public static Integer convertBitValueList(Collection<Integer> serviceItemList) {
        Integer bitValue = 0;
        for (Integer serviceItem : serviceItemList) {
            ServiceItemEnum serviceItemEnum = fromServiceItem(serviceItem);
            if (serviceItemEnum != null) {
                bitValue |= serviceItemEnum.getBitValue();
            }
        }
        return bitValue;
    }

    /**
     * Convert bit value to service item list.
     * 1 -> [GROOMING]
     * 2 -> [BOARDING]
     * 3 -> [GROOMING, BOARDING]
     * 4 -> [DAYCARE]
     * 5 -> [GROOMING, DAYCARE]
     * 6 -> [BOARDING, DAYCARE]
     * 7 -> [GROOMING, BOARDING, DAYCARE]
     * 8 -> [EVALUATION]
     * 9 -> [GROOMING, EVALUATION]
     * 10 -> [BOARDING, EVALUATION]
     * 11 -> [GROOMING, BOARDING, EVALUATION]
     * 12 -> [DAYCARE, EVALUATION]
     * 13 -> [GROOMING, DAYCARE, EVALUATION]
     * 14 -> [BOARDING, DAYCARE, EVALUATION]
     * 15 -> [GROOMING, BOARDING, DAYCARE, EVALUATION]
     * 16 -> [DOG_WALKING]
     * 17 -> [DOG_WALKING, GROOMING]
     * 18 -> [DOG_WALKING, BOARDING]
     * 20 -> [DOG_WALKING, DAYCARE]
     * 32 -> [GROUP_CLASS]
     *
     * @param bitValue bit value
     * @return service item list
     */
    public static List<ServiceItemEnum> convertBitValueList(Integer bitValue) {
        return Stream.of(ServiceItemEnum.values())
                .filter(e -> (bitValue & e.getBitValue()) == e.getBitValue())
                .toList();
    }

    public static List<ServiceItemEnum> convertBitValues(List<Integer> bitValues) {
        return Stream.of(ServiceItemEnum.values())
                .filter(e -> bitValues.stream().anyMatch(bitValue -> (bitValue & e.getBitValue()) == e.getBitValue()))
                .toList();
    }

    /**
     * Converts a list of service items to a list of corresponding bit values.
     * 返回包含服务的 bit 值的列表
     *
     * @param serviceItemList the list of service items
     * @return the list of bit values
     */
    public static List<Integer> convertServiceItemListToBitValueList(Collection<Integer> serviceItemList) {
        return serviceItemList.stream()
                .map(ServiceItemEnum::fromServiceItem)
                .filter(Objects::nonNull)
                .map(ServiceItemEnum::getBitValueListByServiceItem)
                .flatMap(List::stream)
                .distinct()
                .toList();
    }

    public static List<Integer> getBitValueListByServiceItem(ServiceItemEnum serviceItem) {
        return switch (serviceItem) {
            case GROOMING -> List.of(1, 3, 5, 7, 9, 11, 13, 15, 17);
            case BOARDING -> List.of(2, 3, 6, 7, 10, 11, 14, 15, 18);
            case DAYCARE -> List.of(4, 5, 6, 7, 12, 13, 14, 15, 20);
            case EVALUATION -> List.of(8, 9, 10, 11, 12, 13, 14, 15);
            case DOG_WALKING -> List.of(16, 17, 18, 20);
            case GROUP_CLASS -> List.of(32);
        };
    }

    public static List<Integer> getBitValueListByServiceItems(List<ServiceItemEnum> serviceItems) {
        Set<Integer> bitValues = new HashSet<>();
        serviceItems.forEach(serviceItem -> bitValues.addAll(getBitValueListByServiceItem(serviceItem)));
        return bitValues.stream().sorted(Integer::compareTo).toList();
    }

    public boolean isIncludedIn(Integer serviceTypeInclude) {
        return (serviceTypeInclude & this.getBitValue()) == this.getBitValue();
    }

    /**
     * Determines the main service item type from a service type include.
     * Service types have a specific priority order: BOARDING > DAYCARE > EVALUATION|DOG_WALKING > GROOMING
     *
     * @param serviceTypeInclude service type include
     * @return the main ServiceItemEnum based on priority
     */
    public static ServiceItemEnum getMainServiceItemType(Integer serviceTypeInclude) {
        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)) {
            return ServiceItemEnum.BOARDING;
        } else if (ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
            return ServiceItemEnum.DAYCARE;
        } else if (ServiceItemEnum.EVALUATION.isIncludedIn(serviceTypeInclude)) {
            return ServiceItemEnum.EVALUATION;
        } else if (ServiceItemEnum.DOG_WALKING.isIncludedIn(serviceTypeInclude)) {
            return ServiceItemEnum.DOG_WALKING;
        } else if (ServiceItemEnum.GROUP_CLASS.isIncludedIn(serviceTypeInclude)) {
            return ServiceItemEnum.GROUP_CLASS;
        }
        return ServiceItemEnum.GROOMING;
    }

    /**
     * Determines the main service item type from a collection of service item IDs.
     * Service types have a specific priority order: BOARDING > DAYCARE > EVALUATION|DOG_WALKING > GROOMING
     *
     * @param serviceItemTypes Collection of service item IDs
     * @return The main ServiceItemEnum based on priority
     */
    public static ServiceItemEnum getMainServiceItemType(Collection<Integer> serviceItemTypes) {
        return getMainServiceItemType(convertBitValueList(serviceItemTypes));
    }
}
