package com.moego.common.exception;

import com.moego.common.enums.ResponseCodeEnum;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
@Order(ExceptionHandlerAdvice.ORDER)
public class ExceptionHandlerAdvice {

    public static final int ORDER = Ordered.LOWEST_PRECEDENCE - 100;

    @ExceptionHandler(CommonException.class)
    public CommonException.CommonResponse handleCommonException(
            CommonException commonException, HttpServletResponse httpServletResponse) {
        return ExceptionHandlingUtil.handleCommonException(commonException, httpServletResponse);
    }

    /**
     * 参数效验异常处理器
     *
     * @param e 参数验证异常
     * @return ResponseInfo
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonException.CommonResponse parameterExceptionHandler(
            MethodArgumentNotValidException e, HttpServletResponse httpServletResponse) {
        log.error("MethodArgumentNotValidException for {}", e.getMessage());
        // 获取异常信息
        BindingResult exceptions = e.getBindingResult();
        // 判断异常中是否有错误信息，如果存在就使用异常中的消息，否则使用默认消息
        if (exceptions.hasErrors()) {
            List<ObjectError> errors = exceptions.getAllErrors();
            if (!errors.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                errors.forEach(currError -> {
                    FieldError fieldError = (FieldError) currError;
                    sb.append(String.format("%s is invalid: %s", fieldError.getField(), fieldError.getDefaultMessage()))
                            .append(System.lineSeparator());
                });
                return handleCommonException(
                        new CommonException(
                                ResponseCodeEnum.PARAMS_ERROR, sb.toString().trim()),
                        httpServletResponse);
            }
        }
        return handleCommonException(
                new CommonException(ResponseCodeEnum.PARAMS_ERROR, e.getMessage()), httpServletResponse);
    }
}
