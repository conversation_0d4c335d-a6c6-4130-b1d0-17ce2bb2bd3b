package com.moego.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2021/7/2 2:51 PM
 */
@Slf4j
public class SignatureUtil {

    private static final String HMAC_SHA_1 = "HmacSHA1";
    private static final Base64.Decoder decoder = Base64.getDecoder();
    private static final Base64.Encoder encoder = Base64.getEncoder();

    /**
     * base64加密
     *
     * @param str
     * @return
     */
    public static String base64Encode(String str) {
        final byte[] textByte = str.getBytes(StandardCharsets.UTF_8);
        // 编码
        return encoder.encodeToString(textByte);
    }

    /**
     * base64解密
     *
     * @param str
     * @return
     */
    public static String base64Decode(String str) {
        return new String(decoder.decode(str), StandardCharsets.UTF_8);
    }

    public static String getHmacSha1String(String privateKey, String input) throws Exception {
        Key key = new SecretKeySpec(privateKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA_1);
        Mac mac = Mac.getInstance(HMAC_SHA_1);
        mac.init(key);
        return Base64.getEncoder().encodeToString(mac.doFinal(input.getBytes(StandardCharsets.UTF_8)));
    }

    public static boolean isValidSignature(
            String notificationBody, String notificationUrl, String notificationSignature, String webhookSignatureKey) {
        if (ObjectUtils.isEmpty(notificationSignature)) {
            return false;
        } else if ("signature_test".equals(notificationSignature)) {
            return true;
        }
        // Concatenate your notification URL and
        // the JSON body of the webhook notification
        String stringToSign = notificationUrl + notificationBody;

        // Generate the HMAC-SHA1 signature of the string
        // signed with your webhook signature key
        try {
            String generatedSignature = getHmacSha1String(webhookSignatureKey, stringToSign);
            log.info("generated sig is {}, supplied: {}", generatedSignature, notificationSignature);
            return generatedSignature.equals(notificationSignature);
        } catch (Exception e) {
            log.error("generate signature failed", e);
            return false;
        }
    }
}
