package com.moego.common.utils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class EntityUtil {

    public static List<Map<String, Object>> getListMapStrObj(List<Map<String, Object>> listObj) {
        List<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : listObj) {
            Map<String, Object> returnMap = new HashMap<>(map);
            returnList.add(returnMap);
        }
        return returnList;
    }

    /* *
     * 实体类转Map
     *
     * @param object
     * @return*/

    public static Map<String, Object> entityToMap(Object object) {
        Map<String, Object> map = new HashMap();
        for (Field field : object.getClass().getDeclaredFields()) {
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(), o);
                field.setAccessible(flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return map;
    }

    /* *
     * Map转实体类
     *
     * @param map    需要初始化的数据，key字段必须与实体类的成员名字一样，否则赋值为空
     * @param entity 需要转化成的实体类
     * @return
     * */
    @SuppressFBWarnings("REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS")
    public static <T> T mapToEntity(Map<String, Object> map, Class<T> entity) {
        T t = null;
        try {
            t = entity.newInstance();
            for (Field field : entity.getDeclaredFields()) {
                if (map.containsKey(field.getName())) {
                    boolean flag = field.isAccessible();
                    field.setAccessible(true);
                    Object object = map.get(field.getName());
                    if (object != null && field.getType().isAssignableFrom(object.getClass())) {
                        field.set(t, object);
                    }
                    field.setAccessible(flag);
                }
            }
            return t;
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return t;
    }

    public static void recordLog(int count, String tableName, String msg) {
        if (count == 0) {
            log.warn("record failed for {}, info: {}", tableName, msg);
        } else {
            log.info("record for {}, info : {}", tableName, msg);
        }
    }

    /**
     * apptPerWeek
     * 选项1）Less than 7
     * 选项2）7 to 20
     * 选项3）21 to 50
     * 选项4）More than 50
     *
     * businessYears
     * 选项1）Less than 6 months
     * 选项2）6months to 2 years
     * 选项3）2 years to 10 years
     * 选项4）More than 10 years
     *
     * moveFrom
     * 选项1）Just start my business
     * 选项2）Move from paper
     * 选项3）Transfer from other software
     * @param index
     * @return
     */
    public static String getApptPerWeek(Byte index) {
        if (PrimitiveTypeUtil.isNullOrZero(index)) {
            return "NA";
        }
        switch (index) {
            case 1:
                return "Less than 7";
            case 2:
                return "7 to 20";
            case 3:
                return "21 to 50";
            case 4:
                return "More than 50";
            default:
                return "NA";
        }
    }

    public static String getBusinessYears(Byte index) {
        if (PrimitiveTypeUtil.isNullOrZero(index)) {
            return "NA";
        }
        switch (index) {
            case 1:
                return "Less than 6 months";
            case 2:
                return "6 months to 2 years ";
            case 3:
                return "2 years to 10 years";
            case 4:
                return "More than 10 years";
            default:
                return "NA";
        }
    }

    public static String getMoveFrom(Byte index) {
        if (PrimitiveTypeUtil.isNullOrZero(index)) {
            return "NA";
        }
        switch (index) {
            case 1:
                return "Just start my business";
            case 2:
                return "Move from paper";
            case 3:
                return "Transfer from other software";
            default:
                return "NA";
        }
    }
}
