/*
 * @since 2023-04-09 19:08:45
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.common.utils;

import com.github.pagehelper.PageHelper;
import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;

public class PageUtil {
    public static final int DEFAULT_PAGE_SIZE = 500;

    public static <T> List<T> fetchAll(
            final int pageSize,
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, Integer /* total */>> loader) {
        var res = loader.apply(1, pageSize);
        final var out = new ArrayList<T>(res.getSecond());
        out.addAll(res.getFirst());
        final var end = (res.getSecond() / pageSize) + (res.getSecond() % pageSize == 0 ? 0 : 1);
        for (int i = 2; i <= end; i++) {
            res = loader.apply(i, pageSize);
            out.addAll(res.getFirst());
        }
        return out;
    }

    public static <T> List<T> fetchAll(
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, Integer /* total */>> loader) {
        return fetchAll(DEFAULT_PAGE_SIZE, loader);
    }

    public static <I, T> List<T> mapAll(List<I> input, Function<List<I> /* paged input */, List<T>> loader) {
        if (CollectionUtils.isEmpty(input)) {
            return List.of();
        }
        return fetchAll((page, size) -> Pair.of(
                loader.apply(input.subList((page - 1) * size, Math.min(page * size, input.size()))), input.size()));
    }

    public static boolean hasEmptyCollectionFilter(Collection<?>... collections) {
        for (var collection : collections) {
            if (collection != null && collection.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    @Nonnull
    public static <T> Pair<List<T>, Pagination> selectPage(Pagination pagination, Supplier<List<T>> supplier) {
        try (var page = PageHelper.<T>startPage(pagination.pageNum(), pagination.pageSize())) {
            page.doSelectPage(supplier::get);
            return Pair.of(page, new Pagination(pagination.pageNum(), pagination.pageSize(), (int) page.getTotal()));
        }
    }

    public static <T> Pair<List<T>, Pagination> empty(Pagination pagination) {
        if (pagination == null) {
            return Pair.of(List.of(), Pagination.DEFAULT);
        }
        return Pair.of(List.of(), new Pagination(pagination.pageNum(), pagination.pageSize(), 0));
    }
}
