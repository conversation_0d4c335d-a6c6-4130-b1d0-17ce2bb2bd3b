package com.moego.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class StringMoegoUtil {

    public static boolean isAllLowerCase(String str) {
        if (str != null && !StringUtils.isEmpty(str)) {
            int sz = str.length();

            for (int i = 0; i < sz; ++i) {
                if (!Character.isLowerCase(str.charAt(i))) {
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }
    }

    public static boolean isAllUpperCase(String str) {
        if (str != null && !StringUtils.isEmpty(str)) {
            int sz = str.length();

            for (int i = 0; i < sz; ++i) {
                if (!Character.isUpperCase(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return false;
        }
    }

    // 检查字符串是否超过指定长度
    public static boolean isOversize(String text, Integer size) {
        return text != null && size < text.length();
    }

    // 拼接字符串 去除空格并转小写
    public static String compressAndLowerCase(String... strings) {
        return StringUtils.delete(String.join("", strings), " ").toLowerCase();
    }

    // 从 input 内提取数字和-
    public static String extractDigitsAndHyphen(String input) {
        if (input == null) {
            return null;
        }
        input = input.toLowerCase();
        // 使用正则表达式匹配数字和减号
        Pattern pattern = Pattern.compile("[a-z0-9-]+");
        Matcher matcher = pattern.matcher(input);
        StringBuilder result = new StringBuilder();
        // 将匹配到的数字和减号添加到结果字符串中
        while (matcher.find()) {
            result.append(matcher.group());
        }
        return result.toString();
    }
}
