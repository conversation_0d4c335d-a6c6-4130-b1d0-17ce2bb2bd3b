/*
 * @since 2023-04-09 18:18:33
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.common.utils;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Builder;

@Builder(toBuilder = true)
public record Pagination(@Min(1) int pageNum, @Min(0) @Max(1000) int pageSize, @Min(0) int total) {
    public static final Pagination ALL = new Pagination(1, 1000, 0);
    public static final Pagination COUNT = new Pagination(1, 0, 0);
    public static final Pagination DEFAULT = new Pagination(1, 10, 0);
}
