package com.moego.common.utils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2020/12/16 7:37 PM
 */
public class FakeItUtil {

    /**
     * 0-not  1-25%  2-40%  3-50%
     */
    public static final Byte FAKE_TYPE_0 = 0;

    public static final Byte FAKE_TYPE_1 = 1;
    public static final Byte FAKE_TYPE_2 = 2;
    public static final Byte FAKE_TYPE_3 = 3;
    private static final double[] fakeItArray = new double[] {0, 0.25, 0.4, 0.5};

    /**
     * 对map中的每个list，过滤其中的元素
     * @param timeListMap
     * @param fakeType
     */
    public static void fakeItOut(Map<String, List<Integer>> timeListMap, Byte fakeType) {
        timeListMap.forEach((key, value) -> fakeItOut(value, fakeType));
    }

    /**
     * 过滤list中的元素
     * @param timeList
     * @param fakeType
     */
    public static void fakeItOut(List<Integer> timeList, Byte fakeType) {
        if (CollectionUtils.isEmpty(timeList) || fakeType > fakeItArray.length - 1) {
            return;
        } else {
            int fakeCount = (int) (timeList.size() * fakeItArray[fakeType]);
            // 删除fakeCount个元素
            for (int index = 0; index < fakeCount; index++) {
                timeList.remove(ThreadLocalRandom.current().nextInt(timeList.size()));
            }
        }
    }
}
/**/
