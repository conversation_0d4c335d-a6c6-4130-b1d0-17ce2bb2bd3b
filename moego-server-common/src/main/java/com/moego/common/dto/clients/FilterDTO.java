package com.moego.common.dto.clients;

import com.moego.common.enums.PropertyEnum;
import com.moego.common.enums.filter.OperatorEnum;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/4/1
 */
@Data
@Accessors(chain = true)
public class FilterDTO {

    private PropertyEnum property;
    private OperatorEnum operator;
    private String value;
    private List<String> values;
    private boolean isReversed;
    private String whereClause;

    private String condition;
    private String singleValue;
    private String secondValue;
    private List<Object> listValues;

    public boolean isSingle() {
        return singleValue != null && secondValue == null && CollectionUtils.isEmpty(listValues);
    }

    public boolean isBetween() {
        return singleValue != null && secondValue != null && CollectionUtils.isEmpty(listValues);
    }

    public boolean isList() {
        return singleValue == null && secondValue == null && listValues != null && !listValues.isEmpty();
    }

    public boolean isCond() {
        return singleValue == null
                && secondValue == null
                && CollectionUtils.isEmpty(listValues)
                && StringUtils.hasText(condition);
    }
}
