package com.moego.common.params;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * <AUTHOR>
 * @since 2023/4/1
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "filterType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = Filter.class, name = "filter"),
    @JsonSubTypes.Type(value = FilterGroup.class, name = "filterGroup"),
})
public interface FilterStructure {}
