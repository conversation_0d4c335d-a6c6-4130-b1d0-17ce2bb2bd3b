package com.moego.common.constant;

/**
 * <AUTHOR>
 * @date 2020-06-06 17:43
 */
public class CommonConstant {

    // Value constant
    public static final String MOE_STATUS_OK = "200";

    // Map keys
    public static final String KEY_SESSION_CONTEXT = "MOE_SESSION_CONTEXT";
    public static final String KEY_REQ_START_AT = "MOE_REQ_START_AT";
    public static final String KEY_REQ_SPENT_TIME = "MOE_REQ_SPENT_TIME";
    public static final String KEY_TRACING_CONTEXT = "MOE_TRACING_CONTEXT";

    // Req headers
    public static final String X_ACCOUNT_TOKEN = "Account-Token";
    public static final String X_STAFF_TOKEN = "Staff-Token";
    public static final String X_CUSTOMER_TOKEN = "Customer-Token";
    public static final String X_ACCOUNT_ID = "X-MOE-ACCOUNT-ID";
    public static final String X_BUSINESS_ID = "X-MOE-BUSINESS-ID";
    public static final String X_STAFF_ID = "X-MOE-STAFF-ID";
    public static final String X_SESSION_ID = "X-MOE-SESSION-ID";
    public static final String X_CUSTOMER_ID = "X-MOE-CUSTOMER-ID";

    public static final String X_SESSION_V1_ID = "X-MOE-SESSION-V1-ID";
    public static final String X_SESSION_V1_ACCOUNT_ID = "X-MOE-SESSION-V1-ACCOUNT-ID";
    public static final String X_SESSION_V1_DATA = "X-MOE-SESSION-V1-DATA";

    public static final String X_GOOG_CHANNEL_ID = "X-Goog-Channel-ID";
    public static final String X_REQUEST_ID = "X-REQUEST-ID";
    public static final String X_COOKIE = "Cookie";
    public static final String GREY_VERSION = "Grey-Version";
    public static final String X_INTERNAL_ORIGIN = "X-MOE-INTERNAL-ORIGIN";
    // X_REQUEST_ID 在日志内的key
    public static final String LOG_REQUEST_ID = "id";

    // Res headers
    public static final String X_MOE_STATUS = "X-MOE-STATUS";

    // 缓存的默认有效期
    public static final int DEFAULT_CACHE_TTL = 30 * 24 * 3600;

    // 一英里等于多少米
    public static final double METERS_PER_MILE = 1609.344;

    // 一公里等于多少米
    public static final int METERS_PER_KILOMETER = 1000;

    // 一英里等于多少公里
    public static final double MILE_TO_KILOMETER = 1.609344;

    /**
     * 是否 变量 是
     */
    public static Integer YES = Integer.valueOf(1);

    /**
     * 是否 变量 否
     */
    public static Integer NO = Integer.valueOf(0);

    /**
     * 启用, 授权, 开启
     */
    public static Byte ENABLE = (byte) 1;

    /**
     * 禁用, 关闭
     */
    public static Byte DISABLE = (byte) 0;

    /**
     * 正常数据，未被删除
     */
    public static Byte NORMAL = (byte) 1;

    /**
     * 数据库中标记为删除
     */
    public static Byte DELETED = (byte) 2;
    /**
     * is_xxx 类开关打开
     */
    public static Byte IS_XXX_OPEN = (byte) 1;

    /**
     * is_xxx 类开关关闭
     */
    public static Byte IS_XXX_CLOSE = (byte) 0;

    /**
     * 是否可用 (rate, customer等)
     */
    public static Byte ACTIVE = (byte) 1;
}
