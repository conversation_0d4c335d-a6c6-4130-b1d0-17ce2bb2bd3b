{"deps": {"moego-server-common": {"com.moego:moego-server-common": "moego-server-common"}, "moego-server-api": {"com.moego:moego-server-api": "moego-server-api"}, "moego-api-definitions": {"com.moego.api:moego-api-java": "out/java"}, "moego-java-lib": {"com.moego.lib:moego-lib-common": "moego-lib-common", "com.moego.lib:moego-lib-messaging": "", "com.moego.lib:moego-lib-aws": "moego-lib-aws", "com.moego.lib:moego-lib-utils": "moego-lib-utils", "com.moego.lib:moego-lib-springdoc": "", "com.moego.lib:moego-lib-activemq": "moego-lib-activemq", "com.moego.lib:moego-lib-risk-control": "moego-lib-risk-control", "com.moego.lib:moego-lib-permission": "moego-lib-permission", "com.moego.lib:moego-lib-event-bus": "moego-lib-event-bus", "com.moego.lib:moego-lib-feature-flag": ""}, "moego-svc-activity-log": {"com.moego:moego-svc-activity-log-processor": ""}, "moego": {"com.moego.api:moego": ""}}}