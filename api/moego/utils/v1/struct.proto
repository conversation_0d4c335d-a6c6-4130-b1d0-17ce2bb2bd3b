// @since 2023-04-07 11:19:05
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v1";

// the int64 list
message Int64ListValue {
  // the values
  repeated int64 values = 1 [(validate.rules).repeated = {
    max_items: 200
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// the string list
message StringListValue {
  // the values
  repeated string values = 1 [(validate.rules).repeated = {
    max_items: 200
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
}
