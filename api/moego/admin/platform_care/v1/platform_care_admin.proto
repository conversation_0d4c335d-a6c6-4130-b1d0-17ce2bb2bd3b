// @since 2-23-10-20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.platform_care.v1;

import "moego/models/agreement/v1/agreement_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/platform_care/v1;platformcareapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.platform_care.v1";

// platform care record
message PlatformCareRecord {
  // id
  int64 id = 1;
  // code
  string code = 2;
  // email
  string email = 3;
  // agreement record uuid
  string agreement_record_uuid = 4;
  // account
  int64 account_id = 5;
  // status
  int32 status = 6;
  // signed_time
  int64 signed_time = 7;
  // created_time
  int64 create_time = 8;
  // updated_time
  int64 update_time = 9;
  // is deleted
  bool is_deleted = 10;
  // agreement id
  optional int64 agreement_id = 11;
  // link
  optional string link = 12;
  // discount code
  string discount_code = 13;
  // order shipping status
  string order_shipping_status = 14;
  // show accounting
  int32 show_accounting = 15;
}

// create platform care link params
message CreatePlatformCareLinkParams {
  // email
  string email = 1 [(validate.rules).string = {email: true}];
  // agreement id
  int64 agreement_id = 2 [(validate.rules).int64 = {gte: 0}];
  // show accounting
  bool show_accounting = 3;
}

// create platform care link result
message CreatePlatformCareLinkResult {
  // link
  string link = 1;
}

// add platform care record request
message AddPlatformCareRecordParams {
  // code
  string code = 1 [(validate.rules).string = {max_len: 64}];
  // email
  string email = 2 [(validate.rules).string = {
    email: true
    max_len: 64
  }];
  // agreement_record_uuid
  string agreement_record_uuid = 3 [(validate.rules).string = {max_len: 32}];
  // account
  int64 account_id = 4 [(validate.rules).int64 = {gte: 0}];
}

// add platform care record result
message AddPlatformCareRecordResult {
  // code
  int64 id = 1;
}

// get platform care record By Code params
message GetPlatformCareRecordByCodeParams {
  // code
  string code = 1 [(validate.rules).string = {max_len: 32}];
}

// get platform care record By Code result
message GetPlatformCareRecordByCodeResult {
  // platform care record
  PlatformCareRecord platform_care_record = 1;
}

// get platform care record list params
message GetPlatformCareRecordListParams {
  // code
  optional string code = 1 [(validate.rules).string = {max_len: 64}];
  // email
  optional string email = 2 [(validate.rules).string = {max_len: 64}];
  // agreement_uuid
  optional string agreement_uuid = 3 [(validate.rules).string = {max_len: 32}];
  // account
  optional int64 account_id = 4 [(validate.rules).int64 = {gte: 0}];
  // status
  optional int32 status = 5 [(validate.rules).int32 = {gte: 0}];
  // agreement id
  optional int64 agreement_id = 6 [(validate.rules).int64 = {gte: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// get platform care record list result
message GetPlatformCareRecordListResult {
  // platform care record list
  repeated PlatformCareRecord platform_care_records = 1;
  // agreement map
  map<int64, moego.models.agreement.v1.AgreementModelSimpleView> agreement_map = 2;
  // status map
  map<int32, string> status_map = 3;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

//  update platform care record params
message UpdatePlatformCareRecordParams {
  // id
  int64 id = 1;
  // status
  optional int32 status = 6 [(validate.rules).int32 = {gte: 0}];
}

// update platform care record result
message UpdatePlatformCareRecordResult {
  // id
  int64 id = 1;
}

// delete platform care record params
message DeletePlatformCareRecordParams {
  // id
  int64 id = 1;
}

// delete platform care record result
message DeletePlatformCareRecordResult {
  // id
  int64 id = 1;
}

// platform care service
service PlatformCareService {
  // create platform care link
  rpc CreatePlatformCareLink(CreatePlatformCareLinkParams) returns (CreatePlatformCareLinkResult);

  // get platform care record
  rpc GetPlatformCareRecordByCode(GetPlatformCareRecordByCodeParams) returns (GetPlatformCareRecordByCodeResult);

  // add platform care record
  rpc AddPlatformCareRecord(AddPlatformCareRecordParams) returns (AddPlatformCareRecordResult);

  // get platform care record list
  rpc GetPlatformCareRecordList(GetPlatformCareRecordListParams) returns (GetPlatformCareRecordListResult);

  // update platform care record
  rpc UpdatePlatformCareRecord(UpdatePlatformCareRecordParams) returns (UpdatePlatformCareRecordResult);

  // delete platform care record
  rpc DeletePlatformCareRecord(DeletePlatformCareRecordParams) returns (DeletePlatformCareRecordResult);
}
