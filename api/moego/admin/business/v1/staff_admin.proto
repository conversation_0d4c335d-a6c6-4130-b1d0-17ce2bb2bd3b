// @since 2023-06-21 18:46:17
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.business.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_models.proto";
import "moego/models/organization/v1/van_model.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/business/v1;businessapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.business.v1";

// describe staffs params
message DescribeStaffsParams {
  // the business id
  optional int64 business_id = 1;
  // the account id
  optional int64 account_id = 2;
  // include deleted
  optional bool include_deleted = 3;
  // name like
  optional string name = 4;
  // the company id
  optional int64 company_id = 5;

  // the pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// describe staffs result
message DescribeStaffsResult {
  // the staffs
  repeated google.protobuf.Struct deprecated_staffs = 1;
  // the business map, key is business id
  map<int64, google.protobuf.Struct> deprecated_business_map = 2;
  // the account map, key is account id
  map<int64, moego.models.account.v1.AccountModelSearchView> account_map = 3;
  //staff company van list
  map<int64, moego.models.organization.v1.VanListModel> company_van_list_map = 5;
  // the pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create staff params
message EditStaffParams {
  // staff id, if not set, create new staff
  optional int64 id = 1;
  // the business id
  int64 business_id = 2;
  // first name
  string first_name = 3 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // last name
  string last_name = 4 [(validate.rules).string = {
    max_len: 50
    min_len: 1
  }];
  // account id
  int64 account_id = 5;
  // role id
  int64 role_id = 6;
  // hire date
  optional google.protobuf.Timestamp hire_date = 7;
  // note
  string note = 8 [(validate.rules).string = {max_len: 255}];
  // avatar path
  string avatar_path = 9 [(validate.rules).string = {
    max_len: 255
    uri: true
    ignore_empty: true
  }];
  // phone number
  string phone_number = 10 [(validate.rules).string = {max_len: 50}];
  // fire date
  optional google.protobuf.Timestamp fire_date = 11;
  // allow login
  int32 allow_login = 12;
  // book online available
  int32 book_online_available = 13;
  // show on calendar
  int32 show_on_calendar = 14;
  // show calendar staff all
  int32 show_calendar_staff_all = 15;
  // status
  int32 status = 16;
}

// create staff result
message EditStaffResult {
  // the staff
  google.protobuf.Struct deprecated_staff = 1;
}

// delete staff params
message DeleteStaffParams {
  // the staff id
  int64 id = 1;
  // the business id
  int64 business_id = 2;
}

// delete staff result
message DeleteStaffResult {}

// force assign staff params
message ForceAssignStaffParams {
  // the staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // the van id
  optional int64 van_id = 2;
}

// force assign staff
message ForceAssignStaffResult {}

// the staff service
service StaffService {
  // describe staffs
  rpc DescribeStaffs(DescribeStaffsParams) returns (DescribeStaffsResult);
  // create staff
  rpc CreateStaff(EditStaffParams) returns (EditStaffResult);
  // update staff
  rpc UpdateStaff(EditStaffParams) returns (EditStaffResult);
  // delete staff
  rpc DeleteStaff(DeleteStaffParams) returns (DeleteStaffResult);
  // force assign staff to van
  rpc ForceAssignStaff(ForceAssignStaffParams) returns (ForceAssignStaffResult);
}
