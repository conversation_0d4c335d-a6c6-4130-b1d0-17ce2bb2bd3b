// @since 2023-07-05 19:43:15
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.ai_assistant.v1;

import "google/protobuf/struct.proto";
import "moego/models/ai_assistant/v1/business_conversation_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/ai_assistant/v1;aiassistantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.ai_assistant.v1";

// describe system summaries params
message DescribeSystemSummariesParams {}

// describe system summaries result
message DescribeSystemSummariesResult {
  // business count
  int32 business_count = 1;
  // conversation count
  int32 conversation_count = 2;
  // nonempty conversation count
  int32 nonempty_conversation_count = 10;
  // adopted conversation count
  int32 adopted_conversation_count = 3;
  // question count
  int32 question_count = 4;
  // total cost
  double total_cost = 5;
  // total input token
  int32 total_input_token = 6;
  // total output token
  int32 total_output_token = 7;
  // total token
  int32 total_token = 8;
}

// describe business conversation summaries params
message DescribeBusinessConversationSummariesParams {
  // business id
  optional int64 business_id = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
  // order by, field name should be in BusinessConversationSummaryModel
  optional moego.utils.v2.OrderBy order_by = 3;
}

// describe business conversation summaries result
message DescribeBusinessConversationSummariesResult {
  // summaries
  repeated moego.models.ai_assistant.v1.BusinessConversationSummaryModel summaries = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // business map, key is business id
  map<int64, google.protobuf.Struct> deprecated_business_map = 3 [deprecated = true];
}

// describe business conversations params
message DescribeBusinessConversationsParams {
  // business id
  optional int64 business_id = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// describe business conversations result
message DescribeBusinessConversationsResult {
  // conversations
  repeated moego.models.ai_assistant.v1.BusinessConversationModel conversations = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // business map, key is business id
  map<int64, google.protobuf.Struct> deprecated_business_map = 3 [deprecated = true];
  // staff map, key is staff id
  map<int64, google.protobuf.Struct> deprecated_staff_map = 4 [deprecated = true];
}

// get conversation questions params
message GetConversationQuestionsParams {
  // conversation id
  int64 conversation_id = 1;
}

// get conversation questions result
message GetConversationQuestionsResult {
  // questions
  repeated moego.models.ai_assistant.v1.BusinessConversationQuestionModel questions = 1;
  // staff map, key is staff id
  map<int64, google.protobuf.Struct> deprecated_staff_map = 4 [deprecated = true];
}

// business conversation service
service BusinessConversationService {
  // describe system summaries
  rpc DescribeSystemSummaries(DescribeSystemSummariesParams) returns (DescribeSystemSummariesResult);

  // describe business conversation summaries
  rpc DescribeConversationSummaries(DescribeBusinessConversationSummariesParams) returns (DescribeBusinessConversationSummariesResult);

  // describe business conversations
  rpc DescribeConversations(DescribeBusinessConversationsParams) returns (DescribeBusinessConversationsResult);

  // get conversation questions
  rpc GetConversationQuestions(GetConversationQuestionsParams) returns (GetConversationQuestionsResult);
}
