// @since 2023-05-23 11:43:54
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.account.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/account_impersonate_log_models.proto";
import "moego/models/account/v1/account_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.account.v1";

// get login token request
message CreateLoginTokenParams {
  // account identifier
  oneof identifier {
    option (validate.required) = true;

    // account id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];

    // email
    string email = 2 [(validate.rules).string = {
      min_len: 3
      max_len: 100
    }];

    // phone number
    string phone_number = 3 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  }

  // source
  string source = 4 [(validate.rules).string = {
    in: [
      "business",
      "customer"
    ]
  }];

  // renewable
  bool renewable = 5;

  // max age, [0, 15d]
  // > 2h will disable renewable
  google.protobuf.Duration max_age = 6 [(validate.rules).duration = {
    gte: {seconds: 0}
    lte: {seconds: 1296000}
  }];
}

// get login token response
message CreateLoginTokenResult {
  // token, if lark_approval_status is not "APPROVED", token will be empty
  string token = 1;
  // remaining max age, may not set if token is empty
  google.protobuf.Duration remaining_max_age = 2;
  // lark approval status
  string lark_approval_status = 3;
  // instance code for lark approval
  string instance_code = 4;
}

// list account impersonate logs request
message ListAccountImpersonateLogsParams {
  // impersonator
  optional string impersonator = 1 [(validate.rules).string = {max_len: 100}];

  // target account id
  optional int64 target_account_id = 2 [(validate.rules).int64 = {gt: 0}];

  // sources
  repeated string sources = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];

  // start time
  optional google.protobuf.Timestamp start_time = 4;

  // end time
  optional google.protobuf.Timestamp end_time = 5;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 10;
}

// list account impersonate logs response
message ListAccountImpersonateLogsResult {
  // impersonate logs
  repeated moego.models.account.v1.AccountImpersonateLogModel logs = 1;
  // target accounts' emails
  map<int64, string> target_account_emails = 2;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// search account request
message SearchAccountParams {
  // the term
  string term = 1 [(validate.rules).string = {max_len: 50}];
}

// search account response
message SearchAccountResult {
  // accounts
  repeated moego.models.account.v1.AccountModelSearchView accounts = 1;
}

// describe accounts request
message DescribeAccountsParams {
  // account id
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // name like
  optional string name_like = 2 [(validate.rules).string = {max_len: 50}];

  // email like
  optional string email_like = 3 [(validate.rules).string = {max_len: 100}];

  // source
  optional string source = 4 [(validate.rules).string = {
    in: [
      "business",
      "customer",
      "mis"
    ]
  }];

  // include deleted
  bool include_deleted = 5;

  // order by, field name should be in AccountModel
  optional moego.utils.v2.OrderBy order_by = 14;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// describe accounts response
message DescribeAccountsResult {
  // account list
  repeated moego.models.account.v1.AccountModel accounts = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// change password params
message ChangePasswordParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // new password
  string password = 2 [(validate.rules).string = {
    min_len: 6
    max_len: 50
  }];
}

// change email params
message ChangeEmailParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // new email
  string email = 2 [(validate.rules).string = {
    min_len: 3
    max_len: 100
  }];
}

// describe account with companies params
message DescribeAccountWithCompaniesParams {
  // type
  string type = 1 [(validate.rules).string = {
    in: [
      "accountId",
      "accountEmail",
      "businessId",
      "companyId",
      "staffId"
    ]
  }];

  // value
  string value = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
}

// describe business def
message DescribeBusinessDef {
  // business
  google.protobuf.Struct business = 1;
  // staffs in this business
  repeated google.protobuf.Struct staffs = 2;
}

// describe company def
message DescribeCompanyDef {
  // company
  google.protobuf.Struct company = 1;
  // businesses in this company
  repeated DescribeBusinessDef businesses = 2;
}

// describe account with companies result
message DescribeAccountWithCompaniesResult {
  // account
  moego.models.account.v1.AccountModel account = 1;
  // companies
  repeated DescribeCompanyDef companies = 2;
}

// freeze account params
message FreezeAccountParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// unfreeze account params
message UnfreezeAccountParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete account params
message DeleteAccountParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// recover account params
message RecoverAccountParams {
  // account id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// the account admin service
service AccountService {
  // get login token
  rpc CreateLoginToken(CreateLoginTokenParams) returns (CreateLoginTokenResult);

  // list account impersonate logs
  rpc ListAccountImpersonateLogs(ListAccountImpersonateLogsParams) returns (ListAccountImpersonateLogsResult);

  // search account without pagination
  // if has no permission, will match email extract
  rpc SearchAccount(SearchAccountParams) returns (SearchAccountResult);

  // describe accounts
  rpc DescribeAccounts(DescribeAccountsParams) returns (DescribeAccountsResult);

  // describe account with companies
  rpc DescribeAccountWithCompanies(DescribeAccountWithCompaniesParams) returns (DescribeAccountWithCompaniesResult);

  // change password
  rpc ChangePassword(ChangePasswordParams) returns (google.protobuf.Empty);

  // change email
  rpc ChangeEmail(ChangeEmailParams) returns (google.protobuf.Empty);

  // freeze account
  rpc FreezeAccount(FreezeAccountParams) returns (google.protobuf.Empty);

  // unfreeze account
  rpc UnfreezeAccount(UnfreezeAccountParams) returns (google.protobuf.Empty);

  // delete account
  rpc DeleteAccount(DeleteAccountParams) returns (google.protobuf.Empty);

  // recover account
  rpc RecoverAccount(RecoverAccountParams) returns (google.protobuf.Empty);
}
