// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/type/money.proto";
import "moego/models/pay_ops/v1/billing_enums.proto";
import "moego/models/pay_ops/v1/billing_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// params for getting balance change records api
message GetStripeCompanyCustomerListParams {
  // account id, reserved field, which is not available for the current business.
  optional int64 account_id = 1 [(validate.rules).int64.gt = 0];
  // company id, used to query the stripe company customer list
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // pagination params
  moego.utils.v2.PaginationRequest pagination = 15;
}

// result for getting balance change records api
message GetStripeCompanyCustomerListResult {
  // List data, used to query the list of relationships between company and customer
  repeated moego.models.pay_ops.v1.StripeCompanyCustomerSimpleView stripe_company_customer_list = 1;
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 15;
}

// query the request parameters of a single stripe company customer
message GetStripeCompanyCustomerDetailParams {
  // account id, reserved field, which is not available for the current business.
  optional int64 id = 1 [(validate.rules).int64.gt = 0];
}

// get stripe customer info params
message GetStripeCustomerInfoParams {
  // stripe company customer model
  string stripe_customer_id = 1;
}

// change customer balance params
message ChangeCustomerBalanceParams {
  // stripe customer id
  string stripe_customer_id = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 20
  }];
  // adjustment type
  // represents the type of adjustment, which can be CREDIT or DEBIT
  string adjustment_type = 2 [(validate.rules).string = {
    in: [
      "CREDIT",
      "DEBIT"
    ]
  }];
  // amount,
  // represents the numerical value of the amount modified in a single transaction
  // The unit of currency is in cents
  double amount = 3 [(validate.rules).double = {
    gt: 0
    lt: 1000
  }];
  // note
  // represents the note of the transaction
  optional string note = 4 [(validate.rules).string = {max_len: 250}];
}

// change customer balance result
message ChangeCustomerBalanceResult {
  // transaction id, used to query the transaction details
  string id = 1;
}

// get pay ops company permission state single params
message GetCompanyPermissionStateSingleParams {
  // id
  optional int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // stripe subscriptions id
  optional string stripe_subscriptions_id = 3;
}

// get stripe subscription info params
message GetStripeSubscriptionParams {
  // stripe subscription id
  string stripe_subscription_id = 1;
}

// edit stripe subscription params
message EditStripeSubscriptionParams {
  // coupon id
  string coupon_id = 1;
  // stripe subscription id
  string stripe_subscription_id = 2;
  // edit type
  // used to distinguish actions
  moego.models.pay_ops.v1.EditSubscriptionType edit_type = 3;
}

// edit stripe subscription result
message EditStripeSubscriptionResult {
  // stripe subscription id
  string stripe_subscription_id = 1;
}

// stripe subscription card list params
message GetStripeSubscriptionCardListParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// stripe subscription card list result
message GetStripeSubscriptionCardListResult {
  //stripe subscription card list
  repeated moego.models.pay_ops.v1.StripeCardListInfoModel cards_list = 1;
}

// stripe subscription Recharge params
message RechargeSubscriptionParams {
  //company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  //stripe subscription payment card id
  string card_id = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
}

// stripe subscription Recharge result
message RechargeSubscriptionResult {
  //recharge result
  string recharge_result = 1;
}

// Pay admin ops stripe  create invoice  params
message CreateInvoiceParams {
  //stripe customer id
  string customer_id = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
}

// payadmin create invoice result
message CreateInvoiceResult {
  //stripe invoice id
  string invoice_id = 1;
}

// Pay admin ops stripe  create invoiceItem  params
message CreateInvoiceItemParams {
  //stripe customer id
  string customer_id = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  //item description
  string description = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 100
  }];
  //item amount
  google.type.Money amount = 3;
  //item quantity
  int32 quantity = 4 [(validate.rules).int32.gt = 0];
  //item bind invoiceid
  string invoice_id = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
}

// stripe create invoice item result
message CreateInvoiceItemResult {
  //return invoice id
  string invoice_id = 1;
}

// Pay admin ops stripe  pay invoice params
message PayInvoiceParams {
  //pay invoice id
  string invoice_id = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  //pay card id
  string card_id = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
}

// stripe pay invoice result
message PayInvoiceResult {
  //return invoice paid status
  string invoice_id = 1;
}

// Provide billing related rpc services
service BillingService {
  // query the balance record corresponding to the user
  rpc GetStripeCompanyCustomerList(GetStripeCompanyCustomerListParams) returns (GetStripeCompanyCustomerListResult);
  // query the details of a single record
  rpc GetStripeCompanyCustomerDetail(GetStripeCompanyCustomerDetailParams) returns (moego.models.pay_ops.v1.StripeCompanyCustomerModel);
  // query the stripe customer info
  rpc GetStripeCustomerInfo(GetStripeCustomerInfoParams) returns (moego.models.pay_ops.v1.StripeCustomerInfoModel);
  // change customer balance
  rpc ChangeCustomerBalance(ChangeCustomerBalanceParams) returns (ChangeCustomerBalanceResult);
  /**
   * pay ops company permission state api
   */
  // get pay ops company permission state single
  rpc GetCompanyPermissionStateSingle(GetCompanyPermissionStateSingleParams) returns (moego.models.pay_ops.v1.CompanyPermissionStateModel);
  // get stripe subscription info
  rpc GetStripeSubscriptionInfo(GetStripeSubscriptionParams) returns (moego.models.pay_ops.v1.StripeSubscriptionInfoModel);
  // edit stripe subscription
  rpc EditStripeSubscriptionInfo(EditStripeSubscriptionParams) returns (EditStripeSubscriptionResult);
  //  stripe subscription card list
  rpc GetStripeSubscriptionCardList(GetStripeSubscriptionCardListParams) returns (GetStripeSubscriptionCardListResult);

  //  stripe subscription Recharge
  rpc RechargeSubscription(RechargeSubscriptionParams) returns (RechargeSubscriptionResult);
  //  payadmin create invoice
  rpc CreateInvoice(CreateInvoiceParams) returns (CreateInvoiceResult);
  //  payadmin create invoice item
  rpc CreateInvoiceItem(CreateInvoiceItemParams) returns (CreateInvoiceItemResult);
  //  payadmin pay a invoice
  rpc PayInvoice(PayInvoiceParams) returns (PayInvoiceResult);
}
