// @since 2-23-12-13
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/empty.proto";
import "moego/models/pay_ops/v1/business_dispute_enums.proto";
import "moego/models/pay_ops/v1/business_dispute_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// get stripe dispute by stripe id params
message GetStripeDisputeListParams {
  // business id
  optional int64 business_id = 1;
  // date range
  optional string date_range = 2;
  // status
  optional string status = 3;
  // reason
  optional moego.models.pay_ops.v1.DisputeReasonType reason = 4;
  // customer name
  optional string customer = 5;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// get stripe dispute by stripe id result
message GetStripeDisputeListResult {
  // stripe dispute list
  repeated moego.models.pay_ops.v1.StripeDisputeModel stripe_dispute_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// UploadBusinessDisputeFileParams
message UploadBusinessDisputeFileParams {
  // business id
  bytes file = 1 [(validate.rules).bytes = {
    min_len: 1
    // 100 M
    max_len: 104857600
  }];
}

// UploadBusinessDisputeFileResult
message UploadBusinessDisputeFileResult {
  // dispute file id
  string file_id = 1;
}

// upload file key value model
message UploadFileContentTypeModel {
  // key
  string file_content = 1 [(validate.rules).string = {
    min_len: 1
    // 100 M
    max_len: 5242880
  }];

  // value
  moego.models.pay_ops.v1.DisputeUploadDocumentType file_type = 2;
}

// update load evidence params
message UpdateLoadEvidenceParams {
  // file content and type
  repeated UploadFileContentTypeModel file_list = 1;
  // dispute id
  string dispute_id = 2;
  // customer email address
  string customer_email_address = 3 [(validate.rules).string = {max_len: 20000}];
  // customer name
  string customer_name = 4;
  // billing address
  string billing_address = 5;
  // production description, deprecated, use product_description
  string production_description = 6 [deprecated = true];
  // service date
  int64 service_date = 7;
  // uncategorized text
  string uncategorized_text = 8 [(validate.rules).string = {max_len: 20000}];
  // The timestamp when the event happened
  int64 when_did_the_event_happen = 9;
  // The booking or reservation number
  string booking_or_reservation_number = 10;
  // The status of the booking or reservation
  string booking_or_reservation_status = 11;
  // The start timestamp of the booking or reservation
  int64 booking_or_reservation_start_date = 12;
  // The end timestamp of the booking or reservation
  int64 booking_or_reservation_end_date = 13;
  // A log of access activities, maximum length 20000 characters
  string access_activity_log = 14 [(validate.rules).string = {max_len: 20000}];
  // The status of the service
  string service_status = 15;
  // The original timestamp of the service
  int64 origin_service_date = 16;
  // The updated timestamp of the service
  int64 updated_service_date = 17;
  // The ticket number for the event, consisting of only numbers
  string event_ticket_number = 18;
  // The status of the event
  string event_status = 19;
  // The original timestamp of the event
  int64 original_event_date = 20;
  // The updated timestamp of the event
  int64 updated_event_date = 21;
  // Whether a credit or voucher was given to the customer
  bool credit_or_voucher_to_the_customer = 22;
  // Whether to show the customer
  bool show_the_customer = 23;
  // refund policy, deprecated, use refund_policy_disclosure
  string refund_policy = 24 [deprecated = true];
  // refund details, deprecated, use refund_refusal_explanation
  string refund_details = 25 [deprecated = true];
  /*
   * v2.0
   */
  // The explanation of the duplicate charge, maximum length 20000 characters
  string duplicate_charge_explanation = 26 [(validate.rules).string = {max_len: 20000}];
  // The ID of the charge that was duplicated
  string duplicate_charge_id = 27;
  // The rebuttal of the cancellation, maximum length 20000 characters
  string cancellation_rebuttal = 28 [(validate.rules).string = {max_len: 20000}];
  // The disclosure of the cancellation policy, maximum length 20000 characters
  string cancellation_policy_disclosure = 29 [(validate.rules).string = {max_len: 20000}];
  // The shipping address, maximum length 20000 characters
  string shipping_address = 30 [(validate.rules).string = {max_len: 20000}];
  // shipping date
  int64 shipping_date = 31;
  // The tracking number, maximum length 20000 characters
  string shipping_tracking_number = 32 [(validate.rules).string = {max_len: 20000}];
  // The shipping carrier
  string shipping_carrier = 33;
  // The IP address that the customer used when making the purchase
  string customer_purchase_ip = 34;
  // The product description, maximum length 20000 characters
  string product_description = 35 [(validate.rules).string = {max_len: 20000}];
  // The refund policy, maximum length 20000 characters
  string refund_policy_disclosure = 36 [(validate.rules).string = {max_len: 20000}];
  // Details about the refund, maximum length 20000 characters
  string refund_refusal_explanation = 37 [(validate.rules).string = {max_len: 20000}];
  // Service type maximum length 20000 characters
  string service_type = 38 [(validate.rules).string = {max_len: 20000}];
}

// update load evidence result
message UpdateLoadEvidenceResult {
  // id
  string id = 1;
}

// get dispute evidence info by dispute id params
message GetDisputeEvidenceInfoByDisputeIdParams {
  // dispute id
  string dispute_id = 1;
}

// get dispute evidence info by dispute id result
message GetDisputeEvidenceInfoByDisputeIdResult {
  // dispute evidence info
  moego.models.pay_ops.v1.StripeDisputeInfoModel dispute_evidence_info = 1;
  // dispute evidence file type
  map<int32, string> dispute_file_type = 2;
}

// static value result
message StaticValueModel {
  // dispute reason type
  map<int32, string> dispute_reason_type = 1;
  // dispute status type
  map<int32, string> dispute_status_type = 2;
}

// business dispute service
service BusinessDisputeService {
  // get stripe dispute by stripe id
  rpc GetStripeDisputeList(GetStripeDisputeListParams) returns (GetStripeDisputeListResult);
  // upload business dispute file
  rpc UploadBusinessDisputeFile(UploadBusinessDisputeFileParams) returns (UploadBusinessDisputeFileResult);
  // update load evidence
  rpc UpdateLoadEvidence(UpdateLoadEvidenceParams) returns (UpdateLoadEvidenceResult);
  // get Dispute evidence info by dispute id
  rpc GetDisputeEvidenceInfoByDisputeId(GetDisputeEvidenceInfoByDisputeIdParams) returns (GetDisputeEvidenceInfoByDisputeIdResult);
  // get static value
  rpc GetStaticValue(google.protobuf.Empty) returns (StaticValueModel);
}
