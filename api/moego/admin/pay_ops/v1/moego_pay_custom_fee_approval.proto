syntax = "proto3";

package moego.admin.pay_ops.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// custom fee approval service
service MoegoPayCustomFeeApprovalApi {
  // List custom fee approvals
  rpc ListMoegoPayMoegoPayCustomFeeApproval(ListMoegoPayCustomFeeApprovalParams) returns (ListMoegoPayCustomFeeApprovalResult);
  // Approve custom fee approval
  rpc ApproveMoegoPayCustomFeeApproval(ApproveMoegoPayCustomFeeApprovalParams) returns (ApproveMoegoPayCustomFeeApprovalResult);
  // Reject custom fee approval
  rpc RejectMoegoPayCustomFeeApproval(RejectMoegoPayCustomFeeApprovalParams) returns (RejectMoegoPayCustomFeeApprovalResult);
}

// MoegoPayCustomFeeApproval
message MoegoPayCustomFeeApproval {
  // id
  string id = 1;
  // contract id
  string contract_id = 2;
  // company id
  int64 company_id = 3;
  // account id
  int64 account_id = 4;
  // owner email
  string owner_email = 5;
  // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
  string terminal_percentage = 6;
  // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
  string terminal_fixed = 7;
  // 非终端收款手续费百分比，数字格式
  string non_terminal_percentage = 8;
  // 非终端收款固定手续费，单位为 USD，数字格式
  string non_terminal_fixed = 9;
  // 每月最低交易额要求，单位为 USD，数字格式
  string min_volume = 10;
  // creator
  string creator = 11;
  // handler
  string handler = 12;
  // approval state
  ApprovalState approval_state = 13;
  // create time
  google.protobuf.Timestamp create_time = 14;
  // update time
  google.protobuf.Timestamp update_time = 15;
  // approve time
  google.protobuf.Timestamp approve_time = 16;

  // approval state
  enum ApprovalState {
    // unspecified
    APPROVAL_STATE_UNSPECIFIED = 0;
    // ignored
    IGNORED = 1;
    // pending
    PENDING = 2;
    // approved
    APPROVED = 3;
    // rejected
    REJECTED = 4;
  }
}

// ListMoegoPayCustomFeeApprovalParams
message ListMoegoPayCustomFeeApprovalParams {
  // email
  optional string email = 1;
  // approval states
  repeated MoegoPayCustomFeeApproval.ApprovalState approval_states = 3;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// ListMoegoPayCustomFeeApprovalResult
message ListMoegoPayCustomFeeApprovalResult {
  // CustomFee message for simplified fee structure
  message CustomFee {
    // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
    string terminal_percentage = 1;
    // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
    string terminal_fixed = 2;
    // 非终端收款手续费百分比，数字格式
    string non_terminal_percentage = 3;
    // 非终端收款固定手续费，单位为 USD，数字格式
    string non_terminal_fixed = 4;
    // 每月最低交易额要求，单位为 USD，数字格式
    string min_volume = 5;
  }

  // custom fee approvals
  repeated MoegoPayCustomFeeApproval approvals = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // company custom fees for pending approvals
  map<int64, CustomFee> company_custom_fees = 3;
}

// ApproveMoegoPayCustomFeeApprovalParams
message ApproveMoegoPayCustomFeeApprovalParams {
  // approval id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// ApproveMoegoPayCustomFeeApprovalResult
message ApproveMoegoPayCustomFeeApprovalResult {}

// RejectMoegoPayCustomFeeApprovalParams
message RejectMoegoPayCustomFeeApprovalParams {
  // approval id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// RejectMoegoPayCustomFeeApprovalResult
message RejectMoegoPayCustomFeeApprovalResult {}
