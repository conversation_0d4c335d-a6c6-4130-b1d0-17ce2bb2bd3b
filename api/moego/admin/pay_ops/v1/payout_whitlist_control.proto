// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// company custom feature relation
message CompanyFeatureRelation {
  // id
  uint64 id = 1;
  // company id
  int64 company_id = 2;
  // code
  string code = 3;
  // allow type
  int32 allow_type = 4;
  // enable
  int32 enable = 5;
  // quota
  int32 quota = 6;
  // expiration time
  int64 expiration_time = 7;
  // create time
  int64 create_time = 8;
  // update time
  int64 update_time = 9;
  // note
  optional string note = 10;
  // is deleted
  bool is_deleted = 11;
  // company name
  string company_name = 12;
}

// company custom feature relation list response
message CompanyFeatureRelationListResult {
  // company custom feature relation
  repeated CompanyFeatureRelation company_feature_relation = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// whitelist control get request
message CompanyFeatureRelationGetParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// create company custom feature relation params
message CreateCompanyFeatureRelationParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // note
  string note = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 2048
  }];
}

// create company custom feature relation result
message CreateCompanyFeatureRelationResult {
  // id
  uint64 id = 1 [(validate.rules).uint64 = {gt: 0}];
}

// deleted company custom feature relation params
message DeletedCompanyFeatureRelationParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// deleted company custom feature relation result
message DeletedCompanyFeatureRelationResult {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// payout whitelist control service
service PayoutWhitelistControlService {
  // get payout whitelist control list
  rpc GetCompanyFeatureRelationList(CompanyFeatureRelationGetParams) returns (CompanyFeatureRelationListResult) {}

  // create payout whitelist control
  rpc CreateCompanyFeatureRelation(CreateCompanyFeatureRelationParams) returns (CreateCompanyFeatureRelationResult) {}

  // delete payout whitelist control
  rpc DeletedCompanyFeatureRelation(DeletedCompanyFeatureRelationParams) returns (DeletedCompanyFeatureRelationResult) {}
}
