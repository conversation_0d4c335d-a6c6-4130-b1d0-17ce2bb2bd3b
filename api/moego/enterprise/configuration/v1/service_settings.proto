syntax = "proto3";

package moego.enterprise.configuration.v1;

import "moego/models/enterprise/v1/price_book_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/configuration/v1;configurationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.configuration.v1";

// create lodging type request
message CreateLodgingTypeParams {
  // lodging type
  moego.models.enterprise.v1.CreateLodgingTypeDef lodging_type_def = 1 [(validate.rules).message = {required: true}];
}

// create lodging type response
message CreateLodgingTypeResult {
  // lodging type
  moego.models.enterprise.v1.LodgingType lodging_type = 1;
}

// update lodging type request
message UpdateLodgingTypeParams {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // update lodging type
  moego.models.enterprise.v1.UpdateLodgingTypeDef lodging_type_def = 2 [(validate.rules).message = {required: true}];
}

// update lodging type response
message UpdateLodgingTypeResult {
  // lodging type
  moego.models.enterprise.v1.LodgingType lodging_type = 1;
}

// list lodging types request
message ListLodgingTypesParams {
  // filter
  message Filter {
    // lodging type ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      max_items: 1000
    }];
  }
  // filter
  Filter filter = 1;
}

// list lodging types response
message ListLodgingTypesResult {
  // lodging types
  repeated moego.models.enterprise.v1.LodgingType lodging_types = 1;
}

// PushLodgingTypesParams
message PushLodgingTypesParams {
  // lodging ids
  repeated int64 ids = 1;
  // targets
  repeated moego.models.enterprise.v1.TenantObject targets = 2;
}

// PushLodgingsResult
message PushLodgingTypesResult {
  // success company ids
  repeated int64 success_company_ids = 1;
  // failed company ids
  repeated int64 failed_company_ids = 2;
}
