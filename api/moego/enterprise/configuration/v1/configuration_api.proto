syntax = "proto3";

package moego.enterprise.configuration.v1;

import "google/protobuf/timestamp.proto";
import "moego/enterprise/configuration/v1/pet_settings.proto";
import "moego/enterprise/configuration/v1/service_settings.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/configuration/v1;configurationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.configuration.v1";

// configuration service
service ConfigurationService {
  // create lodging type
  rpc CreateLodgingType(CreateLodgingTypeParams) returns (CreateLodgingTypeResult) {}
  // update lodging type
  rpc UpdateLodgingType(UpdateLodgingTypeParams) returns (UpdateLodgingTypeResult) {}
  // list lodging types
  rpc ListLodgingTypes(ListLodgingTypesParams) returns (ListLodgingTypesResult) {}
  // push lodging types
  rpc PushLodgingTypes(PushLodgingTypesParams) returns (PushLodgingTypesResult) {}

  // create pet code
  rpc CreatePetCode(CreatePetCodeParams) returns (CreatePetCodeResult) {}
  // update pet code
  rpc UpdatePetCode(UpdatePetCodeParams) returns (UpdatePetCodeResult) {}
  // list pet codes
  rpc ListPetCodes(ListPetCodesParams) returns (ListPetCodesResult) {}
  // delete pet code
  rpc DeletePetCode(DeletePetCodeParams) returns (DeletePetCodeResult) {}
  // push pet codes
  rpc PushPetCodes(PushPetCodesParams) returns (PushPetCodesResult) {}
  // sort pet codes
  rpc SortPetCodes(SortPetCodesParams) returns (SortPetCodesResult) {}

  // create pet metadata
  rpc CreatePetMetadata(CreatePetMetadataParams) returns (CreatePetMetadataResult) {}
  // update pet metadata
  rpc UpdatePetMetadata(UpdatePetMetadataParams) returns (UpdatePetMetadataResult) {}
  // delete pet metadata
  rpc DeletePetMetadata(DeletePetMetadataParams) returns (DeletePetMetadataResult) {}
  // list pet metadata
  rpc ListPetMetadata(ListPetMetadataParams) returns (ListPetMetadataResult) {}
  // sort pet metadata
  rpc SortPetMetadata(SortPetMetadataParams) returns (SortPetMetadataResult) {}
  // push pet metadata
  rpc PushPetMetadata(PushPetMetadataParams) returns (PushPetMetadataResult) {}
  // ListSurchargeAssociatedFoodSource
  rpc ListSurchargeAssociatedFoodSource(ListSurchargeAssociatedFoodSourceParams) returns (ListSurchargeAssociatedFoodSourceResult) {}
  // get pet settings time info
  rpc GetPetsSettingTimeInfo(GetPetsSettingTimeInfoParams) returns (GetPetsSettingTimeInfoResult) {}
}

// ListSurchargeAssociatedFoodSource
message ListSurchargeAssociatedFoodSourceParams {}

// ListSurchargeAssociatedFoodSourceResponse
message ListSurchargeAssociatedFoodSourceResult {
  // food source ids
  repeated int64 food_source_ids = 1;
}

// get pet settings time info request
message GetPetsSettingTimeInfoParams {}

// get pet settings time info response
message GetPetsSettingTimeInfoResult {
  // setting type
  enum SettingType {
    // Unspecified setting type
    SETTING_TYPE_UNSPECIFIED = 0;
    // pet code
    PET_CODE = 1;
    // pet feeding
    PET_FEEDING = 2;
  }
  // time info
  message TimeInfo {
    // setting type
    SettingType type = 1;
    // last update time
    google.protobuf.Timestamp last_updated_at = 2;
    // last push time
    google.protobuf.Timestamp last_pushed_at = 3;
  }
  // time infos
  repeated TimeInfo time_infos = 1;
}
