syntax = "proto3";

package moego.enterprise.permission.v1;

import "moego/models/enterprise/v1/role_models.proto";
import "moego/models/permission/v1/permission_defs.proto";
import "moego/models/permission/v1/permission_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/permission/v1;permissionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.permission.v1";

// get role list request
message ListRolesParams {
  // enterprise id
  int64 enterprise_id = 1;
  // need permission
  bool need_permission = 2;
}

// get role list response
message ListRolesResult {
  // role result
  message Result {
    // role
    models.enterprise.v1.RoleModel role = 1;
    // role permissions
    repeated models.permission.v1.PermissionCategoryModel permission_category_list = 2;
  }
  // role list
  repeated Result roles = 1;
}

// get role request
message GetRoleParams {
  // role id
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // need permission
  bool need_permission = 2;
}

// get role response
message GetRoleResult {
  // role
  models.enterprise.v1.RoleModel role = 1;
  // role permissions
  repeated models.permission.v1.PermissionCategoryModel permission_category_list = 2;
}

// create role request
message CreateRoleParams {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// create role response
message CreateRoleResult {
  // created role id
  int64 id = 1;
  // role list after created
  repeated models.enterprise.v1.RoleModel roles = 2;
}

// update role request
message UpdateRoleParams {
  // role id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // role name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// update role response
message UpdateRoleResult {
  // role after updated
  models.enterprise.v1.RoleModel role = 1;
}

// delete role request
message DeleteRoleParams {
  // role id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete role response
message DeleteRoleResult {
  // role list after deleted
  repeated models.enterprise.v1.RoleModel roles = 1;
}

// duplicate role request
message DuplicateRoleParams {
  // role id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // role name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// duplicate role response
message DuplicateRoleResult {
  // role list after duplicated
  repeated models.enterprise.v1.RoleModel roles = 1;
}

// edit permissions request
message EditPermissionsParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gt = 0];
  // permission list in a category
  repeated moego.models.permission.v1.EditCategoryPermissionDef permission_categories = 2;
}

// edit permissions response
message EditPermissionsResult {
  // permission list after edited
  repeated moego.models.permission.v1.PermissionCategoryModel permission_categories = 1;
}

// permission service
service PermissionService {
  // get role list
  rpc ListRoles(ListRolesParams) returns (ListRolesResult) {}
  // get role detail
  rpc GetRole(GetRoleParams) returns (GetRoleResult) {}
  // create role
  rpc CreateRole(CreateRoleParams) returns (CreateRoleResult) {}
  // update role
  rpc UpdateRole(UpdateRoleParams) returns (UpdateRoleResult) {}
  // delete role
  rpc DeleteRole(DeleteRoleParams) returns (DeleteRoleResult) {}
  // duplicate role
  rpc DuplicateRole(DuplicateRoleParams) returns (DuplicateRoleResult) {}
  // edit permissions
  rpc EditPermissions(EditPermissionsParams) returns (EditPermissionsResult) {}
}
