syntax = "proto3";

package moego.models.promotion.v1;

import "google/protobuf/timestamp.proto";
import "google/type/decimal.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/promotion/v1/promotion.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.promotion.v1";

// Coupon 是 Promotion 赋予用户之后的快照，作为优惠的唯一 fact
message Coupon {
  // id
  int64 id = 1;
  // promotion id
  int64 promotion_id = 2;
  // source
  Source source = 3;
  // name
  string name = 4;
  // description
  string description = 5;
  // owner 优惠券的持有者
  User owner = 6;
  // restrictions
  Restrictions restrictions = 7;
  // discount
  Discount discount = 8;
  // validity period 有效期，根据 promotion 的 available_period 计算得到
  google.type.Interval validity_period = 9;
  // redemptions
  Redemptions redemptions = 10;
  // revision
  int64 revision = 11;
  // created at
  google.protobuf.Timestamp created_at = 12;
  // updated at
  google.protobuf.Timestamp updated_at = 13;
}

// CouponUsage 优惠券使用记录
message CouponUsage {
  // coupon
  Coupon coupon = 1;
  // targets
  repeated CouponApplicationTarget targets = 2;
}

// 用户
message User {
  // 用户类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // MoeGo
    MOEGO = 1;
    // 账号
    ACCOUNT = 2;
    // 企业
    ENTERPRISE = 3;
    // 公司
    COMPANY = 4;
    // 商家
    BUSINESS = 5;
    // 顾客
    CUSTOMER = 6;
    // 员工
    STAFF = 7;
  }
  // ID
  int64 id = 1;
  // 类型
  Type type = 2;
}

// coupon revision 消费记录，类似于 payment，不可修改，用来对账
message CouponRevision {
  // type
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // redeem 正向消费
    REDEEM = 1;
    // refund 逆向退款
    REFUND = 2;
  }
  // id
  int64 id = 1;
  // type
  Type type = 2;
  // coupon id
  int64 coupon_id = 3;
  // order id
  int64 order_id = 4;
  // order lint item id
  int64 order_line_item_id = 5;
  // user 使用者，一般是 customer
  User user = 6;
  // operator 操作者，一般是 staff
  User operator = 7;
  // targets 当前扣减时使用的对象
  Targets targets = 8;
  // redeemed times 单次使用的扣减次数
  int64 redeemed_times = 9;
  // cost 成本，根据 targets 当前在 order 中的价格进行记录
  google.type.Money cost = 10;
  // created at
  google.protobuf.Timestamp created_at = 11;
}

// TargetDeduction 目标抵扣结果
message TargetDeduction {
  // target_type 目标类型
  TargetType target_type = 1;
  // target_id 目标ID
  int64 target_id = 2;
  // original_amount 原价
  google.type.Money original_amount = 3;
  // final_amount 最终价格
  google.type.Money final_amount = 4;
  // coupon_deductions 优惠券抵扣列表
  repeated CouponDeduction coupon_deductions = 5;
  // order item id
  optional int64 order_item_id = 6;
}

// CouponDeduction 优惠券抵扣
message CouponDeduction {
  // coupon 优惠券信息
  Coupon coupon = 1;

  // deduction_value 抵扣值
  oneof deduction_value {
    // 固定金额
    google.type.Money fixed_amount = 2;
    // 百分比
    google.type.Decimal percentage = 3; // 0.00 ～ 100.00%
    // 数量
    int32 quantity = 4;
  }
}

// Coupon Redeem
message CouponRedeem {
  // coupon
  Source coupon_sources = 1;

  // Redeem Target
  message RedeemTarget {
    // target_type 目标类型
    TargetType target_type = 1;
    // target_id 目标ID
    int64 target_id = 2;
    // invoice item id
    int64 order_item_id = 3;
    // idempotence_key
    string idempotence_key = 4;
    // amount
    int64 amount = 5;
    // 目标收入 -- discount 核销使用
    google.type.Money target_sales = 6;
  }

  // targets
  repeated RedeemTarget targets = 2;
}

// CouponSearchCondition 优惠券搜索条件
message CouponSearchCondition {
  // customer id
  int64 customer_id = 1;
  // name keyword
  optional string name_keyword = 2;
  // Source type enum
  enum SourceType {
    // unspecified
    SOURCE_TYPE_UNSPECIFIED = 0;
    // discount
    DISCOUNT = 1;
    // package
    PACKAGE = 2;
    // membership
    MEMBERSHIP = 3;
  }
  // source type
  repeated SourceType source_types = 3;
  // simple target
  message Target {
    // target type
    TargetType target_type = 1;
    // target id
    int64 target_id = 2;
  }
  // targets
  repeated Target targets = 4;
}
