// @since 2023-06-16 16:48:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.price_checker.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/price_checker/v1;pricecheckerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.price_checker.v1";

// business mode
enum BusinessMode {
  // unspecified
  BUSINESS_MODE_UNSPECIFIED = 0;
  // mobile
  BUSINESS_MODE_MOBILE = 1;
  // salon
  BUSINESS_MODE_SALON = 2;
}

// pet type
enum PetType {
  // unspecified
  PET_TYPE_UNSPECIFIED = 0;
  // dog
  PET_TYPE_DOG = 1;
  // cat
  PET_TYPE_CAT = 2;
}

// service mode
enum ServiceMode {
  // unspecified
  SERVICE_MODE_UNSPECIFIED = 0;
  // grooming
  SERVICE_MODE_GROOMING = 1;
  // bathing
  SERVICE_MODE_BATHING = 2;
}

// pet size
enum PetSize {
  // unspecified
  PET_SIZE_UNSPECIFIED = 0;
  // small
  PET_SIZE_SMALL = 1;
  // medium
  PET_SIZE_MEDIUM = 2;
  // large
  PET_SIZE_LARGE = 3;
  // extra large
  PET_SIZE_EXTRA_LARGE = 4;
}

// coat type
enum CoatType {
  // unspecified
  COAT_TYPE_UNSPECIFIED = 0;
  // short
  COAT_TYPE_SHORT = 1;
  // wire
  COAT_TYPE_WIRE = 2;
  // double
  COAT_TYPE_DOUBLE = 3;
  // soft
  COAT_TYPE_SOFT = 4;
  // doodle
  COAT_TYPE_DOODLE = 5;
}

// business position
enum BusinessPosition {
  // unspecified
  BUSINESS_POSITION_UNSPECIFIED = 0;
  // affordable
  BUSINESS_POSITION_AFFORDABLE = 1;
  // standard
  BUSINESS_POSITION_STANDARD = 2;
  // upscale
  BUSINESS_POSITION_UPSCALE = 3;
}
