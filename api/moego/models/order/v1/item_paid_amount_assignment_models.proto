syntax = "proto3";

package moego.models.order.v1;

import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Assignment information for a single item
message ItemPaidAmountAssignment {
  // Item ID
  int64 item_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Amount to be assigned
  google.type.Money assigned_paid_amount = 2;
}
