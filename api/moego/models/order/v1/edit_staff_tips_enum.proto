syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// staff change log relate type
enum StaffChangeLogRelateType {
  // staff change log relate type unspecified
  STAFF_CHANGE_LOG_RELATE_TYPE_UNSPECIFIED = 0;
  // staff change log relate type order item
  STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_STAFF = 1;
  // staff change log relate type tips
  STAFF_CHANGE_LOG_RELATE_TYPE_EDIT_TIPS = 2;
}

// staff change log amount type
enum StaffChangeLogAmountType {
  // staff change log amount type unspecified
  STAFF_CHANGE_LOG_AMOUNT_TYPE_UNSPECIFIED = 0;
  // staff change log amount type order item
  STAFF_CHANGE_LOG_AMOUNT_TYPE_ORDER_ITEM = 1;
  // staff change log amount type tips
  STAFF_CHANGE_LOG_AMOUNT_TYPE_TIPS = 2;
}
