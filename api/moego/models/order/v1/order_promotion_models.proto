syntax = "proto3";

package moego.models.order.v1;

import "google/protobuf/timestamp.proto";
import "google/type/decimal.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// order promotion model
message OrderPromotionModel {
  // id
  int64 id = 1;
  // create time
  google.protobuf.Timestamp create_time = 2;
  // update time
  google.protobuf.Timestamp update_time = 3;
  // order id
  int64 order_id = 4;
  // promotion id
  int64 promotion_id = 5;
  // 使用的优惠.
  oneof promotion {
    // discount
    DiscountSubject discount = 6;
    // package
    PackageSubject package = 7;
    // membership
    MembershipSubject membership = 8;
    // store credit
    StoreCreditSubject store_credit = 9;
    // 一次性的 discount.
    OneTimeDiscountCode one_time_discount = 10;
  }

  // DiscountSubject 折扣主体
  // one time discount 也体现在这，order item id 等都是0
  message DiscountSubject {
    // id 折扣ID
    int64 id = 1;
    // subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
    int64 subject_id = 2;
    // discount code
    string discount_code = 3;
    // discount type
    DiscountType discount_type = 4;
    // discount value
    // - type 为 percentage 时对应 百分比
    // - type 为 fixed amount 时对应 金额
    google.type.Decimal discount_value = 5;
  }

  // PackageSubject 套餐服务主体
  message PackageSubject {
    // id 套餐服务ID
    int64 id = 1;
    // subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
    int64 subject_id = 2;
    // package name
    string package_name = 3;
  }

  // MembershipSubject 会员权益主体
  message MembershipSubject {
    // id 会员权益ID
    int64 id = 1;
    // subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
    int64 subject_id = 2;
    // membership name
    string name = 3;
    // discount type
    DiscountType discount_type = 4;
    // discount value
    // - type 为 percentage 时对应 百分比
    // - type 为 fixed amount 时对应 金额
    google.type.Decimal discount_value = 5;
  }

  // StoreCreditSubject
  message StoreCreditSubject {
    // 抵扣的金额
    google.type.Money amount = 1;
  }
  // discount type
  DiscountType discount_type = 11;
  // discount value
  // - type 为 percentage 时对应 百分比
  // - type 为 fixed amount 时对应 金额
  // - type 为 item deduction 时对应 抵扣数量
  google.type.Decimal discount_value = 12;
  // applied amount
  google.type.Money applied_amount = 13;
  // status
  Status status = 14;
  // item list
  repeated OrderPromotionItem items = 15;

  // 状态
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // preview 还未落库
    PREVIEW = 1;
    // created 刚创建，还未调用 redeem
    CREATED = 2;
    // applied 已抵扣
    APPLIED = 3;
    // cancelled 已取消
    CANCELLED = 4;
  }
}

// order promotion item
message OrderPromotionItem {
  // id
  int64 id = 1;
  // create time
  google.protobuf.Timestamp create_time = 2;
  // update time
  google.protobuf.Timestamp update_time = 3;
  // order promotion id
  int64 order_promotion_id = 4;
  // order item id
  int64 order_item_id = 5;
  // external uuid
  string external_uuid = 6;
  // applied amount
  google.type.Money applied_amount = 7;
}

// 优惠类型
enum DiscountType {
  // unspecified
  DISCOUNT_TYPE_UNSPECIFIED = 0;
  // percentage 按百分比
  PERCENTAGE = 1;
  // fixed amount 按固定金额
  FIXED_AMOUNT = 2;
  // item 抵扣
  ITEM_DEDUCTION = 3;
}

// 一次性的优惠
message OneTimeDiscountCode {
  // 一次性优惠只有 2 种模式，并且互斥.
  oneof discount_detail {
    // 按比例打折
    google.type.Decimal discount_percentage = 1;
    // 按金额打折
    google.type.Money discount_amount = 2;
  }
}
