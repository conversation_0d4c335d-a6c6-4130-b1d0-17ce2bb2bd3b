// @since 2025-03-07 14:58:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// surcharge type, also known as service charge type
enum SurchargeType {
  // unspecified value
  SURCHARGE_TYPE_UNSPECIFIED = 0;
  // off-hours fee (Late pick-up/Early drop-off)
  OFF_HOURS_FEE = 1;
  // custom fee
  CUSTOM_FEE = 2;
  // charge 24-hour
  CHARGE_24_HOUR = 3;
  // feeding fee
  FEEDING_FEE = 4;
  // medication fee
  MEDICATION_FEE = 5;
}

// charge method
enum ChargeMethod {
  // unspecified value
  CHARGE_METHOD_UNSPECIFIED = 0;
  // per day
  PER_DAY = 1;
  // per administration
  PER_ADMINISTRATION = 2;
}
