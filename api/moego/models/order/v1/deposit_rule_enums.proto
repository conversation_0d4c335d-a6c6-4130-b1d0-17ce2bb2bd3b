syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// DEPOSIT_AMOUNT_TYPE
enum DepositAmountType {
  // Unspecified
  DEPOSIT_AMOUNT_TYPE_UNSPECIFIED = 0;
  // By fixed amount
  BY_FIXED_AMOUNT = 1;
  // By percentage
  BY_PERCENTAGE = 2;
}
