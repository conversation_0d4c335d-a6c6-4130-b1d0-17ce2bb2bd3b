syntax = "proto3";

package moego.models.order.v1;

//import "moego/models/order/v1/order_enums.proto";
option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * line extra fee info
 */
message OrderLineExtraFeeModel {
  // id
  optional int64 id = 1;
  // businessId
  int64 business_id = 2;
  // orderId
  int64 order_id = 3;
  // orderItemId
  optional int64 order_item_id = 4;
  // applyType
  string apply_type = 5;
  // isDeleted
  optional bool is_deleted = 6;
  // taxId
  string fee_type = 7;
  // taxRate
  double amount = 8;
  // taxAmount
  optional string name = 9;
  // taxAmount
  optional string description = 10;
  // collectType
  string collect_type = 11;
  // applyBy
  int64 apply_by = 12;
  // applySequence
  optional int32 apply_sequence = 13;
  // create time
  optional int64 create_time = 14;
  // update time
  optional int64 update_time = 15;
}
