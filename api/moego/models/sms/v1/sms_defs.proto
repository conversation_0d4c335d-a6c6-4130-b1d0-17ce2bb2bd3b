// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.sms.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1;smspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.sms.v1";

// The Sms Definition
// Note:
// 1. a def message must end with Def
// 2. a def message could be used in both request and response
// 3. all the fields of a def message must be validated
// 4. a def message's semantics must be single, which means you
//    cannot share a single def message when anyone field is
//    different, including labels.
message SmsDef {
  // demo field, please delete me
  string demo_field = 1 [(validate.rules).string = {max_len: 100}];
}
