// @since 2022-05-30 17:50:40
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.todo.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/todo/v1;todopb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.todo.v1";

// todo
message TodoModel {
  // todo status
  enum Status {
    // none
    STATUS_UNSPECIFIED = 0;
    // pending
    STATUS_PENDING = 1;
    // in progress
    STATUS_IN_PROGRESS = 2;
    // done
    STATUS_DONE = 3;
  }

  // id of the todo
  int64 id = 1;
  // the user id
  int64 user_id = 2;
  // title
  string title = 3;
  // status
  Status status = 4;
  // create time
  google.protobuf.Timestamp created_at = 5;
  // update time
  google.protobuf.Timestamp updated_at = 6;
  // delete time
  google.protobuf.Timestamp deleted_at = 7;
}

// todo multiple view demo
message TodoModelListView {
  // id of the todo
  int64 id = 1;
  // the user id
  int64 user_id = 2;
  // title
  string title = 3;
  // status
  TodoModel.Status status = 4;
}

// todo multiple view demo
message TodoModelPrintView {
  // id of the todo
  int64 id = 1;
  // the user id
  int64 user_id = 2;
}
