syntax = "proto3";
package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// resource def
message ResourceDef {
  // is all
  bool is_all = 1;
  // tenant ids
  repeated int64 tenant_ids = 2;
  // group ids
  repeated int64 group_ids = 3;
}

// staff profile
message CreateStaffProfile {
  // avator path
  optional string avatar_path = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // first name
  string first_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // last name
  string last_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // role_id
  int64 role_id = 4 [(validate.rules).int64 = {gte: 0}];
  // hired time
  google.protobuf.Timestamp hire_time = 5;
  // color code
  string color_code = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // note
  optional string note = 7 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // email
  optional string profile_email = 8 [(validate.rules).string = {
    email: true
    max_len: 100
  }];
}

// update staff profile
message UpdateStaffProfile {
  // avator path
  optional string avatar_path = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // first name
  optional string first_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // last name
  optional string last_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // role_id
  optional int64 role_id = 4 [(validate.rules).int64 = {gte: 0}];
  // hired time
  optional google.protobuf.Timestamp hire_time = 5;
  // color code
  optional string color_code = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 20
  }];
  // note
  optional string note = 7 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // email
  optional string profile_email = 8 [(validate.rules).string = {
    email: true
    max_len: 100
  }];
}
