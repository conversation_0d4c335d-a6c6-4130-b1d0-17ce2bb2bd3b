syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

//  the company message cycle
message MessageCycle {
  // the company id
  int64 company_id = 1;
  // the message cycle period
  moego.utils.v1.TimePeriod period = 2;
}

// the message task
message MessageTask {
  // the customer id
  enum Status {
    // Unspecified status
    STATUS_UNSPECIFIED = 0;
    // ready for start
    READY = 1;
    // running
    RUNNING = 2;
    // finished
    FINISHED = 3;
    // failed
    FAILED = 4;
  }
  // id
  int64 id = 1;
  // the customer id
  int64 customer_id = 2;
  // the business id
  int64 business_id = 3;
  // the company id
  int64 company_id = 4;
  // the execute time
  google.protobuf.Timestamp execute_at = 5;
  // the message status
  string message = 6;
  // the business name
  string business_name = 7;
  // the customer phone
  string customer_phone = 8;
  // the message status
  Status status = 9;
  // the failure reason
  string failure_reason = 10;
}
