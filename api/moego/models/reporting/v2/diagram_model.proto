syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/field_model.proto";
import "moego/models/reporting/v2/filter_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// An enumeration of diagram type
enum DiagramType {
  // Unspecified diagram type
  DIAGRAM_TYPE_UNSPECIFIED = 0;
  // BAR diagram
  BAR = 1;
  // Funnel diagram
  FUNNEL = 2;
  // Table diagram
  TABLE = 3;
  // Pie diagram
  PIE = 4;
  // Big diagram
  BIG_NUMBER = 5;
  // Status bar diagram
  STATUS_BAR = 6;
  // Group
  GROUP = 7;
  // Rank
  RANK = 8;
  // Procedure number
  PROCEDURE_NUMBER = 9;
  // A array of number with label
  NUMBER_ARRAY = 10;
  // A line chart type
  LINE = 11;
  // Odometer
  ODOMETER = 12;
  // Rating star
  RATING_STAR = 13;
  // Number list, display as rows
  NUMBER_LIST = 14;
  // Bar chart version 2
  BAR_V2 = 15;
  // Legend
  LEGEND = 16;
  // Dynamic table
  DYNAMIC_TABLE = 17;
  // Multi dimension table
  MULTI_DIMENSION_TABLE = 18;
}

// Customer KPI configuration
message CustomizeKPI {
  // The label of the KPI
  string label = 1;
  // The diagram id of the KPI
  string diagram_id = 2;
  // The checkbox status of the KPI
  bool selected = 3;
}

// A table customized configuration
message TableCustomizedConfig {
  // The sorted field
  repeated string sorted_field_keys = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 50
  }];
  // The hidden field keys
  repeated string hidden_field_keys = 2 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 50
  }];
  // The group by field keys
  repeated CustomizeKPI kpis = 3;
  // Saved filters
  repeated FilterRequest saved_filters = 4;
}

// A table meta
message TableMeta {
  // current report table id
  string diagram_id = 1;
  // The fields of the table
  repeated Field fields = 2;
  // The filters of the table, deprecated by chris, use filter_groups instead
  repeated Filter filters = 3;
  // The customized configurations of the table
  TableCustomizedConfig customized_config = 4;
  // The title of the table
  string title = 5;
  // The description of the table
  string description = 6;
  // The download enable of the table
  bool download_enable = 7;
  // The drill configuration
  DrillConfig drill_config = 8;
  // default group by field key
  repeated string default_group_by_field_keys = 9;
  // Current table's required permission code
  string permission_code = 10;
  // The filter groups of the table
  repeated FilterGroup filter_groups = 11;
  // Whether to display location/franchisee selector
  bool show_scope_selector = 12;
  // Whether to display compare period selector
  bool show_compare_period_selector = 13;
  // Max grouping by dimensions count
  int32 max_query_dimensions = 14;
}

// A dashboard diagram data
message DiagramData {
  // The diagram id
  string diagram_id = 1;
  // The diagram content
  oneof data {
    // The number data
    NumberData number_data = 2;
    // The bar data
    BarData bar_data = 3;
    // The pie data
    PieData pie_data = 4;
    // The rainbow table data
    TableData rainbow_table_data = 5;
    // The rank data
    RankData rank_data = 6;
    // The procedure number data
    ProcedureNumberData procedure_number_data = 7;
    // The funnel data
    FunnelData funnel_data = 8;
    // The number array data
    NumberArrayData number_array_data = 9;
    // Line data
    LineData line_data = 10;
    // Odometer data
    OdometerData odometer_data = 11;
    // Rating star data
    RatingStarData rating_star_data = 12;
    // Number list data
    NumberListData number_list_data = 13;
    // Legend data
    LegendData legend_data = 14;
  }
}

// Number data
message NumberData {
  // The field_key of the big number
  string field_key = 1;
  // The type of the field
  Field.Type field_type = 2;
  // The value of the big number
  Value value = 3;
  // The previous value of the big number
  optional Value previous_value = 4;
  // The label of the big number
  optional string label = 5;
  // The description of the big number
  optional string description = 6;
  // The trend of the field
  Trend trend = 7;
  // The insight information
  optional MoeGoInsight insight = 8;
  // Style config of the line
  optional StyleConfig style = 9;
  // Drill config for the field
  optional DrillConfig drill_config = 10;
  // Linkage config for the field
  optional LinkageConfig linkage_config = 11;
}

// rank unit
message RankUnit {
  // avatar url
  optional string avatar_url = 1;
  // label
  string label = 2;
  // main_metric
  Value main_metric = 3;
  // secondary_metric
  optional Value secondary_metric = 4;
  // Style config of the line
  optional StyleConfig style = 5;
  // main metric data
  NumberData main_metric_data = 6;
  // secondary metric data
  optional NumberData secondary_metric_data = 7;
}

// A MoeGo insight
message MoeGoInsight {
  // The title of the insight
  string title = 1;
  // The description of the insight:
  string description_pattern = 2;
  // The values of the insight description
  repeated string values = 3;
}

// Bar data
message BarData {
  // The groups of the bar data
  repeated BarGroupData groups = 1;
}

// Bar group data
message BarGroupData {
  // The name of the group
  string group_name = 1;
  // numbers
  repeated moego.models.reporting.v2.NumberData numbers = 2;
  // Style config of the line
  optional StyleConfig style = 3;
}

// Pie data
message PieData {
  // The title of the pie data
  repeated moego.models.reporting.v2.NumberData numbers = 1;
  // The total of the pie data
  optional moego.models.reporting.v2.NumberData total = 2;
}

// Funnel data
message FunnelData {
  // The title of the funnel data
  repeated moego.models.reporting.v2.NumberData numbers = 1;
}

// Table data
message TableData {
  // The title of the table
  repeated TableRowData rows = 1;
  // Fields of the table, fields from TableMeta or Dynamic generated fields
  repeated Field fields = 2;
}

// Table row data
message TableRowData {
  // The data of one row
  map<string, NumberData> data = 1;
  // drill config of one row
  optional DrillConfig drill_config = 2;
  // sub data for multi dimension table
  optional TableData sub_data = 3;
}

// A rank data
message RankData {
  // The list of the rank data
  repeated RankUnit ranks = 1;
}

// Step unit of Procedure number data
message StepUnitData {
  // data lead to step, if it's the first one, lead data might be empty
  optional NumberData lead_data = 1;
  // step data
  NumberData step_data = 2;
}

// Procedure number data
message ProcedureNumberData {
  // step data list
  repeated StepUnitData steps = 1;
}

// Number array data
message NumberArrayData {
  // The numbers of the number array data
  repeated NumberData numbers = 1;
  // Title to display
  string title = 2;
  // Description or tips
  string description = 3;
  // Style config
  StyleConfig style = 4;
}

// Line group data, contributed to the line chart
message LineGroupData {
  // The name of the group
  string group_name = 1;
  // The line data
  repeated NumberData numbers = 2;
  // Style config of the line
  optional StyleConfig style = 3;
}

// Line chart data
message LineData {
  // Group list: One group represents a line in the line chart
  repeated LineGroupData groups = 1;
}

// Odometer data
message OdometerData {
  // odometer's right value
  NumberData measure_metric = 1;
  // odometer's left value
  NumberData indicator_metric = 2;
  // odometer's icon
  NumberData main_icon = 3;
  // odometer's center value
  NumberData main_metric = 4;
  // character's first name
  NumberData first_name = 5;
  // character's last name
  NumberData last_name = 6;
}

// Rating star data
message RatingStarData {
  // rating star value
  NumberData rating_star = 1;
}

// Number list data, same as number array, display as rows
message NumberListData {
  // Data of the number list
  repeated NumberData numbers = 1;
}

// Legend data
message LegendData {
  // Number list
  repeated moego.models.reporting.v2.NumberData numbers = 1;
  // Total value of legend data
  optional moego.models.reporting.v2.NumberData total = 2;
}
