syntax = "proto3";

package moego.models.usps.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/usps/v1;uspspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.usps.v1";

// AddressInfo
message AddressInfo {
  // The two-character state code.
  string state = 1;
  // This is the city name of the address.
  string city = 2;
  // This is the abbreviation of the city name for the address.
  optional string city_abbreviation = 3;
  // The number of a building along with the name of the road or street on which it is located.
  optional string street_address = 4;
  // This is the abbreviation of the primary street address line for the address.
  optional string street_address_abbreviation = 5;
  // The secondary unit designator, such as apartment(APT) or suite(STE) number, defining the exact location of the address within a building.
  // For more information please see Postal Explorer.
  optional string secondary_address = 6;
  // This is the 5-digit ZIP code.
  optional string zipcode = 7;
  // This is the 4-digit component of the ZIP+4 code.
  // Using the correct ZIP+4 reduces the number of times your mail is handled and can decrease the chance of a misdelivery or error.
  optional string zip_plus4 = 8;
  // An area, sector, or residential development within a geographic area (typically used for addresses in Puerto Rico).
  optional string urbanization = 9;
}

// AddressAdditionalInfo
message AddressAdditionalInfo {
  // A specific set of digits between 00 and 99 is assigned to every address that is combined with the ZIP + 4® Code to provide a unique identifier for every delivery address.
  // A street address does not necessarily represent a single delivery point because a street address such as one for an apartment building may have several delivery points.
  optional string delivery_point = 1;
  // Central Delivery is for all business office buildings and/or industrial/professional parks.
  // This may include call windows, horizontal locked mail receptacles, and cluster box units.
  optional bool central_delivery_point = 2; // e.g. "Y", "N"
  // This is the carrier route code (values unspecified).
  optional string carrier_route = 3;
  // Enum: "Y" "D" "S" "N"
  // The DPV Confirmation Indicator is the primary method used by the USPS® to determine whether an address is considered deliverable or undeliverable.
  //   - Y 'Address was DPV confirmed for both primary and (if present) secondary numbers.'
  //   - D 'Address was DPV confirmed for the primary number only, and the secondary number information was missing.'
  //   - S 'Address was DPV confirmed for the primary number only, and the secondary number information was present but not confirmed.'
  //   - N 'Both primary and (if present) secondary number information failed to DPV confirm.'
  optional string dpv_confirmation = 4;
  // Indicates if the location is a Commercial Mail Receiving Agency (CMRA).
  // see: https://faq.usps.com/s/article/Commercial-Mail-Receiving-Agency-CMRA
  optional bool dpv_cmra = 5;
  // Indicates whether this is a business address.
  optional bool business = 6;
  // Indicates whether the location designated by the address is occupied.
  optional bool vacant = 7;
}

// AddressCorrection
// Codes that indicate how to improve the address input to get a better match.
// Code 32 will indicate "Default address: The address you entered was found but more information is needed (such as an apartment, suite, or box number."
// The recommended change would be to add additional information, such as an apartment, suite, or box number, to match to a specific address.
// Code 22 will indicate "Multiple addresses were found for the information you entered, and no default exists."
// The address could not be resolved as entered and more information would be needed to identify the address.
message AddressCorrection {
  // The code corresponding to the address correction.
  optional string code = 1;
  // This is the description of the address correction.
  optional string text = 2;
}

// AddressMatch
// Codes that indicate if an address is an exact match.
// Code 31 will be returned "Single Response - exact match" indicating that the address was correctly matched to a ZIP+4 record.
message AddressMatch {
  // code
  optional string code = 1;
  // text
  optional string text = 2;
}
