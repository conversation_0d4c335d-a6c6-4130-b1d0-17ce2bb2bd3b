syntax = "proto3";

package moego.models.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/utils/v1/status_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.agreement.v1";

// AgreementRecordModel
// created according to AgreementModel, used for customer signing.
message AgreementRecordModel {
  // agreement record id
  int64 id = 1;
  // agreement record uuid
  string uuid = 2;
  // agreement id
  optional int64 agreement_id = 3;
  // business id
  int64 business_id = 4;
  // customer id
  int64 customer_id = 5;
  // associated target id
  optional int64 target_id = 6;
  // status: normal, deleted
  moego.utils.v1.Status status = 7;
  // associated services type, see the enum definition in ServiceType
  int32 service_types = 8;
  // signed status: unsigned, signed
  moego.models.agreement.v1.SignedStatus signed_status = 9;
  // signed type, see definition in SignedType
  optional moego.models.agreement.v1.SignedType signed_type = 10;
  // source type, see definition in SourceType
  moego.models.agreement.v1.SourceType source_type = 11;
  // agreement record link
  string agreement_link = 12;
  // agreement title
  string agreement_title = 13;
  // agreement content
  optional string agreement_content = 14;
  // uploaded files: json array
  repeated string upload_files = 15;
  // customer signature
  optional string signature = 16;
  // signed time: milliseconds
  optional int64 signed_time = 17;
  // create time: milliseconds
  int64 create_time = 18;
  // update time: milliseconds
  int64 update_time = 19;
  // company id
  int64 company_id = 20;
  // inputs
  repeated string inputs = 21;
}

// simplified version of AgreementRecordModel for return list
message AgreementRecordSimpleView {
  // agreement record id
  int64 id = 1;
  // agreement record uuid
  string uuid = 2;
  // agreement id
  optional int64 agreement_id = 3;
  // business id
  int64 business_id = 4;
  // customer id
  int64 customer_id = 5;
  // associated target id
  optional int64 target_id = 6;
  // status: normal, deleted
  moego.utils.v1.Status status = 7;
  // associated services type, see the enum definition in ServiceType
  int32 service_types = 8;
  // signed status: unsigned, signed
  moego.models.agreement.v1.SignedStatus signed_status = 9;
  // signed type, see definition in SignedType
  optional moego.models.agreement.v1.SignedType signed_type = 10;
  // source type, see definition in SourceType
  moego.models.agreement.v1.SourceType source_type = 11;
  // agreement record link
  string agreement_link = 12;
  // agreement title
  string agreement_title = 13;
  // signed time: milliseconds
  optional int64 signed_time = 14;
  // create time: milliseconds
  int64 create_time = 15;
  // update time: milliseconds
  int64 update_time = 16;
  // customer signature
  optional string signature = 17;
  // agreement content
  optional string agreement_content = 18;
  // company id
  int64 company_id = 19;
  // inputs
  repeated string inputs = 20;
}

// agreement list with recent signed record
message AgreementWithRecentRecordsView {
  // business id
  int64 business_id = 1;
  // agreement id
  int64 agreement_id = 2;
  // agreement title
  string agreement_title = 3;
  // recent signed time
  int64 recent_signed_time = 4;
  // agreement record view list
  repeated AgreementRecordSimpleView agreement_record_simple_view = 5;
  // company id
  int64 company_id = 6;
}

// AgreementWithRecentRecordsView list
message AgreementWithRecentRecordsViewList {
  // agreement list with recent signed record
  repeated AgreementWithRecentRecordsView values = 1;
}

// unsigned agreement record list
message UnsignedAgreementRecordListView {
  // agreement record view list
  repeated AgreementRecordSimpleView agreement_record_simple_view = 1;
}
