syntax = "proto3";

package moego.models.map.v1;

import "google/geo/type/viewport.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/latlng.proto";
import "google/type/localized_text.proto";
import "google/type/money.proto";
import "moego/models/map/v1/map_models.proto";
import "moego/models/map/v1/route_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.map.v1";

// Waypoint
message Waypoint {
  // Different ways to represent a location.
  oneof location_type {
    // A point specified using geographic coordinates
    google.type.LatLng coordinate = 1;
    // address source, e.g. the Google Map Place ID or a plus code or a custom address id.
    moego.models.map.v1.AddressSource address_source = 2;
    // Human readable address.
    string address = 3;
  }

  // Marks this waypoint as a milestone rather a stopping point.
  // For each non-via waypoint in the request,
  // the response appends an entry to the [Route.legs] array to provide the details for stopovers on that leg of the trip.
  // Set this value to true when you want the route to pass through this waypoint without stopping over.
  // Via waypoints don't cause an entry to be added to the `legs` array, but they do route the journey through the waypoint.
  // You can only set this value on waypoints that are intermediates.
  // The request fails if you set this field on terminal waypoints.
  // If [QueryRoutesRequest.optimize_waypoint_order] is set to true then this field cannot be set to true; otherwise, the request fails.
  optional bool via = 4;

  // Indicates that the waypoint is meant for vehicles to stop at, where the intention is to either pickup or drop-off.
  // When you set this value, the calculated route won't include non-`via` waypoints on roads that are unsuitable for pickup and drop-off.
  // This option works only for `DRIVING` and `TWO_WHEELER` travel modes, and when the `location_type` is [google.type.LatLng].
  optional bool vehicle_stopover = 5;

  // Indicates that the location of this waypoint is meant to have a preference for the vehicle to stop at a particular side of road.
  // When you set this value, the route will pass through the location so that
  // the vehicle can stop at the side of road that the location is biased towards from the center of the road.
  // This option works only for 'DRIVING' and 'TWO_WHEELER'.
  optional bool side_of_road = 6;
}

// Route
message Route {
  // The travel distance of the route, in meters.
  optional int32 distance = 1;
  // The length of time needed to navigate the route.
  optional google.protobuf.Duration duration = 2;
  // The overall route polyline. This polyline is the combined polyline of all `legs`.
  optional Polyline polyline = 3;
  // Labels for the `Route` that are useful to identify specific properties of the route to compare against others.
  repeated moego.models.map.v1.RouteLabel labels = 4;
  // if you set [QueryRoutesRequest.optimize_waypoint_order] to true,
  // this field contains the optimized ordering of intermediate waypoints. Otherwise, this field is empty.
  repeated int32 optimized_point_index = 5;
  // a collection of legs (path segments between waypoints) that make up the route.
  // each leg corresponds to the trip between two non-`via` [Waypoints].
  repeated RouteLeg legs = 6;
  // An array of warnings to show when displaying the route.
  repeated string warnings = 7;
  // A description of the route.
  optional string description = 8;
  // The viewport bounding box of the polyline.
  optional google.geo.type.Viewport viewport = 9;
  // Additional information about the route.
  optional RouteTravelAdvisory travel_advisory = 10;
  // A web-safe, base64-encoded route token that can be passed to the Navigation SDK,
  // that allows the Navigation SDK to reconstruct the route during navigation,
  // and, in the event of rerouting, honor the original intention
  // when you created the route by calling ComputeRoutes. Customers should treat this token as an opaque blob.
  // It is not meant for reading or mutating.
  // NOTE: `Route.route_token` is only available for requests that have set `QueryRoutesRequest.route_preference` to `ROUTE_PREFERENCE_AWARE` or `ROUTE_PREFERENCE_OPTIMAL`.
  // `Route.route_token` is not supported for requests that have Via waypoints.
  optional string route_token = 11;
  // Text representations of properties of the `RouteLegStep`.
  optional LocalizedValues localized_values = 12;
}

// RouteLeg
// Contains a segment between non-`via` waypoints.
message RouteLeg {
  // The travel distance of the route leg, in meters.
  optional int32 distance = 1;
  // The length of time needed to navigate the leg.
  optional google.protobuf.Duration duration = 2;
  // The overall polyline for this leg that includes each `step`'s polyline.
  optional Polyline polyline = 3;
  // The start location of this leg. This location might be different from the provided `origin`.
  // For example, when the provided `origin` is not near a road, this is a point on the road.
  optional google.type.LatLng start_location = 4;
  // The end location of this leg. This location might be different from the provided `destination`.
  // For example, when the provided `destination` is not near a road, this is a point on the road.
  optional google.type.LatLng end_location = 5;
  // An array of steps denoting segments within this leg. Each step represents one navigation instruction.
  repeated RouteLegStep steps = 6;
  // Contains the additional information that the user should be informed about, such as possible traffic zone restrictions, on a route leg.
  optional RouteTravelAdvisory travel_advisory = 7;
  // Overview information about the steps in this `RouteLeg`. This field is only populated for TRANSIT routes.
  optional StepsOverview steps_overview = 8;
  // Text representations of properties of the `RouteLeg`.
  optional LocalizedValues localized_values = 9;
}

// RouteLegStep
// Contains a segment of a [RouteLeg].
// A step corresponds to a single navigation instruction. Route legs are made up of steps.
message RouteLegStep {
  // The travel distance of this step, in meters.
  // In some circumstances, this field might not have a value.
  optional int32 distance = 1;
  // The duration of travel through this step without taking traffic conditions into consideration.
  // In some circumstances, this field might not have a value.
  optional google.protobuf.Duration duration = 2;
  // The polyline associated with this step.
  optional Polyline polyline = 3;
  // The start location of this step.
  optional google.type.LatLng start_location = 4;
  // The end location of this step.
  optional google.type.LatLng end_location = 5;
  // The travel mode used for this step.
  optional moego.models.map.v1.TravelMode travel_mode = 6;
  // Navigation instructions.
  optional NavigationInstruction navigation_instruction = 7;
  // Contains the additional information that the user should be informed
  // about, such as possible traffic zone restrictions, on a leg step.
  optional RouteTravelAdvisory travel_advisory = 8;
  // Details pertaining to this step if the travel mode is `TRANSIT`.
  optional RouteLegStepTransitDetails transit_details = 9;
  // Text representations of properties of the `RouteLegStep`.
  optional LocalizedValues localized_values = 10;
}

// RouteModifiers
// Encapsulates a set of optional conditions to satisfy when calculating the routes.
message RouteModifiers {
  // if true, avoids highways where reasonable, giving preference to routes not containing highways.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
  optional bool avoid_tolls = 1;
  // if true, avoids toll roads where reasonable, giving preference to routes not containing toll roads.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
  optional bool avoid_highways = 2;
  // if true, avoids ferries where reasonable, giving preference to routes not containing ferries.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
  optional bool avoid_ferries = 3;
  // if true, avoids navigating indoors where reasonable, giving preference to routes not containing indoor navigation.
  // when specified, the travel_mode must be WALKING, otherwise the value will be ignored.
  optional bool avoid_indoor = 4;
  // Specifies the vehicle information.
  // specifies the vehicle's emission type.
  // when specified, the travel_mode must be DRIVING, otherwise the value will be ignored.
  optional moego.models.map.v1.VehicleEmissionType emission_type = 5;
  // specifies the information about toll passes.
  // If toll passes are provided, the API tries to return the pass price.
  // If toll passes are not provided, the API treats the toll pass as unknown and tries to return the cash price.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the value will be ignored.
  // list of supported toll passes see: https://developers.google.com/maps/documentation/routes/reference/rest/v2/RouteModifiers#tollpass
  repeated string toll_passes = 6;
}

// Encapsulates an encoded polyline.
message Polyline {
  // Encapsulates the type of polyline. Defaults to encoded_polyline.
  oneof polyline_type {
    // The string encoding of the polyline using the [polyline encoding algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm)
    string encoded_polyline = 1;

    // Specifies a polyline using the [GeoJSON LineString format](https://tools.ietf.org/html/rfc7946#section-3.1.4)
    google.protobuf.Struct geo_json_linestring = 2;
  }
}

// TollInfo
// Encapsulates toll information on a `Route` or on a `RouteLeg`.
message TollInfo {
  // The monetary amount of tolls for the corresponding `Route` or `RouteLeg`.
  // This list contains a money amount for each currency that is expected to be charged by the toll stations.
  // Typically this list will contain only one item for routes with tolls in one currency.
  // For international trips, this list may contain multiple items to reflect tolls in different currencies.
  repeated google.type.Money estimated_price = 1;
}

// TransitPreferences
message TransitPreferences {
  // a set of travel modes to use when getting a TRANSIT route. Defaults to all supported modes of travel.
  repeated moego.models.map.v1.TransitTravelMode transit_mode = 1;
  // a routing preference that, when specified, influences the TRANSIT route returned.
  optional moego.models.map.v1.TransitRoutePreference routing_preference = 2;
}

// TrafficDensityIndicator
// Traffic density indicator on a contiguous segment of a polyline or path.
// Given a path with points P_0, P_1, ... , P_N (zero-based index),
// the TrafficDensityIndicator defines an interval and describes its traffic using the following categories.
message TrafficDensityIndicator {
  // The starting index of this interval in the polyline.
  optional int32 start_index = 1;
  // The ending index of this interval in the polyline.
  optional int32 end_index = 2;
  // SpeedType
  oneof speed_type {
    // Traffic speed in this interval.
    TrafficCondition speed = 3;
  }
}

// RouteTravelAdvisory
// Contains the additional information that the user should be informed about, such as possible traffic zone restrictions.
message RouteTravelAdvisory {
  // Speed reading intervals detailing traffic density. Applicable in case of
  // `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
  // The intervals cover the entire polyline of the route without overlap.
  // The start point of a specified interval is the same as the end point of the
  // preceding interval.
  //
  // Example:
  //
  //     polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
  //     speed_reading_intervals: [A,C), [C,D), [D,G).
  repeated TrafficDensityIndicator speed_reading_intervals = 1;
  // Contains information about tolls on the route. This field is only populated if tolls are expected on the route.
  // If this field is set, but the estimatedPrice subfield is not populated, then the route contains tolls, but the estimated price is unknown.
  // If this field is not set, then there are no tolls expected on the route.
  optional TollInfo toll_info = 2;
  // The predicted fuel consumption in microliters.
  optional int64 fuel_consumption = 3;
  // Returned route may have restrictions that are not suitable for requested travel mode or route modifiers.
  optional bool route_restrictions_partially_ignored = 4;
  // If present, contains the total fare or ticket costs on this route
  // This property is only returned for `TRANSIT` requests and only for routes where fare information is available for all transit steps.
  optional google.type.Money transit_fare = 5;
}

// NavigationInstruction
// Encapsulates navigation instructions for a [RouteLegStep]
message NavigationInstruction {
  // Encapsulates the navigation instructions for the current step (e.g., turn left, merge, straight, etc.).
  // This field determines which icon to display.
  // see: https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRoutes#maneuver
  optional string maneuver = 1;
  // Instructions for navigating this step.
  optional string instruction = 2;
}

// StepsOverview
// Provides overview information about a list of `RouteLegStep`s.
message StepsOverview {
  // Summarized information about different multi-modal segments of the `RouteLeg.steps`.
  // This field is not populated if the `RouteLeg` does not contain any multi-modal segments in the steps.
  repeated MultiModalSegment multi_modal_segments = 1;
}

// MultiModalSegment
// Provides summarized information about different multi-modal segments of the `RouteLeg.steps`.
// A multi-modal segment is defined as one or more contiguous `RouteLegStep` that have the same `TravelMode`.
// This field is not populated if the `RouteLeg` does not contain any multi-modal segments in the steps.
message MultiModalSegment {
  // The corresponding `RouteLegStep` index that is the start of a multi-modal segment.
  optional int32 start_index = 1;
  // The corresponding `RouteLegStep` index that is the end of a multi-modal segment.
  optional int32 end_index = 2;
  // NavigationInstruction for the multi-modal segment.
  optional NavigationInstruction navigation_instruction = 3;
  // The travel mode of the multi-modal segment.
  optional moego.models.map.v1.TravelMode travel_mode = 4;
}

// RouteLegStepTransitDetails
// Additional information for the `RouteLegStep` related to `TRANSIT` routes.
message RouteLegStepTransitDetails {
  // Localized descriptions of values for RouteTransitDetails.
  message LocalizedValuesType {
    // Time in its formatted text representation with a corresponding time zone.
    optional LocalizedTime arrival_time = 1;

    // Time in its formatted text representation with a corresponding time zone.
    optional LocalizedTime departure_time = 2;
  }

  // Specifies the direction in which to travel on this line as marked on the vehicle or at the departure stop.
  // The direction is often the terminus station.
  optional string headsign = 1;
  // Specifies the expected time as a duration between departures from the same stop at this time.
  // For example, with a headway seconds value of 600, you would expect a ten minute wait if you should miss your bus.
  optional google.protobuf.Duration headway = 2;
  // The number of stops from the departure to the arrival stop. This count
  // includes the arrival stop, but excludes the departure stop. For example, if
  // your route leaves from Stop A, passes through stops B and C, and arrives at
  // stop D, stop_count will return 3.
  optional int32 stop_count = 3;
  // The text that appears in schedules and sign boards to identify a transit
  // trip to passengers. The text should uniquely identify a trip within a
  // service day. For example, "538" is the `trip_short_text` of the Amtrak
  // train that leaves San Jose, CA at 15:10 on weekdays to Sacramento, CA.
  optional string trip_name = 4;
  // Information about the arrival and departure stops for the step.
  optional TransitStopDetails transit_stop = 5;
  // Information about the transit line used in this step.
  optional TransitLine transit_line = 6;
  // Text representations of properties of the `RouteLegStepTransitDetails`.
  optional LocalizedValuesType localized_values = 7;
}

// TransitStopDetails
// Details about the transit stops for the `RouteLegStep`
message TransitStopDetails {
  // The estimated time of departure for the step.
  optional google.protobuf.Timestamp departure_time = 1;
  // Information about the departure stop for the step.
  optional TransitStop departure_stop = 2;

  // The estimated time of arrival for the step.
  optional google.protobuf.Timestamp arrival_time = 3;
  // Information about the arrival stop for the step.
  optional TransitStop arrival_stop = 4;
}

// TransitStop
// Information about a transit stop.
message TransitStop {
  // The name of the transit stop.
  optional string name = 1;
  // The location of the stop expressed in latitude/longitude coordinates.
  optional google.type.LatLng coordinate = 2;
}

// TransitLine
// Contains information about the transit line used in this step.
message TransitLine {
  // The full name of this transit line, For example, "8 Avenue Local".
  optional string name = 1;
  // The short name of this transit line. This name will normally be a line
  // number, such as "M7" or "355".
  optional string short_name = 2;
  // the URI for this transit line as provided by the transit agency.
  optional string uri = 3;
  // The URI for the icon associated with this line.
  optional string icon_uri = 4;
  // The color commonly used in signage for this line. Represented in
  // hexadecimal.
  optional string color = 5;
  // The color commonly used in text on signage for this line. Represented in
  // hexadecimal.
  optional string text_color = 6;
  // The transit agency (or agencies) that operates this transit line.
  repeated TransitAgency agency = 7;
  // The type of vehicle that operates on this transit line.
  optional TransitVehicle vehicle = 8;
}

// TransitAgency
// A transit agency that operates a transit line.
message TransitAgency {
  // The name of this transit agency.
  optional string name = 1;
  // The transit agency's URI.
  optional string uri = 2;
  // The transit agency's locale-specific formatted phone number.
  optional string phone_number = 3;
}

// TransitVehicle
// Information about a vehicle used in transit routes.
message TransitVehicle {
  // The name of this vehicle, capitalized.
  optional string name = 1;
  // The type of vehicle used.
  // see https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRoutes#transitvehicletype
  optional string type = 2;
  // The URI for an icon associated with this vehicle type.
  optional string icon_uri = 3;
  // The URI for the icon associated with this vehicle type, based on the local transport signage.
  optional string local_icon_uri = 4;
}

// RouteMatrixElement
message RouteMatrixElement {
  // Error status code for this element.
  google.rpc.Status status = 1;
  // Zero-based index of the origin in the request.
  optional int32 origin_index = 2;
  // Zero-based index of the destination in the request.
  optional int32 destination_index = 3;
  // The travel distance of the route, in meters.
  optional int32 distance = 4;
  // The duration from origin to destination
  optional google.protobuf.Duration duration = 5;
  // Additional information about the route. For example: restriction information and toll information
  optional RouteTravelAdvisory travel_advisory = 6;
  // In some cases when the server is not able to compute the route with the given preferences for this particular origin/destination pair,
  // it may fall back to using a different mode of computation. When fallback mode is used, this field contains detailed information about the fallback response.
  // Otherwise this field is unset.
  optional FallbackInfo fallback_info = 7;
  // Text representations of properties of the `RouteMatrixElement`.
  optional LocalizedValues localized_values = 8;
}

// FallbackInfo
// Information related to how and why a fallback result was used.
// If this field is set, then it means the server used a different routing mode from your preferred mode as fallback.
message FallbackInfo {
  // Routing mode used for the response.
  // If fallback was triggered, the mode may be different from routing preference set in the original client request.
  optional moego.models.map.v1.FallbackRoutingMode routing_mode = 1;
  // The reason why fallback response was used instead of the original response.
  // This field is only populated when the fallback mode is triggered and the fallback response is returned.
  optional moego.models.map.v1.FallbackReason reason = 2;
}

// LocalizedValues
// Text representations of certain properties.
message LocalizedValues {
  // Travel distance represented in text form.
  optional google.type.LocalizedText distance = 1;
  // Duration represented in text form taking traffic conditions into consideration.
  // Note: If traffic information was not requested, this value is the same value as static_duration.
  optional google.type.LocalizedText duration = 2;
  // Transit fare represented in text form.
  optional google.type.LocalizedText transit_fare = 3;
}

// LocalizedTime
// Localized description of time.
message LocalizedTime {
  // The time specified as a string in a given time zone.
  optional google.type.LocalizedText time = 1;
  // Contains the time zone.
  // The value is the name of the time zone as defined in the [IANA Time Zone Database](http://www.iana.org/time-zones),
  // e.g. "America/New_York".
  optional string time_zone = 2;
}
