syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/appointment_note_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment note create definition
message AppointmentNoteCreateDef {
  // note
  string note = 1 [(validate.rules).string = {max_len: 1048576}];

  // type
  AppointmentNoteType type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Appointment note update definition
message AppointmentNoteUpdateDef {
  // note id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // note
  string note = 2 [(validate.rules).string = {max_len: 1048576}];
}
