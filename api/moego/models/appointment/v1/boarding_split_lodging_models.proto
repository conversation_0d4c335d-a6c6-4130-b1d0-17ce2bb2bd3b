// @since 2025-04-02 17:04:12
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// The BoardingSplitLodging model
message BoardingSplitLodgingModel {
  // the unique id
  int64 id = 1;

  // appointment id
  int64 appointment_id = 2;
  // pet detail id
  int64 pet_detail_id = 3;
  // pet id
  int64 pet_id = 4;

  // selected lodging id
  int64 lodging_id = 5;
  // start date time
  google.protobuf.Timestamp start_date_time = 6;
  // end date time
  google.protobuf.Timestamp end_date_time = 7;
  // price
  google.type.Money price = 8;

  // the create time
  google.protobuf.Timestamp created_at = 9;

  // is applicable
  bool is_applicable = 10;
}
