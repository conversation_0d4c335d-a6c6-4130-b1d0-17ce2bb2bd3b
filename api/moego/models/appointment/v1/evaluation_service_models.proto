syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment evaluation detail model
message EvaluationServiceModel {
  // pet evaluation detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // service id
  int64 service_id = 4;
  // service time, in minutes
  int32 service_time = 5;
  // service price
  double service_price = 6;
  // start time, in minutes
  int32 start_time = 7;
  // end time, in minutes
  int32 end_time = 8;
  // service start date, in yyyy-MM-dd format
  string start_date = 9;
  // service end date, in yyyy-MM-dd format
  string end_date = 10;
  // The staff id responsible for this evaluation service
  optional int64 staff_id = 11;
  // lodging id
  optional int64 lodging_id = 12;
  // order line item id
  int64 order_line_item_id = 13;
}

// evaluation pet detail model client view
message PetEvaluationDetailClientView {
  // pet evaluation detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // service id
  int64 service_id = 4;
  // service time, in minutes
  int32 service_time = 5;
  // service price
  double service_price = 6;
  // start time, in minutes
  int32 start_time = 7;
  // end time, in minutes
  int32 end_time = 8;
  // service start date, in yyyy-MM-dd format
  string start_date = 9;
  // service end date, in yyyy-MM-dd format
  string end_date = 10;
  // The staff id responsible for this evaluation service
  optional int64 staff_id = 11;
  // lodging id
  optional int64 lodging_id = 12;
}
