syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// appointment type
enum AppointmentSource {
  // unspecified
  APPOINTMENT_SOURCE_UNSPECIFIED = 0;
  // web
  WEB = 22018;
  // online booking
  ONLINE_BOOKING = 22168;
  // android
  ANDROID = 17216;
  // ios
  IOS = 17802;
  // auto dm
  AUTO_DM = 23426;
  // google calendar
  GOOGLE_CALENDAR = 19826;
  // open api
  OPEN_API = 23333;
}

// appointment status
enum AppointmentStatus {
  // unspecified
  APPOINTMENT_STATUS_UNSPECIFIED = 0;
  // unconfirmed
  UNCONFIRMED = 1;
  // confirmed
  CONFIRMED = 2;
  // finished
  FINISHED = 3;
  // canceled
  CANCELED = 4;
  // ready to check in
  READY = 5;
  // checked in
  CHECKED_IN = 6;
}

// appointment payment status
enum AppointmentPaymentStatus {
  // unspecified
  APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED = 0;
  // fully paid
  FULLY_PAID = 1;
  // unpaid
  UNPAID = 2;
  // partial paid
  PARTIAL_PAID = 3;
  // deprecated prepaid
  PREPAID = 4;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// appointment book online status
enum AppointmentBookOnlineStatus {
  // not book online
  NOT_BOOK_ONLINE = 0;
  // book online
  BOOK_ONLINE = 1;
}

// appointment type
enum AppointmentType {
  // unspecified
  APPOINTMENT_TYPE_UNSPECIFIED = 0;
  // upcoming appointment
  UPCOMING = 1;
  // history appointment
  PAST = 2;
  // booking request
  PENDING = 3;
}

// AppointmentScheduleType
enum AppointmentScheduleType {
  // unspecified
  APPOINTMENT_SCHEDULE_TYPE_UNSPECIFIED = 0;
  // normal repeat
  NORMAL_REPEAT = 1;
  // smart schedule repeat
  SMART_SCHEDULE_REPEAT = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Appointment canceled by type
enum AppointmentUpdatedBy {
  // by business
  BY_BUSINESS = 0;
  // by customer reply msg
  BY_CUSTOMER_REPLY_MESSAGE = 1;
  // by delete pet
  BY_DELETE_PET = 2;
  // by client portal
  BY_CLIENT_PORTAL = 3;
  // by pet parent app
  BY_PET_PARENT_APP = 4;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// AppointmentNotificationStatus
enum AppointmentNotificationStatus {
  // not sent
  NOTIFICATION_NOT_SENT = 0;
  // success
  NOTIFICATION_SUCCESS = 1;
  // failed
  NOTIFICATION_FAILED = 2;
}

// AppointmentReminderType
enum AppointmentReminderType {
  // unspecified
  APPOINTMENT_REMINDER_TYPE_UNSPECIFIED = 0;
  // message
  MESSAGE = 1;
  // email
  EMAIL = 2;
  // phone call
  PHONE_CALL = 3;
}

// Calendar card type
enum CalendarCardType {
  // unspecified
  CALENDAR_CARD_TYPE_UNSPECIFIED = 0;
  // appointment
  APPOINTMENT = 1;
  // service detail
  // May contain one or more service cards
  SERVICE = 2;
  // multi-staff operation
  // May contain one or more operation cards
  OPERATION = 3;
  // block time
  BLOCK = 4;
  // pending booking request
  BOOKING_REQUEST = 5;
  // service and multi-staff operation
  SERVICE_AND_OPERATION = 6;
}

// AppointmentNoShowStatus
enum AppointmentNoShowStatus {
  // unspecified
  APPOINTMENT_NO_SHOW_STATUS_UNSPECIFIED = 0;
  // no show
  NO_SHOW = 1;
  // not no show
  NOT_NO_SHOW = 2;
}

// AppointmentSortField
enum AppointmentSortField {
  // unspecified
  APPOINTMENT_SORT_FIELD_UNSPECIFIED = 0;
  // start date time
  START_DATE_TIME = 1;
  // end date time
  END_DATE_TIME = 2;
}
