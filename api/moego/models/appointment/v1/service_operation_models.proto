syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment service operation model, for multi-staff work mode
message ServiceOperationModel {
  // service operation id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
  // appointment id
  int64 appointment_id = 4;
  // pet detail id
  int64 grooming_service_id = 5;
  // pet id
  int64 pet_id = 6;
  // staff id
  int64 staff_id = 7;
  // operation name
  string operation_name = 8;
  // start time, in minutes
  int32 start_time = 9;
  // duration, in minutes
  int32 duration = 10;
  // comment
  string comment = 11;
  // price
  double price = 12;
  // price ratio
  double price_ratio = 13;
  // create time
  google.protobuf.Timestamp create_time = 15;
  // update time
  google.protobuf.Timestamp update_time = 16;
}
