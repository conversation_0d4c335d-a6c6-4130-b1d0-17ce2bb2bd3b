// @since 2025-05-21 18:13:31
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// get auto apply service charge Model
message ApplyServiceChargeModel {
  // service charge id
  int64 service_charge_id = 1;
  // apply quantity
  int32 apply_quantity = 2;
  // price
  double price = 3;
  // tax
  int32 tax_id = 4;
}

// calculate service charge
message CalculateServiceChargeParam {
  // pet details
  repeated PetDetail pet_details = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];

  // feeding schedule
  repeated moego.models.appointment.v1.AppointmentPetFeedingScheduleDef feeding_schedule = 2;
  // medication schedule
  repeated moego.models.appointment.v1.AppointmentPetMedicationScheduleDef medication_schedule = 3;

  // calculate service charge pet detail
  message PetDetail {
    // pet detail id
    optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // grooming id
    optional int64 grooming_id = 2 [(validate.rules).int64 = {gt: 0}];
    // pet id
    int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
    // service id
    int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
    // staff id
    optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
    // service type
    models.offering.v1.ServiceType service_type = 6 [(validate.rules).enum = {defined_only: true}];
    // service start date, in yyyy-MM-dd format, for boarding or daycare service
    optional string start_date = 7;
    // service end date, in yyyy-MM-dd format, for boarding or daycare service
    optional string end_date = 8;
    // start time
    optional int32 start_time = 9 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // end time
    optional int32 end_time = 10 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // service item type, different from service type, it includes grooming, boarding, daycare or other services.
    models.offering.v1.ServiceItemType service_item_type = 11;
    // service price
    double service_price = 12;
    // price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
    int32 price_unit = 13;
    // add-on associated service id
    optional int64 associated_service_id = 14;
    // pet detail date type
    optional models.appointment.v1.PetDetailDateType date_type = 15;
    // add-on specific dates, yyyy-MM-dd
    optional string specific_dates = 16;
  }
}
