syntax = "proto3";

package moego.models.event_bus.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for fulfillment cancel event
message FulfillmentCanceledEvent {
  // fulfillment id
  int64 id = 1;
  // Whether to auto refund the order, default is false
  bool auto_refund_order = 2;
  // Cancel reason, use "Fulfillment canceled" if not provided
  optional string reason = 3;
}
