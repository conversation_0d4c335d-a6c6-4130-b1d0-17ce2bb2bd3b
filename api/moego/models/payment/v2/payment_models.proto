syntax = "proto3";

package moego.models.payment.v2;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// User Payment 参与方
// (-- api-linter: core::0123::resource-annotation=disabled
//     aip.dev/not-precedent: Not applicable for MoeGo. --)
message User {
  // 用户 类型
  models.payment.v2.EntityType entity_type = 1;
  // 用户 ID，当 entity_type 为 MoeGo 时，entity_id 为 0
  int64 entity_id = 2;
  // 用户名
  string name = 3;
}

// Payment 实体
message PaymentModel {
  // PaymentStatus 支付状态
  enum PaymentStatus {
    // Unspecified
    PAYMENT_STATUS_UNSPECIFIED = 0;
    // Created，这是初始化状态
    CREATED = 1;
    // ACCEPTED，支付已被受理，在提交支付凭据后就会变成这个状态，无法撤销
    ACCEPTED = 2;
    // Submitted，已向支付渠道提交支付请求
    SUBMITTED = 3;
    // SUCCEEDED，支付成功，这是最终状态
    SUCCEEDED = 4;
    // Cancelled，支付取消，这是最终状态
    CANCELLED = 5;
    // Failed，支付失败，这是最终状态
    FAILED = 6;
    // Authorized，只出现于 pre-auth 场景，表示已授权
    AUTHORIZED = 7;
  }

  // 支付类型
  enum PaymentType {
    // 未指定
    PAYMENT_TYPE_UNSPECIFIED = 0;
    // Standard 支付
    STANDARD = 1;
    // Pre-Auth 支付
    PRE_AUTH = 2;
    // Pre-Pay 兼容老的支付
    PRE_PAY = 3;
  }

  // 退款信息
  message Refund {
    // 退款状态
    enum Status {
      // 未退款
      STATUS_UNSPECIFIED = 0;
      // 部分退款
      PARTIAL_REFUND = 1;
      // 全额退款
      FULL_REFUND = 2;
    }

    // 退款状态
    Status refund_status = 1;
    // 退款总金额
    google.type.Money refund_amount = 2;
    // 退款次数
    int32 refund_count = 3;
  }

  // 支付单据 ID
  int64 id = 1;
  // payer 支付方
  User payer = 2;
  // payee 收款方
  User payee = 3;
  // 调用支付的上层业务系统类型
  ExternalType external_type = 4;
  // 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
  // 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
  string external_id = 5;
  // 支付渠道
  ChannelType channel_type = 6;
  // 渠道支付单据 ID，与 id 一一对应
  string channel_payment_id = 7;
  // 支付金额，创建支付单据时传入的金额
  google.type.Money amount = 8;
  // 支付时关联的 tips 金额
  google.type.Money tips_amount = 9;
  // 手续费
  google.type.Money processing_fee = 10;
  // convenience fee
  google.type.Money convenience_fee = 11;
  // 优惠金额
  google.type.Money discount_amount = 12;
  // 支付状态
  PaymentStatus status = 13;
  // 退款信息
  Refund refund = 14;
  // 支付类型
  PaymentType payment_type = 15;
  // 支付方式类型
  PaymentMethod.MethodType method_type = 16;
  // transaction id
  int64 transaction_id = 17;
  // method id
  int64 method_id = 18;
  // module
  string module = 19;
  // module id
  string module_id = 20;
  // invoice id
  int64 invoice_id = 21;
  // payment method
  PaymentMethod payment_method = 22;
  // display payment method
  string display_payment_method = 23;
}

// payment view
message PaymentView {
  // 支付单据 ID
  int64 id = 1;
  // payer 支付方
  User payer = 2;
  // payee 收款方
  User payee = 3;
  // 调用支付的上层业务系统类型
  ExternalType external_type = 4;
  // 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
  // 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
  string external_id = 5;
  // 支付渠道
  ChannelType channel_type = 6;
  // 渠道支付单据 ID，与 id 一一对应
  string channel_payment_id = 7;
  // 支付金额，创建支付单据时传入的金额
  google.type.Money amount = 8;
  // 支付时关联的 tips 金额
  google.type.Money tips_amount = 9;
  // 手续费
  google.type.Money processing_fee = 10;
  // convenience fee
  google.type.Money convenience_fee = 11;
  // 优惠金额
  google.type.Money discount_amount = 12;
  // 支付类型
  PaymentModel.PaymentType payment_type = 13;
  // 支付方式类型
  PaymentMethod.MethodType method_type = 14;
  // 支付状态
  PaymentModel.PaymentStatus status = 15;
  // payment实体对应的退款
  PaymentModel.Refund refund = 16;
  // 退款列表
  repeated RefundView refund_views = 17;
  // 关联的 payout id
  int64 payout_id = 18;
  // 渠道payout id
  string channel_payout_id = 19;
  // 支付描述
  string description = 20;
  // 剩余可退款金额
  google.type.Money refundable_amount = 21;
  // create time
  google.protobuf.Timestamp create_time = 22;
  // update time
  google.protobuf.Timestamp update_time = 23;
  // 展示的支付方式
  string display_payment_method = 24;
  // module
  string module = 25;
  // module id , e.g. grooming id
  string module_id = 26;
  // paid by
  string paid_by = 27;
  // transaction id
  int64 transaction_id = 28;
  // invoice id
  int64 invoice_id = 29;
}

// PaymentMethod 支付方式，会和 Payment 一一对应
message PaymentMethod {
  // 支付方式类型
  enum MethodType {
    // 未指定
    METHOD_TYPE_UNSPECIFIED = 0;
    // 卡支付
    CARD = 1;
    // 已经存储了的支付方式
    RECURRING_PAYMENT_METHOD = 2;
    // Book entry，记账式支付，不会发生第三方渠道的支付方式，如 Cash、Check 等
    BOOK_ENTRY = 3;
    // terminal
    TERMINAL = 4;
    // Online
    ONLINE = 5;
  }

  // 详细信息
  message Detail {
    // Adyen 的支付信息前端不需要感知，直接从 OnSubmit 事件的 data 中获取，原样透传即可
    message AdyenDetail {
      // OnSubmit 事件的 data
      string detail = 1;
    }

    // stripe 相关的支付详情信息
    message StripeDetail {
      // 比如卡的 token tok_xxxx
      string detail = 1;
      // 在stripe pay online 的场景会传递这个
      optional string payment_intent_id = 2;
    }

    // Card
    message Card {
      // extra
      message Extra {
        // 卡号后四位
        optional string last_four_digits = 1;
        // 卡有效期月份
        optional string expiry_month = 2;
        // 卡有效期年份
        optional string expiry_year = 3;
        // 卡品牌, 如 Visa, MasterCard
        optional string brand = 4;
        // 卡的别名，用于显示
        optional string alias = 5;
        // 是否已经 authorized
        optional bool is_authorized = 6;
        // 持卡人 first name，是存卡时的快照，用于显示
        optional string first_name = 7;
        // 持卡人 last name, 是存卡时的快照，用于显示
        optional string last_name = 8;
        // phone number, 是存卡时的快照，用于显示
        optional string phone_number = 9;
        // email, 是存卡时的快照，用于显示
        optional string email = 10;
        // funding source, credit card 或者 debit card
        optional FundingSource funding_source = 11;
      }

      // 详细信息
      oneof detail {
        // AdyenDetail
        AdyenDetail adyen = 1;
        // stripe detail
        StripeDetail stripe = 6;
      }
      // 卡支付还会有签名
      optional string signature_url = 2;
      // 是否支付时存卡，默认不存
      bool save_card = 3;
      // funding source，不需要前端传
      optional FundingSource funding_source = 4;
      // extra，不需要前端传
      optional Extra extra = 5;
    }

    // ApplePay
    message ApplePay {
      // 详细信息
      oneof detail {
        // AdyenDetail
        AdyenDetail adyen = 1;
        // stripe detail
        StripeDetail stripe = 2;
      }
    }

    // GooglePay
    message GooglePay {
      // 详细信息
      oneof detail {
        // AdyenDetail
        AdyenDetail adyen = 1;
        // stripe detail
        StripeDetail stripe = 2;
      }
    }

    // LegacyReader, 为了兼容当前stripe的模型，后面做完数据迁移，这个message需要删掉
    message LegacyReader {
      // Reader 类型
      models.payment.v2.Terminal.TerminalType reader_type = 1;
      // 渠道的 reader id，渠道在发起支付的时候就已经确定
      string channel_reader_id = 2;
      // Reader 支付也会有签名（会刷卡）
      optional string signature_url = 3;
      // payment intent id; server driven会有第二次的请求
      optional string channel_payment_id = 4;
    }

    // BookEntry 记账式支付
    message BookEntry {
      // Check
      message Check {
        // 支票号
        string check_number = 1;
      }

      // 其他自定义的支付方式
      message Other {
        // 支付方式 id，这个数据当前存储在 moe_business_payment_method，后续考虑迁移
        int64 method_id = 1;
      }

      // 详细信息
      oneof detail {
        // Check
        Check check = 1;
        // Other
        Other other = 2;
      }
      // 支付方式id,这个数据当前存储在business
      int64 method_id = 11;
      // 支付名称
      string method_name = 12;
    }

    // RecurringPaymentMethod 已存储的支付方式
    message RecurringPaymentMethod {
      // payment method id 和 channel recurring id 二选一
      // recurring payment method id
      optional int64 id = 1;
      // 卡支付还会有签名, 当recurring的payment method 是COF的时候需要
      optional string signature_url = 2;
      // 前端不需要传，后端自行根据 id 填充
      optional RecurringPaymentMethodModel recurring_payment_method = 3;
      // stripe cof id, card_xxxxx / pm_xxxxx
      optional string channel_recurring_id = 4;
    }

    // Terminal 终端支付
    message Terminal {
      // terminal id
      int64 id = 1;
      // tips base amount
      google.type.Money tips_base_amount = 2;
      // terminal，前端不需要传，后端自行根据 id 填充
      optional models.payment.v2.Terminal terminal = 3;
    }

    // 详细信息
    oneof detail {
      // Card
      Card card = 1;
      // RecurringPaymentMethod
      RecurringPaymentMethod recurring_payment_method = 2;
      // Book entry
      BookEntry book_entry = 3;
      // terminal
      Terminal terminal = 4;
      // ApplePay
      ApplePay apple_pay = 5;
      // GooglePay
      GooglePay google_pay = 6;
      // legacy reader
      LegacyReader legacy_reader = 7;
    }
  }

  // ID
  int64 id = 1;
  // 对应支付单据 ID, deprecated
  int64 payment_id = 2;
  // 支付方式类型，这里是业务逻辑上的类型，如 Card支付、Online支付、Reader 等
  MethodType method_type = 3;
  // 渠道
  ChannelType channel_type = 4;
  // 渠道侧支付方式 ID
  string channel_payment_method_id = 5;
  // 支付凭证
  Detail detail = 6;
}

// 退款模型
message RefundModel {
  // 退款状态
  enum RefundStatus {
    // Unspecified
    REFUND_STATUS_UNSPECIFIED = 0;
    // Created，这是初始化状态
    CREATED = 1;
    // Submitted，已向支付渠道提交退款请求
    SUBMITTED = 2;
    // SUCCEEDED，退款成功，这是最终状态
    SUCCEEDED = 3;
    // Failed，退款失败，这是最终状态
    FAILED = 4;
  }

  // 退款单据id
  int64 id = 1;
  // 原支付付款方
  models.payment.v2.User payer = 2;
  // 原支付收款方
  models.payment.v2.User payee = 3;
  // 外部调用方类型，如ORDER
  ExternalType external_type = 4;
  // 外部调用方关联id
  string external_id = 5;
  // 支付渠道类型，如Adyen、Stripe
  ChannelType channel_type = 6;
  // 渠道退款id
  string channel_refund_id = 7;
  // 退款金额
  google.type.Money amount = 8;
  // 支付单据id
  int64 payment_id = 9;
  // 退款状态
  RefundStatus status = 10;
  // 退款原因
  string reason = 11;
}

// refund view
message RefundView {
  // 退款单据id
  int64 refund_id = 1;
  // 买家
  User payer = 2;
  // 卖家
  User payee = 3;
  // 外部调用方类型
  ExternalType external_type = 4;
  // 外部调用方关联id
  string external_id = 5;
  // 渠道类型
  ChannelType channel_type = 6;
  // 渠道退款id
  string channel_refund_id = 7;
  // 退款金额
  google.type.Money amount = 8;
  // 支付单据id
  int64 payment_id = 9;
  // 渠道支付id
  string channel_payment_id = 10;
  // 退款状态
  RefundModel.RefundStatus status = 11;
  // 退款原因
  string reason = 12;
  // create time
  google.protobuf.Timestamp create_time = 13;
  // update time
  google.protobuf.Timestamp update_time = 14;
}

// 已存储的支付方式
message RecurringPaymentMethodModel {
  // method type
  enum MethodType {
    // unspecified
    METHOD_TYPE_UNSPECIFIED = 0;
    // card on file
    COF = 1;
  }

  // channel payment method
  message ChannelPaymentMethod {
    // stripe cof
    message StripeCOF {
      // 保存的 method id , pm_xxx 格式
      string recurring_method_id = 1;
    }
    // adyen cof
    message AdyenCOF {
      // adyen cof id
      string recurring_detail_reference = 1;
      // encrypted security code
      string encrypted_security_code = 2;
    }
    // 渠道保存的payment method
    oneof channel_payment_method {
      // StripeCOF
      StripeCOF stripe_cof = 1;
      // AdyenCOF
      AdyenCOF adyen_cof = 2;
    }
  }

  // extra
  message Extra {
    // extra
    oneof extra {
      // CardExtra
      PaymentMethod.Detail.Card.Extra card = 1;
    }
  }

  // id
  int64 id = 1;
  // 渠道类型
  ChannelType channel_type = 2;
  // 用户
  User user = 3;
  // 渠道 customer id
  string channel_customer_id = 4;
  // 渠道保存的payment method
  ChannelPaymentMethod channel_payment_method = 5;
  // 存储的 payment method 类型
  MethodType method_type = 6;
  // extra
  optional Extra extra = 7;
  // 是否primary
  bool is_primary = 8;
  // created at
  google.protobuf.Timestamp created_at = 9;
}

// recurring payment method view
message RecurringPaymentMethodView {
  // id
  int64 id = 1;
  // 渠道类型
  ChannelType channel_type = 2;
  // 用户
  User user = 3;
  // 渠道 customer id
  string channel_customer_id = 4;
  // 渠道保存的payment method
  RecurringPaymentMethodModel.ChannelPaymentMethod channel_payment_method = 5;
  // 存储的 payment method 类型
  RecurringPaymentMethodModel.MethodType method_type = 6;
  // 是否primary
  bool is_primary = 7;
  // extra
  RecurringPaymentMethodModel.Extra extra = 8;
  // created at
  google.protobuf.Timestamp created_at = 9;
}

// transaction model
message PaymentTransactionModel {
  // TransactionStatus
  enum TransactionStatus {
    // Unspecified
    TRANSACTION_STATUS_UNSPECIFIED = 0;
    // Created，这是初始化状态
    CREATED = 1;
    // ACCEPTED，支付已被受理，在提交支付凭据后就会变成这个状态，无法撤销
    ACCEPTED = 2;
    // Submitted，已向支付渠道提交支付请求
    SUBMITTED = 3;
    // SUCCEEDED，支付成功，这是最终状态
    SUCCEEDED = 4;
    // Cancelled，支付取消，这是最终状态
    CANCELLED = 5;
    // Failed，支付失败，这是最终状态
    FAILED = 6;
    // Authorized，只出现于 pre-auth 场景，表示已授权
    AUTHORIZED = 7;
  }

  // 支付单据 ID
  int64 id = 1;
  // payer 支付方
  User payer = 2;
  // payee 收款方
  User payee = 3;
  // 调用支付的上层业务系统类型
  ExternalType external_type = 4;
  // 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
  // 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
  string external_id = 5;
  // 支付渠道
  ChannelType channel_type = 6;
  // 渠道支付单据 ID，与 id 一一对应
  string channel_transaction_id = 7;
  // 支付金额，创建支付单据时传入的金额
  google.type.Money amount = 8;
  // 支付时关联的 tips 金额
  google.type.Money tips_amount = 9;
  // 手续费
  google.type.Money processing_fee = 10;
  // convenience fee
  google.type.Money convenience_fee = 11;
  // 支付状态
  TransactionStatus status = 13;
  // 交易创建时间
  google.protobuf.Timestamp create_time = 14;
  // 交易更新时间
  google.protobuf.Timestamp update_time = 15;
  // company id
  int64 company_id = 16;
  // method type
  PaymentMethod.MethodType method_type = 17;
  // payment method
  PaymentMethod.Detail method_detail = 18;
  // method id
  int64 method_id = 19;
}

// transaction view
message PaymentTransactionView {
  // 支付单据 ID
  int64 id = 1;
  // payer 支付方
  User payer = 2;
  // payee 收款方
  User payee = 3;
  // 支付时的备注，一般是client name
  string paid_by = 4;
  // 调用支付的上层业务系统类型
  ExternalType external_type = 5;
  // 调用支付的上层业务系统内单据 ID，用于关联支付单据，与 id 一一对应
  // 支付系统会将此字段作为幂等 key，上层业务系统需要保证此字段的唯一性
  string external_id = 6;
  // 支付渠道
  ChannelType channel_type = 7;
  // 渠道支付单据 ID，与 id 一一对应
  string channel_transaction_id = 8;
  // 支付金额，创建支付单据时传入的金额
  google.type.Money amount = 9;
  // 支付时关联的 tips 金额
  google.type.Money tips_amount = 10;
  // 手续费
  google.type.Money processing_fee = 11;
  // convenience fee
  google.type.Money convenience_fee = 12;
  // 支付状态
  PaymentTransactionModel.TransactionStatus status = 13;
  // 交易创建时间
  google.protobuf.Timestamp create_time = 14;
  // 交易更新时间
  google.protobuf.Timestamp update_time = 15;
  // 展示的支付方式
  string transaction_method = 16;
  // module
  string module = 17;
  // module id , e.g. grooming id
  string module_id = 18;
  // transaction 描述
  string description = 19;
  // transaction 关联的 payment 列表
  repeated PaymentView payment_views = 20;
}

// Terminal
message Terminal {
  // Terminal 类型
  enum TerminalType {
    // Unspecified
    TERMINAL_TYPE_UNSPECIFIED = 0;
    // Smart reader
    SMART_READER = 1;
    // BT reader
    BLUETOOTH_READER = 2;
    // Tap-to-pay reader
    TAP_TO_PAY_READER = 3;
  }

  // Terminal 状态
  enum State {
    // Unspecified
    STATE_UNSPECIFIED = 0;
    // Idle
    IDLE = 1;
    // Busy
    BUSY = 2;
  }

  // terminal id
  int64 id = 1;
  // reader type
  TerminalType terminal_type = 2;
  // channel type
  ChannelType channel_type = 3;
  // 渠道reader id
  string channel_terminal_id = 4;
  // state
  State state = 5;
  // transaction id
  int64 last_transaction_id = 6;
}

// terminal view
message TerminalView {
  // terminal id
  int64 id = 1;
  // terminal type
  Terminal.TerminalType terminal_type = 2;
  // channel type
  ChannelType channel_type = 3;
  // 渠道terminal id
  string channel_terminal_id = 4;
  // state
  Terminal.State state = 5;
  // transaction id
  int64 last_transaction_id = 6;
}

// stripe payment intent, pay online 需要提前获取
message StripeChannelPayment {
  // payment intent id
  string payment_intent_id = 1;
  // payment intent secret
  string payment_intent_secret = 2;
}

// channel payment
message ChannelPayment {
  // channel payment
  oneof channel_payment {
    // stripe channel payment
    StripeChannelPayment stripe_channel_payment = 1;
  }
  // channel type
  models.payment.v2.ChannelType channel_type = 11;
}

// 支付费率
message FeeRate {
  // 费率百分比，注意使用时需要除以 10000，如 334 表示 3.34%
  uint32 rate_basis_points = 1;
  // fixed amount
  google.type.Money fixed_amount = 2;
}

// payment setting
message PaymentSetting {
  // convenience fee 配置
  message ConvenienceFeeConfig {
    // 计算方式
    enum CalculationMethod {
      // Unspecified
      CALCULATION_METHOD_UNSPECIFIED = 0;
      // full amount，会保证商家入账为 full amount
      // 计算公式为： $ cvf = (amount + fixed_fee) / (1 - rate) - amount $
      // 这样会使得手续费为 $ processing_fee = (amount + cvf) * rate + fixed_fee $
      // 这种方式下，商家入账为 $ amount + cvf - processing_fee = amount $
      AMOUNT_FULL = 1;
      // 仅进行一轮计算，只添加最初值的 processing fee 作为 cvf
      // $ cvf = amount * rate + fixed_fee $
      AMOUNT_ONE_TIME = 2;
    }

    // 是否启用 convenience fee
    bool enabled = 1;
    // agreement signature url
    string agreement_signature_url = 2;
    // agreement signed time
    google.protobuf.Timestamp agreement_signed_time = 3;
    // fee name
    string fee_name = 4;
    // fee rate for online
    FeeRate online_fee_rate = 5;
    // fee rate for terminal
    FeeRate terminal_fee_rate = 6;
    // 计算方式
    CalculationMethod calculation_method = 7;
    // exclude debit card
    bool exclude_debit_card = 8;
  }

  // tips setting
  message TipsConfig {
    // tips option，表示 tipping 时的金额选择配置
    message TipsOption {
      // tips calculation method
      enum CalculationMethod {
        // Unspecified
        CALCULATION_METHOD_UNSPECIFIED = 0;
        // By percentage
        BY_PERCENTAGE = 1;
        // By amount
        BY_AMOUNT = 2;
      }

      // tips position
      enum Position {
        // Unspecified
        POSITION_UNSPECIFIED = 0;
        // Top
        TOP = 1;
        // Medium
        MEDIUM = 2;
        // Bottom
        BOTTOM = 3;
      }

      // tips percentage
      // 注意使用时需要除以 10000，如 2000 表示 20%
      message Percentage {
        // top
        uint32 top = 1;
        // medium
        uint32 medium = 2;
        // bottom
        uint32 bottom = 3;
      }
      // tips amount
      message Amount {
        // top
        google.type.Money top = 1;
        // medium
        google.type.Money medium = 2;
        // bottom
        google.type.Money bottom = 3;
      }

      // tips option
      oneof tips_option {
        // Percentage
        Percentage percentage = 1;
        // FixedAmount
        Amount amount = 2;
      }

      // calculation method
      CalculationMethod calculation_method = 3;
      // preferred tips option
      Position preferred_option_position = 4;
    }

    // tips option
    TipsOption tips_option = 1;
    // smart tips option
    TipsOption smart_tips_option = 2;
    // if less than amount, then use this tips option
    google.type.Money smart_tips_threshold = 3;
  }

  // convenience fee 配置
  ConvenienceFeeConfig convenience_fee_config = 2;
  // 收银台的 tips 配置
  TipsConfig cashier_tips_config = 3;
  // terminal tips 配置
  TipsConfig terminal_tips_config = 4;
  // tips appearance
  string tips_appearance = 5;
}
