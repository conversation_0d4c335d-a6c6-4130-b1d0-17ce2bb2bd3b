syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// deposit status
enum DepositStatus {
  // unspecified
  DEPOSIT_STATUS_UNSPECIFIED = 0;
  // processing
  DEPOSIT_STATUS_PROCESSING = 1;
  // require payment method
  DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD = 2;
  // require confirm
  DEPOSIT_STATUS_REQUIRE_CONFIRM = 3;
  // require capture
  DEPOSIT_STATUS_REQUIRE_CAPTURE = 4;
  // processing capture
  DEPOSIT_STATUS_PROCESSING_CAPTURE = 5;
  // paid
  DEPOSIT_STATUS_PAID = 6;
  // refunded
  DEPOSIT_STATUS_REFUNDED = 7;
  // cancel
  DEPOSIT_STATUS_CANCEL = 8;
  // failed, database mapping is -1
  DEPOSIT_STATUS_FAILED = 9;
}
