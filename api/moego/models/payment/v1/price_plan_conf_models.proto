syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// price plan conf
message PricePlanConf {
  // id
  int64 id = 1;
  // stripe plan id
  string stripe_plan_id = 2;
  // level
  int64 level = 3;
  // plan type
  int64 plan_type = 4;
  // price
  double price = 5;
  // title
  string title = 6;
  // description
  string description = 7;
  // update time
  int64 update_time = 8;
  // create time
  int64 create_time = 9;
  // plan name
  string plan_name = 10;
  // is by id
  int64 is_by_id = 11;
  // is new pricing
  int64 is_new_pricing = 12;
  // business type
  int64 business_type = 13;
  // business num
  int64 business_num = 14;
}
