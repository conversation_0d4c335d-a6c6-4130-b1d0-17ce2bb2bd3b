syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// stripe payment method
enum StripePaymentMethod {
  // unspecified
  STRIPE_PAYMENT_METHOD_UNSPECIFIED = 0;
  // credit card
  STRIPE_PAYMENT_METHOD_CARD = 1;
  // card on file
  STRIPE_PAYMENT_METHOD_COF = 2;
  // bluetooth reader
  STRIPE_PAYMENT_METHOD_BLUETOOTH_READER = 3;
  // smart reader
  STRIPE_PAYMENT_METHOD_SMART_READER = 4;
  // apple pay
  STRIPE_PAYMENT_METHOD_APPLE_PAY = 5;
  // google pay
  STRIPE_PAYMENT_METHOD_GOOGLE_PAY = 6;
  // tap to pay on ios
  STRIPE_PAYMENT_METHOD_TTPOI = 7;
  // ach
  STRIPE_PAYMENT_METHOD_ACH = 8;
}

// SquarePaymentMethod is the method of a square payment
enum SquarePaymentMethod {
  // unspecified
  SQUARE_PAYMENT_METHOD_UNSPECIFIED = 0;
  // card
  CARD = 1;
  // card on file
  COF = 2;
  // terminal
  TERMINAL = 3;
  // reader
  READER = 4;
  // pos
  POS = 5;
}

// PaymentMethod is the method of a payment
enum PaymentMethod {
  // unspecified
  PAYMENT_METHOD_UNSPECIFIED = 0;
  // credit card
  CREDIT_CARD = 1;
  // cash
  CASH = 2;
  // check
  CHECK = 3;
  // venmo
  VENMO = 4;
  // paypal
  PAYPAL = 5;
  // chase
  CHASE = 6;
  // other card reader
  OTHER_CARD_READER = 7;
  // square
  SQUARE = 8;
}
