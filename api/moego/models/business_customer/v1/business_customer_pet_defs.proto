syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/date.proto";
import "moego/models/business_customer/v1/business_pet_medical_info_defs.proto";
import "moego/models/business_customer/v1/business_pet_note_defs.proto";
import "moego/models/business_customer/v1/business_pet_preference_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_defs.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Create def for pet model
message BusinessCustomerPetCreateDef {
  // pet name, default is empty
  // 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
  string pet_name = 1 [(validate.rules).string = {max_len: 50}];

  // avatar path, default is empty
  string avatar_path = 2 [(validate.rules).string = {
    ignore_empty: true
    uri: true
    max_len: 255
  }];

  // pet type id, required
  moego.models.customer.v1.PetType pet_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // breed, default is empty
  // 业务上必填, 但是也兼容不填的情况, 为以后业务变更做准备
  optional string breed = 4 [(validate.rules).string = {max_len: 50}];
  // breed mixed, default is false
  optional bool breed_mixed = 5;

  // gender, optional
  optional moego.models.customer.v1.PetGender gender = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // weight, optional
  // should be a number string
  optional string weight = 7 [(validate.rules).string = {max_len: 50}];
  // coat type, optional
  optional string coat_type = 8 [(validate.rules).string = {max_len: 50}];
  // fixed, optional
  optional string fixed = 9 [(validate.rules).string = {max_len: 50}];
  // behavior, optional
  optional string behavior = 10 [(validate.rules).string = {max_len: 50}];

  // birthday, optional
  optional google.type.Date birthday = 11;

  // evaluation status, optional
  optional models.customer.v1.EvaluationStatus evaluation_status = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // pet code ids
  repeated int64 pet_code_ids = 13 [(validate.rules).repeated = {
    unique: true
    max_items: 20
    items: {
      int64: {gt: 0}
    }
  }];
}

// Create def for pet model with additional info
message BusinessCustomerPetWithAdditionalInfoCreateDef {
  // pet, required
  BusinessCustomerPetCreateDef pet = 1 [(validate.rules).message.required = true];

  // medical info, optional
  optional BusinessPetMedicalInfoUpdateDef medical_info = 2;

  // preference, optional
  optional BusinessCustomerPetPreferenceUpdateDef preference = 3;

  // vaccine records, empty means no vaccine records
  repeated BusinessPetVaccineRecordCreateDef vaccine_records = 4 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // notes, empty means no notes
  repeated BusinessPetNoteCreateDef notes = 5 [(validate.rules).repeated = {
    max_items: 200
    items: {
      message: {required: true}
    }
  }];

  // todo: other additional info, e.g.: photos
}

// Update def for pet model
message BusinessCustomerPetUpdateDef {
  // pet name
  optional string pet_name = 1 [(validate.rules).string = {max_len: 50}];

  // avatar path
  optional string avatar_path = 2 [(validate.rules).string = {
    ignore_empty: true
    uri: true
    max_len: 255
  }];

  // pet type
  optional moego.models.customer.v1.PetType pet_type = 3;

  // breed
  optional string breed = 4 [(validate.rules).string = {max_len: 50}];

  // breed mixed
  optional bool breed_mixed = 5;

  // gender
  optional moego.models.customer.v1.PetGender gender = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // weight, optional
  // should be a number string
  // if empty string is set, the value will be set to empty
  optional string weight = 7 [(validate.rules).string = {max_len: 50}];

  // coat type, optional
  // if empty string is set, the value will be set to empty
  optional string coat_type = 8 [(validate.rules).string = {max_len: 50}];
  // fixed, optional
  // if empty string is set, the value will be set to empty
  optional string fixed = 9 [(validate.rules).string = {max_len: 50}];
  // behavior, optional
  // if empty string is set, the value will be set to empty
  optional string behavior = 10 [(validate.rules).string = {max_len: 50}];

  // pet codes, optional
  // if this message is not set, pet codes of the pet will not be updated
  // if this message is set but the ids field is empty, pet codes of the pet will be cleared
  optional PetCodeList pet_codes = 11;

  // birthday
  optional google.type.Date birthday = 12;

  // passed away
  optional bool passed_away = 13;

  // evaluation status
  optional models.customer.v1.EvaluationStatus evaluation_status = 14 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // medical info, optional
  optional BusinessPetMedicalInfoUpdateDef medical_info = 15;

  // pet code list
  message PetCodeList {
    // pet code ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      unique: true
      max_items: 20
      items: {
        int64: {gt: 0}
      }
    }];
  }
}
