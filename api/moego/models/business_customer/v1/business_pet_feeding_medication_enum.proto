syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// feeding medication schedule date type
enum FeedingMedicationScheduleDateType {
  // unspecified
  FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED = 0;
  // Every day except for checkout day
  EVERYDAY_EXCEPT_CHECKOUT_DATE = 1;
  // Every day include checkout day
  EVERYDAY_INCLUDE_CHECKOUT_DATE = 2;
  // Specific date
  SPECIFIC_DATE = 3;
}
