syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet schedule time definition
message BusinessPetScheduleTimeDef {
  // schedule time, schedule time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  int32 schedule_time = 1 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // schedule extra json, such as schedule time label etc.
  map<string, string> extra_json = 2 [(validate.rules).map = {
    min_pairs: 0
    max_pairs: 1000
  }];
}
