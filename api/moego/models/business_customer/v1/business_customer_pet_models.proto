syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// business customer pet info model
message BusinessCustomerPetInfoModel {
  // id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // pet name
  string pet_name = 3;
  // avatar path
  string avatar_path = 4;
  // pet type id
  moego.models.customer.v1.PetType pet_type = 5;
  // breed
  string breed = 6;
  // breed mixed
  bool breed_mixed = 7;
  // gender
  moego.models.customer.v1.PetGender gender = 8;
  // weight
  string weight = 9;
  // coat type
  string coat_type = 10;
  // fixed
  string fixed = 11;
  // behavior
  string behavior = 12;
  // pet code ids
  repeated int64 pet_code_ids = 13;
  // birthday, may not exist
  optional google.type.Date birthday = 14;
  // passed away
  bool passed_away = 15;
  // deleted
  bool deleted = 16;
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 17;
  // company id
  int64 company_id = 18;
  // playgroup id
  int64 playgroup_id = 19;
  // expiry notification
  bool expiry_notification = 20;
  // created at
  google.protobuf.Timestamp create_time = 21;
  // updated at
  google.protobuf.Timestamp update_time = 22;
}

// business customer pet model
// 这个模型弃用, 里面的字段太乱了, 没有设计好. 请使用 BusinessCustomerPetInfoModel
message BusinessCustomerPetModel {
  // id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // business id, please do not use this field
  int64 business_id = 3 [deprecated = true];

  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;

  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  bool breed_mixed = 10;

  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  string behavior = 13;

  // birthday, may not exist
  optional google.type.Date birthday = 15;
  // passed away
  bool passed_away = 16;
  // deleted
  bool deleted = 17;

  // expiry notification
  bool expiry_notification = 20;

  /**
   * vet info, these fields should be redesigned
   */
  // vet name
  string vet_name = 30;
  // vet phone number
  string vet_phone_number = 31;
  // vet address
  string vet_address = 32;
  // emergency contact name
  string emergency_contact_name = 33;
  // emergency contact phone number
  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35;
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 36;

  // pet appearance color
  string pet_appearance_color = 37;
  // pet appearance notes
  string pet_appearance_notes = 38;
}

// business customer pet model in c app view
message BusinessCustomerPetModelClientView {
  // business pet id
  int64 id = 1;
  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;
  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
}

// business customer pet model for appointment
message BusinessCustomerPetCalendarView {
  // business pet id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;
  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
}

// business customer pet model for print card
message BusinessCustomerPetPrintCardView {
  // id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;

  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;

  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  bool breed_mixed = 10;

  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  string behavior = 13;

  /**
   * vet info, these fields should be redesigned
   */
  // vet name
  string vet_name = 30;
  // vet phone number
  string vet_phone_number = 31;
  // vet address
  string vet_address = 32;
  // emergency contact name
  string emergency_contact_name = 33;
  // emergency contact phone number
  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35;
}

// business customer pet model
message BusinessCustomerPetModelOverview {
  // id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // business id, please do not use this field
  //  int64 business_id = 3 [deprecated = true];

  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;

  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  //  bool breed_mixed = 10;

  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  //  string behavior = 13;

  // birthday, may not exist
  //  optional google.type.Date birthday = 15;
  // passed away
  bool passed_away = 16;
  // deleted
  bool deleted = 17;

  // expiry notification
  bool expiry_notification = 20 [deprecated = true];

  /**
   * vet info, these fields should be redesigned
   */
  // vet name
  //  string vet_name = 30;
  // vet phone number
  //  string vet_phone_number = 31;
  // vet address
  //  string vet_address = 32;
  // emergency contact name
  //  string emergency_contact_name = 33;
  // emergency contact phone number
  //  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35 [deprecated = true];
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 36;

  // pet code id list
  repeated int64 pet_code_ids = 13;
}

// business customer pet model online booking view
message BusinessCustomerPetOnlineBookingView {
  // pet id
  int64 id = 1;
  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;
  // pet type
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  bool breed_mixed = 10;
  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  double weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  string behavior = 13;
  // birthday, may not exist
  optional google.type.Date birthday = 15;
  // vet name
  string vet_name = 30;
  // vet phone number
  string vet_phone_number = 31;
  // vet address
  string vet_address = 32;
  // emergency contact name
  string emergency_contact_name = 33;
  // emergency contact phone number
  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35;
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 36;
  // new pet：没有创建过 appointment 的 pet
  bool is_new_pet = 37;
}

// business customer pet view for branded app
message BusinessCustomerPetBrandedAppView {
  // id
  int64 id = 1;
  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;

  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  bool breed_mixed = 10;

  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  string behavior = 13;

  // birthday, may not exist
  optional google.type.Date birthday = 15;

  // vet name
  string vet_name = 30;
  // vet phone number
  string vet_phone_number = 31;
  // vet address
  string vet_address = 32;
  // emergency contact name
  string emergency_contact_name = 33;
  // emergency contact phone number
  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35;
}
