syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/dayofweek.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "moego/utils/v1/time_period.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Update def for business customer communication preference. All fields are optional. Only fields that are set will be updated.
message BusinessCustomerCommunicationPreferenceUpdateDef {
  // if block message from this customer
  optional bool block_message = 1;

  // Enable auto message - SMS
  optional bool enable_auto_message_sms = 2;
  // Enable auto message - email
  optional bool enable_auto_message_email = 3;

  // Enable appointment reminder - SMS
  optional bool enable_appointment_reminder_sms = 5;
  // Enable appointment reminder - email
  optional bool enable_appointment_reminder_email = 6;
  // Enable appointment reminder - Call
  optional bool enable_appointment_reminder_call = 7;

  // Enable marketing email
  optional bool enable_marketing_email = 8;
}

// Update def for business customer appointment preference. All fields are optional. Only fields that are set will be updated.
message BusinessCustomerAppointmentPreferenceUpdateDef {
  // Preferred groomer id. If set to 0, preferred grooming will be cleared.
  optional int64 preferred_groomer_id = 1 [(validate.rules).int64 = {gte: 0}];
  // Preferred grooming frequency
  optional utils.v1.TimePeriod preferred_grooming_frequency = 2;
  // Preferred day of week
  optional DayOfWeekList preferred_day_of_week = 3;
  // Preferred time of day
  optional utils.v1.TimeOfDayInterval preferred_time_of_day = 4;

  // day of week list
  message DayOfWeekList {
    // List of preferred day of week
    repeated google.type.DayOfWeek days = 1 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
}

// Update def for business customer payment preference. All fields are optional. Only fields that are set will be updated.
message BusinessCustomerPaymentPreferenceUpdateDef {
  // Enable auto tipping
  optional bool enable_auto_tipping = 1;
  // Auto Tipping percentage or amount. Optional and only one of them should be set.
  oneof auto_tipping_value {
    option (validate.required) = false;
    // Percentage. If set, auto tipping mode will be switched to percentage mode.
    // Percentage should range from 1 to 100.
    int32 auto_tipping_percentage = 2 [(validate.rules).int32 = {
      gte: 1
      lte: 100
    }];
    // Amount. If set, auto tipping mode will be switched to amount mode.
    // Amount should be greater than or equal to 0, and keep at most two decimals. e.g. 1, 1.1, 1.01.
    double auto_tipping_amount = 3 [(validate.rules).double = {gte: 0}];
  }
}
