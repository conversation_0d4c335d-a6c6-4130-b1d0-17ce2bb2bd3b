syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// customer note create def
// 支持数据导入时填写 created_at 和 updated_at 字段
message BusinessCustomerNoteCreateDef {
  // note
  string note = 1 [(validate.rules).string = {max_len: 10000}];

  // created_at, optional
  // 这个字段是给数据导入用的, 可以填一个过去的时间. 不填则使用当前时间
  optional google.protobuf.Timestamp created_at = 2 [(validate.rules).timestamp = {
    gte: {
      seconds: 0
      nanos: 0
    }
  }];

  // updated_at, optional
  // 这个字段是给数据导入用的, 可以填一个过去的时间. 不填则使用当前时间
  optional google.protobuf.Timestamp updated_at = 3 [(validate.rules).timestamp = {
    gte: {
      seconds: 0
      nanos: 0
    }
  }];
}

// customer note update def
// 不支持 update_at 字段, 默认使用当前时间作为更新时间
message BusinessCustomerNoteUpdateDef {
  // note
  string note = 1 [(validate.rules).string = {max_len: 10000}];
}
