syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet metadata definition
message BusinessPetMetadataDef {
  // metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // metadata value
  string metadata_value = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 3 [(validate.rules).map = {
    min_pairs: 0
    max_pairs: 1000
  }];
}
