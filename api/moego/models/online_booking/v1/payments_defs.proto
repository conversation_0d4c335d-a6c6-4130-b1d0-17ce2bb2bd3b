syntax = "proto3";

package moego.models.online_booking.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// require card on file definition (No-show protection)
message CardOnFileDef {
  // stripe card token id
  optional string charge_token = 1 [(validate.rules).string = {max_len: 1024}];
  // has card on file
  bool is_has_card = 2;
}

// Prepayment / Deposit definition
message PrepayDef {
  // prepay guid
  string prepay_guid = 1 [(validate.rules).string = {max_len: 128}];
  // subtotal, service total or deposit amount
  double subtotal = 3;
  // tax amount
  double tax_amount = 4;
  // tips amount
  double tips_amount = 5;
  // service charge amount
  double service_charge_amount = 6;
  // booking fee
  double booking_fee = 7;
  // processing fee
  double processing_fee = 8;
  // discount amount
  optional double discount_amount = 9;
}

// MoeGo Pay Pre-auth definition
message PreAuthDef {
  // service total
  double service_total = 1;
  // tax amount
  double tax_amount = 2;
  // tips amount
  double tips_amount = 3;
  // service charge amount
  double service_charge_amount = 4;
  // booking fee
  double booking_fee = 5;
  // processing fee
  double processing_fee = 6;
  // payment method id, selected payment method
  optional string payment_method_id = 7;
  // card type number, visa(4242)
  optional string card_type_number = 8;
  // stripe card token id
  optional string charge_token = 9;
  // discount amount
  optional double discount_amount = 10;
}
