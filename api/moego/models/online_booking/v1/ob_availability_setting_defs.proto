// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/online_booking/v1/ob_availability_setting_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// grooming service availability update def
message GroomingServiceAvailabilityUpdateDef {
  // accept customer type
  optional AcceptCustomerType accept_customer_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// boarding service availability update def
message BoardingServiceAvailabilityUpdateDef {
  // accepted pet types
  repeated models.customer.v1.PetType accepted_pet_types = 1;
  // available date range
  DateRangeDef available_date_range = 2;
  // pick up time range
  optional ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
  // accept customer type
  optional AcceptCustomerType accept_customer_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // lodging availability
  optional LodgingAvailabilityDef lodging_availability = 5;
}

// daycare service availability update def
message DaycareServiceAvailabilityUpdateDef {
  // accepted pet types
  repeated models.customer.v1.PetType accepted_pet_types = 1;
  // available date range
  DateRangeDef available_date_range = 2;
  // pick up time range
  optional ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
  // accept customer type
  optional AcceptCustomerType accept_customer_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // lodging availability
  optional LodgingAvailabilityDef lodging_availability = 5;
}

// evaluation service availability update def
message EvaluationServiceAvailabilityUpdateDef {
  // available date range
  DateRangeDef available_date_range = 2;
  // pick up time range
  ArrivalPickUpTimeDef arrival_pick_up_time_range = 3;
}

// booking range model
message DateRangeDef {
  // start date type
  DateLimitType start_date_type = 1;
  // specific date or max date offset from today
  oneof start_date {
    option (validate.required) = true;
    // specific start date
    google.type.Date specific_start_date = 2;
    // dynamic setting, max start date offset from today
    int32 max_start_date_offset = 3 [(validate.rules).int32 = {
      gte: 0
      lte: 365
    }];
  }
  // start date type
  DateLimitType end_date_type = 4;
  // specific date or max date offset from today
  oneof end_date {
    option (validate.required) = true;
    // specific end date
    google.type.Date specific_end_date = 5;
    // dynamic setting, max end date offset from today
    int32 max_end_date_offset = 6 [(validate.rules).int32 = {
      gte: 0
      lte: 365
    }];
  }
}

// arrival and pick up time
message ArrivalPickUpTimeDef {
  // is customized
  bool is_customized = 1;
  // start date
  optional google.type.Date start_date = 2;
  // end date
  optional google.type.Date end_date = 3;
  // schedule type
  ScheduleType schedule_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service ids
  repeated int64 service_ids = 5;
  // is all services
  bool is_all_service = 6;
  // arrival time range
  TimeRangeDef arrival_time_range = 9;
  // pick up time range
  TimeRangeDef pick_up_time_range = 10;
  // time range setting id
  optional int64 time_range_setting_id = 11;
}

// time range model
message TimeRangeDef {
  // first week
  DayOfWeekTimeRangeDef first_week = 4 [(validate.rules).message.required = true];
  // second week
  optional DayOfWeekTimeRangeDef second_week = 5;
  // third week
  optional DayOfWeekTimeRangeDef third_week = 6;
  // forth week
  optional DayOfWeekTimeRangeDef forth_week = 7;
}

// time detail
message DayOfWeekTimeRangeDef {
  // monday
  repeated DayTimeRangeDef monday = 1 [(validate.rules).repeated = {ignore_empty: true}];
  // tuesday
  repeated DayTimeRangeDef tuesday = 2 [(validate.rules).repeated = {ignore_empty: true}];
  // wednesday
  repeated DayTimeRangeDef wednesday = 3 [(validate.rules).repeated = {ignore_empty: true}];
  // thursday
  repeated DayTimeRangeDef thursday = 4 [(validate.rules).repeated = {ignore_empty: true}];
  // friday
  repeated DayTimeRangeDef friday = 5 [(validate.rules).repeated = {ignore_empty: true}];
  // saturday
  repeated DayTimeRangeDef saturday = 6 [(validate.rules).repeated = {ignore_empty: true}];
  // sunday
  repeated DayTimeRangeDef sunday = 7 [(validate.rules).repeated = {ignore_empty: true}];
}

// time range
message DayTimeRangeDef {
  // start time
  int32 start_time = 1 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // end time
  int32 end_time = 2 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // max pet count can take during this time range. Available for evaluation arrival time range
  optional int32 pet_capacity = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 100
  }];
}

// lodging availability
message LodgingAvailabilityDef {
  // if limit requests based on  lodging/area capacity
  bool is_capacity_limited = 1;
  // limit requests based on service related lodging/area capacity
  int32 capacity_limit = 2 [(validate.rules).int32 = {gte: 0}];
  // allow waitlist signups
  optional bool allow_waitlist_signups = 3;
}

// day time range list
message DayTimeRangeDefList {
  // The list of values
  repeated DayTimeRangeDef values = 1;
}

// capacity override model
message CapacityOverrideDef {
  // id, empty for create new record
  optional int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // date ranges
  repeated CapacityDateRangeDef date_ranges = 2 [(validate.rules).repeated = {min_items: 1}];
  // capacity
  int32 capacity = 3 [(validate.rules).int32 = {gte: 0}];
  // unit type
  CapacityOverrideUnitType unit_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // capacity date range def
  message CapacityDateRangeDef {
    // start date
    google.type.Date start_date = 1;
    // end date
    google.type.Date end_date = 2;
  }
}
