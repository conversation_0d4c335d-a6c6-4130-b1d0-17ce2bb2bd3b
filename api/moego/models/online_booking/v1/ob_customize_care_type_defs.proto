syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// create booking care type def
message CreateBookingCareTypeDef {
  // booking care type name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // description
  optional string description = 2;
  // icon url
  string icon = 3;
  // image url
  string image = 4;
  // service type
  models.offering.v1.ServiceType service_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // applicable services
  ApplicableServices applicable_services = 7;
}

// update booking care type def
message UpdateBookingCareTypeDef {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // booking care type name
  optional string name = 2;
  // description
  optional string description = 3;
  // icon url
  optional string icon = 4;
  // image url
  optional string image = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // applicable services
  ApplicableServices applicable_services = 8;
}

// booking care type view
message BookingCareTypeView {
  // the unique id
  int64 id = 1;
  // booking care type name
  string name = 2;
  // description
  optional string description = 3;
  // icon image url
  string icon = 4;
  // image url
  string image = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6;
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 7;
  // applicable services
  ApplicableServices applicable_services = 8;
}

// applicable services
message ApplicableServices {
  // is all service applicable
  bool is_all_service_applicable = 1;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_services = 2 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}
