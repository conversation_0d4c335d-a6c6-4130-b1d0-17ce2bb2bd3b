syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The daycare service detail
message DaycareServiceDetailModel {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of current service
  int64 service_id = 4;
  // The specific dates of the daycare service
  repeated string specific_dates = 5;
  // The price of current service
  double service_price = 6;
  // taxId
  int64 tax_id = 7;
  // The max duration of the daycare service, unit minute
  int32 max_duration = 8;
  // The pet arrival time of the service, unit minute, 540 means 09:00
  int32 start_time = 9;
  // The pet latest pickup time of the service, unit minute, 540 means 09:00
  int32 end_time = 10;
  // createdAt
  google.protobuf.Timestamp created_at = 11;
  // updatedAt
  google.protobuf.Timestamp updated_at = 12;
}
