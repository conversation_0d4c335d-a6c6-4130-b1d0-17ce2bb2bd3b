syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The booking request note model,
message BookingRequestNoteModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // customer id
  int64 customer_id = 3;
  // question name
  string note = 4;
  // customer id
  int64 booking_request_id = 5;
  // createdAt
  google.protobuf.Timestamp created_at = 6;
  // updatedAt
  google.protobuf.Timestamp updated_at = 7;
}
