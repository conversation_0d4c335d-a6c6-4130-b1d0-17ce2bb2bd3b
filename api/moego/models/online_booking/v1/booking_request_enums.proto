// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Booking request status
enum BookingRequestStatus {
  // unspecified
  BOOKING_REQUEST_STATUS_UNSPECIFIED = 0;
  // The user submitted it from online booking, but the business did not process it.
  SUBMITTED = 1;
  // moved to wait list
  WAIT_LIST = 2;
  // business accepts and scheduled to an appointment
  SCHEDULED = 3;
  // business rejects the booking request, it will be displayed in the canceled list.
  DECLINED = 4;
  // business deletes the booking request, it won't show up on any lists
  DELETED = 5;
  // payment failed, BookingRequest 通过 deposit 状态来判断是否支付成功
  PAYMENT_FAILED = 6 [deprecated = true];
}

// Booking request source platform
enum BookingRequestSourcePlatform {
  // unspecified
  BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED = 0;
  // reserve with google
  RESERVE_WITH_GOOGLE = 1;
  // pet parent app
  PET_PARENT_APP = 2;
}

// Booking request associated model
enum BookingRequestAssociatedModel {
  // unspecified
  BOOKING_REQUEST_ASSOCIATED_MODEL_UNSPECIFIED = 0;
  // service
  SERVICE = 1;
  // add-on
  ADD_ON = 2;
  // feeding
  FEEDING = 3;
  // medication
  MEDICATION = 4;
  // auto assign
  AUTO_ASSIGN = 5;
}

// Booking request sort field
enum BookingRequestSortField {
  // unspecified
  BOOKING_REQUEST_SORT_FIELD_UNSPECIFIED = 0;
  // start date
  START_DATE = 1;
  // created at
  CREATED_AT = 2;
}
