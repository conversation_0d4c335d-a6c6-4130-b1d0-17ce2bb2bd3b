syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The booking request service
message Service {
  // the service type
  oneof service {
    // boarding service
    BoardingService boarding = 2;
    // daycare service
    DaycareService daycare = 3;
  }
}

// Boarding service
message BoardingService {
  // Boarding service id
  PetServiceDetail service = 1;
}

// Daycare service
message DaycareService {
  // Boarding service id
  PetServiceDetail service = 1;
}

//service base info
message PetServiceDetail {
  //pet id
  int64 pet_id = 1;
  // The id of current service
  int64 service_id = 2;
  // The time of current service, unit minute
  int32 service_time = 3;
  // The price of current service
  double service_price = 4;
}
