// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/online_booking/v1/automation_enums.proto";
import "moego/models/online_booking/v1/ob_config_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// automation condition def
message AutomationConditionDef {
  // accept client type
  AcceptClientType accept_client_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // profile update condition
  ProfileUpdateCondition profile_update_condition = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // vaccine status condition
  VaccineStatusCondition vaccine_status_condition = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}
