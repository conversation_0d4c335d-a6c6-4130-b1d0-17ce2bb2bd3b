syntax = "proto3";

package moego.models.online_booking.v1;

import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// date range
message ServiceDateRangeDef {
  // start date
  google.type.Date start_date = 1 [(validate.rules).message = {required: true}];
  // end date
  google.type.Date end_date = 2 [(validate.rules).message = {required: true}];
}

// date list
message ServiceDateListDef {
  // date list
  repeated google.type.Date dates = 1 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];
}
