syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Stores information about medication events.
message MedicationModel {
  // The primary key identifier for each medication event.
  int64 id = 1;
  // The booking request identifier.
  int64 booking_request_id = 2;
  // The service detail identifier.
  int64 service_detail_id = 3;
  // service detail type, 2: boarding, 3: daycare
  moego.models.offering.v1.ServiceItemType service_detail_type = 4;
  // Medication time.
  repeated MedicationSchedule time = 5;
  // Medication amount, must be greater than 0.
  double amount = 6;
  // Medication unit.
  string unit = 7;
  // Medication name.
  string medication_name = 8;
  // Additional notes about the medication.
  string notes = 9;
  // createdAt
  google.protobuf.Timestamp created_at = 10;
  // updatedAt
  google.protobuf.Timestamp updated_at = 11;
  // Medication amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 12;
  // Medication select date
  optional moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef selected_date = 13;

  // Medication schedule.
  message MedicationSchedule {
    // Label for the schedule.
    string label = 1;
    // Time for the schedule, in minutes.
    int32 time = 2;
  }
}
