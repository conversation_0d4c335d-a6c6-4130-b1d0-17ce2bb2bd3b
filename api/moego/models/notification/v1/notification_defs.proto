syntax = "proto3";

package moego.models.notification.v1;

import "moego/models/notification/v1/notification_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// notification list sort definition
message NotificationSortDef {
  // sort field
  NotificationSortField field = 1 [(validate.rules).enum = {defined_only: true}];
  // sort asc or desc
  bool asc = 2;
}

// app push definition
message AppPushDef {
  // content source
  oneof content_source {
    // from template
    bool from_template = 1;
    // from content
    AppPushByContentDef from_content = 2;
  }
  // push token source
  moego.models.notification.v1.PushTokenSource source = 3 [(validate.rules).enum = {defined_only: true}];

  // badge
  optional uint64 badge = 4;
}

// app push by content definition
message AppPushByContentDef {
  // title
  optional string title = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // content
  optional string content = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}
