syntax = "proto3";

package moego.models.capital.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/capital/v1/loan_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/capital/v1;capitalpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.capital.v1";

// LoadOfferModel
message LoanOfferModel {
  // The ID of the offer.
  string offer_id = 1 [(validate.rules).string.max_len = 64];
  // The name of the channel that created the offer.
  LoanChannel channel_name = 2;
  // The ID of the account in the channel.
  string channel_account_id = 3 [(validate.rules).string.max_len = 64];
  // The ID of the offer in the channel.
  string channel_offer_id = 4 [(validate.rules).string.max_len = 64];
  // The type of the offer, e.g. MCA, Term Loan.
  LoanOfferType offer_type = 5;
  // The unix timestamp in seconds when the offer was created.
  google.protobuf.Timestamp created_at = 6 [(validate.rules).timestamp.gte.seconds = 0];
  // The unix timestamp in seconds when the offer expires.
  google.protobuf.Timestamp expire_at = 7 [(validate.rules).timestamp.gte.seconds = 0];
  // The unix timestamp in seconds when the offer is closed (that is, changed to a terminal status).
  google.protobuf.Timestamp closed_at = 8 [(validate.rules).timestamp.gte.seconds = 0];
  // The offered terms of the offer.
  LoanTerms offered_terms = 9;
  // The accepted terms of the offer.
  optional LoanTerms accepted_terms = 10;
  // Financing product identifier. e.g. Standard, Refill.
  LoanProductType product_type = 11;
  // Offer status.
  LoanOfferStatus status = 12;
  // The status of the offer in the channel.
  // This field is a backup for original status field from the channel, so we use string type instead of enum.
  // Possible values for Stripe channel: "delivered", "accepted", "canceled", "expired", "fully_repaid", "paid_out",
  // "rejected", "replaced", "undelivered".
  string channel_status = 13 [(validate.rules).string = {
    max_len: 20
    in: [
      "delivered",
      "accepted",
      "canceled",
      "expired",
      "fully_repaid",
      "paid_out",
      "rejected",
      "replaced",
      "undelivered"
    ]
  }];
  // Remaining amount of financing offered;
  double remaining_amount = 14;
  // The ID of the company
  int64 company_id = 15 [(validate.rules).int64.gt = 0];
  // The type of the entity
  string entity_type = 16 [(validate.rules).string = {
    max_len: 64
    in: [
      "company",
      "business"
    ]
  }];
  // The ID of the entity
  int64 entity_id = 17 [(validate.rules).int64.gt = 0];
  // The unix timestamp in seconds when the repayment starts.
  google.protobuf.Timestamp repayment_start_at = 18 [(validate.rules).timestamp.gte.seconds = 0];
  // Remaining amount of principal part. For MCA, this is the remaining advanced amount.
  double remaining_principal_amount = 19 [(validate.rules).double.gte = 0];
}

// OfferedTerms
message LoanTerms {
  // Amount of financing offered, in minor units.
  double advance_amount = 1;
  // Type of campaign, e.g. "newly_eligible_user", "previously_eligible_user", "repeat_user"
  LoanCampaignType campaign_type = 2;
  // Currency code, e.g. "usd", "cad"
  string currency = 3;
  // Fixed fee amount, in minor units.
  double fee_amount = 4;
  // The full amount to repay.
  double full_repay_amount = 5;
  // Populated when the product_type is refill.
  // Represents the discount rate percentage on remaining fee on the existing loan.
  // When the financing_offer is paid out, the previous_financing_fee_discount_amount
  // will be computed as the multiple of this rate and the remaining fee.
  // Not available for the accepted terms.
  optional double previous_financing_fee_discount_rate = 6;
  // Per-transaction rate at which Stripe will withhold funds to repay the financing.
  double withhold_rate = 7;
  // Populated when the product type of the offer is refill. Represents the discount amount on remaining premium for the
  // existing loan at payout time.
  // Not available for the offered terms.
  optional double previous_financing_fee_discount_amount = 8;
  // Interest rate, available for Term Loan.
  double interest_rate = 9 [(validate.rules).double.gte = 0];
}

// Interval information for a loan offer, if applicable.
message LoanOfferIntervalModel {
  // The ID of the loan offer
  string offer_id = 1 [(validate.rules).string.max_len = 64];
  // The sequence number of the current interval
  int64 sequence = 2 [(validate.rules).int64.gte = 0];
  // The begin time of the current interval
  google.protobuf.Timestamp begin_at = 3 [(validate.rules).timestamp.gte.seconds = 0];
  // The end time of the current interval
  google.protobuf.Timestamp due_at = 4 [(validate.rules).timestamp.gte.seconds = 0];
  // The minimum amount of the current interval
  double minimum_amount = 5;
  // The paid amount of the current interval
  double paid_amount = 6;
}

// LoanOfferRepaymentTransactionModel
message LoanOfferRepaymentTransactionModel {
  // The ID of the repayment transaction.
  string transaction_id = 1 [(validate.rules).string.max_len = 64];
  // The ID of the repayment transaction in the channel.
  string channel_transaction_id = 2 [(validate.rules).string.max_len = 64];
  // The type of the repayment
  LoanTransactionType transaction_type = 3;
  // The reason of the transaction.
  LoanTransactionReason transaction_reason = 4;
  // The name of the channel that created the repayment transaction.
  LoanChannel channel_name = 5;
  // The ID of the channel payment that is linked to this repayment transaction.
  string linked_channel_payment_id = 6 [(validate.rules).string.max_len = 64];
  // The ID of the payment that is linked to this repayment transaction.
  int64 linked_payment_id = 7 [(validate.rules).int64.gte = 0];
  // The ID of the order that is linked to this repayment transaction.
  int64 linked_order_id = 8 [(validate.rules).int64.gte = 0];
  // The unix timestamp in seconds when the repayment transaction was effective.
  google.protobuf.Timestamp effective_time = 9 [(validate.rules).timestamp.gte.seconds = 0];
  // The amount of the repayment transaction that is used to pay the advance amount.
  double advance_payment_amount = 10;
  // The amount of the repayment transaction that is used to pay the fee.
  double fee_payment_amount = 11;
  // The total amount of the repayment transaction.
  double total_payment_amount = 12;
  // The status of the repayment transaction. Should be one of: PROCESSING, SUCCESSFUL, FAILED.
  // For Stripe pay down transaction, this field is unfilled and unused.
  string status = 13 [(validate.rules).string = {
    max_len: 16
    in: [
      "",
      "SUCCESSFUL",
      "PROCESSING",
      "FAILED"
    ]
    ignore_empty: true
  }];
  // the id of the repayment transaction that is reversed by this transaction.
  string reversed_transaction_id = 14 [(validate.rules).string.max_len = 64];
  // the description of the repayment transaction.
  string description = 15 [(validate.rules).string.max_len = 128];
  // The repaid amount to the interest. Only applicable for Term Loan.
  google.type.Money interest_payment_amount = 16;
  // The fee type.
  LoanTransactionFeeType fee_type = 17 [(validate.rules).enum = {defined_only: true}];
  // The unix timestamp when the repayment transaction was settled.
  // TODO(Perqin, P0): Confirm will it be set for failed?
  google.protobuf.Timestamp settled_time = 18 [(validate.rules).timestamp.gte.seconds = 0];
}

// Notable offer update
message NotableOfferUpdate {
  // Update type
  enum NotableOfferUpdateType {
    // Unspecified
    NOTABLE_OFFER_UPDATE_TYPE_UNSPECIFIED = 0;
    // Offer is rejected
    REJECTED = 1;
  }

  // The ID of the offer
  string offer_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 64
  }];
  // update type
  NotableOfferUpdateType update_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}
