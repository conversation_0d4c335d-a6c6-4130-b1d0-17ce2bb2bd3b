syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

/*
 * lodging unit list model
 */
message LodgingUnitView {
  // business id
  int64 business_id = 1;
  // id of the lodging unit
  int64 id = 2;
  // name of the lodging unit
  string name = 3;
  // lodging type of this lodging unit
  int64 lodging_type_id = 4;
  // camera id
  int64 camera_id = 5;
  // Sort for the lodging unit
  int32 sort = 6;
}

/*
 * lodging unit list model
 */
message LodgingUnitModel {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // id of the lodging unit
  int64 id = 3;
  // name of the lodging unit
  string name = 4;
  // lodging type of this lodging unit
  int64 lodging_type_id = 5;
  // camera id
  int64 camera_id = 6;
  // Sort for the lodging unit
  int32 sort = 7;
}
