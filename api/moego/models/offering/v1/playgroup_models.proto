syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// playgroup model
message PlaygroupModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // description
  string description = 3;
  // color code
  string color_code = 4;
  // max pet capacity
  int32 max_pet_capacity = 5;
  // playgroup list sort. start with 1 and put the smallest first
  int32 sort = 6;
}
