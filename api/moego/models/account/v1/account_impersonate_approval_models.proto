syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account model
message AccountImpersonateApprovalInstanceModel {
  // instance code, identify the approval instance
  string instance_code = 1;

  // impersonator
  string impersonator = 2;

  // source
  string source = 3;

  // target account id
  int64 target_account_id = 4;

  // target account id
  string target_account_email = 5;

  // max age
  google.protobuf.Duration max_age = 6;

  // status, PENDING, APPROVED, REJECTED, CANCELED, DELETED
  string status = 7;
  // created at
  google.protobuf.Timestamp created_at = 8;
  // updated at
  google.protobuf.Timestamp approved_at = 9;
}
