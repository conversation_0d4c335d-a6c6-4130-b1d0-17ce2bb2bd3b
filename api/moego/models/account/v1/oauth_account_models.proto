syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/account/v1/oauth_account_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// oauth account model
message OauthAccountModel {
  // id
  int64 id = 1;

  // account id
  string account_id = 2;

  // oauth provider
  OauthAccountProvider provider = 3;

  // oauth open id
  string open_id = 4;

  // oauth user's email
  string email = 5;

  // oauth user's first name
  string first_name = 6;

  // oauth user's last name
  string last_name = 7;

  // oauth user's avatar path
  string avatar_path = 8;

  // extra data
  google.protobuf.Struct extra_data = 9;

  // created at
  google.protobuf.Timestamp created_at = 10;

  // updated at
  google.protobuf.Timestamp updated_at = 11;
}
