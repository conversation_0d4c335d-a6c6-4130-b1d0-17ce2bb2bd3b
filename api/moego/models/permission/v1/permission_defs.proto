syntax = "proto3";

package moego.models.permission.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1;permissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.permission.v1";

// definition of permission scope extra param.
message EditPermissionScopeExtraParamDef {
  // before days.
  // current used to control staff message permissions.
  optional uint32 before_day = 1 [(validate.rules).uint32 = {
    gte: 0
    lte: 30
    ignore_empty: true
  }];
  // after days.
  // current used to control staff message permissions.
  optional uint32 after_day = 2 [(validate.rules).uint32 = {
    gte: 0
    lte: 30
    ignore_empty: true
  }];
}

// definition of permission edit.
message EditPermissionDef {
  // permission id.
  int64 permission_id = 1 [(validate.rules).int64.gt = 0];
  // whether permission is active.
  bool is_active = 2;
  // scope of permission.
  optional int64 selected_scope_index = 3 [(validate.rules).int64.gte = 0];
  // active status of sub-permissions.
  repeated EditPermissionDef sub_permissions = 4;
  // sub scope of the scope.
  optional int64 selected_sub_scope_index = 5 [(validate.rules).int64.gte = 0];
  // extra params for the the deepest scope.
  // if selected a sub scope, the param is for the sub scope.
  // if selected a scope, the param is for the scope.
  // otherwise, the param is useless.
  optional EditPermissionScopeExtraParamDef scope_extra_param = 6;
}

// definition of permission edit in a category
message EditCategoryPermissionDef {
  // category id
  int64 category_id = 1 [(validate.rules).int64.gt = 0];
  // active status of permissions
  repeated EditPermissionDef permissions = 2;
}
