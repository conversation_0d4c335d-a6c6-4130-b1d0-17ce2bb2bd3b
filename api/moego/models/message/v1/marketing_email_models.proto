syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/message/v1/marketing_email_defs.proto";
import "moego/models/message/v1/marketing_email_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// marketing email model
message MarketingEmailModel {
  // email id
  int64 id = 1;
  // email subject
  string subject = 2;
  // email content
  string content = 3;
  // for schedule emails, the send time
  google.protobuf.Timestamp send_at = 4;
  // recipients
  repeated RecipientDef recipients = 5;
  // email attachment urls
  repeated AttachmentDef attachment_urls = 6;
  // last update time (only for draft)
  google.protobuf.Timestamp update_time = 7;
  // count of recipients
  int64 recipients_number = 8;
  // open rate
  int64 opened_number = 9;
  // email status
  MarketingEmailStatus status = 10;
  // count of delivered
  int64 delivered_number = 11;
  // count of clicked
  int64 clicked_number = 12;
  // count of replied
  int64 replied_number = 13;
  // client filter condition, JSON string
  string client_filter = 14;
  // if any unread reply
  bool has_unread_reply = 15;
}

// marketing email model brief view
message MarketingEmailModelBriefView {
  // email id
  int64 id = 1;
  // email subject
  string subject = 2;
  // email status
  MarketingEmailStatus status = 3;
  // last update time (only for draft)
  google.protobuf.Timestamp update_time = 4;
  // sent time
  google.protobuf.Timestamp sent_time = 5;
  // count of recipients
  int64 recipients_number = 6;
  // open rate
  int64 opened_number = 7;
  // if any unread reply
  bool has_unread_reply = 8;
}

// marketing email template model
message MarketingEmailTemplateModel {
  // template id
  int64 id = 1;
  // template name
  string name = 2;
  // template picture url
  string picture_url = 3;
  // template content
  string content = 4;
  // template subject
  string subject = 5;
  // template description
  string description = 6;
  // default client filter of this template, JSON string
  string client_filter = 7;
  // template type
  MarketingEmailTemplateType type = 8;
  // enterprise id
  int64 enterprise_id = 9;

  // marketing email template type
  enum MarketingEmailTemplateType {
    // unspecified
    MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED = 0;
    // Merry Christmas
    MERRY_CHRISTMAS = 1;
    // Thanksgiving
    THANKSGIVING = 2;
    // Halloween
    HALLOWEEN = 3;
    // Introduce pet parent app
    INTRODUCE_PET_PARENT_APP = 4;
    // Win back former customers
    WIN_BACK_FORMER_CUSTOMERS = 5;
    // Book again
    BOOK_AGAIN = 6;
    // Review booster
    REVIEW_BOOSTER = 7;
    // Fill time slots
    FILL_TIME_SLOTS = 8;
    // Introduce groomer
    INTRODUCE_GROOMER = 9;
    // Converter prospects
    CONVERTER_PROSPECTS = 10;
    // Enterprise customize
    ENTERPRISE_CUSTOMIZE = 11;
  }
}

// marketing email template model brief view
message MarketingEmailTemplateModelBriefView {
  // template id
  int64 id = 1;
  // template name
  string name = 2;
  // template picture url
  string picture_url = 3;
  // template description
  string description = 4;
  // template subject
  string subject = 5;
  // template type
  MarketingEmailTemplateModel.MarketingEmailTemplateType type = 6;
  // enterprise id
  int64 enterprise_id = 7;
}

// marketing email recipient model
message MarketingEmailRecipientModel {
  // customer id
  int64 customer_id = 1;
  // recipient id
  int64 recipient_id = 2;
  // recipient email
  string email = 3;
  // recipient name
  string name = 4;
  // recipient open status
  bool is_opened = 5;
  // recipient click status
  bool is_clicked = 6;
  // recipient reply status
  bool is_replied = 7;
  // whether the recipient reply is read
  bool is_reply_read = 8;
  // recipient send status
  MarketingEmailRecipientSendStatus send_status = 9;
  // open time
  google.protobuf.Timestamp open_time = 10;
  // click time
  google.protobuf.Timestamp click_time = 11;
  // reply time
  google.protobuf.Timestamp reply_time = 12;
}

// marketing email recipient model brief view
message MarketingEmailApptBriefView {
  // appt id
  int64 id = 1;
  // appt revenue amount
  string revenue = 2;
  // customer name
  string customer_name = 3;
  // customer email
  string customer_email = 4;
  // appt created time
  google.protobuf.Timestamp created_time = 5;
}
