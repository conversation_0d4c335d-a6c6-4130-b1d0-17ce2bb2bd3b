// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.message.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// The MessageTemplate Definition
// Note:
// 1. a def message must end with Def
// 2. a def message could be used in both request and response
// 3. all the fields of a def message must be validated
// 4. a def message's semantics must be single, which means you
//    cannot share a single def message when anyone field is
//    different, including labels.
message MessageTemplateDef {
  // template name
  string template_name = 1 [(validate.rules).string = {
    max_len: 30
    pattern: "^[a-zA-Z]([-a-zA-Z0-9\\s]*[\\S])?$$"
  }];
  // template content
  string template_content = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 2000
  }];
}

// message template type
enum MessageTemplateType {
  // default
  MESSAGE_TEMPLATE_UNSPECIFIED = 0;
  // system
  SYSTEM = 1;
  // customize
  CUSTOMIZE = 2;
  // enterprise
  ENTERPRISE = 3;
}
