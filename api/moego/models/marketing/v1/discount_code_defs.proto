// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.marketing.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/marketing/v1/discount_code_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1;marketingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.marketing.v1";

// discount code summary def
message DiscountCodeSummaryDef {
  // client name
  repeated string client_names = 1;
  // service name
  repeated string service_names = 2;
  // product name
  repeated string product_names = 3;
  // location name
  repeated string location_names = 4;
}

// item
message Item {
  // object id
  int64 object_id = 1;
  // item type
  string type = 2;
  // subTotal amount
  double sub_total_amount = 3;
}

// expiry def
message ExpiryDef {
  // expiry type
  moego.models.marketing.v1.ExpiryType type = 1;
  // expiry time
  optional google.protobuf.Timestamp time = 2;
}
