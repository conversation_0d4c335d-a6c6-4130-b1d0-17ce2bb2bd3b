syntax = "proto3";

package moego.models.google_partner.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/google_partner/v1/google_reserve_integration_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/google_partner/v1;googlepartnerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.google_partner.v1";

// Google Reserve Integration model
message GoogleReserveIntegrationModel {
  // id
  int64 id = 1;
  // business id
  int32 business_id = 2;
  // enabled
  bool enabled = 5;
  // status
  moego.models.google_partner.v1.GoogleReserveIntegrationStatus status = 6;
  // create time
  google.protobuf.Timestamp create_time = 20;
  // update time
  google.protobuf.Timestamp update_time = 21;
}
