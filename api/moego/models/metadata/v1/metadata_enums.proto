// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.metadata.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1;metadatapb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.metadata.v1";

// the owner type of a key
enum OwnerType {
  // unspecified value
  OWNER_TYPE_UNSPECIFIED = 0;
  // system target, which means it is a global configuration,
  // no customization allowed, and everyone can read it.
  OWNER_TYPE_SYSTEM = 1;
  // company target, owned by company owner
  OWNER_TYPE_COMPANY = 2;
  // business target, owned by company owner or business owner
  OWNER_TYPE_BUSINESS = 3;
  // staff target, owned by company/business owner or staff self
  OWNER_TYPE_STAFF = 4;
  // account target, owned by account self
  OWNER_TYPE_ACCOUNT = 5;
  // enterprise target, owned by enterprise owner
  OW<PERSON><PERSON>_TYPE_ENTERPRISE = 6;
}

// the allowed operator type
enum PermissionLevel {
  // unspecified value
  PERMISSION_LEVEL_UNSPECIFIED = 0;

  // the target owner, which is described in KeyTarget
  PERMISSION_LEVEL_OWNER = 1;
  // no body
  PERMISSION_LEVEL_NOBODY = 2;

  // any business owner for company
  PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER = 3;
  // any staff for company
  PERMISSION_LEVEL_COMPANY_ANY_STAFF = 4;

  // any staff for business
  PERMISSION_LEVEL_BUSINESS_ANY_STAFF = 5;
}
