// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.risk_control.v1;

import "moego/models/risk_control/v1/recaptcha_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1;riskcontrolpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.risk_control.v1";

// https://developers.google.com/recaptcha/docs/v3
// Google recaptcha definition
message RecaptchaDef {
  // recaptcha site key
  string site_key = 1;
  // recaptcha token
  string token = 2;
  // recaptcha version
  RecaptchaVersion version = 3;
  // recaptcha action
  RecaptchaAction action = 4;
}
