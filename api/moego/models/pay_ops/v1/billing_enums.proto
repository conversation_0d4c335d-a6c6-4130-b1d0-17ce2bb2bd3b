// @since 2-23-12-05
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// in the sdk provided by Stripe,
// the add, delete, and update api have been combined into one
// therefore, at the api layer,
// actions are differentiated through enumeration
// and data integration is performed in the admin api based on the type passed from the frontend
enum EditSubscriptionType {
  // unspecified
  EDIT_SUBSCRIPTION_UNSPECIFIED = 0;
  // add subscription
  EDIT_SUBSCRIPTION_ADD = 1;
  // delete subscription
  EDIT_SUBSCRIPTION_DELETE = 2;
  // update subscription
  EDIT_SUBSCRIPTION_UPDATE = 3;
}
