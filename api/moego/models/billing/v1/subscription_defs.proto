syntax = "proto3";

package moego.models.billing.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/billing/v1;billingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.billing.v1";

// price unit object
message PlanUnit {
  // PlanUnit count
  int64 count = 1;
  // PlanUnit price
  int64 unit_price_id = 2;
}

// https://docs.stripe.com/api/subscriptions/create#create_subscription-transfer_data
message TransferData {
  // destination, stripe account id
  string destination = 1;
  // amount percent
  // A non-negative decimal between 0 and 100, with at most two decimal places.
  // This represents the percentage of the subscription invoice total that will be transferred to the destination account.
  // By default, the entire amount is transferred to the destination.
  optional double amount_percent = 2;
}

// subscription event type
// ref: https://stripe.com/docs/api/events/types
enum BillingEventType {
  // unknown event type
  BILLING_EVENT_TYPE_UNSPECIFIED = 0;
  // subscription created
  SUBSCRIPTION_CREATED = 1;
  // subscription updated  -> customer.subscription.updated
  SUBSCRIPTION_UPDATED = 2;
  // subscription canceled
  SUBSCRIPTION_CANCELED = 3;
  // invoice paid -> invoice.paid
  INVOICE_PAID = 4;
  // invoice payment failed -> invoice.payment_failed
  INVOICE_PAYMENT_FAILED = 5;
  // invoice upcoming -> invoice.upcoming
  INVOICE_UPCOMING = 6;
  // invoice created -> invoice.created
  INVOICE_CREATED = 7;
}

// event model for event bus
message EventModel {
  // event type
  BillingEventType event_type = 1;
  // event data
  oneof event_detail {
    // subscription
    Subscription subscription = 2;
    // Invoice
    Invoice invoice = 3;
  }
}

// billing subscription
message Subscription {
  // subscription id, 注意此处 id 是 vendor 的 subscription id，非 billing 库中的主键id
  string subscription_id = 1;
  // customer id
  string customer_id = 2;
  // subscription status
  string status = 3;
  // subscription start time
  google.protobuf.Timestamp start_time = 4;
  // subscription end time
  google.protobuf.Timestamp end_time = 5;
  // subscription cancel time： A date in the future at which the subscription will automatically get canceled
  google.protobuf.Timestamp cancel_at = 6;
  // If the subscription has been canceled, the date of that cancellation.
  // If the subscription was canceled with cancel_at_period_end, canceled_at will reflect the time of the most recent update request,
  // not the end of the subscription period when the subscription is automatically moved to a canceled state.
  google.protobuf.Timestamp canceled_at = 7;
  // created time
  google.protobuf.Timestamp created_at = 8;
  // subscription cancel at period end
  bool cancel_at_period_end = 9;
  // subscription transfer data
  string latest_invoice = 10;
  // description
  string description = 11;
  // metadata
  map<string, string> metadata = 12;
  // id，注意此处 id 是billing库中的主键id，才是真正意义上的 subscription_id
  int64 id = 13;
  // payment method
  string payment_method = 14;
}

// event bus: invoice event
message Invoice {
  // invoice id
  string invoice_id = 1;
  // customer id
  string customer_id = 2;
  // subscription id，注意此处是 vendor 的 subscription id
  string subscription_id = 3;
  // invoice status
  string status = 4;
  // invoice amount
  int64 amount = 5;
  // invoice currency
  string currency = 6;
  // invoice start time
  google.protobuf.Timestamp start_time = 7;
  // invoice end time
  google.protobuf.Timestamp end_time = 8;
  // application fee amount
  int64 application_fee_amount = 9;
  // created time
  google.protobuf.Timestamp created_at = 10;
  // amount remaining
  int64 amount_remaining = 11;
  // billing reason
  string billing_reason = 12;
  // description
  string description = 13;
  // live mode
  bool livemode = 14;
  // payment intent
  string payment_intent = 15;
  // billing subscription id，注意此处 id 是billing库中的主键id
  int64 billing_subscription_id = 16;
}
