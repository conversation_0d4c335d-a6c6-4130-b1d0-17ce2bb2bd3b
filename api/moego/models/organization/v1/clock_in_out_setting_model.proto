syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// clock in/out setting model
message ClockInOutSettingModel {
  // enable clock in/out
  bool enable_clock_in_out = 1;
  // clock in/out notify
  bool clock_in_out_notify = 2;
  // enable clock in/out overnight
  bool enable_clock_in_out_overnight = 3;
}
