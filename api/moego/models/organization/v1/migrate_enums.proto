syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// migrate status enums
enum MigrateStatus {
  // UNSPECIFIED
  MIGRATE_STATUS_UNSPECIFIED = 0;
  // UN_MIGRATED
  UN_MIGRATED = 1;
  // MIGRATING
  MIGRATING = 2;
  // MIGRATED
  MIGRATED = 3;
}
