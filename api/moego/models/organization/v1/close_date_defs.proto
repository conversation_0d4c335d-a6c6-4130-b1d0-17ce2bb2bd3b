syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/close_date_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// definition of close date
message CloseDateDef {
  // id of close date config
  optional int64 id = 1 [(validate.rules).int64.gt = 0];
  // start date of close date
  string start_date = 2 [(validate.rules).string = {len: 10}];
  // end date of close date
  string end_date = 3 [(validate.rules).string = {len: 10}];
  // description of close date
  optional string description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 65535
  }];
  // type of close date
  optional CloseDateType type = 5;
}

// definition of closed holiday mass edit
message ClosedHolidayMassEditDef {
  // year of closed holiday mass edit
  string year = 1 [(validate.rules).string = {len: 4}];
  // close date list
  repeated CloseDateDef close_dates = 2;
}
