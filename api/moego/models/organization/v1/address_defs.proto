syntax = "proto3";

package moego.models.organization.v1;

import "google/type/latlng.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// definition of address
message AddressDef {
  // address 1
  optional string address1 = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // address 2
  optional string address2 = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // city
  optional string city = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // state
  optional string state = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // zip code
  optional string zipcode = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // country
  optional string country = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // latitude and longitude
  optional google.type.LatLng coordinate = 7;
}
