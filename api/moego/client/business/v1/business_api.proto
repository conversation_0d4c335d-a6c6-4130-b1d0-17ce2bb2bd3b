syntax = "proto3";

package moego.client.business.v1;

import "moego/models/business/v1/business_models.proto";
import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/business/v1;businessapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.business.v1";

// get business info request
message GetBusinessInfoRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get business info response
message GetBusinessInfoResponse {
  // business info
  moego.models.business.v1.BusinessModelClientView business = 1;
}

// The params message for ListBusinesses
message ListBusinessesParams {
  // The company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListBusinesses
message ListBusinessesResult {
  // Business list
  repeated moego.models.business.v1.BusinessModelClientView businesses = 1;
  // business ob config,key is business id
  map<int64, moego.models.online_booking.v1.BusinessOBConfigSimpleView> business_ob_configs = 2;
}

// business service
service BusinessService {
  // get business info
  rpc GetBusinessInfo(GetBusinessInfoRequest) returns (GetBusinessInfoResponse);

  // Get all the businesses of the companies with the current account
  // require c-side account session
  rpc ListBusinesses(ListBusinessesParams) returns (ListBusinessesResult);
}
