// @since 2024-06-03 11:55:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.client.enterprise.v1;

import "moego/models/branded_app/v1/branded_app_config_models.proto";
import "moego/models/branded_app/v1/branded_pack_config_models.proto";
import "moego/models/branded_app/v1/branded_theme_config_models.proto";
import "moego/models/online_booking/v1/booking_question_enums.proto";
import "moego/models/online_booking/v1/booking_question_models.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/organization/v1/location_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-Client-definitions/out/go/moego/client/enterprise/v1;enterpriseapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.enterprise.v1";

// The params message for GetBrandedAppConfig
message GetBrandedAppConfigParams {}

// The result message for GetBrandedAppConfig
message GetBrandedAppConfigResult {
  // The first screen config
  moego.models.branded_app.v1.BrandedAppConfigView config = 1;
  // the default theme config
  moego.models.branded_app.v1.BrandedThemeConfigModel default_theme = 2;
  // pack config view
  moego.models.branded_app.v1.BrandedPackConfigView pack_config = 3;
}

// The params message for ListCompanies
message ListCompaniesParams {
  // need list business
  optional bool list_business = 1;
}

// The result message for ListCompanies
message ListCompaniesResult {
  // The company list
  repeated moego.models.organization.v1.CompanyBriefView companies = 1;
  // business list
  message BusinessList {
    // business
    repeated moego.models.organization.v1.LocationBriefView businesses = 1;
  }
  // company business list
  map<int64, BusinessList> company_to_businesses = 2;
}

// The params message for ListBrandedQuestions
message ListBrandedQuestionsParams {
  // Selected company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // question apply type
  moego.models.online_booking.v1.BookingQuestionApplyType type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The result message for ListBrandedQuestions
message ListBrandedQuestionsResult {
  // The list of account profile question
  repeated moego.models.online_booking.v1.BookingQuestionModel questions = 1;
}

// the enterprise service
service EnterpriseService {
  // Get the branded app config
  rpc GetBrandedAppConfig(GetBrandedAppConfigParams) returns (GetBrandedAppConfigResult);

  // Get company list
  rpc ListCompanies(ListCompaniesParams) returns (ListCompaniesResult);

  // List questions
  rpc ListBrandedQuestions(ListBrandedQuestionsParams) returns (ListBrandedQuestionsResult);
}
