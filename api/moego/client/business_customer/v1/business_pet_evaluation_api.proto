syntax = "proto3";

package moego.client.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_evaluation_models.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.business_customer.v1";

// ListPetEvaluationParams
message ListPetEvaluationParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
}

// ListPetEvaluationResult
message ListPetEvaluationResult {
  // pet evaluations
  repeated moego.models.business_customer.v1.PetEvaluationModel pet_evaluations = 1;
  //evaluation view
  repeated moego.models.offering.v1.EvaluationView evaluations = 2;
}

// check evaluation for services
message CheckEvaluationForServicesParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // service ids
  repeated int64 service_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

// CheckEvaluationForServicesResult
message CheckEvaluationForServicesResult {
  // evaluation result
  enum EvaluationResult {
    // unspecified evaluation result
    EVALUATION_RESULT_UNSPECIFIED = 0;
    // evaluation is required and not passed
    REQUIRED = 1;
    // evaluation is required and passed
    PASSED = 2;
    // evaluation is not required
    NOT_REQUIRED = 3;
  }
  // service to evaluation result map
  map<int64, EvaluationResult> service_evaluation_results = 1;
}

// API for business pet evaluation
service BusinessPetEvaluationService {
  // List pet evaluation
  rpc ListPetEvaluation(ListPetEvaluationParams) returns (ListPetEvaluationResult);
  // Check evaluation for services
  rpc CheckEvaluationForServices(CheckEvaluationForServicesParams) returns (CheckEvaluationForServicesResult);
}
