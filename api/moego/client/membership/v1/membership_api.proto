// @since 2024-08-15 11:59:45
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.client.membership.v1;

import "moego/models/business_customer/v1/business_customer_defs.proto";
import "moego/models/business_customer/v1/business_customer_pet_defs.proto";
import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/sell_link_models.proto";
import "moego/models/online_booking/v1/booking_availability_defs.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.membership.v1";

// the membership service
service MembershipService {
  // list membership
  rpc ListMemberships(ListMembershipsParams) returns (ListMembershipsResult);
  // create sell link
  rpc CreateSellLink(CreateSellLinkParams) returns (CreateSellLinkResult);
  // list membership for sale
  rpc ListMembershipsForSale(ListMembershipsForSaleParams) returns (ListMembershipsForSaleResult);
  // create sell link for branded app
  rpc CreateSellLinkForApp(CreateSellLinkForAppParams) returns (CreateSellLinkForAppResult);
  // list membership for sale for branded app
  rpc ListMembershipsForSaleForApp(ListMembershipsForSaleForAppParams) returns (ListMembershipsForSaleForAppResult);
  // list memberships without not applicable service perks
  rpc ListMembershipsWithoutNotApplicableServicePerks(ListMembershipsWithoutNotApplicableServicePerksParams) returns (ListMembershipsWithoutNotApplicableServicePerksResult);
}

// list membership params
message ListMembershipsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // filter by status
  optional moego.models.membership.v1.MembershipModel.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // name like, case insensitive
  optional string name_like = 4 [(validate.rules).string = {max_len: 50}];
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 5;
}

// list membership result
message ListMembershipsResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// create sell link params
message CreateSellLinkParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // membership id
  int64 membership_id = 3 [(validate.rules).int64 = {gt: 0}];
  // membership revision
  optional int32 membership_revision = 4 [(validate.rules).int32 = {gt: 0}];
  // customer with pet info (only for new customer)
  message CustomerWithPetInfo {
    // customer with additional info
    moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef customer_with_additional_info = 1 [(validate.rules).message = {required: true}];

    // pets with additional info
    repeated moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef pets_with_additional_info = 2 [(validate.rules).repeated = {
      max_items: 20
      items: {
        message: {required: true}
      }
    }];
  }
  // customer with pet info
  optional CustomerWithPetInfo customer_with_pet_info = 5;
}

// create sell link response
message CreateSellLinkResult {
  // link
  moego.models.membership.v1.SellLinkModel sell_link = 1;
  // uuid
  string public_token = 2;
}

// list membership for sales params
message ListMembershipsForSaleParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // filter by status
  optional moego.models.membership.v1.MembershipModel.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // name like, case insensitive
  optional string name_like = 4 [(validate.rules).string = {max_len: 50}];

  //filter
  message Filter {
    // selected pet
    repeated models.online_booking.v1.BookingPetDef pets = 1;
  }
  // filter
  optional Filter filter = 5;
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list memberships for sales result
message ListMembershipsForSaleResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create sell link params
message CreateSellLinkForAppParams {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // membership id
  int64 membership_id = 3 [(validate.rules).int64 = {gt: 0}];
  // membership revision
  optional int32 membership_revision = 4 [(validate.rules).int32 = {gt: 0}];
  // customer with pet info (only for new customer)
  message CustomerWithPetInfo {
    // customer with additional info
    moego.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef customer_with_additional_info = 1 [(validate.rules).message = {required: true}];

    // pets with additional info
    repeated moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef pets_with_additional_info = 2 [(validate.rules).repeated = {
      max_items: 20
      items: {
        message: {required: true}
      }
    }];
  }
  // customer with pet info
  optional CustomerWithPetInfo customer_with_pet_info = 5;
}

// create sell link response
message CreateSellLinkForAppResult {
  // link
  moego.models.membership.v1.SellLinkModel sell_link = 1;
  // uuid
  string public_token = 2;
}

// list membership for sales for branded app params
message ListMembershipsForSaleForAppParams {
  // company id
  int64 company_id = 1;

  // filter by status
  optional moego.models.membership.v1.MembershipModel.Status status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // name like, case insensitive
  optional string name_like = 3 [(validate.rules).string = {max_len: 50}];

  //filter
  message Filter {
    // selected pet
    repeated models.online_booking.v1.BookingPetDef pets = 1;
  }
  // filter
  optional Filter filter = 4;
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list memberships for sales for app result
message ListMembershipsForSaleForAppResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// for app, list memberships without not applicable service perks
message ListMembershipsWithoutNotApplicableServicePerksParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list memberships without not applicable service perks result
message ListMembershipsWithoutNotApplicableServicePerksResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}
