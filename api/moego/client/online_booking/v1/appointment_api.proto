syntax = "proto3";

package moego.client.online_booking.v1;

import "google/type/date.proto";
import "moego/api/offering/v1/group_class_api.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// The params message for GetPriorityAppointmentCard
message GetPriorityAppointmentCardParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// The result message for GetPriorityAppointmentCard
message GetPriorityAppointmentCardResult {
  // appointment card item
  AppointmentCardItem card = 1;

  // priority appointment card item count
  int32 count = 2;
}

// The appointment card item message
message AppointmentCardItem {
  // appointment card type
  AppointmentCardType card_type = 1;

  // appointment summary item
  AppointmentSummaryItem appointment = 2;

  // pet and services summary item
  repeated PetAndServicesSummaryItem pet_and_services = 3;
}

// The enum for appointment card type
enum AppointmentCardType {
  // unspecified
  APPOINTMENT_CARD_TYPE_UNSPECIFIED = 0;
  // The last finished appointment
  // status = finished and end_date < today
  LAST = 2;
  // The pending appointment
  PENDING = 4;
  // The upcoming appointment
  // status in (unconfirmed, confirmed, checked_in, ready)
  // upcoming: today < start_date
  // today: start_date < today < end_date
  // delayed: today > end_date
  UPCOMING = 6;
  // The in progress appointment
  // status = checked_in
  IN_PROGRESS = 8;
}

// The appointment summary item message
message AppointmentSummaryItem {
  // The booking request id or appointment id
  oneof id {
    // The booking request id
    int64 booking_request_id = 1;
    // The appointment id
    int64 appointment_id = 2;
  }
  // The appointment start date
  optional string start_date = 3;
  // The appointment start time
  optional int32 start_time = 4;
  // The appointment end date
  optional string end_date = 5;
  // The appointment end time
  optional int32 end_time = 6;
  // The main care type
  moego.models.offering.v1.ServiceItemType main_care_type = 7;
  // The flag of is booking request
  bool is_booking_request = 8;
}

// The pet and service summary item
message PetAndServicesSummaryItem {
  // pet
  PetItem pet = 1;
  // services
  repeated ServiceItem services = 2;

  // The pet item
  message PetItem {
    // The pet id
    int64 id = 1;
    // The pet name
    string pet_name = 2;
    // The pet type
    moego.models.customer.v1.PetType pet_type = 3;
    // The avatar path
    string avatar_path = 4;
  }

  // The service item
  message ServiceItem {
    // The service id
    int64 id = 1;
    // The service name
    string service_name = 2;
    // The care type
    moego.models.offering.v1.ServiceItemType care_type = 3;
    // The service start date
    optional google.type.Date start_date = 4;
    // The service start time
    optional int32 start_time = 5;
    // The service end date
    optional google.type.Date end_date = 6;
    // The service end time
    optional int32 end_time = 7;
  }
}

// The appointment payment item message
message AppointmentPaymentItem {
  // The estimated total price
  double estimated_total_price = 1;
  // The total amount
  double total_amount = 2;
  // The payment status
  moego.models.appointment.v1.AppointmentPaymentStatus payment_status = 3;
  // The estimated total price for evaluation
  double evaluation_estimated_total_price = 4;
}

// The params message for ListAppointments
message ListAppointmentsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // The filter
  Filter filter = 3;

  // The pagination request
  moego.utils.v2.PaginationRequest pagination = 4;

  // sort by
  repeated AppointmentSortDef sorts = 5;

  // The filter message
  message Filter {
    // The appointment type
    AppointmentType appointment_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }

  // The appointment type enum
  enum AppointmentType {
    // unspecified
    APPOINTMENT_TYPE_UNSPECIFIED = 0;
    // The pending type
    PENDING = 1;
    // The upcoming type
    UPCOMING = 2;
    // The past type
    PAST = 3;
    // The canceled type
    CANCELED = 4;
  }

  // appointment list sort definition
  message AppointmentSortDef {
    // sort field
    AppointmentSortField field = 1;
    // sort asc or desc
    bool asc = 2;
  }

  // appointment list sort field
  enum AppointmentSortField {
    // unspecified
    APPOINTMENT_SORT_FIELD_UNSPECIFIED = 0;
    // appointment date and time
    DATE_TIME = 1;
  }
}

// The result message for ListAppointments
message ListAppointmentsResult {
  // Appointments
  repeated AppointmentListItem appointments = 1;

  // The pagination response
  moego.utils.v2.PaginationResponse pagination = 2;

  // The appointment list item message
  message AppointmentListItem {
    // appointment summary item
    AppointmentSummaryItem appointment = 1;

    // pet and services summary item
    repeated PetAndServicesSummaryItem pet_and_services = 2;

    // payment info
    AppointmentPaymentItem payment = 3;
  }
}

// The params message for GetAppointmentDetail
message GetAppointmentDetailParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }
}

// The result message for GetAppointmentDetail
message GetAppointmentDetailResult {
  // appointment detail item
  AppointmentItem appointment = 1;
  // pet and services detail item
  repeated PetAndServicesDetailItem pet_and_services = 2;
  // payment detail item
  PaymentItem payment = 3;

  // The appointment item message
  message AppointmentItem {
    // The appointment id or booking request id
    oneof id {
      // The appointment id
      int64 appointment_id = 1;
      // The booking request id
      int64 booking_request_id = 2;
    }
    // The appointment start date, boarding/daycare arrive date
    string start_date = 3;
    // The appointment start time, boarding/daycare arrive time
    int32 start_time = 4;
    // The appointment end date, boarding/daycare pickup date
    string end_date = 5;
    // The appointment end time, boarding/daycare pickup time
    int32 end_time = 6;
    // The main care type
    moego.models.offering.v1.ServiceItemType main_care_type = 7;
    // The flag of is booking request
    bool is_booking_request = 8;
    // The check-in and check-out date time
    // There is only one check-in and check-out time for boarding, grooming and evaluation
    // There are multiple check-in and check-out time for daycare
    repeated CheckInOutDateTime check_in_out_date_times = 9;
    // appointment status. For appointment only
    optional models.appointment.v1.AppointmentStatus appointment_status = 10;
  }

  // The check-in and check-out date time message
  message CheckInOutDateTime {
    // The check-in date
    string check_in_date = 1;
    // The check-in time
    int32 check_in_time = 2;
    // The check-out date
    string check_out_date = 3;
    // The check-out time
    int32 check_out_time = 4;
  }

  // The pet and service detail item
  message PetAndServicesDetailItem {
    // pet
    PetItem pet = 1;
    // services detail items
    repeated ServiceDetailItem services = 2;
    // add-ons detail items
    repeated AddOnDetailItem add_ons = 3;
  }

  // The pet item
  message PetItem {
    // The pet id
    int64 id = 1;
    // The pet name
    string pet_name = 2;
    // The pet type
    moego.models.customer.v1.PetType pet_type = 3;
    // The pet breed
    string breed = 4;
    // The avatar path
    string avatar_path = 5;
    // vet name
    string vet_name = 6;
    // vet phone number
    string vet_phone_number = 7;
    // vet address
    string vet_address = 8;
    // emergency contact name
    string emergency_contact_name = 9;
    // emergency contact phone number
    string emergency_contact_phone_number = 10;
  }

  // The service detail item message
  message ServiceDetailItem {
    // The service id
    int64 id = 1;
    // The service name
    string service_name = 2;
    // The care type
    moego.models.offering.v1.ServiceItemType care_type = 3;
    // pet detail id
    int64 pet_detail_id = 4;
    // The add-on date type
    moego.models.appointment.v1.PetDetailDateType date_type = 5;
    // The specific dates
    repeated string specific_dates = 6;
    // The service start date
    optional string start_date = 7;
    // The service start time
    optional int32 start_time = 8;
    // The service end date
    optional string end_date = 9;
    // The service end time
    optional int32 end_time = 10;

    // feeding instruction
    repeated moego.models.appointment.v1.AppointmentPetFeedingScheduleDef feedings = 11;

    // medication instruction
    repeated moego.models.appointment.v1.AppointmentPetMedicationScheduleDef medications = 12;
    // max duration (only for daycare service)
    optional int32 max_duration = 13;
    // service time, in minutes
    optional int32 service_time = 14;
  }

  // The add-on detail item message
  message AddOnDetailItem {
    // The add-on id
    int64 id = 1;
    // The add-on name
    string add_on_name = 2;
    // The add-on date type
    moego.models.appointment.v1.PetDetailDateType date_type = 3;
    // The specific dates
    repeated string specific_dates = 4;
    // The care type
    moego.models.offering.v1.ServiceItemType care_type = 5;
    // pet detail id
    int64 pet_detail_id = 6;
    // The service start time
    optional int32 start_time = 7;
    // The service end time
    optional int32 end_time = 8;
    // quantity per day
    optional int32 quantity_per_day = 9;
    // service time, in minutes
    optional int32 service_time = 10;
    // if require dedicated staff
    bool require_dedicated_staff = 11;
    // start date
    // dateType 为 PET_DETAIL_DATE_DATE_POINT 时，start_date 有值
    optional string start_date = 12;
  }

  // The payment item
  message PaymentItem {
    // The estimated total price
    double estimated_total_price = 1;
    // The total amount
    double total_amount = 2;
    // The payment status
    moego.models.appointment.v1.AppointmentPaymentStatus payment_status = 3;
    // paid amount
    optional double paid_amount = 4;
    // pre pay amount
    optional double pre_pay_amount = 5;
    // pre auth enable
    optional bool pre_auth_enable = 6;
    // The estimated total price for evaluation
    double evaluation_estimated_total_price = 7;
  }
}

// the params message for GetTrainingDetail
message GetGroupClassDetailParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }
}

// the result message for GetTrainingDetail
message GetGroupClassDetailResult {
  // pet
  GetAppointmentDetailResult.PetItem pet = 1;
  // instance
  api.offering.v1.GroupClassInstanceView group_class_instance = 2;
  // class
  models.offering.v1.ServiceModel service_model = 3;
}

// Update pet feeding medication params
message ReschedulePetFeedingMedicationParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }

  // Pet's schedules
  repeated PetScheduleDef schedules = 5 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pet's feeding and medication schedules
  message PetScheduleDef {
    // pet detail id
    int64 pet_detail_id = 1 [(validate.rules).int64 = {gt: 0}];

    // The care type
    moego.models.offering.v1.ServiceItemType care_type = 2 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];

    // feeding instruction
    repeated moego.models.appointment.v1.AppointmentPetFeedingScheduleDef feedings = 3 [(validate.rules).repeated = {
      min_items: 0
      max_items: 100
      items: {
        message: {required: true}
      }
    }];

    // medication instruction
    repeated moego.models.appointment.v1.AppointmentPetMedicationScheduleDef medications = 4 [(validate.rules).repeated = {
      min_items: 0
      max_items: 100
      items: {
        message: {required: true}
      }
    }];
  }
}

// Update pet feeding medication result
message ReschedulePetFeedingMedicationResult {}

// The params message for CancelAppointment
message CancelAppointmentParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }
}

// The result message for CancelAppointment
message CancelAppointmentResult {}

// The params message for UpdateAppointment
message UpdateAppointmentParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }

  // The params message for UpdatePetServiceDetail
  repeated UpdatePetServiceDetailParams pet_and_services = 5;

  // The params message for UpdatePetServiceDetail
  message UpdatePetServiceDetailParams {
    // pet id
    int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
    // the params message for UpdateServiceDetail
    repeated UpdateServiceDetailParams services = 2;
    // the params message for UpdateAddOnDetail
    repeated UpdateAddOnDetailParams add_ons = 3;
  }

  // The service detail item message
  message UpdateServiceDetailParams {
    // The care type. Non-updatable fields
    moego.models.offering.v1.ServiceItemType care_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // pet detail id. Non-updatable fields
    int64 pet_detail_id = 2 [(validate.rules).int64 = {gt: 0}];
    // The add-on date type. It is required for update date/time
    optional moego.models.appointment.v1.PetDetailDateType date_type = 3 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // The specific dates. Valid only when date_type is PET_DETAIL_DATE_SPECIFIC_DATE
    repeated string specific_dates = 4 [(validate.rules).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];
    // The service start date
    optional string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
    // The service start time
    optional int32 start_time = 6 [(validate.rules).int32 = {
      gte: 0
      lt: 1440
    }];
    // The service end date
    optional string end_date = 7 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
    // The service end time
    optional int32 end_time = 8 [(validate.rules).int32 = {
      gte: 0
      lt: 1440
    }];
    // quantity per day
    optional int32 quantity_per_day = 9 [(validate.rules).int32 = {gte: 0}];
  }

  // The add-on detail item message
  message UpdateAddOnDetailParams {
    // The care type. Non-updatable fields
    moego.models.offering.v1.ServiceItemType care_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // pet detail id. Non-updatable fields
    int64 pet_detail_id = 2 [(validate.rules).int64 = {gt: 0}];
    // The add-on date type. It is required for update date/time
    optional moego.models.appointment.v1.PetDetailDateType date_type = 3 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // The specific dates. Valid only when date_type is valid
    repeated string specific_dates = 4 [(validate.rules).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];
    // The service start time
    optional int32 start_time = 5 [(validate.rules).int32 = {
      gte: 0
      lt: 1440
    }];
    // The service end time
    optional int32 end_time = 6 [(validate.rules).int32 = {
      gte: 0
      lt: 1440
    }];
    // quantity per day
    optional int32 quantity_per_day = 7 [(validate.rules).int32 = {gte: 0}];
    // start date
    // dateType 为 PET_DETAIL_DATE_DATE_POINT 时，start_date 有值
    optional string start_date = 8 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  }
}

// The result message for UpdateAppointment
message UpdateAppointmentResult {}

// check if dates available for reschedule request
message IsAvailableForRescheduleParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // appointment id or booking request id
  oneof id {
    // The appointment id
    int64 appointment_id = 3;

    // The booking request id
    int64 booking_request_id = 4;
  }
  // selected service item type
  models.offering.v1.ServiceItemType service_item_type = 5 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // date from
  google.type.Date start_date = 6;
  // date to
  google.type.Date end_date = 7;
  // do reschedule for this service id
  optional int64 service_id = 8 [(validate.rules).int64 = {gt: 0}];
}

// check if dates available for reschedule result
message IsAvailableForRescheduleResult {
  // is available
  bool is_available = 1;
}

// list evaluations params
message ListEvaluationsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // The pagination request
  moego.utils.v2.PaginationRequest pagination = 3;
}

// list evaluations results
message ListEvaluationsResult {
  // Appointments
  repeated AppointmentSummaryItem appointments = 1;

  // The pagination response
  moego.utils.v2.PaginationResponse pagination = 2;
}

// The appointment service
service AppointmentService {
  // Get priority appointment card
  // IN_PROGRESS > UPCOMING > PENDING > LAST
  rpc GetPriorityAppointmentCard(GetPriorityAppointmentCardParams) returns (GetPriorityAppointmentCardResult);

  // List appointments by filter
  rpc ListAppointments(ListAppointmentsParams) returns (ListAppointmentsResult);

  // Get appointment detail by id
  rpc GetAppointmentDetail(GetAppointmentDetailParams) returns (GetAppointmentDetailResult);
  // Get training class batch detail by id
  rpc GetGroupClassDetail(GetGroupClassDetailParams) returns (GetGroupClassDetailResult);

  // Update pet feeding and medication
  rpc ReschedulePetFeedingMedication(ReschedulePetFeedingMedicationParams) returns (ReschedulePetFeedingMedicationResult);

  // Cancel appointment
  rpc CancelAppointment(CancelAppointmentParams) returns (CancelAppointmentResult);

  // Update appointment
  rpc UpdateAppointment(UpdateAppointmentParams) returns (UpdateAppointmentResult);

  // Check if available for reschedule
  rpc IsAvailableForReschedule(IsAvailableForRescheduleParams) returns (IsAvailableForRescheduleResult);

  // List evaluations
  rpc ListEvaluations(ListEvaluationsParams) returns (ListEvaluationsResult);
}
