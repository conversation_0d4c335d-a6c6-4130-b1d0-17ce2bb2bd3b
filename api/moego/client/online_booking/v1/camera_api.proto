syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/offering/v1/lodging_unit_models.proto";
import "moego/models/organization/v1/camera_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// get camera list params
message GetCameraListParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get camera list result
message GetCameraListResult {
  // camera list
  repeated CameraView cameras = 1;
  //camera view
  message CameraView {
    // camera info
    models.organization.v1.CameraModel camera = 1;
    // id of the lodging unit
    optional models.offering.v1.LodgingUnitView lodging_unit = 2;
  }
}

// camera service
service CameraService {
  // get camera list
  rpc GetCameraList(GetCameraListParams) returns (GetCameraListResult);
}
