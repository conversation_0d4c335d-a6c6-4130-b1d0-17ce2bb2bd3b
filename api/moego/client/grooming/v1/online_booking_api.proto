syntax = "proto3";

package moego.client.grooming.v1;

import "google/type/latlng.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/grooming/v1/category_models.proto";
import "moego/models/grooming/v1/customer_service_models.proto";
import "moego/models/grooming/v1/service_models.proto";
import "moego/models/online_booking/v1/available_defs.proto";
import "moego/models/online_booking/v1/ob_config_enums.proto";
import "moego/models/online_booking/v1/selected_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v1";

// select available pet list request
message GetAvailablePetListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// select available pet list response
message GetAvailablePetListResponse {
  // online booking available pet list
  repeated moego.models.business_customer.v1.BusinessCustomerPetModelClientView business_pets = 1;
  // pet's available meta data
  repeated moego.models.online_booking.v1.AvailablePetMetadataDef available_pets = 2;
}

// select service list request
message GetAvailableServiceListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // selected pet id list
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 10
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// select available service list response
message GetAvailableServiceListResponse {
  // category list
  repeated moego.models.grooming.v1.CategorySelectServiceView categories = 1;
  // category with service list
  repeated moego.models.grooming.v1.ServiceModelSelectServiceView services = 2;
  // customer's custom service list
  repeated moego.models.grooming.v1.CustomerServiceModelSelectServiceView customer_services = 3;
  // pet's applicable service list
  repeated moego.models.online_booking.v1.AvailableServiceDef available_services = 4;
}

// select available staff list request
message GetAvailableStaffListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
}

// select available staff list response
message GetAvailableStaffListResponse {
  // available staff list
  repeated moego.models.online_booking.v1.AvailableStaffDef available_staffs = 1;
}

// get the first available date request
message GetFirstAvailableDateRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // selected staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // start search date
  optional string start_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // selected address id, default is primary address
  optional int64 address_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get the first available date response
message GetFirstAvailableDateResponse {
  // staff's first available date list
  repeated moego.models.online_booking.v1.AvailableStaffDateDef available_dates = 1;
}

// select available date list request
message GetAvailableDateListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // selected staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // selected date
  string date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // search date range
  oneof search_date_range {
    option (validate.required) = true;
    // search the specified number of days starting from the first available day
    int32 search_days = 5;
    // search from the first available day to the end of the month
    bool is_end_of_month = 6;
  }

  // selected address id, default is primary address
  optional int64 address_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// select available date list response
message GetAvailableDateListResponse {
  // available date list
  repeated moego.models.online_booking.v1.AvailableDateDef available_dates = 1;
}

// get the available timeslot on a specified day request
message GetAvailableTimeslotRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // selected staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // selected date
  string date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // selected address id, default is primary address
  optional int64 address_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get the available timeslot on a specified day response
message GetAvailableTimeslotResponse {
  // available timeslot list
  repeated moego.models.online_booking.v1.AvailableTimeslotDef available_timeslots = 1;
}

// can book online request
message CanBookOnlineRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// can book online response
message CanBookOnlineResponse {
  // can book online
  bool is_can_book_online = 1;
  // is book online enable
  bool is_book_online_enable = 2;
  // is client type accepted
  bool is_accepted_client_type = 3;
  // is blocked online booking
  bool is_blocked_online_booking = 4;
  // primary address out of service area
  bool is_out_of_service_area = 5;
  // has primary address
  bool is_has_primary_address = 6;
  // is need address
  bool is_need_address = 7;
  // is allowed simplify submit
  bool is_allowed_simplify_submit = 8;
  // is check existing client's address
  bool is_check_existing_client = 9;
}

// get staff working hour range list request
message GetStaffWorkingHourRangeListRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // selected staff id list
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // start date
  string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// get staff working hour range list response
message GetStaffWorkingHourRangeListResponse {
  // staff working hour range list
  repeated moego.models.online_booking.v1.AvailableStaffWorkingHourRangeDef staffs = 1;
}

// get book online address request
message GetBookOnlineAddressRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get book online address response
message GetBookOnlineAddressResponse {
  // primary address
  moego.models.business_customer.v1.BusinessCustomerAddressModelClientView primary_address = 1;
}

// list book online addresses params
message ListBookOnlineAddressesParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// list book online addresses result
message ListBookOnlineAddressesResult {
  // The list of account address.
  repeated moego.models.business_customer.v1.BusinessCustomerAddressView addresses = 1;
  // address extra info
  repeated AddressExtraInfo extra_infos = 2;

  // AddressExtraInfo is the extra info for address.
  message AddressExtraInfo {
    // address id
    int64 address_id = 1;
    // require address & out of service area: false
    // no require address: true
    bool is_available = 2;
  }
}

// The params message for the CheckBookOnlineAddress
message CheckBookOnlineAddressParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // address coordinate
  google.type.LatLng coordinate = 2 [(validate.rules).message = {required: true}];
  // zipcode
  string zipcode = 3 [(validate.rules).string = {max_len: 10}];
}

// The result message for the CheckBookOnlineAddress
message CheckBookOnlineAddressResult {
  // is available
  bool is_available = 1;
}

// The params message for the GetBookingStatus
message GetBookingStatusParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for the GetBookingStatus
message GetBookingStatusResult {
  // can book online
  bool is_can_book_online = 1;
}

// The params message for the GetEstimatedPaymentInfo
message GetEstimatedPaymentInfoParams {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 10
    items: {
      message: {required: true}
    }
  }];
  // discount code
  optional string discount_code = 3 [(validate.rules).string = {max_len: 20}];
}

// The result message for the GetEstimatedPaymentInfo
message GetEstimatedPaymentInfoResult {
  // book online payment
  moego.models.online_booking.v1.PaymentType payment_type = 1;
  // prepay type
  models.online_booking.v1.PrepayType prepay_type = 2;
  // subtotal, service total + service charge
  double subtotal = 3;
  // tax and fees, tax + (convenience fee + booking fee)
  double tax_and_fees = 4;
  // discount amount, all payment type support
  double discount = 6;
  // total amount, subtotal + tax + fees - discount, exclude tip
  double total = 7;
  // deposit amount, prepayment deposit only
  optional double deposit = 8;
  // subtotal item
  SubtotalItem subtotal_item = 9;

  // subtotal item. service & add-ons, service charges
  message SubtotalItem {
    // Service items
    repeated ServiceItem service_items = 1;
    // Service charge items
    repeated ServiceChargeItem service_charge_items = 2;
  }

  // service item. service & add-ons
  message ServiceItem {
    // service id
    int64 id = 1;
    // service name
    string name = 2;
    // quantity
    int32 quantity = 3;
    // unit price
    double unit_price = 4;
  }

  // service charge item.
  message ServiceChargeItem {
    // service charge id
    int64 id = 1;
    // service charge name
    string name = 2;
    // quantity
    int32 quantity = 3;
    // unit price
    double unit_price = 4;
  }
}

// booking service, required c-side account session
service BookingService {
  // select pet list, filter deleted and passed away pet
  rpc GetAvailablePetList(GetAvailablePetListRequest) returns (GetAvailablePetListResponse);
  // select service list
  rpc GetAvailableServiceList(GetAvailableServiceListRequest) returns (GetAvailableServiceListResponse);
  // select professional list
  rpc GetAvailableStaffList(GetAvailableStaffListRequest) returns (GetAvailableStaffListResponse);
  // get the first available date
  rpc GetFirstAvailableDate(GetFirstAvailableDateRequest) returns (GetFirstAvailableDateResponse);
  // get available date list
  rpc GetAvailableDateList(GetAvailableDateListRequest) returns (GetAvailableDateListResponse);
  // get the timeslot on a specified day
  rpc GetAvailableTimeslot(GetAvailableTimeslotRequest) returns (GetAvailableTimeslotResponse);
  // can book online
  rpc CanBookOnline(CanBookOnlineRequest) returns (CanBookOnlineResponse);
  // select staff working hour range list
  rpc GetStaffWorkingHourRangeList(GetStaffWorkingHourRangeListRequest) returns (GetStaffWorkingHourRangeListResponse);
  // get book online address, primary address
  rpc GetBookOnlineAddress(GetBookOnlineAddressRequest) returns (GetBookOnlineAddressResponse);
  // list book online addresses
  rpc ListBookOnlineAddresses(ListBookOnlineAddressesParams) returns (ListBookOnlineAddressesResult);

  // check book online address
  rpc CheckBookOnlineAddress(CheckBookOnlineAddressParams) returns (CheckBookOnlineAddressResult);

  // Get the status of online booking
  // Branded apps always ask for an address on mobile grooming
  // If client's address out of service area list: ignore the following two configurations
  //  Allow all clients to submit request without date and time.
  //  Allow existing clients to submit request with date and time.
  rpc GetBookingStatus(GetBookingStatusParams) returns (GetBookingStatusResult);

  // Get the estimated payment info
  // No Payment:
  //    Subtotal, Tax and fees, Discount(optional), Total
  // CoF:
  //    Subtotal, Tax and fees, Discount(optional), Total
  // Pre-auth:
  //    Subtotal, Tax and fees, Tip(optional), Discount(optional), Total
  // Prepayment(full amount):
  //    Subtotal, Tax and fees, Tip(optional), Discount(optional), Total
  // Prepayment(deposit):
  //    Subtotal, Tax and fees, Discount(optional), Total, Deposit
  // Subtotal = service total + service charge
  // Tax and fees = tax + (convenience fee + booking fee)
  // Tax = service tax + service charge tax
  // Fees = convenience fee + booking fee
  // Convenience fee(customizable) = (subtotal + tax) * 3.4% + 100cents
  // Booking fee(prepayment only) = 1 to 3
  // Tip(customizable) = front-end computing. fixed amount or subtotal percentage
  // Discount(customizable) = fixed amount or subtotal percentage
  // Total = subtotal + tax + fees - discount
  rpc GetEstimatedPaymentInfo(GetEstimatedPaymentInfoParams) returns (GetEstimatedPaymentInfoResult);
}
