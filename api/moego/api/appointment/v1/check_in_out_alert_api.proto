syntax = "proto3";

package moego.api.appointment.v1;

import "moego/models/appointment/v1/check_in_out_alert_defs.proto";
import "moego/models/appointment/v1/check_in_out_alert_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// CheckInOutAlertService
service CheckInOutAlertService {
  // get check-in-out alter settings
  rpc GetAlertSettings(GetAlertSettingsParams) returns (GetAlertSettingsResult);

  // save check-in-out alter settings
  rpc SaveAlertSettings(SaveAlertSettingsParams) returns (SaveAlertSettingsResult);

  // batch get alter detail for check in
  rpc BatchGetAlertsForCheckIn(BatchGetAlertsForCheckInParams) returns (BatchGetAlertsForCheckInResult);

  // get alter detail for check in
  rpc GetAlertsForCheckIn(GetAlertsForCheckInParams) returns (GetAlertsForCheckInResult);

  // get alter detail for check out
  rpc GetAlertsForCheckOut(GetAlertsForCheckOutParams) returns (GetAlertsForCheckOutResult);
}

// GetAlertSettingsParams
message GetAlertSettingsParams {}

// GetAlertSettingsResult
message GetAlertSettingsResult {
  // check in/out alerts settings
  moego.models.appointment.v1.CheckInOutAlertSettings settings = 1;
}

// SaveAlertSettingsParams
message SaveAlertSettingsParams {
  // check in alert settings
  optional moego.models.appointment.v1.CheckInAlertSettings check_in_settings = 1;
  // check out alert settings
  optional moego.models.appointment.v1.CheckOutAlertSettings check_out_settings = 2;
}

// SaveAlertSettingsResult
message SaveAlertSettingsResult {
  // check in/out alerts settings
  moego.models.appointment.v1.CheckInOutAlertSettings settings = 1;
}

// GetAlertsForCheckInParams
message GetAlertsForCheckInParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// GetAlertsForCheckInResult
message GetAlertsForCheckInResult {
  // check in alert
  optional moego.models.appointment.v1.AlertDetail alert = 1;
}

// GetAlertsForCheckOutParams
message GetAlertsForCheckOutParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// GetAlertsForCheckOutResult
message GetAlertsForCheckOutResult {
  // check out alert
  optional moego.models.appointment.v1.AlertDetail alert = 1;
}

// BatchGetAlertsForCheckInParams
message BatchGetAlertsForCheckInParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // client and pet ids
  repeated moego.models.appointment.v1.ClientPetsMapping client_pets = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// BatchGetAlertsForCheckInResult
message BatchGetAlertsForCheckInResult {
  // client and pet alerts
  repeated moego.models.appointment.v1.AlertDetail alerts = 1;
}
