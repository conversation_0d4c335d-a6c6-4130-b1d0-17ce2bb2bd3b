syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_pet_schedule_defs.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// Reschedule All-in-One service params
message RescheduleAllInOneServiceParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and services schedule list
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Reschedule All-in-One service result
message RescheduleAllInOneServiceResult {}

// Reschedule boarding service params
message RescheduleBoardingServiceParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and lodging schedule
  repeated models.appointment.v1.BoardingServiceScheduleDef boarding_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];
}

// Reschedule boarding service result
message RescheduleBoardingServiceResult {
  // conflict service names, if there are no conflicts, the list is empty
  repeated string conflict_service_names = 1;
}

// Reschedule grooming service params
message RescheduleGroomingServiceParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // card ids corresponding to different types of cards
  // APPOINTMENT: appt id
  // SERVICE: pet detail id
  // OPERATION: multi-staff service operation id
  // BLOCK: appt id
  // BOOKING_REQUEST: booking request id
  optional int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // card type
  moego.models.appointment.v1.CalendarCardType card_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // rescheduled staff id, if not specified, the original staff id will be inherited.
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // rescheduled start date, in the format of "YYYY-MM-DD"
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // rescheduled start time, in minutes
  int32 start_time = 6 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // rescheduled end time, in minutes
  optional int32 end_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // rescheduled pet detail ids, when card type is SERVICE
  repeated int64 pet_detail_ids = 8 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
  // The scope of impact of this modification on repeat appointments
  optional moego.models.appointment.v1.RepeatAppointmentModifyScope repeat_type = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Reschedule grooming service result
message RescheduleGroomingServiceResult {}

// Reschedule evaluation service params
message RescheduleEvaluationServiceParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and lodging schedule
  repeated models.appointment.v1.EvaluationServiceScheduleDef evaluation_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];
}

// Reschedule evaluation service result
message RescheduleEvaluationServiceResult {}

// Lodging assign params
message LodgingAssignParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging for services to be assigned
  repeated models.appointment.v1.ServiceLodgingAssignDef services = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
  // lodging for evaluations to be assigned
  repeated models.appointment.v1.ServiceLodgingAssignDef evaluations = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Lodging assign result
message LodgingAssignResult {}

// Get pet feeding and medication schedules params
message GetPetFeedingMedicationSchedulesParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get pet feeding and medication schedules result
message GetPetFeedingMedicationSchedulesResult {
  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 1 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Update pet feeding medication params
message ReschedulePetFeedingMedicationParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Update pet feeding medication result
message ReschedulePetFeedingMedicationResult {}

// Calculate appointment and pet details schedule params
message CalculateAppointmentScheduleParams {
  // Appointment params
  models.appointment.v1.AppointmentCreateDef appointment = 2 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Calculate appointment and pet details schedule result
message CalculateAppointmentScheduleResult {
  // Appointment schedule
  models.appointment.v1.AppointmentScheduleDef appointment_schedule = 1 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Reschedule daycare service params
message RescheduleDaycareServiceParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Daycare service pet detail
  repeated RescheduleDaycareServiceSchedule daycare_service_schedules = 2 [(validate.rules).repeated = {min_items: 1}];

  // Reschedule daycare service pet detail
  message RescheduleDaycareServiceSchedule {
    // pet id
    int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
    // service start date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
    string start_date = 2 [(validate.rules).string = {
      pattern: "^\\d{4}-\\d{2}-\\d{2}$"
      ignore_empty: true
    }];
    // service end date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
    string end_date = 3 [(validate.rules).string = {
      pattern: "^\\d{4}-\\d{2}-\\d{2}$"
      ignore_empty: true
    }];
    // start time, in minutes
    int32 start_time = 4 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // end time, in minutes
    int32 end_time = 5 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // selected lodging id
    int64 lodging_id = 6 [(validate.rules).int64 = {gte: 0}];

    // Certain dates
    // support daycare associated to boarding with specific days
    repeated string specific_dates = 7 [(validate.rules).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];

    // pet detail date type
    optional models.appointment.v1.PetDetailDateType date_type = 8 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }
}

// Reschedule daycare service result
message RescheduleDaycareServiceResult {
  // conflict service names, if there are no conflicts, the list is empty
  repeated string conflict_service_names = 1;
}

// Batch reschedule appointment by staff and date params
message BatchRescheduleAppointmentParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected staff id
  int64 source_staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected date
  google.type.Date source_date = 3;

  // Target staff id (optional, but either this or target_date must be provided)
  optional int64 target_staff_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Target date (optional, but either this or target_staff_id must be provided)
  optional google.type.Date target_date = 5;
}

// Batch reschedule appointment by staff and date result
message BatchRescheduleAppointmentResult {
  // reschedule appointments detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// Reschedule calendar card params
message RescheduleCalendarCardParams {
  // Reschedule appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Reschedule card type
  moego.models.appointment.v1.CalendarCardType card_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Rescheduled pet detail ids, dragged cards objects, used to identify the dragged pet details
  // SERVICE/OPERATION/SERVICE_AND_OPERATION card type, the pet_detail_ids field is required
  repeated int64 pet_detail_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];

  // Original staff id, current card location staff id
  optional int64 original_staff_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Target staff id, if not specified, the original staff id will be inherited.
  optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];

  // Target start date, in the format of "YYYY-MM-DD"
  string start_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // Target start time, in minutes
  optional int32 start_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // Stretch end time, in minutes
  // The end time that can be used to indicate stretching service hours when only one service is included for a pet selection
  optional int32 end_time = 8 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // The scope of impact of this modification on repeat appointments
  optional moego.models.appointment.v1.RepeatAppointmentModifyScope repeat_modify_type = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // The flag of move all cards
  bool move_all_cards = 10;
}

// Reschedule calendar card result
message RescheduleCalendarCardResult {}

// Reschedule appointment
message RescheduleAppointmentParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Reschedule start date
  optional string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // Reschedule start time, in minutes
  optional int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // The scope of impact of this modification on repeat appointments
  optional moego.models.appointment.v1.RepeatAppointmentModifyScope repeat_modify_type = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Reschedule appointment result
message RescheduleAppointmentResult {}

// Switch all pets start at the same time params
message SwitchAllPetsStartAtSameTimeParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // All pets start at the same time
  bool all_pets_start_at_same_time = 2;
}

// Switch all pets start at the same time result
message SwitchAllPetsStartAtSameTimeResult {}

// The params message for reschedule pet details
message ReschedulePetDetailsParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Pet details schedule
  repeated models.appointment.v1.PetDetailScheduleDef pet_details = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The result message for reschedule pet details
message ReschedulePetDetailsResult {}

// The appointment schedule service, used to manage appointment schedule
// Can change the date and time of the appointment or pet details
service AppointmentScheduleService {
  // Reschedule All-in-One (boarding&daycare&grooming) service, used to appointment card edit schedule
  // Replace to @see [ReschedulePetDetails](#moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetDetails)
  rpc RescheduleAllInOneService(RescheduleAllInOneServiceParams) returns (RescheduleAllInOneServiceResult) {
    option deprecated = true;
  }

  // Reschedule boarding service, used to lodging calendar
  // If there are multiple pets, the date and time of other pets will be changed in conjunction.
  rpc RescheduleBoardingService(RescheduleBoardingServiceParams) returns (RescheduleBoardingServiceResult);

  // Reschedule daycare service.
  rpc RescheduleDaycareService(RescheduleDaycareServiceParams) returns (RescheduleDaycareServiceResult);

  // Reschedule grooming service, used to grooming calendar
  // Replace to @see [RescheduleCalendarCard](#moego.api.appointment.v1.CalendarService.RescheduleCalendarCard)
  rpc RescheduleGroomingService(RescheduleGroomingServiceParams) returns (RescheduleGroomingServiceResult) {
    option deprecated = true;
  }

  // Reschedule evaluation service
  rpc RescheduleEvaluationService(RescheduleEvaluationServiceParams) returns (RescheduleEvaluationServiceResult);

  // lodging assign (boarding&daycare&evaluation) service
  rpc LodgingAssign(LodgingAssignParams) returns (LodgingAssignResult);

  // Get pet's feeding and medication schedules
  rpc GetPetFeedingMedicationSchedules(GetPetFeedingMedicationSchedulesParams) returns (GetPetFeedingMedicationSchedulesResult);

  // Update pet feeding and medication, used to appointment's Feeding & Medication tasks
  // Fully update the schedules of a single pet, including feeding and medication
  rpc ReschedulePetFeedingMedication(ReschedulePetFeedingMedicationParams) returns (ReschedulePetFeedingMedicationResult);

  // Calculate appointment and pet's service schedules
  // Appointment: Start date, end date, start time, end time
  // Pet Service: Start date, end date, start time, end time
  rpc CalculateAppointmentSchedule(CalculateAppointmentScheduleParams) returns (CalculateAppointmentScheduleResult) {
    option deprecated = true;
  }

  // Batch reschedule appointment by staff and date
  rpc BatchRescheduleAppointment(BatchRescheduleAppointmentParams) returns (BatchRescheduleAppointmentResult);

  // Reschedule calendar card
  // For use with @see [ListDayCardsWithMixType](#moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType)
  // reference: https://moego.atlassian.net/wiki/spaces/ET/pages/665387113/Calendar+card+split#%E4%BF%AE%E6%94%B9-RescheduleGroomingService-API
  // Reschedule object: card_type + appointment_id + pet_detail_ids
  // Target position: staff_id + start_date + start_time
  // Scope: repeat_modify_type
  // Stretch position: end_time
  rpc RescheduleCalendarCard(RescheduleCalendarCardParams) returns (RescheduleCalendarCardResult);

  // Reschedule appointment
  rpc RescheduleAppointment(RescheduleAppointmentParams) returns (RescheduleAppointmentResult);

  // Switch all pets start at the same time
  rpc SwitchAllPetsStartAtSameTime(SwitchAllPetsStartAtSameTimeParams) returns (SwitchAllPetsStartAtSameTimeResult);

  // Reschedule pet details, Each modification is independent and does not affect other pet details
  // Allow scheduling can be done for any of the pet details of the appointment
  // Not allow to grooming only Modification
  rpc ReschedulePetDetails(ReschedulePetDetailsParams) returns (ReschedulePetDetailsResult);
}
