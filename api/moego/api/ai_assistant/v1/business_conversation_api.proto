// @since 2023-06-29 16:13:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.ai_assistant.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/ai_assistant/v1;aiassistantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.ai_assistant.v1";

// conversation mass text scenario
message ConversationMassTextScenario {}

// conversation message scenario
message ConversationTwoWayMessageScenario {
  // the customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// conversation marketing email scenario
message ConversationMarketingEmailScenario {}

// create business conversation Params
message CreateBusinessConversationParams {
  // prompt. If provided, the conversation will start based on this prompt
  string prompt = 1 [(validate.rules).string = {max_len: 8192}];
  // scenario
  oneof scenario {
    option (validate.required) = true;
    // mass text scenario
    ConversationMassTextScenario mass_text = 2;
    // message scenario
    ConversationTwoWayMessageScenario two_way_message = 3;
    // marketing email scenario
    ConversationMarketingEmailScenario marketing_email = 4;
  }
}

// create business conversation Result
message CreateBusinessConversationResult {
  // conversation id
  int64 conversation_id = 1;
}

// close business conversation Params
message CloseBusinessConversationParams {
  // conversation id
  int64 conversation_id = 1;
}

// close business conversation Result
message CloseBusinessConversationResult {}

// ask Params
message AskParams {
  // conversation id
  int64 conversation_id = 1;

  // question
  string question = 2 [(validate.rules).string = {max_len: 8192}];
}

// ask Result
message AskResult {
  // question id
  int64 question_id = 1;
  // answer
  string answer = 2;
}

// rephrase answer Params
message RephraseAnswerParams {
  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;
}

// rephrase answer Result
message RephraseAnswerResult {
  // new question id, different from the one in RephraseAnswerRequest
  int64 question_id = 1;
  // answer
  string answer = 2;
}

// adopt answer Params
message AdoptAnswerParams {
  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;
}

// adopt answer Result
message AdoptAnswerResult {}

// send answer Params
message SendAnswerParams {
  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;

  // message id
  int64 message_id = 3;
}

// send answer Result
message SendAnswerResult {}

// business conversation service
service BusinessConversationService {
  // create a business conversation
  rpc CreateConversation(CreateBusinessConversationParams) returns (CreateBusinessConversationResult);

  // close the business conversation
  rpc CloseConversation(CloseBusinessConversationParams) returns (CloseBusinessConversationResult);

  // pose a question and ask for an answer
  rpc Ask(AskParams) returns (AskResult);

  // get a rephrase answer for the question
  rpc RephraseAnswer(RephraseAnswerParams) returns (RephraseAnswerResult);

  // adopt an answer
  rpc AdoptAnswer(AdoptAnswerParams) returns (AdoptAnswerResult);

  // send an answer
  rpc SendAnswer(SendAnswerParams) returns (SendAnswerResult);
}
