syntax = "proto3";

package moego.api.notification.v1;

import "google/protobuf/struct.proto";
import "moego/models/message/v1/notification_defs.proto";
import "moego/models/message/v1/notification_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.notification.v1";

// String tab, Integer startingAfter, Integer limit
// get notification param
message GetNotificationsParams {
  // specific business ids, empty for all
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // tab type
  models.message.v1.NotificationTabType tab_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // starting after
  optional int32 starting_after = 3 [(validate.rules).int32 = {
    gt: 0
    ignore_empty: true
  }];
  // limit
  int32 limit = 4 [(validate.rules).int32 = {
    gt: 0
    lt: 100
  }];
}

// get notification result
message GetNotificationsResult {
  // notification list  data
  message NotificationListData {
    // notification record model view
    // notification id
    int64 notification_id = 1;
    // business id
    int64 business_id = 2;
    // send time
    int32 send_time = 3;
    // read time
    int32 read_time = 4;
    // company id
    int64 company_id = 5;
    // create time
    int32 create_time = 6;

    // notification model view begin 30
    // type
    string type = 30;
    // title
    string title = 31;
    // body
    string body = 32;
    //extra
    google.protobuf.Struct extra = 33;
  }
  // notification list
  repeated NotificationListData notification_list_data = 1;
  // has more
  bool has_more = 2;
}

// read notification params
message NotificationReadParams {
  // notification record id
  int64 notification_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source
  optional string source = 2 [(validate.rules).string = {max_len: 1024}];
}

// read notification result
message NotificationReadResult {}

// dismiss notification params
message NotificationDismissParams {
  // notification record id
  int64 notification_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// dismiss notification result
message NotificationDismissResult {}

// read all notification params
message NotificationReadAllParams {
  // specific business ids, empty means all business in the token company
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // tab type
  models.message.v1.NotificationTabType tab_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // source
  optional string source = 3 [(validate.rules).string = {max_len: 1024}];
  // specific type,empty for all
  repeated string type = 4 [(validate.rules).repeated = {
    items: {
      string: {max_len: 1024}
    }
    max_items: 100
    unique: true
    ignore_empty: true
  }];
}

// read all notification result
message NotificationReadAllResult {
  // read count
  int32 read_count = 1;
}

// get notification unread count params
message GetNotificationUnreadCountParams {
  // specific business ids
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    max_items: 100
    unique: true
  }];
}

// get notification unread count result
message GetNotificationUnreadCountResult {
  // business id to unread count map
  map<int64, models.message.v1.NotificationUnreadCountInfoDef> business_id_to_unread_count = 1;
}

// NotificationService
service NotificationService {
  // get notification with token staff id and token company id
  rpc GetNotifications(GetNotificationsParams) returns (GetNotificationsResult) {}
  // read notification
  rpc NotificationRead(NotificationReadParams) returns (NotificationReadResult) {}
  // dismiss notification
  rpc NotificationDismiss(NotificationDismissParams) returns (NotificationDismissResult) {}
  // read all notification
  rpc NotificationReadAll(NotificationReadAllParams) returns (NotificationReadAllResult) {}
  // get notification unread count
  rpc GetNotificationUnreadCount(GetNotificationUnreadCountParams) returns (GetNotificationUnreadCountResult) {}
}
