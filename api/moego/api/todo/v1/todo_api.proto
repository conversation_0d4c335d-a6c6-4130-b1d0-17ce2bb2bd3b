// @since 2022-05-30 17:05:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.todo.v1;

import "google/protobuf/empty.proto";
import "moego/models/todo/v1/todo_models.proto";
import "moego/models/universal/v1/entities_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/todo/v1;todoapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.todo.v1";

// add todo input
message AddTodoRequest {
  // todo title
  string title = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
  }];
}

// update todo input
message UpdateTodoRequest {
  // todo title
  string title = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
    ignore_empty: true
  }];
  // todo status
  moego.models.todo.v1.TodoModel.Status status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the todo id
  int64 id = 3 [(validate.rules).int64 = {gt: 0}];
}

//response body
message HelloChannyServiceResponse {
  //response
  string hello_channy = 1;
}

// response body
message HelloDongServiceResponse {
  // response content
  string hello_dong = 1;
}

// response body
message HelloJettServiceResponse {
  // response content
  string hello_jett = 1;
}

// EchoHzParams
message EchoHzParams {
  // msg
  string msg = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
    ignore_empty: true
  }];
  // id
  int64 id = 2;
}

// EchoHzResult
message EchoHzResult {
  // msg
  string msg = 1;
  // id
  int64 id = 2;
}

// HelloArkParams
message HelloArkParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 512
  }];
}

// HelloArkResult
message HelloArkResult {
  // history messages
  repeated string history_messages = 1;
}

// HelloBetterParams
message HelloBetterParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 512
  }];
}

// HelloBetterResult
message HelloBetterResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloPerqinParams
message HelloPerqinParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloPerqinResult
message HelloPerqinResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloYueyueParams
message HelloYueyueParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloYueyueResult
message HelloYueyueResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloKaiParams
message HelloKaiParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloKaiResult
message HelloKaiResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloKurokoParams
message HelloKurokoParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloKurokoResult
message HelloKurokoResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloBrysonParams
message HelloBrysonParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloBrysonResult
message HelloBrysonResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// HelloHarvieParams
message HelloHarvieParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // message
  string message = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloHarvieResult
message HelloHarvieResult {
  // id
  int64 id = 1;
  // reply
  string reply = 2;
}

// the todo service
service TodoService {
  // add todo
  rpc AddTodo(AddTodoRequest) returns (moego.models.todo.v1.TodoModel);
  // get single todo by id
  rpc GetTodo(moego.utils.v1.Id) returns (moego.models.todo.v1.TodoModel);
  // list all todos
  rpc ListTodo(google.protobuf.Empty) returns (moego.models.universal.v1.EntityListModel);
  // update single todo
  rpc UpdateTodo(UpdateTodoRequest) returns (moego.models.todo.v1.TodoModel);
  // delete single todo
  rpc DeleteTodo(moego.utils.v1.Id) returns (google.protobuf.Empty);
  // hello world
  rpc HelloChanny(google.protobuf.Empty) returns (HelloChannyServiceResponse);
  // newcomer hello world API by zhangdong
  rpc HelloDong(google.protobuf.Empty) returns (HelloDongServiceResponse);
  // hello world api by jett
  rpc HelloJett(google.protobuf.Empty) returns (HelloJettServiceResponse);
  // echo api by haozhi
  rpc EchoHz(EchoHzParams) returns (EchoHzResult);
  // hello world api by ark
  rpc HelloArk(HelloArkParams) returns (HelloArkResult);

  // hello world api by better
  rpc HelloBetter(HelloBetterParams) returns (HelloBetterResult);
  // hello world api by perqin
  rpc HelloPerqin(HelloPerqinParams) returns (HelloPerqinResult);
  //  hello world api by yueyue
  rpc HelloYueyue(HelloYueyueParams) returns (HelloYueyueResult);
  //  hello world api by kai
  rpc HelloKai(HelloKaiParams) returns (HelloKaiResult);
  //  hello world api by kuroko
  rpc HelloKuroko(HelloKurokoParams) returns (HelloKurokoResult);
  //  hello world api by Bryson
  rpc HelloBryson(HelloBrysonParams) returns (HelloBrysonResult);
  //  hello world api by Harvie
  rpc HelloHarvie(HelloHarvieParams) returns (HelloHarvieResult);
}
