// (-- api-linter: core::0131::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

syntax = "proto3";

package moego.api.payment.v2;

import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/service/payment/v2/payment_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// Payment service.
service PaymentService {
  // 获取支付版本
  rpc GetPaymentVersion(GetPaymentVersionParams) returns (GetPaymentVersionResult);

  // GetChannelBalanceAccountInfo 获取渠道 账户余额 信息
  rpc GetChannelBalanceAccountInfo(GetChannelBalanceAccountInfoParams) returns (GetChannelBalanceAccountInfoResult);

  // GetPayData 获取支付数据，用于前端加载第三方支付组件
  rpc GetPayData(GetPayDataParams) returns (GetPayDataResult);
  // submit action detail
  rpc SubmitActionDetail(SubmitActionDetailParams) returns (SubmitActionDetailResult);
  // 查询payment
  rpc GetPayment(GetPaymentParams) returns (GetPaymentResult);
  // 获取支付列表
  rpc ListPayment(ListPaymentParams) returns (ListPaymentResult);
  // 获取transaction 列表
  rpc ListTransaction(ListTransactionParams) returns (ListTransactionResult);
  // 根据transaction 获取对应的 payment list
  rpc ListPaymentByTransaction(ListPaymentByTransactionParams) returns (ListPaymentByTransactionResult);
  // 查询Transaction
  rpc GetTransaction(GetTransactionParams) returns (GetTransactionResult);
  // 导出 transaction
  rpc ExportTransactionList(ExportTransactionListParams) returns (ExportTransactionListResult);

  // 添加绑定的支付方式
  rpc AddRecurringPaymentMethod(AddRecurringPaymentMethodParams) returns (AddRecurringPaymentMethodResult);
  // 删除绑定的支付方式
  rpc DeleteRecurringPaymentMethod(DeleteRecurringPaymentMethodParams) returns (DeleteRecurringPaymentMethodResult);
  // 将payment method设置为primary
  rpc SetRecurringPaymentMethodPrimary(SetRecurringPaymentMethodPrimaryParams) returns (SetRecurringPaymentMethodPrimaryResult);
  // 获取用户所有绑定的支付方式
  rpc ListRecurringPaymentMethods(ListRecurringPaymentMethodsParams) returns (ListRecurringPaymentMethodsResult);

  // Get Payment setting
  rpc GetPaymentSetting(GetPaymentSettingParams) returns (GetPaymentSettingResult);
  // Update Payment setting
  rpc UpdatePaymentSetting(UpdatePaymentSettingParams) returns (UpdatePaymentSettingResult);
}

// get transaction params
message GetTransactionParams {
  // transaction id
  int64 transaction_id = 1;
}

// get transaction result
message GetTransactionResult {
  // transaction view
  models.payment.v2.PaymentTransactionView transaction_view = 1;
}

// get payment version params
message GetPaymentVersionParams {}

// get payment version result
message GetPaymentVersionResult {
  // 支付版本
  models.payment.v2.PaymentVersion payment_version = 1;
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// get channel balance account info params
message GetChannelBalanceAccountInfoParams {
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// get channel balance account info result
message GetChannelBalanceAccountInfoResult {
  // available balance
  google.type.Money available_balance = 1;
  // pending balance
  google.type.Money pending_balance = 2;
}

// get pay data params
message GetPayDataParams {
  // 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
  optional models.payment.v2.ChannelType channel_type = 1;
}

// get pay data result
message GetPayDataResult {
  // adyen data
  message AdyenData {
    // data
    string data = 1;
  }

  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 2;
  }
}

// submit action detail params
message SubmitActionDetailParams {
  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // Action Result，前端从组件拿到的原始数据，
  // e.g. ayden 3ds2:
  // `{
  //   "details": {
  //     "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
  //   }
  // }`
  string raw_action_result = 2;
}

// submit action result
message SubmitActionDetailResult {
  // msg 可以展示给用户的信息
  optional string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
  // e.g. adyen:
  // `{
  //   "resultCode": "Authorised",
  //   "pspReference": "V4HZ4RBFJGXXGN82"
  // }`
  string raw_channel_response = 2;
}

// get payment params
message GetPaymentParams {
  // 支付单据id
  int64 payment_id = 1;
}

// get payment result
message GetPaymentResult {
  // payment view
  models.payment.v2.PaymentView payment = 1;
}

// 查询payment列表请求参数
message ListPaymentParams {
  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // customer id
    repeated int64 customer_ids = 1;
    // order id
    repeated int64 order_ids = 2;
    // order payment id
    repeated int64 order_payment_ids = 3;
    // payment id
    repeated int64 payment_ids = 4;
    // 查询时间范围
    optional google.type.Interval time_range = 5;
  }
  // filter
  Filter filter = 2;
}

// list payment result
message ListPaymentResult {
  // 支付列表
  repeated models.payment.v2.PaymentView payments = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}

// list payment transaction params
message ListTransactionParams {
  // 分页查询请求
  utils.v2.PaginationRequest pagination = 1;
  // filter
  service.payment.v2.ListTransactionRequest.Filter filter = 2 [(validate.rules).message = {required: true}];
  // 排序条件
  repeated service.payment.v2.ListTransactionRequest.OrderBy order_bys = 3;
}

// list payment transaction result
message ListTransactionResult {
  // 支付列表
  repeated models.payment.v2.PaymentTransactionView transactions = 1;
  // 分页
  utils.v2.PaginationResponse pagination = 2;
}

// list payment bu transaction params
message ListPaymentByTransactionParams {
  // transaction id
  int64 transaction_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// list payment by transaction result
message ListPaymentByTransactionResult {
  // 支付列表
  repeated models.payment.v2.PaymentView payments = 1;
}

// export transaction list params
message ExportTransactionListParams {
  // 文件名，不传默认 uuid
  optional string file_name = 1;
  // 文件类型，不传默认 xlsx
  optional string file_type = 2;
  // filter
  service.payment.v2.ListTransactionRequest.Filter filter = 3 [(validate.rules).message = {required: true}];
  // 排序条件
  repeated service.payment.v2.ListTransactionRequest.OrderBy order_bys = 4;
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// export transaction list result
message ExportTransactionListResult {
  // file id
  int64 file_id = 1;
}

// add recurring payment method params
message AddRecurringPaymentMethodParams {
  // customer id
  optional int64 customer_id = 1;
  // customer code
  optional string encrypted_customer_id = 2;
  // channel type
  optional models.payment.v2.ChannelType channel_type = 3;
  // payment method type，目前只能是 Card
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 4;
  // 要存储的支付方式
  models.payment.v2.PaymentMethod.Detail detail = 5;
  // 透传参数，一般是用户自定义的额外信息
  optional models.payment.v2.RecurringPaymentMethodModel.Extra extra = 6;
}

// add recurring payment method result
message AddRecurringPaymentMethodResult {
  // 已存储的 payment method，如果发生了 3ds 验证的话不会返回
  optional models.payment.v2.RecurringPaymentMethodView recurring_payment_method_view = 1;
  // 渠道返回的原始数据，用于 3ds 验证等
  string channel_response = 2;
}

// delete recurring payment method params
message DeleteRecurringPaymentMethodParams {
  // 存储的 payment method id
  int64 payment_method_id = 1;
}

// delete recurring payment method result
message DeleteRecurringPaymentMethodResult {}

// set recurring payment method primary params
message SetRecurringPaymentMethodPrimaryParams {
  // 存储的payment method id
  int64 payment_method_id = 1;
}

// set recurring payment method primary result
message SetRecurringPaymentMethodPrimaryResult {
  // 已存储的 payment method
  models.payment.v2.RecurringPaymentMethodView recurring_payment_method_view = 1;
}

// list recurring payment method params
message ListRecurringPaymentMethodsParams {
  // 用户id
  int64 customer_id = 1;
}

// list recurring payment method result
message ListRecurringPaymentMethodsResult {
  // 已存储的 payment method 列表
  repeated models.payment.v2.RecurringPaymentMethodView recurring_payment_method_views = 1;
}

// get payment setting params
message GetPaymentSettingParams {}

// get payment setting result
message GetPaymentSettingResult {
  // 支付设置
  models.payment.v2.PaymentSetting payment_setting = 1;
}

// update payment setting params
message UpdatePaymentSettingParams {
  // convenience fee config
  optional service.payment.v2.UpdatePaymentSettingRequest.ConvenienceFeeConfigOperation convenience_fee_config = 1;
  // tips config
  optional service.payment.v2.UpdatePaymentSettingRequest.TipsConfigOperation tips_config = 2;
}

// update payment setting result
message UpdatePaymentSettingResult {
  // 更新后的支付设置
  models.payment.v2.PaymentSetting payment_setting = 1;
}
