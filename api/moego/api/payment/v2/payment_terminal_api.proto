syntax = "proto3";

package moego.api.payment.v2;

import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// Payment Terminal service.
service PaymentTerminalService {
  // GetReader 获取支付终端
  rpc GetTerminal(GetTerminalParams) returns (GetTerminalResult);
  // ListTerminal 获取支付终端列表
  rpc ListTerminals(ListTerminalsParams) returns (ListTerminalsResult);
}

// get reader params
message GetTerminalParams {
  // reader id
  int64 id = 1;
}

// get reader result
message GetTerminalResult {
  // 支付终端
  models.payment.v2.TerminalView terminal = 1;
}

// list terminal params
message ListTerminalsParams {
  // 分页参数
  utils.v2.PaginationRequest pagination = 1;
}

// list terminal result
message ListTerminalsResult {
  // 支付终端列表
  repeated models.payment.v2.TerminalView terminals = 1;
  // 分页信息
  utils.v2.PaginationResponse pagination = 2;
}
