syntax = "proto3";

package moego.api.payment.v2;

import "moego/service/payment/v2/payment_ops_service.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// Payment's operations & maintenance service.
service PaymentOpsService {
  // Import external Square customers and cards on file to Stripe.
  rpc ImportExternalSquareCofToStripe(ImportExternalSquareCofToStripeParams) returns (ImportExternalSquareCofToStripeResult);
}

// Request for ImportExternalSquareCofToStripe
message ImportExternalSquareCofToStripeParams {
  // Company ID to import into.
  int64 target_company_id = 1;
  // Business ID to import into.
  int64 target_business_id = 2;
  // The entries to import.
  repeated moego.service.payment.v2.ImportExternalSquareCofToStripeRequest.CofEntry cof_entries = 3;
  // Whether to do a dry run.
  bool dry_run = 4;
}

// Response for ImportExternalSquareCofToStripe
message ImportExternalSquareCofToStripeResult {}
