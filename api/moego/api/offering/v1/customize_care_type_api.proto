syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/customize_care_type_model.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// update care type name params
message UpdateCareTypeNameParams {
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // name of the care type
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
}

// update care type name result
message UpdateCareTypeNameResult {}

// list care types params
message ListCareTypesParams {
  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// list care types result
message ListCareTypesResult {
  // care types
  repeated models.offering.v1.CustomizeCareTypeView care_types = 1;
}

// customize care type service
service CustomizeCareTypeService {
  // update care type name
  rpc UpdateCareTypeName(UpdateCareTypeNameParams) returns (UpdateCareTypeNameResult);
  // list care types
  rpc ListCareTypes(ListCareTypesParams) returns (ListCareTypesResult);
}
