syntax = "proto3";

package moego.api.offering.v1;

import "google/type/date.proto";
import "moego/api/offering/v1/group_class_api.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// search pet for quick check-in params
message SearchPetForQuickCheckInParams {
  // service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message.required = true];

  // keyword, search by client or pet name
  optional string keyword = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];

  // date
  optional google.type.Date date = 4;

  // business id
  optional int64 business_id = 5 [(validate.rules).int64.gt = 0];
}

// search pet for quick check-in result
message SearchPetForQuickCheckInResult {
  // pet client view list
  repeated PetViewForQuickCheckIn pets = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;

  // pet and client view
  message PetViewForQuickCheckIn {
    // pet
    models.business_customer.v1.BusinessCustomerPetCalendarView pet = 1;
    // client
    models.business_customer.v1.BusinessCustomerModelNameView client = 2;
    // whether the pet has appointment today
    optional bool has_appointment_today = 3;
    // memberships
    moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 4;
  }
}

// search association information
message SearchAssociationInformationParams {
  // customer ids
  repeated int64 customer_ids = 1;

  // pet ids
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // service id
  int64 service_id = 3 [(validate.rules).int64.gt = 0];

  // date
  optional google.type.Date date = 4;
}

// SearchAssociationInformationResult
message SearchAssociationInformationResult {
  // has appointment
  message HasAppointment {
    // pet id
    int64 pet_id = 1;

    // whether the pet has appointment
    bool has_appointment = 2;
  }

  // client membership subscription
  message ClientMemberShipSubscription {
    // customer id
    int64 customer_id = 1;

    // memberships
    models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 2;
  }
  // pets
  repeated HasAppointment pet_appointments = 1;

  // client membership list
  repeated ClientMemberShipSubscription client_membership_subscriptions = 2;
}

// The params message for SearchEnrollmentInfo
message SearchEnrollmentInfoParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The training group class instance id
  int64 group_class_instance_id = 2 [(validate.rules).int64.gt = 0];

  // The enrolled pet ids or keyword and pagination
  oneof enrollment_type {
    // The enrolled pet ids
    EnrollmentByPetIds by_pet_ids = 3;

    // The keyword and pagination for search
    EnrollmentByKeyword by_keyword = 4;
  }
}

// The enrollment pet ids
message EnrollmentByPetIds {
  // The enrolled pet ids
  repeated int64 pet_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The keyword and pagination message for SearchEnrollmentInfo
message EnrollmentByKeyword {
  // The keyword, search by client or pet name
  optional string keyword = 1 [(validate.rules).string = {max_len: 100}];

  // The pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
}

// The result message for SearchEnrollmentInfo
message SearchEnrollmentInfoResult {
  // The pet list
  repeated EnrollmentPetView pets = 1;

  // The pagination
  moego.utils.v2.PaginationResponse pagination = 2;

  // The pet view for enrollment
  message EnrollmentPetView {
    // The pet view
    GroupClassPetView pet = 1;

    // The client view
    GroupClassClientView client = 2;

    // The not completed prerequisite class view
    repeated models.offering.v1.GroupClassModel not_completed_prerequisites = 3;
  }
}

// The params message for SearchPetForGroupClassCheckIn
message SearchPetForGroupClassCheckInParams {
  // The business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The group class session id
  optional int64 group_class_session_id = 2 [(validate.rules).int64.gt = 0];

  // keyword, search by client or pet name
  optional string keyword = 3 [(validate.rules).string = {max_len: 100}];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message.required = true];
}

// The result message for SearchPetForGroupClassCheckIn
message SearchPetForGroupClassCheckInResult {
  // The pet list
  repeated CheckInPetView pets = 1;

  // The pagination
  moego.utils.v2.PaginationResponse pagination = 2;

  // The pet view for group class check-in
  message CheckInPetView {
    // The pet view
    GroupClassPetView pet = 1;

    // The client view
    GroupClassClientView client = 2;

    // The group class instance view
    GroupClassInstanceView group_class_instance = 3;
  }
}

// pet service
service PetService {
  // Search pet for quick check-in
  // deprecated, the search interface has been moved to moego/search service, please stop using the search here.
  rpc SearchPetForQuickCheckIn(SearchPetForQuickCheckInParams) returns (SearchPetForQuickCheckInResult) {
    option deprecated = true;
  }

  // search pet and customer association information
  rpc SearchAssociationInfo(SearchAssociationInformationParams) returns (SearchAssociationInformationResult);

  // Search pet info for enrollment
  rpc SearchEnrollmentInfo(SearchEnrollmentInfoParams) returns (SearchEnrollmentInfoResult);

  // Search pet for group class check-in
  // It will be retrieved within the current business day
  rpc SearchPetForGroupClassCheckIn(SearchPetForGroupClassCheckInParams) returns (SearchPetForGroupClassCheckInResult);
}
