syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/lodging_enum.proto";
import "moego/models/offering/v1/lodging_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

/**
 * Request body for create LodgingType service
 */
message CreateLodgingTypeParams {
  // name of the lodging type
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the lodging type
  string description = 2 [(validate.rules).string = {max_len: 1024}];
  // images of this lodging type
  repeated string photo_list = 3 [(validate.rules).repeated = {min_items: 0}];
  // max pet number of this lodging type
  int32 max_pet_num = 4 [(validate.rules).int32.gt = 0];
  // max pet total weight of this lodging type
  int32 max_pet_total_weight = 5 [deprecated = true];
  // available for all pet size
  bool all_pet_sizes = 6 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 7;
  // lodging unit type in this lodging type
  moego.models.offering.v1.LodgingUnitType lodging_unit_type = 8;
  // whether the lodging type is available for all pet size
  bool pet_size_filter = 9;
}

/**
 * Response body for create LodgingType
 */
message CreateLodgingTypeResult {
  // lodging type
  models.offering.v1.LodgingTypeView lodging_type = 1;
}

/**
 * Request body for update LodgingType
 */
message UpdateLodgingTypeParams {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name of the lodging type
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the lodging type
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1024
  }];
  // images of this lodging type
  repeated string photo_list = 4 [(validate.rules).repeated = {min_items: 0}];
  // max pet number of this lodging type
  optional int32 max_pet_num = 5 [(validate.rules).int32.gt = 0];
  // max pet total weight of this lodging type
  optional int32 max_pet_total_weight = 6 [deprecated = true];
  // available for all pet size
  optional bool all_pet_sizes = 7 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 8;
  // lodging unit type in this lodging type
  optional moego.models.offering.v1.LodgingUnitType lodging_unit_type = 9;
  // whether the lodging type is available for all pet size
  optional bool pet_size_filter = 10;
}

/**
 * Response body for update LodgingType
 */
message UpdateLodgingTypeResult {
  // lodging type
  models.offering.v1.LodgingTypeView lodging_type = 1;
}

/**
 * Request body for delete LodgingType
 */
message DeleteLodgingTypeParams {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

/**
 * Response body for delete LodgingType
 */
message DeleteLodgingTypeResult {}

/**
 * Request body for get LodgingType list
 */
message GetLodgingTypeListParams {}

/**
 * get LodgingType list response
 */
message GetLodgingTypeListResult {
  // lodging type list
  repeated models.offering.v1.LodgingTypeView lodging_type_list = 1;
}

// The params for sort lodging type by ids
message SortLodgingTypeByIdsParams {
  // ids of lodging types to sort
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The result for sort lodging type by ids
message SortLodgingTypeByIdsResult {}

// lodging type service
service LodgingTypeService {
  // create lodging type
  rpc CreateLodgingType(CreateLodgingTypeParams) returns (CreateLodgingTypeResult);
  // update lodging type
  rpc UpdateLodgingType(UpdateLodgingTypeParams) returns (UpdateLodgingTypeResult);
  // delete lodging type
  rpc DeleteLodgingType(DeleteLodgingTypeParams) returns (DeleteLodgingTypeResult);
  // get lodging type list
  rpc GetLodgingTypeList(GetLodgingTypeListParams) returns (GetLodgingTypeListResult);
  // Sort lodging type
  rpc SortLodgingTypeByIds(SortLodgingTypeByIdsParams) returns (SortLodgingTypeByIdsResult);
}
