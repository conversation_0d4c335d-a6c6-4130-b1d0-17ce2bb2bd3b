syntax = "proto3";

package moego.api.fulfillment.v1;

import "moego/models/fulfillment/v1/fulfillment_defs.proto";
import "moego/models/fulfillment/v1/fulfillment_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/fulfillment/v1;fulfillmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.fulfillment.v1";

// The prams for enrolling a pet into a group class instance
message EnrollPetParams {
  // The business ID
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The training group class instance ID
  int64 instance_id = 2 [(validate.rules).int64.gt = 0];

  // The pet's ID
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];

  // The fulfillment source
  models.fulfillment.v1.Source source = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The result of enrolling a pet into a group class instance
message EnrollPetResult {
  // The order ID
  int64 order_id = 1;
}

// The params for removing a pet from a group class instance
message RemovePetParams {
  // The training group class instance ID
  int64 instance_id = 1 [(validate.rules).int64.gt = 0];
  // The pet's ID
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
  // Whether to auto refund the order, default is false
  bool auto_refund_order = 4;
}

// The result of removing a pet from a group class instance
message RemovePetResult {}

// The params for checking in a group class session
message CheckInGroupClassSessionParams {
  // The business ID
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The pet's ID
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The group class session ID
  optional int64 group_class_session_id = 3 [(validate.rules).int64.gt = 0];
}

// The result of checking in a group class session
message CheckInGroupClassSessionResult {
  // Check in count
  int32 check_in_count = 1;

  // Checked in group class instance ID
  repeated int64 checked_in_instance_ids = 2;
}

// The params of preview order line items
message PreviewOrderLineItemsParams {
  // The fulfillment ID
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// The result of preview order line items
message PreviewOrderLineItemsResult {
  // The pet service details with line item
  repeated models.fulfillment.v1.PetService pet_services = 1;

  // The surcharge details
  repeated models.fulfillment.v1.SurchargeItem surcharges = 2;
}

// The Fulfillment API service definition.
service FulfillmentService {
  // Enroll a pet into a group class instance
  rpc EnrollPet(EnrollPetParams) returns (EnrollPetResult);

  // Check in group class session
  // It will be retrieved within the current business day
  rpc CheckInGroupClassSession(CheckInGroupClassSessionParams) returns (CheckInGroupClassSessionResult);

  // Remove a pet from a group class instance
  rpc RemovePet(RemovePetParams) returns (RemovePetResult);

  // Preview order line items, include services and surcharges
  rpc PreviewOrderLineItems(PreviewOrderLineItemsParams) returns (PreviewOrderLineItemsResult);
}
