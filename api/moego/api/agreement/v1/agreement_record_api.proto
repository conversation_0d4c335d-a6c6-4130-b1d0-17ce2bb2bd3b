syntax = "proto3";

package moego.api.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/customer/v1/customer_models.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/utils/v1/id_messages.proto";
import "moego/utils/v1/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.agreement.v1";

// GetRecordByUUIDRequest
message GetRecordByUUIDRequest {
  // agreement record uuid
  string uuid = 1 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
}

// get record by uuid response
message GetRecordByUUIDResponse {
  // agreement info
  moego.models.agreement.v1.AgreementRecordModel agreement_record = 1;
  // business info
  moego.models.business.v1.BusinessModelPublicView business_public_view = 2;
}

// get agreement record by target id with service type
message GetRecordByTargetRequest {
  // target_id
  int64 target_id = 1;
  // associated service type: see definition in ServiceType
  moego.models.agreement.v1.ServiceType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get agreement record list for business request
message GetRecordListForBusinessRequest {
  // the page info
  moego.utils.v1.PaginationRequest pagination = 1;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 2 [(validate.rules).int32 = {gt: 0}];
  // sign status
  optional moego.models.agreement.v1.SignedStatus signed_status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // source type
  optional moego.models.agreement.v1.SourceType source_type = 5 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// get agreement record list for business response
message GetRecordListForBusinessResponse {
  // agreement record simple view
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
  // customer name view
  repeated moego.models.customer.v1.CustomerModelNameView customer_name_view = 2;
  // page info
  moego.utils.v1.PaginationResponse pagination = 3;
}

// get agreement record list for customer request
message GetRecordListForCustomerRequest {
  // customer id is required
  int64 customer_id = 1;
  // sign status
  optional moego.models.agreement.v1.SignedStatus signed_status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// get agreement record list for customer response
message GetRecordListForCustomerResponse {
  // record list
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
}

// GetRecentSignedAgreementListRequest
message GetRecentSignedAgreementListRequest {
  // customer id
  int64 customer_id = 1;
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 3 [(validate.rules).int32 = {gt: 0}];
}

// GetRecentSignedAgreementListResponse
message GetRecentSignedAgreementListResponse {
  // agreement with recent signed record list
  repeated moego.models.agreement.v1.AgreementWithRecentRecordsView agreement_recent_view = 1;
}

// GetRecordDetailResponse
message GetRecordDetailResponse {
  // business info
  moego.models.business.v1.BusinessModelPublicView business_public_view = 1;
  // customer name view
  moego.models.customer.v1.CustomerModelNameView customer_name_view = 2;
  // agreement record
  moego.models.agreement.v1.AgreementRecordModel agreement_record = 3;
}

// AddRecordRequest
message AddRecordRequest {
  // agreement id
  int64 agreement_id = 1;
  // customer id
  int64 customer_id = 2;
  // associated target id
  optional int64 target_id = 3;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // no call currently
  optional moego.models.message.v1.MessageType send_message_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// SignAgreementRequest
message SignRecordRequest {
  // agreement record uuid
  string uuid = 1 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
  // customer signature
  string signature = 2 [(validate.rules).string = {
    uri: true
    max_len: 1024
  }];
}

// UploadSignedFileRequest
message UploadSignedFileRequest {
  // customer id
  int64 customer_id = 1;
  // agreement title
  string agreement_title = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 3 [(validate.rules).int32 = {gt: 0}];
  // upload files
  repeated string upload_files = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 64
    unique: true
    items: {
      string: {
        uri: true
        max_len: 1024
      }
    }
  }];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// SendAgreementRecordRequest
message SendSignRequestRequest {
  // agreement record id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // message channel type
  moego.models.message.v1.MessageType send_message_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// SendSignRequestResponse
message SendSignRequestResponse {
  // message id
  int64 msg_id = 1;
}

// DeleteRecordResponse
message DeleteRecordResponse {
  // number of delete
  int32 number = 1;
}

// SignAgreementRequest
message SignAgreementParams {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // agreement id
  int64 agreement_id = 3;
  // customer signature
  string signature = 4 [(validate.rules).string = {
    uri: true
    max_len: 1024
  }];
  // associated target id
  optional int64 target_id = 5;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 6 [(validate.rules).int32 = {gt: 0}];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Agreement Record API
service AgreementRecordService {
  // create an unsigned agreement record
  rpc AddRecord(AddRecordRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // upload signed agreement files
  rpc UploadSignedFile(UploadSignedFileRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get agreement record list for business
  rpc GetRecordListForBusiness(GetRecordListForBusinessRequest) returns (GetRecordListForBusinessResponse);

  // get agreement record list for customer
  rpc GetRecordListForCustomer(GetRecordListForCustomerRequest) returns (GetRecordListForCustomerResponse);

  // get agreement with recent signed record list
  rpc GetRecentSignedAgreementList(GetRecentSignedAgreementListRequest) returns (GetRecentSignedAgreementListResponse);

  // get an agreement record by id
  rpc GetRecordById(moego.utils.v1.Id) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get an agreement record by uuid
  rpc GetRecordByUUID(GetRecordByUUIDRequest) returns (GetRecordByUUIDResponse);

  // get an agreement record by target id
  rpc GetRecordByTarget(GetRecordByTargetRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get an agreement record detail, include business and customer simple info
  rpc GetRecordDetail(moego.utils.v1.Id) returns (GetRecordDetailResponse);

  // sign an agreement
  rpc SignRecord(SignRecordRequest) returns (moego.models.agreement.v1.AgreementRecordSimpleView);

  // sign an already created agreement record
  rpc SendSignRequest(SendSignRequestRequest) returns (SendSignRequestResponse);

  // delete an agreement record
  rpc DeleteRecord(moego.utils.v1.Id) returns (DeleteRecordResponse);

  // direct sign a record from an agreement
  rpc SignAgreement(SignAgreementParams) returns (moego.models.agreement.v1.AgreementRecordSimpleView);
}
