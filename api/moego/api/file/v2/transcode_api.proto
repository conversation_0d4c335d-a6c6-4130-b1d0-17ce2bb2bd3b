syntax = "proto3";

package moego.api.file.v2;

import "moego/models/file/v2/transcode_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/file/v2;fileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.file.v2";

// GetJobRequest
message GetJobRequest {
  // job id
  int64 job_id = 1;
}

// GetJobResponse
message GetJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// CancelJobRequest
message CancelJobRequest {
  // job id
  int64 job_id = 1;
}

// CancelJobResponse
message CancelJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// CreateJobRequest
message CreateJobRequest {
  // file id
  int64 file_id = 1;
  // input settings
  optional moego.models.file.v2.InputSettings input_settings = 2;

  // Note: Both parameters `template_id` and `output_groups` are optional, and you can specify neither.
  // Usually, you only need to specify one of them. We recommend specifying parameter `template_id`.
  // If both are specified, parameter `template_id` will be used first, and parameter `output_groups` will be ignored.
  // If neither is specified, the service will use the default settings for transcoding.
  optional string template_id = 3;
  // custom output settings
  repeated moego.models.file.v2.OutputGroup output_groups = 4;
}

// CreateJobResponse
message CreateJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// TranscodeService
service TranscodeService {
  // get job
  rpc GetJob(GetJobRequest) returns (GetJobResponse);

  // create job
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse);

  // cancel job
  rpc CancelJob(CancelJobRequest) returns (CancelJobResponse);
}
