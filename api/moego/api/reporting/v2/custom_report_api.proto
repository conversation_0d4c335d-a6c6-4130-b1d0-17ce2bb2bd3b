syntax = "proto3";

package moego.api.reporting.v2;

import "moego/models/reporting/v2/custom_report_def.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2;reportingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.reporting.v2";

// Custom report API for company
service CustomReportService {
  // SaveCustomReport
  rpc SaveCustomReport(moego.models.reporting.v2.SaveCustomReportParams) returns (moego.models.reporting.v2.SaveCustomReportResult);
  // ModifyCustomDiagram
  rpc ModifyCustomDiagram(moego.models.reporting.v2.ModifyCustomDiagramParams) returns (moego.models.reporting.v2.ModifyCustomDiagramResult);
  // DuplicateCustomReport
  rpc DuplicateCustomReport(moego.models.reporting.v2.DuplicateCustomReportParams) returns (moego.models.reporting.v2.DuplicateCustomReportResult);
  // DeleteCustomReport
  rpc DeleteCustomReport(moego.models.reporting.v2.DeleteCustomReportParams) returns (moego.models.reporting.v2.DeleteCustomReportResult);
}
