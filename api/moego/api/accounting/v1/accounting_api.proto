syntax = "proto3";

package moego.api.accounting.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/accounting/v1/accounting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/accounting/v1;accountingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.accounting.v1";

// GetVisibilityParams
message GetVisibilityParams {}

// GetVisibilityResult
message GetVisibilityResult {
  // visibility
  bool visible = 1;
  // visibility class
  moego.models.accounting.v1.VisibilityClass visibility_class = 2;
}

// GetOnboardingStatusParams
message GetOnboardingStatusParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
}

// GetOnboardingStatusResult
message GetOnboardingStatusResult {
  // onboarding status
  moego.models.accounting.v1.OnboardingStatus onboarding_status = 1;
}

// GetBusinessesParams
message GetBusinessesParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
}

// Business
message AccountingBusinessView {
  // sync status
  enum SyncStatus {
    // Unspecified
    SYNC_STATUS_UNSPECIFIED = 0;
    // synced
    SYNCED = 1;
    // not synced
    NOT_SYNCED = 2;
  }

  // ID
  int64 id = 1;
  // name
  string name = 2;
  // avatar
  string avatar = 3;
  // sync status
  SyncStatus sync_status = 4;
  // sync time
  google.protobuf.Timestamp sync_time = 5;
}

// GetBusinessesResult
message GetBusinessesResult {
  // if has completed selection
  bool has_completed_selection = 1;
  // businesses, only valid when has_completed_selection is true
  repeated AccountingBusinessView businesses = 2;
}

// SetBusinessesParams
message SetBusinessesParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // business ids
  repeated int64 business_ids = 2;
  // legal name
  string legal_name = 4;
  // us state
  moego.models.accounting.v1.USState state = 5;
  // entity type
  moego.models.accounting.v1.EntityType entity_type = 6;
}

// SetBusinessesResult
message SetBusinessesResult {}

// AddBusinessesParams
message AddBusinessesParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // business id
  repeated int64 business_ids = 2;
}

// AddBusinessesResult
message AddBusinessesResult {}

// RemoveBusinessesParams
message RemoveBusinessesParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
  // business id
  repeated int64 business_ids = 2;
}

// RemoveBusinessesResult
message RemoveBusinessesResult {
  // business ids
  repeated int64 business_ids = 1;
}

// GetAuthTokenParams
message GetAuthTokenParams {
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 1;
}

// GetAuthTokenResult
message GetAuthTokenResult {
  // channel business id
  string channel_business_id = 1;
  // token
  string token = 2;
  // expiration time
  google.protobuf.Timestamp expiration_time = 3;
}

// AccountingService
service AccountingService {
  // get visibility
  rpc GetVisibility(GetVisibilityParams) returns (GetVisibilityResult);

  // get onboarding status
  rpc GetOnboardingStatus(GetOnboardingStatusParams) returns (GetOnboardingStatusResult);

  // get businesses
  rpc GetBusinesses(GetBusinessesParams) returns (GetBusinessesResult);
  // set businesses
  rpc SetBusinesses(SetBusinessesParams) returns (SetBusinessesResult);
  // add businesses
  rpc AddBusinesses(AddBusinessesParams) returns (AddBusinessesResult);
  // remove businesses
  rpc RemoveBusinesses(RemoveBusinessesParams) returns (RemoveBusinessesResult);

  // get auth token
  rpc GetAuthToken(GetAuthTokenParams) returns (GetAuthTokenResult);

  // retry sync data
  rpc RetrySync(google.protobuf.Empty) returns (google.protobuf.Empty);
}
