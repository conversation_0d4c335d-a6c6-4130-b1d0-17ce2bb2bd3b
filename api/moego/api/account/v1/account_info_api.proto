syntax = "proto3";

package moego.api.account.v1;

import "moego/models/account/v1/account_models.proto";
import "moego/models/risk_control/v1/verification_code_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.account.v1";

// get account info request
message GetAccountInfoRequest {}

// get account info response
message GetAccountInfoResponse {
  // account
  optional moego.models.account.v1.AccountModel account = 1;
}

// update profile request
message UpdateProfileRequest {
  // first name
  optional string first_name = 1 [(validate.rules).string = {max_len: 50}];
  // last name
  optional string last_name = 2 [(validate.rules).string = {max_len: 50}];
  // avatar path
  optional string avatar_path = 3 [(validate.rules).string = {
    ignore_empty: true
    max_len: 256
    uri: true
  }];
}

// update profile response
message UpdateProfileResponse {
  // account
  moego.models.account.v1.AccountModel account = 1;
}

// update password request
message UpdatePasswordRequest {
  // old password
  string old_password = 1 [(validate.rules).string = {max_len: 100}];

  // new password
  string new_password = 2 [(validate.rules).string = {
    min_len: 6
    max_len: 100
  }];
}

// update password response
message UpdatePasswordResponse {}

// add phone number request
message AddPhoneNumberRequest {
  // phone number
  string phone_number = 1 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^\\+[1-9]\\d{1,18}$"
  }];
  // verification code
  models.risk_control.v1.VerificationCodeDef verification = 2;
}

// add phone number response
message AddPhoneNumberResponse {}

// get relevant accounts request
message GetRelevantAccountsRequest {}

// get relevant accounts response
message GetRelevantAccountsResponse {
  // relevant accounts
  repeated moego.models.account.v1.AccountModelRelevantView accounts = 1;
}

// account info API
service AccountInfoService {
  // Get account info.
  // If the request is in a valid session, it returns `anonymous = false`, else it returns `anonymous = true`.
  // `accountId` in the request is optional. If `accountId` is `null` and the request is in a valid session,
  // the account info related to the session will be fetched.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
  rpc GetAccountInfo(GetAccountInfoRequest) returns (GetAccountInfoResponse);

  // Update profile of the account related to the current session.
  // The request should be in a valid session.
  rpc UpdateProfile(UpdateProfileRequest) returns (UpdateProfileResponse);

  // Update password of the account related to the current session.
  // The request should be in a valid session.
  // After success, all sessions of this account will be invalidated, and a new valid session will be created.
  //
  // Error codes:
  // - OLD_PASSWORD_ERROR: old password error.
  rpc UpdatePassword(UpdatePasswordRequest) returns (UpdatePasswordResponse);

  // Add a new phone number of the account related to the current session.
  // The request should be in a valid session.
  // Different from update phone number
  //
  // Error codes:
  // - CODE_INVALID_VERIFICATION_CODE: Verification code verification error
  rpc AddPhoneNumber(AddPhoneNumberRequest) returns (AddPhoneNumberResponse);

  // Get relevant accounts of the account related to the current session.
  // The request should be in a valid session.
  rpc GetRelevantAccounts(GetRelevantAccountsRequest) returns (GetRelevantAccountsResponse);
}
