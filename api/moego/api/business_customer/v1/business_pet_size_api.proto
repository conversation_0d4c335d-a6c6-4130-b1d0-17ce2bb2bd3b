syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_size_defs.proto";
import "moego/models/business_customer/v1/business_pet_size_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet size params
message ListPetSizeParams {}

// list pet size result
message ListPetSizeResult {
  // pet size list
  repeated moego.models.business_customer.v1.BusinessPetSizeModel sizes = 1;
}

// Batch upsert pet size params
message BatchUpsertPetSizeParams {
  // pet sizes to create
  repeated moego.models.business_customer.v1.BusinessPetSizeUpsertDef sizes_to_create = 1;

  // pet sizes to update
  map<int64, moego.models.business_customer.v1.BusinessPetSizeUpsertDef> sizes_to_update = 2;
}

// Batch upsert pet size result
message BatchUpsertPetSizeResult {}

// API for pet size settings
service BusinessPetSizeService {
  // List pet sizes of current company
  rpc ListPetSize(ListPetSizeParams) returns (ListPetSizeResult);

  // Batch upsert pet sizes of current company
  rpc BatchUpsertPetSize(BatchUpsertPetSizeParams) returns (BatchUpsertPetSizeResult);
}
