syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_coat_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_coat_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet coat type template params
message ListPetCoatTypeTemplateParams {}

// list pet coat type template result
message ListPetCoatTypeTemplateResult {
  // pet coat types
  repeated moego.models.business_customer.v1.BusinessPetCoatTypeNameView coat_types = 1;
}

// list pet coat type params
message ListPetCoatTypeParams {}

// list pet coat type result
message ListPetCoatTypeResult {
  // pet coat type list
  repeated moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_types = 1;
}

// create pet coat type params
message CreatePetCoatTypeParams {
  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeCreateDef coat_type = 1 [(validate.rules).message.required = true];
}

// create pet coat type result
message CreatePetCoatTypeResult {
  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_type = 1;
}

// update pet coat type params
message UpdatePetCoatTypeParams {
  // pet coat type id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet coat type
  moego.models.business_customer.v1.BusinessPetCoatTypeUpdateDef coat_type = 2 [(validate.rules).message.required = true];
}

// update pet coat type result
message UpdatePetCoatTypeResult {}

// sort pet coat type params
message SortPetCoatTypeParams {
  // pet coat type id list, should contain all pet coat type ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet coat type result
message SortPetCoatTypeResult {}

// delete pet coat type params
message DeletePetCoatTypeParams {
  // pet coat type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet coat type result
message DeletePetCoatTypeResult {}

// API for pet coat type settings
service BusinessPetCoatTypeService {
  // List pet coat type template
  rpc ListPetCoatTypeTemplate(ListPetCoatTypeTemplateParams) returns (ListPetCoatTypeTemplateResult);

  // List pet coat type of current company
  rpc ListPetCoatType(ListPetCoatTypeParams) returns (ListPetCoatTypeResult);

  // Create a coat type
  rpc CreatePetCoatType(CreatePetCoatTypeParams) returns (CreatePetCoatTypeResult);

  // Update a coat type
  rpc UpdatePetCoatType(UpdatePetCoatTypeParams) returns (UpdatePetCoatTypeResult);

  // Sort coat types
  rpc SortPetCoatType(SortPetCoatTypeParams) returns (SortPetCoatTypeResult);

  // Delete a coat type
  rpc DeletePetCoatType(DeletePetCoatTypeParams) returns (DeletePetCoatTypeResult);
}
