syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_vaccine_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet vaccine template params
message ListPetVaccineTemplateParams {}

// list pet vaccine template result
message ListPetVaccineTemplateResult {
  // pet vaccines
  repeated moego.models.business_customer.v1.BusinessPetVaccineNameView vaccines = 1;
}

// list pet vaccine params
message ListPetVaccineParams {}

// list pet vaccine result
message ListPetVaccineResult {
  // pet vaccine list
  repeated moego.models.business_customer.v1.BusinessPetVaccineModel vaccines = 1;

  // vaccine id to requirements setting
  // requirements of service
  message VaccineRequirementByService {
    // vaccine id
    int64 vaccine_id = 1;

    // requirements of service item types
    repeated models.offering.v1.ServiceItemType required_by_service_item_types = 2 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
  // requirements of service
  repeated VaccineRequirementByService vaccine_requirement_by_service = 2;
}

// create pet vaccine params
message CreatePetVaccineParams {
  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineCreateDef vaccine = 1 [(validate.rules).message.required = true];

  // vaccine requirements
  // requirements of service item types
  repeated models.offering.v1.ServiceItemType required_by_service_item_types = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// create pet vaccine result
message CreatePetVaccineResult {
  // vaccine
  moego.models.business_customer.v1.BusinessPetVaccineModel vaccine = 1;
}

// update pet vaccine params
message UpdatePetVaccineParams {
  // pet vaccine id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet vaccine
  moego.models.business_customer.v1.BusinessPetVaccineUpdateDef vaccine = 2 [(validate.rules).message.required = true];

  // vaccine requirements
  // requirements of service item types
  message ServiceItemTypeList {
    // requirements of service item types
    repeated models.offering.v1.ServiceItemType types = 1 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
  // requirements of service
  optional ServiceItemTypeList required_by_service_item_types = 3;
}

// sort pet vaccine params
message SortPetVaccineParams {
  // pet vaccine id list, should contain all pet vaccine ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet vaccine result
message SortPetVaccineResult {}

// update pet vaccine result
message UpdatePetVaccineResult {}

// delete pet vaccine params
message DeletePetVaccineParams {
  // pet vaccine id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet vaccine result
message DeletePetVaccineResult {}

// API for pet vaccine settings
service BusinessPetVaccineService {
  // List pet vaccine template
  rpc ListPetVaccineTemplate(ListPetVaccineTemplateParams) returns (ListPetVaccineTemplateResult);

  // List pet vaccines of current company
  rpc ListPetVaccine(ListPetVaccineParams) returns (ListPetVaccineResult);

  // Create a pet vaccine
  rpc CreatePetVaccine(CreatePetVaccineParams) returns (CreatePetVaccineResult);

  // Update a pet vaccine
  rpc UpdatePetVaccine(UpdatePetVaccineParams) returns (UpdatePetVaccineResult);

  // Sort pet vaccines
  rpc SortPetVaccine(SortPetVaccineParams) returns (SortPetVaccineResult);

  // Delete a pet vaccine
  rpc DeletePetVaccine(DeletePetVaccineParams) returns (DeletePetVaccineResult);
}
