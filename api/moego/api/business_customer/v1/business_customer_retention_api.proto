syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_retention_defs.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// BusinessCustomerRetentionService: Client & Pets retention api
service BusinessCustomerRetentionService {
  // Fetch retention data
  rpc FetchRetentionData(FetchRetentionDataParams) returns (FetchRetentionDataResult);
}

// FetchRetentionDataParams
message FetchRetentionDataParams {
  // filter for retention
  moego.models.business_customer.v1.RetentionFilter retention_filter = 1;
  // smart-client list filter params as json string
  optional string client_filter = 2;
  // pagination params
  moego.utils.v2.PaginationRequest pagination = 3;
  // order by columns
  repeated moego.utils.v2.OrderBy order_bys = 4;
}

// FetchRetentionDataResult
message FetchRetentionDataResult {
  // retention data
  repeated moego.models.business_customer.v1.CustomerRetentionData data = 1;
  // pagination response
  moego.utils.v2.PaginationResponse pagination = 2;
}
