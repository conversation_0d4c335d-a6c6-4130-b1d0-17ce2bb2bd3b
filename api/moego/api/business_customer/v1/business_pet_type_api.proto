syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_type_defs.proto";
import "moego/models/business_customer/v1/business_pet_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet type params
message ListPetTypeParams {
  // include unavailable pet type, default is false
  bool include_unavailable = 1;
  // include breed count, default is false
  bool include_breed_count = 2;
}

// list pet type result
message ListPetTypeResult {
  // pet type list
  repeated moego.models.business_customer.v1.BusinessPetTypeModel types = 1;

  // Key is pet type id, value is breed count.
  // Only available when `include_breed_count` is true.
  map<int64, int32> breed_count = 2;
}

// update pet type params
message UpdatePetTypeParams {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet type
  moego.models.business_customer.v1.BusinessPetTypeUpdateDef type = 4 [(validate.rules).message.required = true];
}

// update pet type result
message UpdatePetTypeResult {}

// sort pet type params
message SortPetTypeParams {
  // id list, should contain all pet type ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet type result
message SortPetTypeResult {}

// API for pet type settings
service BusinessPetTypeService {
  // List pet types of current company
  rpc ListPetType(ListPetTypeParams) returns (ListPetTypeResult);

  // Update pet type
  rpc UpdatePetType(UpdatePetTypeParams) returns (UpdatePetTypeResult);

  // Sort pet type
  rpc SortPetType(SortPetTypeParams) returns (SortPetTypeResult);
}
