syntax = "proto3";

package moego.api.organization.v1;

import "moego/models/organization/v1/clock_in_out_setting_defs.proto";
import "moego/models/organization/v1/clock_in_out_setting_model.proto";
import "moego/models/organization/v1/company_defs.proto";
import "moego/models/organization/v1/company_models.proto";
import "moego/models/organization/v1/country_defs.proto";
import "moego/models/organization/v1/location_defs.proto";
import "moego/models/organization/v1/location_enums.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/models/organization/v1/tax_defs.proto";
import "moego/models/organization/v1/tax_models.proto";
import "moego/models/organization/v1/time_zone.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// create company request
message CreateCompanyParams {
  // location info
  models.organization.v1.CreateLocationDef location = 1 [(validate.rules).message = {required: true}];
  // how to know us
  optional models.organization.v1.SourceType source = 2;
  // country
  models.organization.v1.CountryDef country = 3 [(validate.rules).message = {required: true}];
  // time zone
  models.organization.v1.TimeZone time_zone = 4 [(validate.rules).message = {required: true}];
  // know about us, if source is other, this field is required
  optional string know_about_us = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // phone number
  string phone_number = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // currency code
  string currency_code = 7 [(validate.rules).string.len = 3];
  // currency symbol
  string currency_symbol = 8 [(validate.rules).string.min_len = 1];
  //company type
  optional int32 company_type = 9;
}

// create company response
message CreateCompanyResult {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
}

// query company staff by account request
message QueryCompanyStaffByAccountParams {}

// query company staff by account response
message QueryCompanyStaffByAccountResult {
  // company staff
  message CompanyStaff {
    // staff
    moego.models.organization.v1.StaffBriefView staff = 1;
    // company
    moego.models.organization.v1.CompanyBriefView company = 2;
  }
  // company staffs
  repeated CompanyStaff company_staffs = 1;
}

// update company preference setting request
message UpdateCompanyPreferenceSettingParams {
  // preference setting
  models.organization.v1.UpdateCompanyPreferenceSettingDef preference_setting = 1 [(validate.rules).message = {required: true}];
}

// update company preference setting response
message UpdateCompanyPreferenceSettingResult {
  // success or not
  bool success = 1;
}

// get company preference setting request
message GetCompanyPreferenceSettingParams {}

// get company preference setting response
message GetCompanyPreferenceSettingResult {
  // preference setting
  models.organization.v1.CompanyPreferenceSettingModel preference_setting = 1;
}

// switch company request params
message SwitchCompanyParams {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business_id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// switch company result
message SwitchCompanyResult {}

// get companies extra info request params
message GetCompaniesExtraInfoParams {}

// get companies extra info response
message GetCompaniesExtraInfoResponse {
  // company extra info map,key is company id
  map<int64, models.organization.v1.CompanyExtraInfoDef> company_extra_info_map = 1;
}

// query company staff by account with locations request
message QueryCompanyStaffByAccountWithLocationsParams {}

// query company staff by account with locations response
message QueryCompanyStaffByAccountWithLocationsResult {
  // company staff with locations
  message CompanyStaffWithLocations {
    // staff
    moego.models.organization.v1.StaffBriefView staff = 1;
    // company
    moego.models.organization.v1.CompanyBriefView company = 2;
    // locations
    repeated moego.models.organization.v1.LocationBriefView locations = 3;
  }
  // company staffs with locations
  repeated CompanyStaffWithLocations company_staffs_with_locations = 1;
}

// add tax rule
message AddTaxRuleParams {
  // tax rule to add
  models.organization.v1.TaxRuleDef tax_rule = 1 [(validate.rules).message = {required: true}];
}

// add tax rule response
message AddTaxRuleResult {
  // tax rule id
  int64 id = 1;
}

// update tax rule
message UpdateTaxRuleParams {
  // tax id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // tax rule to update
  models.organization.v1.TaxRuleDef tax_rule = 2 [(validate.rules).message = {required: true}];
}

// update tax rule response
message UpdateTaxRuleResult {
  // update result
  bool success = 1;
}

// delete tax rule
message DeleteTaxRuleParams {
  // tax id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete tax rule response
message DeleteTaxRuleResult {
  // delete result
  bool success = 1;
}

// get tax rule list
message GetTaxRuleListParams {}

// get tax rule list response
message GetTaxRuleListResult {
  // tax rule list
  repeated models.organization.v1.TaxRuleModel tax_rule = 1;
}

// get clock in/out setting params
message GetClockInOutSettingParams {}

// get clock in out setting result
message GetClockInOutSettingResult {
  // clock in/out setting
  models.organization.v1.ClockInOutSettingModel clock_in_out_setting = 1;
}

// update clock in/out setting params
message UpdateClockInOutSettingParams {
  // update clock in/out setting params
  models.organization.v1.UpdateClockInOutSettingDef clock_in_out_setting = 6 [(validate.rules).message = {required: true}];
}

// update clock in/out setting result
message UpdateClockInOutSettingResult {
  // update result
  bool success = 1;
}

//company question query
message CompanyQuestionRecordQueryParams {
  //query company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

//company question result
message CompanyQuestionRecordQueryResult {
  // is have file question
  bool is_fill_question = 1;
}

// save company question record
message CompanyQuestionRecordParams {
  // pets per month
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pets per month
  string pet_per_month = 2 [(validate.rules).string.max_len = 50];
  //total location number
  optional string total_locations = 3 [(validate.rules).string.max_len = 50];
  //total van number
  optional string total_vans = 4 [(validate.rules).string.max_len = 50];
  //move from other software
  string move_from = 5 [(validate.rules).string.max_len = 50];
  //source from
  string source_from = 6 [(validate.rules).string.max_len = 50];
  //other source from
  optional string source_from_other = 7 [(validate.rules).string.max_len = 50];
}

// save company question record result
message CompanyQuestionRecordResult {
  // update result
  bool success = 1;
}

// sort company params
message SortCompanyParams {
  // ids
  repeated int64 ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// sort company result
message SortCompanyResult {}

// company service
service CompanyService {
  // create company
  rpc CreateCompany(CreateCompanyParams) returns (CreateCompanyResult);
  // query company staff by account
  rpc QueryCompanyStaffByAccount(QueryCompanyStaffByAccountParams) returns (QueryCompanyStaffByAccountResult);
  // update company preference setting
  rpc UpdateCompanyPreferenceSetting(UpdateCompanyPreferenceSettingParams) returns (UpdateCompanyPreferenceSettingResult);
  // get company preference setting
  rpc GetCompanyPreferenceSetting(GetCompanyPreferenceSettingParams) returns (GetCompanyPreferenceSettingResult);
  // switch company
  rpc SwitchCompany(SwitchCompanyParams) returns (SwitchCompanyResult) {}
  // get companies extra info
  rpc GetCompaniesExtraInfo(GetCompaniesExtraInfoParams) returns (GetCompaniesExtraInfoResponse) {}
  // query company staff by account with locations, only working locations
  rpc QueryCompanyStaffByAccountWithLocations(QueryCompanyStaffByAccountWithLocationsParams) returns (QueryCompanyStaffByAccountWithLocationsResult) {}
  // add tax rule
  rpc AddTaxRule(AddTaxRuleParams) returns (AddTaxRuleResult) {}
  // update tax rule
  rpc UpdateTaxRule(UpdateTaxRuleParams) returns (UpdateTaxRuleResult) {}
  // delete tax rule
  rpc DeleteTaxRule(DeleteTaxRuleParams) returns (DeleteTaxRuleResult) {}
  // get tax rule list
  rpc GetTaxRuleList(GetTaxRuleListParams) returns (GetTaxRuleListResult) {}
  // get clock in/out setting
  rpc GetClockInOutSetting(GetClockInOutSettingParams) returns (GetClockInOutSettingResult) {}
  // update clock in/out setting
  rpc UpdateClockInOutSetting(UpdateClockInOutSettingParams) returns (UpdateClockInOutSettingResult) {}
  //query company question status
  rpc GetCompanyQuestionRecord(CompanyQuestionRecordQueryParams) returns (CompanyQuestionRecordQueryResult) {}
  //save company question
  rpc CompanyQuestionRecordSave(CompanyQuestionRecordParams) returns (CompanyQuestionRecordResult) {}
  // sort company
  rpc SortCompany(SortCompanyParams) returns (SortCompanyResult) {}
}
