syntax = "proto3";

package moego.service.engagement.v1;

import "moego/models/engagement/v1/setting.proto";
import "moego/models/engagement/v1/setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.engagement.v1";

//SettingService
service SettingService {
  // GetSetting
  rpc GetSetting(GetSettingRequest) returns (GetSettingResponse);
  // UpdateSetting
  rpc UpdateSetting(UpdateSettingRequest) returns (UpdateSettingResponse);
  // tmp calling seats
  rpc TmpCallingSeats(TmpCallingSeatsRequest) returns (TmpCallingSeatsResponse);
}

//GetSettingRequest
message GetSettingRequest {
  //business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  //company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

//GetSettingResponse
message GetSettingResponse {
  //setting model
  moego.models.engagement.v1.Setting setting = 1;
  // seats setting
  moego.models.engagement.v1.SeatsSetting seats_setting = 2;
}

//UpdateSettingRequest
message UpdateSettingRequest {
  //business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];
  //staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  //company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
  //setting model
  moego.models.engagement.v1.UpdateSettingDef update_setting = 4;
  //update seats setting
  optional moego.models.engagement.v1.UpdateSeatsSettingDef update_seats_setting = 5;
}

//UpdateSettingResponse
message UpdateSettingResponse {
  //setting model
  moego.models.engagement.v1.Setting setting = 1;
  // seats setting
  moego.models.engagement.v1.SeatsSetting seats_setting = 2;
}

//TmpCallingSeatsRequest
message TmpCallingSeatsRequest {}

//TmpCallingSeatsResponse
message TmpCallingSeatsResponse {
  //tmp calling seats
  repeated moego.models.engagement.v1.TmpSeat tmp_calling_seats = 1;
  // seats limit
  uint32 seats_limit = 2;
  // is seats limit reached
  bool is_seats_limit_reached = 3;
}
