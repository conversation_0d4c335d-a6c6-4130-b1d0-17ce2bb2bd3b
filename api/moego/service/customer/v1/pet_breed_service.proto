syntax = "proto3";

package moego.service.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_pet_breed_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/customer/v1;customersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.customer.v1";

//  pet breed list output
message PetBreedListOutput {
  // pet breed list
  repeated moego.models.customer.v1.PetBreedModel pet_breed_list = 1;
}

// pet breed service
service PetBreedService {
  // get pet breed list
  rpc GetPetBreedList(google.protobuf.Empty) returns (PetBreedListOutput);
}
