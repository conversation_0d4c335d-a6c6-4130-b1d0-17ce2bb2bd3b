syntax = "proto3";

package moego.service.accounting.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "moego/models/accounting/v1/accounting_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/accounting/v1;accountingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.accounting.v1";

// compensate by time range request
message CompensateByTimeRangeRequest {
  // business id
  int64 business_id = 1;
  // time range
  google.type.Interval time_range = 2;
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 3;
}

// compensate by id request
message CompensateByIDRequest {
  // entity type
  moego.models.accounting.v1.SyncEntityType entity_type = 1;
  // entity id
  string entity_id = 2;
  // business id
  int64 business_id = 3;
  // channel type
  moego.models.accounting.v1.ChannelType channel_type = 4;
}

// CompensationService
service CompensationService {
  // compensate by time range
  rpc CompensateByTimeRange(CompensateByTimeRangeRequest) returns (google.protobuf.Empty);
  // compensate by id
  rpc CompensateByID(CompensateByIDRequest) returns (google.protobuf.Empty);
}
