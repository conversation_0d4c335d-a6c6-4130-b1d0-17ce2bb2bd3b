syntax = "proto3";

package moego.service.google_partner.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/google_partner/v1;googlepartnersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.google_partner.v1";

// SendFeeds request param
message SendFeedsRequest {
  // business ids
  repeated int64 business_ids = 1;
}

// Feed service
service FeedService {
  // Send feeds to Google for the given business ids, deprecated
  rpc SendFeeds(SendFeedsRequest) returns (google.protobuf.Empty) {
    option deprecated = true;
  }
  // Send feeds to Google for all businesses
  rpc SendFeedsForAll(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option deprecated = true;
  }
  // Send feeds to sandbox environment for the given business ids
  rpc SendFeedsToSandbox(SendFeedsRequest) returns (google.protobuf.Empty);
  // Send feeds to prod environment for the given business ids
  rpc SendFeedsToProd(SendFeedsRequest) returns (google.protobuf.Empty);
  // Send feeds to sandbox environment for all businesses
  rpc SendFeedsToSandboxForAll(google.protobuf.Empty) returns (google.protobuf.Empty);
  // Send feeds to prod environment for all businesses
  rpc SendFeedsToProdForAll(google.protobuf.Empty) returns (google.protobuf.Empty);
}
