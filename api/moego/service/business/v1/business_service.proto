// @since 2022-07-04 09:59:57
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.business.v1;

import "moego/models/business/v1/business_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business/v1;businesssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business.v1";

// get business input
message GetBusinessRequest {
  // id
  int64 id = 1;
}

// business profile service
service BusinessService {
  // get single business public view
  rpc GetBusinessPublicView(GetBusinessRequest) returns (moego.models.business.v1.BusinessModelPublicView);

  // get single business model
  rpc GetBusiness(GetBusinessRequest) returns (moego.models.business.v1.BusinessModel);
}
