syntax = "proto3";

package moego.service.reporting.v1;

import "google/protobuf/empty.proto";
import "moego/models/reporting/v1/reporting_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v1;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v1";

// the report service
service ReportService {
  // report campaign
  rpc ReportCampaign(ReportCampaignRequest) returns (google.protobuf.Empty);
  // update campaign metrics
  rpc UpdateCampaignMetrics(UpdateCampaignMetricsRequest) returns (google.protobuf.Empty);
  // replay appointments
  rpc ReplayAppointments(google.protobuf.Empty) returns (google.protobuf.Empty);
}

// report campaign request
message ReportCampaignRequest {
  // reports
  repeated models.reporting.v1.CampaignReportModel reports = 1;
}

// update campaign metrics request
message UpdateCampaignMetricsRequest {
  // report
  models.reporting.v1.CampaignReportModel report = 1;
}
