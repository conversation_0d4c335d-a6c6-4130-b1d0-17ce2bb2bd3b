syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/staff_access_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// get staff access tenant
message GetStaffAccessRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
}

// get staff access tenant response
message GetStaffAccessResponse {
  // staff access
  repeated moego.models.enterprise.v1.StaffAccess staff_access = 1;
}

// create staff access tenant
message CreateStaffAccessRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // resource list
  repeated models.enterprise.v1.TenantObject resources = 2;
}

// create staff access tenant response
message CreateStaffAccessResponse {
  // staff access
  repeated moego.models.enterprise.v1.StaffAccess staff_access = 1;
}

// update staff access tenant
message UpdateStaffAccessRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // resource list
  repeated models.enterprise.v1.TenantObject resources = 2;
}

// update staff access tenant response
message UpdateStaffAccessResponse {
  // staff access
  repeated moego.models.enterprise.v1.StaffAccess staff_access = 1;
}

// list staff access tenant
message ListStaffAccessRequest {
  // filter
  message Filter {
    // ids
    repeated int64 ids = 1;
    // tenant ids
    repeated int64 staff_ids = 2;
  }
  // filter
  Filter filter = 1;
}

// list staff access tenant response
message ListStaffAccessResponse {
  // staff access
  repeated moego.models.enterprise.v1.StaffAccess staff_access = 1;
}

// delete staff access tenant
message DeleteStaffAccessRequest {
  // staff access
  repeated int64 ids = 1 [(validate.rules).repeated.min_items = 1];
}

// delete staff access tenant response
message DeleteStaffAccessResponse {}

// staff access service
service StaffAccessService {
  // get staff access tenant
  rpc GetStaffAccess(GetStaffAccessRequest) returns (GetStaffAccessResponse);
  // create staff access tenant
  rpc CreateStaffAccess(CreateStaffAccessRequest) returns (CreateStaffAccessResponse);
  // update staff access tenant
  rpc UpdateStaffAccess(UpdateStaffAccessRequest) returns (UpdateStaffAccessResponse);
  // list staff access tenant
  rpc ListStaffAccess(ListStaffAccessRequest) returns (ListStaffAccessResponse);
  // delete staff access tenant
  rpc DeleteStaffAccess(DeleteStaffAccessRequest) returns (DeleteStaffAccessResponse);
}
