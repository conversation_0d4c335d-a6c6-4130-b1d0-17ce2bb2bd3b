// @since 2024-06-14 13:53:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.membership.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/event_bus/v1/event_defs.proto";
import "moego/models/event_bus/v1/subscription_models.proto";
import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/utils/v2/operation_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1;membershipsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.membership.v1";

// the membership service
service MembershipService {
  // create membership
  rpc CreateMembership(CreateMembershipRequest) returns (CreateMembershipResponse);
  // get membership
  rpc GetMembership(GetMembershipRequest) returns (GetMembershipResponse);
  // list membership
  rpc ListMemberships(ListMembershipsRequest) returns (ListMembershipsResponse);
  // update membership
  rpc UpdateMembership(UpdateMembershipRequest) returns (UpdateMembershipResponse);
  // delete membership
  rpc DeleteMembership(DeleteMembershipRequest) returns (DeleteMembershipResponse);
  // list available membership
  rpc ListMembershipsForSale(ListMembershipsForSaleRequest) returns (ListMembershipsForSaleResponse);
  // 查询推荐会员内容
  rpc ListRecommendedMemberships(ListRecommendMembershipsRequest) returns (ListRecommendMembershipsResponse);
  // 核销会员权益
  rpc RedeemMemberships(RedeemMembershipRequest) returns (RedeemMembershipResponse);
  // 查询核销历史详情
  rpc GetRedeemHistory(GetRedeemHistoryRequest) returns (GetRedeemHistoryResponse);
  // 查询用户的 membership
  rpc ListMembershipsForCustomer(ListMembershipsForCustomerRequest) returns (ListMembershipsForCustomerResponse);
  // 修改订单使用权益
  rpc UpsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest) returns (UpsertRecommendBenefitUsageResponse);
  // 查询订单使用权益
  rpc GetRecommendBenefitUsage(GetRecommendBenefitUsageRequest) returns (GetRecommendBenefitUsageResponse);
  // 查询perk的周期值
  rpc ListAllPerkCycle(ListAllPerkCycleRequest) returns (ListAllPerkCycleResponse);
  // get perk usage detail
  rpc GetPerkUsageDetail(GetPerkUsageDetailRequest) returns (GetPerkUsageDetailResponse);
  // flush membership perk
  rpc FlushMembershipPerk(FlushMembershipPerkRequest) returns (FlushMembershipPerkResponse);
  // 权益转换
  rpc TransferCredits(TransferCreditsRequest) returns (TransferCreditsResponse);
  // 订阅更新事件重放（数据修复接口）
  rpc ReplaySubscriptionUpdated(ReplaySubscriptionUpdatedRequest) returns (ReplaySubscriptionUpdatedResponse);
}

// create membership request
message CreateMembershipRequest {
  // the membership def
  moego.models.membership.v1.MembershipCreateDef membership_def = 1 [(validate.rules).message = {required: true}];
  // the company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // discount
  optional moego.models.membership.v1.CreateMembershipDiscountBenefitsDef membership_discount_benefits = 3;
  // quantity
  optional moego.models.membership.v1.CreateMembershipQuantityBenefitsDef membership_quantity_benefits = 4;
  // business id
  optional int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// create membership response
message CreateMembershipResponse {
  // the created membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// get membership request
message GetMembershipRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter by company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get membership response
message GetMembershipResponse {
  // the membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// list membership request
message ListMembershipsRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // name like, ignore case
  optional string name_like = 2 [(validate.rules).string = {max_len: 50}];
  // status in
  repeated moego.models.membership.v1.MembershipModel.Status status_in = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // id in
  repeated int64 id_in = 4 [(validate.rules).repeated = {
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // only online booking enabled
  optional bool only_online_booking_enabled = 5;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// list membership response
message ListMembershipsResponse {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 3;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 4;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// update membership request
message UpdateMembershipRequest {
  // id
  int64 id = 1;

  // filter by the current revision
  optional int32 revision = 2;
  // filter by company id
  optional int64 company_id = 4;

  // the membership def
  moego.models.membership.v1.MembershipUpdateDef membership_def = 3 [(validate.rules).message = {required: true}];

  // discount
  optional moego.models.membership.v1.UpdateMembershipDiscountBenefitsDef membership_discount_benefits = 5;
  // quantity
  optional moego.models.membership.v1.UpdateMembershipQuantityBenefitsDef membership_quantity_benefits = 6;

  // business id
  optional int64 business_id = 7 [(validate.rules).int64 = {gt: 0}];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// update membership response
message UpdateMembershipResponse {
  // the updated membership
  moego.models.membership.v1.MembershipModel membership = 1;
  // discount
  optional moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // quantity
  optional moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// delete membership request
message DeleteMembershipRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter by company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// delete membership response
message DeleteMembershipResponse {}

// list membership for sale params
message ListMembershipsForSaleRequest {
  //filter
  message Filter {
    // company id
    optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
    // name like, ignore case
    optional string name_like = 2 [(validate.rules).string = {max_len: 50}];
    // status in
    repeated moego.models.membership.v1.MembershipModel.Status status_in = 3 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // id in
    repeated int64 id_in = 4 [(validate.rules).repeated = {
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
    // only online booking enabled
    optional bool only_online_booking_enabled = 5;
    // pet filter
    repeated models.membership.v1.PetFilter pet_filters = 6;
  }
  // filter
  optional Filter filter = 1;
  // pagination, default size is 20
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list memberships for sale result
message ListMembershipsForSaleResponse {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// list recommended memberships request
message ListRecommendMembershipsRequest {
  // filter
  message Filter {
    // membership ids if set, BenefitRecommendView will be based on it
    repeated int64 target_membership_ids = 5;
  }
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // context
  moego.models.membership.v1.RedeemContext context = 4;
  // filter
  optional Filter filter = 6;
}

// list recommended memberships response
message ListRecommendMembershipsResponse {
  // the recommended memberships
  repeated moego.models.membership.v1.MembershipModel recommended = 1;
  // all memberships
  repeated moego.models.membership.v1.MembershipModel all = 2;
  // the benefit combination
  repeated moego.models.membership.v1.BenefitRecommendView benefit_combination = 3;
  // the usage views
  repeated moego.models.membership.v1.MembershipUsageView usage_views = 4;
}

// redeem membership request
message RedeemMembershipRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // order id
  int64 order_id = 3;
  // source type
  moego.models.membership.v1.SourceType source_type = 4;
  // source id
  int64 source_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// redeem membership response
message RedeemMembershipResponse {}

// get redeem history request
message GetRedeemHistoryRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // the customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // the membership id
  int64 membership_id = 4 [(validate.rules).int64 = {gt: 0}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// get redeem history response
message GetRedeemHistoryResponse {
  // the redeem history
  repeated moego.models.membership.v1.IncludeBenefitView included_benefits = 1;

  // the redeem history
  repeated moego.models.membership.v1.RedeemHistory redeem_history = 2;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// list user memberships request
message ListMembershipsForCustomerRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // the customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// list user memberships response
message ListMembershipsForCustomerResponse {
  // the user memberships
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // membership discount benefits
  repeated moego.models.membership.v1.MembershipDiscountBenefitsDef membership_discount_benefits = 2;
  // membership quantity benefit
  repeated moego.models.membership.v1.MembershipQuantityBenefitsDef membership_quantity_benefits = 3;
}

// query benefit usage request
message QueryBenefitSummaryRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // the customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // the membership id
  int64 membership_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// query benefit usage response
message QueryBenefitSummaryResponse {
  // discounts
  repeated moego.models.membership.v1.BenefitSummaryView discounts = 1;
  // quantities
  repeated moego.models.membership.v1.BenefitSummaryView quantities = 2;
}

// upsert recommend benefit usage request
message UpsertRecommendBenefitUsageRequest {
  // the order id
  int64 order_id = 1;
  // the customer id
  int64 customer_id = 2;
  // all memberships
  repeated moego.models.membership.v1.MembershipModel all_memberships = 3;
  // the recommend benefit combination
  repeated moego.models.membership.v1.BenefitRecommendView benefit_combination = 4;
  // the usage views
  repeated moego.models.membership.v1.MembershipUsageView usage_views = 6;
}

// upsert recommend benefit usage response
message UpsertRecommendBenefitUsageResponse {
  // all memberships
  repeated moego.models.membership.v1.MembershipModel all_memberships = 1;
  // the recommend benefit combination
  repeated moego.models.membership.v1.BenefitRecommendView benefit_combination = 2;
  // the usage views
  repeated moego.models.membership.v1.MembershipUsageView usage_views = 3;
}

// get recommend benefit usage request
message GetRecommendBenefitUsageRequest {
  // the invoice id
  int64 order_id = 1;
}

// get recommend benefit usage response
message GetRecommendBenefitUsageResponse {
  // all memberships
  repeated moego.models.membership.v1.MembershipModel all_memberships = 2;
  // the recommend benefit combination
  repeated moego.models.membership.v1.BenefitRecommendView benefit_combination = 3;
  // the usage views
  repeated moego.models.membership.v1.MembershipUsageView usage_views = 4;
}

// The request message for list all perk cycle
message ListAllPerkCycleRequest {
  // membership id
  int64 membership_id = 1;
  // customer id
  int64 customer_id = 2;
  //company id
  int64 company_id = 3;
}

// The response message for list all perk cycle
message ListAllPerkCycleResponse {
  // the perk cycle items
  repeated models.membership.v1.PerkCycleItemDef perk_cycle_item = 1;
}

// The request message for get perk usage detail
message GetPerkUsageDetailRequest {
  // filter
  message Filter {
    // validity start time
    optional google.protobuf.Timestamp validity_start_time = 2;
  }
  // filter
  optional Filter filter = 1;
  // company id
  int64 company_id = 2;

  // membership id
  int64 membership_id = 3;

  // customer id
  int64 customer_id = 4;
}

// The response message for get perk usage detail
message GetPerkUsageDetailResponse {
  // the included benefits
  repeated moego.models.membership.v1.IncludeBenefitView included_benefits = 1;
  // 之前不知道为什么不展示 discount benefits，现在只能先用另外一个字段返回
  repeated moego.models.membership.v1.IncludeBenefitView discount_benefits = 2;
}

// flush membership perk response
message FlushMembershipPerkRequest {
  // company id
  optional int64 company_id = 1;
}

// flush membership perk response
message FlushMembershipPerkResponse {
  // success
  bool success = 1;
}

// transfer credits request
message TransferCreditsRequest {
  // customer id
  int64 customer_id = 1;
  // company id
  int64 company_id = 2;
  // quantity id
  int64 quantity_id = 3;
  // transfer quantity nums
  google.protobuf.Timestamp validity_start_time = 4;
  // transfer credit num
  int64 transfer_quantity_num = 5;
}

// transfer credits response
message TransferCreditsResponse {}

// ReplaySubscriptionUpdatedRequest
message ReplaySubscriptionUpdatedRequest {
  // event_type
  moego.models.event_bus.v1.EventType event_type = 1;
  // sub_event
  moego.models.event_bus.v1.SubscriptionUpdated sub_event = 2;
}

// ReplaySubscriptionUpdatedResponse
message ReplaySubscriptionUpdatedResponse {
  // success
  bool is_success = 1;
  // message
  string message = 2;
}
