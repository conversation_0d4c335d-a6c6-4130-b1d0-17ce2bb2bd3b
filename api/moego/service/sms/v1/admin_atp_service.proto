// @since 2023-09-05 17:29:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.sms.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/sms/v1;smssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.sms.v1";

// submit or flush atp status
message SubmitAndFlushAtpStatusRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// response for submit flush atp
message SubmitAndFlushAtpStatusResponse {}

// sms atp for admin support api
service AdminAtpService {
  // submit or flush atp status
  rpc SubmitAndFlushAtpStatus(SubmitAndFlushAtpStatusRequest) returns (SubmitAndFlushAtpStatusResponse);
}
