// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.fulfillment.v1;

import "moego/models/fulfillment/v1/staff_time_slot_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1;fulfillmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.fulfillment.v1";

// list staff time slots request
message ListStaffTimeSLotsRequest {
  // The filter
  Filter filter = 1 [(validate.rules).message.required = true];

  // The filter message
  message Filter {
    // The fulfillment id
    repeated int64 fulfillment_ids = 1 [(validate.rules).repeated = {
      min_items: 0
      max_items: 1000
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// list staff time slots response
message ListStaffTimeSLotsResponse {
  // the staff time slots
  repeated moego.models.fulfillment.v1.StaffTimeSlotModel time_slots = 1;
}

// the staff_time_slot service
service StaffTimeSlotService {
  // list staff time slots
  rpc ListStaffTimeSLots(ListStaffTimeSLotsRequest) returns (ListStaffTimeSLotsResponse);
}
