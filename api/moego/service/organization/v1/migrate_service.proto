// @since 2024-02-20 16:14:40
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/migrate_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// get company migrate status request
message GetMigrateStatusRequest {
  // identity id, company id or business id
  oneof identifier {
    option (validate.required) = true;
    // company id
    int64 company_id = 1;
    // business id
    int64 business_id = 2;
  }
}

// get company migrate status response
message GetMigrateStatusResponse {
  // migrate status
  models.organization.v1.MigrateStatus status = 1;
}

// the migrate service
service MigrateService {
  // get company migrate status
  rpc GetMigrateStatus(GetMigrateStatusRequest) returns (GetMigrateStatusResponse);
}
