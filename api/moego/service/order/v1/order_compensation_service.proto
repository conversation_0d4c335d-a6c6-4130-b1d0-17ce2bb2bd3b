syntax = "proto3";

package moego.service.order.v1;

import "google/type/interval.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// get order id by business id and create time range request
message GetIDByBusinessIDAndTimeRangeRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // time range
  google.type.Interval time_range = 2;
}

// order id response
message GetIDByBusinessIDAndTimeRangeResponse {
  // order id list
  repeated int64 order_ids = 1;
}

// order compensation service internal api
service OrderCompensationService {
  // get order id by business id and create time range
  rpc GetIDByBusinessIDAndTimeRange(GetIDByBusinessIDAndTimeRangeRequest) returns (GetIDByBusinessIDAndTimeRangeResponse);
}
