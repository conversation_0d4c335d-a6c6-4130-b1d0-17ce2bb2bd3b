syntax = "proto3";

package moego.service.marketing.v1;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1;marketingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.marketing.v1";

// Marketing merge client request
message MergeCustomerMarketingRecordRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// Marketing merge client response
message MergeCustomerMarketingRecordResponse {}

//marketing merge service
service MarketingMergeService {
  //marketing merge customer
  rpc MergeCustomerMarketingRecord(MergeCustomerMarketingRecordRequest) returns (MergeCustomerMarketingRecordResponse);
}
