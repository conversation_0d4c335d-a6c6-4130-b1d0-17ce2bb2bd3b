syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/playgroup_defs.proto";
import "moego/models/offering/v1/playgroup_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// list playgroup request
message ListPlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
  // playgroup ids
  repeated int64 ids = 3 [(validate.rules).repeated = {
    min_items: 0
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // include deleted
  optional bool include_deleted = 4;
}

// list playgroup response
message ListPlaygroupResponse {
  // playgroups
  repeated moego.models.offering.v1.PlaygroupModel playgroups = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// create playgroup request
message CreatePlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // create playgroup def
  moego.models.offering.v1.CreatePlaygroupDef playgroup = 3;
}

// create playgroup response
message CreatePlaygroupResponse {
  // playgroup
  moego.models.offering.v1.PlaygroupModel playgroup = 1;
}

// update playgroup request
message UpdatePlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // update playgroup def
  moego.models.offering.v1.UpdatePlaygroupDef playgroup = 3;
}

// update playgroup response
message UpdatePlaygroupResponse {
  // playgroup
  moego.models.offering.v1.PlaygroupModel playgroup = 1;
}

// delete playgroup request
message DeletePlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // playgroup id
  int64 id = 3 [(validate.rules).int64.gt = 0];
}

// delete playgroup response
message DeletePlaygroupResponse {}

// sort playgroup request
message SortPlaygroupRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // playgroup id list
  repeated int64 ids = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort playgroup response
message SortPlaygroupResponse {}

// playgroup service
service PlaygroupService {
  // list playgroup
  rpc ListPlaygroup(ListPlaygroupRequest) returns (ListPlaygroupResponse);
  // create playgroup
  rpc CreatePlaygroup(CreatePlaygroupRequest) returns (CreatePlaygroupResponse);
  // update playgroup
  rpc UpdatePlaygroup(UpdatePlaygroupRequest) returns (UpdatePlaygroupResponse);
  // delete playgroup
  rpc DeletePlaygroup(DeletePlaygroupRequest) returns (DeletePlaygroupResponse);
  // sort playgroup
  rpc SortPlaygroup(SortPlaygroupRequest) returns (SortPlaygroupResponse);
}
