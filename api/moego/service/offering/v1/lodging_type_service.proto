syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/lodging_enum.proto";
import "moego/models/offering/v1/lodging_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

/**
 * Request body for create LodgingType service
 */
message CreateLodgingTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // name of the lodging type
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the lodging type
  string description = 3 [(validate.rules).string = {max_len: 1024}];
  // images of this lodging type
  repeated string photo_list = 4;
  // max pet number of this lodging type
  int32 max_pet_num = 5 [(validate.rules).int32.gt = 0];
  // max pet total weight of this lodging type
  int32 max_pet_total_weight = 6 [deprecated = true];
  // token staff id
  int64 token_staff_id = 7 [(validate.rules).int64.gt = 0];
  // available for all pet size
  bool all_pet_sizes = 8 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 9;
  // lodging unit type in this lodging type
  moego.models.offering.v1.LodgingUnitType lodging_unit_type = 10;
  // whether the lodging type is available for all pet size
  bool pet_size_filter = 11;
  // source
  optional moego.models.offering.v1.LodgingTypeModel.Source source = 12;
}

/**
 * Request body for photo list
 */
message PhotoList {
  // image list
  repeated string photo_list = 1 [(validate.rules).repeated = {min_items: 0}];
}

/**
 * Response body for create LodgingType
 */
message CreateLodgingTypeResponse {
  // lodging type
  models.offering.v1.LodgingTypeModel lodging_type = 1;
}

/**
 * Request body for update LodgingType
 */
message UpdateLodgingTypeRequest {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // company id for authentication
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // name of the lodging type
  optional string name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // description of the lodging type
  optional string description = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 1024
  }];
  // images of this lodging type
  optional PhotoList photo_list = 6;
  // max pet number of this lodging type
  optional int32 max_pet_num = 7 [(validate.rules).int32.gt = 0];
  // max pet total weight of this lodging type
  optional int32 max_pet_total_weight = 8 [deprecated = true];
  // available for all pet size
  optional bool all_pet_sizes = 9 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 10;
  // lodging unit type in this lodging type
  optional moego.models.offering.v1.LodgingUnitType lodging_unit_type = 11;
  // whether the lodging type is available for all pet size
  optional bool pet_size_filter = 12;
  // source
  optional models.offering.v1.LodgingTypeModel.Source source = 13;
}

/**
 * Response body for update LodgingType
 */
message UpdateLodgingTypeResponse {
  // lodging type
  models.offering.v1.LodgingTypeModel lodging_type = 1;
}

/**
 * Request body for delete LodgingType
 */
message DeleteLodgingTypeRequest {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // token staff id
  int64 token_staff_id = 2 [(validate.rules).int64.gt = 0];
  // company id for authentication
  optional int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

/**
 * Response body for delete LodgingType
 */
message DeleteLodgingTypeResponse {}

/**
 * Request body for get LodgingType list
 */
message GetLodgingTypeListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

/**
 * get LodgingType list response
 */
message GetLodgingTypeListResponse {
  // lodging type list
  repeated models.offering.v1.LodgingTypeModel lodging_type_list = 1;
}

/**
 * Request body for get LodgingType
 */
message MGetLodgingTypeRequest {
  // lodging type id list
  repeated int64 id_list = 1 [(validate.rules).repeated = {min_items: 0}];
}

/**
 * get LodgingType list response
 */
message MGetLodgingTypeResponse {
  // lodging type list
  repeated models.offering.v1.LodgingTypeModel lodging_type_list = 1;
}

// The params for sort lodging type by ids
message SortLodgingTypeByIdsRequest {
  // ids of lodging type to sort
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // the company id
  optional int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // the login staff id
  optional int64 staff_id = 3 [(validate.rules).int64.gt = 0];
}

// The result for sort lodging type by ids
message SortLodgingTypeByIdsResponse {}

// lodging type service
service LodgingTypeService {
  // create lodging type
  rpc CreateLodgingType(CreateLodgingTypeRequest) returns (CreateLodgingTypeResponse);
  // update lodging type
  rpc UpdateLodgingType(UpdateLodgingTypeRequest) returns (UpdateLodgingTypeResponse);
  // delete lodging type
  rpc DeleteLodgingType(DeleteLodgingTypeRequest) returns (DeleteLodgingTypeResponse);
  // get lodging type list
  rpc GetLodgingTypeList(GetLodgingTypeListRequest) returns (GetLodgingTypeListResponse);
  // mget
  rpc MGetLodgingType(MGetLodgingTypeRequest) returns (MGetLodgingTypeResponse);
  // sort lodging type by ids
  rpc SortLodgingTypeByIds(SortLodgingTypeByIdsRequest) returns (SortLodgingTypeByIdsResponse);
}
