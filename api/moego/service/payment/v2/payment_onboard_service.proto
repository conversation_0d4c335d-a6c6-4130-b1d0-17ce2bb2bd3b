syntax = "proto3";

package moego.service.payment.v2;

import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/onboard_defs.proto";
import "moego/models/payment/v2/onboard_enums.proto";
import "moego/models/payment/v2/onboard_models.proto";
import "moego/models/payment/v2/payment_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// Payment's onboard service.
service PaymentOnboardService {
  // Get onboard information for specific channel.
  rpc GetOnboard(GetOnboardRequest) returns (GetOnboardResponse);
  // Proceed onboard for specific channel.
  rpc ProceedOnboard(ProceedOnboardRequest) returns (ProceedOnboardResponse);
  // User confirms onboard. Should be only called in ONBOARDED status.
  rpc ConfirmOnboard(ConfirmOnboardRequest) returns (ConfirmOnboardResponse);
  // Get channel account detail.
  rpc GetChannelAccountDetail(GetChannelAccountDetailRequest) returns (GetChannelAccountDetailResponse);
  // Get channel account
  rpc GetChannelAccount(GetChannelAccountRequest) returns (GetChannelAccountResponse);
  // Request an update to the onboard information.
  rpc RequestOnboardUpdate(RequestOnboardUpdateRequest) returns (RequestOnboardUpdateResponse);
}

// Request for GetOnboard
message GetOnboardRequest {
  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道，先强制要求传，固定传 Adyen
  moego.models.payment.v2.ChannelType channel_type = 3;
}

// Response for GetOnboard
message GetOnboardResponse {
  // Onboard 状态：
  // - INITIAL：初始状态
  // - CONFIGURED：配置完毕，onboarding 中
  // - ONBOARDED：已完成 onboarding，待用户确认
  // - FINISHED：完成 onboarding，包括后续有 upcoming verification 的状态
  // - NOT_APPLICABLE：不适用，预留的状态，目前的接口设计不会返回这种状态
  moego.models.payment.v2.OnboardStatus status = 1;
  // 包含的步骤，status 为 CONFIGURED 时有效。
  repeated moego.models.payment.v2.OnboardStep steps = 2;
  // 当前步骤下标（相对于 steps 字段）
  int32 current_step = 3;
}

// Request for GetChannelAccount
message GetChannelAccountRequest {
  // user from payment
  models.payment.v2.User user = 1;
  // 渠道
  moego.models.payment.v2.ChannelType channel_type = 2;
}

// Response for GetChannelAccount
message GetChannelAccountResponse {
  // 账号的基本信息
  moego.models.payment.v2.ChannelAccount channel_account = 1;
}

// Request for ProceedOnboard
message ProceedOnboardRequest {
  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道
  moego.models.payment.v2.ChannelType channel_type = 3;
  // 链接内完成后跳回的地址
  optional string return_url = 4;
  // 在 configure 之前需要提供的渠道特定的数据。
  oneof params {
    // Adyen 的 pre configuration，只有 OnboardStatus 为 INITIAL 状态时会被使用。
    moego.models.payment.v2.AdyenPreConfigDef adyen_pre_config = 5;
  }
}

// Response for ProceedOnboard
message ProceedOnboardResponse {
  // 链接，若有则需要跳转到该链接来推进 onboarding
  optional string url = 1;
}

// Request for ConfirmOnboardFinished
message ConfirmOnboardRequest {
  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道
  moego.models.payment.v2.ChannelType channel_type = 3;
}

// Response for ConfirmOnboardFinished
message ConfirmOnboardResponse {}

// Request for GetChannelAccountDetail
message GetChannelAccountDetailRequest {
  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道
  moego.models.payment.v2.ChannelType channel_type = 3;
}

// Response for GetChannelAccountDetail
message GetChannelAccountDetailResponse {
  // 账号的基本信息
  moego.models.payment.v2.ChannelAccount channel_account = 1;
  // 需要关注的 KYC 验证信息
  repeated moego.models.payment.v2.OnboardVerification verifications = 2;
  // Bank Accounts
  repeated moego.models.payment.v2.BankAccount bank_accounts = 3;
}

// Request for RequestOnboardUpdate
message RequestOnboardUpdateRequest {
  // Additional params for Adyen.
  message AdyenParams {
    // Additional configuration for current business.
    optional moego.models.payment.v2.AdyenBusinessConfig business_config = 1;
  }

  // Company ID
  int64 company_id = 1;
  // Business ID
  int64 business_id = 2;
  // 渠道
  moego.models.payment.v2.ChannelType channel_type = 3;
  // 链接内完成后跳回的地址
  optional string return_url = 4;
  // 各个渠道可能需要的额外参数
  oneof channel_params {
    // Adyen
    AdyenParams adyen = 5;
  }
}

// Response for RequestOnboardUpdate
message RequestOnboardUpdateResponse {
  // 链接，若有则需要跳转到该链接来更新 onboard 信息
  optional string url = 1;
}
