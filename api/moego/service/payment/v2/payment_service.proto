syntax = "proto3";

package moego.service.payment.v2;

import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// PaymentService 支付服务
service PaymentService {
  // GetPaymentVersion 获取支付版本, 用于前端判断该走哪个支付流程，控制灰度
  rpc GetPaymentVersion(GetPaymentVersionRequest) returns (GetPaymentVersionResponse);

  // GetChannelBalanceAccountInfo 获取渠道 账户余额 信息
  rpc GetChannelBalanceAccountInfo(GetChannelBalanceAccountInfoRequest) returns (GetChannelBalanceAccountInfoResponse);

  // CreatePayment 创建支付单据，返回支付单据 ID
  rpc CreatePayment(CreatePaymentRequest) returns (CreatePaymentResponse);
  // 创建合单支付，多笔订单合成一次支付
  rpc CreateCombinedPayment(CreateCombinedPaymentRequest) returns (CreateCombinedPaymentResponse);
  // Cancel 取消支付
  rpc CancelPayment(CancelPaymentRequest) returns (CancelPaymentResponse);
  // GetPayData 获取支付时所需的数据，用于前端加载第三方支付组件，注意此 API 只关注 Pay 支付动作
  rpc GetPayData(GetPayDataRequest) returns (GetPayDataResponse);
  // Pay 确认支付 Confirm，在用户提交完所有支付凭证后调用
  // 合单也是调用这个接口，以 transaction 为主体
  rpc PayPayment(PayPaymentRequest) returns (PayPaymentResponse);
  // SubmitActionDetail 提交支付 detail 凭证，一般是在完成 Pay 要求的 Action 后调用
  rpc SubmitActionDetail(SubmitActionDetailRequest) returns (SubmitActionDetailResponse);
  // GetPayment 获取支付单据
  rpc GetPayment(GetPaymentRequest) returns (GetPaymentResponse);
  // ListPayment 获取支付单据列表
  rpc ListPayment(ListPaymentRequest) returns (ListPaymentResponse);

  // 添加绑定的支付方式
  rpc AddRecurringPaymentMethod(AddRecurringPaymentMethodRequest) returns (AddRecurringPaymentMethodResponse);
  // 删除绑定的支付方式
  rpc DeleteRecurringPaymentMethod(DeleteRecurringPaymentMethodRequest) returns (DeleteRecurringPaymentMethodResponse);
  // 将payment method设置为primary
  rpc SetRecurringPaymentMethodPrimary(SetRecurringPaymentMethodPrimaryRequest) returns (SetRecurringPaymentMethodPrimaryResponse);
  // ListRecurringPaymentMethods 获取用户的所有绑定的支付方式，如 COF、ACH等
  rpc ListRecurringPaymentMethods(ListRecurringPaymentMethodsRequest) returns (ListRecurringPaymentMethodsResponse);
  // 获取 transaction 列表
  rpc ListTransaction(ListTransactionRequest) returns (ListTransactionResponse);
  // 获取渠道的 支付模型 ，对于pay online这种场景需要
  rpc GetChannelPayment(GetChannelPaymentRequest) returns (GetChannelPaymentResponse);
  // 根据 transaction 查询 payment list
  rpc ListPaymentByTransaction(ListPaymentByTransactionRequest) returns (ListPaymentByTransactionResponse);
  // 获取Transaction
  rpc GetTransaction(GetTransactionRequest) returns (GetTransactionResponse);
  // 获取payment view
  rpc GetPaymentView(GetPaymentViewRequest) returns (GetPaymentViewResponse);
  // 导出 transaction
  rpc ExportTransactionList(ExportTransactionListRequest) returns (ExportTransactionListResponse);

  // GetPaymentSetting 获取支付设置
  rpc GetPaymentSetting(GetPaymentSettingRequest) returns (GetPaymentSettingResponse);
  // UpdatePaymentSetting 更新支付设置
  rpc UpdatePaymentSetting(UpdatePaymentSettingRequest) returns (UpdatePaymentSettingResponse);
}

// export transaction list request
message ExportTransactionListRequest {
  // filter
  ListTransactionRequest.Filter filter = 1 [(validate.rules).message = {required: true}];
  // 排序条件
  repeated ListTransactionRequest.OrderBy order_bys = 2;
  // company_id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
  // 文件名，不传默认 uuid
  optional string file_name = 6;
  // 文件类型，不传默认 xlsx
  optional string file_type = 7;
}

// export transaction list response
message ExportTransactionListResponse {
  // file id
  int64 file_id = 1;
}

// get payment view request
message GetPaymentViewRequest {
  // 支付单据id
  int64 id = 1;
}

// get payment view response
message GetPaymentViewResponse {
  // payment view
  models.payment.v2.PaymentView payment = 1;
}

// get transaction request
message GetTransactionRequest {
  // transaction id
  int64 id = 1;
}

// get transaction response
message GetTransactionResponse {
  // transaction view
  models.payment.v2.PaymentTransactionView transaction = 1;
}

// get channel payment request
message GetChannelPaymentRequest {
  // 收款方
  models.payment.v2.User payee = 1;
}

// get channel payment response
message GetChannelPaymentResponse {
  // channel payment
  oneof channel_payment {
    // stripe channel payment
    models.payment.v2.StripeChannelPayment stripe_channel_payment = 1;
  }
  // channel type
  models.payment.v2.ChannelType channel_type = 11;
}

// Request for GetPaymentVersion
message GetPaymentVersionRequest {
  // 收款方
  models.payment.v2.User payee = 1;
}

// Response for GetPaymentVersion
message GetPaymentVersionResponse {
  // 支付版本
  models.payment.v2.PaymentVersion payment_version = 1;
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// Request for GetChannelBalanceAccountInfo
message GetChannelBalanceAccountInfoRequest {
  // user
  models.payment.v2.User user = 1;
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// Response for GetChannelBalanceAccountInfo
message GetChannelBalanceAccountInfoResponse {
  // available balance
  google.type.Money available_balance = 1;
  // pending balance
  google.type.Money pending_balance = 2;
}

// Request for CreatePayment
message CreatePaymentRequest {
  // 必填，上层业务系统类型
  models.payment.v2.ExternalType external_type = 1;
  // 必填，上层业务系统内单据 ID
  string external_id = 2;
  // 付款方，可选
  models.payment.v2.User payer = 3;
  // 收款方
  models.payment.v2.User payee = 4;
  // 金额
  google.type.Money amount = 5;
  // 支付类型，决定是pre-pay、pre-auth还是常规支付
  models.payment.v2.PaymentModel.PaymentType payment_type = 6;
}

// Response for CreatePayment
message CreatePaymentResponse {
  // 生成的支付单据
  models.payment.v2.PaymentModel payment = 1;
}

// cancel payment request
message CancelPaymentRequest {
  // 支付单据id
  int64 id = 1;
}

// cancel payment response
message CancelPaymentResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
}

// Request for GetPayData
message GetPayDataRequest {
  // Business ID
  int64 business_id = 1;
  // 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
  optional models.payment.v2.ChannelType channel_type = 2;
}

// Response for GetPayData
message GetPayDataResponse {
  // adyen data
  message AdyenData {
    // data
    string data = 1;
  }

  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 2;
  }
}

// Request for Pay
message PayPaymentRequest {
  // 支付单据 id
  int64 id = 1;
  // 支付方式
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 2;
  // 支付凭证
  models.payment.v2.PaymentMethod.Detail detail = 3;
  // 是否添加cv fee,不传的时候后端判断
  optional bool add_convenience_fee = 4;
  // payer, 一般是customer name
  string payer = 5;
  // payment description 支付描述 自定义
  string description = 6;
}

// Response for Pay
message PayPaymentResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件
  // e.g. adyen 3ds2:
  // `{
  //   "resultCode": "IdentifyShopper",
  //   "action": {
  //     "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
  //     "paymentMethodType": "scheme",
  //     "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
  //     "subtype": "fingerprint",
  //     "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
  //     "type": "threeDS2"
  //   }
  // }`
  string channel_response = 2;
  // channel payment
  models.payment.v2.ChannelPayment channel_payment = 3;
}

// Request for SubmitActionDetail
message SubmitActionDetailRequest {
  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // Action Result，前端从组件拿到的原始数据，
  // e.g. ayden 3ds2:
  // `{
  //   "details": {
  //     "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
  //   }
  // }`
  string action_result = 2;
}

// Response for SubmitPayDetail
message SubmitActionDetailResponse {
  // msg 可以展示给用户的信息
  string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
  // e.g. adyen:
  // `{
  //   "resultCode": "Authorised",
  //   "pspReference": "V4HZ4RBFJGXXGN82"
  // }`
  string channel_response = 2;
}

// get payment request
message GetPaymentRequest {
  // 支付单据id
  int64 id = 1;
}

// get payment response
message GetPaymentResponse {
  // payment 实体
  models.payment.v2.PaymentModel payment = 1;
}

// list payment request
message ListPaymentRequest {
  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // 买家
    repeated models.payment.v2.User payers = 1;
    // 卖家
    repeated models.payment.v2.User payees = 2;
    // order id
    repeated int64 order_ids = 3;
    // order payment id
    repeated int64 order_payment_ids = 4;
    // payment
    repeated int64 ids = 5;
    // 查询时间范围
    optional google.type.Interval time_range = 6;
  }
  // filter
  Filter filter = 2;
}

// list payment response
message ListPaymentResponse {
  // 支付列表
  repeated models.payment.v2.PaymentView payment_views = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}

// add recurring payment method request
message AddRecurringPaymentMethodRequest {
  // customer id
  optional int64 customer_id = 1;
  // customer code
  optional string encrypted_customer_id = 2;
  // payee
  models.payment.v2.User payee = 3;
  // channel type
  optional models.payment.v2.ChannelType channel_type = 4;
  // payment method type
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 5;
  // 支付凭证
  models.payment.v2.PaymentMethod.Detail detail = 6;
  // 透传参数，一般是用户自定义的额外信息
  optional models.payment.v2.RecurringPaymentMethodModel.Extra extra = 7;
}

// add recurring payment method response
message AddRecurringPaymentMethodResponse {
  // 已保存的支付方式
  optional models.payment.v2.RecurringPaymentMethodModel recurring_payment_method = 1;
  // 渠道返回的原始数据
  string channel_response = 2;
}

// delete recurring payment method request
message DeleteRecurringPaymentMethodRequest {
  // 存储的 payment method id
  int64 payment_method_id = 1;
}

// delete recurring payment method response
message DeleteRecurringPaymentMethodResponse {}

// set recurring payment method primary request
message SetRecurringPaymentMethodPrimaryRequest {
  // payment method id
  int64 payment_method_id = 1;
}

// set recurring payment method primary response
message SetRecurringPaymentMethodPrimaryResponse {
  // 已设置的 payment method
  models.payment.v2.RecurringPaymentMethodModel recurring_payment_method = 1;
}

// list recurring payment method request
message ListRecurringPaymentMethodsRequest {
  // 分页请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // 用户
    repeated models.payment.v2.User users = 1;
    // payment method ids
    repeated int64 recurring_payment_method_ids = 2;
    // users
  }

  // filter
  Filter filter = 2;
}

// list recurring payment method response
message ListRecurringPaymentMethodsResponse {
  // 分页
  utils.v2.PaginationResponse pagination_response = 1;
  // 已保存的支付方式
  repeated models.payment.v2.RecurringPaymentMethodModel payment_methods = 2;
}

// create combined payment request
message CreateCombinedPaymentRequest {
  // combined item, 单个请求金额
  message CombinedItem {
    // 必填，上层业务系统类型
    models.payment.v2.ExternalType external_type = 1;
    // 必填，上层业务系统内单据 ID
    string external_id = 2;
    // 付款方，可选
    models.payment.v2.User payer = 3;
    // 收款方
    models.payment.v2.User payee = 4;
    // 金额
    google.type.Money amount = 5;
    // extra，非关键信息
    message Extra {
      // 模块
      string module = 1;
      // 模块id
      int64 module_id = 2;
      // invoice id
      int64 invoice_id = 3;
      // company id
      int64 company_id = 4;
      // staff id
      int64 staff_id = 5;
    }
    // extra
    Extra extra = 6;
  }
  // 必填，单个支付请求
  repeated CombinedItem combined_items = 1;
  // 支付类型，合并支付的这里都是传standard的了
  models.payment.v2.PaymentModel.PaymentType payment_type = 2;
}

// create combined payment response
message CreateCombinedPaymentResponse {
  // transaction id
  int64 transaction_id = 1;
  // 生成的支付单据
  repeated models.payment.v2.PaymentModel payment_models = 2;
}

// list payment transaction request
message ListTransactionRequest {
  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;

  // filter
  message Filter {
    // 渠道类型
    repeated models.payment.v2.ChannelType channel_types = 1 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // 查询时间范围
    google.type.Interval interval = 2 [(validate.rules).message = {required: true}];
    // 支付方法类型
    repeated models.payment.v2.PaymentMethod.MethodType method_types = 3 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // business id
    optional int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
    // customer id
    optional int64 customer_id = 5 [(validate.rules).int64 = {gt: 0}];
    // transaction status
    repeated models.payment.v2.PaymentTransactionModel.TransactionStatus statuses = 6 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
  // filter
  Filter filter = 2 [(validate.rules).message = {required: true}];
  // 排序条件
  message OrderBy {
    // 是否降序
    bool desc = 1;
    // 排序字段
    oneof field {
      // 创建时间
      bool created_at = 2;
      // ID
      bool id = 3;
    }
  }
  // 排序条件
  repeated OrderBy order_bys = 3;
  // company_id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// list payment transaction response
message ListTransactionResponse {
  // 支付列表
  repeated models.payment.v2.PaymentTransactionView transactions = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}

// list payment by transaction request
message ListPaymentByTransactionRequest {
  // transaction id
  int64 transaction_id = 1;
}

// list payment by transaction response
message ListPaymentByTransactionResponse {
  // 生成的支付单据
  repeated models.payment.v2.PaymentView payments = 1;
}

// get payment setting request
message GetPaymentSettingRequest {
  // user
  models.payment.v2.User user = 1;
}

// get payment setting response
message GetPaymentSettingResponse {
  // 支付设置
  models.payment.v2.PaymentSetting payment_setting = 1;
}

// update payment setting request
message UpdatePaymentSettingRequest {
  // convenience fee 配置
  message ConvenienceFeeConfigOperation {
    // 是否启用 convenience fee
    optional bool enable = 1;
    // agreement signature url
    optional string agreement_signature_url = 2;
    // fee name
    optional string fee_name = 3;
    // fee rate for online
    optional models.payment.v2.FeeRate online_fee_rate = 4;
    // fee rate for in person
    optional models.payment.v2.FeeRate terminal_fee_rate = 5;
    // 计算方式
    optional models.payment.v2.PaymentSetting.ConvenienceFeeConfig.CalculationMethod calculation_method = 6;
    // exclude debit card
    optional bool exclude_debit_card = 7;
  }

  // tips config
  message TipsConfigOperation {
    // tips option for invoice page
    optional models.payment.v2.PaymentSetting.TipsConfig cashier_tips_option = 1;
    // tips option for terminal
    optional models.payment.v2.PaymentSetting.TipsConfig terminal_tips_option = 2;
    // appearance
    optional string tips_appearance = 3;
  }

  // user
  models.payment.v2.User user = 1;
  // convenience fee 配置
  optional ConvenienceFeeConfigOperation convenience_fee_config = 2;
  // tips 配置
  optional TipsConfigOperation tips_config = 3;
}

// update payment setting response
message UpdatePaymentSettingResponse {
  // 支付设置
  models.payment.v2.PaymentSetting payment_setting = 1;
}
