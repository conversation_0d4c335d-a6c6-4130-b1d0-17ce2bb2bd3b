syntax = "proto3";

package moego.service.business_customer.v1;

import "google/type/interval.proto";
import "moego/models/business_customer/v1/business_customer_pet_defs.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Service for business customer's pet
service BusinessCustomerPetService {
  // get a pet by id
  // use `GetPetInfo` instead
  rpc GetPet(GetPetRequest) returns (GetPetResponse) {
    option deprecated = true;
  }

  // get a pet's info by id
  rpc GetPetInfo(GetPetInfoRequest) returns (GetPetInfoResponse);

  // get pets by ids
  // use `BatchGetPetInfo` instead
  rpc BatchGetPet(BatchGetPetRequest) returns (BatchGetPetResponse) {
    option deprecated = true;
  }

  // get pets' info by ids
  rpc BatchGetPetInfo(BatchGetPetInfoRequest) returns (BatchGetPetInfoResponse);

  // list pets of a customer
  rpc ListPet(ListPetRequest) returns (ListPetResponse);

  // create a pet with additional info
  rpc CreatePetWithAdditionalInfo(CreatePetWithAdditionalInfoRequest) returns (CreatePetWithAdditionalInfoResponse);

  // update a pet
  rpc UpdatePet(UpdatePetRequest) returns (UpdatePetResponse);

  // list pet info
  rpc ListPetInfo(ListPetInfoRequest) returns (ListPetInfoResponse);
}

// get pet request
message GetPetRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;

  // pet id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// get pet response
message GetPetResponse {
  // pet
  moego.models.business_customer.v1.BusinessCustomerPetModel pet = 1;
}

// get pet info request
message GetPetInfoRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // pet id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// get pet info response
message GetPetInfoResponse {
  // pet
  moego.models.business_customer.v1.BusinessCustomerPetInfoModel pet = 1;
}

// batch get pet request
// deprecated, use `BatchGetPetInfo` instead
message BatchGetPetRequest {
  option deprecated = true;
  // tenant, optional
  // If tenant is provided, all pet ids must belong to the tenant. If there is any pet not belonging to the tenant,
  // it will not be included in the response.
  optional moego.models.organization.v1.Tenant tenant = 1;

  // pet ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get pet response
// deprecated, use `BatchGetPetInfo` instead
message BatchGetPetResponse {
  option deprecated = true;
  // pets
  repeated moego.models.business_customer.v1.BusinessCustomerPetModel pets = 1;
}

// batch get pet info request
message BatchGetPetInfoRequest {
  // tenant, optional
  // If tenant is provided, all pet ids must belong to the tenant. If there is any pet not belonging to the tenant,
  // it will not be included in the response.
  optional moego.models.organization.v1.Tenant tenant = 1;

  // pet ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get pet info response
message BatchGetPetInfoResponse {
  // pets
  repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 1;
}

// list pet request
message ListPetRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 4;

  // customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];
  // include passed away
  bool include_passed_away = 3;
}

// list pet response
message ListPetResponse {
  // pets
  repeated moego.models.business_customer.v1.BusinessCustomerPetModel pets = 1;
}

// create pet with additional info request
message CreatePetWithAdditionalInfoRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // customer id, required
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];

  // pet with additional info
  moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef pet_with_additional_info = 3 [(validate.rules).message.required = true];

  // created by (staff id), optional
  optional int64 created_by = 4 [(validate.rules).int64 = {gt: 0}];
}

// create pet with additional info response
message CreatePetWithAdditionalInfoResponse {
  // pet id
  int64 pet_id = 1;
}

// update pet request
message UpdatePetRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // pet id
  int64 id = 2 [(validate.rules).int64.gt = 0];

  // pet
  moego.models.business_customer.v1.BusinessCustomerPetUpdateDef pet = 3 [(validate.rules).message.required = true];
}

// update pet response
message UpdatePetResponse {}

// list pet info request
message ListPetInfoRequest {
  // filter, each field will be used as AND , under the same field will be used as OR
  message Filter {
    // pet ids
    repeated int64 ids = 1 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      max_items: 1000
    }];
    // customer id
    repeated int64 customer_ids = 2 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      max_items: 1000
    }];
    // company ids
    repeated int64 company_ids = 3 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
      max_items: 1000
    }];
    // include passed away
    bool include_passed_away = 4;
    // update time
    google.type.Interval update_time = 5;
  }
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// list pet info response
message ListPetInfoResponse {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // pets
  repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 2;
}
