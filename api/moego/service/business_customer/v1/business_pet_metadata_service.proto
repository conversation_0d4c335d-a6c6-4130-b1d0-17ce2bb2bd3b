syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_metadata_defs.proto";
import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";
import "moego/models/business_customer/v1/business_pet_metadata_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Create pet metadata request
message CreatePetMetadataRequest {
  // Pet metadata
  models.business_customer.v1.BusinessPetMetadataDef metadata = 1 [(validate.rules).message = {required: true}];
  // Company id
  int64 company_id = 2 [(validate.rules).int64 = {gte: 0}];
}

// Create pet metadata response
message CreatePetMetadataResponse {
  // Pet metadata id
  int64 id = 1;
}

// Update pet metadata request
message UpdatePetMetadataRequest {
  // Pet metadata id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // Pet metadata value
  string metadata_value = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 3 [(validate.rules).map = {
    min_pairs: 0
    max_pairs: 1000
  }];
  // Company id
  int64 company_id = 4 [(validate.rules).int64 = {gte: 0}];
}

// Update pet metadata response
message UpdatePetMetadataResponse {}

// Delete pet metadata request
message DeletePetMetadataRequest {
  // Pet metadata id
  int64 id = 1 [(validate.rules).int64 = {gte: 0}];
  // Company id
  int64 company_id = 2 [(validate.rules).int64 = {gte: 0}];
}

// Delete pet metadata response
message DeletePetMetadataResponse {}

// List pet metadata request
message ListPetMetadataRequest {
  // metadata name
  repeated models.business_customer.v1.BusinessPetMetadataName metadata_names = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // Company id
  int64 company_id = 2 [(validate.rules).int64 = {gte: 0}];
}

// List pet metadata response
message ListPetMetadataResponse {
  // Pet metadata
  repeated models.business_customer.v1.BusinessPetMetadataModel metadata = 1;
}

// Sort pet metadata request
message SortPetMetadataRequest {
  // Pet metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Pet metadata ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      int64: {gte: 0}
    }
  }];
  // Company id
  int64 company_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// Sort pet metadata response
message SortPetMetadataResponse {}

// Initialize pet metadata request
message InitPetMetadataRequest {
  // Company id
  int64 company_id = 1 [(validate.rules).int64 = {gte: 0}];
}

// Initialize pet metadata response
message InitPetMetadataResponse {}

// Business pet metadata service, which provides pet metadata related operations
service BusinessPetMetadataService {
  // Create pet metadata. Below are some examples of common metadata values:
  // Feeding/Medication schedule: 540 (09:00 AM) / 1080 (06:00 PM)
  // Feeding/Medication unit: Cup / Oz
  // Feeding type: Wet food / Dry food
  // Feeding Source: Owner provide / House provide
  // Feeding Instruction: Free feed / Feed individually
  // Display rules: {Feeding schedule} {Feeding type} {Feeding amount} {Feeding unit} {Feeding instruction}
  rpc CreatePetMetadata(CreatePetMetadataRequest) returns (CreatePetMetadataResponse);

  // Update pet metadata
  rpc UpdatePetMetadata(UpdatePetMetadataRequest) returns (UpdatePetMetadataResponse);

  // Delete pet metadata
  // The metadata deleted cannot be added again, but it will not affect the original display
  rpc DeletePetMetadata(DeletePetMetadataRequest) returns (DeletePetMetadataResponse);

  // List pet metadata
  rpc ListPetMetadata(ListPetMetadataRequest) returns (ListPetMetadataResponse);

  // Sort pet metadata
  rpc SortPetMetadata(SortPetMetadataRequest) returns (SortPetMetadataResponse);

  // Initialize pet metadata
  rpc InitPetMetadata(InitPetMetadataRequest) returns (InitPetMetadataResponse);
}
