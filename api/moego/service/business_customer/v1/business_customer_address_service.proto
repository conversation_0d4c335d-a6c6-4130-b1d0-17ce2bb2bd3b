syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_address_defs.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get customer address request
message GetCustomerAddressRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;
  // address id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// get customer address response
message GetCustomerAddressResponse {
  // address
  moego.models.business_customer.v1.BusinessCustomerAddressModel address = 1;
}

// batch get customer address request
message BatchGetCustomerAddressRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;
  // address ids
  repeated int64 ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get customer address response
message BatchGetCustomerAddressResponse {
  // address for each id, key is address id, value is address
  map<int64, moego.models.business_customer.v1.BusinessCustomerAddressModel> addresses = 1;
}

// get customer primary address request
message GetCustomerPrimaryAddressRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;

  // customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];
}

// get customer primary address response
message GetCustomerPrimaryAddressResponse {
  // customer's primary address, may not exist
  optional moego.models.business_customer.v1.BusinessCustomerAddressModel address = 1;
}

// batch get customer primary address request
message BatchGetCustomerPrimaryAddressRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;

  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get customer primary address response
message BatchGetCustomerPrimaryAddressResponse {
  // primary address for each customer, key is customer id, value is primary address
  map<int64, moego.models.business_customer.v1.BusinessCustomerAddressModel> addresses = 1;
}

// list customer address request
message ListCustomerAddressRequest {
  // company id, deprecated, use tenant instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 3;

  // customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];
}

// list customer address response
message ListCustomerAddressResponse {
  // Customer's all addresses. The list may be empty if the customer has no addresses.
  repeated moego.models.business_customer.v1.BusinessCustomerAddressModel addresses = 1;
}

// create customer address request
message CreateCustomerAddressRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // customer id
  // 优先用这个 customer id, 如果没传(=0) 则用 BusinessCustomerAddressCreateDef 里的 customer id 兜底
  int64 customer_id = 3 [(validate.rules).int64.gte = 0];

  // address
  moego.models.business_customer.v1.BusinessCustomerAddressCreateDef address = 2 [(validate.rules).message.required = true];
}

// create customer address response
message CreateCustomerAddressResponse {
  // address
  moego.models.business_customer.v1.BusinessCustomerAddressModel address = 1;
}

// update customer address request
message UpdateCustomerAddressRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // address id
  int64 id = 3 [(validate.rules).int64.gt = 0];

  // address
  moego.models.business_customer.v1.BusinessCustomerAddressUpdateDef address = 2 [(validate.rules).message.required = true];
}

// update customer address response
message UpdateCustomerAddressResponse {
  // address
  moego.models.business_customer.v1.BusinessCustomerAddressModel address = 1;
}

// delete customer address request
message DeleteCustomerAddressRequest {
  // tenant, optional
  optional moego.models.organization.v1.Tenant tenant = 1;

  // address id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// delete customer address response
message DeleteCustomerAddressResponse {}

// Service for business customer's addresses
service BusinessCustomerAddressService {
  // Get an address by id.
  // Deleted addresses can also be retrieved. Please check by `address.getDeleted()`.
  //
  // Error codes:
  // - CODE_ADDRESS_NOT_FOUND: The address of given id does not exist.
  rpc GetCustomerAddress(GetCustomerAddressRequest) returns (GetCustomerAddressResponse);

  // Batch get addresses by ids. Deleted addresses can also be retrieved.
  // If there are no addresses for given ids, the response will not contain the ids as keys, and no error will be thrown.
  // Please check by `addresses.get(id) != null` in the response.
  rpc BatchGetCustomerAddress(BatchGetCustomerAddressRequest) returns (BatchGetCustomerAddressResponse);

  // Get the primary address for a customer.
  // The customer may not have primary address, please check by `hasAddress()` in the response.
  rpc GetCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest) returns (GetCustomerPrimaryAddressResponse);

  // Get the primary address for each customer in a batch.
  // Some customers may not have primary address, please check by `addresses.get(customerId) != null` in the response.
  rpc BatchGetCustomerPrimaryAddress(BatchGetCustomerPrimaryAddressRequest) returns (BatchGetCustomerPrimaryAddressResponse);

  // List all addresses for a customer.
  // The list may be empty if the customer has no addresses.
  rpc ListCustomerAddress(ListCustomerAddressRequest) returns (ListCustomerAddressResponse);

  // Create a new address.
  // If the address is the first address for the customer, it will be set as the primary address.
  // The coordinate is optional. If provided, the latitude and longitude range will be checked.
  //
  // Error codes:
  // - CODE_INVALID_LATITUDE_OR_LONGITUDE: The latitude or longitude is invalid.
  // - CODE_ADDRESS_IS_EMPTY: All of address1, address2, city, state, country, zipcode, coordinate are empty.
  rpc CreateCustomerAddress(CreateCustomerAddressRequest) returns (CreateCustomerAddressResponse);

  // Update an existing address.
  // If the address is set as the primary address, the other addresses of this customer will be updated to be non-primary.
  // The coordinate is optional. If provided, the latitude and longitude range will be checked.
  // If the address does not exist or has been deleted, an error will be thrown.
  //
  // Error codes:
  // - CODE_ADDRESS_NOT_FOUND: The address of given id does not exist or has been deleted.
  // - CODE_INVALID_LATITUDE_OR_LONGITUDE: The latitude or longitude is invalid.
  rpc UpdateCustomerAddress(UpdateCustomerAddressRequest) returns (UpdateCustomerAddressResponse);

  // Delete an existing address.
  // The method is idempotent without throwing exception if the address has been deleted.
  // Primary address cannot be deleted.
  //
  // Error codes:
  // - CODE_ADDRESS_NOT_FOUND: The address of given id does not exist.
  // - CODE_CANNOT_DELETE_PRIMARY_ADDRESS: The address is the primary address and cannot be deleted.
  rpc DeleteCustomerAddress(DeleteCustomerAddressRequest) returns (DeleteCustomerAddressResponse);
}
