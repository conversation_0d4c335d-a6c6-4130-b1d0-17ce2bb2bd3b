syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/organization/v1/company_enums.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// init setting definition
message InitSettingDef {
  // copy from tenant, optional
  // if set, will copy client & pet settings from this tenant
  optional models.organization.v1.Tenant copy_from_tenant = 1;
  // weight unit, optional
  // if `copy_from_tenant` is not set, this field should be set
  optional moego.models.organization.v1.WeightUnit weight_unit = 2;
}

// init demo profile definition
message InitDemoProfileDef {
  // preferred business id
  int64 preferred_business_id = 1 [(validate.rules).int64.gt = 0];
  // staff id, it's better to use the owner's staff id
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
}

// initialize business customer module request
message InitializeBusinessCustomerModuleRequest {
  // company id, deprecated
  // use `tenant` instead
  int64 company_id = 1 [
    deprecated = true,
    (validate.rules).int64.gte = 0
  ];

  // business id, deprecated
  // use `preferred_business_id` in `init_demo_profile` instead
  optional int64 business_id = 2 [
    deprecated = true,
    (validate.rules).int64.gte = 0
  ];

  // staff id, deprecated
  // use `staff_id` in `init_demo_profile` instead
  int64 staff_id = 3 [
    deprecated = true,
    (validate.rules).int64.gte = 0
  ];

  // weight unit, deprecated
  // use `weight_unit` in `init_setting` instead
  moego.models.organization.v1.WeightUnit weight_unit = 4 [deprecated = true];

  // tenant, required
  models.organization.v1.Tenant tenant = 5;

  // init setting, optional
  optional InitSettingDef init_setting = 6;

  // init demo profile, optional
  optional InitDemoProfileDef init_demo_profile = 7;
}

// initialize business customer module response
message InitializeBusinessCustomerModuleResponse {
  // customer id
  // if `init_demo_profile` is set, customer id of demo profile will be returned,
  // otherwise, it will be 0
  int64 customer_id = 1;

  // mini's pet id
  // if `init_demo_profile` is set, mini's pet id will be returned,
  // otherwise, it will be 0
  int64 mini_pet_id = 2;

  // max's pet id
  // if `init_demo_profile` is set, max's pet id will be returned,
  // otherwise, it will be 0
  int64 max_pet_id = 3;
}

// Service for initialization of business customer
service BusinessCustomerInitializationService {
  // Initialize business customer module for a tenant (company).
  // If `init_setting` is set, the following settings will be initialized:
  // - customer referral source
  // - customer tag
  // - default preferred frequency
  // - pet type
  // - pet breed
  // - pet behavior
  // - pet code
  // - pet coat type
  // - pet fixed
  // - pet size
  // - pet vaccine
  // If `copy_from_tenant` is set, these settings will be copied from it.
  // Otherwise, these settings will be copied from platform template, and in this case `weight_unit` should be set to
  // help initialize pet size.
  //
  // If `init_demo_profile` is set, the following data will be initialized:
  // - a customer named Demo Profile with a main contact and a primary address
  // - a note for Demo Profile, created by given `staff_id`
  // - two pets for Demo Profile, named Mini and Max
  // - a note for each pet, created by given `staff_id`
  // - bind three tags to Demo Profile, if there are enough tags initialized
  // - bind two pet codes to each pet, if there are enough pet codes initialized
  // - add a vaccine record to each pet, if there are enough vaccines initialized
  rpc InitializeBusinessCustomerModule(InitializeBusinessCustomerModuleRequest) returns (InitializeBusinessCustomerModuleResponse);
}
