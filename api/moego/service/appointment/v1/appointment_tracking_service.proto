syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/latlng.proto";
import "moego/models/appointment/v1/appointment_tracking.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// the appointment tracking service
service AppointmentTrackingService {
  // get or init appointment tracking
  rpc GetOrInitAppointmentTracking(GetOrInitAppointmentTrackingRequest) returns (GetOrInitAppointmentTrackingResponse);
  // upsert appointment tracking
  rpc UpsertAppointmentTracking(UpsertAppointmentTrackingRequest) returns (UpsertAppointmentTrackingResponse);
  // update appointment tracking
  rpc UpdateAppointmentTracking(UpdateAppointmentTrackingRequest) returns (UpdateAppointmentTrackingResponse);

  // list appointment tracking
  rpc ListAppointmentTracking(ListAppointmentTrackingRequest) returns (ListAppointmentTrackingResponse);
  // batch get or init appointment tracking
  rpc BatchGetOrInitAppointmentTracking(BatchGetOrInitAppointmentTrackingRequest) returns (BatchGetOrInitAppointmentTrackingResponse);
  // batch upsert appointment tracking
  rpc BatchUpsertAppointmentTracking(BatchUpsertAppointmentTrackingRequest) returns (BatchUpsertAppointmentTrackingResponse);
  // sync appointment tracking
  rpc SyncAppointmentTracking(SyncAppointmentTrackingRequest) returns (SyncAppointmentTrackingResponse);
  // update appointment tracking status
  rpc UpdateStaffLocationStatus(UpdateStaffLocationStatusRequest) returns (UpdateStaffLocationStatusResponse);
}

// get appointment tracking request
// if not found, will init a new one
message GetOrInitAppointmentTrackingRequest {
  // appointment id
  int64 appointment_id = 1;
}

// get appointment tracking response
message GetOrInitAppointmentTrackingResponse {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}

// list appointment tracking request
message ListAppointmentTrackingRequest {
  // filter
  message Filter {
    // appointment id,must have when pagination is not set
    repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
      ignore_empty: true
      max_items: 3000
    }];
    // staff location status
    repeated moego.models.appointment.v1.StaffLocationStatus staff_location_statuses = 2 [(validate.rules).repeated = {
      ignore_empty: true
      items: {
        enum: {defined_only: true}
      }
    }];
    // staff id list
    repeated int64 location_sharing_staff_ids = 3 [(validate.rules).repeated = {
      ignore_empty: true
      max_items: 3000
    }];
  }

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// list appointment tracking response
message ListAppointmentTrackingResponse {
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 1;
  // appointment tracking
  repeated moego.models.appointment.v1.AppointmentTracking appointment_tracking = 2;
}

// upsert appointment tracking request
message UpsertAppointmentTrackingRequest {
  // appointment id
  int64 appointment_id = 1;
  // update appointment tracking def
  moego.models.appointment.v1.UpdateAppointmentTrackingDef appointment_tracking = 2;
}

// upsert appointment tracking  response
message UpsertAppointmentTrackingResponse {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}

// update appointment tracking request
message UpdateAppointmentTrackingRequest {
  // company
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment ids
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
  // update appointment tracking def
  moego.models.appointment.v1.UpdateAppointmentTrackingDef appointment_tracking = 3;
}

// update appointment tracking response
message UpdateAppointmentTrackingResponse {
  // appointment tracking
  repeated moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}

// batch upsert appointment tracking request
message BatchUpsertAppointmentTrackingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // update appointment tracking def
  moego.models.appointment.v1.UpdateAppointmentTrackingDef appointment_tracking = 2;
  // appointment id list
  repeated int64 appointment_ids = 3 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 1
    max_items: 1000
  }];
  // filter
  Filter filter = 4;
  // filter
  message Filter {
    // staff location status
    repeated moego.models.appointment.v1.StaffLocationStatus staff_location_statuses = 2 [(validate.rules).repeated = {
      ignore_empty: true
      items: {
        enum: {defined_only: true}
      }
    }];
    // staff id list
    repeated int64 location_sharing_staff_ids = 3 [(validate.rules).repeated = {
      ignore_empty: true
      max_items: 3000
    }];
  }
}

// batch upsert appointment tracking response
message BatchUpsertAppointmentTrackingResponse {
  // appointment tracking
  repeated moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}

// batch get or init appointment tracking request
message BatchGetOrInitAppointmentTrackingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id list
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 1
    max_items: 1000
  }];
}

// batch get or init appointment tracking response
message BatchGetOrInitAppointmentTrackingResponse {
  // appointment tracking
  repeated moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}

// sync appointment tracking request
message SyncAppointmentTrackingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // coordinate
  google.type.LatLng coordinate = 3;
  // device id
  string device_id = 4;
}

// sync appointment tracking response
message SyncAppointmentTrackingResponse {}

// update appointment tracking StaffLocationStatus request
message UpdateStaffLocationStatusRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // status
  moego.models.appointment.v1.StaffLocationStatus staff_location_status = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // staff coordinate
  optional google.type.LatLng staff_coordinate = 5;
  // device id, must have when staff location status is change to IN_TRANSIT
  optional string device_id = 6;
}

// update appointment tracking status response
message UpdateStaffLocationStatusResponse {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTracking appointment_tracking = 1;
}
