syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_pet_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_schedule_setting_models.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/pet_detail_models.proto";
import "moego/models/appointment/v1/service_operation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// Reschedule All-in-One service request
message RescheduleAllInOneServiceRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and services schedule list
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// Reschedule All-in-One service response
message RescheduleAllInOneServiceResponse {}

// Reschedule boarding service request
message RescheduleBoardingServiceRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and lodging schedule
  repeated models.appointment.v1.BoardingServiceScheduleDef boarding_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// Reschedule boarding service response
message RescheduleBoardingServiceResponse {
  // conflict service names, if there are no conflicts, the list is empty
  repeated string conflict_service_names = 1;
}

// Reschedule boarding service request
message RescheduleDaycareServiceRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and lodging schedule
  repeated DaycareServiceSchedule daycare_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];

  // Reschedule daycare service pet detail
  message DaycareServiceSchedule {
    // pet id
    int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
    // service start date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
    optional string start_date = 2 [(validate.rules).string = {pattern: "^(\\d{4}-\\d{2}-\\d{2})$"}];
    // service end date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
    optional string end_date = 3 [(validate.rules).string = {pattern: "^(\\d{4}-\\d{2}-\\d{2})$"}];
    // start time, in minutes
    int32 start_time = 4 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // end time, in minutes
    int32 end_time = 5 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // selected lodging id
    int64 lodging_id = 6 [(validate.rules).int64 = {gte: 0}];

    // Certain dates
    // support daycare associated to boarding with specific days
    repeated string specific_dates = 7 [(validate.rules).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];

    // pet detail date type
    models.appointment.v1.PetDetailDateType date_type = 8 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }
}

// Reschedule daycare service response
message RescheduleDaycareServiceResponse {
  // conflict service names, if there are no conflicts, the list is empty
  repeated string conflict_service_names = 1;
}

// Reschedule grooming service request
message RescheduleGroomingServiceRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // card ids corresponding to different types of cards
  // APPOINTMENT: appt id
  // SERVICE: pet detail id
  // OPERATION: multi-staff service operation id
  // BLOCK: appt id
  // BOOKING_REQUEST: booking request id
  optional int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // card type
  moego.models.appointment.v1.CalendarCardType card_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // rescheduled staff id, if not specified, the original staff id will be inherited.
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // rescheduled start date, in the format of "YYYY-MM-DD"
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // rescheduled start time, in minutes
  int32 start_time = 6 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // rescheduled end time, in minutes
  optional int32 end_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // rescheduled pet detail ids, when card type is SERVICE
  repeated int64 pet_detail_ids = 8 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
  // The scope of impact of this modification on repeat appointments
  optional moego.models.appointment.v1.RepeatAppointmentModifyScope repeat_type = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // company id
  int64 company_id = 11 [(validate.rules).int64 = {gt: 0}];

  // staff id, operator
  int64 token_staff_id = 13 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];
}

// Reschedule grooming service response
message RescheduleGroomingServiceResponse {}

// Reschedule evaluation service request
message RescheduleEvaluationServiceRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and lodging schedule
  repeated models.appointment.v1.EvaluationServiceScheduleDef evaluation_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// Reschedule evaluation service response
message RescheduleEvaluationServiceResponse {}

// lodging assign request
message LodgingAssignRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // lodging for services to be assigned
  repeated models.appointment.v1.ServiceLodgingAssignDef services = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
  // lodging for evaluations to be assigned
  repeated models.appointment.v1.ServiceLodgingAssignDef evaluations = 4 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// lodging assign response
message LodgingAssignResponse {}

// Get pet feeding and medication schedules request
message GetPetFeedingMedicationSchedulesRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [
    (validate.rules).int64 = {
      gt: 0
      ignore_empty: true
    },
    deprecated = true
  ];

  // staff id
  int64 staff_id = 7 [
    (validate.rules).int64 = {
      gt: 0
      ignore_empty: true
    },
    deprecated = true
  ];
}

// Get pet feeding and medication schedules response
message GetPetFeedingMedicationSchedulesResponse {
  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 1 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Schedule settings
  repeated models.appointment.v1.AppointmentPetScheduleSettingModel schedule_settings = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// batch get pet feeding and medication schedules request
message BatchGetPetFeedingMedicationSchedulesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment ids
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch get pet feeding and medication schedules response
message BatchGetPetFeedingMedicationSchedulesResponse {
  // appointment id to pet schedules
  map<int64, models.appointment.v1.AppointmentPetScheduleDef> appointment_pet_schedules = 1;
}

// Update pet feeding medication request
message ReschedulePetFeedingMedicationRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 2 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gte: 0}];
}

// Update pet feeding medication response
message ReschedulePetFeedingMedicationResponse {}

// Calculate appointment and pet details schedule request
message CalculateAppointmentScheduleRequest {
  // Appointment params
  models.appointment.v1.AppointmentCreateDef appointment = 2 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// Calculate appointment and pet details schedule response
message CalculateAppointmentScheduleResponse {
  // Appointment schedule
  models.appointment.v1.AppointmentScheduleDef appointment_schedule = 1 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceScheduleDef pet_service_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Batch reschedule appointment
message BatchRescheduleAppointmentRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment ids
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];

  // operate staff id
  int64 reschedule_by = 3 [(validate.rules).int64 = {gt: 0}];

  // Source staff id
  int64 source_staff_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Target staff id (optional, but either this or target_date must be provided)
  optional int64 target_staff_id = 5 [(validate.rules).int64 = {gt: 0}];

  // Target date (optional, but either this or target_staff_id must be provided)
  optional google.type.Date target_date = 6;
}

// Batch reschedule appointment
message BatchRescheduleAppointmentResponse {
  // reschedule appointments detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// The request of preview calendar schedule
message PreviewCalendarScheduleRequest {
  // Company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];

  // Selected appointment id
  // Used to edit schedule
  optional int64 appointment_id = 4 [(validate.rules).int64.gt = 0];

  // Multi pets start at the same time, default to false
  bool all_pets_start_at_same_time = 5;

  // Appointment calendar schedule
  moego.models.appointment.v1.AppointmentCalendarScheduleDef appointment_schedule = 6 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceCalendarDef pet_services = 7 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Original pet id
  // Apply to existing appointment modify directly replace pet scenario
  optional int64 original_pet_id = 8 [(validate.rules).int64 = {gt: 0}];
}

// The response for preview calendar schedule
message PreviewCalendarScheduleResponse {
  // Appointment model
  moego.models.appointment.v1.AppointmentModel appointment = 1;

  // Pet and service detail
  repeated models.appointment.v1.PetDetailModel pet_details = 2;

  // Service operation
  repeated models.appointment.v1.ServiceOperationModel operations = 3;
}

// The request of reschedule calendar card
message RescheduleCalendarCardRequest {
  // Reschedule appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Reschedule card type
  moego.models.appointment.v1.CalendarCardType card_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Rescheduled pet detail ids, dragged cards objects, used to identify the dragged pet details
  // SERVICE/OPERATION/SERVICE_AND_OPERATION card type, the pet_detail_ids field is required
  repeated int64 pet_detail_ids = 3 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];

  // Original staff id, current card location staff id
  optional int64 original_staff_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Target staff id, if not specified, the original staff id will be inherited.
  optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];

  // Target start date, in the format of "YYYY-MM-DD"
  string start_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // Target start time, in minutes
  optional int32 start_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // Stretch end time, in minutes
  // The end time that can be used to indicate stretching service hours when only one service is included for a pet selection
  optional int32 end_time = 8 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // The scope of impact of this modification on repeat appointments
  optional moego.models.appointment.v1.RepeatAppointmentModifyScope repeat_modify_type = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // The flag of move all cards
  bool move_all_cards = 10;

  // The updated staff id
  optional int64 updated_by = 11 [(validate.rules).int64 = {gt: 0}];
}

// The response of reschedule calendar card
message RescheduleCalendarCardResponse {}

// Switch all pets start at the same time request
message SwitchAllPetsStartAtSameTimeRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // All pets start at the same time
  bool all_pets_start_at_same_time = 2;
}

// Switch all pets start at the same time response
message SwitchAllPetsStartAtSameTimeResponse {}

// The request message for reschedule pet details
message ReschedulePetDetailsRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Pet details schedule
  repeated models.appointment.v1.PetDetailScheduleDef pet_details = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The response message for reschedule pet details
message ReschedulePetDetailsResponse {}

// The appointment schedule service, used to manage appointment schedule
// Can change the date and time of the appointment or pet details
service AppointmentScheduleService {
  // Reschedule All-in-One (boarding&daycare&grooming) service, used to appointment card edit schedule
  rpc RescheduleAllInOneService(RescheduleAllInOneServiceRequest) returns (RescheduleAllInOneServiceResponse);

  // Reschedule boarding service, used to lodging calendar
  // If there are multiple pets, the date and time of other pets will be changed in conjunction.
  rpc RescheduleBoardingService(RescheduleBoardingServiceRequest) returns (RescheduleBoardingServiceResponse);

  // Reschedule daycare service, used to lodging calendar
  rpc RescheduleDaycareService(RescheduleDaycareServiceRequest) returns (RescheduleDaycareServiceResponse);

  // Reschedule grooming service, used to grooming calendar
  // Replace to @see [RescheduleCalendarCard](#moego.api.appointment.v1.CalendarService.RescheduleCalendarCard)
  rpc RescheduleGroomingService(RescheduleGroomingServiceRequest) returns (RescheduleGroomingServiceResponse) {
    option deprecated = true;
  }

  // Reschedule evaluation service
  rpc RescheduleEvaluationService(RescheduleEvaluationServiceRequest) returns (RescheduleEvaluationServiceResponse);

  // lodging assign (boarding&daycare&evaluation) service
  rpc LodgingAssign(LodgingAssignRequest) returns (LodgingAssignResponse);

  // Get pet's feeding and medication schedules
  rpc GetPetFeedingMedicationSchedules(GetPetFeedingMedicationSchedulesRequest) returns (GetPetFeedingMedicationSchedulesResponse);

  // Batch get pet's feeding and medication schedules
  rpc BatchGetPetFeedingMedicationSchedules(BatchGetPetFeedingMedicationSchedulesRequest) returns (BatchGetPetFeedingMedicationSchedulesResponse);

  // Update pet feeding and medication, used to appointment's Feeding & Medication tasks
  // Fully update the schedules of a single pet, including feeding and medication
  rpc ReschedulePetFeedingMedication(ReschedulePetFeedingMedicationRequest) returns (ReschedulePetFeedingMedicationResponse);

  // Calculate appointment and pet's service schedules
  // Appointment: Start date, end date, start time, end time
  // Pet Service: Start date, end date, start time, end time
  rpc CalculateAppointmentSchedule(CalculateAppointmentScheduleRequest) returns (CalculateAppointmentScheduleResponse);

  // Batch reschedule appointment by staff and date
  rpc BatchRescheduleAppointment(BatchRescheduleAppointmentRequest) returns (BatchRescheduleAppointmentResponse);

  // Preview calendar schedule
  rpc PreviewCalendarSchedule(PreviewCalendarScheduleRequest) returns (PreviewCalendarScheduleResponse);

  // Reschedule calendar card
  // For use with @see [ListDayCardsWithMixType](#moego.api.appointment.v1.CalendarService.ListDayCardsWithMixType)
  // reference: https://moego.atlassian.net/wiki/spaces/ET/pages/665387113/Calendar+card+split#%E4%BF%AE%E6%94%B9-RescheduleGroomingService-API
  // Reschedule object: card_type + appointment_id + pet_detail_ids
  // Target position: staff_id + start_date + start_time
  // Scope: repeat_modify_type
  // Stretch position: end_time
  rpc RescheduleCalendarCard(RescheduleCalendarCardRequest) returns (RescheduleCalendarCardResponse);

  // Switch all pets start at the same time
  rpc SwitchAllPetsStartAtSameTime(SwitchAllPetsStartAtSameTimeRequest) returns (SwitchAllPetsStartAtSameTimeResponse);

  // Reschedule pet details, Each modification is independent and does not affect other pet details
  // Allow scheduling can be done for any of the pet details of the appointment
  // Not allow to grooming only Modification
  rpc ReschedulePetDetails(ReschedulePetDetailsRequest) returns (ReschedulePetDetailsResponse);
}
