syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/interval.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/evaluation_service_models.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/pet_detail_models.proto";
import "moego/models/fulfillment/v1/fulfillment_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// Save or update appointment pet detail request
message SaveOrUpdatePetDetailsRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // DO NOT use this field, use `pet_details` instead.
  optional models.appointment.v1.PetDetailDef pet_detail = 2 [deprecated = true];

  // Multi pets update details
  // It contains all the services and add-on information selected for a single pet.
  // If it already exists, it will be overwritten with the latest data.
  // If it does not exist, the latest data will be directly inserted.
  repeated models.appointment.v1.PetDetailDef pet_details = 7 [(validate.rules).repeated.max_items = 100];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// Save or update appointment pet detail response
message SaveOrUpdatePetDetailsResponse {}

// create pet detail request
message CreatePetDetailsForExtraOrderRequest {
  // extra order id
  int64 extra_order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected pet and services
  // If it already exists, it will report an error.
  // If it does not exist, the latest data will be directly inserted.
  models.appointment.v1.PetDetailDef pet_detail = 3 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// create pet detail response
message CreatePetDetailsForExtraOrderResponse {
  // pet detail ids
  repeated int64 pet_detail_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// Delete appointment selected pet request
message DeletePetRequest {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // selected pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // staff id
  int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// Delete appointment selected pet response
message DeletePetResponse {}

// Delete appointment selected pet evaluation request
message DeletePetEvaluationRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Appointment id
  int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];

  // selected evaluation service detail id
  int64 evaluation_service_detail_id = 3 [(validate.rules).int64.gt = 0];

  // token staff id
  int64 token_staff_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// Delete appointment selected pet result
message DeletePetEvaluationResponse {}

// Update upcoming appt pet details request
message UpdateUpcomingPetDetailsRequest {
  // Updated service id
  int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];

  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // staff id
  // The staff id of the staff who updated the service
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];

  // location override rule
  repeated models.offering.v1.LocationOverrideRule location_override_rule = 4 [(validate.rules).repeated = {
    items: {
      message: {required: true}
    }
  }];
}

// Update upcoming appt pet details response
message UpdateUpcomingPetDetailsResponse {
  // affected appt count
  int64 affected_appt_count = 1 [(validate.rules).int64 = {gt: 0}];
}

// get pet detail request
message GetPetDetailListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];
  // the appointment id
  repeated int64 appointment_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // 是否返回转换后的静态日期信息。当前预约支持动态日期配置，且层级复杂。提供一个参数支持解析后返回。短期内暂时只支持 service 类型的 petDetail 解析
  optional bool with_actual_dates = 3;
}

// get pet detail request
message GetPetDetailListResponse {
  // pet detail
  repeated models.appointment.v1.PetDetailModel pet_details = 1;
  // pet evaluation detail
  repeated models.appointment.v1.EvaluationServiceModel pet_evaluations = 2;
}

// Update pet detail request
message UpdatePetDetailRequest {
  // pet detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // grooming id
  optional int64 grooming_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pet id
  optional int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // service id
  optional int64 service_id = 5 [(validate.rules).int64 = {gt: 0}];
  // date type
  optional moego.models.offering.v1.DateType date_type = 28 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type
  optional models.offering.v1.ServiceType service_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service time, in minutes
  optional int32 service_time = 7 [(validate.rules).int32 = {gte: 0}];
  // service price
  optional double service_price = 8 [(validate.rules).double = {gte: 0}];
  // start time, in minutes
  optional int32 start_time = 9 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // end time, in minutes
  optional int32 end_time = 10 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // status
  optional moego.models.appointment.v1.PetDetailStatus status = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // scope type price
  optional models.offering.v1.ServiceScopeType scope_type_price = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // scope type time
  optional models.offering.v1.ServiceScopeType scope_type_time = 13 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // star staff id
  optional int64 star_staff_id = 14 [(validate.rules).int64 = {gt: 0}];
  // package service id
  optional int64 package_service_id = 15 [(validate.rules).int64 = {gt: 0}];
  // enable operation
  optional bool enable_operation = 16;
  // work mode, 0-parallel, 1-sequence
  optional models.appointment.v1.WorkMode work_mode = 17 [(validate.rules).enum = {defined_only: true}];
  // service color code
  optional string service_color_code = 18 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  optional string start_date = 19 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  optional string end_date = 20 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  optional models.offering.v1.ServiceItemType service_item_type = 26 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // lodging id, only for boarding service item type
  optional int64 lodging_id = 21 [(validate.rules).int64 = {gt: 0}];
  // price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
  optional int32 price_unit = 22 [(validate.rules).int32 = {gt: 0}];
  // add-on specific dates, yyyy-MM-dd
  optional string specific_dates = 23 [(validate.rules).string = {max_len: 1000}];
  // add-on associated service id
  optional int64 associated_service_id = 24 [(validate.rules).int64 = {gt: 0}];
  // price override type
  optional models.offering.v1.ServiceOverrideType price_override_type = 25 [(validate.rules).enum = {defined_only: true}];
  // duration override type
  optional models.offering.v1.ServiceOverrideType duration_override_type = 27 [(validate.rules).enum = {defined_only: true}];
}

// Create pet detail request
message CreatePetDetailRequest {
  // grooming id, aka. appointment id
  int64 grooming_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 3 [(validate.rules).int64 = {gt: 0}];
  // date type
  moego.models.offering.v1.DateType date_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // quantity per day
  optional int32 quantity_per_day = 5 [(validate.rules).int32 = {gt: 0}];
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  // use service's item type if not specified
  optional models.offering.v1.ServiceItemType service_item_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, use service's type if not specified
  optional models.offering.v1.ServiceType service_type = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // price unit, use service's price unit if not specified
  optional moego.models.offering.v1.ServicePriceUnit price_unit = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // staff id
  optional int64 staff_id = 9 [(validate.rules).int64 = {gt: 0}];
  // service time, in minutes
  optional int32 service_time = 10 [(validate.rules).int32 = {gte: 0}];
  // service price
  optional double service_price = 11 [(validate.rules).double = {gte: 0}];
  // start time, in minutes
  optional int32 start_time = 12 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // end time, in minutes
  optional int32 end_time = 13 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // service start date, in yyyy-MM-dd format
  optional string start_date = 14 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service end date, in yyyy-MM-dd format
  optional string end_date = 15 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // lodging id, only for boarding/daycare service item type
  optional int64 lodging_id = 16 [(validate.rules).int64 = {gt: 0}];
  // add-on specific dates, yyyy-MM-dd
  repeated string specific_dates = 17 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // add-on associated service id
  optional int64 associated_service_id = 18 [(validate.rules).int64 = {gt: 0}];
}

// Update pet detail response
message UpdatePetDetailResponse {
  // affected count
  int64 affected_count = 1;
}

// Get pet detail request
message GetPetDetailRequest {
  // Pet detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // Optional company id
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Get pet detail response
message GetPetDetailResponse {
  // Pet detail
  models.appointment.v1.PetDetailModel record = 1;
}

// delete pet detail request
message DeletePetDetailForExtraOrderRequest {
  // pet detail id
  int64 pet_detail_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete peta detail
message DeletePetDetailForExtraOrderResponse {
  // result
  bool result = 1;
}

// Update upcoming appointments request
message UpdateUpcomingAppointmentsRequest {
  // business ids，需要 apply to upcoming 的 business id 列表，空列表不会更新任何数据
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // old service, 修改之前的 service
  // update coming appointments 操作需要根据 old/new service 做一些逻辑判断
  models.offering.v1.ServiceModel old_service = 2 [(validate.rules).message = {required: true}];
}

// Update upcoming appointments response
message UpdateUpcomingAppointmentsResponse {}

// get last pet detail request
message GetLastPetDetailRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  repeated int64 customer_id = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];
  // pet id
  repeated int64 pet_id = 3 [(validate.rules).repeated = {
    min_items: 1
    items: {
      int64: {gt: 0}
    }
  }];

  // filter
  optional Filter filter = 5;

  // filter
  message Filter {
    // business id
    optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
    // appointment status, if is empty then return not canceled appointment
    repeated moego.models.appointment.v1.AppointmentStatus status = 2 [(validate.rules).repeated = {
      unique: true
      items: {
        enum: {defined_only: true}
      }
    }];
    // if no start time or end time, default is before current time
    // start time range
    optional google.type.Interval start_time_range = 3;
    // end_time_range
    optional google.type.Interval end_time_range = 4;
    // service item type
    repeated models.offering.v1.ServiceItemType service_item_types = 5;
    // filter no start time, default include
    optional bool filter_no_start_time = 6;
    // filter booking request, default include
    optional bool filter_booking_request = 7;
    // service ids
    repeated int64 service_ids = 8 [(validate.rules).repeated = {
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// get last pet detail response
message GetLastPetDetailResponse {
  // pet detail
  repeated models.appointment.v1.PetDetailModel pet_details = 1;
}

// get staff pet detail request
message GetStaffPetDetailsRequest {
  // company_id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business_id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // start time range
  google.type.Interval start_time_range = 4;
  // end_time_range
  google.type.Interval end_time_range = 5;
}

// get staffs pet detail response
message GetStaffPetDetailsResponse {
  // pet detail
  repeated models.appointment.v1.StaffPetDetail staff_pet_details = 1;
}

// The request message for listing pet services and surcharge
message ListPetServicesAndSurchargesRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The response message for listing pet services and surcharge
message ListPetServicesAndSurchargesResponse {
  // pet services
  repeated models.fulfillment.v1.PetService pet_services = 1;
  // surcharges
  repeated models.fulfillment.v1.SurchargeItem surcharges = 2;
}

// Appointment pet detail service
service PetDetailService {
  // Get pet detail by id, not including deleted records.
  rpc GetPetDetail(GetPetDetailRequest) returns (GetPetDetailResponse);

  // Save or update pet's selected services
  // If there is no pet, the selected services will be saved directly.
  // If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
  rpc SaveOrUpdatePetDetails(SaveOrUpdatePetDetailsRequest) returns (SaveOrUpdatePetDetailsResponse);

  // add new pet detail for extra order
  rpc CreatePetDetailsForExtraOrder(CreatePetDetailsForExtraOrderRequest) returns (CreatePetDetailsForExtraOrderResponse);

  // delete pet detail for extra order
  rpc DeletePetDetailForExtraOrder(DeletePetDetailForExtraOrderRequest) returns (DeletePetDetailForExtraOrderResponse);

  // Delete selected pet
  rpc DeletePet(DeletePetRequest) returns (DeletePetResponse);

  // Delete selected pet evaluation service detail
  rpc DeletePetEvaluation(DeletePetEvaluationRequest) returns (DeletePetEvaluationResponse);

  // Update upcoming appt pet details
  rpc UpdateUpcomingPetDetails(UpdateUpcomingPetDetailsRequest) returns (UpdateUpcomingPetDetailsResponse);

  // Update upcoming appointments
  // NOTE: 这个接口调用很慢，业务代码不要尝试同步调用。
  // 这个接口的使用场景很独立，只有在修改 service 触发 apply to upcoming 时才会调用。
  // 不要尝试复用这个接口，除非你知道这个接口的真正目的。
  rpc UpdateUpcomingAppointments(UpdateUpcomingAppointmentsRequest) returns (UpdateUpcomingAppointmentsResponse);

  // get pet detail list
  rpc GetPetDetailList(GetPetDetailListRequest) returns (GetPetDetailListResponse);

  // Selectively update a PetDetail, only the fields that are set in the request will be updated
  rpc UpdatePetDetail(UpdatePetDetailRequest) returns (UpdatePetDetailResponse);

  // get last pet detail
  rpc GetLastPetDetail(GetLastPetDetailRequest) returns (GetLastPetDetailResponse);

  // get staff pet detail
  rpc GetStaffPetDetails(GetStaffPetDetailsRequest) returns (GetStaffPetDetailsResponse);

  // list pet services and surcharge
  rpc ListPetServicesAndSurcharges(ListPetServicesAndSurchargesRequest) returns (ListPetServicesAndSurchargesResponse);
}
