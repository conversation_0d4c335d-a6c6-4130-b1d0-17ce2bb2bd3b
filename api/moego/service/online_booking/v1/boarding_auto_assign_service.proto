syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/online_booking/v1/boarding_auto_assign_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Get BoardingAutoAssign response
message GetBoardingAutoAssignResponse {
  // Existing record
  optional moego.models.online_booking.v1.BoardingAutoAssignModel record = 1;
}

// List BoardingAutoAssign by booking request ids request
message ListBoardingAutoAssignByBookingRequestIdsRequest {
  // Booking request ids
  repeated int64 booking_request_ids = 1;
}

// List BoardingAutoAssign by booking request ids response
message ListBoardingAutoAssignByBookingRequestIdsResponse {
  // List of boarding auto assign records
  repeated moego.models.online_booking.v1.BoardingAutoAssignModel records = 1;
}

// Get BoardingAutoAssign by service detail id request
message GetBoardingAutoAssignByServiceDetailIdRequest {
  // Boarding service detail id
  int64 service_detail_id = 1;
}

// Get BoardingAutoAssign by service detail id response
message GetBoardingAutoAssignByServiceDetailIdResponse {
  // Existing record
  optional moego.models.online_booking.v1.BoardingAutoAssignModel record = 1;
}

// List BoardingAutoAssign by service detail ids request
message ListBoardingAutoAssignByServiceDetailIdsRequest {
  // Boarding service detail ids
  repeated int64 service_detail_ids = 1;
}

// List BoardingAutoAssign by service detail ids response
message ListBoardingAutoAssignByServiceDetailIdsResponse {
  // List of boarding auto assign records
  repeated moego.models.online_booking.v1.BoardingAutoAssignModel records = 1;
}

// BoardingAutoAssign service
service BoardingAutoAssignService {
  // List boarding auto assign records by booking request ids.
  rpc ListBoardingAutoAssignByBookingRequestIds(ListBoardingAutoAssignByBookingRequestIdsRequest) returns (ListBoardingAutoAssignByBookingRequestIdsResponse) {}
  // Get boarding auto assign record by service detail id.
  rpc GetBoardingAutoAssignByServiceDetailId(GetBoardingAutoAssignByServiceDetailIdRequest) returns (GetBoardingAutoAssignByServiceDetailIdResponse) {}
  // List boarding auto assign records by service detail ids.
  rpc ListBoardingAutoAssignByServiceDetailIds(ListBoardingAutoAssignByServiceDetailIdsRequest) returns (ListBoardingAutoAssignByServiceDetailIdsResponse) {}
}
