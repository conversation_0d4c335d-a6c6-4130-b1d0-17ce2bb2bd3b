syntax = "proto3";

package moego.service.online_booking.v1;

import "google/type/date.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Get available date time request
message GetAvailableDateTimeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // from date
  google.type.Date from_date = 3;
  // to date
  google.type.Date to_date = 4;
  // business id
  models.offering.v1.ServiceItemType service_item_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service/evaluation ids 用来 filter capacity
  repeated int64 relation_service_ids = 6;
}

// get available date time response
message GetAvailableDateTimeResponse {
  // arrival available time range
  repeated DateTimeRange arrival_time_range = 1;
  // pick up available time range
  repeated DateTimeRange pick_up_time_range = 2;

  // date time range
  message DateTimeRange {
    // date
    google.type.Date date = 1;
    // time range
    repeated moego.models.online_booking.v1.DayTimeRangeDef time_range = 2;
  }
}

// available service
// 相关计算，要在 B 端和 C 端复用，所有抽离聚合到 svc 层
service OBAvailableDateTimeService {
  // get available date time
  rpc GetAvailableDateTime(GetAvailableDateTimeRequest) returns (GetAvailableDateTimeResponse);
}
