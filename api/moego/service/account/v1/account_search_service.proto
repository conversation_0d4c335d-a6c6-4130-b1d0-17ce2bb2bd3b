syntax = "proto3";

package moego.service.account.v1;

import "moego/models/account/v1/account_models.proto";
import "moego/utils/v1/condition_messages.proto";
import "moego/utils/v1/pagination_messages.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// search account for admin request
message SearchAccountForAdminRequest {
  option deprecated = true;
  // page
  moego.utils.v1.PaginationRequest page = 1 [(validate.rules).message.required = true];
  // source condition
  optional moego.utils.v1.StringCondition source_condition = 2;
  // email condition
  optional moego.utils.v1.StringCondition email_condition = 3;
  // name condition
  optional moego.utils.v1.StringCondition name_condition = 4;
  // account id condition
  optional moego.utils.v1.Int64Condition account_id_condition = 5;
}

// search account for admin response
message SearchAccountForAdminResponse {
  option deprecated = true;
  // page
  moego.utils.v1.PaginationResponse page = 1;

  // account list
  repeated models.account.v1.AccountModel accounts = 2;
}

// search account request
message SearchAccountRequest {
  // predicate
  optional moego.utils.v2.Predicate predicate = 1;

  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 2;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message.required = true];
}

// search account response
message SearchAccountResponse {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;

  // account list
  repeated models.account.v1.AccountModel accounts = 2;
}

// account search service
service AccountSearchService {
  // search accounts for admin
  rpc SearchAccountForAdmin(SearchAccountForAdminRequest) returns (SearchAccountForAdminResponse) {
    option deprecated = true;
  }

  // search accounts
  rpc SearchAccount(SearchAccountRequest) returns (SearchAccountResponse);
}
